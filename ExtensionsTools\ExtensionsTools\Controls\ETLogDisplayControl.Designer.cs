/*
 * ============================================================================
 * 功能模块：ET日志显示控件 (ETLogDisplayControl) - 设计器文件
 * ============================================================================
 * 
 * 模块作用：提供一个用于显示日志信息的Windows Forms控件，支持多种日志级别、自动滚动等功能
 * 
 * 主要功能：
 * - 显示日志文本
 * - 支持不同日志级别的颜色区分
 * - 自动滚动到底部
 * - 可配置日志行数上限
 * 
 * 执行逻辑：
 * 1. 初始化控件和相关组件
 * 2. 提供写入日志的方法
 * 3. 根据配置自动清理旧日志
 * 4. 释放资源时进行清理
 * 
 * 注意事项：
 * - 此文件由设计器自动生成，请勿手动修改
 * - 如需扩展功能，请在主代码文件中进行
 * ============================================================================
 */

/*
 * ============================================================================
 * 功能模块：ET日志显示控件 (ETLogDisplayControl) - 设计器文件
 * ============================================================================
 * 
 * 模块作用：提供一个用于显示日志信息的控件，支持只读、滚动条、自动换行等功能
 * 
 * 主要功能：
 * - 显示日志文本
 * - 支持只读模式
 * - 支持垂直和水平滚动条
 * - 支持自动换行
 * - 支持设置字体、背景色、前景色
 * 
 * 执行逻辑：
 * 1. 初始化控件和属性
 * 2. 设置日志文本框的样式和行为
 * 3. 提供资源释放方法
 * 
 * 注意事项：
 * - 此文件由设计器生成，不应手动修改
 * - 如需修改，请在其他文件中进行
 * ============================================================================
 */

using ET;

namespace ET.Controls
{
    partial class ETLogDisplayControl
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // 释放自定义资源
                if (!_disposed)
                {
                    try
                    {
                        ETLogManager.Info(this, "ETLogDisplayControl正在释放资源");
                    }
                    catch
                    {
                        // 忽略释放时的日志异常
                    }
                    _disposed = true;
                }

                // 释放组件资源
                if (components != null)
                {
                    components.Dispose();
                }
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.textBoxLog = new System.Windows.Forms.TextBox();
            this.SuspendLayout();
            // 
            // textBoxLog
            // 
            this.textBoxLog.BackColor = System.Drawing.Color.White;
            this.textBoxLog.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.textBoxLog.Dock = System.Windows.Forms.DockStyle.Fill;
            this.textBoxLog.Font = new System.Drawing.Font("Consolas", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.textBoxLog.ForeColor = System.Drawing.Color.Black;
            this.textBoxLog.Location = new System.Drawing.Point(0, 0);
            this.textBoxLog.Multiline = true;
            this.textBoxLog.Name = "textBoxLog";
            this.textBoxLog.ReadOnly = true;
            this.textBoxLog.ScrollBars = System.Windows.Forms.ScrollBars.Both;
            this.textBoxLog.Size = new System.Drawing.Size(400, 300);
            this.textBoxLog.TabIndex = 0;
            this.textBoxLog.WordWrap = false;
            // 
            // ETLogDisplayControl
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.textBoxLog);
            this.Name = "ETLogDisplayControl";
            this.Size = new System.Drawing.Size(400, 300);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        /// <summary>
        /// 用于显示日志的文本框
        /// </summary>
        private System.Windows.Forms.TextBox textBoxLog;
    }
}
