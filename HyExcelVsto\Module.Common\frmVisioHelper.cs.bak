﻿using ET;
using System;
using System.Windows.Forms;
using Range = Microsoft.Office.Interop.Excel.Range;

namespace HyExcelVsto.Module.Common
{
    public partial class frmVisioHelper : Form
    {
        public bool 允许保存为2003版本visio;

        #region 执行

        private void button执行_读取图衔_Click(object sender, EventArgs e)
        { VisioHelperFunctions.执行_仅读取图衔(ucERS文件路径_仅读取图衔.SelectedRange, comboBox图衔名称.Text, textBoxProgress); }

        private void button执行_写入图衔_Click(object sender, EventArgs e)
        {
            VisioHelperFunctions.写入图衔PerRowsThread(
                ucERS文件路径_图衔信息.SelectedRange,
                ucERS页面名称_图衔信息.SelectedRange,
                ds输出路径_图衔信息.Text,
                checkBox直接保存原文件_图衔信息.Checked,
                ucERS图纸名称_写图衔.SelectedRange,
                ucERS单位比例_写图衔.SelectedRange,
                ucERS出图日期_写图衔.SelectedRange,
                ucERS图纸编号_写图衔.SelectedRange,
                fs图衔文件_写图衔.Text,
                comboBox图衔名称.Text,
                textBox修改比例值_写图衔.Text,
                checkBox根据关键字改比例_写图衔.Checked,
                checkBox删除图衔左侧内容_写图衔.Checked,
                checkBox删除图衔图号_写图衔.Checked,
                textBoxProgress,
                textBoxError);
        }

        private void button执行_转换为PDF_Click(object sender, EventArgs e)
        {
            Range filePathRange = ucERS文件路径_转换为PDF.SelectedRange?.OptimizeRangeSize();

            VisioHelperFunctions.VisioToPdfPerRowsThread(
                ds输出路径_转换为PDF.Text,
                ds原根路径_转换为PDF.Text,
                filePathRange,
                textBoxProgress,
                textBoxError);
        }

        private void button执行_更新说明_Click(object sender, EventArgs e)
        {
            VisioHelperFunctions.操作说明文本PerRowsThread(
                ucERS文件路径_文本说明.SelectedRange,
                ucERS页面名称_文本说明.SelectedRange,
                ds输出路径_文本说明.Text,
                checkBox直接保存原文件_文本说明.Checked,
                ucERS铁塔站名编码_读_文本说明.SelectedRange,
                ucERS经纬度及地址_读_文本说明.SelectedRange,
                ucERS安全风险点_读_文本说明.SelectedRange,
                ucERS说明_读_文本说明.SelectedRange,
                ucERS铁塔站名编码_写_文本说明.SelectedRange,
                ucERS经纬度及地址_写_文本说明.SelectedRange,
                ucERS安全风险点_写_文本说明.SelectedRange,
                ucERS说明_写_文本说明.SelectedRange,
                textBoxProgress,
                textBoxError);
        }

        private void button执行_检查图纸_Click(object sender, EventArgs e)
        {
        }

        private void button执行_读取信息_Click(object sender, EventArgs e)
        {
        }

        private void button执行_其它_Click(object sender, EventArgs e)
        {
        }

        #endregion 执行

        #region 界面控制

        public frmVisioHelper()
        { InitializeComponent(); }

        private void frmHelper_Load(object sender, EventArgs e)
        {
            if (ETVisio.IsVisioThreadRunning())
            {
                bool result = ETExcelExtensions.VerifyCode("Visio进程正在运行，多个Visio运行将影响代码正确运行，是否强制结束");
                if (result)
                    ETVisio.KillVisioApplication();
            }

            允许保存为2003版本visio = ThisAddIn.ConfigurationSettings.GetValue("Visio辅助", "允许保存为2003版本visio", "false") == "true";
            //ETVisio.允许保存为2003版本visio = 允许保存为2003版本visio;

            ETForm.BindWindowsFormControl(fs图衔文件_写图衔, ThisAddIn.ConfigurationSettings, "Visio辅助", "fs图衔文件_替换图衔");
            ETForm.BindWindowsFormControl(ds输出路径_图衔信息, ThisAddIn.ConfigurationSettings, "Visio辅助", "fs输出路径_图衔信息");
            ETForm.BindWindowsFormControl(ds输出路径_文本说明, ThisAddIn.ConfigurationSettings, "Visio辅助", "ds输出路径_文本说明");
            ETForm.BindWindowsFormControl(ds输出路径_转换为PDF, ThisAddIn.ConfigurationSettings, "Visio辅助", "ds输出路径_转换为PDF");
            ETForm.BindWindowsFormControl(ds原根路径_转换为PDF, ThisAddIn.ConfigurationSettings, "Visio辅助", "ds原根路径_转换为PDF");
            ETForm.BindWindowsFormControl(checkBox删除图衔左侧内容_写图衔, ThisAddIn.ConfigurationSettings, "Visio辅助", "checkBox删除图衔左侧内容");
            ETForm.BindWindowsFormControl(checkBox删除图衔图号_写图衔, ThisAddIn.ConfigurationSettings, "Visio辅助", "checkBox删除图衔图号");

            ETForm.BindWindowsFormControl(
                checkBox根据关键字改比例_写图衔,
                ThisAddIn.ConfigurationSettings,
                "Visio辅助",
                "checkBox根据关键字修改比例_写_图衔信息");

            ETForm.BindComboBox(comboBox图衔名称, ThisAddIn.ConfigurationSettings, "Visio辅助", "comboBox图衔名称");

            tabControl1_SelectedIndexChanged(null, null);

            tabControl1.HideTabPage(tabPage其它);
            tabControl1.HideTabPage(tabPage读取数据表);
        }

        private void button导入文件清单_Click(object sender, EventArgs e)
        {
            frm文件操作 frm = new(false);
            ThisAddIn.OpenForm(frm);
            frm.tabControl1.SelectedTab = frm.tabPage导入文件名;
            frm.checkBox导入文件_文件.Checked = true;
            frm.checkBox含子目录.Checked = true;
            frm.checkBox导入文件_文件夹.Checked = false;
            frm.导入文件_添加文件夹ToolStripMenuItem_Click(null, null);
        }

        /// <summary>
        /// Tab页切换事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>根据不同的Tab页设置splitContainer1的SplitterDistance</remarks>
        private void tabControl1_SelectedIndexChanged(object sender, EventArgs e)
        {
            int addDistance = 46;

            if (tabControl1.SelectedTab == tabPage仅读取图衔)
            {
                splitContainer1.SplitterDistance = button执行_仅读取图衔.Top + button执行_仅读取图衔.Height + addDistance;
            }
            else if (tabControl1.SelectedTab == tabPage读写取图衔)
            {
                splitContainer1.SplitterDistance = textBox修改比例值_写图衔.Top + textBox修改比例值_写图衔.Height + addDistance;
            }
            else if (tabControl1.SelectedTab == tabPage读取数据表)
            {
                splitContainer1.SplitterDistance = ucERS新增天馈表_读取信息.Top + ucERS新增天馈表_读取信息.Height + addDistance;
            }
            else if (tabControl1.SelectedTab == tabPage文本说明)
            {
                splitContainer1.SplitterDistance = ucERS说明_写_文本说明.Top + ucERS说明_写_文本说明.Height + addDistance;
            }
            else if (tabControl1.SelectedTab == tabPage转换为PDF)
            {
                splitContainer1.SplitterDistance = ds原根路径_转换为PDF.Top + ds原根路径_转换为PDF.Height + addDistance;
            }
            else if (tabControl1.SelectedTab == tabPage其它)
            {
                splitContainer1.SplitterDistance = groupBox1.Top + groupBox1.Height + addDistance;
            }
            else if (tabControl1.SelectedTab == tabPage检查图纸)
            {
                splitContainer1.SplitterDistance = ucERS铁塔站名_检查图纸.Top + ucERS铁塔站名_检查图纸.Height + addDistance;
            }
        }

        private void KillAllVisioApp_Click(object sender, EventArgs e)
        {
            ETVisio.KillVisioApplication();
        }

        #endregion 界面控制
    }
}