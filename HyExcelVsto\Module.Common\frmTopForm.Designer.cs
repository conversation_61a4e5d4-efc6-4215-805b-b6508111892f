﻿/*
 * ============================================================================
 * 功能模块：顶层窗体设计器
 * ============================================================================
 * 
 * 模块作用：定义顶层窗体的用户界面布局和控件配置
 * 
 * 主要功能：
 * - 界面布局：配置顶层窗体的基本属性和样式
 * - 控件初始化：设置定时器控件的属性和事件绑定
 * - 窗体配置：设置窗体为隐藏的工具窗体，不显示在任务栏
 * 
 * 执行逻辑：
 * 1. 控件声明：声明窗体所需的组件容器和定时器控件
 * 2. 布局配置：设置窗体大小、边框样式和显示属性
 * 3. 事件关联：绑定定时器事件到对应处理方法
 * 
 * 注意事项：
 * - 此文件由Visual Studio设计器自动生成，手动修改需谨慎
 * - 窗体设置为隐藏状态，仅作为其他窗体的父窗体使用
 * ============================================================================
 */

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// 顶层窗体的设计器部分类
    /// </summary>
    partial class frmTopMostForm
    {
        /// <summary>
        /// 设计器所需的组件容器
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为true；否则为false</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要使用代码编辑器修改此方法的内容
        /// </summary>
        private void InitializeComponent()
        {
            // 初始化组件容器和定时器控件
            this.components = new System.ComponentModel.Container();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            
            // 暂停布局更新以提高性能
            this.SuspendLayout();
            
            // 
            // timer1 - Excel活动状态检查定时器（已废弃，保留以维持兼容性）
            // 
            this.timer1.Tick += new System.EventHandler(this.CheckExcelActiveTimer_Tick);
            
            // 
            // frmTopForm - 顶层窗体主体配置
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(212, 128);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frmTopForm";
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.Visible = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.Manual;
            this.Text = "frmTopForm";
            this.Load += new System.EventHandler(this.frmTopForm_Load);
            
            // 恢复布局更新
            this.ResumeLayout(false);

        }

        #endregion

        /// <summary>
        /// Excel活动状态检查定时器（已废弃，保留以维持向后兼容性）
        /// </summary>
        public System.Windows.Forms.Timer timer1;
    }
}