﻿/*
 * ============================================================================
 * 功能模块：Excel工作表内容合规性检查工具
 * ============================================================================
 * 
 * 模块作用：对Excel工作表中的数据进行合规性检查，标注不符合规范的单元格
 * 
 * 主要功能：
 * - 合规检查：根据预定义的候选项列表检查单元格内容是否合规
 * - 数据验证：支持从Excel数据验证下拉列表中获取候选项
 * - 范围选择：支持从指定范围获取候选项列表
 * - 视觉标注：对不合规的单元格进行颜色标注
 * - 消息接收：实现IExcelMessageReceiver接口，接收Excel选区消息
 * 
 * 执行逻辑：
 * 1. 用户选择要检查的Excel单元格范围
 * 2. 设置候选项来源（下拉可选项或可选项列表）
 * 3. 系统获取候选项列表并显示在文本框中
 * 4. 执行合规检查，比较单元格值与候选项
 * 5. 对不合规的单元格进行颜色标注
 * 
 * 注意事项：
 * - 支持多种候选项来源：数据验证列表、指定范围
 * - 检查过程中会跳过空单元格
 * - 使用条件格式进行视觉标注
 * - 实现了Excel消息接收接口，支持刷新操作
 * ============================================================================
 */

using ET;
using HyExcelVsto.Interfaces;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Windows.Forms;
using Application = Microsoft.Office.Interop.Excel.Application;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// Excel工作表内容合规性检查窗体
    /// 提供数据合规性检查和视觉标注功能，实现Excel消息接收接口
    /// </summary>
    public partial class frm合规检查 : Form, IExcelMessageReceiver
    {
        /// <summary>
        /// Excel应用程序实例
        /// 用于与Excel进行交互操作
        /// </summary>
        public Application XlApp;

        /// <summary>
        /// 初始化合规检查窗体
        /// </summary>
        public frm合规检查()
        {
            InitializeComponent();
            // 获取Excel应用程序实例
            XlApp = Globals.ThisAddIn.Application;
        }

        /// <summary>
        /// 获取窗体标识符
        /// 用于Excel消息接收接口的窗体识别
        /// </summary>
        public string FormIdentifier => "合规检查";

        /// <summary>
        /// 处理来自Excel的选区消息
        /// </summary>
        /// <param name="target">目标单元格范围</param>
        /// <param name="message">消息内容，可选参数</param>
        /// <remarks>
        /// 实现IExcelMessageReceiver接口的方法，
        /// 当前支持"刷新"消息的处理
        /// </remarks>
        public void OnExcelSelectionMessage(Range target, string message = null)
        {
            if (message == "刷新")
            {
                // 处理刷新消息，可在此添加刷新逻辑
            }
        }

        /// <summary>
        /// 窗体加载事件处理程序
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 窗体加载时的初始化操作，
        /// 包含窗体位置设置的可选代码（已注释）
        /// </remarks>
        void frm合规检查_Load(object sender, EventArgs e)
        {
            // 窗体加载时设置位置（注释掉的代码根据需求决定是否启用）
            // 可选：将窗体定位到屏幕右侧中央位置
            // Left = Screen.PrimaryScreen.Bounds.Width - Width - 50;
            // Top = (Screen.PrimaryScreen.Bounds.Height - Height) / 2 + 200;
        }

        /// <summary>
        /// 标注按钮点击事件处理程序，执行合规性检查并标注不合规单元格
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 执行逻辑：
        /// 1. 获取用户选择的Excel单元格范围
        /// 2. 获取候选项文本并验证是否为空
        /// 3. 将候选项文本分割为有效值集合
        /// 4. 遍历选中范围内的每个单元格
        /// 5. 检查单元格值是否在有效值集合中
        /// 6. 对不合规的单元格进行颜色标注
        /// </remarks>
        void button标注_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取用户选择的Excel单元格范围
                Range selectedRange = ETExcelExtensions.GetSelectionRange();
                // 获取候选项文本内容
                string candidateText = textBox候选项.Text;

                // 验证候选项内容是否为空
                if (string.IsNullOrWhiteSpace(candidateText))
                {
                    MessageBox.Show("请输入候选项内容", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 将候选项文本分割为有效值集合（使用HashSet提高查找性能）
                HashSet<string> validValues = new(candidateText.Split());

                // 遍历选中范围内的每个单元格进行合规检查
                foreach (Range cell in selectedRange.Cells)
                {
                    try
                    {
                        // 跳过空单元格
                        if (cell.IsCellEmpty())
                            continue;

                        // 获取单元格的值
                        string cellValue = cell.Value?.ToString();
                        // 检查单元格值是否在有效值集合中，如果不在则标注为不合规
                        if (cellValue != null && !validValues.Contains(cellValue))
                        {
                            cell.Format条件格式警示色(EnumWarningColor.提醒);
                        }
                    }
                    catch (Exception cellEx)
                    {
                        throw new ETException("单元格合规检查失败", "单元格格式设置", cellEx);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new ETException("合规检查失败", "工作表内容检查", ex);
            }
        }

        /// <summary>
        /// 从Excel数据验证下拉列表中加载候选项到文本框
        /// </summary>
        /// <param name="targetRange">包含数据验证的目标单元格范围</param>
        /// <remarks>
        /// 此方法从Excel单元格的数据验证设置中提取下拉列表选项，
        /// 并将这些选项以换行符分隔的形式显示在候选项文本框中
        /// </remarks>
        void LoadValidationListToTextBox(Range targetRange)
        {
            try
            {
                // 获取目标范围的数据验证选项列表
                List<string> validationList = targetRange.GetValidationOptions();
                if (validationList != null)
                {
                    // 将验证选项以换行符分隔的形式显示在文本框中
                    textBox候选项.Text = string.Join(Environment.NewLine, validationList);
                }
            }
            catch (Exception ex)
            {
                throw new ETException("加载数据验证列表失败", "数据验证读取", ex);
            }
        }

        /// <summary>
        /// 从指定Excel范围中加载所有单元格值到候选项文本框
        /// </summary>
        /// <param name="targetRange">要读取值的目标单元格范围</param>
        /// <remarks>
        /// 此方法读取指定范围内所有单元格的值，
        /// 并将这些值以换行符分隔的形式显示在候选项文本框中，
        /// 通常用于从候选项列表范围中获取有效值
        /// </remarks>
        void LoadRangeValuesToTextBox(Range targetRange)
        {
            try
            {
                // 将目标范围转换为字符串列表（去除重复值）
                List<string> valueList = targetRange.ConvertRangeToStringList(true);
                if (valueList != null)
                {
                    // 将值列表以换行符分隔的形式显示在文本框中
                    textBox候选项.Text = string.Join(Environment.NewLine, valueList);
                }
            }
            catch (Exception ex)
            {
                throw new ETException("加载值列表失败", "单元格值读取", ex);
            }
        }

        /// <summary>
        /// 单选按钮状态改变事件处理程序，根据选择的选项加载不同来源的候选项
        /// </summary>
        /// <param name="sender">触发事件的单选按钮</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 支持两种候选项来源：
        /// - "下拉可选项"：从Excel数据验证下拉列表中获取候选项
        /// - "可选项列表"：从用户指定的Excel范围中获取候选项
        /// </remarks>
        void radioButton_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                RadioButton selectedRadioButton = (RadioButton)sender;
                switch (selectedRadioButton.Text)
                {
                    case "下拉可选项":
                        // 从当前选择范围的数据验证下拉列表中加载候选项
                        LoadValidationListToTextBox(ETExcelExtensions.GetSelectionRange(true, false));
                        break;

                    case "可选项列表":
                        // 从用户指定的范围中加载候选项
                        LoadRangeValuesToTextBox(ucExcelRangeSelect候选项.SelectedRange);
                        break;
                }
            }
            catch (Exception ex)
            {
                throw new ETException("切换选项失败", "选项切换操作", ex);
            }
        }

        /// <summary>
        /// 用户选择候选项范围后的事件处理程序
        /// </summary>
        /// <param name="sender">触发事件的范围选择控件</param>
        /// <param name="er">事件参数</param>
        /// <remarks>
        /// 当用户通过范围选择控件选择了新的候选项范围后，
        /// 自动从该范围中加载所有值到候选项文本框
        /// </remarks>
        void ucExcelRangeSelect候选项_SelectionMadeEvent(object sender, EventArgs er)
        {
            try
            {
                // 从用户新选择的范围中加载候选项值
                LoadRangeValuesToTextBox(ucExcelRangeSelect候选项.SelectedRange);
            }
            catch (Exception ex)
            {
                throw new ETException("加载选定范围值失败", "范围选择操作", ex);
            }
        }
    }
}