html,
body {
  height: 100%;
  padding: 0;
  margin: 0;
  border: 0;
  box-sizing: border-box;
  font-family: helvetica neue, luxi sans, dejavu sans, segoe ui, hiragino sans gb, microsoft yahei, sans-serif;
}

* {
  box-sizing: border-box;
}

input {
  vertical-align: middle;
}

input[type="checkbox"],
input[type="radio"] {
  outline: none;
  vertical-align: middle;
}

.border-container {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}

.border-column {
  -webkit-flex-flow: column nowrap;
  flex-flow: column nowrap;
}

.border-row {
  -webkit-flex-flow: row nowrap;
  flex-flow: row nowrap;
}

.border-auto {
  flex: auto;
  position: relative;
}

.border-none {
  flex: none;
}

#tabsContainer {
  border: 0;
  margin-bottom: 2px;
}

#tabsContainer>.a {
  height: 1em;
}

.tab.segment {
  padding: 0;
  margin: 0 !important;
}

iframe {
  border: 0;
  display: block;
  width: 100%;
}

#workspaceContainer {
  position: relative;
  height: 100%;
  overflow: hidden;
}

.maxwithtab>.item>label {
  max-width: 10em;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.mdi.window {
  display: flex;
  position: absolute;
  flex-flow: column;

  z-index: 100;

  box-shadow: 1px 3px 3px 0 rgba(0, 0, 0, .2), 1px 3px 15px 2px rgba(0, 0, 0, .2);
  border: none;
  border-radius: .28571429rem;
  min-width: 200px;

  max-width: 100vw;
  max-height: 100vh;
}

.mdi.window.active {
  z-index: 200;
}

.mdi.window.hidden {
  display: none;
}

.mdi.window.mini {
  height: 30px !important;
  width: 200px !important;
}

.mdi>.header {
  font-weight: bold;
  font-size: 1.1em;
  padding: 8px;
  cursor: default;
  display: flex;
}

.mdi.mini>.header {
  font-size: small;
}

.mdi>.header .title {
  flex: auto;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.mdi>.header .btns {
  white-space: nowrap;
}

.mdi>.header .btns .icon {
  cursor: pointer;
  font-size: small;
}

.mdi.mini>.header .btns .minimize.icon,
.mdi.mini>.header .btns .maximize.icon {
  display: none;
}

.mdi>.mdicontent {
  flex-grow: 1;
  padding: 5px;
  display: flex;
  width: unset;
  height: unset;
  overflow: auto;
  position: relative;
}

.mdi.mini>.mdicontent {
  display: none;
}

.mdi>.mdicontent iframe {
  flex-grow: 1;
  min-height: 100%;
  height: auto !important;
}

.mdi.ui-draggable-dragging>.mdicontent {
  pointer-events: none;
}

.mdi.fullscreen {
  width: 95%;
}

.mdi.large {
  width: 80%;
}

.mdi.small {
  width: 70%;
}

.mdi.tiny {
  width: 50%;
}

.mdi.mini {
  width: 35%;
}

.mdi.full {
  max-height: unset !important;
  max-width: unset !important;
}

.mdidimmer {
  z-index: 10;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  position: fixed;
  pointer-events: none;
  justify-content: center;
  align-items: center;
}

.mdi .actions {
  text-align: right;
}

.mdi .actions>.button {
  margin-left: .75em;
}

/*semantic*/
.ui.form.compact .fields {
  margin: 0 0 0.6em;
}

.ui.form.compact input:not([type]),
.ui.form.compact input[type=date],
.ui.form.compact input[type=datetime-local],
.ui.form.compact input[type=email],
.ui.form.compact input[type=file],
.ui.form.compact input[type=number],
.ui.form.compact input[type=password],
.ui.form.compact input[type=search],
.ui.form.compact input[type=tel],
.ui.form.compact input[type=text],
.ui.form.compact input[type=time],
.ui.form.compact input[type=url] {
  padding: 0.4em 0.4em;
}

.ui.form.compact .search.selection.dropdown>input.search {
  padding: 0.4em 0.4em;
}

.ui.form.compact .multiple.search.selection.dropdown>input.search {
  position: absolute;
  line-height: 1.21428571em;
  margin: 0 0;
  width: 100%;
  z-index: -1;
}

.ui.form.compact .multiple.search.dropdown>.text {
  position: relative;
  left: 1px;
  margin: 0 0;
}

.ui.form.compact .selection.dropdown {
  padding: 0.4em;
  min-height: unset;
  line-height: 1.21428571em;
}

.ui.form.compact .selection.dropdown>.delete.icon,
.ui.form.compact .ui.selection.dropdown>.dropdown.icon,
.ui.form.compact .ui.selection.dropdown>.search.icon {
  padding: 0.4em;
}

.ui.form.compact .loading.selection.dropdown>i.icon {
  padding: 1.2em !important;
}

.ui.form .disabled.field,
.ui.form .disabled.fields .field,
.ui.form .field :disabled {
  pointer-events: auto;
}

.ui.form input[type=checkbox] {
  vertical-align: middle;
}

.ui.checkbox {
  vertical-align: middle;
}

.ui.modal>.content {
  padding: 10px;
}

.ui.modal>.header {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.ui.table:not(.dataTable) {
  margin: 0;
}

.ui.table:not(.dataTable) thead th {
  padding-top: 4px;
  padding-bottom: 4px;
  font-size: 12px;
}

.ui.table:not(.dataTable) thead th>.icon.col {
  display: none;
  cursor: pointer;
}

.ui.table:not(.dataTable) thead th:hover>.icon.col {
  display: inline;
}

.ui.table thead th>.resizer {
  width: 6px;
  height: 100%;
  position: absolute;
  display: inline-block;
  right: -3px;
  top: 0;
  cursor: col-resize;
  z-index: 10;
}

.ui.table:not(.dataTable) td {
  word-break: break-all;
}

.ui.table tbody tr td.editable {
  padding: .2em .3em;
}

.ui.table tbody tr td.editable .inner-input {
  width: 100%;
  height: 100%;
}

.ui.table tbody tr td.editable .inner-input input {
  padding: .5em .8em;
}

.ui.table tbody tr td.editable .inner-dictCombo {
  width: 100%;
  height: 100%;
}

.ui.table tbody tr td.editable .inner-dictCombo .selection {
  width: 100%;
  height: 100%;
  padding: 0;
}

.ui.table tbody tr td.editable .inner-dictCombo .selection .text {
  padding: .5em .8em;
  min-height: 2.1em;
}

.ui.table tbody tr td.editable .inner-objCombo {
  width: 100%;
  height: 100%;
}

.ui.table tbody tr td.editable .inner-objCombo .selection {
  width: 100%;
  height: 100%;
  min-height: 0;
  padding: 0;
}

.ui.table tbody tr td.editable .inner-objCombo .selection .text {
  padding: .5em .8em;
  min-height: 2.1em;
}

td:not(.editable).cell-edit-dirty::after {
  content: '*';
  float: right;
}

.ui.attached.menu {
  margin: 0;
}

.ui.grid:not(.pure)>.column {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.ui.modal>.content {
  width: auto;
}

.ui.modal .scrolling.content {
  max-height: calc(60vh);
}

.ui.divider {
  color: gray;
}

.ui.dropdown:not(.pure) .item .left.dropdown.icon,
.ui.dropdown:not(.pure) .left.menu .item .dropdown.icon {
  float: right !important;
}

.ui.dropdown:not(.pure) .item .left.dropdown.icon,
.ui.dropdown:not(.pure) .left.menu .item .dropdown.icon {
  float: right !important;
}

/* 
.ui.form .ui.dropdown>.text {
  width:100%;
  text-overflow:ellipsis;
  white-space: nowrap;
  overflow: hidden;
  display: list-item;
} */

.ui.dropdown>.text>[class*="left floated"],
.ui.dropdown .menu .item>[class*="left floated"] {
  float: right !important;
}

.ui.dropdown:not(.pure) .item .left.dropdown.icon:before,
.ui.dropdown:not(.pure) .left.menu .item .dropdown.icon:before {
  content: "\f0da" !important;
}

#menubar .ui.menu .ui.dropdown .menu>.item {
  width: 100%;
}

#menubar div[data-has-submenu]>div.left>span:only-child {
  margin-left: 1.38em;
}

.ui.selection.dropdown:not(.pure) .menu {
  min-width: 100%;
  width: 100%;
}


.ui.accordion .title:not(.ui) {
  padding: 4px 0;
  color: inherit;
}

.ui.loading.button:before {
  box-sizing: border-box;
}

.ui.loading.button:after {
  box-sizing: border-box;
}

.ui.dimmer {
  background-color: rgba(0, 0, 0, .25);
}

.dimmer.dimmer-invalid-mask {
  display: none !important;
}

i.icon.fa {
  /* remove i.icon.fa:before{content:"\f2b4"} in semantic-ui */
  font-family: "Font Awesome 5 Free";
  font-weight: bold;
}

.collapseToggleBtn {
  z-index: 5;
  cursor: pointer;
  position: absolute;
  top: 48%;
  right: -20px;
  width: 23px;
  height: 48px;
  background: rgba(255, 255, 255, 0.9) url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAUCAQAAAAXDMSnAAAAi0lEQVR4AX3JQcqBURQG4O/+9WNG30D3vOfSDTuQsgcZyBakZANSzMVMBme3zsBI5/VMn4ZKLP5ki1E4tYejWpilxVUtzOEUD68odYmXR5BJNp/4zllXD2phllYvamHmirsayUkfJ5ruHzueTldC08kcT5YOY9xYujqQM03XKXuaLmEtNF1e1Nz89gbL+0do6OEwRwAAAABJRU5ErkJggg==) 7px center/7px 10px no-repeat;
  border-left: 1px solid #D4D4D4;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.3);
  display: none !important;
}

aside.collapsed {
  width: 0 !important;
}

aside.collapsed> :not(.collapseToggleBtn) {
  visibility: hidden;
}

aside.collapsed .collapseToggleBtn {
  transform: scaleX(-1);
  display: block !important;
}

aside:hover .collapseToggleBtn {
  display: block !important;
}

aside.west .collapseToggleBtn {
  transform: scaleX(-1);
  right: none;
  left: -20px;
}

aside.west.collapsed .collapseToggleBtn {
  transform: none;
}

/* y轴  */
.collapseToggleBtn-y {
  z-index: 5;
  cursor: pointer;
  position: absolute;
  bottom: 0px;
  right: 48%;
  width: 48px;
  height: 23px;
  background: rgba(255, 255, 255, 0.9) url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAOCAYAAAAvxDzwAAAAAXNSR0IArs4c6QAAAVlpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IlhNUCBDb3JlIDUuNC4wIj4KICAgPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4KICAgICAgPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIKICAgICAgICAgICAgeG1sbnM6dGlmZj0iaHR0cDovL25zLmFkb2JlLmNvbS90aWZmLzEuMC8iPgogICAgICAgICA8dGlmZjpPcmllbnRhdGlvbj4xPC90aWZmOk9yaWVudGF0aW9uPgogICAgICA8L3JkZjpEZXNjcmlwdGlvbj4KICAgPC9yZGY6UkRGPgo8L3g6eG1wbWV0YT4KTMInWQAAAThJREFUOBGtkr9OhEAQxgEVQsKfUkKMMcQXsDnfwOez8hrPhzBnbayNlRbaYVjgbFYSICHKcfcNcYyudyZyTjOZmW9+fLuLpv1z6ODpYRjubco1TfM9juPZNkALxMgwjJOh0K7rtKZpzrE/2yJIVVVPruvu67q+OxA6zfP8mnaND0AHyxM4Tf8KhIm7LMumvNc7pKIoitb3/XtAjyGyWPBbxlGfATuDZs46dtjXQgjZtu0Y0JYF6zI0hWVZBHv7qvl0yM26rl89z5Ooj7inZsAIcpokyYs6+wEkQVmWwnGcHRz9UF2gF0VM8AiP6ozqlUAa0MvDKf2fAdUc+Mgl7u2GazV/u0NluLBt+wI9wX0c9TZN0yuuV+W1DkkspZzj5R8AGqGEsWyM3J+Z5oMjCIKDKIr8wYBNFpeDq3xGZ2i9qQAAAABJRU5ErkJggg==) 19px center/9px 6px no-repeat;
  border-left: 1px solid #D4D4D4;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.3);
  display: none !important;
}

aside.collapsed-y {
  height: 0 !important;
}

aside.collapsed-y> :not(.collapseToggleBtn-y) {
  visibility: hidden;
}

aside.collapsed-y .collapseToggleBtn-y {
  transform: scaleY(-1);
  display: block !important;
}

aside:hover .collapseToggleBtn-y {
  display: block !important;
}

aside.west .collapseToggleBtn-y {
  transform: scaleX(-1);
  right: none;
  left: -20px;
}

aside.west.collapsed-y .collapseToggleBtn-y {
  transform: none;
}

._tabitem>label {
  max-width: 10rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/** scroll bar */
::-webkit-scrollbar {
  -webkit-appearance: none;
  width: 8px !important;
  height: 8px !important;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 0px;
}

::-webkit-scrollbar-thumb {
  cursor: pointer;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.25);
  -webkit-transition: color 0.2s ease;
  transition: color 0.2s ease;
}

::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(0, 0, 0, 0.15);
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(128, 135, 139, 0.8);
}

/** aside collapsed */
aside.collapsed .ui.vertical.menu .item>.menu {
  margin: 0;
}

/** forms */
.form .field>label {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: capitalize !important;
}

/** loading progress */
#main_progress_loading {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;

  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;
}

#main_progress_loading>div {
  font-weight: bold;
  width: 200px;
  text-align: center;
}

.ui.form .required.fields:not(.grouped)>.field>label:before,
.ui.form .required.fields.grouped>label:before,
.ui.form .required.field>label:before,
.ui.form .required.fields:not(.grouped)>.field>.checkbox:before,
.ui.form .required.field>.checkbox:before {
  margin: -0.2em 0em 0em 0.2em;
  content: '*';
  color: #DB2828;
}

.ui.form .required.fields:not(.grouped)>.field>label:after,
.ui.form .required.fields.grouped>label:after,
.ui.form .required.field>label:after,
.ui.form .required.fields:not(.grouped)>.field>.checkbox:after,
.ui.form .required.field>.checkbox:after {
  margin: 0;
  content: '';
  color: #DB2828;
}

table._maintable {
  min-height: 60px;
}

table._maintable thead th {
  position: sticky;
  top: 0;
}

table._maintable .suitablerow .colhead {
  font-weight: bold;
  font-size: smaller;
  text-align: center;
  z-index: 1;
}

tr.sui-row-checked {}

td.sui-cell-selected {
  background-color: #5ea8d5 !important;
  border: 1px dashed white;
}

.dropdown ._dp_extra_icons {
  display: none;
  position: absolute;
  right: 2.2em;
  opacity: .8;
  z-index: 4;

  /* top: .84615385em; */
  line-height: 1.21428571em;
  /* margin: -.84615385em;
  padding: 0.4em; */
  height: auto;
}

.ui.form.compact .dropdown ._dp_extra_icons {
  top: .84615385em;
  margin: -.84615385em;
  padding: 0.4em;
}

.ui.form.compact .dropdown ._dp_icon {
  z-index: 4;
}

.ui.form.compact .dropdown ._dp_stub {
  position: relative;
  display: inline-block;
  pointer-events: none;
}

.ui.form.compact .dropdown ._dp_text {
  position: absolute;
  width: 100%;
  padding: 0 0.4em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown:hover ._dp_extra_icons {
  display: block;
}

._suisearch .search.icon {
  display: none !important;
  cursor: pointer;
}

._suisearch:hover .search.icon {
  display: block !important;
}

._suisearch.loading .search.icon {
  display: block !important;
}

._suisearch>.results {
  max-height: 300px;
  overflow: auto;
}

._suisearch>.results>.result {
  font-size: smaller !important;
}

._gridMenu {
  background-color: #fcf8f3;
  border: 1px solid #ece8f3;
}

._gridMenu .ui.attached.menu {
  width: unset;
}

.ui.form .field.requiredwarn input,
.ui.form .fields.requiredwarn .field select,
.ui.form .fields.requiredwarn .field textarea,
.ui.form .field.requiredwarn div.dropdown.selection {
  background: #fff6f6;
  border-color: #e0b4b4;
  color: #9f3a38;
  box-shadow: none;
}

.scroll-heilight-item {
  background-color: #65BCF1;
}

@keyframes glow {
  0% {
    background-color: #FCFCFD;
  }

  50% {
    background-color: #65BCF1;
  }

  100% {
    background-color: #FCFCFD;
  }
}

.ui.form .field {
  position: relative;
}

.ui.form .field .field-gencode-btn {
  position: absolute;
  right: 16px;
  top: 5px;
  cursor: pointer;
  display: none;
}

.ui.form .field:hover .field-gencode-btn {
  display: block;
}

.ui.form .field .field-act-bar {
  position: absolute;
  right: 16px;
  top: 5px;
  cursor: pointer;
  display: none;
}

.ui.form .field.disabled .field-act-bar {
  pointer-events: none;
}

.ui.form .field.disabled .icon.dropdown {
  pointer-events: none;
}

.ui.form .field:hover .field-act-bar {
  display: block;
}

.ui.form .field .field-act-bar.active {
  display: block;
}

.ui.accordion .title>._bar>* {
  padding-left: 2px;
}

._suiitem #_copybtn {
  padding-left: 10px;
}

._objpopup.ui.dropdown .menu.treemenu {
  min-width: 140px;
  max-width: 300px;
  box-shadow: 5px 5px 5px gray;
}

.menu.treemenu .header>div {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 90%;
}

.ui.dropdown .menu.treemenu .item {
  padding: .5em 1.15384615rem !important;
}

.ui.multiple.dropdown._dropdown>.label {
  padding: 2px 4px;
  margin: 1px 4px;
  font-size: 0.9em;
}

.bar_action {
  cursor: pointer;
  margin: 2px;
}

.bar_action>span {
  font-size: smaller;
}

.form_search_bar {
  /* width: 100%;
  padding: 0 20px 5px 0px;
  text-align: right; */
  display: flex;
  border-top: 1px solid rgba(0, 0, 0, .12);
  padding-top: 1rem;
  max-height: 37px;
}

.form_search_bar>button:last-child {
  margin-right: 0;
}

.form_search_bar .form_search_option_bar {
  /* padding: 5px 15px;
  position: absolute;
  left: 0;
  font-size: smaller; */
  flex: 1 1 auto;
  display: flex;
  align-items: center;
  font-size: smaller;
  gap: 4px;
  color: rgba(0, 0, 0, 0.65);
}

.form_search_bar .form_search_option_bar>label[for='ignore_case'] {
  flex: 0 1 7em;
  width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

.form_search_bar .form_search_option_bar>label[for='ignore_case']:hover {
  color: rgba(0, 0, 0, 1)
}

.form_search_bar .form_search_option_bar>label[for='ignore_case']:active {
  color: rgba(0, 0, 0, 0.85)
}

.tree-node-checked-common {
  background-color: #65BCF1;
  border: 1px dashed gray;
  border-radius: 2px;
}

input[type=checkbox]:checked._itemcheck_notall {
  filter: grayscale(1);
}

._suitree-title:hover {
  /* background-color: #B2E1FF; */
  background: #EFEFEF;
}

.sui-dragover-item {
  cursor: inherit;
  border-bottom: 1px dashed #ccc;
  box-sizing: border-box;
  animation: hold-higther 200ms ease-in;
}

.sidebar-transition:not([data-resize]) {
  transition: width ease-in-out .35s;
}

#workspaceContainer>.ui.menu>.item {
  padding: 0 1.75em;
}

/* g5 primary classes */

.g5.right.aligned {
  text-align: right;
}

.g5.center.aligned {
  text-align: center;
}

.field-center {
  display: flex;
  align-items: center;
}


#frametree ._suitree-title {
  display: flex;
  align-items: center;
  border-radius: 2px;
}

#frametree ._suitree-title ._label {
  flex: 1 1 auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  width: 0;
}

#frametree ._suitree-title ._bar {
  margin-right: 8px;
}

#frametree ._suitree-title ._bar>*:not(:active) {
  transition: color ease .35s;
}

#frametree ._suitree-title ._bar>* {
  color: rgba(0, 0, 0, 0.45);
}

#frametree ._suitree-title ._bar>*:hover {
  color: rgba(0, 0, 0, 0.85);
}

#frametree ._suitree-title ._bar>*:active {
  color: rgb(0, 113, 188);
}

#frametree ._node_content {
  padding: 0px 0px 0px .5rem !important;
}

#frametree ._node_content>.accordion {
  border-left: 1px solid transparent;
  padding-left: .5rem;
}

#frametree:hover .accordion {
  border-left-color: rgba(0, 0, 0, .12);
}

#_queryInput {
  transition: width .15s;
}

#gridtree ._suitree-title {
  display: flex;
  align-items: center;
  border-radius: 2px;
}

#gridtree ._suitree-title ._label {
  flex: 1 1 auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  width: 0;
}

#gridtree ._suitree-title ._bar {
  margin-right: 8px;
}

#gridtree ._suitree-title ._bar>*:not(:active) {
  transition: color ease .35s;
}

#gridtree ._suitree-title ._bar>* {
  color: rgba(0, 0, 0, 0.45);
}

#gridtree ._suitree-title ._bar>*:hover {
  color: rgba(0, 0, 0, 0.85);
}

#gridtree ._suitree-title ._bar>*:active {
  color: rgb(0, 113, 188);
}

#gridtree ._node_content {
  padding: 0px 0px 0px .5rem !important;
}

#gridtree ._node_content>.accordion {
  border-left: 1px solid transparent;
  padding-left: .5rem;
}

#gridtree:hover .accordion {
  border-left-color: rgba(0, 0, 0, .12);
}

/* global search  */

.global-search {
  display: flex;
}

.global-search>.global-search-items {
  width: 122px;
}

.global-search>.global-search-items:not(:last-child) {
  border-right: 1px solid rgba(0, 0, 0, .12);
}

.global-search-item {
  display: flex;
  align-items: center;
  padding: 0 .5em;
  border-radius: 2px;
  cursor: pointer;
}

.global-search-item:hover {
  background-color: #B2E1FFFF;
}

.global-search-item>.global-search-item-title {
  font-weight: bold;
  color: #0E6EB8;
}

.global-search-item>.global-search-item-subtitle {
  color: #000000;
  flex: 1 1 auto;
  width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.global-search-item>.global-search-item-description {
  font-weight: bolder;
}

.global-search-item.active,
.global-search-item.active>.global-search-item-subtitle {
  color: #B03060;
}

.global-search-item:active {
  background-color: #B2E1FFAA;
}

.grid-select-container {
  height: 60vh;
  display: flex;
  flex-direction: column;
}

.grid-search-popup.ui.popup {
  z-index: 1000;
}

.hover_input {
  opacity: 0.6;
}

.hover_input:hover {
  opacity: 1;
}

#message-container.message-container {
  position: absolute;
  z-index: 1000;
  max-height: 80vh;
  overflow: hidden;

  right: 0;
  bottom: 0;
  padding: 5px;
}

.sui-alert-content {
  max-height: 80vh;
  overflow: auto;
}