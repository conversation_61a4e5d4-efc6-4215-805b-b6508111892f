using ET;
using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;

namespace HyAssistant
{
    public partial class FileCopierConfigForm : Form
    {
        readonly string _iniFilePath;
        readonly ETIniFile _iniFile;
        readonly Dictionary<string, string> _sectionUuids = new Dictionary<string, string>(); // 存储显示名到UUID的映射
        readonly Dictionary<string, Control> _sectionControls = new Dictionary<string, Control>();
        const string SECTION_PREFIX = "filesync-"; // 配置组名前缀常量
        bool _suppressReload = false; // 添加标记以防止保存时重新加载

        public FileCopierConfigForm()
        {
            try
            {
                InitializeComponent();
                _iniFilePath = ETConfig.GetConfigDirectory("filecopier.ini");

                // 简化初始化逻辑（同样的结果）
                _iniFile = new ETIniFile(_iniFilePath);

                LoadSections();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化配置表单时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        void LoadSections()
        {
            listBoxSections.Items.Clear();
            _sectionUuids.Clear();
            _sectionControls.Clear();

            // 如果配置文件不存在，直接返回
            if (!File.Exists(_iniFilePath))
            {
                return;
            }

            foreach (string sectionName in _iniFile.GetSectionNames())
            {
                if (sectionName.StartsWith(SECTION_PREFIX, StringComparison.OrdinalIgnoreCase))
                {
                    string displayName = _iniFile.GetValue(sectionName, "name", sectionName);
                    _sectionUuids[displayName] = sectionName;
                    listBoxSections.Items.Add(displayName);
                }
            }
        }

        void listBoxSections_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listBoxSections.SelectedItem == null) return;

            // 如果标记为true，不执行加载操作
            if (_suppressReload) return;

            string selectedDisplayName = listBoxSections.SelectedItem.ToString();
            string selectedUuid = _sectionUuids[selectedDisplayName];
            LoadSectionData(selectedUuid);
        }

        void LoadSectionData(string sectionName)
        {
            try
            {
                textBoxName.Text = _iniFile.GetValue(sectionName, "name", string.Empty);
                textBoxSourcePaths.Text = _iniFile.GetValue(sectionName, "SourcePaths", string.Empty).Replace("|", Environment.NewLine);
                textBoxTargetPath.Text = _iniFile.GetValue(sectionName, "TargetPath", string.Empty);
                textBoxFilePattern.Text = _iniFile.GetValue(sectionName, "FilePattern", "*.*").Replace("|", Environment.NewLine);

                // 处理IntervalInMinutes，确保它是有效的数字
                int intervalValue;
                if (!int.TryParse(_iniFile.GetValue(sectionName, "IntervalInMinutes", "0"), out intervalValue))
                {
                    intervalValue = 0;
                }
                numericUpDownInterval.Value = intervalValue;

                // 处理日期时间值，使用固定格式并提供默认值
                DateTime defaultDate = DateTime.Now;
                DateTime creationTime, modificationTime;

                if (!DateTime.TryParse(_iniFile.GetValue(sectionName, "CreationTimeLimit", defaultDate.ToString("yyyy-MM-dd HH:mm:ss")), out creationTime))
                {
                    creationTime = defaultDate;
                }

                if (!DateTime.TryParse(_iniFile.GetValue(sectionName, "ModificationTimeLimit", defaultDate.ToString("yyyy-MM-dd HH:mm:ss")), out modificationTime))
                {
                    modificationTime = defaultDate;
                }

                dateTimePickerCreationTime.Value = creationTime;
                dateTimePickerModificationTime.Value = modificationTime;

                checkBoxCreateDateTimeSubDir.Checked = _iniFile.GetValue(sectionName, "CreateDateTimeSubDir", "0") == "1";
                checkBoxUseCreationTime.Checked = _iniFile.GetValue(sectionName, "UseCreationTimeForSubDir", "0") == "1";
                checkBoxEnable.Checked = _iniFile.GetValue(sectionName, "enable", "0") == "1";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载配置数据时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                ClearForm();
            }
        }

        void buttonSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (listBoxSections.SelectedItem == null) return;

                // 验证必填字段
                if (string.IsNullOrWhiteSpace(textBoxName.Text))
                {
                    MessageBox.Show("请输入配置名称", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    textBoxName.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(textBoxSourcePaths.Text))
                {
                    MessageBox.Show("请输入源路径", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    textBoxSourcePaths.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(textBoxTargetPath.Text))
                {
                    MessageBox.Show("请输入目标路径", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    textBoxTargetPath.Focus();
                    return;
                }

                string selectedDisplayName = listBoxSections.SelectedItem.ToString();
                string selectedUuid = _sectionUuids[selectedDisplayName];
                string newDisplayName = textBoxName.Text.Trim();

                // 检查名称是否重复
                if (newDisplayName != selectedDisplayName && _sectionUuids.ContainsKey(newDisplayName))
                {
                    MessageBox.Show("配置名称已存在，请使用其他名称", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    textBoxName.Focus();
                    return;
                }

                // 设置标记防止重新加载
                _suppressReload = true;

                // 更新显示名称
                if (newDisplayName != selectedDisplayName)
                {
                    _sectionUuids.Remove(selectedDisplayName);
                    _sectionUuids[newDisplayName] = selectedUuid;
                    int selectedIndex = listBoxSections.SelectedIndex;
                    listBoxSections.Items[selectedIndex] = newDisplayName;
                }

                SaveSectionData(selectedUuid);

                // 确保配置文件目录存在
                string configDir = Path.GetDirectoryName(_iniFilePath);
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }

                _iniFile.IniWriteFile();

                // 重置标记
                _suppressReload = false;

                MessageBox.Show("配置已保存", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                // 确保在异常时也重置标记
                _suppressReload = false;
                MessageBox.Show($"保存配置时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        void SaveSectionData(string sectionName)
        {
            try
            {
                _iniFile.SetValue(sectionName, "name", textBoxName.Text.Trim());
                _iniFile.SetValue(sectionName, "SourcePaths", textBoxSourcePaths.Text.Replace(Environment.NewLine, "|").TrimEnd('|'));
                _iniFile.SetValue(sectionName, "TargetPath", textBoxTargetPath.Text.Trim());
                _iniFile.SetValue(sectionName, "FilePattern", string.IsNullOrWhiteSpace(textBoxFilePattern.Text) ? "*.*" : textBoxFilePattern.Text.Replace(Environment.NewLine, "|").TrimEnd('|'));
                _iniFile.SetValue(sectionName, "IntervalInMinutes", numericUpDownInterval.Value.ToString());

                // 使用固定格式保存日期时间值，以确保一致性
                string creationTimeFormat = dateTimePickerCreationTime.Value.ToString("yyyy-MM-dd HH:mm:ss");
                string modificationTimeFormat = dateTimePickerModificationTime.Value.ToString("yyyy-MM-dd HH:mm:ss");

                _iniFile.SetValue(sectionName, "CreationTimeLimit", creationTimeFormat);
                _iniFile.SetValue(sectionName, "ModificationTimeLimit", modificationTimeFormat);
                _iniFile.SetValue(sectionName, "CreateDateTimeSubDir", checkBoxCreateDateTimeSubDir.Checked ? "1" : "0");
                _iniFile.SetValue(sectionName, "UseCreationTimeForSubDir", checkBoxUseCreationTime.Checked ? "1" : "0");
                _iniFile.SetValue(sectionName, "enable", checkBoxEnable.Checked ? "1" : "0");
            }
            catch (Exception ex)
            {
                throw new Exception($"保存节点数据时出错: {ex.Message}", ex);
            }
        }

        void buttonAdd_Click(object sender, EventArgs e)
        {
            try
            {
                // 设置标记防止重新加载
                _suppressReload = true;

                string newDisplayName = $"新配置{_sectionUuids.Count + 1}";

                // 检查名称是否重复
                while (_sectionUuids.ContainsKey(newDisplayName))
                {
                    newDisplayName = $"新配置{_sectionUuids.Count + 1}";
                }

                string newUuid = $"{SECTION_PREFIX}{Guid.NewGuid().ToString("N")}";
                _sectionUuids[newDisplayName] = newUuid;
                listBoxSections.Items.Add(newDisplayName);

                // 使用与其他地方一致的格式初始化新配置
                _iniFile.SetValue(newUuid, "name", newDisplayName);
                _iniFile.SetValue(newUuid, "enable", "1");
                _iniFile.SetValue(newUuid, "CreateDateTimeSubDir", "0");
                _iniFile.SetValue(newUuid, "IntervalInMinutes", "5");

                // 使用与SaveSectionData一致的日期时间格式
                string nowFormatted = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                _iniFile.SetValue(newUuid, "CreationTimeLimit", nowFormatted);
                _iniFile.SetValue(newUuid, "ModificationTimeLimit", nowFormatted);

                _iniFile.SetValue(newUuid, "FilePattern", "*.*");
                _iniFile.SetValue(newUuid, "UseCreationTimeForSubDir", "1");

                // 确保配置文件目录存在
                string configDir = Path.GetDirectoryName(_iniFilePath);
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }

                _iniFile.IniWriteFile();

                // 选择新项后再重置标记
                listBoxSections.SelectedItem = newDisplayName;

                // 手动加载数据后重置标记
                LoadSectionData(newUuid);
                _suppressReload = false;
            }
            catch (Exception ex)
            {
                // 确保在异常时也重置标记
                _suppressReload = false;
                MessageBox.Show($"添加新配置时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        void buttonDelete_Click(object sender, EventArgs e)
        {
            if (listBoxSections.SelectedItem == null) return;

            string selectedDisplayName = listBoxSections.SelectedItem.ToString();
            string selectedUuid = _sectionUuids[selectedDisplayName];

            try
            {
                // 设置标记防止重新加载
                _suppressReload = true;

                _iniFile.DeleteSection(selectedUuid);
                _sectionUuids.Remove(selectedDisplayName);
                int selectedIndex = listBoxSections.SelectedIndex;
                listBoxSections.Items.Remove(selectedDisplayName);
                _iniFile.IniWriteFile();

                // 如果删除后还有项目，选择下一个项目
                if (listBoxSections.Items.Count > 0)
                {
                    int newIndex = Math.Min(selectedIndex, listBoxSections.Items.Count - 1);
                    listBoxSections.SelectedIndex = newIndex;

                    // 手动加载新选中项的数据
                    string newSelectedDisplayName = listBoxSections.Items[newIndex].ToString();
                    string newSelectedUuid = _sectionUuids[newSelectedDisplayName];
                    LoadSectionData(newSelectedUuid);
                }
                else
                {
                    ClearForm();
                }

                // 重置标记
                _suppressReload = false;
            }
            catch (Exception ex)
            {
                // 确保在异常时也重置标记
                _suppressReload = false;
                MessageBox.Show($"删除配置时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        void ClearForm()
        {
            textBoxName.Clear();
            textBoxSourcePaths.Clear();
            textBoxTargetPath.Clear();
            textBoxFilePattern.Clear();
            numericUpDownInterval.Value = 0;
            dateTimePickerCreationTime.Value = DateTime.Now;
            dateTimePickerModificationTime.Value = DateTime.Now;
            checkBoxCreateDateTimeSubDir.Checked = false;
            checkBoxUseCreationTime.Checked = false;
            checkBoxEnable.Checked = false;
        }

        void buttonBrowseSource_Click(object sender, EventArgs e)
        {
            using (FolderBrowserDialog dialog = new FolderBrowserDialog())
            {
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    if (string.IsNullOrEmpty(textBoxSourcePaths.Text))
                    {
                        textBoxSourcePaths.Text = dialog.SelectedPath;
                    }
                    else
                    {
                        textBoxSourcePaths.Text += $"{Environment.NewLine}{dialog.SelectedPath}";
                    }
                }
            }
        }

        void buttonBrowseTarget_Click(object sender, EventArgs e)
        {
            using (FolderBrowserDialog dialog = new FolderBrowserDialog())
            {
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    textBoxTargetPath.Text = dialog.SelectedPath;
                }
            }
        }
    }
}