using ET;
using SharpCompress.Archives;
using SharpCompress.Common;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace HyAssistant
{
    /// <summary>
    /// 文件提取服务类，负责文件解压缩处理逻辑
    /// </summary>
    /// <remarks>
    /// 模块功能：文件自动提取与解压缩服务
    /// 作用：根据配置自动监控指定目录，提取并解压符合条件的压缩文件，支持定时执行和重复文件处理
    /// 
    /// 主要功能：
    /// 1. 支持多源路径和多文件模式的文件监控
    /// 2. 基于时间条件（创建时间、修改时间）的文件筛选
    /// 3. 自动解压缩文件到指定目标路径
    /// 4. 自动检测并删除重复内容的文件夹
    /// 5. 支持定时执行任务，可设置执行间隔
    /// 
    /// 执行逻辑：
    /// 1. 读取配置文件，获取监控任务信息
    /// 2. 验证源路径和目标路径的有效性和权限
    /// 3. 根据配置的时间间隔定期执行文件处理
    /// 4. 查找符合条件的文件并解压到目标路径
    /// 5. 计算解压后文件夹的大小和文件数量
    /// 6. 检测并删除内容相似的重复文件夹
    /// 
    /// 注意事项：
    /// 1. 使用缓存机制减少重复计算文件夹大小和文件数量
    /// 2. 实现了防路径穿越攻击的安全措施
    /// 3. 支持任务取消和资源释放
    /// 4. 使用异步操作提高性能和响应性
    /// </remarks>
    public class FileExtractService : IDisposable
    {
        /// <summary>
        /// 文件夹大小缓存，用于避免重复计算
        /// 键：文件夹路径
        /// 值：Tuple包含（文件夹大小，文件数量，缓存时间）
        /// </summary>
        readonly ConcurrentDictionary<string, Tuple<long, int, DateTime>> _folderInfoCache =
            new ConcurrentDictionary<string, Tuple<long, int, DateTime>>(StringComparer.OrdinalIgnoreCase);

        /// <summary>
        /// 缓存过期时间（分钟）
        /// </summary>
        const int CACHE_EXPIRY_MINUTES = 30;

        /// <summary>
        /// 标识是否正在处理文件
        /// </summary>
        bool _isProcessing = false;

        /// <summary>
        /// 取消令牌源，用于取消正在执行的任务
        /// </summary>
        CancellationTokenSource _cancellationTokenSource;

        /// <summary>
        /// 处理任务集合，键为配置节名称，值为对应的处理任务
        /// </summary>
        readonly Dictionary<string, Task> _processingTasks = new Dictionary<string, Task>();

        /// <summary>
        /// 初始化文件提取服务
        /// </summary>
        public FileExtractService()
        {
            _cancellationTokenSource = new CancellationTokenSource();
        }

        /// <summary>
        /// 验证路径的有效性和权限
        /// </summary>
        /// <param name="sourcePaths">源路径数组</param>
        /// <param name="targetPath">目标路径</param>
        /// <returns>如果所有路径有效且具有足够权限则返回true，否则返回false</returns>
        /// <remarks>
        /// 此方法执行以下验证：
        /// 1. 检查目标路径是否存在，不存在则尝试创建
        /// 2. 测试目标路径的写入权限
        /// 3. 验证所有源路径的读取权限
        /// </remarks>
        public bool ValidatePaths(string[] sourcePaths, string targetPath)
        {
            try
            {
                // 验证目标路径
                if (!Directory.Exists(targetPath))
                {
                    try
                    {
                        Directory.CreateDirectory(targetPath);
                    }
                    catch
                    {
                        return false;
                    }
                }

                // 测试目标路径写入权限
                string testFile = Path.Combine(targetPath, $"permission_test_{Guid.NewGuid()}.tmp");
                try
                {
                    using (FileStream fs = File.Create(testFile, 1, FileOptions.DeleteOnClose)) { }
                }
                catch
                {
                    return false;
                }

                // 验证源路径
                foreach (string sourcePath in sourcePaths)
                {
                    if (Directory.Exists(sourcePath))
                    {
                        try
                        {
                            // 测试读取权限
                            Directory.GetFiles(sourcePath, "*", SearchOption.TopDirectoryOnly);
                        }
                        catch
                        {
                            return false;
                        }
                    }
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 启动文件处理任务
        /// </summary>
        /// <param name="configFilePath">配置文件路径</param>
        /// <param name="logCallback">日志回调函数，参数为（日志类型，日志消息，是否重要）</param>
        /// <returns>表示异步操作的任务</returns>
        /// <remarks>
        /// 此方法从配置文件读取任务设置并启动相应的处理任务：
        /// 1. 检查是否已有处理任务在运行
        /// 2. 验证配置文件是否存在
        /// 3. 读取配置文件中的所有节
        /// 4. 对每个有效的配置节创建并启动处理任务
        /// 5. 同时支持新格式和旧格式的配置节
        /// </remarks>
        public async Task StartProcessing(string configFilePath, Action<string, string, bool> logCallback)
        {
            if (_isProcessing)
            {
                logCallback("警告", "已有处理任务正在运行，请先停止当前任务", true);
                return;
            }

            if (!File.Exists(configFilePath))
            {
                logCallback("错误", $"配置文件不存在: {configFilePath}", true);
                return;
            }

            _isProcessing = true;

            try
            {
                await Task.Run(() =>
                {
                    ETIniFile configFile = new ETIniFile(configFilePath);

                    // 获取所有节
                    List<string> sections = configFile.GetSectionNames();

                    foreach (string section in sections)
                    {
                        // 检查是否是有效的配置节，只处理带有指定前缀的节
                        if (section.StartsWith(FileExtract.SECTION_PREFIX, StringComparison.OrdinalIgnoreCase) &&
                            configFile.GetValue(section, "enable") == "1")
                        {
                            string taskName = configFile.GetValue(section, "name", string.Empty);
                            int intervalInMinutes = int.Parse(configFile.GetValue(section, "IntervalInMinutes", "0"));
                            DateTime creationTimeLimit = DateTime.Parse(
                                configFile.GetValue(section, "CreationTimeLimit", DateTime.MinValue.ToString()));
                            DateTime modificationTimeLimit = DateTime.Parse(
                                configFile.GetValue(section, "ModificationTimeLimit", DateTime.MinValue.ToString()));
                            string[] sourcePaths = configFile.GetValue(section, "SourcePaths").Split('|');
                            string[] filePatterns = configFile.GetValue(section, "FilePattern").Split('|');
                            string targetPath = configFile.GetValue(section, "TargetPath");

                            // 安全检查
                            if (!ValidatePaths(sourcePaths, targetPath))
                            {
                                logCallback("错误", $"任务 {taskName} 的路径无效或无足够权限", true);
                                continue;
                            }

                            // 创建并启动任务
                            Task task = CreateProcessingTask(
                                taskName,
                                sourcePaths,
                                filePatterns,
                                targetPath,
                                creationTimeLimit,
                                modificationTimeLimit,
                                intervalInMinutes,
                                logCallback);

                            _processingTasks[section] = task;
                        }
                        // 处理旧式配置节
                        else if (!section.StartsWith(FileExtract.SECTION_PREFIX, StringComparison.OrdinalIgnoreCase) &&
                                 configFile.GetValue(section, "enable") == "1")
                        {
                            logCallback("警告", $"检测到旧式配置节: {section}，请使用配置工具更新为新格式", true);

                            string taskName = configFile.GetValue(section, "name", string.Empty);
                            int intervalInMinutes = int.Parse(configFile.GetValue(section, "IntervalInMinutes", "0"));
                            DateTime creationTimeLimit = DateTime.Parse(
                                configFile.GetValue(section, "CreationTimeLimit", DateTime.MinValue.ToString()));
                            DateTime modificationTimeLimit = DateTime.Parse(
                                configFile.GetValue(section, "ModificationTimeLimit", DateTime.MinValue.ToString()));
                            string[] sourcePaths = configFile.GetValue(section, "SourcePaths").Split('|');
                            string[] filePatterns = configFile.GetValue(section, "FilePattern").Split('|');
                            string targetPath = configFile.GetValue(section, "TargetPath");

                            // 安全检查
                            if (!ValidatePaths(sourcePaths, targetPath))
                            {
                                logCallback("错误", $"任务 {taskName} 的路径无效或无足够权限", true);
                                continue;
                            }

                            // 创建并启动任务
                            Task task = CreateProcessingTask(
                                taskName,
                                sourcePaths,
                                filePatterns,
                                targetPath,
                                creationTimeLimit,
                                modificationTimeLimit,
                                intervalInMinutes,
                                logCallback);

                            _processingTasks[section] = task;
                        }
                    }
                }).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                logCallback("错误", $"启动处理任务时出错: {ex.Message}", true);
                _isProcessing = false;
            }
        }

        /// <summary>
        /// 取消所有正在进行的处理任务
        /// </summary>
        /// <returns>表示异步操作的任务</returns>
        /// <remarks>
        /// 此方法执行以下操作：
        /// 1. 发送取消信号给所有任务
        /// 2. 等待所有任务完成或被取消
        /// 3. 清空任务集合
        /// 4. 创建新的取消令牌源
        /// 5. 重置处理状态
        /// </remarks>
        public async Task CancelAllProcessing()
        {
            if (_cancellationTokenSource != null && !_cancellationTokenSource.IsCancellationRequested)
            {
                _cancellationTokenSource.Cancel();

                // 等待所有任务完成或被取消
                foreach (Task task in _processingTasks.Values)
                {
                    try
                    {
                        await task;
                    }
                    catch (OperationCanceledException)
                    {
                        // 正常取消，不需要处理
                    }
                    catch (Exception)
                    {
                        // 其他错误，忽略
                    }
                }

                _processingTasks.Clear();
                _cancellationTokenSource = new CancellationTokenSource();
                _isProcessing = false;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <remarks>
        /// 实现IDisposable接口，确保资源正确释放：
        /// 1. 取消所有处理任务
        /// 2. 释放取消令牌源
        /// </remarks>
        public void Dispose()
        {
            try
            {
                // 取消处理任务
                CancelAllProcessing().Wait();

                // 释放取消令牌
                if (_cancellationTokenSource != null)
                {
                    _cancellationTokenSource.Dispose();
                    _cancellationTokenSource = null;
                }
            }
            catch
            {
                // 忽略释放资源时的错误
            }
        }

        /// <summary>
        /// 创建处理任务
        /// </summary>
        /// <param name="taskName">任务名称</param>
        /// <param name="sourcePaths">源路径数组</param>
        /// <param name="filePatterns">文件模式数组</param>
        /// <param name="targetPath">目标路径</param>
        /// <param name="creationTimeLimit">文件创建时间限制</param>
        /// <param name="modificationTimeLimit">文件修改时间限制</param>
        /// <param name="intervalInMinutes">执行间隔（分钟）</param>
        /// <param name="logCallback">日志回调函数</param>
        /// <returns>创建的任务</returns>
        /// <remarks>
        /// 此方法创建一个循环执行的任务，该任务会：
        /// 1. 按照指定的时间间隔定期执行文件处理
        /// 2. 在每次执行之间等待指定的时间
        /// 3. 响应取消请求
        /// </remarks>
        Task CreateProcessingTask(
            string taskName,
            string[] sourcePaths,
            string[] filePatterns,
            string targetPath,
            DateTime creationTimeLimit,
            DateTime modificationTimeLimit,
            int intervalInMinutes,
            Action<string, string, bool> logCallback)
        {
            return Task.Run(
                async () =>
                {
                    while (!_cancellationTokenSource.Token.IsCancellationRequested)
                    {
                        await ProcessFiles(
                            taskName,
                            sourcePaths,
                            filePatterns,
                            targetPath,
                            creationTimeLimit,
                            modificationTimeLimit,
                            logCallback).ConfigureAwait(false);

                        await Task.Delay(TimeSpan.FromMinutes(intervalInMinutes), _cancellationTokenSource.Token).ConfigureAwait(false);
                    }
                },
                _cancellationTokenSource.Token);
        }

        /// <summary>
        /// 处理文件
        /// </summary>
        /// <param name="taskName">任务名称</param>
        /// <param name="sourcePaths">源路径数组</param>
        /// <param name="filePatterns">文件模式数组</param>
        /// <param name="targetPath">目标路径</param>
        /// <param name="creationTimeLimit">文件创建时间限制</param>
        /// <param name="modificationTimeLimit">文件修改时间限制</param>
        /// <param name="logCallback">日志回调函数</param>
        /// <returns>表示异步操作的任务</returns>
        /// <remarks>
        /// 此方法执行以下操作：
        /// 1. 记录任务开始执行的日志
        /// 2. 处理每个源路径下符合条件的文件
        /// 3. 记录任务完成的日志
        /// 4. 处理可能发生的异常
        /// </remarks>
        async Task ProcessFiles(
            string taskName,
            string[] sourcePaths,
            string[] filePatterns,
            string targetPath,
            DateTime creationTimeLimit,
            DateTime modificationTimeLimit,
            Action<string, string, bool> logCallback)
        {
            try
            {
                logCallback("运行", $"开始执行：{taskName}", true);

                // 处理每个源路径下的文件
                foreach (string sourcePath in sourcePaths)
                {
                    if (!Directory.Exists(sourcePath))
                    {
                        continue;
                    }

                    await ProcessFilesInPath(
                        sourcePath,
                        filePatterns,
                        targetPath,
                        creationTimeLimit,
                        modificationTimeLimit,
                        logCallback).ConfigureAwait(false);
                }

                logCallback("完成", $"{taskName}", true);
            }
            catch (OperationCanceledException)
            {
                logCallback("信息", $"{taskName} 任务已取消", true);
            }
            catch (Exception ex)
            {
                logCallback("错误", $"{taskName} 任务执行出错：{ex.Message}", true);
            }
        }

        /// <summary>
        /// 处理指定路径下的文件
        /// </summary>
        /// <param name="sourcePath">源路径</param>
        /// <param name="filePatterns">文件模式数组</param>
        /// <param name="targetPath">目标路径</param>
        /// <param name="creationTimeLimit">文件创建时间限制</param>
        /// <param name="modificationTimeLimit">文件修改时间限制</param>
        /// <param name="logCallback">日志回调函数</param>
        /// <returns>表示异步操作的任务</returns>
        /// <remarks>
        /// 此方法执行以下操作：
        /// 1. 对每个文件模式搜索匹配的文件
        /// 2. 处理每个匹配的文件
        /// 3. 处理可能发生的异常
        /// 4. 响应取消请求
        /// </remarks>
        async Task ProcessFilesInPath(
            string sourcePath,
            string[] filePatterns,
            string targetPath,
            DateTime creationTimeLimit,
            DateTime modificationTimeLimit,
            Action<string, string, bool> logCallback)
        {
            foreach (string filePattern in filePatterns)
            {
                try
                {
                    string[] files = Directory.GetFiles(sourcePath, filePattern, SearchOption.AllDirectories);

                    foreach (string file in files)
                    {
                        if (_cancellationTokenSource.Token.IsCancellationRequested)
                        {
                            return;
                        }

                        try
                        {
                            await ProcessSingleFile(
                                file,
                                targetPath,
                                creationTimeLimit,
                                modificationTimeLimit,
                                logCallback).ConfigureAwait(false);
                        }
                        catch (Exception ex)
                        {
                            logCallback("错误", $"处理文件 {file} 时出错：{ex.Message}", false);
                        }
                    }
                }
                catch (Exception ex)
                {
                    logCallback("错误", $"搜索文件模式 {filePattern} 在 {sourcePath} 时出错: {ex.Message}", false);
                }
            }
        }

        /// <summary>
        /// 处理单个文件
        /// </summary>
        /// <param name="file">文件路径</param>
        /// <param name="targetPath">目标路径</param>
        /// <param name="creationTimeLimit">文件创建时间限制</param>
        /// <param name="modificationTimeLimit">文件修改时间限制</param>
        /// <param name="logCallback">日志回调函数</param>
        /// <returns>表示异步操作的任务</returns>
        /// <remarks>
        /// 此方法执行以下操作：
        /// 1. 检查文件的创建时间和修改时间是否满足条件
        /// 2. 创建目标文件夹（按日期和文件名组织）
        /// 3. 验证解压路径的安全性
        /// 4. 解压文件到目标文件夹
        /// 5. 计算解压后文件夹的大小和文件数量
        /// 6. 检测并删除重复文件夹
        /// </remarks>
        async Task ProcessSingleFile(
            string file,
            string targetPath,
            DateTime creationTimeLimit,
            DateTime modificationTimeLimit,
            Action<string, string, bool> logCallback)
        {
            FileInfo fileInfo = new FileInfo(file);
            if (fileInfo.CreationTime >= creationTimeLimit &&
                fileInfo.LastWriteTime >= modificationTimeLimit)
            {
                string dateFolder = fileInfo.LastWriteTime.ToString("yyyy-MM-dd");
                string extractFolder = Path.Combine(
                    targetPath,
                    dateFolder,
                    Path.GetFileNameWithoutExtension(file));

                // 防止路径穿越攻击
                if (!IsValidExtractPath(targetPath, extractFolder))
                {
                    logCallback("警告", $"检测到不安全的路径: {extractFolder}", true);
                    return;
                }

                if (!Directory.Exists(extractFolder) ||
                    Directory.GetFileSystemEntries(extractFolder).Length == 0)
                {
                    try
                    {
                        Directory.CreateDirectory(extractFolder);
                        await Task.Run(() => ExtractArchive(file, extractFolder, _cancellationTokenSource.Token)).ConfigureAwait(false);
                        logCallback("信息", $"已解压文件：{file} 到 {extractFolder}", true);
                    }
                    catch (Exception ex) when (!(ex is OperationCanceledException))
                    {
                        logCallback("错误", $"创建或解压到目录 {extractFolder} 时出错: {ex.Message}", false);
                        return;
                    }
                }

                Tuple<long, int> folderInfo = await GetFolderInfoAsync(extractFolder, _cancellationTokenSource.Token).ConfigureAwait(false);
                long folderSize = folderInfo.Item1;
                int fileCount = folderInfo.Item2;

                await DeleteDuplicateFolders(
                    targetPath,
                    Path.GetFileNameWithoutExtension(file),
                    extractFolder,
                    folderSize,
                    fileCount,
                    logCallback).ConfigureAwait(false);
            }
        }

        /// <summary>
        /// 验证解压路径是否合法（防止路径穿越攻击）
        /// </summary>
        /// <param name="basePath">基础路径</param>
        /// <param name="fullPath">完整路径</param>
        /// <returns>如果路径合法则返回true，否则返回false</returns>
        /// <remarks>
        /// 此方法通过以下步骤验证路径安全性：
        /// 1. 规范化基础路径和完整路径
        /// 2. 确保完整路径是基础路径的子目录
        /// </remarks>
        bool IsValidExtractPath(string basePath, string fullPath)
        {
            // 规范化路径
            string normalizedBasePath = Path.GetFullPath(basePath);
            string normalizedFullPath = Path.GetFullPath(fullPath);

            // 确保解压路径是基础路径的子目录
            return normalizedFullPath.StartsWith(normalizedBasePath, StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 异步获取文件夹信息（大小和文件数量）
        /// </summary>
        /// <param name="folderPath">文件夹路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>包含文件夹大小和文件数量的元组</returns>
        /// <remarks>
        /// 此方法执行以下操作：
        /// 1. 检查缓存中是否有文件夹信息
        /// 2. 如果缓存未过期，直接返回缓存的信息
        /// 3. 否则并行计算文件夹大小和文件数量
        /// 4. 更新缓存并返回结果
        /// </remarks>
        async Task<Tuple<long, int>> GetFolderInfoAsync(string folderPath, CancellationToken cancellationToken)
        {
            // 先检查缓存
            if (_folderInfoCache.TryGetValue(folderPath, out Tuple<long, int, DateTime> cachedInfo))
            {
                // 检查缓存是否过期
                if ((DateTime.Now - cachedInfo.Item3).TotalMinutes < CACHE_EXPIRY_MINUTES)
                {
                    return new Tuple<long, int>(cachedInfo.Item1, cachedInfo.Item2);
                }
            }

            // 并行计算文件夹大小和文件数量
            Task<long> folderSizeTask = Task.Run(() => GetDirectorySize(folderPath, cancellationToken), cancellationToken);
            Task<int> fileCountTask = Task.Run(() => GetDirectoryFileCount(folderPath, cancellationToken), cancellationToken);

            await Task.WhenAll(folderSizeTask, fileCountTask).ConfigureAwait(false);

            long folderSize = folderSizeTask.Result;
            int fileCount = fileCountTask.Result;

            // 更新缓存
            _folderInfoCache[folderPath] = new Tuple<long, int, DateTime>(folderSize, fileCount, DateTime.Now);

            return new Tuple<long, int>(folderSize, fileCount);
        }

        /// <summary>
        /// 获取文件夹中的文件数量
        /// </summary>
        /// <param name="folderPath">文件夹路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>文件数量，出错时返回-1</returns>
        /// <remarks>
        /// 此方法计算指定文件夹（包括所有子文件夹）中的文件总数
        /// </remarks>
        int GetDirectoryFileCount(string folderPath, CancellationToken cancellationToken)
        {
            try
            {
                return Directory.GetFiles(folderPath, "*", SearchOption.AllDirectories).Length;
            }
            catch (Exception ex) when (!(ex is OperationCanceledException))
            {
                ETLogManager.Error($"计算文件夹 {folderPath} 的文件数量时出错", ex);
                return -1;
            }
        }

        /// <summary>
        /// 使用 SharpCompress 解压文件
        /// </summary>
        /// <param name="sourceFile">源文件路径</param>
        /// <param name="destinationDirectory">目标目录</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <remarks>
        /// 此方法执行以下操作：
        /// 1. 打开压缩文件
        /// 2. 遍历所有非目录条目
        /// 3. 验证每个条目路径的安全性
        /// 4. 将条目解压到目标目录
        /// 5. 响应取消请求
        /// </remarks>
        void ExtractArchive(string sourceFile, string destinationDirectory, CancellationToken cancellationToken)
        {
            try
            {
                using (IArchive archive = ArchiveFactory.Open(sourceFile))
                {
                    foreach (IArchiveEntry entry in archive.Entries)
                    {
                        // 检查取消标记
                        cancellationToken.ThrowIfCancellationRequested();

                        if (!entry.IsDirectory)
                        {
                            // 验证条目路径是否安全
                            string fullPath = Path.Combine(destinationDirectory, entry.Key);
                            if (!IsValidExtractPath(destinationDirectory, fullPath))
                            {
                                ETLogManager.Warning($"检测到不安全的压缩文件条目: {entry.Key}");
                                continue;
                            }

                            entry.WriteToDirectory(
                                destinationDirectory,
                                new ExtractionOptions { ExtractFullPath = true, Overwrite = true });
                        }
                    }
                }
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                // 保留内部异常的堆栈跟踪
                throw new Exception($"解压文件 {sourceFile} 时出错：{ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取文件夹大小
        /// </summary>
        /// <param name="folderPath">文件夹路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>文件夹大小（字节），出错时返回-1</returns>
        /// <remarks>
        /// 此方法计算指定文件夹（包括所有子文件夹）中所有文件的总大小
        /// </remarks>
        long GetDirectorySize(string folderPath, CancellationToken cancellationToken)
        {
            try
            {
                // 分批处理文件以减少内存使用
                long totalSize = 0;
                foreach (string file in Directory.GetFiles(folderPath, "*", SearchOption.AllDirectories))
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    totalSize += new FileInfo(file).Length;
                }
                return totalSize;
            }
            catch (Exception ex) when (!(ex is OperationCanceledException))
            {
                ETLogManager.Error($"计算文件夹 {folderPath} 大小时出错", ex);
                return -1;
            }
        }

        /// <summary>
        /// 查找并删除重复文件夹
        /// </summary>
        /// <param name="targetPath">目标路径</param>
        /// <param name="folderName">文件夹名称</param>
        /// <param name="originalFolder">原始文件夹路径</param>
        /// <param name="originalSize">原始文件夹大小</param>
        /// <param name="originalFileCount">原始文件夹文件数量</param>
        /// <param name="logCallback">日志回调函数</param>
        /// <returns>表示异步操作的任务</returns>
        /// <remarks>
        /// 此方法执行以下操作：
        /// 1. 查找所有同名但不同路径的文件夹
        /// 2. 对每个潜在的重复文件夹：
        ///    a. 确保不是原始文件夹的子文件夹或父文件夹
        ///    b. 获取文件夹信息（大小和文件数量）
        ///    c. 使用高级比较算法判断内容是否相似
        ///    d. 如果内容相似，删除重复文件夹
        /// 3. 响应取消请求
        /// </remarks>
        async Task DeleteDuplicateFolders(
            string targetPath,
            string folderName,
            string originalFolder,
            long originalSize,
            int originalFileCount,
            Action<string, string, bool> logCallback)
        {
            try
            {
                // 查找所有同名但不同路径的文件夹
                IEnumerable<string> potentialDuplicateFolders = Directory.GetDirectories(
                    targetPath,
                    "*",
                    SearchOption.AllDirectories)
                    .Where(f => Path.GetFileName(f).Equals(folderName, StringComparison.OrdinalIgnoreCase)
                             && !f.Equals(originalFolder, StringComparison.OrdinalIgnoreCase));

                foreach (string folder in potentialDuplicateFolders)
                {
                    _cancellationTokenSource.Token.ThrowIfCancellationRequested();

                    // 改进文件夹路径比较，确保不是子文件夹或父文件夹
                    if (!IsSubdirectoryOf(folder, originalFolder) && !IsSubdirectoryOf(originalFolder, folder))
                    {
                        Tuple<long, int> folderInfo = await GetFolderInfoAsync(folder, _cancellationTokenSource.Token).ConfigureAwait(false);
                        long folderSize = folderInfo.Item1;
                        int fileCount = folderInfo.Item2;

                        // 使用更高级的比较算法
                        if (IsFolderContentSimilar(originalSize, originalFileCount, folderSize, fileCount))
                        {
                            try
                            {
                                Directory.Delete(folder, true);
                                logCallback("信息", $"删除重复文件夹：{folder}", false);
                            }
                            catch (Exception ex)
                            {
                                logCallback("错误", $"删除重复文件夹 {folder} 时出错：{ex.Message}", false);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) when (!(ex is OperationCanceledException))
            {
                logCallback("错误", $"检查重复文件夹时出错: {ex.Message}", false);
            }
        }

        /// <summary>
        /// 判断一个路径是否是另一个路径的子目录
        /// </summary>
        /// <param name="potentialChild">潜在的子目录路径</param>
        /// <param name="potentialParent">潜在的父目录路径</param>
        /// <returns>如果是子目录则返回true，否则返回false</returns>
        /// <remarks>
        /// 此方法通过遍历目录层次结构来判断一个路径是否是另一个路径的子目录
        /// </remarks>
        bool IsSubdirectoryOf(string potentialChild, string potentialParent)
        {
            DirectoryInfo di1 = new DirectoryInfo(potentialChild);
            DirectoryInfo di2 = new DirectoryInfo(potentialParent);

            while (di1 != null)
            {
                if (di1.FullName.Equals(di2.FullName, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
                di1 = di1.Parent;
            }
            return false;
        }

        /// <summary>
        /// 更高级的文件夹内容相似度比较
        /// </summary>
        /// <param name="size1">第一个文件夹的大小</param>
        /// <param name="count1">第一个文件夹的文件数量</param>
        /// <param name="size2">第二个文件夹的大小</param>
        /// <param name="count2">第二个文件夹的文件数量</param>
        /// <returns>如果内容相似则返回true，否则返回false</returns>
        /// <remarks>
        /// 此方法使用以下规则判断两个文件夹内容是否相似：
        /// 1. 文件数量必须完全相同
        /// 2. 如果文件大小完全相同，认为是相同内容
        /// 3. 如果文件大小接近（允许1%的误差），认为是相似内容
        /// </remarks>
        bool IsFolderContentSimilar(long size1, int count1, long size2, int count2)
        {
            // 文件数量必须完全相同
            if (count1 != count2) return false;

            // 如果文件大小完全相同，很可能是相同内容
            if (size1 == size2) return true;

            // 如果文件大小接近（允许1%的误差），考虑为相似
            double sizeRatio = (double)size1 / size2;
            return sizeRatio >= 0.99 && sizeRatio <= 1.01;
        }
    }
}