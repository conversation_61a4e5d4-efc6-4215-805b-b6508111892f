namespace HyAssistant.ChinaTowerDownload
{
    partial class ChinaTowerDownload
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBoxLogin = new System.Windows.Forms.GroupBox();
            this.labelLoginStatus = new System.Windows.Forms.Label();
            this.buttonLogout = new System.Windows.Forms.Button();
            this.buttonLogin = new System.Windows.Forms.Button();
            this.buttonInputAuth = new System.Windows.Forms.Button();
            this.groupBoxPath = new System.Windows.Forms.GroupBox();
            this.buttonSelectPath = new System.Windows.Forms.Button();
            this.textBoxSavePath = new System.Windows.Forms.TextBox();
            this.labelSavePath = new System.Windows.Forms.Label();
            this.groupBoxStations = new System.Windows.Forms.GroupBox();
            this.buttonUpdateStations = new System.Windows.Forms.Button();
            this.buttonCrawlAllStations = new System.Windows.Forms.Button();
            this.buttonDownloadPhotos = new System.Windows.Forms.Button();
            this.groupBoxPhotos = new System.Windows.Forms.GroupBox();
            this.buttonSearchStation = new System.Windows.Forms.Button();
            this.textBoxSearchKeyword = new System.Windows.Forms.TextBox();
            this.labelSearchKeyword = new System.Windows.Forms.Label();
            this.buttonCrawlPhotos = new System.Windows.Forms.Button();
            this.textBoxStationList = new System.Windows.Forms.TextBox();
            this.labelStationList = new System.Windows.Forms.Label();
            this.groupBoxLog = new System.Windows.Forms.GroupBox();
            this.textBoxLog = new System.Windows.Forms.TextBox();
            this.statusStrip = new System.Windows.Forms.StatusStrip();
            this.labelStatus = new System.Windows.Forms.ToolStripStatusLabel();
            this.groupBoxLogin.SuspendLayout();
            this.groupBoxPath.SuspendLayout();
            this.groupBoxStations.SuspendLayout();
            this.groupBoxPhotos.SuspendLayout();
            this.groupBoxLog.SuspendLayout();
            this.statusStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBoxLogin
            // 
            this.groupBoxLogin.Controls.Add(this.labelLoginStatus);
            this.groupBoxLogin.Controls.Add(this.buttonLogout);
            this.groupBoxLogin.Controls.Add(this.buttonLogin);
            this.groupBoxLogin.Controls.Add(this.buttonInputAuth);
            this.groupBoxLogin.Location = new System.Drawing.Point(12, 12);
            this.groupBoxLogin.Name = "groupBoxLogin";
            this.groupBoxLogin.Size = new System.Drawing.Size(760, 60);
            this.groupBoxLogin.TabIndex = 0;
            this.groupBoxLogin.TabStop = false;
            this.groupBoxLogin.Text = "登录认证";
            // 
            // labelLoginStatus
            // 
            this.labelLoginStatus.AutoSize = true;
            this.labelLoginStatus.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold);
            this.labelLoginStatus.Location = new System.Drawing.Point(280, 27);
            this.labelLoginStatus.Name = "labelLoginStatus";
            this.labelLoginStatus.Size = new System.Drawing.Size(44, 17);
            this.labelLoginStatus.TabIndex = 3;
            this.labelLoginStatus.Text = "未登录";
            // 
            // buttonLogout
            // 
            this.buttonLogout.Location = new System.Drawing.Point(185, 23);
            this.buttonLogout.Name = "buttonLogout";
            this.buttonLogout.Size = new System.Drawing.Size(75, 25);
            this.buttonLogout.TabIndex = 2;
            this.buttonLogout.Text = "登出";
            this.buttonLogout.UseVisualStyleBackColor = true;
            this.buttonLogout.Click += new System.EventHandler(this.ButtonLogout_Click);
            // 
            // buttonLogin
            // 
            this.buttonLogin.Location = new System.Drawing.Point(15, 23);
            this.buttonLogin.Name = "buttonLogin";
            this.buttonLogin.Size = new System.Drawing.Size(75, 25);
            this.buttonLogin.TabIndex = 0;
            this.buttonLogin.Text = "登录";
            this.buttonLogin.UseVisualStyleBackColor = true;
            this.buttonLogin.Click += new System.EventHandler(this.ButtonLogin_Click);
            // 
            // buttonInputAuth
            // 
            this.buttonInputAuth.Location = new System.Drawing.Point(100, 23);
            this.buttonInputAuth.Name = "buttonInputAuth";
            this.buttonInputAuth.Size = new System.Drawing.Size(75, 25);
            this.buttonInputAuth.TabIndex = 1;
            this.buttonInputAuth.Text = "输入Auth";
            this.buttonInputAuth.UseVisualStyleBackColor = true;
            this.buttonInputAuth.Click += new System.EventHandler(this.ButtonInputAuth_Click);
            // 
            // groupBoxPath
            // 
            this.groupBoxPath.Controls.Add(this.buttonSelectPath);
            this.groupBoxPath.Controls.Add(this.textBoxSavePath);
            this.groupBoxPath.Controls.Add(this.labelSavePath);
            this.groupBoxPath.Location = new System.Drawing.Point(12, 78);
            this.groupBoxPath.Name = "groupBoxPath";
            this.groupBoxPath.Size = new System.Drawing.Size(760, 60);
            this.groupBoxPath.TabIndex = 1;
            this.groupBoxPath.TabStop = false;
            this.groupBoxPath.Text = "保存路径";
            // 
            // buttonSelectPath
            // 
            this.buttonSelectPath.Location = new System.Drawing.Point(670, 25);
            this.buttonSelectPath.Name = "buttonSelectPath";
            this.buttonSelectPath.Size = new System.Drawing.Size(75, 25);
            this.buttonSelectPath.TabIndex = 2;
            this.buttonSelectPath.Text = "浏览...";
            this.buttonSelectPath.UseVisualStyleBackColor = true;
            this.buttonSelectPath.Click += new System.EventHandler(this.ButtonSelectPath_Click);
            // 
            // textBoxSavePath
            // 
            this.textBoxSavePath.Location = new System.Drawing.Point(80, 27);
            this.textBoxSavePath.Name = "textBoxSavePath";
            this.textBoxSavePath.Size = new System.Drawing.Size(580, 21);
            this.textBoxSavePath.TabIndex = 1;
            // 
            // labelSavePath
            // 
            this.labelSavePath.AutoSize = true;
            this.labelSavePath.Location = new System.Drawing.Point(15, 30);
            this.labelSavePath.Name = "labelSavePath";
            this.labelSavePath.Size = new System.Drawing.Size(59, 12);
            this.labelSavePath.TabIndex = 0;
            this.labelSavePath.Text = "保存路径:";
            //
            // groupBoxStations
            //
            this.groupBoxStations.Controls.Add(this.buttonUpdateStations);
            this.groupBoxStations.Controls.Add(this.buttonCrawlAllStations);
            this.groupBoxStations.Controls.Add(this.buttonDownloadPhotos);
            this.groupBoxStations.Location = new System.Drawing.Point(12, 144);
            this.groupBoxStations.Name = "groupBoxStations";
            this.groupBoxStations.Size = new System.Drawing.Size(760, 60);
            this.groupBoxStations.TabIndex = 2;
            this.groupBoxStations.TabStop = false;
            this.groupBoxStations.Text = "批量下载流程（三步骤）";
            //
            // buttonUpdateStations
            //
            this.buttonUpdateStations.Location = new System.Drawing.Point(15, 23);
            this.buttonUpdateStations.Name = "buttonUpdateStations";
            this.buttonUpdateStations.Size = new System.Drawing.Size(120, 25);
            this.buttonUpdateStations.TabIndex = 0;
            this.buttonUpdateStations.Text = "①更新站址库";
            this.buttonUpdateStations.UseVisualStyleBackColor = true;
            this.buttonUpdateStations.Click += new System.EventHandler(this.ButtonUpdateStations_Click);
            //
            // buttonCrawlAllStations
            //
            this.buttonCrawlAllStations.Location = new System.Drawing.Point(150, 23);
            this.buttonCrawlAllStations.Name = "buttonCrawlAllStations";
            this.buttonCrawlAllStations.Size = new System.Drawing.Size(150, 25);
            this.buttonCrawlAllStations.TabIndex = 1;
            this.buttonCrawlAllStations.Text = "②抓取所有站点照片";
            this.buttonCrawlAllStations.UseVisualStyleBackColor = true;
            this.buttonCrawlAllStations.Click += new System.EventHandler(this.ButtonCrawlAllStations_Click);
            //
            // buttonDownloadPhotos
            //
            this.buttonDownloadPhotos.Location = new System.Drawing.Point(315, 23);
            this.buttonDownloadPhotos.Name = "buttonDownloadPhotos";
            this.buttonDownloadPhotos.Size = new System.Drawing.Size(170, 25);
            this.buttonDownloadPhotos.TabIndex = 2;
            this.buttonDownloadPhotos.Text = "③批量下载所有新增照片";
            this.buttonDownloadPhotos.UseVisualStyleBackColor = true;
            this.buttonDownloadPhotos.Click += new System.EventHandler(this.ButtonDownloadPhotos_Click);
            //
            // groupBoxPhotos
            //
            this.groupBoxPhotos.Controls.Add(this.buttonSearchStation);
            this.groupBoxPhotos.Controls.Add(this.textBoxSearchKeyword);
            this.groupBoxPhotos.Controls.Add(this.labelSearchKeyword);
            this.groupBoxPhotos.Controls.Add(this.buttonCrawlPhotos);
            this.groupBoxPhotos.Controls.Add(this.textBoxStationList);
            this.groupBoxPhotos.Controls.Add(this.labelStationList);
            this.groupBoxPhotos.Location = new System.Drawing.Point(12, 210);
            this.groupBoxPhotos.Name = "groupBoxPhotos";
            this.groupBoxPhotos.Size = new System.Drawing.Size(760, 120);
            this.groupBoxPhotos.TabIndex = 3;
            this.groupBoxPhotos.TabStop = false;
            this.groupBoxPhotos.Text = "指定站点下载";
            //
            // buttonSearchStation
            //
            this.buttonSearchStation.Location = new System.Drawing.Point(670, 25);
            this.buttonSearchStation.Name = "buttonSearchStation";
            this.buttonSearchStation.Size = new System.Drawing.Size(75, 25);
            this.buttonSearchStation.TabIndex = 3;
            this.buttonSearchStation.Text = "搜索";
            this.buttonSearchStation.UseVisualStyleBackColor = true;
            this.buttonSearchStation.Click += new System.EventHandler(this.ButtonSearchStation_Click);
            //
            // textBoxSearchKeyword
            //
            this.textBoxSearchKeyword.Location = new System.Drawing.Point(80, 27);
            this.textBoxSearchKeyword.Name = "textBoxSearchKeyword";
            this.textBoxSearchKeyword.Size = new System.Drawing.Size(580, 21);
            this.textBoxSearchKeyword.TabIndex = 2;
            //
            // labelSearchKeyword
            //
            this.labelSearchKeyword.AutoSize = true;
            this.labelSearchKeyword.Location = new System.Drawing.Point(15, 30);
            this.labelSearchKeyword.Name = "labelSearchKeyword";
            this.labelSearchKeyword.Size = new System.Drawing.Size(59, 12);
            this.labelSearchKeyword.TabIndex = 1;
            this.labelSearchKeyword.Text = "搜索站点:";
            //
            // buttonCrawlPhotos
            //
            this.buttonCrawlPhotos.Location = new System.Drawing.Point(580, 85);
            this.buttonCrawlPhotos.Name = "buttonCrawlPhotos";
            this.buttonCrawlPhotos.Size = new System.Drawing.Size(165, 25);
            this.buttonCrawlPhotos.TabIndex = 5;
            this.buttonCrawlPhotos.Text = "下载站点清单内站点照片";
            this.buttonCrawlPhotos.UseVisualStyleBackColor = true;
            this.buttonCrawlPhotos.Click += new System.EventHandler(this.ButtonCrawlPhotos_Click);
            //
            // textBoxStationList
            //
            this.textBoxStationList.Location = new System.Drawing.Point(80, 55);
            this.textBoxStationList.Multiline = true;
            this.textBoxStationList.Name = "textBoxStationList";
            this.textBoxStationList.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBoxStationList.Size = new System.Drawing.Size(490, 55);
            this.textBoxStationList.TabIndex = 4;
            //
            // labelStationList
            //
            this.labelStationList.AutoSize = true;
            this.labelStationList.Location = new System.Drawing.Point(15, 58);
            this.labelStationList.Name = "labelStationList";
            this.labelStationList.Size = new System.Drawing.Size(59, 12);
            this.labelStationList.TabIndex = 0;
            this.labelStationList.Text = "站点列表:";
            // 
            // groupBoxLog
            // 
            this.groupBoxLog.Controls.Add(this.textBoxLog);
            this.groupBoxLog.Location = new System.Drawing.Point(12, 336);
            this.groupBoxLog.Name = "groupBoxLog";
            this.groupBoxLog.Size = new System.Drawing.Size(760, 200);
            this.groupBoxLog.TabIndex = 4;
            this.groupBoxLog.TabStop = false;
            this.groupBoxLog.Text = "操作日志";
            // 
            // textBoxLog
            // 
            this.textBoxLog.BackColor = System.Drawing.Color.Black;
            this.textBoxLog.ForeColor = System.Drawing.Color.Lime;
            this.textBoxLog.Location = new System.Drawing.Point(15, 20);
            this.textBoxLog.Multiline = true;
            this.textBoxLog.Name = "textBoxLog";
            this.textBoxLog.ReadOnly = true;
            this.textBoxLog.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBoxLog.Size = new System.Drawing.Size(730, 170);
            this.textBoxLog.TabIndex = 0;
            // 
            // statusStrip
            // 
            this.statusStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.labelStatus});
            this.statusStrip.Location = new System.Drawing.Point(0, 548);
            this.statusStrip.Name = "statusStrip";
            this.statusStrip.Size = new System.Drawing.Size(784, 22);
            this.statusStrip.TabIndex = 5;
            this.statusStrip.Text = "statusStrip1";
            // 
            // labelStatus
            // 
            this.labelStatus.Name = "labelStatus";
            this.labelStatus.Size = new System.Drawing.Size(32, 17);
            this.labelStatus.Text = "就绪";
            // 
            // ChinaTowerDownload
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(784, 570);
            this.Controls.Add(this.statusStrip);
            this.Controls.Add(this.groupBoxLog);
            this.Controls.Add(this.groupBoxPhotos);
            this.Controls.Add(this.groupBoxStations);
            this.Controls.Add(this.groupBoxPath);
            this.Controls.Add(this.groupBoxLogin);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.Name = "ChinaTowerDownload";
            this.Text = "中国铁塔照片下载助手";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.ChinaTowerDownload_FormClosing);
            this.Load += new System.EventHandler(this.ChinaTowerDownload_Load);
            this.groupBoxLogin.ResumeLayout(false);
            this.groupBoxLogin.PerformLayout();
            this.groupBoxPath.ResumeLayout(false);
            this.groupBoxPath.PerformLayout();
            this.groupBoxStations.ResumeLayout(false);
            this.groupBoxStations.PerformLayout();
            this.groupBoxPhotos.ResumeLayout(false);
            this.groupBoxPhotos.PerformLayout();
            this.groupBoxLog.ResumeLayout(false);
            this.groupBoxLog.PerformLayout();
            this.statusStrip.ResumeLayout(false);
            this.statusStrip.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        /// <summary>
        /// 登录认证分组框控件
        /// </summary>
        private System.Windows.Forms.GroupBox groupBoxLogin;
        
        /// <summary>
        /// 登录状态显示标签控件
        /// </summary>
        private System.Windows.Forms.Label labelLoginStatus;
        
        /// <summary>
        /// 登出按钮控件
        /// </summary>
        private System.Windows.Forms.Button buttonLogout;
        
        /// <summary>
        /// 登录按钮控件
        /// </summary>
        private System.Windows.Forms.Button buttonLogin;
        
        /// <summary>
        /// 输入认证信息按钮控件
        /// </summary>
        private System.Windows.Forms.Button buttonInputAuth;
        
        /// <summary>
        /// 保存路径分组框控件
        /// </summary>
        private System.Windows.Forms.GroupBox groupBoxPath;
        
        /// <summary>
        /// 选择保存路径按钮控件
        /// </summary>
        private System.Windows.Forms.Button buttonSelectPath;
        
        /// <summary>
        /// 保存路径文本框控件
        /// </summary>
        private System.Windows.Forms.TextBox textBoxSavePath;
        
        /// <summary>
        /// 保存路径标签控件
        /// </summary>
        private System.Windows.Forms.Label labelSavePath;
        
        /// <summary>
        /// 站点操作分组框控件
        /// </summary>
        private System.Windows.Forms.GroupBox groupBoxStations;
        
        /// <summary>
        /// 更新站址库按钮控件
        /// </summary>
        private System.Windows.Forms.Button buttonUpdateStations;
        
        /// <summary>
        /// 抓取所有站点照片按钮控件
        /// </summary>
        private System.Windows.Forms.Button buttonCrawlAllStations;
        
        /// <summary>
        /// 批量下载照片按钮控件
        /// </summary>
        private System.Windows.Forms.Button buttonDownloadPhotos;
        
        /// <summary>
        /// 照片操作分组框控件
        /// </summary>
        private System.Windows.Forms.GroupBox groupBoxPhotos;
        
        /// <summary>
        /// 搜索站点按钮控件
        /// </summary>
        private System.Windows.Forms.Button buttonSearchStation;
        
        /// <summary>
        /// 搜索关键词文本框控件
        /// </summary>
        private System.Windows.Forms.TextBox textBoxSearchKeyword;
        
        /// <summary>
        /// 搜索关键词标签控件
        /// </summary>
        private System.Windows.Forms.Label labelSearchKeyword;
        
        /// <summary>
        /// 抓取照片按钮控件
        /// </summary>
        private System.Windows.Forms.Button buttonCrawlPhotos;
        
        /// <summary>
        /// 站点列表文本框控件
        /// </summary>
        private System.Windows.Forms.TextBox textBoxStationList;
        
        /// <summary>
        /// 站点列表标签控件
        /// </summary>
        private System.Windows.Forms.Label labelStationList;
        
        /// <summary>
        /// 日志分组框控件
        /// </summary>
        private System.Windows.Forms.GroupBox groupBoxLog;
        
        /// <summary>
        /// 日志显示文本框控件
        /// </summary>
        private System.Windows.Forms.TextBox textBoxLog;
        
        /// <summary>
        /// 状态栏控件
        /// </summary>
        private System.Windows.Forms.StatusStrip statusStrip;
        
        /// <summary>
        /// 状态标签控件
        /// </summary>
        private System.Windows.Forms.ToolStripStatusLabel labelStatus;
    }
}