using ET;
using Microsoft.Office.Interop.Excel;
using Microsoft.Office.Interop.Visio;
using System;
using System.Collections.Generic;
using TextBox = System.Windows.Forms.TextBox;

namespace HyExcelVsto.Module.Common
{
    public static partial class VisioHelperFunctions
    {
        #region 变量
        const string pattern铁塔站名 = @"(?:铁塔站址名称为|铁塔站址名称|铁塔站名)[:：]?\s*([\u4e00-\u9fa5\p{N}\p{L}\s]+)";
        const string pattern铁塔编码 = @"(\d{14,22})";
        const string pattern地址 = "(?:地址：?(.*))|(.+)(?=\\r?\\n\\s*\\Z)\r\n";
        static string pattern经度 = ETGPS.LongitudePattern;
        static string pattern纬度 = ETGPS.LatitudePattern;
        #endregion 变量

        #region 操作说明文本
        public static void 操作说明文本PerRowsThread(
            Range filePathRange,
            Range pageNameRange,
            string targetDirectory,
            bool isDirectSave,
            Range readRange铁塔站名编码,
            Range readRange经纬度及地址,
            Range readRange安全风险点,
            Range readRange说明,
            Range writeRange铁塔站名编码,
            Range writeRange经纬度及地址,
            Range writeRange安全风险点,
            Range writeRange说明,
            TextBox textBoxProgress,
            TextBox textBoxError)
        {
            new System.Threading.Thread(
                () => 操作说明文本PerRows(
                    filePathRange,
                    pageNameRange,
                    targetDirectory,
                    isDirectSave,
                    readRange铁塔站名编码,
                    readRange经纬度及地址,
                    readRange安全风险点,
                    readRange说明,
                    writeRange铁塔站名编码,
                    writeRange经纬度及地址,
                    writeRange安全风险点,
                    writeRange说明,
                    textBoxProgress,
                    textBoxError)).Start();
        }

        public static void 操作说明文本PerRows(
            Range filePathRange,
            Range pageNameRange,
            string targetDirectory,
            bool isDirectSave,
            Range readRange铁塔站名编码,
            Range readRange经纬度及地址,
            Range readRange安全风险点,
            Range readRange说明,
            Range writeRange铁塔站名编码,
            Range writeRange经纬度及地址,
            Range writeRange安全风险点,
            Range writeRange说明,
            TextBox textBoxProgress,
            TextBox textBoxError)
        {
            #region 变量值获取，信息初始化
            if (filePathRange == null)
            {
                textBoxProgress.WriteLog("请选择文件路径所在的列");
                return;
            }

            switch (pageNameRange)
            {
                case null when filePathRange.Columns.Count > 1:
                    pageNameRange = filePathRange.Columns[2];
                    break;

                case null:
                    textBoxProgress.WriteLog("没有选择页面所在列，默认操作第一页");
                    break;
            }
            filePathRange = filePathRange.OptimizeRangeSize();

            if (readRange铁塔站名编码 != null && readRange铁塔站名编码.Columns.Count > 5 && readRange经纬度及地址 == null)
                readRange经纬度及地址 = readRange铁塔站名编码.GetColumnsInRange(3, 5).EntireColumn;
            if (readRange铁塔站名编码 != null && readRange铁塔站名编码.Columns.Count > 2 && readRange安全风险点 == null)
                readRange安全风险点 = readRange铁塔站名编码.Columns[6].EntireColumn;
            if (readRange铁塔站名编码 != null && readRange铁塔站名编码.Columns.Count > 3 && readRange说明 == null)
                readRange说明 = readRange铁塔站名编码.Columns[7].EntireColumn;
            bool needRead = readRange铁塔站名编码 != null ||
                readRange经纬度及地址 != null ||
                readRange安全风险点 != null ||
                readRange说明 != null;
            ;
            if (writeRange铁塔站名编码 != null && writeRange铁塔站名编码.Columns.Count > 1 && writeRange经纬度及地址 == null)
                writeRange经纬度及地址 = writeRange铁塔站名编码.GetColumnsInRange(3, 5).EntireColumn;
            if (writeRange铁塔站名编码 != null && writeRange铁塔站名编码.Columns.Count > 2 && writeRange安全风险点 == null)
                writeRange安全风险点 = writeRange铁塔站名编码.Columns[6].EntireColumn;
            if (writeRange铁塔站名编码 != null && writeRange铁塔站名编码.Columns.Count > 3 && writeRange说明 == null)
                writeRange说明 = writeRange铁塔站名编码.Columns[7].EntireColumn;
            bool needWrite = writeRange铁塔站名编码 != null ||
                writeRange经纬度及地址 != null ||
                writeRange安全风险点 != null ||
                writeRange说明 != null;
            #endregion 变量值获取，信息初始化
            Microsoft.Office.Interop.Visio.Application visioApp = ETVisio.CreateApplication(true);
            ;
            Document docPrev = null;
            Range filePathCellPrev = null;

            int rowCount = filePathRange.GetVisibleRowCount();
            int currentRow = 1;

            Dictionary<string, string> textShapes_Dic = new()
            {
                { "安全风险点", "安全风险点" },
                { "说明", "说明" }
            };

            foreach (Range row in filePathRange.Rows)
            {
                if (row.EntireRow.Hidden)
                    continue;

                Range filePathCell = filePathRange.Cells[row.Row, 1];
                Range pageNameCell = pageNameRange?.Cells[row.Row, 1];
                if (ETExcelExtensions.IsCellEmpty(filePathCell))
                    continue;
                ;

                string filePath = filePathCell.Value2.ToString();
                string pageName = pageNameCell?.Value2.ToString();
                string fileName = System.IO.Path.GetFileName(filePath);
                Document doc;

                textBoxProgress.WriteLog($"{currentRow++}/{rowCount}.正在处理 {fileName}");

                doc = ETVisio.FindOpenDocumentByPath(visioApp, filePath);
                if (doc == null)
                {
                    if (needWrite)
                    {
                        if (isDirectSave)
                            docPrev.Save(true);
                        else
                            docPrev.SaveToAnotherDirectory(targetDirectory, true);
                    }

                    if (filePathCellPrev != null)
                        filePathCellPrev.Format条件格式警示色(EnumWarningColor.蓝绿);

                    doc = ETVisio.Open(visioApp, filePath, textBoxProgress, false);
                    docPrev = doc;
                    filePathCellPrev = filePathCell;
                }
                else
                {
                    // 文件已经打开，更新引用
                    docPrev = doc;
                    filePathCellPrev = filePathCell;
                }

                if (doc == null)
                {
                    textBoxProgress.WriteLog($"出现意外错误，无法处理文件：{fileName}");
                    textBoxError.WriteLog($"出现意外错误，无法处理文件：{fileName}");
                    filePathCell.Format条件格式警示色(EnumWarningColor.虾红);
                    continue;
                }

                dynamic page = string.IsNullOrEmpty(pageName) ? doc.Pages[1] : ETVisio.FindPage(doc, pageName);
                if (page == null)
                {
                    textBoxProgress.WriteLog($"文件{fileName}中没有找到页面{pageName}");
                    textBoxError.WriteLog($"文件{fileName}中没有找到页面{pageName}");
                    filePathCell.Format条件格式警示色(EnumWarningColor.虾红);
                    continue;
                }

                Dictionary<string, List<dynamic>> textShapesDictionary = ETVisio.FindShapesStartingWithText(
                    page,
                    textShapes_Dic);

                if (textShapesDictionary.ContainsKey("说明"))
                {
                    List<dynamic> shapes = textShapesDictionary["说明"];
                    if (shapes.Count == 0)
                        continue;
                    Microsoft.Office.Interop.Visio.Shape shape = shapes[0];
                    string shapeText = shape.GetShapeText();

                    string originalText说明 = shapeText.RemoveFirstLineIfStartsWith("说明");

                    string originalValue铁塔站名 = ETString.RegexMatchText(shapeText, pattern铁塔站名, 1);
                    string originalValue铁塔编码 = ETString.RegexMatchText(shapeText, pattern铁塔编码);
                    string originalValue经度 = ETString.RegexMatchText(shapeText, pattern经度);
                    string originalValue纬度 = ETString.RegexMatchText(shapeText, pattern纬度);
                    string originalValue地址 = ETString.RegexMatchText(shapeText, pattern地址, 1);

                    if (needRead)
                    {
                        ShapeTextToExcel(readRange说明, row.Row, 1, originalText说明);

                        ShapeTextToExcel(readRange铁塔站名编码, row.Row, 1, originalValue铁塔站名);
                        ShapeTextToExcel(readRange铁塔站名编码, row.Row, 2, originalValue铁塔编码);
                        Set文本格式(readRange铁塔站名编码, row.Row, 2, originalValue铁塔编码);
                        ShapeTextToExcel(readRange经纬度及地址, row.Row, 1, originalValue经度);
                        ShapeTextToExcel(readRange经纬度及地址, row.Row, 2, originalValue纬度);
                        ShapeTextToExcel(readRange经纬度及地址, row.Row, 3, originalValue地址);
                    }
                    if (needWrite)
                    {
                        ETVisio.SetShapeText(shape, writeRange说明, row.Row, 1);

                        ETVisio.ReplaceText(shape, originalValue铁塔站名, writeRange铁塔站名编码, row.Row, 1);
                        ETVisio.ReplaceText(shape, originalValue铁塔编码, writeRange铁塔站名编码, row.Row, 2);
                        ETVisio.ReplaceText(shape, originalValue经度, writeRange经纬度及地址, row.Row, 1);
                        ETVisio.ReplaceText(shape, originalValue纬度, writeRange经纬度及地址, row.Row, 2);
                        ETVisio.ReplaceText(shape, originalValue地址, writeRange经纬度及地址, row.Row, 3);
                    }
                }

                if (textShapesDictionary.ContainsKey("安全风险点"))
                {
                    List<dynamic> shapes = textShapesDictionary["安全风险点"];
                    if (shapes.Count == 0)
                        continue;
                    Microsoft.Office.Interop.Visio.Shape shape = shapes[0];
                    string shapeText = shape.GetShapeText();

                    string originalText安全风险点 = shapeText.RemoveFirstLineIfStartsWith("安全风险点");

                    if (needRead)
                    {
                        ShapeTextToExcel(readRange安全风险点, row.Row, 1, originalText安全风险点);
                    }
                    if (needWrite)
                    {
                        ETVisio.SetShapeText(shape, readRange安全风险点, row.Row, 1);
                    }
                }
            }

            if (needWrite)
            {
                if (isDirectSave)
                    docPrev.Save(true);
                else
                    docPrev.SaveToAnotherDirectory(targetDirectory, true);
            }
            if (filePathCellPrev != null)
                filePathCellPrev.Format条件格式警示色(EnumWarningColor.蓝绿);

            visioApp.Quit();
            textBoxProgress.WriteLog("执行完成");
        }

        /// <summary>
        /// 将文本写入指定范围内的特定单元格
        /// </summary>
        /// <param name="range">Excel范围对象</param>
        /// <param name="rowIndex">行索引（从0开始）</param>
        /// <param name="colIndex">列索引（从0开始）</param>
        /// <param name="value">要写入的值</param>
        /// <exception cref="ETException">当参数无效或Excel操作失败时抛出</exception>
        /// <remarks>
        /// 此方法会：
        /// 1. 验证输入参数的有效性
        /// 2. 检查目标单元格是否在范围内
        /// 3. 将文本写入指定单元格
        /// </remarks>
        public static void ShapeTextToExcel(Range range, int rowIndex, int colIndex, string value)
        {
            if (range == null)
                throw new ETException("Excel范围对象不能为空", "Visio.ShapeTextToCell");
            if (rowIndex < 0)
                throw new ETException("行索引不能为负数", "Visio.ShapeTextToCell");
            if (colIndex < 0)
                throw new ETException("列索引不能为负数", "Visio.ShapeTextToCell");

            try
            {
                if (string.IsNullOrEmpty(value))
                {
                    ETLogManager.Info("Visio.ShapeTextToCell", "要写入的值为空，操作已跳过");
                    return;
                }

                if (range.Columns.Count < colIndex || range.Rows.Count <= rowIndex)
                {
                    ETLogManager.Info("Visio.ShapeTextToCell", "目标单元格超出范围，操作已跳过");
                    return;
                }

                range.Cells[rowIndex, colIndex].Value = value;
                ETLogManager.Info("Visio.ShapeTextToCell", $"成功将文本写入单元格[{rowIndex},{colIndex}]: {value}");
            }
            catch (Exception ex)
            {
                string errorMessage = $"写入Excel单元格[{rowIndex},{colIndex}]失败";
                ETLogManager.Error("Visio.ShapeTextToCell", ex);
                throw new ETException(errorMessage, "Visio.ShapeTextToCell", ex);
            }
        }


        #endregion 操作说明文本
    }
}