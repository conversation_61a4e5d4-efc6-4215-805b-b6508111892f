<?xml version="1.0" encoding="utf-8"?>
<!-- action预定义,一般放置可供多处调用的action,可通过_context.doAction(name)方式调用 -->
<actions>
  <action name="add" label="新增" label-en="New" type="obj" icon="add circle" />
  <action name="modify" label="修改" label-en="Modify" type="obj" icon="write" itemsable="true" />
  <action name="view" label="查看" label-en="View" type="obj" />
  <action name="remove" label="删除" label-en="Delete" type="obj" icon="delete circle" _ui_class="red" itemsable="true" />
  <action name="locate" label="定位" label-en="Locate" type="obj" icon="marker" context="top" permission="BASIC_MAP" if="!sourceui || sourceui.source !== 'map'" />
  <action name="imp" label="导入" label-en="Import" type="obj" icon="upload" />
  <action name="exp" label="导出" label-en="Export Select Data" type="obj" icon="file" />
  <action name="expall" label="导出全部" label-en="Export all" type="obj" icon="file text" />
  <action name="expgrid" label="导出" type="script" icon="file">
    <![CDATA[
      _util.xls.writeObjectTable(_actionContext.source, _actionContext.params.fileName || _actionContext.source.expFileName || _actionContext.def.fileName || '导出结果.xls');
    ]]>
  </action>
  <action name="exportAll" label="查询结果大数量导出" label-en="Export All" type="script" icon="file text" url="apps/gdo3/action/exportAll.js" />
  <action name="offlineExportAll" label="查询结果大数量离线导出" label-en="Export All" type="script" icon="file text" url="apps/gdo3/action/offlineExportAll.js" />

  <action name="importTemplate" type="script" label="批量查询" icon="search">
    <![CDATA[
      let action = _actionContext.action
      let grid = _actionContext.source
      let objType = action.objectType
      _sui.showUploadModal(_context.fullDataUrl('bean/imp.do?_type=' + objType + '&_scriptmethod=impBatchSearch'), {
          success: function (data, file) {
              if (data && data.length > 0) {
                  grid.setData(data)
              }
          }
      })
    ]]>
  </action>
  <action name="exportTemplate" type="script" label="导出批量查询模板" icon="download">
    <![CDATA[
      let action = _actionContext.action
      let objType = action.objectType
      let filename = objType.toLowerCase() + '_batchimport_template.xls';
      let url = 'bean/exp.do?_type=' + objType + '&_scriptmethod=downloadTemplateFile&_expfilename=' + filename;
      url = _context.fullDataUrl(url);
      window.open(url, '_blank');
    ]]>
  </action>

  <action name="oneclick_alldelete" label="一键删除" label-en="onedelete" type="script" url="modules/onedelete/onedelete.js" permission="" target="*" /> <!-- 暂定target=*的为适应所有选中对象操作的调用 -->
  <action name="oneclick_allexport" label="一键导出" label-en="oneexport" type="script" url="modules/oneexport/oneexport.js" permission="" target="*" /> <!-- 暂定target=*的为适应所有选中对象操作的调用 -->
  <!-- 地图点对象重定位 -->
  <action name="relocate" label="重定位" label-en="Relocate" type="script" icon="random" url="modules/map/relocate.js" />

  <!-- 管道系统定位 -->
  <action name="locateDuctSystem" type="script" label="定位" label-en="Locate" icon="location arrow" _ui_class="blue" url="modules/pipeSystemMgt/js/locatePipeSystem.js" />

  <!-- 人井展开图 -->
  <action name="wellview" permission="SUPPORTING_PANORAMAFIND" type="window" class="large" style="height:600px;" label="展开图" label-en="Expanded view" title="展开图" icon="keyboard" mainConfig="modules/facility/well/expandview.xml&amp;wellId=${id}&amp;facilityType=${objectType}" />
  <!-- 电杆展开图 -->
  <action name="poleview" permission="SUPPORTING_PANORAMAFIND" type="window" class="large" closable="true" style="height:600px;" label="展开图" label-en="Expanded view" title="${NAME}" icon="keyboard" mainConfig="modules/facility/pole/expandview.xml&amp;poleId=${id}" />

  <!-- 管道段断面图 -->
  <action name="pipesectionGraph" type="window" class="large" style="height:600px;" label="截面图" label-en="Sectional view" title="截面图 ${CODE}" title-en="Sectional view ${CODE}" icon="keyboard" permission="SUPPORTING_PANORAMAFIND" mainConfig="apps/pipe/pipeline/secview.xml&amp;pipesectionId=${id}" />
  <!-- 缆段敷设 -->
  <action name="layingByCable" label="敷设" label-en="Laying" title="敷设-${NAME}" title-en="Laying -${NAME}" mainConfig="apps/pipe/pipeline/layingBySec.xml&amp;secId=${id}&amp;showHoleSelect=${showHoleSelect}" permission="SUPPORTING_LAYING" />

  <action name="layingByGrid" permission="SUPPORTING_LAYING" label="列表敷设" label-en="LayingGrid" title="敷设-${NAME}" title-en="Laying -${NAME}" contentUrl="apps/pipe/pipeline/layingGrid.html" type="window" width="760" height="660" resizable="true" />
  <action name="clearLaying" permission="SUPPORTING_LAYING" label="删除路由" url="apps/pipe/pipeline/clearLaying.js" type="script" />

  <!-- 光电缆定位 -->
  <action name="locateNetCable" type="script" label="定位" label-en="Locate" permission="BASIC_MAP" url="apps/pipe/cable/locateNetCable.js" />

  <!--设备熔接 -->
  <action type="workspace" name="fuseview" label="熔接图" label-en="Fusion Diagram" icon="connectdevelop" url="fusefiber.html?objecttype=${objectType}&amp;id=${id}" extra="_ui_class:gray" title="纤芯熔接图-${NAME}" title-en="Core splice diagram -${NAME}" permission="APPLY_OUTLOGICALMAP"></action>
  <!-- 端子面板图 -->
  <action type="workspace" name="connview-${id}" id="connview" permission="OPTICAL_FIND" label="端子图" label-en="Panel Diagram" icon="delicious" url="connector.html?objecttype=${objectType}&amp;id=${id}" extra="_ui_class:gray" title="端子面板图-${NAME}" title-en="Panel Diagram-${NAME}"></action>
  <!-- 端子面板图（正反面） -->
  <action type="workspace" name="portDiagram" permission="OPTICAL_FIND" label="正反面端子图" label-en="Panel Diagram" icon="delicious" mainConfig="modules/connector/portDiagram/index.xml&amp;id=${id}" extra="_ui_class:gray" title="正反面端子面板图-${NAME}" title-en="Panel Diagram-${NAME}"></action>
  <!-- 设备详情 -->
  <action type="workspace" class="fullscreen" style="height:600px;" name="devInfo-${id}" id="devInfo" label="设备详情" label-en="Panel Diagram" icon="delicious" url="modules/connector/devInfo.html?objecttype=${objectType}&amp;id=${id}" extra="_ui_class:gray" title="${type}详情-${NAME}" title-en="Device Info -${NAME}" />

  <!--附属设备管理 -->
  <action type="window" class="fullscreen" style="height:600px;" name="attachdevice_man" label="附属设备" label-en="Affiliated Equipment" title="附属设备管理 ${NAME}" title-en="Affiliated Equipment ${NAME}" mainConfig="modules/odevice/attachdevices.xml&amp;deviceId=${id}" icon="archive" permission="OPTICAL_FIND" />

  <!--成端管理 -->
  <action type="workspace" name="chengduan" id="chengduan" label="成端管理" label-en="chengduan" icon="delicious" url="modules/chengduan/chengduan.html?devid=${id}" extra="_ui_class:gray" title="成端管理图${NAME}" title-en="chengduan"></action>

  <!--设备标签打印-->
  <action type="workspace" name="biaoqiandayin" id="biaoqiandayin" label="设备标签打印" label-en="biaoqiandayin" icon="delicious" mainConfig="apps/gdo3/labelprint/deviceLabel/index.xml&amp;deviceId=${id}" extra="_ui_class:gray" title="设备标签打印-${NAME}" title-en="biaoqiandayin"></action>

  <!--光路标签打印(按设备)-->
  <action type="workspace" name="deviceOpticalcircuitLabel" label="光路标签打印(按设备)" icon="delicious" mainConfig="apps/gdo3/labelprint/opticalcircuitLabel/index_dev.xml&amp;deviceId=${id}" extra="_ui_class:gray" title="光路标签打印(按设备)-${NAME}"></action>

  <!--光缆段局向信息标签-->
  <action type="workspace" name="siteFiberLabel" label="光缆段局向信息标签" icon="delicious" mainConfig="apps/gdo3/labelprint/siteFiberLabel/index.xml&amp;deviceName=${NAME}&amp;deviceMetacategory=${METACATEGORY}" extra="_ui_class:gray" title="光缆局向信息标签-${NAME}"></action>

  <!--直达缆段有部分未成端纤芯-->
  <action type="window" name="zhida" id="zhida" style="height:500px;width:700px;" label="直达缆段有部分未成端纤芯" label-en="zhida" icon="delicious" mainConfig="modules/chengduan/zhida.xml&amp;id=${id}" extra="_ui_class:gray" title="直达缆段有部分未成端纤芯" title-en="zhida"></action>

  <!--拓扑图-->
  <action type="workspace" name="tuputu" id="tuputu" label="拓扑图" label-en="tuputu" icon="delicious" url="http://132.121.94.168:8802/bigdataplatform/com.ccssoft.bigdataplatform.application.web.view.ShowDeviceSafeAnalysis.d?deviceId=${id}&amp;shardingId=${sharding_id}" extra="_ui_class:gray" title="拓扑图" title-en="tuputu"></action>

  <!-- 设备占用率 -->
  <action type="workspace" name="deviceOccupyRate" id="deviceOccupyRate" label="设备占用率" label-en="deviceOccupyRate" icon="delicious" mainConfig="modules/chengduan/deviceOccupyRate.xml&amp;id=${id}&amp;spec_name=${specName}" extra="_ui_class:gray" title="设备占用率" title-en="deviceOccupyRate"></action>

  <!--设备映射-->
  <action type="window" name="deviceMapping" width="800" label="设备映射" label-en="deviceMapping" contentUrl="apps\gdo3\deviceMapping\deviceMapping.html" />

  <!--退网状态维护-->
  <action type="window" name="backOffNetworkStatusRepair" style="width:400px;height:182px;" label="退网状态维护" label-en="backOffNetworkStatusRepair" contentUrl="apps\gdo3\backoffnetwork\backOffNetworkStatusRepair.html" />

  <!--覆盖地址管理 -->
  <action type="window" permission="ADDRESS_FIND" class="large" style="height:600px;" name="devicecoveraddress_man" label="覆盖地址" label-en="Coverage Address" title="覆盖地址管理 ${NAME}" title-en="Coverage Address ${NAME}" icon="map marker alternate" mainConfig="modules/address/devicecoveraddress.xml&amp;deviceId=${id}" />

  <!-- 附件管理 -->
  <action id="filesman" permission="BASIC_ATTACHMANAGE" name="filesman-${id}" label="文件管理" label-en="File" title="文件管理-${NAME}" title-en="File Management-${NAME}" type="workspace" objectType="FILE" icon="folder open" options='{"baseParams":{"ENTITY_SPEC_ID": "${objectType}", "ENTITY_ID":"${id}"}}' />
  <!-- 3D模型预览 -->
  <action id="view3D" label="3D模型" label-en="3D model" title="3D-${NAME}" title-en="3D-${NAME}" type="window" width="600px" height="400px" mainConfig="modules/3d/3dpreview.xml" urlParams="objectType=${objectType}&amp;id=${id}" />

  <action id="showMapLegend" title="{{资源图例|en:legend}}" type="modal" class="small" style="background:transparent;">
    <![CDATA[
			<img src="apps/pipe/assets/map_legend.png" style="width:100%;height:100%;">
		]]>
  </action>

  <action id="opticMapLocate" label="多光缆光路定位" type="script" url="modules/map/opticMapLocate.js" />
  <action id="opticMapLocateZj" label="浙江光路定位" type="script" url="modules/map/opticMaplocateZj.js" />

  <action name="abilityAndBusiness" type="workspace" class="large" label="能力和业务展示" title="${NAME}-能力和业务展示" mainConfig="apps/pipe/pipeline/abilityandbusiness/abilityAndBusiness.xml" urlParams="pipeId=${id}" />
  <action name="abilityAndBusinessOCable" type="workspace" class="large" label="能力和业务展示" title="${NAME}-能力和业务展示" mainConfig="apps/pipe/pipeline/abilityandbusiness/abilityAndBusiness.xml" urlParams="ocablesectionId=${id}&amp;type=ocablesection" />
  <!--支撑段查看缆线-->
  <action name="showCable" type="workspace" label="查看缆线" title="${NAME}-缆线信息" mainConfig="apps/pipe/pipeline/showCable/pipelineShowCable.xml" urlParams="pipeId=${id}" />

  <!--资产卡片-->
  <action name="assetCard" label="资产卡片" label-en="asset card" type="script" url="modules/asset/assetCard.js" />

  <!--支撑段的缆线复制-->
  <action name="cableCopy" class="large" height="600px" label="缆线复制" title="缆线复制" label-en="cable copy" type="window" mainConfig="apps/pipe/pipeline/layingCopyAndCut/cableCopyAndCut.xml" urlParams="spec=${objectType}&amp;sourceId=${id}&amp;opt=copy" />

  <!--支撑段的缆线剪切-->
  <action name="cableCut" class="large" height="600px" label="缆线剪切" title="缆线剪切" label-en="cable cut" type="window" mainConfig="apps/pipe/pipeline/layingCopyAndCut/cableCopyAndCut.xml" urlParams="spec=${objectType}&amp;sourceId=${id}&amp;opt=cut" />

  <!--设施段影响分析-->
  <action name="pipelineImpactAnalysis" type="workspace" class="large" label="设施段影响分析" title="${NAME}-设施段影响分析" mainConfig="apps/pipe/pipeline/pipelineImpactAnalysis/pipelineImpactAnalysis.xml" urlParams="initId=${id}&amp;spec=${METACATEGORY}" />

  <!--转闲置-->
  <action name="setidle" label="转闲置" type="window" width="800px" height="150px" contentUrl="apps/pipe/idle/setidle.html" />

  <action name="bindODevice" permission="OPTICAL_DEVICEBIND" label="关联设备管理" type="script">
    <![CDATA[
			addWorkspace("关联设备管理", {url: "main.html?config=modules/odevice/bindDevice/bindDevice.xml&devid="+_actionContext.params.id+"&mc="+_actionContext.params.METACATEGORY}, false, false);
		]]>
  </action>
  <!--调用图纸管理的逻辑图-->
  <action name="invokeLogicDiagram" type="script" label="逻辑图">
    <![CDATA[
			let item = _actionContext.params;
			_util.loadScript('lib/common/index.js').then(res=>{
          let catname="逻辑图";
          if(item.METACATEGORY=="OBD"||item.METACATEGORY=="ONU")
            catname="PON拓扑图"
			    $.diagramOperation.openDiagram(catname, item.METACATEGORY?item.METACATEGORY:item.objectType, item.id, '逻辑图-' + (item.NAME?item.NAME:item.id))
			 })
		]]>
  </action>
  <action name="invokePONDiagram" type="script" label="逻辑图">
    <![CDATA[
			let item = _actionContext.params;
			let category = 18;
			_bean.action("DIAGRAM","findDiagramByDevice",{"devid":item.id,"category":category}).then(function(diagram)
			{
				if(diagram)
				{
					addWorkspace("逻辑图:"+diagram.NAME, {type:'workspace',url:'modules/diagram/diagram_editor.html?id='+diagram.id});
				}
				else
				{
					alert("不存在对应的逻辑图");
					return;
				}
			});
		]]>
  </action>
  <!--设备调用图纸管理的路由图-->
  <action name="invokeRouteDiagram" type="script" label="路由图">
    <![CDATA[
			let item = _actionContext.params;
			 _util.loadScript('lib/common/index.js').then(res=>{
			    $.diagramOperation.openDiagram('路由图', item.METACATEGORY?item.METACATEGORY:item.objectType, item.id, '路由图-' + (item.NAME?item.NAME:item.id))
			 })
		]]>
  </action>
  <!---多数据定位-->
  <action name="locateDatas" label="多条定位" label-en="locate" type="script" url="apps/pipe/gridarea/moreDataLocate.js" />

  <!--关联设备管理-->
  <action name="deviceRelaDeviceMgt" permission="OPTICAL_DEVICEBIND" label="关联设备管理" title="关联设备管理--原设备:${NAME}" type="window" class="large" height="600px" mainConfig="modules/odevice/deviceRelaDeviceMgt/deviceRelaDevice.xml" urlParams="deviceId=${id}" />

  <!-- 故障点查询 -->
  <action id="faultpoint_locate" label="定位故障点" label-en="locate fault point" type="window" width="800px" height="85vh" mainConfig="apps/pipe/ocable/faultpoint_locate.xml" />
  <!-- 光路配置 -->
  <action id="optic_search" title="{{g5.menu.opticSearch}}" type="workspace" url="modules/opticroute/opticroute_search.html" permission="OPTICAL_ROUTEMANAGE" />
  <action id="showCablesByOptic" label="光缆" title="光缆信息-${CODE}" type="modal" style="padding:2px;" objectType="OCABLESECTION" options='{"gridName":"byoptic","queryActionName":"queryByOptic","baseParams":{"opticId":"${id}"}}' />
  <action id="ftth_info" label="FTTH客户资料" type="window" width="80vw" height="60vh" contentUrl="apps/pipe/ocable/optic/ftth_info.html" />
  <action id="access_check" label="通路检查" title="通路检查 ${NAME}" type="window" width="85vw" height="85vh" mainConfig="apps/pipe/ocable/optic/access_check.xml" />
  <action id="optic_schedule" label="应急调度" title="应急调度-${ordercode}" type="window" width="85vw" height="85vh" contentUrl='apps/scheduling/opticschedule.html' />

  <action name="coveragestat1" label="测试窗口" icon="chart area" type="script">
    <![CDATA[
			var item = {
				title: '测试窗口',
				type: 'window',
				icon: 'chart area',
				maxable: false, style: 'width: 300px',
				contentUrl: 'test.html',
			}
			_context.doAction(item)
        ]]>
  </action>

  <action name="show_near_entities" label="附近资源" title="${NAME}-附近资源" type="window" width="70vw" height="600px" contentUrl="modules/map/near_entities.html" />
  <action name="show_hanglingsection_expandview" permission="SUPPORTING_PANORAMAFIND" label="展开图" title="${NAME}-展开图" icon="keyboard" type="window" width="800px" height="420px" contentUrl="apps/pipe/pipeline/hanglingsection/expandview.html" />
  <!--GF群管理 -->
  <action type="workspace" name="gfgruop_mgr" label="GF管理" label-en="GF Management" title="GF管理 ${CODE}" title-en="GF Management ${CODE}" mainConfig="modules/odevice/gfgroupMgr.xml&amp;deviceId=${id}" icon="archive" />

  <action name="locates" label="多点定位" itemsable="true" type="window" width="800" height="400" contentUrl="modules/map/locates.html" />
  <action name="show_announce" label="显示公告" type="script" url="modules/common/show_announce.js" />

  <action id="addBuildingAddress" label="新增政企楼宇地址" title="新增政企楼宇地址" type="modal" class="small" closable="false" contentUrl='modules/building/addBuildingAddress.html' />
  <action id="addJQcode" label="选择局代码组合" title="选择局代码组合" type="modal" class="small" closable="false" contentUrl='modules/building/addJQcode.html' />
  <action id="floorManager" label="楼层管理" title="楼层管理" type="modal" class="mini" contentUrl='modules/building/floorManager.html' />
  <action id="buildingRelateGJ" label="关联设备" title="关联设备:${NAME}(${CODE})" type="window" width="1000" height="500" contentUrl='modules/building/buildingRelateGJ.html' />
  <!--杭州-云网资源中心第一轮用户测试-问题管理-26410-点击关联沿街商铺提示楼宇关联的6级地址不存在 -->
  <action id="buildingRelateShop" label="关联沿街商铺" title="关联沿街商铺:${NAME}(${CODE})" type="script">
    <![CDATA[
  		var item=_actionContext.params;
  		//alert("123456");
  		_context.doAction({"type":'window',"contentUrl":'modules/building/addRelationShop.html',"title":'关联沿街商铺'+item.CODE,width:800,height:500},{"item":item});
   ]]>
  </action>
  <action id="addShopAddress" label="新增沿街商铺地址" title="新增沿街商铺地址" type="modal" class="mini" contentUrl='modules/building/addShopAddress.html' />
  <action id="addRoomAddress" label="新增机房地址" title="新增机房地址" type="modal" class="small" closable="false" contentUrl='modules/building/addRoomAddress.html' />
  <action id="gridmergeGrid" permission="GRID_GRIDMERGEGRID" label="合并网格" label-en="merge grid" type="script" icon="clone">
    <![CDATA[
                var item = _actionContext.params;
                let url = 'bean/action.do?_type=X_GIS_HIGHDANGER_SMALLUNIT&name=queryHighDancer';
				let param = {smallunitId:item.id,_showLoading:true};
			    _bean.post(url,param).then(function (data) {
			        if(data){
			        	_sui.alert("高危网格，禁止操作!!!");
			        }else{
			        	 _context.doAction({
				           		type: 'modal',  closable: false, active: false,
				           		contentUrl: 'apps/pipe/gridarea/GridMerge.html',
				           		urlParams: {params:item}
			           		}
		           		);
			        }
			    })
            ]]>
  </action>
  <action id="gridmoveGrid" permission="GRID_GRIDMOVEGRID" label="移动网格" label-en="move grid" icon="expand arrows alternate" type="script">
    <![CDATA[
                var item = _actionContext.params;
                let url = 'bean/action.do?_type=X_GIS_HIGHDANGER_SMALLUNIT&name=queryHighDancer';
				let param = {smallunitId:item.id,_showLoading:true};
			    _bean.post(url,param).then(function (data) {
			        if(data){
			        	_sui.alert("高危网格，禁止操作!!!");
			        }else{
			        	 _context.doAction({
				           		type: 'modal',  closable: false, active: false,
				           		contentUrl: 'apps/pipe/gridarea/GridMove.html',
				           		urlParams: {params:item}
			           		}
		           		);
			        }
			    })
            ]]>
  </action>
  <action id="gridremove" label="删除" label-en="Delete" type="script" icon="delete circle" _ui_class="red">
    <![CDATA[
                var item = _actionContext.params;
                item.removeConfirmText = _locale.getString('g5.application.gridarea.removeRelationConfirmText');
                _context.doAction({type: 'obj', name: 'remove'}, item);
            ]]>
  </action>
  <!--综合接入区 - 添加图形-->
  <action name="addImg" label="添加图形" label-en="Add polygon" icon="image" type="script">
    <![CDATA[
	         var item = _actionContext.params;
            var drawType= 'polygon';
            _bean.getGeomField('ACCESSAREAZJZ').then(function (geomField) {
                var om = _bean.getMetaForce('POSITION');
                var drawOptions = {
                    draw: {
                        circle: false,
                        rectangle: false,
                        marker: false,
                        polyline: false,
                        polygon: false
                    }
                };
                if (drawType) drawOptions.draw[drawType] = true;
                drawOptions.initMode = drawType;
                activeWorkspace(_locale.getString('g5.map.title'));
                _context.map.clear();
                _context.map.activeDrawSelect(false);
                _context.map.activeDraw(drawOptions, function (type, graphic) {
                    var gra = _context.map.getWkt(graphic);
                    _bean.post('bean/action.do?_type=ACCESSAREAZJZ&name=locateMap',{id: item.id, shape: gra,_showLoading:true}).then(function () {
                        // console.log(gra);
                        _context.map.clear();
                        _context.map.activeDraw(false);
                        alert(_locale.getString("g5.application.common.alert.locatesuccess"));
                    });

                });
            });
            ]]>
  </action>
  <!--综合接入区 - 删除图形-->
  <action name="delImg" label="删除图形" icon="image" label-en="Delete polygon" type="script">
    <![CDATA[
                var item = _actionContext.params;
               _bean.post('bean/action.do?_type=ACCESSAREAZJZ&name=queryAccessAreaShapeList',{id: item.id,_showLoading:true}).then(function (data) {
                        if(data == null || data.length < 1){
                          alert('该接入区未落图，不能进行删除操作');
                        }else if(data.length==1){
                         _sui.confirm('请确认，是否删除综合接入区图形，确认删除后该综合接入区不会关联资源信息','提示','确认','取消').then(function(){
                          _bean.action('ACCESSAREAZJZ', 'deletePolyInfo', {accessAreaId:item.id,_showLoading: true});
                          });
                       }
                 });
            ]]>
  </action>
  <!-- 支撑点转电杆 -->
  <action type="script" name="toPole" label="转电杆" label-en="To Pole" icon="exchange">
    <![CDATA[
            //$('body').append('<div id="blockdiv" style="display:flex;position:absolute;top:0;left:0;width:100%;height:100%;background:#A0A0A0;opacity:0.8;filter:alpha(opacity=80);z-index:10000;justify-content:center;align-items:center">' +
        //'<h1>'+_locale.getString('g5.application.supportApplication.loadingmsg')+'</h1>' +
       // '</div>');
            var supportPoint = _actionContext.params;
            _bean.find('SITE',supportPoint.TML_ID).then(function(site){
                 var pre = site.CODE + '/P';
                _bean.autoCode(site, pre, 4, 'POLE', {site: site.id}).then(function(code){
                    _bean.action('SUPPORTPOINT','toPole',{id:supportPoint.id,code:code,'_showLoading':true}).then(function(){
                     //$('#blockdiv').remove();
                        _context.map.refresh();
                    });
                });
            });
        ]]>
  </action>
  <!-- 电杆转支撑点 -->
  <action type="script" name="toSupportPoint" label="转支撑点" label-en="To SupportPoint" icon="exchange">
    <![CDATA[
                //$('body').append('<div id="blockdiv" style="display:flex;position:absolute;top:0;left:0;width:100%;height:100%;background:#A0A0A0;opacity:0.8;filter:alpha(opacity=80);z-index:10000;justify-content:center;align-items:center">' +
        //'<h1>'+_locale.getString('g5.application.supportApplication.loadingmsg')+'</h1>' +
        //'</div>');
        		  ;
                  var pole = _actionContext.params;
                  _bean.find('SITE',pole.TML_ID).then(function(site){
                      var pre = site.CODE + '/CD';
                     _bean.autoCode(site, pre, 4, 'SUPPORTPOINT', {site: site.id}).then(function(code){
                        _bean.action('POLE','toSupporintPoint',{id:pole.id,code:code,'_showLoading':true}).then(function(){
                         	//$('#blockdiv').remove();
                            _context.map.refresh();
                        });
                     });
                  });
            ]]>
  </action>

  <!-- 资源关联的电缆 -->
  <!--70555 【浙江】【演示评审问题跟踪】接头有所属缆但是关联电缆中无电缆信息展示 -->
  <!--杭州-云网资源中心第一轮用户测试-问题管理-26090、26088、26086、26084、26081  -->
  <action id="connectESection" label="关联电缆" title="关联电缆-${NAME}" type="workspace" style=""
    objectType="ECABLE" options='{"gridName":"RelationEcable","queryActionName":"queryRelationEcable","baseParams":{"resourceType":"${objectType}","resourceId":"${id}"},"autoLoad":true}' />
  <!-- 资源关联的光缆 -->
  <action id="resRelationCables" label="关联光缆" title="关联光缆-${NAME}" type="workspace" style=""
    objectType="OCABLE" options='{"gridName":"resRelationCableGrid","queryActionName":"queryResourceRefCables","baseParams":{"resourceType":"${objectType}","resourceId":"${id}"},"autoLoad":true}' />
  <!--关联光缆-不能新增光缆  -->
  <action id="resRelationCables2" label="关联光缆" title="关联光缆-${NAME}" type="workspace" style=""
    objectType="OCABLE" options='{"gridName":"resRelationCableGrid2","queryActionName":"queryResourceRefCables","baseParams":{"resourceType":"${objectType}","resourceId":"${id}"},"autoLoad":true}' />

  <!--新增光缆 -->
  <!--杭州-云网资源中心第一轮用户测试-问题管理-26767-->
  <action name="addOCABLE" label="新增" label-en="New" type="script" icon="add circle">
    <![CDATA[
     		;
     		var deviceId=_actionContext.source.baseParams.resourceId;
     		var deviceType=_actionContext.source.baseParams.resourceType;
     		_bean.find(deviceType, {id:deviceId}).then(function (device) {
     		    ;
     		    var siteId = '';
     		    var pre ='';
     		    if(device.objectType == 'SITE'){
     		        siteId = device.id;
     		        pre = device.CODE + "/PXG";
     		         _bean.autoCode(this, pre, 3, 'OCABLE', {site:siteId}).then(function(code){
                     _context.doAction({type:'obj',name:'add',objectType:'OCABLE'},{baseParams:{"NAME":code,"CODE":code,"site":siteId,"CABLE_TYPE_ID":80202924}});
                  });
     		    }else{
                  siteId = device.TML_ID;
                  pre = device.CODE + "/PXG";
                  var devId = device.id;
                  _bean.autoCode(this, pre, 3, 'OCABLE', {adevice:devId}).then(function(code){
                     _context.doAction({type:'obj',name:'add',objectType:'OCABLE'},{baseParams:{"adevice":devId,"NAME":code,"CODE":code,"site":siteId,"CABLE_TYPE_ID":80202924}});
                  });
     		    }


     		});
     ]]>
  </action>
  <action name="addECABLE" label="新增" label-en="New" type="script" icon="add circle">
    <![CDATA[
     		;
     		var deviceId=_actionContext.source.baseParams.resourceId;
     		var deviceType=_actionContext.source.baseParams.resourceType;
     		_context.doAction({type:'obj',name:'add',objectType:'ECABLE'},{baseParams:{"adevice":deviceId}});
     ]]>
  </action>
  <action name="listRelationGFHtml" label="关联光分线盒" type="window" width="1000px" height="400px" title="关联分线盒-${NAME}" overflow="auto" contentUrl="modules/odevice/gjListRelationGF.html" />
  <!-- 浙江 局站/机房 需要校验是否有权限 -->
  <action name="addbypermission" type="script" label="新增" url="apps/zjz/modules/common/addbypermission.js" flag="0" />

  <action name="show-ocables" label="光缆管理" type="script">
    <![CDATA[
      var params = _actionContext.params;
      if (params.objectType === 'OCABLESECTION') {
        _bean.find(params.objectType, {id: params.id, _assemble: 'net'}).then(function(cable){
          _context.doAction({type: 'window', title: '光缆管理-' + cable.net.CODE, width: 800, height: 600, objectType: 'OCABLE', options: {baseParams: {id: cable.net.id}}});
        });
      }
  ]]>
  </action>

  <!--资产属性-->
  <action name="assetAttribute" type="script" label="资产属性">
    <![CDATA[
        let params = _actionContext.params;
        window.open("http://*************:7001/threecode-web/#/asset/assetDetail?entityId=" + params.id + "");
    ]]>
  </action>

  <!--重置-->
  <action name="reset" type="script" label="重置" label-en="Reset" icon="circle outline">
    <![CDATA[
        _actionContext.source.toolbar.find('input').val('');
        this.baseParams={};
        _actionContext.source.tbody.empty();
    ]]>
  </action>

  <!--网格属性-->
  <action name="gridElement" type="script" label="网格属性" label-en="Grid Element">
    <![CDATA[
        let addressId = _actionContext.params.ADDRESS_ID;
        _bean.callService("gridServiceImpl", "getGridElement", [addressId]).then(function (data) {
            if(!data){
                alert("查无网格属性");
                return;
            }
            let content=
            '<div class="ui equal width form attached tab active" data-tab="对象属性">'+
            '	<div class="ui equal width form compact" style="padding:10px">'+
            //'		<div class="ui horizontal divider">网格属性</div>'+
            '		<div class="fields inline">'+
            '			<div class="field" style="display: flex;">'+
            '				<label title="网格单元ID" style="align-items: center; width: 6rem;">网格单元ID</label>'+
            '				<input name="gridUnitId" type="text" style="flex-grow: 1; width: 4em;" readonly="readOnly">'+
            '			</div>'+
            '		</div>'+
            '		<div class="fields inline">'+
            '			<div class="field" style="display: flex;">'+
            '				<label title="网格单元名称" style="align-items: center; width: 6rem;">网格单元名称</label>'+
            '				<input name="gridUnitName" type="text" style="flex-grow: 1; width: 4em;" readonly="readOnly">'+
            '			</div>'+
            '		</div>'+
            '		<div class="fields inline">'+
            '			<div class="field" style="display: flex;">'+
            '				<label title="网格编码" style="align-items: center; width: 6rem;">网格编码</label>'+
            '				<input name="gridUnitCode" type="text" style="flex-grow: 1; width: 4em;" readonly="readOnly">'+
            '			</div>'+
            '		</div>'+
            '		<div class="fields inline">'+
            '			<div class="field" style="display: flex;">'+
            '				<label title="县分名称" style="align-items: center; width: 6rem;">县分名称</label>'+
            '				<input name="unitBranchOrgName" type="text" style="flex-grow: 1; width: 4em;" readonly="readOnly">'+
            '			</div>'+
            '		</div>'+
            '		<div class="fields inline">'+
            '			<div class="field" style="display: flex;">'+
            '				<label title="营服名称" style="align-items: center; width: 6rem;">营服名称</label>'+
            '				<input name="unitManageOrgName" type="text" style="flex-grow: 1; width: 4em;" readonly="readOnly">'+
            '			</div>'+
            '		</div>'+
            '		<div class="fields inline">'+
            '			<div class="field" style="display: flex;">'+
            '				<label title="建筑物群ID" style="align-items: center; width: 6rem;">建筑物群ID</label>'+
            '				<input name="buildingId" type="text" style="flex-grow: 1; width: 4em;" readonly="readOnly">'+
            '			</div>'+
            '		</div>'+
            '		<div class="fields inline">'+
            '			<div class="field" style="display: flex;">'+
            '				<label title="建筑群物名称" style="align-items: center; width: 6rem;">建筑群物名称</label>'+
            '				<input name="buildingName" type="text" style="flex-grow: 1; width: 4em;" readonly="readOnly">'+
            '			</div>'+
            '		</div>'+
            '	</div>'+
            '</div>';
            let win = _sui.showCommonModal('网格属性', content, [], {onApprove:false,closeable:false});
            // 
            win.find('input[name="gridUnitId"]').val(data.gridUnitId);
            win.find('input[name="gridUnitName"]').val(data.gridUnitName);
            win.find('input[name="gridUnitCode"]').val(data.gridUnitCode);
            win.find('input[name="unitBranchOrgName"]').val(data.unitBranchOrgName);
            win.find('input[name="unitManageOrgName"]').val(data.unitManageOrgName);
            win.find('input[name="buildingId"]').val(data.buildingId);
            win.find('input[name="buildingName"]').val(data.buildingName);
        });
    ]]>
  </action>

  <!--工程流水-->
  <action name="projectSerial" type="script" label="工程流水" label-en="project serial">
    <![CDATA[
        let params = _actionContext.params;
        let telantflow = "http://**************:9001/websg/interfaceTab.action?serialNo=";
        _bean.find('OBD', {id: params.id, _assemble: 'project'}).then(function(data){
            if(!data || !data.project || !data.project.SERIALNO) {
                _sui.alert("该记录无工程流程水号");
                return;
            }
            telantflow += data.project.SERIALNO;
            window.open(telantflow);
        });
    ]]>
  </action>

  <!--关联电缆段-->
  <action name="relateECablesection" type="window" label="关联电缆段" title="关联电缆段-${NAME}" objectType="ECABLESECTION" width="800" height="600"
    options='{"gridName": "resourcesRelationEcablesectionGrid", "showSearch": false, "autoLoad": true, "pagable": false, "baseParams": {"A_DEVICE_ID": "${id}","Z_DEVICE_ID":"${id}","_opgroup":"or(A_DEVICE_ID,Z_DEVICE_ID)"}, "actions": "", "extraActions": "", "queryActionName": ""}' />

  <!--关联光分纤箱-->
  <action name="showRelateODevice" label="关联光设备" label-en="Relation ODevice" title="查看关联光设备-${NAME}" title-en="Relation ODevice -${NAME}" contentUrl="apps/gdo3/relate/deviceRelateODevice.html" type="window" width="650px" height="450px" resizable="true" />

  <!--关联光缆段-->
  <action name="relateOCablesection" type="window" label="关联光缆段" title="关联光缆段-${NAME}" objectType="OCABLESECTION" width="800" height="600"
    options='{"gridName": "resourcesRelationOcablesectionGrid", "showSearch": false, "autoLoad": true, "pagable": false, "baseParams": {"A_DEVICE_ID": "${id}","Z_DEVICE_ID":"${id}","_opgroup":"or(A_DEVICE_ID,Z_DEVICE_ID)"}, "actions": "", "extraActions": "", "queryActionName": ""}' />

  <!--相关局向光纤组-->
  <action name="relateFiberGroup" label="相关局向光纤组" label-en="Relate Fiber Group" title="相关局向光纤组-${NAME}" title-en="Relation Fiber Group -${NAME}" contentUrl="apps/pipe/fiberComprehensiveQuery/fiberComprehensiveQuery.html" urlParams='{"adeviceId":"${id}"}' type="window" width="1000" height="700" resizable="true" />

  <!--相关接头-->
  <action name="relateGT" type="workspace" label="相关接头" title="相关接头-${NAME}" objectType="GT"
    options='{"gridName": "resourcesRelationGTGrid", "showSearch": false, "autoLoad": true, "baseParams": {"net": "${id}"}}' />
  <!-- 端子面板-端子详情-工程关联 -->
  <action name="DeviceProjectRelation" type="window" label="${NAME}-设备详情-工程关联" label-en="project serial" width="600">
    <![CDATA[
      <script>
        let params = _actionContext.params;
        let url = "bean/action.do?_type=PORT&name=getProjectRelationByDeviceID";
    	  let param = {_showLoading:true,DEVICEID:params.id};
    	  _bean.post(url,param).then(function (data) {
    		  ;
        	_bean.grid('ODEVICE', 'deviceprojectinfo', _actionContext.el, {data: data,rowDetailField:false,metaGrid:{
                    typeActions: 'expgrid',
                    extraActions: '',
                    showSearch: false,
                    itemActions:[],
                    moreItemActions: [],
          }});
          
    	  });
      </script>
    ]]>

  </action>

  <action name="addressman" label="标准地址管理" type="window" width="1000px" height="600px" mainConfig="apps/gdo3/address/man.xml" />

  <!--释放虚占端子-->
  <action name="releaseport" type="script" label="释放虚占端子" label-en="Release Port" itemsable="true">
    <![CDATA[
        let params = _actionContext.params;
        if(!params || params.length <= 0) {
            throw new Error('请选择记录');
        }
        var ids = params.map(item => {
            return item.id
        })
        _bean.callService("cableServiceImpl", "releaseVirtualport", [ids]).then(function (data) {
            if(data.length <= 0) {
                throw new Error('查无数据');
            }
            _context.doAction({type:"window",label:"返回结果列表",contentUrl:"apps/gdo3/ecable/releaseport.html"}, {datas: data});
        });
    ]]>
  </action>
  <action id="introduceLocation" label="引入第三方辅助数据定位" title="引入第三方辅助数据定位" type="workspace" contentUrl='apps/gdo3/samerouteanaly/introduceLocation.html' />

  <action name="nearDrillingAddressResource" type="window" label="钻取附近落图信息" label-en="钻取附近落图信息" icon="bullseye" contentUrl="apps/gdo3/address/nearDrillingAddressResource/index.html?addressId" title="钻取附近落图信息" title-en="钻取附近落图信息" target="*" gridMenuShowObjTypes="POSITION" width="890" height="600" />

  <action name="drawtool" title="请选择绘制方式并在地图上进行绘制" type="window" width="400" contentUrl="modules/map/drawtool.html" context="top" />

  <!-- 三资三率图 -->
  <action name="TreResTreProdPic" label="三资三率图" type="window" width="1400" height="1000" contentUrl="apps/gdo3/address/TreResTreProd/TreResTreProdPic.html" urlParams='{"addressId":"${id}","buildingAddressId":"${BUILDING_ADDRESS_ID}"}' resizable="true" if="!sourceui || sourceui.source !== 'map'"/>

  <action name="addObdFromFttxsolution" label="新增OBD" label-en="New" type="script" icon="add circle">
    <![CDATA[
            // 父级光交id
     		let parentDevId = _actionContext.source.baseParams.parentDevice
            // 父级光交类型
     		let parentDevObjectType = _actionContext.source.baseParams.parentObjectType
            // 当前FTTX模块化方案工程
     		let parentCurrentProjectId = _actionContext.source.baseParams.parentCurrentProjectId
     		if(!parentDevId || !parentDevObjectType) {
     		    _sui.alert('点击的光交设施信息有误')
     		}
     		_bean.find(parentDevObjectType, {id: parentDevId}).then(function (entity) {
                _context.doAction({
                      type: 'obj',
                      name: 'add',
                      objectType: 'OBD',
                      data: {
                          project: parentCurrentProjectId,
                          parentDevice: entity
                      }
                })
     		})
     ]]>
  </action>

  <action name="releAddress" label="关联地址" label-en="Rele Address" type="script"
          url="apps/pipe/address/action/releAddress.js"/>
</actions>