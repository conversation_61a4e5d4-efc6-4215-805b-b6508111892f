<?xml version="1.0" encoding="utf-8"?>
<metas>
    <!-- 浙江电信O3项目 TASK 66843 屏蔽导出全部，其它地方使用在配置文件加上即可 ,  extraActions="imp,exp" 改为extraActions="imp,exp,expall"-->
    <ObjMeta objectType="*" typeActions="add,remove" itemActions="locate,modify,remove"
             extraActions="expgrid,exportAll,offlineExportAll">
        <field name="NAME"/>
        <field name="CODE"/>
        <grid>
            <field name="NAME"/>
            <field name="CODE"/>
        </grid>
        <grid name="search" appendColumnActions="view" appendColumnActionsStyle="width:5em">
          <field name="NAME" />
          <field name="CODE" />
          <field name="site" />
        </grid>
        <script>
            <![CDATA[
            if (!om.imageField) {
                om.imageField = function(o) {
                    if (!om.previewFile) return false;
                    var previewFileName;
                    if (typeof om.previewFile === 'string') previewFileName = om.previewFile;
                    return _bean.find('FILE', {ENTITY_ID: o.id, ENTITY_SPEC_ID: o.objectType, NAME: previewFileName}).then(function(file){
                        return file ? 'bean/getfile.do?_type=FILE&id=' + file.id : false;
                    });
                };
                if (!om.imageAction) om.imageAction = 'filesman';
            }
        ]]>
        </script>
    </ObjMeta>
    <!-- 光配线架型号 -->
    <ObjMeta objectType="TYPEODF">
        <!--新增/修改面板-->
        <form>
            <row>
                <field name="CREATOR" label="创建人" readOnly="true"/>
                <field name="CREATOR_ID" label="创建人ID" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFIER_ID" label="修改人ID" readOnly="true"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="创建时间" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="SHARDING_ID" type="number" readOnly="true"/>
                <field name="VERSION" label="版本" readOnly="true"/>
            </row>
            <row>
                <field name="MODEL" label="型号" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="TYPESPEC_ID" label="类型规格ID" readOnly="true"/>
                <field name="VENDOR_IDz" label="生产厂家" readOnly="true"/>
            </row>
        </form>
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="MODEL" label="型号"/>
                <field name="NAME" label="名称"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid>
            <field name="MODEL" label="型号"/>
            <field name="NAME" label="名称"/>
        </grid>
        <grid name="search">
            <field name="MODEL" label="型号"/>
            <field name="NAME" label="名称"/>
        </grid>
    </ObjMeta>
    <!-- 综合配线架型号 -->
    <ObjMeta objectType="TYPEIDF">
        <!--新增/修改面板-->
        <form>
            <row>
                <field name="CREATOR" label="创建人" readOnly="true"/>
                <field name="CREATOR_ID" label="创建人ID" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFIER_ID" label="修改人ID" readOnly="true"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="创建时间" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="SHARDING_ID" type="number" readOnly="true"/>
                <field name="VERSION" label="版本" readOnly="true"/>
            </row>
            <row>
                <field name="MODEL" label="型号" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="TYPESPEC_ID" label="类型规格ID" readOnly="true"/>
                <field name="VENDOR_IDz" label="生产厂家" readOnly="true"/>
            </row>
        </form>
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="MODEL" label="型号"/>
                <field name="NAME" label="名称"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid>
            <field name="MODEL" label="型号"/>
            <field name="NAME" label="名称"/>
        </grid>
        <grid name="search">
            <field name="MODEL" label="型号"/>
            <field name="NAME" label="名称"/>
        </grid>
    </ObjMeta>
    <!-- 综合配线箱型号 -->
    <ObjMeta objectType="TYPEZHX">
        <!--新增/修改面板-->
        <form>
            <row>
                <field name="CREATOR" label="创建人" readOnly="true"/>
                <field name="CREATOR_ID" label="创建人ID" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFIER_ID" label="修改人ID" readOnly="true"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="创建时间" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="SHARDING_ID" type="number" readOnly="true"/>
                <field name="VERSION" label="版本" readOnly="true"/>
            </row>
            <row>
                <field name="MODEL" label="型号" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="TYPESPEC_ID" label="类型规格ID" readOnly="true"/>
                <field name="VENDOR_IDz" label="生产厂家" readOnly="true"/>
            </row>
        </form>
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="MODEL" label="型号"/>
                <field name="NAME" label="名称"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid>
            <field name="MODEL" label="型号"/>
            <field name="NAME" label="名称"/>
        </grid>
        <grid name="search">
            <field name="MODEL" label="型号"/>
            <field name="NAME" label="名称"/>
        </grid>
    </ObjMeta>
    <!-- 光交接箱型号 -->
    <ObjMeta objectType="TYPEGJ">
        <!--新增/修改面板-->
        <form>
            <row>
                <field name="CREATOR" label="创建人" readOnly="true"/>
                <field name="CREATOR_ID" label="创建人ID" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFIER_ID" label="修改人ID" readOnly="true"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="创建时间" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="SHARDING_ID" type="number" readOnly="true"/>
                <field name="VERSION" label="版本" readOnly="true"/>
            </row>
            <row>
                <field name="MODEL" label="型号" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="TYPESPEC_ID" label="类型规格ID" readOnly="true"/>
                <field name="VENDOR_IDz" label="生产厂家" readOnly="true"/>
            </row>
        </form>
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="MODEL" label="型号"/>
                <field name="NAME" label="名称"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid>
            <field name="MODEL" label="型号"/>
            <field name="NAME" label="名称"/>
        </grid>
        <grid name="search">
            <field name="MODEL" label="型号"/>
            <field name="NAME" label="名称"/>
        </grid>
    </ObjMeta>
    <!-- 光分纤箱型号 -->
    <ObjMeta objectType="TYPEGF">
        <!--新增/修改面板-->
        <form>
            <row>
                <field name="CREATOR" label="创建人" readOnly="true"/>
                <field name="CREATOR_ID" label="创建人ID" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFIER_ID" label="修改人ID" readOnly="true"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="创建时间" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="SHARDING_ID" type="number" readOnly="true"/>
                <field name="VERSION" label="版本" readOnly="true"/>
            </row>
            <row>
                <field name="MODEL" label="型号" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="TYPESPEC_ID" label="类型规格ID" readOnly="true"/>
                <field name="VENDOR_IDz" label="生产厂家" readOnly="true"/>
            </row>
        </form>
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="MODEL" label="型号"/>
                <field name="NAME" label="名称"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid>
            <field name="MODEL" label="型号"/>
            <field name="NAME" label="名称"/>
        </grid>
        <grid name="search">
            <field name="MODEL" label="型号"/>
            <field name="NAME" label="名称"/>
        </grid>
    </ObjMeta>
    <!-- 光终端盒型号 -->
    <ObjMeta objectType="TYPEGB">
        <!--新增/修改面板-->
        <form>
            <row>
                <field name="CREATOR" label="创建人" readOnly="true"/>
                <field name="CREATOR_ID" label="创建人ID" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFIER_ID" label="修改人ID" readOnly="true"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="创建时间" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="SHARDING_ID" type="number" readOnly="true"/>
                <field name="VERSION" label="版本" readOnly="true"/>
            </row>
            <row>
                <field name="MODEL" label="型号" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="TYPESPEC_ID" label="类型规格ID" readOnly="true"/>
                <field name="VENDOR_IDz" label="生产厂家" readOnly="true"/>
            </row>
        </form>
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="MODEL" label="型号"/>
                <field name="NAME" label="名称"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid>
            <field name="MODEL" label="型号"/>
            <field name="NAME" label="名称"/>
        </grid>
        <grid name="search">
            <field name="MODEL" label="型号"/>
            <field name="NAME" label="名称"/>
        </grid>
    </ObjMeta>
    <!-- 光缆接头型号 -->
    <ObjMeta objectType="TYPEGT">
        <!--新增/修改面板-->
        <form>
            <row>
                <field name="CREATOR" label="创建人" readOnly="true"/>
                <field name="CREATOR_ID" label="创建人ID" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFIER_ID" label="修改人ID" readOnly="true"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="创建时间" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="SHARDING_ID" type="number" readOnly="true"/>
                <field name="VERSION" label="版本" readOnly="true"/>
            </row>
            <row>
                <field name="MODEL" label="型号" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="TYPESPEC_ID" label="类型规格ID" readOnly="true"/>
                <field name="VENDOR_IDz" label="生产厂家" readOnly="true"/>
            </row>
        </form>
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="MODEL" label="型号"/>
                <field name="NAME" label="名称"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid>
            <field name="MODEL" label="型号"/>
            <field name="NAME" label="名称"/>
        </grid>
        <grid name="search">
            <field name="MODEL" label="型号"/>
            <field name="NAME" label="名称"/>
        </grid>
    </ObjMeta>
    <!-- 分光器型号 -->
    <ObjMeta objectType="TYPEOBD">
        <!--新增/修改面板-->
        <form>
            <row>
                <field name="CREATOR" label="创建人" readOnly="true"/>
                <field name="CREATOR_ID" label="创建人ID" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFIER_ID" label="修改人ID" readOnly="true"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="创建时间" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="SHARDING_ID" type="number" readOnly="true"/>
                <field name="VERSION" label="版本" readOnly="true"/>
            </row>
            <row>
                <field name="MODEL" label="型号" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="TYPESPEC_ID" label="类型规格ID" readOnly="true"/>
                <field name="VENDOR_IDz" label="生产厂家" readOnly="true"/>
            </row>
        </form>
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="MODEL" label="型号"/>
                <field name="NAME" label="名称"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid>
            <field name="MODEL" label="型号"/>
            <field name="NAME" label="名称"/>
        </grid>
        <grid name="search">
            <field name="MODEL" label="型号"/>
            <field name="NAME" label="名称"/>
        </grid>
    </ObjMeta>
    <ObjMeta objectType="OPERATION_LOG" typeActions="add,remove" itemActions="modify,remove">
        <grid>
            <field name="USERCODE"/>
            <field name="USERNAME"/>
            <field name="SYSTIME"/>
            <field name="RESOURCE_CODE"/>
            <field name="RESOURCE_NAME"/>
            <field name="RESOURCE_SPEC"/>
            <field name="TYPE"/>
        </grid>
        <field name="USERNAME"/>
        <field name="TYPE"/>
    </ObjMeta>
    <ObjMeta objectType="PROJECT" labelField="NAME"
             itemActions="locate,relocateProject,modify,resourceinput,statisticsdata,submitaudit,remove"
             moreItemActions="filesman,exportDuctViews,exportCableViews">
        <form>
            <row>
                <field name="NAME" required="true"/>
                <field name="CODE" required="true"/>
            </row>
            <row>
                <field name="OWNER" required="true"/>
                <field name="TELNUM" required="true"/>
            </row>
            <row>
                <!-- 【浙江】【O3管线第一轮内测】工程管理界面无法正常新增 TASK 66731 -->
                <field name="CM_SINGLE_PROJECT_STATUS_ID" readOnly="false" required="true" default="80402346"/>
                <field name="CM_PROJECT_STATUS_ID" readOnly="false" required="true" default="80402339"/>
            </row>
            <row>
                <field name="NOTES"/>
            </row>
        </form>
        <form name="search">
            <row>
                <field name="SERIALNO" label="工程流水号"/>
                <field name="NAME" label="工程名称"/>
            </row>
            <row>
                <field name="PROJECT_TYPE_ID" label="工程类型"/>
                <field name="PROJECTWBS" label="所属WBS"/>
            </row>
            <row>
                <field name="CODE" label="工程编码"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="DOCUMENT_NUMBER" label="文档编号"/>
                <field name="CONSTRUCTION_NO" label="报建批文号"/>
            </row>
        </form>
        <grid name="search" tableStyle="height:200px;" >
            <field name="CODE" label="工程编码"/>
            <field name="NAME" label="工程名称"/>
            <field name="PROJECTWBS_value.CODE" label="所属WBS"/>
            <field name="PROJECT_TYPE_ID" label="工程类型"/>
            <field name="SERIALNO" label="工程流水号"/>
            <field name="CM_SINGLE_PROJECT_STATUS_ID" label="工程状态"/>
            <field name="DOCUMENT_NUMBER" label="文档编号"/>
            <field name="CONSTRUCTION_NO" label="报建批文号"/>
            <field name="NOTES" label="备注"/>
        </grid>
        <grid name="stat" rowDetailField="false" rowCheckable="false" showRowNum="false" typeActions="imp,exp,expall"
              extraActions="">
            <field name="NAME"/>
            <field name="NUM" label="数量" label-en="Num"/>
        </grid>
        <grid name="audit" toolbar="false" itemActions="auditpass,auditrejecte,statisticsdata,view">
            <field name="NAME"/>
            <field name="CODE"/>
            <field name="CM_SINGLE_PROJECT_STATUS_ID"/>
        </grid>
        <grid>
            <field name="NAME"/>
            <field name="CODE"/>
            <field name="CM_PROJECT_STATUS_ID"/>
            <field name="CM_SINGLE_PROJECT_STATUS_ID"/>
        </grid>
        <action name="resourceinput" label="资源录入" label-en="Resource Input" type="script"
                url="apps/pipe/project/resourceinput.js"/>
        <action name="submitaudit" label="提交审核" label-en="Submit Audit" type="script"
                url="apps/pipe/project/submitaudit.js"/>
        <action name="statisticsdata" label="资源统计" label-en="Statistics Data" type="workspace"
                mainConfig="apps/pipe/project/statisticsdata.xml" urlParams="projectId=${id}&amp;projectName=${NAME}"/>
        <action name="auditrejecte" label="驳回" label-en="Rejecte" type="script"
                url="apps/pipe/project/auditrejecte.js"/>
        <action name="auditpass" label="通过" label-en="Pass" type="script" url="apps/pipe/project/auditpass.js"/>
        <action name="view" label="查看" label-en="View" type="script">
            <![CDATA[
                _bean.showModify(this.data.objectType, this.data.id, '', '', {readOnly: true});
            ]]>
        </action>
        <action name="exportDuctViews" type="script" label="导出管道图" label-en="Get Pipe DXFs" icon="file">
            <![CDATA[
                var url = 'bean/exp.do?_type=PROJECT&_scriptmethod=exportDxf&sourceType=201&entityId=' + _actionContext.params.id+'&_expfilename='+_actionContext.params.NAME+'_pipes.zip';
                url = _context.fullDataUrl(url);
                window.open(url, '_blank');
            ]]>
        </action>
        <action name="exportCableViews" type="script" label="导出光缆图" label-en="Get Cable DXFs" icon="file">
            <![CDATA[
                var url = 'bean/exp.do?_type=PROJECT&_scriptmethod=exportDxf&sourceType=202&entityId=' + _actionContext.params.id+'&_expfilename='+_actionContext.params.NAME+'_cables.zip';
                url = _context.fullDataUrl(url);
                window.open(url, '_blank');
            ]]>
        </action>
        <action name="relocateProject" label="重定位" label-en="Relocate" type="script" icon="random"
                url="apps/pipe/project/relocateProject.js"/>
    </ObjMeta>
    <!-- 局向 -->
    <ObjMeta objectType="SITEDIRECTION">
        <form name="search" labelWidth="8em">
            <row>
                <field name="ORG_TYPE_ID" label="机构类型"/>
            </row>
            <row>
                <field name="CREATOR" label="创建人"/>
            </row>
            <row>
                <field name="CREATOR_ID" label="创建人ID"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人"/>
            </row>
            <row>
                <field name="MODIFIER_ID" label="修改人ID"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="创建时间"/>
            </row>
            <row>
                <field name="MODIFY_DATE" label="修改时间"/>
            </row>
            <row>
                <field name="SHARDING_ID" label="本地网ID"/>
            </row>
            <row>
                <field name="VERSION" label="版本"/>
            </row>
            <row>
                <field name="region"  label="所属区县"/>
            </row>
            <row>
                <field name="nets" label="覆盖网"/>
            </row>
            <row>
                <field name="IS_VALID" label="有效性"/>
            </row>
            <row>
                <field name="PARENT_ID" label="父区域ID"/>
            </row>
            <row>
                <field name="AREA_TYPE_ID" label="区域类型ID"/>
            </row>
            <row>
                <field name="AREA_LEVEL_ID" label="行政区划单位ID"/>
            </row>
            <row>
                <field name="ZONE_NUMBER" label="区号"/>
            </row>
            <row>
                <field name="ZIP_CODE" label="邮编"/>
            </row>
            <row>
                <field name="REGION_ID" label="所属区县ID"/>
            </row>
            <row>
                <field name="ALIAS" label="简称"/>
            </row>
            <row>
                <field name="CITY_SUBURB_ID" label="城郊属性"/>
            </row>
            <row>
                <field name="MEASURE_AREA" label="面积（平方米）"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="STANDARD_CODE" label="集团编码"/>
            </row>
            <row>
                <field name="STANDARD_NAME" label="集团名称"/>
            </row>
            <row>
                <field name="marketing" label="所属营服"/>
            </row>
            <row>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="CODE" label="编码"/>
            </row>
            <row>
                <field name="SPECIAL_FLAG_ID" label="特殊标志"/>
            </row>
            <row>
                <field name="LAN_ATTRIBUTE_ID" label="LAN属性"/>
            </row>
            <row>
                <field name="LINE_TYPE_ID" label="线路类型"/>
            </row>
            <row>
                <field name="IS_COMMITMENT_REGION_ID" label="是否承诺区域"/>
            </row>
            <row>
                <field name="IS_PASS_MEASUREMENT_ID" label="是否经过测量"/>
            </row>
            <row>
                <field name="IS_STATION_ID" label="是否母局"/>
            </row>
            <row>
                <field name="parent" type="obj" label="服务区"/>
            </row>
            <row>
                <field name="parentparent" label="分局"/>
            </row>
            <row>
                <field name="rsite" label="关联局站"/>
            </row>
        </form>
    </ObjMeta>
    <ObjMeta objectType="REGION">
        <form>
            <field name="CODE"/>
            <field name="NAME"/>
            <field name="AREA_LEVEL_ID"/>
        </form>
    </ObjMeta>
    <!-- 局站 -->
    <ObjMeta objectType="SITE" needgeom="true"
             typeActions="" itemActions="view,locate,rooms" extraActions="expgrid"
             moreItemActions="filesman,codeCut">
        <form name="search" labelWidth="8em">
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="region" label="所属父区域"/>
                <field name="region" label="所属区域"/>
            </row>
            <row>
                <field name="CITY_SUBURB_ID" label="属地性质"/>
                <field name="SITE_TYPE_ID" label="站点类型"/>
            </row>
            <row>
                <field label="有效性" name="IS_VALID"/>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
            </row>
            <row>
                <field name="MEASURE_AREA" label="占地面积"/>
            </row>
        </form>
        <form>
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="region" required="true" dropdownOptions="titleField:NAME"/>
                <field name="LONG_LOCAL_ID" required="true"/>
            </row>
            <row>
                <field name="MEASURE_AREA" label="占地面积" label-en="Floor Area"/>
                <field name="SITE_TYPE_ID" required="true"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置" required="true"/>
                <field name="UNIT" label="幢，座，楼号"/>
            </row>
            <row>
                <field name="CITY_SUBURB_ID" label="属地性质"/>
            </row>
            <row>
                <field name="JQ_CODE" readOnly="true"/>
                <field name="SITE_TYPE_LEVEL_ID" label="局站分类等级"/>
            </row>
            <row>
                <field name="IS_BS_ID" label="是否无线基站站点" default="100382" required="true"/>
                <field name="PROCESS_NO"/>
                <field name="STATION_TYPE_ID" label="站点等级"/>
            </row>
            <row>
                <field name="STANDARD_TYPE_ID"/>
                <field name="STATION_NAME"/>
            </row>
            <field type="divider" label="网格属性" label-en="Grid information"/>
            <row>
                <field name="BRANCH_NAME" readOnly="true"/>
                <field name="gridcode" label="所属网格" readOnly="true"/>
            </row>
            <row>
                <field name="LONGITUDE" readOnly="true"/>
                <field name="LATITUDE" readOnly="true"/>
            </row>
            <field type="divider" label="楼宇属性" label-en="Property of building"/>
            <row>
                <field name="ROAD_NAME"/>
                <field name="HOUSE_NUMBER"/>
            </row>
            <row>
                <field name="BUILDING_NAME"/>
                <field name="FLOORS"/>
            </row>
            <field type="divider" label="建设属性" label-en="Construction attribute"/>
            <row>
                <field name="MAINTENANCE_CONTACT"/>
                <field name="MAINTENANCE_PHONE"/>
            </row>
            <field type="divider" label="审计属性" label-en="Audit attribute"/>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
        </grid>
        <grid>
            <field name="CODE" label="局站编码"/>
            <field name="NAME" label="局站名称"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
        </grid>
        <action type="obj" name="addRoom" label="新增机房" label-en="Add Room" method="add" objectType="ROOM"
                parentField="site" icon="file outline"/>
        <action type="workspace" name="rooms" label="机房列表" label-en="Room List" title="${NAME}-机房列表"
                title-en="${NAME}-Room list" objectType="ROOM" icon="file alternate outline"
                options='{"baseParams": {"site": "${id}"}}'/>
        <!--<action type="script" name="codeCut" label="编码割接" label-en="Code Cut" icon="cut"
                url="apps/pipe/cutover/siteCodeMerger.js"/>-->
        <action type="script" name="view" label="查看" label-en="View">
            <![CDATA[
                var telanturl=_context.clientConf.telantUrl;
                var siteurl = "com.ccssoft.inventory.web.area.view.AreaResourceDetail.d7?openMode=modify&metaClassName=SITE&filter=pipe&entityId="+_actionContext.params.id+"&gisCallParam=NDQxMDAwMDAwMDAwMDAxMDA0MTU5OTM5";
                window.open(telanturl+siteurl);
            ]]>
        </action>
    </ObjMeta>
    <!--楼宇-->
    <ObjMeta objectType="BUILDING" needgeom="true"
             itemActions="relocate,locate,modify,addRoom,rooms,buildingRelateGJ,buildingRelateShop,remove"
             moreItemActions="filesman">
        <form>
            <row>
                <field name="NAME" required="true"/>
                <field name="CODE" required="true" type="text"/>
            </row>
            <row>
                <field name="region" required="true" readOnly="true"/>
                <field name="UNIT"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="政企楼宇地址" label-en="building address" required="true" readOnly="true"/>

            </row>

            <field type="divider" label="网格属性" label-en="Grid information"/>
            <row>
                <field name="BRANCH_NAME" readOnly="true"/>
                <field name="gridcode" label="所属网格" label-en="Affiliated grid" readOnly="true"/>
            </row>
            <row>
                <field name="LONGITUDE" readOnly="true"/>
                <field name="LATITUDE" readOnly="true"/>
            </row>

            <field type="divider" label="审计属性" label-en="Audit information"/>
            <row>
                <field name="CREATOR" readOnly="true"/>
                <field name="CREATE_DATE" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" readOnly="true"/>
                <field name="MODIFY_DATE" readOnly="true"/>
            </row>
            <script>
                <![CDATA[
                        genPreCode();
                        form.getFieldEl('CODE').find('input').on('change', function(){
                           genCode();
                         });

                        form.getFieldEl('ADDRESS_DESC').find('input').on('click', function(){
                             var name = form.getField('NAME').getValue();
                             var unit = form.getField('UNIT').getValue();
                             var regionSelect = form.getField('region').dropdown.getSelectedItem();
                             if(name =='' || name == null || name == undefined){
                               alert('请先填写名称！');
                               return;
                             }
                             var id=form.data.id;
                             if(id){//修改
                               _context.doAction("addBuildingAddress",{"areaId":regionSelect.id,"name":name,"unit":unit,"id":id,"openModel":"modify"});
                             }else{//新增
                               _context.doAction("addBuildingAddress",{"areaId":regionSelect.id,"name":name,"unit":unit,"openModel":"add"});
                             }

                        });

                        function genPreCode(){
                            var id=form.data.id;
                            if(!id){//修改
                              var regionSelect = form.getField('region').dropdown.getSelectedItem();
                              var oldCode = regionSelect==null?"":regionSelect.CODE+'.';
                              form.getField('CODE').setValue(oldCode);
                            }
                        }

                        function genCode(){
                        debugger;
                          var id=form.data.id;
                          var code = form.data.CODE;
                            if(id){//修改
                              form.getField('CODE').setValue(code);
                            }else{
                              var regionSelect = form.getField('region').dropdown.getSelectedItem();
                              var oldCode = regionSelect==null?"":regionSelect.CODE+'.';
                              var inputCode = form.getField('CODE').getValue();
                              inputCode =inputCode.replace(/[\u4e00-\u9fa5]/g,'');
						       var index = inputCode.indexOf(oldCode);
                               if(index !=-1 && index ==0){
                                    newCode = inputCode.substr(index);
                                    form.getField('CODE').setValue(inputCode.toUpperCase());
                               }else
                                  form.getField('CODE').setValue(oldCode.toUpperCase());
                            }
                        }
                        window.setAddress =function(address){
                           form.getField('ADDRESS_DESC').setValue(address);
                        }
                        window.setBaseData = function(addressData,coverType,addressType){
                          form.baseData.addressData=addressData;
                          form.baseData.coverType=coverType;
                          form.baseData.addressType=addressType;
                        }

                     ]]>
            </script>
        </form>
        <action type="obj" name="addRoom" label="新增机房" label-en="Add Room" method="add" objectType="ROOM"
                parentField="site" icon="file outline"/>
        <action type="workspace" name="rooms" label="机房列表" label-en="Room List" title="${NAME}-机房列表"
                title-en="${NAME}-Room list" objectType="ROOM" icon="file alternate outline"
                options='{"baseParams": {"site": "${id}"}}'/>

    </ObjMeta>

    <!-- 网格 -->
    <ObjMeta objectType="MANAGE" needgeom="true" typeActions="" itemActions="view,releAddress,locate">
        <form name="search" labelWidth="8em">
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
        </form>
        <form>
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE"/>
                <field name="NAME" required="true"/>
            </row>
            <field type="divider" label="审计属性" label-en="Audit attribute"/>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
        </grid>
        <grid>
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
        </grid>
    </ObjMeta>

    <ObjMeta objectType="AREAIDLING" needgeom="false" itemActions="modify" moreItemActions="">
        <form>
            <row>
                <field name="NAME"/>
                <field name="CODE"/>
            </row>
            <row>
                <field name="ADDRESS_DESC"/>
            </row>
        </form>

    </ObjMeta>
    <ObjMeta objectType="FACILITYIDLING" needgeom="false" itemActions="modify" moreItemActions="">
        <form>
            <row>
                <field name="NAME"/>
                <field name="CODE"/>
            </row>
        </form>
    </ObjMeta>
    <!-- 机房 -->
    <ObjMeta objectType="ROOM" previewFile="PREVIEW"
             typeActions="" itemActions="view,locate,roomlayout,room3d,roomdesign" extraActions="expgrid"
             moreItemActions="filesman">
        <form name="search" labelWidth="8em">
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="ROOM_KIND_ID" label="机房类型"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="地理位置"/>
                <field name="region" label="所属区域"/>
            </row>
            <row>
                <field name="PROCESS_NO" label="流水号"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <form labelWidth="8em">
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="site" required="true" onChange="region=site.REGION_ID;siteName=site.NAME"/>
                <field name="siteName" label="所属局站名称" readOnly="true"/><!-- <field name="region"/> -->
            </row>
            <!-- <row><field name="LONG_LOCAL_ID"/><field name="MNT_WAY_ID"/></row> -->
            <row>
                <field name="LENGTH" label="长度(米)" label-en="Length (m)"/>
                <field name="WIDTH" label="宽度(米)" label-en="Width (m)"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" required="true" default="80204847"/>
                <field name="ADDRESS_DESC" label="具体位置" required="true"
                       label-en="Specific Location"/><!-- <field name="position" baseParams="{&quot;_solr&quot;:true}" dropdownOptions="titleField:FULL_NAME"/> -->
            </row>
            <row>
                <field name="ENVIRONMENT_ID" label="地理环境"/>
                <field name="WEIGHT_CAPACITY" label="机房承重(KN)"/>
            </row>
            <row>
                <field name="ROOM_HEIGHT" label="机房层高(m)"/>
                <field name="ROOM_LEVEL_ID"/>
                <field name="ROOM_KIND_ID" required="true"/>
            </row>
            <row>
                <field name="USABLE_AREA" label="机房可安装面积(㎡)"/>
                <field name="BUILD_AREA" label="机房建筑面积(㎡)"/>
                <field name="USED_BUILD_AREA" label="机房已用建筑面积(㎡)"/>
            </row>
            <!--杭州-云网资源中心第一轮用户测试-问题管理-26053-->
            <row>
                <field name="IDLE_AREA" label="空闲面积(㎡)"/>
                <field name="RING_STAION_NAME" label="动环规范局站名称"/>
            </row>
            <row>
                <field name="FLOOR_HEIGHT" label="地板高度(cm)"/>
                <field name="BLOWING_MODE_ID" label="空调送风形式"/>
                <field name="PLAN_NUMBER" label="机房平面图编号"/>
            </row>
            <row>
                <field name="ROOM_TOTAL_RACK" label="机房设计机架总数(架)"/>
                <field name="USED_RACK" label="已安装机架数(架)"/>
                <field name="ROOM_USED_RACK" label="已使用机架数(架)"/>
            </row>
            <row>
                <field name="RACK_CONSUMPTION" label="单机架功耗(KW)"/>
                <field name="MAX_POWER_CAPABILITY" label="电源系统最大供电能力(KW)"/>
                <field name="MAX_LOAD_CAPABILITY" label="头柜最大供电能力(KW)"/>
            </row>
            <row>
                <field name="ROOM_USED_DEVICE" label="已使用设备负荷(KW)"/>
                <field name="MAX_REFRIGERATION_CAPABILITY" label="空调最大制冷能力(KW)"/>
                <field name="ROOM_BUSINESS_SYSTEM" label="机房主要业务/电源(系统)名称"/>
            </row>
            <row>
                <field name="ROOM_AIRCONDITION_SYSTEM" label="电源空调系统名称"/>
                <field name="ROOM_DEPARTMENT_NAME" label="管理责任单位"/>
                <field name="ROOM_PRODUCTION_DATE" label="机房投产时间"/>
            </row>
            <!-- <row><field name="MODIFIER" readOnly="true"/><field name="MODIFY_DATE" readOnly="true"/></row>
            <row><field name="CREATOR" readOnly="true"/><field name="CREATE_DATE" readOnly="true"/></row> -->
            <field type="divider" label="坐标" label-en="Location information"/>
            <row>
                <field name="LONGITUDE" readOnly="true"/>
                <field name="LATITUDE" readOnly="true"/>
            </row>
            <field type="divider" label="网格属性" label-en="Grid information"/>
            <row>
                <field name="BRANCH_NAME" label="支局名称" readOnly="true"/>
                <field name="gridcode" label="所属网格" label-en="Affiliated grid" readOnly="true"/>
            </row>
            <field type="divider" label="建设属性" label-en="Construction attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" required="true"/>
                <field name="PROCESS_NO" label="流水号"/>
            </row>
            <field type="divider" label="维护属性" label-en="Maintain attributes"/>
            <row>
                <field name="MNT_WAY_ID" default="100675" required="true"/>
                <field name="AGENT_MNT_COMPANY" label="代维单位"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护部门名称"/>
                <field name="RENT_CONTRACT_NO" label="租用合同号"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人名称"/>
                <field name="AGENT_MNT_PHONE" label="代维电话"/>
            </row>
            <field type="divider" label="物业属性" label-en="Property attribute"/>
            <row>
                <field name="PROPERTY_MANAGEMENT_UNIT" label="物业单位"/>
                <field name="PROPERTY_MANAGEMENT_CONTACT_NO" label="物业合同编号"/>
            </row>
            <row>
                <field name="PROPRIETOR" label="业主联系人"/>
                <field name="PROPERTY_MANAGEMENT_PHONE" label="物业、业主联系人电话"/>
            </row>
            <field type="divider" label="审计属性" label-en="AUDIT ATTRIBUTE"/>
            <row>
                <field name="MODIFIER" readOnly="true"/>
                <field name="MODIFY_DATE" readOnly="true"/>
            </row>
            <row>
                <field name="CREATOR" readOnly="true"/>
                <field name="CREATE_DATE" readOnly="true"/>
            </row>
            <field type="divider" label="灾备属性" label-en="Disaster recovery attribute"/>
            <row>
                <field name="DISATER_MAINTENANCE_ID" label="灾备信息是否维护" default="100382"/>
            </row>
            <row>
                <field name="HIGHEST_LEVEL" label="最高水位"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="WIND_LEVEL_ID" label="抗风等级"/>
                <field name="WATERPROOF_HEIGHT" label="防水高度"/>
            </row>
            <row>
                <field name="WATER_RECORD" label="水浸记录"/>
                <field name="BLACKOUT_RECORD" label="断电记录"/>
            </row>
            <row>
                <field name="BUSINESS_LEVEL_ID" label="业务等级"/>
                <field name="IS_STANDBY_GENERATOR_ID" label="备用发电机"/>
            </row>
        </form>
        <grid name="mini" autoLoad="false">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="address" label="标准地址"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <grid tableStyle="height:180px;" >
            <field name="CODE" label="机房编码"/>
            <field name="NAME" label="机房名称"/>
            <field name="ROOM_KIND_ID" label="机房类型" />
            <field name="region" label="所属区域"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="address_value.FULL_NAME" label="标准地址"/>
            <field name="site_value.NAME" label="所属局站"/>
            <!-- <field name="ROOM_KIND_ID" label="机房类型"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="address" label="标准地址"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="PROPERTY_TYPE_ID" label="资产产权性质"/>
            <field name="REFORMVEST_ID" label='产权性质' getLabel="row.REFORMVEST_ID_value.NAME"/>
            <field name="ROOM_LEVEL_ID" label="机房等级"/>
            <field name="NOTES" label="备注"/> -->
        </grid>
        <form name="convertToRoom">
            <field name="ROOM_TYPE_LEVEL_ID" label="机房分类等级" required="true"/>
        </form>
        <action name="roomlayout" label="设备管理" title="设备管理-${NAME}" label-en="Devices" type="workspace"
                mainConfig="apps/pipe/room/room.xml" icon="hdd"
                urlParams="roomId=${id}&amp;height=${HEIGHT}&amp;width=${WIDTH}&amp;length=${LENGTH}"/>
<!--        <action name="room3d" label="3D视图" label-en="3D View" _ui_class="orange" mainConfig="apps/pipe/room/room3d.xml"-->
<!--                icon="cube" urlParams="roomId=${id}"/>-->
        <!--<action name="convertToChildAccesspoint" label="转子接入点" label-en="Convert to ChildAccesspoint" type="script"
                url="apps/pipe/room/action/convertToChildAccesspoint.js"/>-->
        <action name="roomdesign" type="script" label="机房平面图" icon="building" label-en="View">
            <![CDATA[
                var telanturl=_context.clientConf.telantUrl;
                var roomurl = "com.ccssoft.inventory.web.area.view.FacilityRoomGraph.d7?metaClassName=ROOM&entityId="+_actionContext.params.id+"&windowmode=newPage";
                window.open(telanturl+roomurl);
            ]]>
        </action>
        <action type="script" name="view" label="查看" label-en="View">
            <![CDATA[
                var telanturl=_context.clientConf.telantUrl;
                var roomurl = "com.ccssoft.inventory.web.area.view.FacilityResourceDetail.d7?openMode=modify&metaClassName=ROOM&filter=pipe&entityId="+_actionContext.params.id+"&gisCallParam=NDQxMDAwMDAwMDAwMDAxMDA0MTU5OTM5";
                window.open(telanturl+roomurl);
            ]]>
        </action>
    </ObjMeta>
    <!-- 安装点 -->
    <ObjMeta objectType="OUTDOORADDRESS" label="安装点" label-en="Mounting point" moreItemActions="filesman"
             typeActions="" itemActions="view,locate,roomlayout,room3d,roomdesign" extraActions="expgrid">
        <action name="outdooraddresslayout" label="安装点设备管理" label-en="Installation point equipment management"
                mainConfig="apps/pipe/outdooraddress/outdooraddress.xml" urlParams="outdooraddressId=${id}"/>
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="region" label="所属区域"/>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
         <!-- 安装点属性面板 -->
        <form labelWidth="8em">
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="site" required="true" onChange="region=site.REGION_ID;siteName=site.NAME"/>
                <field name="siteName" label="所属局站名称" readOnly="true"/><!-- <field name="region"/> -->
            </row>
            <row>
                <field name="LENGTH" label="长度(米)" label-en="Length (m)"/>
                <field name="WIDTH" label="宽度(米)" label-en="Width (m)"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" required="true" default="80204847"/>
                <field name="ADDRESS_DESC" label="具体位置" required="true"
                       label-en="Specific Location"/><!-- <field name="position" baseParams="{&quot;_solr&quot;:true}" dropdownOptions="titleField:FULL_NAME"/> -->
            </row>
            <row>
                <field name="ENVIRONMENT_ID" label="地理环境"/>
                <field name="WEIGHT_CAPACITY" label="机房承重(KN)"/>
            </row>
            <row>
                <field name="FLOOR_HEIGHT" label="地板高度(cm)"/>
                <field name="BLOWING_MODE_ID" label="空调送风形式"/>
            </row>
            <row>
                <field name="RACK_CONSUMPTION" label="单机架功耗(KW)"/>
                <field name="MAX_POWER_CAPABILITY" label="电源系统最大供电能力(KW)"/>
                <field name="MAX_LOAD_CAPABILITY" label="头柜最大供电能力(KW)"/>
            </row>
            <row>
                <field name="ROOM_USED_DEVICE" label="已使用设备负荷(KW)"/>
                <field name="MAX_REFRIGERATION_CAPABILITY" label="空调最大制冷能力(KW)"/>
                <field name="ROOM_BUSINESS_SYSTEM" label="机房主要业务/电源(系统)名称"/>
            </row>
            <row>
                <field name="ROOM_AIRCONDITION_SYSTEM" label="电源空调系统名称"/>
                <field name="ROOM_DEPARTMENT_NAME" label="管理责任单位"/>
                <field name="ROOM_PRODUCTION_DATE" label="机房投产时间"/>
            </row>
            <field type="divider" label="坐标" label-en="Location information"/>
            <row>
                <field name="LONGITUDE" readOnly="true"/>
                <field name="LATITUDE" readOnly="true"/>
            </row>
            <field type="divider" label="网格属性" label-en="Grid information"/>
            <row>
                <field name="BRANCH_NAME" label="支局名称" readOnly="true"/>
                <field name="gridcode" label="所属网格" label-en="Affiliated grid" readOnly="true"/>
            </row>
            <field type="divider" label="建设属性" label-en="Construction attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" required="true"/>
                <field name="PROCESS_NO" label="流水号"/>
            </row>
            <field type="divider" label="维护属性" label-en="Maintain attributes"/>
            <row>
                <field name="MNT_WAY_ID" default="100675" required="true"/>
                <field name="AGENT_MNT_COMPANY" label="代维单位"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护部门名称"/>
                <field name="RENT_CONTRACT_NO" label="租用合同号"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人名称"/>
                <field name="AGENT_MNT_PHONE" label="代维电话"/>
            </row>
            <field type="divider" label="物业属性" label-en="Property attribute"/>
            <row>
                <field name="PROPERTY_MANAGEMENT_UNIT" label="物业单位"/>
                <field name="PROPERTY_MANAGEMENT_CONTACT_NO" label="物业合同编号"/>
            </row>
            <row>
                <field name="PROPRIETOR" label="业主联系人"/>
                <field name="PROPERTY_MANAGEMENT_PHONE" label="物业、业主联系人电话"/>
            </row>
            <row>
                <field name="DISATER_MAINTENANCE_ID" label="灾备信息是否维护" default="100382"/>
            </row>
            <row>
                <field name="HIGHEST_LEVEL" label="最高水位"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="WIND_LEVEL_ID" label="抗风等级"/>
                <field name="WATERPROOF_HEIGHT" label="防水高度"/>
            </row>
            <row>
                <field name="WATER_RECORD" label="水浸记录"/>
                <field name="BLACKOUT_RECORD" label="断电记录"/>
            </row>
            <row>
                <field name="BUSINESS_LEVEL_ID" label="业务等级"/>
                <field name="IS_STANDBY_GENERATOR_ID" label="备用发电机"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:1200;">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="region" label="所属区域"/>
            <field name="site" label="所属局站"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="address" label="标准地址"/>
        </grid>
        <action name="roomlayout" label="设备管理" title="设备管理-${NAME}" label-en="Devices" type="workspace"
                mainConfig="apps/pipe/room/room.xml" icon="hdd"
                urlParams="roomId=${id}&amp;height=${HEIGHT}&amp;width=${WIDTH}&amp;length=${LENGTH}"/>
        <action name="room3d" label="3D视图" label-en="3D View" _ui_class="orange" mainConfig="apps/pipe/room/room3d.xml"
                icon="cube" urlParams="roomId=${id}"/>
        <action name="convertToChildAccesspoint" label="转子接入点" label-en="Convert to ChildAccesspoint" type="script"
                url="apps/pipe/room/action/convertToChildAccesspoint.js"/>
        <action name="roomdesign" type="script" label="安装点平面图" icon="building" label-en="View">
            <![CDATA[
                var telanturl=_context.clientConf.telantUrl;
                var roomurl = "com.ccssoft.inventory.web.area.view.FacilityRoomGraph.d7?metaClassName=ROOM&entityId="+_actionContext.params.id+"&windowmode=newPage";
                window.open(telanturl+roomurl);
            ]]>
        </action>
        <action type="script" name="view" label="查看" label-en="View">
            <![CDATA[
                var telanturl=_context.clientConf.telantUrl;
                var outdooraddressurl = "com.ccssoft.inventory.web.area.view.FacilityOutDoorAddressDetail.d7?openMode=modify&metaClassName=OUTDOORADDRESS&filter=pipe&entityId="+_actionContext.params.id+"&gisCallParam=NDQxMDAwMDAwMDAwMDAxMDA0MTU5OTM5";
                window.open(telanturl+outdooraddressurl);
            ]]>
        </action>
        <grid name="FTTXOUTDOORADDRESS" filterable="false" sortable="false">
            <field name="SEQNO" label="序号"/>
            <field name="CODE" label="安装点编码" getLabel="row.entity ? row.entity.CODE : row.CODE"/>
            <field name="NAME" label="安装点名称" getLabel="row.entity ? row.entity.NAME : row.NAME"/>
        </grid>
    </ObjMeta>

    <!-- 接入点 -->
    <ObjMeta objectType="ACCESSPOINT" needgeom="true"
             itemActions="relocate,locate,accessPointlayout,filesman,modify,remove">
        <form>
            <field type="divider" label="基础属性" label-en="BASIC ATTRIBUTE"/>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="ACCESS_KIND_ID" required="true"/>
                <field name="region"/>
            </row>
            <row>
                <field name="OLD_NAME" label="原名称" required="false"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置" label-en="Specific Location"/>
            </row>
            <row>
                <field name="ENVIRONMENT_ID" label="所处环境" required="false"/>
                <field name="LENGTH" label="长度(米)" label-en="Length (m)"/>
            </row>
            <row>
                <field name="WIDTH" label="宽度(米)" label-en="Width (m)"/>
                <field name="HEIGHT" label="高度(米)" label-en="Length (m)"/>
            </row>
            <field type="divider" label="坐标" label-en="LOCATION"/>
            <row>
                <field name="LONGITUDE" label="经度" readOnly="true"/>
                <field name="LATITUDE" label="纬度" readOnly="true"/>
            </row>

            <field type="divider" label="审计属性" label-en="AUDIT ATTRIBUTE"/>
            <row>
                <field name="CREATOR" readOnly="true"/>
                <field name="CREATE_DATE" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" readOnly="true"/>
                <field name="MODIFY_DATE" readOnly="true"/>
            </row>

            <field name="divider" label="网格属性" label-en="Grid" readOnly="true"/>
            <row>
                <!--【浙江】接入点新增无法获取所属网格信息(把field标签里的readonly属性值改为false或者去掉) BUG 56604  -->
                <field name="gridcode" label="所属网格" readOnly="true"/>
                <field name="PROPERTY_TYPE_ID" label='产权性质'/>
            </row>
        </form>
        <grid name="accesspointGrid" toolbar="false">
            <field name="NAME"/>
            <field name="CODE"/>
        </grid>
        <action name="accessPointlayout" label="设备管理" label-en="Devices" type="workspace"
                mainConfig="apps/pipe/accesspoint/accesspoint.xml" icon="hdd"
                urlParams="roomId=${id}&amp;height=${HEIGHT}&amp;width=${WIDTH}&amp;length=${LENGTH}"/>
    </ObjMeta>

    <!-- 子接入点 -->
    <ObjMeta objectType="CHILDACCESSPOINT"
             itemActions="relocate,locate,modify,childaccesspointlayout,filesman,convertToRoom,remove">
        <form>
            <field type="divider" label="基础属性" label-en="BASIC ATTRIBUTE"/>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="site" required="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置" label-en="Specific Location"/>
            </row>
            <row>
                <field name="gridcode" label="所属网格" readOnly="true"/>
                <field name="RING_STAION_NAME" label="动环规范局站名称"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" label-en="Maintenance Level"/>
                <field name="LIFE_STATE_ID" label="使用状态" default="80204847"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式" default="100675"/>
                <field name="supplycompany"/>
            </row>
            <field type="divider" label="坐标" label-en="LOCATION"/>
            <row>
                <field name="LONGITUDE" label="经度" readOnly="true"/>
                <field name="LATITUDE" label="纬度" readOnly="true"/>
            </row>

            <field type="divider" label="审计属性" label-en="AUDIT ATTRIBUTE"/>
            <row>
                <field name="CREATOR" readOnly="true"/>
                <field name="CREATE_DATE" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" readOnly="true"/>
                <field name="MODIFY_DATE" readOnly="true"/>
            </row>

        </form>
        <grid name="childaccesspointGrid" toolbar="false">
            <field name="NAME"/>
            <field name="CODE"/>
        </grid>
        <action name="childaccesspointlayout" label="设备管理" label-en="Devices" type="workspace"
                mainConfig="apps/pipe/room/room.xml" icon="hdd"
                urlParams="roomId=${id}&amp;height=${HEIGHT}&amp;width=${WIDTH}&amp;length=${LENGTH}"/>
        <action name="convertToRoom" label="转成机房" label-en="Convert to Room" type="script"
                url="apps/pipe/room/action/convertToRoom.js"/>

    </ObjMeta>

    <ObjMeta objectType="POSITION" needgeom="false" labelField="FULL_NAME" itemActions="loacleAdress,show_near_entities,attr,nearDrillingAddressResource,releAddress">
        <form>
            <field name="FULL_NAME"/>
            <row>
                <field name="NAME"/>
                <field name="ADDRESS_TYPE_ID"/>
            </row>
            <row>
                <field name="ADDR_LEVEL1"/>
                <field name="ADDR_LEVEL2"/>
            </row>
            <row>
                <field name="ADDR_LEVEL3"/>
                <field name="ADDR_LEVEL4"/>
            </row>
            <row>
                <field name="ADDR_LEVEL5"/>
                <field name="ADDR_LEVEL6"/>
            </row>
            <row>
                <field name="ADDR_LEVEL7"/>
                <field name="ADDR_LEVEL8"/>
            </row>
            <row>
                <field name="ADDR_LEVEL9"/>
                <field name="ADDR_LEVEL10"/>
            </row>
            <row>
                <field name="DATARESOURCE" label="数据来源" label-en="Data Sources"/>
                <field name="HAS_MAPOBJECT"/>
            </row>
            <row>
                <field name="ZIP_CODE"/>
            </row>
            <row>
                <field name="CREATOR" label="采集人"/>
                <field name="CREATE_DATE" label="采集时间"/>
            </row>
        </form>
        <form name="modify">
            <field name="parentfullname" readOnly="true" label="父地址" label-en="Parent address"/>
            <row>
                <field name="NAME" onChange="FULL_NAME=parentfullname+NAME"/>
                <field name="ADDRESS_TYPE_ID" readOnly="true"/>
            </row>
            <row>
                <field name="SIMPLE_SPELL"/>
                <field name="WB"/>
            </row>
            <row>
                <field name="FULL_NAME" readOnly="true"/>
            </row>
            <row>
                <field name="buildingType" queryActionName="queryWithCategory" search="false"
                       dropdownOptions="autoLoad:true"/>
                <field name="ALIAS"/>
            </row>
            <row>
                <field name="ZIP_CODE"/>
            </row>
        </form>
        <form name="add">
            <field name="parentfullname" readOnly="true" label="父地址" label-en="Parent address"/>
            <row>
                <field name="NAME" onChange="FULL_NAME=parentfullname+NAME"/>
                <field name="ADDRESS_TYPE_ID" readOnly="true"/>
            </row>
            <row>
                <field name="SIMPLE_SPELL"/>
                <field name="WB"/>
            </row>
            <row>
                <field name="FULL_NAME" readOnly="true"/>
            </row>
            <row>
                <field name="buildingType" queryActionName="queryWithCategory" search="false"/>
            </row>
            <row>
                <field name="ZIP_CODE"/>
            </row>
            <row>
                <field name="cp_shape" label="继承地图对象" label-en="Inherit map object" type="bool"/>
                <!--<field name="cp_site" label="继承局向" label-en="Inherit Office Direction" type="bool"/>-->
                <field name="cp_cover" label="继承覆盖" label-en="Inheritance coverage" type="bool"/>
            </row>
        </form>

        <form name="add_splitting">
            <field name="parent" label="父地址" onChange="FULL_NAME=@genFullNameBy;ADDRESS_TYPE_ID=@genAddressTypeBy"/>
            <row>
                <field name="NAME" onChange="FULL_NAME=@genFullNameBy"/>
                <field name="ADDRESS_TYPE_ID" readOnly="true"/>
            </row>
            <row>
                <field name="SIMPLE_SPELL"/>
                <field name="WB"/>
            </row>
            <row>
                <field name="FULL_NAME" readOnly="true"/>
            </row>
            <row>
                <field name="buildingType" queryActionName="queryWithCategory" search="false"/>
            </row>
            <row>
                <field name="ZIP_CODE"/>
            </row>
            <row>
                <field name="cp_shape" label="继承地图对象" label-en="Inherit map object" type="bool"/>
                <field name="cp_cover" label="继承覆盖" label-en="Inheritance coverage" type="bool"/>
            </row>
        </form>
        <action name="genFullNameBy" type="script">
            <![CDATA[
          parent.FULL_NAME + NAME
        ]]>
        </action>
        <action name="genAddressTypeBy" type="script">
            <![CDATA[
          parent.ADDRESS_TYPE_ID + 1
        ]]>
        </action>
        <action name="loacleAdress" label="标准地址管理" type="script">
            <![CDATA[
            let params = _actionContext.params;debugger;
            _context.events.trigger('STANDAADDRESS_TREE_LOCATE', {id:params.id});
            _context.doAction({
                    mainConfig: 'apps/gdo3/address/man.xml?timestamp='+new Date().getTime(),
                    title: '标准地址管理',
                    context: 'top',
                    type: "workspace",
                    urlParams: {addressId: params.id || params}
                })
            ]]>
        </action>
        <form name="splitting" label="地址属性" _ui_assemble="coverDevices,coverPorts">
            <field name="id" label="地址ID"/>
            <field name="NAME" label="地址名称"/>
            <field name="FULL_NAME" label="地址全称"/>
            <field name="ADDRESS_TYPE_ID" label="地址级别"/>
        </form>

        <grid name="splitting" label="地址属性" rowDetailField="false" extraActions="">
            <field name="id" label="地址ID"/>
            <field name="NAME" label="地址名称"/>
            <field name="FULL_NAME" label="地址全称"/>
            <field name="ADDRESS_TYPE_ID" label="地址级别"/>
        </grid>

        <grid  searchBaseParam="{'_assemble':'place'}">
            <field name="FULL_NAME"/>
            <field name="IS_AUDIT"/>
            <field name="IS_VALID"/>
            <field name="SIMPLE_SPELL"/>
            <field name="place_value.OLD_BUILDING_ID" label="群物id"/>
            <field name="id" label="地址id"/>
        </grid>


        <grid name="search">
            <field name="FULL_NAME"/>
            <field name="IS_AUDIT"/>
            <field name="IS_VALID"/>
            <field name="SIMPLE_SPELL"/>
        </grid>

        <grid name="positionList" typeActions="doAddAction,batchAdd,moveInGreen,moveOutGreen,leatherCableMaintenance"
              itemActions="guidedBatchAdd,show_near_entities,doLocateMapAction,delMapLocate,doModifyAction,doMoveAddrAction,doMergeAddrAction,doSplitAddrAction,customExport,doDeleteAction">
            <field name="NAME"/>
            <field name="FULL_NAME"/>
        </grid>

        <grid name="drilling">
            <field label="地址ID" name="id"/>
            <field label="地址等级" name="addressLevel"/>
            <field label="地址全称" name="FULL_NAME"/>
        </grid>

        <action name="doAddAction" type="script" label="地址新增" label-en="Add" icon="add">
            <![CDATA[
               var addableLevel = [2, 3, 4, 5, 6, 7, 8, 9];
               var param = new Object();
               var tree = window.tempTree;
               param.id = _actionContext.source.baseParams.PARENT_ID;
               _bean.query('POSITION', param).then(function (item) {
                    if (addableLevel.indexOf(item[0].addressLevel) < 0) {
                        alert(_locale.getString("g5.application.position.message.permissiondenied"));
                        return;
                    }
                    _bean.showAdd("POSITION", {
                        parent: item[0].id,
                        parentfullname: item[0].FULL_NAME,
                        ADDRESS_TYPE_ID: item[0].ADDRESS_TYPE_ID + 1
                        }).then(function () {
                        tree.refreshNode(item[0]);
                    })
               });
            ]]>
        </action>
        <action name="attr" type="workspace" mainConfig="apps/pipe/address/position_attr.xml" icon="table" label="详情"
                urlParams="id=${id}" title="${NAME}-详细信息"/>
        <action name="batchAdd" type="modal" label="批量新增" label-en="Batch Add" icon="add"
                contentUrl="apps/pipe/address/standard_batchAdd_window.html" closable="false"/>

        <action name="moveInGreen" type="script" label="移入绿标" url="apps/pipe/address/js/moveInGreen.js"/>
        <action name="moveOutGreen" type="script" label="移出绿标" url="apps/pipe/address/js/moveOutGreen.js"/>
        <action name="guidedBatchAdd" label="向导式批量新增" type="workspace"
                mainConfig="apps/pipe/address/guideBatchAdd/guidedBatchAdd.xml" urlParams="parentId=${id}"
                if="obj.addressLevel>=5"/>
        <action name="leatherCableMaintenance" type="script" label="皮缆标识维护"
                url="apps/pipe/address/js/leatherCableMaintenance.js"/>
        <action name="doModifyAction" type="script" label="修改" label-en="Modify" icon="write">
            <![CDATA[
                var modifyableLevel = [3, 4, 5, 6, 7, 8, 9, 10];
                var tree = window.tempTree;
                <!--【鸿程】【O3管线第一轮内测】标准地址修改地址类型无效 TASK 66925 -->
                var param = {_assemble:"parent,buildingType"};
                param.id = _actionContext.params.id;
                _bean.find('POSITION', param).then(function (addressItem) {
                    if (modifyableLevel.indexOf(addressItem.addressLevel) < 0) {
                        alert(_locale.getString("g5.application.position.message.permissiondenied"));
                        return;
                    }
                    addressItem.parentfullname = addressItem.parent.FULL_NAME;
                    _bean.showModify("POSITION", addressItem).then(function () {
                        tree.refreshNode(addressItem.parent);
                    })
                });
            ]]>
        </action>

        <action name="doDeleteAction" type="script" label="删除" label-en="Delete" icon="delete circle" _ui_class="red">
            <![CDATA[
                var delableLevel = [3, 4, 5, 6, 7, 8, 9, 10];
                var param = {_assemble:parent};
                param.id = _actionContext.params.id;
                _bean.find('POSITION', param).then(function (item) {
                    if (delableLevel.indexOf(item.addressLevel) < 0) {
                        alert(_locale.getString("g5.application.position.message.permissiondenied"));
                        return;
                    }
                    window.doDeleteAction(item);
                });
            ]]>
        </action>

        <action name="doMoveAddrAction" type="script" label="移动" label-en="Move" icon="expand arrows alternate">
            <![CDATA[
                var moveLevel = [3, 4, 5, 6, 7, 8, 9, 10];
                var param = new Object();
                param.id = _actionContext.params.id;
                _bean.query('POSITION', param).then(function (item1) {
                    if (moveLevel.indexOf(item1[0].addressLevel) < 0) {
                        alert(_locale.getString("g5.application.position.message.permissiondenied"));
                        return;
                    }
                    var parent = new Object();
                    parent.id = item1[0].PARENT_ID;
                    _bean.query('POSITION', parent).then(function (item2) {
                        item1[0].parent=item2[0];
                    });
                    window.doMoveAddrAction(item1[0]);
                });
            ]]>
        </action>

        <action name="doMergeAddrAction" type="script" label="合并" label-en="Merge" icon="clone">
            <![CDATA[
                var mergeLevel = [3, 4, 5, 6, 7, 8, 9, 10];
                var param = new Object();
                param.id = _actionContext.params.id;
                _bean.query('POSITION', param).then(function (item1) {
                    if (mergeLevel.indexOf(item1[0].addressLevel) < 0) {
                        alert(_locale.getString("g5.application.position.message.permissiondenied"));
                        return;
                    }
                    var parent = new Object();
                    parent.id = item1[0].PARENT_ID;
                    _bean.query('POSITION', parent).then(function (item2) {
                        item1[0].parent=item2[0];
                    });
                    window.doMergeAddrAction(item1[0]);
                });
            ]]>
        </action>

        <action name="doSplitAddrAction" label="拆分" title="地址拆分" icon="cut" type="window" style="width:1000px;"
                contentUrl="apps/pipe/address/action/doSplitAddrAction.html"/>

        <action name="doLocateMapAction" type="script" label="落图" label-en="Add Shape" icon="map marker alternate">
            <![CDATA[
                    var param = new Object();
                    param.id = _actionContext.params.id;
                    _bean.query('POSITION', param).then(function (item) {
                        _sui.doAlert(_locale.getString("g5.application.position.message.chooselocategraphic"), _locale.getString("g5.application.common.alert.plzselect"), [{
                            html: '<div class="ui approve positive right icon button">'+_locale.getString("g5.application.position.alert.point")+'</i></div>\n', fn: function () {
                            drawPositionObj('point', item[0]);
                            }
                        }, {
                            html: '<div class="ui approve positive right icon button">'+_locale.getString("g5.application.position.alert.line")+'</i></div>\n', fn: function () {
                                drawPositionObj('polyline', item[0]);
                            }
                        }, {
                            html: '<div class="ui approve positive right icon button">'+_locale.getString("g5.application.position.alert.polygon")+'</i></div>\n', fn: function () {
                                drawPositionObj('polygon', item[0]);
                            }
                        }, {html: '<div class="ui cancel button">'+_locale.getString("common.cancel")+'</div>'}])
                    });


                 //根据activeObjDraw针对address画点线面选择稍微做些改动
                function drawPositionObj(drawType, item) {
                    _bean.getGeomField('POSITION').then(function (geomField) {
                        var om = _bean.getMetaForce('POSITION');
                        var drawOptions = {
                            draw: {
                                circle: false,
                                rectangle: false,
                                marker: false,
                                polyline: false,
                                polygon: false
                            }
                        };
                        if (drawType === 'point') drawType = 'marker';
                        if (drawType) drawOptions.draw[drawType] = true;
                        drawOptions.initMode = drawType;
                        activeWorkspace(_locale.getString('g5.map.title'));
                        _context.map.clear();
                        _context.map.activeDrawSelect(false);
                        _context.map.activeDraw(drawOptions, function (type, graphic) {
                        var gra = _context.map.getWkt(graphic);
                        _bean.post('bean/action.do?_type=POSITION&name=locateMap',{id: item.id, shape: gra,_showLoading:true}).then(function () {
                             //console.log(gra);
                             _context.map.clear();
                             _context.map.activeDraw(false);
                             alert(_locale.getString("g5.application.common.alert.locatesuccess"));
                        });
                        });
                    });
                }
            ]]>
        </action>
        <action name="delMapLocate" type="script" label="解除" label-en="Delete Shape"
                if="obj.HAS_MAPOBJECT_value==80201001||obj.HAS_MAPOBJECT==80201001" icon="times">
            <![CDATA[
                var item = _actionContext.params;
                var id = item.id;
                _bean.post('bean/action.do?_type=POSITION&name=deleteMapLocate&id='+id,{_showLoading:true}).then(function(){
                    alert(_locale.getString('bean.successMsg'));
                });
            ]]>
        </action>
        <action name="customExport" type="modal" label="导出" label-en="Export"
                contentUrl="modules/address/CustomExport.html" closable="false" icon="sign-out"/>
        <action name="change" label-en="Change" label="修改边界" type="script" icon="edit outline">
            <![CDATA[
                var item = _actionContext.params;
                if(item.SHAPE_TYPE_ID==80400571){
                    _context.map.activeEdit(true);
                    _context.map.getLayer('resource').setObjectTypeVisible('map.lineaddress', true);
                    _context.doAction({type: 'obj', name: 'locate'}, {objectType: item.objectType, id: item.id});
                } else if(item.SHAPE_TYPE_ID==80400572){
                    _context.map.activeEdit(true);
                    _context.map.getLayer('resource').setObjectTypeVisible('map.polygonaddress', true);
                    _context.doAction({type: 'obj', name: 'locate'}, {objectType: item.objectType, id: item.id});
                } else {
                    _context.map.activeEdit(true);
                    _context.map.getLayer('resource').setObjectTypeVisible('map.pointaddress', true);
                    _context.doAction({type: 'obj', name: 'locate'}, {objectType: item.objectType, id: item.id});
                }

            ]]>
        </action>
    </ObjMeta>
    <ObjMeta objectType="STANDARDADDRESSALIAS">
        <form>
            <field name="NAME" onChange="SIMPLE_SPELL=@genSpell"/>
            <field name="SIMPLE_SPELL"/>
        </form>
        <grid>
            <field name="NAME"/>
            <field name="SIMPLE_SPELL"/>
        </grid>
        <action name="genSpell">
            <![CDATA[
        _bean.post('app/getPinyinCode', {text: NAME})
      ]]>
        </action>
    </ObjMeta>

    <ObjMeta objectType="DEVICEMODEL" itemActions="view,modify,remove,filesman,uploadifc,view3D">
        <grid name="devicemgtSelectTemplate" toolbar="false" rowDetailField="false">
            <field name="MODEL"/>
            <field name="spec"/>
        </grid>
        <form>
            <row>
                <field name="MODEL" required="true"/>
            </row>
            <row>
                <field name="spec" readOnly="false"
                       onChange="${type_spec}.dropdown.setBaseParam('selectSpec',${spec}.getValue() || -1)"/>
                <field name="type_spec" queryActionName="queryTypeSpecId" search="true"/>
            </row>
            <row>
                <field name="HEIGHT"/>
                <field name="DEPTH"/>
            </row>
            <row>
                <field name="WIDTH"/>
                <field name="vendor"/>
            </row>
            <row>
                <field name="TYPE_STATUS_ID" extra="dtype:TYPE_1;"/>
                <field name="TYPE_LEVEL_ID" extra="dtype:TYPE_1;"/>
            </row>
            <row>
                <field name="COMMENTS"/>
                <field name="IMG_URL"/>
            </row>
        </form>

    </ObjMeta>

    <ObjMeta objectType="EQUIPMODEL" itemActions="view,modify,remove,filesman,viewChildModal,uploadifc,view3D">
        <grid typeActions="add" searchBaseParam="{'spec':'801100580100001'}">
            <field name="MODEL"/>
            <field name="spec"/>
            <field name="type_spec"/>
            <field name="HEIGHT"/>
            <field name="WIDTH"/>
            <field name="DEPTH"/>
            <field name="TERMINALDEVICE_TYPE_ID"/>
        </grid>
        <grid typeActions="" itemActions="view" name="onlyView">
            <field name="MODEL"/>
            <field name="spec"/>
        </grid>
        <grid name="devicemgtSelectTemplate" toolbar="false" rowDetailField="false">
            <field name="MODEL"/>
            <field name="spec"/>
        </grid>
        <form>
            <row>
                <field name="MODEL" required="true"/>
            </row>
            <row>
                <field name="spec" required="true" readOnly="false"
                       onChange="${type_spec}.dropdown.setBaseParam('selectSpec',${spec}.getValue() || -1,false)"/>
                <field name="type_spec" required="true" queryActionName="queryTypeSpecId" search="true"/>
            </row>
            <row>
                <field name="HEIGHT"/>
                <field name="DEPTH"/>
            </row>
            <row>
                <field name="WIDTH"/>
                <field name="vendor"/>
            </row>
            <row>
                <field name="TYPE_STATUS_ID" extra="dtype:TYPE_1;"/>
                <field name="TYPE_LEVEL_ID" extra="dtype:TYPE_1;"/>
            </row>
            <row>
                <field name="COMMENTS"/>
                <field name="IMG_URL"/>
            </row>
        </form>
        <form name="view">
            <row>
                <field name="MODEL" required="true"/>
            </row>
            <row>
                <field name="vendor"/>
                <field name="spec" readOnly="false"/>
            </row>
            <row>
                <field name="HEIGHT"/>
                <field name="DEPTH"/>
            </row>
            <row>
                <field name="WIDTH"/>
                <field name="TYPE_LEVEL_ID" extra="dtype:TYPE_1;"/>
            </row>
            <row>
                <field name="TYPE_STATUS_ID" extra="dtype:TYPE_1;"/>
                <field name="IMG_URL"/>
            </row>
            <row>
                <field name="COMMENTS"/>
                <field name="type_spec"/>
            </row>
        </form>
        <action name="view" label="查看" label-en="View" type="script">
            <![CDATA[
                _bean.showModify(this.data.objectType, this.data.id, '', 'view', {readOnly: true});
            ]]>
        </action>
        <action name="uploadifc" label="上传ifc文件" label-en="Upload ifc file" type="script"></action>
        <action name="view3D" label="浏览3D模型" label-en="Browse 3D models"></action>
        <action name="viewChildModal" label="配置详情" label-en="Configuration details" type="modal"
                contentUrl="modules/model/configChildType.html" closable="false"/>
        <action name="add" label="新增" label-en="New" type="script" icon="add circle">
            <![CDATA[
                _bean.showAdd('EQUIPMODEL', {spec:801100580100001}, '', 'add');
            ]]>
        </action>
    </ObjMeta>

    <ObjMeta objectType="TYPERACK" itemActions="view,modify,remove,filesman,viewChildModal,uploadifc,view3D">
        <grid typeActions="add" searchBaseParam="{'spec':'801100580100002'}">
            <field name="MODEL"/>
            <field name="spec"/>
            <field name="type_spec"/>
            <field name="HEIGHT"/>
            <field name="WIDTH"/>
            <field name="DEPTH"/>
        </grid>
        <grid typeActions="" itemActions="view" name="onlyView">
            <field name="MODEL"/>
            <field name="spec"/>
        </grid>
        <form>
            <row>
                <field name="MODEL" required="true"/>
            </row>
            <row>
                <field name="spec" required="true" readOnly="false"
                       onChange="${type_spec}.dropdown.setBaseParam('selectSpec',${spec}.getValue() || -1,false)"/>
                <field name="type_spec" required="true" queryActionName="queryTypeSpecId" search="true"/>
            </row>
            <row>
                <field name="HEIGHT"/>
                <field name="DEPTH"/>
            </row>
            <row>
                <field name="WIDTH"/>
                <field name="vendor"/>
            </row>
            <row>
                <field name="TYPE_STATUS_ID" extra="dtype:TYPE_1;"/>
                <field name="TYPE_LEVEL_ID" extra="dtype:TYPE_1;"/>
            </row>
            <row>
                <field name="COMMENTS"/>
                <field name="IMG_URL"/>
            </row>
        </form>
        <form name="view">
            <row>
                <field name="MODEL" required="true"/>
            </row>
            <row>
                <field name="vendor"/>
                <field name="spec" readOnly="false"/>
            </row>
            <row>
                <field name="HEIGHT"/>
                <field name="DEPTH"/>
            </row>
            <row>
                <field name="WIDTH"/>
                <field name="TYPE_LEVEL_ID" extra="dtype:TYPE_1;"/>
            </row>
            <row>
                <field name="TYPE_STATUS_ID" extra="dtype:TYPE_1;"/>
                <field name="IMG_URL"/>
            </row>
            <row>
                <field name="COMMENTS"/>
                <field name="type_spec"/>
            </row>
        </form>
        <action name="view" label="查看" label-en="View" type="script">
            <![CDATA[
                _bean.showModify(this.data.objectType, this.data.id, '', 'view', {readOnly: true});
            ]]>
        </action>
        <action name="uploadifc" label="上传ifc文件" label-en="Upload ifc file" type="script"></action>
        <action name="view3D" label="浏览3D模型" label-en="Browse 3D models"></action>
        <action name="viewChildModal" label="配置挂载" label-en="Configuration details" type="modal"
                contentUrl="modules/model/configChildType.html" closable="false"/>
        <action name="add" label="新增" label-en="New" type="script" icon="add circle">
            <![CDATA[
                _bean.showAdd('TYPERACK', {spec:801100580100002,type_spec:1030100002}, '', 'add');
            ]]>
        </action>
    </ObjMeta>

    <ObjMeta objectType="TYPEFRAME" itemActions="view,modify,remove,filesman,viewChildModal,uploadifc,view3D">
        <grid typeActions="add" searchBaseParam="{'spec':'801100580100003'}">
            <field name="MODEL"/>
            <field name="spec"/>
            <field name="type_spec"/>
            <field name="HEIGHT"/>
            <field name="WIDTH"/>
            <field name="DEPTH"/>
        </grid>
        <grid typeActions="" itemActions="view" name="onlyView">
            <field name="MODEL"/>
            <field name="spec"/>
        </grid>
        <form>
            <row>
                <field name="MODEL" required="true"/>
            </row>
            <row>
                <field name="spec" required="true" readOnly="false"
                       onChange="${type_spec}.dropdown.setBaseParam('selectSpec',${spec}.getValue() || -1,false)"/>
                <field name="type_spec" required="true" queryActionName="queryTypeSpecId" search="true"/>
            </row>
            <row>
                <field name="HEIGHT"/>
                <field name="DEPTH"/>
            </row>
            <row>
                <field name="WIDTH"/>
                <field name="vendor"/>
            </row>
            <row>
                <field name="TYPE_STATUS_ID" extra="dtype:TYPE_1;"/>
                <field name="TYPE_LEVEL_ID" extra="dtype:TYPE_1;"/>
            </row>
            <row>
                <field name="COMMENTS"/>
                <field name="IMG_URL"/>
            </row>
        </form>
        <form name="view">
            <row>
                <field name="MODEL" required="true"/>
            </row>
            <row>
                <field name="vendor"/>
                <field name="spec" readOnly="false"/>
            </row>
            <row>
                <field name="HEIGHT"/>
                <field name="DEPTH"/>
            </row>
            <row>
                <field name="WIDTH"/>
                <field name="TYPE_LEVEL_ID" extra="dtype:TYPE_1;"/>
            </row>
            <row>
                <field name="TYPE_STATUS_ID" extra="dtype:TYPE_1;"/>
                <field name="IMG_URL"/>
            </row>
            <row>
                <field name="COMMENTS"/>
                <field name="type_spec"/>
            </row>
        </form>
        <action name="view" label="查看" label-en="View" type="script">
            <![CDATA[
                _bean.showModify(this.data.objectType, this.data.id, '', 'view', {readOnly: true});
            ]]>
        </action>
        <action name="uploadifc" label="上传ifc文件" label-en="Upload ifc file" type="script"></action>
        <action name="view3D" label="浏览3D模型" label-en="Browse 3D models"></action>
        <action name="viewChildModal" label="配置挂载" label-en="Configuration details" type="modal"
                contentUrl="modules/model/configChildType.html" closable="false"/>
        <action name="add" label="新增" label-en="New" type="script" icon="add circle">
            <![CDATA[
                _bean.showAdd('TYPEFRAME', {spec:801100580100003,type_spec:1030200001}, '', 'add');
            ]]>
        </action>
    </ObjMeta>

    <ObjMeta objectType="TYPESLOT" itemActions="view,modify,remove,filesman,viewChildModal,uploadifc,view3D"
             typeActions="add">
        <grid typeActions="add" searchBaseParam="{'spec':'801100580100004'}">
            <field name="MODEL"/>
            <field name="spec"/>
            <field name="type_spec"/>
            <field name="HEIGHT"/>
            <field name="WIDTH"/>
            <field name="DEPTH"/>
        </grid>
        <grid typeActions="" itemActions="view" name="onlyView">
            <field name="MODEL"/>
            <field name="spec"/>
        </grid>
        <form>
            <row>
                <field name="MODEL" required="true"/>
            </row>
            <row>
                <field name="spec" required="true" readOnly="false"
                       onChange="${type_spec}.dropdown.setBaseParam('selectSpec',${spec}.getValue() || -1,false)"/>
                <field name="type_spec" required="true" queryActionName="queryTypeSpecId" search="true"/>
            </row>
            <row>
                <field name="HEIGHT"/>
                <field name="DEPTH"/>
            </row>
            <row>
                <field name="WIDTH"/>
                <field name="vendor"/>
            </row>
            <row>
                <field name="TYPE_STATUS_ID" extra="dtype:TYPE_1;"/>
                <field name="TYPE_LEVEL_ID" extra="dtype:TYPE_1;"/>
            </row>
            <row>
                <field name="COMMENTS"/>
                <field name="IMG_URL"/>
            </row>
        </form>
        <form name="view">
            <row>
                <field name="MODEL" required="true"/>
            </row>
            <row>
                <field name="vendor"/>
                <field name="spec" readOnly="false"/>
            </row>
            <row>
                <field name="HEIGHT"/>
                <field name="DEPTH"/>
            </row>
            <row>
                <field name="WIDTH"/>
                <field name="TYPE_LEVEL_ID" extra="dtype:TYPE_1;"/>
            </row>
            <row>
                <field name="TYPE_STATUS_ID" extra="dtype:TYPE_1;"/>
                <field name="IMG_URL"/>
            </row>
            <row>
                <field name="COMMENTS"/>
                <field name="type_spec"/>
            </row>
        </form>
        <action name="view" label="查看" label-en="View" type="script">
            <![CDATA[
                _bean.showModify(this.data.objectType, this.data.id, '', 'view', {readOnly: true});
            ]]>
        </action>
        <action name="uploadifc" label="上传ifc文件" label-en="Upload ifc file" type="script"></action>
        <action name="view3D" label="浏览3D模型" label-en="Browse 3D models"></action>
        <action name="viewChildModal" label="配置详情" label-en="Configuration details" type="modal"
                contentUrl="modules/model/configChildType.html" closable="false"/>
        <action name="add" label="新增" label-en="New" type="script" icon="add circle">
            <![CDATA[
                _bean.showAdd('TYPESLOT', {spec:801100580100004,type_spec:1030300001}, '', 'add');
            ]]>
        </action>
    </ObjMeta>

    <ObjMeta objectType="TYPECARD" itemActions="view,modify,remove,filesman,uploadifc,view3D" typeActions="add">
        <grid typeActions="add" searchBaseParam="{'spec':'801100580100005'}">
            <field name="MODEL"/>
            <field name="spec"/>
            <field name="type_spec"/>
            <field name="HEIGHT"/>
            <field name="WIDTH"/>
            <field name="DEPTH"/>
        </grid>
        <grid typeActions="" itemActions="view" name="onlyView">
            <field name="MODEL"/>
            <field name="spec"/>
        </grid>
        <form>
            <row>
                <field name="MODEL" required="true"/>
            </row>
            <row>
                <field name="spec" required="true" readOnly="false"
                       onChange="${type_spec}.dropdown.setBaseParam('selectSpec',${spec}.getValue() || -1,false)"/>
                <field name="type_spec" required="true" queryActionName="queryTypeSpecId" search="true"/>
            </row>
            <row>
                <field name="HEIGHT"/>
                <field name="DEPTH"/>
            </row>
            <row>
                <field name="WIDTH"/>
                <field name="vendor"/>
            </row>
            <row>
                <field name="TYPE_STATUS_ID" extra="dtype:TYPE_1;"/>
                <field name="TYPE_LEVEL_ID" extra="dtype:TYPE_1;"/>
            </row>
            <row>
                <field name="COMMENTS"/>
                <field name="IMG_URL"/>
            </row>
        </form>
        <form name="view">
            <row>
                <field name="MODEL" required="true"/>
            </row>
            <row>
                <field name="vendor"/>
                <field name="spec" readOnly="false"/>
            </row>
            <row>
                <field name="HEIGHT"/>
                <field name="DEPTH"/>
            </row>
            <row>
                <field name="WIDTH"/>
                <field name="TYPE_LEVEL_ID" extra="dtype:TYPE_1;"/>
            </row>
            <row>
                <field name="TYPE_STATUS_ID" extra="dtype:TYPE_1;"/>
                <field name="IMG_URL"/>
            </row>
            <row>
                <field name="COMMENTS"/>
                <field name="type_spec"/>
            </row>
        </form>
        <action name="view" label="查看" label-en="View" type="script">
            <![CDATA[
                _bean.showModify(this.data.objectType, this.data.id, '', 'view', {readOnly: true});
            ]]>
        </action>
        <action name="uploadifc" label="上传ifc文件" label-en="Upload ifc file" type="script"></action>
        <action name="view3D" label="浏览3D模型" label-en="Browse 3D models"></action>
        <action name="modify" label="修改" label-en="Mofify" type="modal" icon="write"
                contentUrl="modules/model/cardAttrOpt.html" closable="false"/>
        <action name="add" label="新增" label-en="New" icon="add circle" type="modal"
                contentUrl="modules/model/cardAttrOpt.html" closable="false"/>
    </ObjMeta>

    <ObjMeta objectType="TYPEPORT">
        <form>
            <row>
                <field name="type_spec" queryActionName="queryPortTypeSpec" required="true"
                       onChange="MODEL=@genModel(type_spec,MAXRATE_ID,ELECTRIC_TYPE_ID,DIRECTION_ID)"/>
                <field name="MAXRATE_ID" required="true"
                       onChange="MODEL=@genModel(type_spec,MAXRATE_ID,ELECTRIC_TYPE_ID,DIRECTION_ID)"/>
            </row>
            <row>
                <field name="ELECTRIC_TYPE_ID" required="true"
                       onChange="MODEL=@genModel(type_spec,MAXRATE_ID,ELECTRIC_TYPE_ID,DIRECTION_ID)"/>
                <field name="DIRECTION_ID" required="true"
                       onChange="MODEL=@genModel(type_spec,MAXRATE_ID,ELECTRIC_TYPE_ID,DIRECTION_ID)"/>
            </row>
            <row>
                <field name="MODEL" label="类型名称" label-en="Model" required="true" readOnly="true"/>
                <field name="PORT_COUNTS" label-en="Port count" label="端口数" required="true" type="number"/>
            </row>
            <row>
                <field name="STARTNO" label="起始编号" label-en="Start NO" required="true" type="number"/>
                <field name="NO_COUNTS_ID" label-en="NO count" required="true" type="dict" label="编号位数"
                       dictObjectType="TYPE_1_CONTAIN_TYPE_1"/>
            </row>
        </form>
        <action name="genModel" type="script">
            <![CDATA[
                var typespec_value = type_spec.NAME;
                var maxrate_value = MAXRATE_ID.name;
                var electric_value = ELECTRIC_TYPE_ID.name;
                var direction_value = DIRECTION_ID.name;
                type_spec.NAME+"-" +maxrate_value + "-" + ELECTRIC_TYPE_ID.name + "-" + DIRECTION_ID.name;
            ]]>
        </action>
    </ObjMeta>

    <ObjMeta objectType="CARDTEMP_CONTAIN_PORTTEMP" itemActions="modify,remove">
        <grid toolbar="false" rowDetailField="false">
            <field name="port" getLabel="row.port.MODEL"/>
            <field name="PORT_COUNTS"/>
            <field name="STARTNO"/>
            <field name="NO_COUNTS_ID"/>
            <field name="portSpec" getLabel="row.port.type_spec" label="端口规格" label-en="Type Spec"/>
            <field name="DIRECTION_ID" getLabel="row.port.DIRECTION_ID" label="方向" label-en="Direction"/>
            <field name="ELECTRIC_TYPE_ID" getLabel="row.port.ELECTRIC_TYPE_ID" label="光电属性" label-en="Electric type"/>
            <field name="MAXRATE_ID" getLabel="row.port.MAXRATE_ID" label="最大能力速率" label-en="Max rate"/>
        </grid>

        <form>
            <row>
                <field name="port"/>
                <field name="PORT_COUNTS"/>
            </row>
            <row>
                <field name="STARTNO"/>
                <field name="NO_COUNTS_ID"/>
            </row>
        </form>
    </ObjMeta>

    <ObjMeta objectType="VENDOR" itemActions="view,vendormodify" typeActions="add">
        <grid>
            <field name="NAMECN"/>
            <field name="NAMEEN"/>
        </grid>
        <form>
            <row>
                <field name="NAMECN" required="true"/>
                <field name="NAMEEN" required="true"/>
            </row>
            <row>
                <field name="ABBREV"/>
                <field name="CODINGRULE"/>
            </row>
            <row>
                <field name="COMMENTS"/>
            </row>
        </form>
        <action name="view" label="查看" label-en="View" type="script">
            <![CDATA[
                _bean.showModify(this.data.objectType, this.data.id, '', 'view', {readOnly: true});
            ]]>
        </action>
        <action name="vendormodify" type="script" label="修改" label-en="Modify" icon="write">
            <![CDATA[
                var vendorid = this.data.id;
                var url = "bean/action.do?_type=VENDOR&name=checkModelReference&id="+vendorid;
                _bean.post(url).then(function(res){
                    if(res>0){
                        _sui.confirm(_locale.getString('g5.application.templateMgt.commonApp.modifyVendorMsg'), _locale.getString('g5.application.templateMgt.commonApp.plzConfirm'), null, _locale.getString('common.cancel')).then(function () {
                            _bean.showModify("VENDOR", vendorid).then(function () {
                            });
                        }, function () {
                        });
                    } else {
                         _bean.showModify("VENDOR", vendorid).then(function () {
                        });
                    }
                });
            ]]>
        </action>
    </ObjMeta>


    <ObjMeta objectType="INSTALLATIONANDMAINTENANCEAREA" typeActions="" itemActions="listRelationDevice,viewTeamCode">
        <form>
            <row>
                <field name="CODE"/>
                <field name="NAME"/>
            </row>
            <row>
                <field name="region"/>
                <field name="IS_VALID"/>
            </row>
        </form>
        <form name="viewTeamCode">
            <row>
                <field name="NAME" readOnly="true" lable="区域编码"/>
            </row>
            <row>
                <field name="PROVINCE_CODE"/>
            </row>
            <row>
                <field name="PROVINCE_NAME"/>
            </row>
            <row>
                <field name="CITY_CODE"/>
            </row>
            <row>
                <field name="CITY_NAME"/>
            </row>
            <row>
                <field name="COUNTY_CODE"/>
            </row>
            <row>
                <field name="COUNTY_NAME"/>
            </row>
            <row>
                <field name="BRANCH_CODE"/>
            </row>
            <row>
                <field name="BRANCH_NAME"/>
            </row>
            <row>
                <field name="GROUPBRANCH_CODE"/>
            </row>
            <row>
                <field name="IS_CITY_BRANCH_ID"/>
            </row>
            <!--         	<row><field name="ID" readOnly="true"/></row> -->
        </form>
        <grid>
            <field name="NAME"/>
            <field name="CODE"/>

        </grid>
        <grid name="clean" rowDetailField="false" typeActions="" extraActions="">
            <field name="NAME"/>
            <field name="CODE"/>
        </grid>
        <action icon="add circle" name="add" label="新增" type="script">
            <![CDATA[
                var item = _actionContext.params;
                _sui.doAlert(_locale.getString('g5.application.gridarea.onlyUsedTree'), _locale.getString('g5.application.gridarea.select'), [{
                    html: '<div class="ui approve positive right icon button">'+_locale.getString('common.ok')+'</div>', fn: function (btn) {
                        setTimeout(function(){_bean.showAdd('INSTALLATIONANDMAINTENANCEAREA',{DATA_SOURCE_ID:80206974,AREA_TYPE_ID:80206243,parent:item.id}).then(function(){
                        })},300);
                    }
                }, {
                    html: '<div class="ui cancel button">'+_locale.getString('common.cancel')+'</div>',fn:function (btn) {
                        setTimeout(function(){_bean.showAdd('INSTALLATIONANDMAINTENANCEAREA',{DATA_SOURCE_ID:80206973,AREA_TYPE_ID:80206243,parent:item.id}).then(function(){
                        })},300);
                    }
                }]);
            ]]>
        </action>
        <action name="listRelationDevice" label="查询关联设备" label-en="list relate device" icon="linkify" type="script"
                url="apps/pipe/gridarea/GridQueryDevice.js"/>
        <action name="viewTeamCode" label="查询集团编码" label-en="list group code" icon="linkify" type="script"
                url="apps/pipe/gridarea/ViewTeamCode.js"/>
        <action name="mergeGrid" label="合并支局" type="modal" icon="clone"
                contentUrl="apps/pipe/gridarea/ZhijuGridMerge.html" closable="false"/>
    </ObjMeta>

    <ObjMeta objectType="SMALLUNIT" needgeom="true" typeActions="add,batchDelete,exportAbility"
             itemActions="locate,modify,addShape,listRelationDevice,updateGrid,gridremove,deviceAbility,serviceAbility"
             moreItemActions="filesman,delShape,relationCheck,filterDevice,gridmergeGrid,gridmoveGrid">
        <form>
            <row>
                <field name="NAME"/>
            </row>
            <row>
                <field name="parent"/>
                <field name="GRID_TYPE_ID" required="true"/>
            </row>
            <row>
                <field name="SMALLUNIT_CLASSIFY_ID" required="true"/>
                <field name="IS_COMPETITIVE_AREA_ID"/>
            </row>
            <row>
                <field name="GRID_USE_ID"/>
                <field name="RESPONSIBLEPERSON"/>
            </row>
            <row>
                <field name="ASSETS_ATTRIBUTE_ID"/>
                <field name="LIGHT_COVER_ID"/>
            </row>
        </form>
        <form name="modify">
            <row>
                <field name="CODE" readOnly="true"/>
                <field name="NAME"/>
            </row>
            <row>
                <field name="parent"/>
                <field name="GRID_TYPE_ID" required="true"/>
            </row>
            <row>
                <field name="SMALLUNIT_CLASSIFY_ID" required="true"/>
                <field name="IS_COMPETITIVE_AREA_ID"/>
            </row>
            <row>
                <field name="GRID_USE_ID"/>
                <field name="RESPONSIBLEPERSON"/>
            </row>
            <row>
                <field name="ASSETS_ATTRIBUTE_ID"/>
                <field name="LIGHT_COVER_ID"/>
            </row>
        </form>
        <grid name="showCrossGrid" rowDetailField="false" toolbar="false">
            <field name="NAME"/>
            <field name="CODE"/>
            <field name="COUNTRY" label="子区域" getLabel="row.parent.parent.parent.NAME"/>
            <field name="BRANCH" label="分局" getLabel="row.parent.parent.NAME"/>
            <field name="ZHI" label="支局" getLabel="row.parent.NAME"/>
        </grid>
        <grid name="clean" rowDetailField="false" typeActions="" extraActions="">
            <field name="NAME"/>
            <field name="CODE"/>
        </grid>
        <grid name="querySmallUnit" rowDetailField="false" toolbar="false">
            <field name="CODE" label="网格单元编码"/>
            <field name="NAME" label="网格单元名称"/>
            <field name="parent" label="所属支局" getLabel="row.parent.NAME"/>
            <field name="GRID_TYPE_ID" label="网格单元类型"/>
            <field name="ASSETS_ATTRIBUTE_ID" label="网格资产属性"/>
        </grid>
        <action icon="add circle" name="add" label="新增" label-en="Add" type="script"
                url="apps/pipe/gridarea/smallunitAction.js"/>
        <action name="addShape" permission="GRID_ADDSHAPE" label="添加图形" label-en="Add polygon" icon="image" type="script"
                url="apps/pipe/gridarea/smallunitAction.js"/>
        <action name="delShape" permission="GRID_DELSHAPE" label="删除图形" icon="image" label-en="Delete polygon" type="script"
                if="options.source === 'map'">
            <![CDATA[
                var graid = _actionContext.source.sourceObject.properties.id;
                var r = window.confirm(_locale.getString('g5.application.gridarea.removeDrawConfirmText'));
                if(r){
                    _bean.post('bean/delete.do?_type=map.smallunit&id='+graid,{_showLoading:true}).then(function(){
                        _context.map.refresh();
                        _context.doAction({type:'script',url:'apps/pipe/gridarea/ManualGridEntity.js'},{id:_actionContext.params.id});
                    })
                }
            ]]>
        </action>
        <action name="batchDelete" permission="GRID_BATCHDELETE" type="script" label="批量删除" label-en="Delete">
            <![CDATA[
                var seldatas = this.checkedData;
                var flag = true;
                if(seldatas.length==0){
                    alert(_locale.getString('g5.application.position.message.nodataneeddel'));
                    flag = false;
                }
                if(flag){
                    var selids=[];
                    for (var i = 0; i < seldatas.length; i++) {
                        selids.push(seldatas[i].id)
                    }
                    var url = 'bean/delete.do?_type=SMALLUNIT&id='+selids.join(",");
                    var _self=this;
                    _bean.post(url, {}).then((res) => {
                        if (res !=null&&res.length>0) 
                        {
                            alert(res);
                        }
                        else
                        {
                            alert(_locale.getString('g5.application.common.alert.delsuccess'));
                            _self.reload();
                        }
                    });
                }
            ]]>
        </action>
        <action name="exportAbility" itemsable="true" type="script" label="设备资源能力批量导出"
                url="apps/pipe/gridarea/ExportAbility.js"/>
        <action name="locate" label="定位" label-en="locate" type="script" url="apps/pipe/gridarea/SmallunitLocate.js"/>
        <action name="listRelationDevice" label="查询关联设备" label-en="list relate device" icon="linkify" type="script"
                url="apps/pipe/gridarea/GridQueryDevice.js"/>
        <action name="relationCheck" label="关联检查" icon="crosshairs" label-en="check relationship" type="script"
                url="apps/pipe/gridarea/GridRelationCheck.js"/>
        <action name="filterDevice" label="过滤关系" label-en="filter relationship" icon="crop" type="script"
                url="apps/pipe/gridarea/GridFilterDevice.js"/>
        <action name="serviceAbility" label="业务信息号码统计" title="${NAME}-业务信息号码统计" type="workspace"
                mainConfig="apps/pipe/gridarea/GridServiceInfo.xml" urlParams="gridid=${id}"/>
        <action name="deviceAbility" label="设备资源能力统计" title="${NAME}-设备资源能力统计" type="workspace"
                mainConfig="apps/pipe/gridarea/GridDeviceAbility.xml" urlParams="gridid=${id}"/>
        <!--         <action name="mergeGrid" label="合并网格" label-en="merge grid" type="modal" icon="clone" contentUrl="apps/pipe/gridarea/GridMerge.html" closable="false"/> -->

        <action name="updateGrid" label="修改包含设备" label-en="modify cover device" type="script" icon="clone"
                url="apps/pipe/gridarea/ManualGridEntity.js"/>
    </ObjMeta>

    <ObjMeta objectType="LINK" needgeom="true" typeActions="add" itemActions="cut">
        <action name="cut" label="隔断" label-en="cut" type="script"/>
    </ObjMeta>

    <ObjMeta objectType="FRAME" itemAssemble="device">
        <form>
            <row>
                <field name="NAME"/>
                <field name="CODE"/>
            </row>
            <row>
                <field name="device"/>
                <field name="parentRack"/>
            </row>
            <row>
                <field name="facility"/>
                <field name="model"/>
            </row>
            <row>
                <field name="START_U"/>
                <field name="END_U"/>
            </row>
        </form>
        <form name="whenAddOltextraAttr">
            <row>
                <field name="START_U"/>
                <field name="END_U"/>
            </row>
        </form>
        <script>
            <![CDATA[
            //显示具体名称的时候带上所属设备的名称
            om.titleField = function(o) {
                var name = o.CODE || o.NAME, device = o.device_value || o.device;
                if (device) name = (device.CODE || device.NAME) + '/' + name;
                return name;
            };
        ]]>
        </script>
    </ObjMeta>


    <ObjMeta objectType="CARD">
        <form name="modifyArrangement">
            <row>
                <field name="ROW_NUM" required="true"/>
                <field name="COL_NUM" required="true"/>
            </row>
            <row>
                <field name="arrangement" required="true"/>
            </row>
        </form>
        <form name="modifyTemplateArrangement">
            <row>
                <field name="CODE" label="模块编号" required="true"/>
                <field name="downRowNum" type="number" label="下联行数" required="true"/>
            </row>
            <row>
                <field name="upPortArrangement" label="上联端口" type="dict" dictObjectType="OBD.TEMPLATE" default="1"
                       required="true"/>
                <field name="downColNum" type="number" label="下联列数" required="true"/>
            </row>
            <row>
                <field name="upDownArrangement" label="上下联位置" type="dict" dictObjectType="OBD.TEMPLATE" default="0"
                       required="true"/>
                <field name="ARRANGEMENT_ID" label="下联端口顺序" type="dict" dictObjectType="MODULE" required="true"
                       default="100351"/>
            </row>
        </form>
    </ObjMeta>

    <ObjMeta objectType="FILE" label="附件" label-en="Annex" typeActions="upload" itemActions="download,modify,remove">
        <form>
            <field name="NAME"/>
        </form>
        <grid>
            <field name="NAME"/>
            <field name="FILE_NAME"/>
            <field name="FILE_TYPE"/>
            <field name="CREATOR"/>
            <field name="CREATE_DATE"/>
            <field name="status" width="300px"/>
        </grid>
        <grid name="addressUploadImageGrid">
<!--            <field name="index" label="序号" getLabel="col.index+1+''"/>-->
            <field name="FILE_NAME" label="文件名"/>
            <field name="PICTURE_TYPE" label="类型"/>
            <field name="STATE" label="状态"/>
            <field name="ID" label="ID" style="display:none"/>
            <field name="PATH" label="PATH" style="display:none"/>
            <field name="FLAG" label="FLAG" style="display:none"/>
            <field name="IMGDATA" label="IMGDATA" style="display:none"/>
            <field name="ENTITY_ID" label="地址ID" style="display:none"/>
        </grid>
        <action id="upload" label="上传" label-en="upload" icon="upload" type="script"
                url="modules/common/attachFile.js"/>
        <action id="download" permission="BASIC_ATTACHDOWNLOAD" label="下载" label-en="download" icon="download"
                serverUrl="api/getfile.do?_type=FILE&amp;id=${id}"/>
        <script>
            <![CDATA[
            om.imageField = function(o){
                var ret = {};
                var imgtypes = ['jpg', 'png', 'gif', 'jpeg'];
                if (imgtypes.indexOf(o.FILE_TYPE.toLowerCase()) !== -1) ret.src = _context.fullDataUrl('api/getfile.do?_type=FILE&id=' + o.id);
                return ret;
            };
            om.imageAction = function(o){
                window.open(_context.fullDataUrl('api/getfile.do?_type=FILE&id=' + o.id), '_blank');
            };
        ]]>
        </script>
    </ObjMeta>

    <!--资产-->
    <ObjMeta objectType="ASSET">
        <form>
            <field type="divider" label="基础属性" label-en="BASIC ATTRIBUTE"/>
            <row>
                <field name="CODE"/>
                <field name="RELEGATION"/>
            </row>
            <row>
                <field name="ASSET_STATE_ID"/>
                <field name="ASSET_TYPE_ID"/>
            </row>
            <row>
                <field name="USEDEPT_ID"/>
                <field name="USEUNIT_ID"/>
            </row>
            <row>
                <field name="QUANTITY"/>
                <field name="PRICE"/>
            </row>
            <row>
                <field name="project"/>
                <field name="PROJECT_PRE_FIXED_NO"/>
            </row>
            <row>
                <field name="BATCH"/>
                <field name="ASSET_NATURE"/>
            </row>
            <row>
                <field name="MANAGEDEPTID"/>
                <field name="PARENT_ASSET_ID"/>
            </row>
            <row>
                <field name="RESPONSIBLE_PERSON_ID" label="责任人"/>
                <field name="SERIAL_NUMBER"/>
            </row>
            <row>
                <field name="START_USE_DATE"/>
                <field name="STOP_USE_DATE"/>
            </row>
            <row>
                <field name="BUYDATE"/>
                <field name="NOTES"/>
            </row>

            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="INVENTORY_DATE"/>
                <field name="INVENTORY_STATUS"/>
            </row>
            <row>
                <field name="NUMBER_UNIT"/>
                <field name="CREDENTIALS"/>
            </row>
            <row>
                <field name="ABC_TYPE"/>
                <field name="ORIGINAL_SYSTEM_ASSET_NUMBER"/>
            </row>
            <row>
                <field name="PGYZ"/>
                <field name="PGLJZJ" label="评估累计折旧"/>
            </row>
            <row>
                <field name="PGLJJZ"/>
                <field name="PGSKSYNF"/>
            </row>
            <row>
                <field name="COST_VALUE"/>
                <field name="NET_VALUE"/>
            </row>
            <row>
                <field name="CAN_VALUE"/>
                <field name="DEPRECIAE_CODE"/>
            </row>
            <row>
                <field name="DEMOLITION_STATUS"/>
                <field name="MONTH_DEMOLITION_RATE"/>
            </row>
            <row>
                <field name="DEMOLITION_AMOUNT"/>
                <field name="TAX"/>
            </row>
            <row>
                <field name="BUKRS"/>
                <field name="COST_CENTER"/>
            </row>
        </form>
    </ObjMeta>

    <!--  综合接入区  -->
    <ObjMeta objectType="ACCESSAREA" needgeom="true"
             itemActions="classdataexp,detaildataexp,dataexp,coversite,gjrings,mainfiberarea,microteacharea,bandarea,accesspointgroupcustomer,districtgj,secondgj,firstgj,areastat,locate,add,modify,accessarearemove"
             moreItemActions="" typeActions="add">
        <form _ui_assemble="sites" label="基本属性">
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="region" required="true"/>
            </row>
            <row>
                <field name="MEASURE_AREA" required="true"/>
                <field name="CITY_SUBURB_ID"/>
            </row>
            <row>
                <field name="CREATOR" readOnly="true"/>
                <field name="CREATE_DATE" readOnly="true"/>
            </row>
            <row>
                <field name="COMMENTS"/>
            </row>
        </form>
        <form name="add" _ui_assemble="sites" label="基本属性">
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="region.PARENT_ID" label="地市" type="obj" rtype="CITY"
                       onChange="${region}.dropdown.setBaseParam('parent',${region.PARENT_ID}.getValue(),isDirty)"
                       pageSize="50" required="true"/>
                <field name="region" required="true"/>
            </row>
            <row>
                <field name="STANDARD_NAME" required="true"/>
            </row>
            <row>
                <field name="MEASURE_AREA"/>
                <field name="CITY_SUBURB_ID"/>
            </row>
            <row>
                <field name="CREATOR" readOnly="true"/>
                <field name="CREATE_DATE" readOnly="true"/>
            </row>
            <row>
                <field name="COMMENTS"/>
            </row>
        </form>
        <form name="modify" _ui_assemble="sites" label="基本属性">
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="region.PARENT_ID" label="地市" type="obj" rtype="CITY"
                       onChange="${region}.dropdown.setBaseParam('parent',${region.PARENT_ID}.getValue(),isDirty)"
                       pageSize="50" required="true"/>
                <field name="region" required="true"/>
            </row>
            <row>
                <field name="STANDARD_NAME" required="true"/>
            </row>
            <row>
                <field name="MEASURE_AREA"/>
                <field name="CITY_SUBURB_ID"/>
            </row>
            <row>
                <field name="CREATOR" readOnly="true"/>
                <field name="CREATE_DATE" readOnly="true"/>
            </row>
            <row>
                <field name="COMMENTS"/>
            </row>
        </form>
        <grid>
            <field name="CODE"/>
            <field name="NAME"/>
            <field name="region.PARENT_ID" label="地市" type="obj" rtype="CITY"/>
            <field name="MEASURE_AREA"/>
            <field name="CITY_SUBURB_ID"/>
            <field name="CREATOR"/>
            <field name="CREATE_DATE"/>
            <field name="COMMENTS"/>
        </grid>

        <action name="gjrings" type="workspace" label="光交环网管理" title="光交环网-${NAME}" objectType="GJRING"
                options='{"baseParams": {"parent": "${id}"}}'/>

        <action name="dataexp" type="script" label="数据导出" url="modules/accessarea/js/exportAccessareaData.js"/>
        <action name="detaildataexp" type="script" label="数据明细导出"
                url="modules/accessarea/js/exportAccessareaDetailData.js"/>
        <action name="classdataexp" type="script" label="分级表导出"
                url="modules/accessarea/js/exportAccessareaClassData.js"/>
        <action type="script" name="coversite" objectType="SITE" label="汇聚点" label-en="Cover Site" title="汇聚点"
                title-en="Cover Site">
            <![CDATA[
            var params = _actionContext.params;
            _bean.query('ACCESSAREA_COVER_SITE', { AREA_ID: params.id}).then(function (site) {
                if(site.length==0)
                {
                    alert("此综合业务区下没有汇聚点");
                    return;
                }
                var result=new Array();

                for(var k=0;k<site.length;k++)
                {
                    result.push(site[k].ENTITY_ID);
                }
                _context.doAction({type: 'workspace', objectType: 'SITE', title: '汇聚点', options: {"baseParams": {"id": result}}});
            });
        ]]>
        </action>
        <action type="script" name="firstgj" objectType="GJ" label="一级光交" label-en="First Gj" title="一级光交"
                title-en="First Gj">
            <![CDATA[
            var params = _actionContext.params;
            _bean.find('ACCESSAREA', { id: params.id, _assemble: 'shape' }).then(function (area) {
                _bean.query('map.gj', { shape: area.shape }).then(function (gjs) {
                    var firstgj=new Array();
                    for(var k=0;k<gjs.length;k++)
                    {
                        firstgj.push(gjs[k].entityid);
                    }
                    console.log(gjs);
                    _bean.query('GJ', {GJ_CATEGORY_ID:80402253, id:firstgj}).then(function (firstgjs) {
                    console.log(firstgjs);
                        if(firstgjs.length==0)
                        {
                            alert("此综合业务区下没有一级光交");
                            return;
                        }
                        var result=new Array();
                        for(var k=0;k<firstgjs.length;k++)
                        {
                            result.push(firstgjs[k].id);
                        }
                        _context.doAction({type: 'workspace', objectType: 'GJ', title: '一级光交', options: {"baseParams": {"id": result}}});
                    });
                });
            });
        ]]>
        </action>
        <action type="script" name="secondgj" objectType="GJ" label="二级光交" label-en="SECOND Gj" title="二级光交"
                title-en="SECOND Gj">
            <![CDATA[
            var params = _actionContext.params;
            _bean.find('ACCESSAREA', { id: params.id, _assemble: 'shape' }).then(function (area) {
                _bean.query('map.gj', { shape: area.shape }).then(function (gjs) {
                    var secondgj=new Array();
                    for(var k=0;k<gjs.length;k++)
                    {
                        secondgj.push(gjs[k].entityid);
                    }
                    _bean.query('GJ', {GJ_CATEGORY_ID:80402254, id:secondgj}).then(function (secondgjs) {
                        if(secondgjs.length==0)
                        {
                            alert("此综合业务区下没有二级光交");
                            return;
                        }
                        var result=new Array();
                        for(var k=0;k<secondgjs.length;k++)
                        {
                            result.push(secondgjs[k].id);
                        }
                        _context.doAction({type: 'workspace', objectType: 'GJ', title: '二级光交', options: {"baseParams": {"id": result}}});
                    });
                });
            });
        ]]>
        </action>
        <action type="script" name="districtgj" objectType="GJ" label="小区光交" label-en="DISTRICT Gj" title="小区光交"
                title-en="DISTRICT Gj">
            <![CDATA[
            var params = _actionContext.params;
            _bean.find('ACCESSAREA', { id: params.id, _assemble: 'shape' }).then(function (area) {
                _bean.query('map.gj', { shape: area.shape }).then(function (gjs) {
                    var districtgj=new Array();
                    for(var k=0;k<gjs.length;k++)
                    {
                        districtgj.push(gjs[k].entityid);
                    }
                    _bean.query('GJ', {GJ_CATEGORY_ID:80402255, id:districtgj}).then(function (districtgjs) {
                        if(districtgjs.length==0)
                        {
                            alert("此综合业务区下没有小区光交");
                            return;
                        }
                        var result=new Array();
                        for(var k=0;k<districtgjs.length;k++)
                        {
                            result.push(districtgjs[k].id);
                        }
                        _context.doAction({type: 'workspace', objectType: 'GJ', title: '小区光交', options: {"baseParams": {"id": result}}});
                    });
                });
            });
        ]]>
        </action>
        <action type="script" name="accesspointgroupcustomer" objectType="ACCESSPOINTGROUPCUSTOMER" label="集团客户"
                label-en="Access Point" title="集团客户" title-en="Access Point">
            <![CDATA[
            var params = _actionContext.params;
            _bean.find('ACCESSAREA', { id: params.id, _assemble: 'shape' }).then(function (area) {
                _bean.query('map.accesspointgroupcustomer', { shape: area.shape }).then(function (accesspoints) {
                    if(accesspoints.length==0)
                    {
                        alert("此综合业务区下没有集团客户");
                        return;
                    }
                    var entitys=new Array();
                    for(var k=0;k<accesspoints.length;k++)
                    {
                        entitys.push(accesspoints[k].entityid);
                    }
                    _context.doAction({type: 'workspace', objectType: 'ACCESSPOINTGROUPCUSTOMER', title: '集团客户', options: {"baseParams": {"id": entitys}}});
                });
            });
        ]]>
        </action>
        <action type="script" name="bandarea" objectType="BANDAREA" label="小区" label-en="Bandarea" title="小区"
                title-en="Bandarea">
            <![CDATA[
            var params = _actionContext.params;
            _bean.find('ACCESSAREA', { id: params.id, _assemble: 'shape' }).then(function (area) {
                _bean.query('map.bandarea', { shape: area.shape }).then(function (bandareas) {
                    if(bandareas.length==0)
                    {
                        alert("此综合业务区下没有小区");
                        return;
                    }
                    var entitys=new Array();
                    for(var k=0;k<bandareas.length;k++)
                    {
                        entitys.push(bandareas[k].entityid);
                    }
                    _context.doAction({type: 'workspace', objectType: 'BANDAREA', title: '小区', options: {"baseParams": {"id": entitys}}});
                });
            });
        ]]>
        </action>
        <action type="script" name="microteacharea" objectType="MICROTEACHAREA" label="微格" label-en="Microteach Area"
                title="微格" title-en="Microteach Area">
            <![CDATA[
                var params = _actionContext.params;
                _bean.query('MICROTEACHAREA', { PARENT_ID: params.id, }).then(function (microteachareas) {
                    if(microteachareas.length==0)
                    {
                        alert("此综合业务区下没有微格");
                        return;
                    }
                    var entitys=new Array();
                    for(var k=0;k<microteachareas.length;k++)
                    {
                        entitys.push(microteachareas[k].id);
                    }
                    _context.doAction({type: 'workspace', objectType: 'MICROTEACHAREA', title: '微格', options: {"baseParams": {"id": entitys}}});
                });
            ]]>
        </action>
        <action type="script" name="mainfiberarea" objectType="MAINFIBERAREA" label="主干分纤区" label-en="MAIN FIBER AREA"
                title="主干分纤区" title-en="MAIN FIBER AREA">
            <![CDATA[
                var params = _actionContext.params;
                _bean.query('MAINFIBERAREA', { PARENT_ID: params.id }).then(function (area) {
                    console.log(area);
                    if(area.length==0)
                    {
                        alert("此综合业务区下没有主干分纤区");
                        return;
                    }
                    var result=new Array();
                    for(var k=0;k<area.length;k++)
                    {
                        result.push(area[k].id);
                    }
                    _context.doAction({type: 'workspace', objectType: 'MAINFIBERAREA', title: '主干分纤区', options: {"baseParams": {"id": result}}});
                });
            ]]>
        </action>
        <action name="accessarearemove" label="删除" label-en="Delete" type="script" icon="delete circle" _ui_class="red">
            <![CDATA[
                var item = _actionContext.params;
                var url = "bean/action.do?_type=ACCESSAREA&name=queryMainfiberArea&id="+item.id;
                item._showLoading = true;
                _bean.post(url,item).then(function(res){
                    if(res.length>0){
                        _sui.confirm(params.NAME+'综合业务区下存在'+res[0].NAME+'主干分纤区，您是否还要删除？','提示','确认','取消').then(function(){
                            _context.doAction({type: 'obj', name: 'remove'}, item);
                        });
                    } else {
                        _context.doAction({type: 'obj', name: 'remove'}, item);
                    }
                });
            ]]>
        </action>
    </ObjMeta>

    <!--  主干分纤区  -->
    <ObjMeta objectType="MAINFIBERAREA" needgeom="true"
             itemActions="termainfiberarea,locate,add,modify,mainfiberareamove" typeActions="add">
        <form label="基本属性">
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="parentAccessArea" required="true"/>
            </row>
            <row>
                <field name="region.PARENT_ID" label="地市" type="obj" rtype="CITY"
                       onChange="${region}.dropdown.setBaseParam('parent',${region.PARENT_ID}.getValue(),isDirty)"
                       pageSize="50" required="true"/>
                <field name="region" required="true"/>
            </row>
            <row>
                <field name="MEASURE_AREA" required="true"/>
                <field name="CITY_SUBURB_ID"/>
            </row>
            <row>
                <field name="CREATOR" readOnly="true"/>
                <field name="CREATE_DATE" readOnly="true"/>
            </row>
            <row>
                <field name="COMMENTS"/>
            </row>
        </form>
        <grid>
            <field name="CODE"/>
            <field name="NAME"/>
            <field name="parentAccessArea"/>
            <field name="region.PARENT_ID" label="地市" type="obj" rtype="CITY"/>
            <field name="region"/>
            <field name="MEASURE_AREA"/>
            <field name="CITY_SUBURB_ID"/>
            <field name="CREATOR"/>
            <field name="CREATE_DATE"/>
            <field name="COMMENTS"/>
        </grid>
        <action name="mainfiberareamove" label="删除" label-en="Delete" type="script" icon="delete circle"
                _ui_class="red">
            <![CDATA[
                var item = _actionContext.params;
                var url = "bean/action.do?_type=MAINFIBERAREA&name=queryTermainfiberArea&id="+item.id;
                item._showLoading = true;
                _bean.post(url,item).then(function(res){
                    if(res.length>0){
                        _sui.confirm(params.NAME+'主干分纤区下存在'+res[0].NAME+'末端接入区，您是否还要删除？','提示','确认','取消').then(function(){
                            _context.doAction({type: 'obj', name: 'remove'}, item);
                        });
                    } else {
                        _context.doAction({type: 'obj', name: 'remove'}, item);
                    }
                });
            ]]>
        </action>
        <action type="script" name="termainfiberarea" objectType="TERMAINFIBERAREA" label="末端分纤区"
                label-en="TERMAIN FIBER AREA" title="末端分纤区" title-en="TERMAIN FIBER AREA">
            <![CDATA[
                var params = _actionContext.params;
                _bean.query('TERMAINFIBERAREA', { PARENT_ID: params.id }).then(function (area) {
                    if(area.length==0)
                    {
                        alert("此主干分纤区下没有末端分纤区");
                        return;
                    }
                    var result=new Array();
                    for(var k=0;k<area.length;k++)
                    {
                        result.push(area[k].id);
                    }
                    _context.doAction({type: 'workspace', objectType: 'TERMAINFIBERAREA', title: '末端分纤区', options: {"baseParams": {"id": result}}});
                });
            ]]>
        </action>
    </ObjMeta>

    <!--  末端分纤区  -->
    <ObjMeta objectType="TERMAINFIBERAREA" needgeom="true" itemActions="locate,add,modify,remove">
        <form label="基本属性">
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="parentMainfiberArea" required="true"/>
            </row>
            <row>
                <field name="region.PARENT_ID" label="地市" type="obj" rtype="CITY"
                       onChange="${region}.dropdown.setBaseParam('parent',${region.PARENT_ID}.getValue(),isDirty)"
                       pageSize="50" required="true"/>
                <field name="region" required="true"/>
            </row>
            <row>
                <field name="MEASURE_AREA" required="true"/>
                <field name="CITY_SUBURB_ID"/>
            </row>
            <row>
                <field name="CREATOR" readOnly="true"/>
                <field name="CREATE_DATE" readOnly="true"/>
            </row>
            <row>
                <field name="COMMENTS"/>
            </row>
        </form>
        <grid>
            <field name="CODE"/>
            <field name="NAME"/>
            <field name="parentMainfiberArea"/>
            <field name="region.PARENT_ID" label="地市"/>
            <field name="region"/>
            <field name="MEASURE_AREA"/>
            <field name="CITY_SUBURB_ID"/>
            <field name="CREATOR"/>
            <field name="CREATE_DATE"/>
            <field name="COMMENTS"/>
        </grid>
    </ObjMeta>

    <!--  微格  -->
    <ObjMeta objectType="MICROTEACHAREA" needgeom="true" itemActions="locate,add,modify,remove">
        <form label="基本属性">
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="parentAccessArea" required="true"/>
            </row>
            <row>
                <field name="region.PARENT_ID" label="地市" type="obj" rtype="CITY"
                       onChange="${region}.dropdown.setBaseParam('parent',${region.PARENT_ID}.getValue(),isDirty)"
                       pageSize="50" required="true"/>
                <field name="region" required="true"/>
            </row>
            <row>
                <field name="MEASURE_AREA" required="true"/>
                <field name="CITY_SUBURB_ID"/>
            </row>
            <row>
                <field name="CREATOR" readOnly="true"/>
                <field name="CREATE_DATE" readOnly="true"/>
            </row>
            <row>
                <field name="COMMENTS"/>
            </row>
        </form>
        <grid>
            <field name="CODE"/>
            <field name="NAME"/>
            <field name="parentAccessArea"/>
            <field name="region.PARENT_ID" label="地市"/>
            <field name="region"/>
            <field name="MEASURE_AREA"/>
            <field name="CITY_SUBURB_ID"/>
            <field name="CREATOR"/>
            <field name="CREATE_DATE"/>
            <field name="COMMENTS"/>
        </grid>
    </ObjMeta>

    <!--  光交接入区  -->
    <ObjMeta objectType="GJACCESSAREA" needgeom="true" itemActions="locate,add,modify,remove">
        <form _ui_assemble="nets" label="基本属性">
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="parentAccessArea"/>
                <field name="region" required="true"/>
            </row>
            <row>
                <field name="IS_VALID" required="true"/>
                <field name="DESIGNBY"/>
            </row>
            <row>
                <field name="SOURCE"/>
                <field name="COMMENTS"/>
            </row>
        </form>
    </ObjMeta>

    <!--  一级光交覆盖区  -->
    <ObjMeta objectType="GJCOVERAREA" needgeom="true" itemActions="locate,add,modify,remove">
        <form _ui_assemble="gjs" label="基本属性">
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="region" required="true"/>
            </row>
            <row>
                <field name="IS_VALID" required="true"/>
                <field name="DESIGNBY"/>
            </row>
            <row>
                <field name="SOURCE"/>
                <field name="COMMENTS"/>
            </row>
        </form>
    </ObjMeta>

    <!--  街坊  -->
    <ObjMeta objectType="STREETLANE" needgeom="true" itemActions="locate,add,modify,remove">
        <form _ui_assemble="gjs" label="基本属性">
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="parentGjAccessArea" required="true"/>
                <field name="region" required="true"/>
            </row>
            <row>
                <field name="IS_VALID" required="true"/>
                <field name="DESIGNBY"/>
            </row>
            <row>
                <field name="SOURCE"/>
                <field name="COMMENTS"/>
            </row>
        </form>
    </ObjMeta>

    <!-- 光交环网 -->
    <ObjMeta objectType="GJRING" itemActions="locate,modify,remove">
        <grid>
            <field name="CODE"/>
            <field name="NAME"/>
            <field name="AREA_TYPE_ID"/>
            <field name="parent" label="所属综合接入区"/>
            <field name="startSite"/>
            <field name="region.PARENT_ID" label="所属地市" type="obj" rtype="AREA"/>
            <field name="region"/>
            <field name="CREATOR"/>
            <field name="CREATE_DATE"/>
        </grid>
        <form name="search">
            <field name="CODE" required="true"/>
            <field name="NAME" required="true"/>
            <field name="AREA_TYPE_ID"/>
        </form>
        <form label="基本属性" _ui_assemble="gjs,ocables">
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="region.PARENT_ID" readOnly="true" rtype="CITY" label="地市" type="obj"/>
                <field name="region" readOnly="true" onChange="region.PARENT_ID=region.PARENT_ID"/>
            </row>
            <row>
                <field name="parent" label="综合接入区" rtype="ACCESSAREA"
                       onChange="region=parent.REGION_ID"/>
            </row>
            <row>
                <field name="AREA_TYPE_ID"/>
                <field name="startSite"/>
            </row>
            <row>
                <field name="NOTES" inputTagName="textarea"/>
            </row>
        </form>
        <action name="locate" label="定位" type="script">
            <![CDATA[
        _bean.find('GJRING', {id: _actionContext.params.id, _assemble: 'gjs[shape],ocables[shape]'}).then(function(o){
          var shapes = o.gjs.map(function(g){return g.shape;});
          Array.prototype.push.apply(shapes, o.ocables.map(function(g){return g.shape;}));
          shapes = shapes.filter(function(g){return g;});
          _context.map.locateByWkts(shapes, 2000);
        });
      ]]>
        </action>
    </ObjMeta>

    <!--  拉线-->
    <ObjMeta objectType="GISHAULLINE" itemActions="modify,remove">
        <form>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="pole" required="true"/>
                <field name="STAY_WIRE_TYLE_ID"/>
            </row>
            <row>
                <field name="ASSISTANCE_TYPE_ID"/>
                <field name="STAY_WIRE_LENGTH"/>
            </row>
        </form>
    </ObjMeta>

    <!-- 公告 -->
    <ObjMeta objectType="ANNOUNCE" itemActions="modify,remove">
        <form>
            <row>
                <field name="TITLE" required="true"/>
                <field name="PUBLISH_DEP" label="来源"/>
            </row>
            <row>
                <field name="PUBLISH_DATE" required="true"/>
                <field name="OVER_DATE" required="true"/>
            </row>
            <field name="CONTENT" inputTagName="textarea" required="true"/>
        </form>
        <grid>
            <field name="TITLE"/>
            <field name="PUBLISH_DATE"/>
            <field name="CREATOR"/>
            <field name="CREATE_DATE"/>
        </grid>
    </ObjMeta>
    <ObjMeta objectType="FACILITY">
        <grid name="samerouteanaly" toolbar="false" rowDetailField="false">
            <field name="NAME" orderBy="false"/>
            <field name="CODE" orderBy="false"/>
            <field name="METACATEGORYCN" label="类型" orderBy="false"/>
        </grid>
        <grid name="FTTX10FACILITY" filterable="false" sortable="false">
            <field name="CODE" label="机房编码"/>
            <field name="NAME" label="机房名称"/>
            <field name="site" label="所属局站" getLabel="row.site_value ? row.site_value.NAME : ''"/>
        </grid>

    </ObjMeta>

    <ObjMeta objectType="ADDRESS_TYPE" labelField="NAME"></ObjMeta>
    <ObjMeta objectType="CONCEPTAREA" label="自划区域">
        <form>
            <row>
                <field name="CODE"/>
                <field name="NAME"/>
            </row>
            <row>
                <field name="CREATOR" readOnly="true"/>
                <field name="CREATE_DATE" readOnly="true"/>
            </row>
        </form>
    </ObjMeta>
    <!--标签模板-->
    <ObjMeta objectType="LABEL_TEMPLATE" label="标签模板">
        <grid name="TEMPLATENAME" toolbar="false" rowDetailField="false">
            <field name="region" label="所属地市"/>
            <field name="NAME" width="180px"/>
            <field name="CREATOR" />
            <field name="CREATE_DATE" width="150px"/>
        </grid>

        <grid name="bandWidthGrid" toolbar="false" rowDetailField="false">
            <field name="region" label="所属地市"/>
            <field name="NAME" label="模板名称" width="180px"/>
            <field name="TAGTYPE_ID" label="标签类型"/>
            <field name="NOTES" label="标签样式"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间" width="150px"/>
            <field name="TEMPLATETYPE_ID" label="模板类型" width="150px"/>
        </grid>
    </ObjMeta>
    <!-- 划小县区 -->
    <ObjMeta objectType="SMALLCOUNTRY" labelField="NAME"></ObjMeta>
    <!-- 营销区域 -->
    <ObjMeta objectType="MARKETINGAREA" labelField="NAME"></ObjMeta>
    <ObjMeta objectType="SERVICEAREA" labelField="NAME"></ObjMeta>

<!--三资三率地图地址模型-->
    <ObjMeta objectType="RATIOSPOSITION" needgeom="false" labelField="FULL_NAME" itemActions="locate,TreResTreProdPic">
        <grid  searchBaseParam="{'_assemble':'place'}">
            <field name="FULL_NAME"/>
            <field name="IS_AUDIT"/>
            <field name="IS_VALID"/>
            <field name="SIMPLE_SPELL"/>
            <field name="place_value.OLD_BUILDING_ID" label="群物id"/>
        </grid>
    </ObjMeta>

<!--三资三率,商业楼宇/小区社区楼宇地址关系表-->
    <ObjMeta objectType="X_SZSL_BUILDING_ADDRESS" needgeom="false" labelField="BUILDING_ADDRESS_NAME" itemActions="locate,TreResTreProdPic" autoReload="true">
        <grid autoLoad="true">
            <field name="BUILDING_ADDRESS_NAME" label="所属楼宇地址名称"/>
        </grid>

        <grid autoLoad="true"  name="search">
            <field name="BUILDING_ADDRESS_NAME" label="楼宇地址名称"/>
            <field name="BUILDING_NAME" label="楼宇名称"/>
        </grid>

        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="BUILDING_NAME" label="楼宇名称"/>
            </row>
            <row>
                <field name="BUILDING_ADDRESS_NAME" label="楼宇地址名称"/>
            </row>
        </form>
    </ObjMeta>

    <ObjMeta objectType="X_HLW_COM_RESULT" typeActions="batchConfirm,batchCancel,batchCancelEnsure,batchImpeachData,countResult,impeachAbout,batchSendOrder"
             itemActions="gisConfirm,mergeResult,Confirm,Cancel,CancelEnsure,manualMapping,impeachData,sendOrder" extraActions="downloadManualEnsureTemplate,ManualEnsureImport" labelField="MAP_ADDRESS_FULLNAME">
        <form>
            <row>
                <field name="region" baseParams='{"AREA_LEVEL_ID":"100698"}' required="true"/>
                <field name="MAP_ADDRESS_SPEC_ID" label="类型" required="true"/>
            </row>
            <row>
                <field name="MAP_ADDRESS_FULLNAME"	label="图商地址全称" />
                <field name="MAP_ADDRESS_NAME" label="图商地址名称"/>
            </row>
            <row>
                <field name="position"/>
            </row>
            <row>
                <field name="CHECKRESULT"	    label="稽核结果" type="dict" required="true" style="width:300px"/>
                <field name="CONFIRM_RESULT"	label="确认结果" type="dict" required="true" style="width:300px"/>
            </row>
        </form>
        <grid>
            <field name="MAP_ADDRESS_ID"	    label="图商地址ID" excelCellType="forceText"/>
            <field name="MAP_ADDRESS_FULLNAME"	label="图商地址全称"/>
            <field name="MAP_ADDRESS_NAME" label="图商地址名称"/>
            <field name="MAP_ADDRESS_SPEC_ID" label="类型"/>
            <field name="ADDRESS_ID"	    label="标准地址ID" type="id" excelCellType="forceText"/>
            <field name="ADDRESS_FULLNAME"	label="	标准地址全称"/>
            <field name="region"/>
            <field name="CHECKRESULT"	    label="	稽核结果"/>
            <field name="CONFIRM_RESULT"	label="	确认结果"/>
            <field name="CONFIRM_TIME"	    label="	确认时间"/>
            <field name="CURRENT_PERSON" label="操作人"/>
            <field name="creator" label="确认人"/>
            <field name="CREATEDATE"	    label="	创建时间"/>
            <field name="ADDRESS_LON"	    label="标准地址经度"/>
            <field name="ADDRESS_LAT"	    label="标准地址维度"/>
            <field name="MAPADDRESS_LON"	label="图商地址码经度"/>
            <field name="MAPADDRESS_LAT"	label="图商地址码纬度"/>
            <field name="DISTANCE" label="距离(m)"/>
        </grid>
        <action type="modal" contentUrl="apps/internet_position/mapaddress/mapAddressStatic.html"
                label="匹配结果统计" name="countResult" closable="false" style="width:800px;height:600px">
        </action>
        <action type="modal" contentUrl="apps/internet_position/mapaddress/impeachDetail.html"
                label="存疑详情" name="impeachAbout" closable="true" style="width:1000px;height:500px;">
        </action>
        <action name="batchConfirm" label="批量确认" type="script">
            <![CDATA[
                 var data=_actionContext.source.checkedData;
                 debugger;
                  if(data.length==0)
                  {
                     alert("请选择至少一条记录进行操作");
                  }else{
                        //约束下未匹配成功的，不允许确认
                        var errorMessage="";
                         var newData=new Array();
                         for(var i=0;i<data.length;i++){
                             if(data[i].CHECKRESULT=="" || data[i].CHECKRESULT_value==7 || data[i].ADDRESS_ID==""){
                                    errorMessage+=data[i].MAP_ADDRESS_FULLNAME+",";
                               }else{
                                   newData.push(data[i]);
                               }
                         }
                        if(errorMessage!=""){
                           if(newData.length>0){
                                if(confirm("选中的数据："+errorMessage+"不符合确认要求不予确认,是否继续")){
                                   if(confirm("选中的数据,可能存在关联的实体，是否一并同步?")){
                                        var grid = _actionContext.sourceui;
                                         _bean.action("ADDRESS_RELATE_MAPADDRESS","batchMapAddressConfirm",{data:newData,_showLoading:true}).then(function(){
                                              alert("确认成功");
                                              grid.reload();
                                         });
                                   }
                                }
                           }else{
                                 alert("选中数据均不符合确认的要求，不予确认");
                           }
                        }else{
                            if(newData.length>0){
                              var grid = _actionContext.sourceui;
                              if(confirm("选中的数据,可能存在关联的实体，是否一并同步?")){
                                   _bean.action("ADDRESS_RELATE_MAPADDRESS","batchMapAddressConfirm",{data:newData,_showLoading:true}).then(function(){
                                          alert("确认成功");
                                          grid.reload();
                                     });
                              }
                            }else{
                                 alert("选中数据均不符合确认的要求，不予确认");
                            }
                        }
                  }
              ]]>
        </action>
        <action name="Confirm" label="确认" label-en="Ensure" type="modal"
                contentUrl="apps/internet_position/mapaddress/singleConfirm.html"
                closable="false">
        </action>
        <!--        <action name="Confirm" label="确认" type="script">-->
        <!--            <![CDATA[-->
        <!--                var arrayData=new Array();-->
        <!--                 var data=_actionContext.params;-->
        <!--                 if(data.CHECKRESULT=="" || data.CHECKRESULT_value==7 || data.ADDRESS_ID==""){-->
        <!--                     alert("当前数据未匹配成功/未匹配标准地址，不允许确认");-->
        <!--                 }else{-->
        <!--                     arrayData.push(data);-->
        <!--                     var grid = _actionContext.sourceui;-->
        <!--                     _bean.action("ADDRESS_RELATE_MAPADDRESS","batchMapAddressConfirm",{data:arrayData,_showLoading:true}).then(function(){-->
        <!--                          alert("确认成功");-->
        <!--                          grid.reload();-->
        <!--                     });-->
        <!--                 }-->
        <!--              ]]>-->

        <!--        </action>-->
        <action name="batchCancel" label="批量取消确认" type="script">
            <![CDATA[
                var data=_actionContext.source.checkedData;
                   if(data.length==0)
                  {
                     alert("请选择至少一条记录进行操作");
                  }else{
                    if(confirm("确认将所选地址取消确认关联")){
                        var grid = _actionContext.sourceui;
                         //约束下未确认一致的不允许取消
                        var errorMessage="";
                         var newData=new Array();
                         for(var i=0;i<data.length;i++){
                             if(data[i].CONFIRM_RESULT=="" || data[i].CONFIRM_RESULT_value!=2){
                                    errorMessage+=data[i].MAP_ADDRESS_FULLNAME+",";
                               }else{
                                   newData.push(data[i]);
                               }
                         }
                        if(errorMessage!=""){
                           if(newData.length>0){
                                if(confirm("选中的数据："+errorMessage+"不符合取消要求不予取消,是否继续")){
                                   var grid = _actionContext.sourceui;
                                     _bean.action("ADDRESS_RELATE_MAPADDRESS","batchMapAddressCancel",{data:newData,opt:"cancel",_showLoading:true}).then(function(){
                                          alert("取消确认成功");
                                          grid.reload();
                                     });
                                }
                           }else{
                                 alert("选中数据均不符合取消的要求，不予取消");
                           }
                        }else{
                            if(newData.length>0){
                              var grid = _actionContext.sourceui;
                                 _bean.action("ADDRESS_RELATE_MAPADDRESS","batchMapAddressCancel",{data:newData,opt:"cancel",_showLoading:true}).then(function(){
                                      alert("取消确认成功");
                                      grid.reload();
                                 });
                            }else{
                                 alert("选中数据均不符合取消的要求，不予取消");
                            }
                        }
                    }
                  }
              ]]>
        </action>
        <action name="Cancel" label="取消" type="script">
            <![CDATA[
                var data=_actionContext.params;
                var arrayData=new Array();
                 var grid = _actionContext.sourceui;
                 if(data.CONFIRM_RESULT=="" || data.CONFIRM_RESULT_value!=2){
                    alert("未确认一致的数据，不允许取消");
                 }else{
                     arrayData.push(data);
                     _bean.action("ADDRESS_RELATE_MAPADDRESS","batchMapAddressCancel",{data:arrayData,opt:"cancel",_showLoading:true}).then(function(){
                          alert("取消确认成功");
                          grid.reload();
                     });
                 }
              ]]>
        </action>
        <action name="batchCancelEnsure" label="批量确认关联错误" type="script">
            <![CDATA[
                var data=_actionContext.source.checkedData;
                   if(data.length==0)
                  {
                     alert("请选择至少一条记录进行操作");
                  }else{
                     var grid = _actionContext.sourceui;
                      if(confirm("确认将所选地址确认错误关联")){
                           _bean.action("ADDRESS_RELATE_MAPADDRESS","batchMapAddressCancel",{data:data,opt:"error",_showLoading:true}).then(function(){
                              alert("批量确认关联错误成功");
                              grid.reload();
                         });
                      }
                  }
              ]]>
        </action>
        <action name="CancelEnsure" label="确认关联错误" type="script">
            <![CDATA[
                var data=_actionContext.params;
                var arrayData=new Array();
                 var grid = _actionContext.sourceui;
                  arrayData.push(data);
                 _bean.action("ADDRESS_RELATE_MAPADDRESS","batchMapAddressCancel",{data:arrayData,opt:"error",_showLoading:true}).then(function(){
                      alert("确认关联错误成功");
                      grid.reload();
                 });
              ]]>
        </action>
        <action type="modal" contentUrl="apps/internet_position/mapaddress/impeachData.html"
                label="存疑数据" name="impeachData" closable="false" style="width:500px;height:400px"/>
        <action name="batchImpeachData" label="批量存疑数据" type="script">
            <![CDATA[
                var data=_actionContext.source.checkedData;
                   if(data.length==0)
                  {
                     alert("请选择至少一条记录进行操作");
                  }else{
                     var grid = _actionContext.sourceui;
                      if(confirm("确认将所选地址存疑")){
                      //约束下有地址的ID不允许存疑
                        var errorMessage="";
                         var newData=new Array();
                         for(var i=0;i<data.length;i++){
                             if(data[i].ADDRESS_ID!=""){
                                    errorMessage+=data[i].MAP_ADDRESS_FULLNAME+",";
                               }else{
                                   newData.push(data[i]);
                               }
                         }
                        if(errorMessage!=""){
                           if(newData.length>0){
                                if(confirm("选中的数据："+errorMessage+"不符合存疑不予存疑,是否继续")){
                                            _context.doAction({type:'modal',contentUrl:"apps/internet_position/mapaddress/impeachData.html",style:"width:500px;height:400px"},{impeachData:newData});
                                }
                           }else{
                                 alert("选中数据均不符合存疑的要求，不予存疑");
                           }
                        }else{
                            if(newData.length>0){
                              var grid = _actionContext.sourceui;
                                        _context.doAction({type:'modal',contentUrl:"apps/internet_position/mapaddress/impeachData.html",style:"width:500px;height:400px"},{impeachData:newData});
                            }else{
                                 alert("选中数据均不符合存疑的要求，不予存疑");
                            }
                        }
                      }
                  }
              ]]>
        </action>
        <action name="manualMapping" label="人工映射" label-en="Manual mapping" type="modal"
                contentUrl="apps/internet_position/mapaddress/manualMapping.html"
                closable="false">
        </action>
        <action name="gisConfirm" label="Gis地图确认" type="script">
            <![CDATA[
                var data=_actionContext.params;
                let confirmObjType = data.objectType;
                let confirmId = data.id;
                if(data.MAP_ADDRESS_ID){
                    let params ={addressId:'',mapAddressId:data.MAP_ADDRESS_ID,comResultId:data.id,_showLoading:true}
                    _bean.action('X_HLW_COM_RESULT','queryMergeShape',params).then(function (res){
                        let mapAddress = res.mapAddress
                        if(!mapAddress){
                            _sui.alert('未找到图商地址信息，请核查数据！')
                            return false;
                        }
                        let map = _context.map|| parentContext.map
                        map.clear();
                        let layerGroup = null;
                        if(_context && _context._locatebyLayerGroup) layerGroup=_context._locatebyLayerGroup;
                        else if(parentContext && parentContext._locatebyLayerGroup) layerGroup = parentContext._locatebyLayerGroup
                        else layerGroup = map.createLayerGroup();
                        if (layerGroup) layerGroup.clearLayers();

                        if(_context.map){
                            activeWorkspace(_locale.getString('g5.map.title'));
                        }else{
                            window.parent.activeWorkspace(_locale.getString('g5.map.title'));
                        }
                        let sharr = [];
                        if(mapAddress.shape){
                            sharr.push(mapAddress.shape);
                            let iconUrl = 'assets/icon/position/position-marker-ts.png'
                            var p = map.readWkt(mapAddress.shape);
                            var point = map.createMarker(p._latlng, {
                                iconOptions: {
                                    iconUrl: iconUrl,
                                    iconSize: [25, 25] //实际图标的宽高
                                }
                            }, true);
                            point.bindPopup(`<h5>名称：${mapAddress.ADDR || mapAddress.NAME}<br>类型：${mapAddress.METACATEGORYCN}</h5>`, {autoClose: false}).openPopup()
                            point.on('click', function(layer) {
                                let shapesss = map.getBounds(mapAddress.shape).getCenter();
                                map.showObjPopup(shapesss, confirmObjType, confirmId);
                                e.stopPropagation();
                            });
                            layerGroup.addLayer(point);
                        }
                        if(sharr.length==0){
                            _sui.alert('您所定位的实体无坐标，请核查数据！')
                            return
                        }
                        map.zoomTo(sharr);
                    })
                } else {
                    alert("该记录未关联标准地址,不允许地图确认");
                }
            ]]>
        </action>
        <action name="mergeResult" label="融合结果展示" type="script" url="apps/internet_position/mapaddress/js/mergeResult.js"/>
        <action name="sendOrder" label="派单" label-en="Send Order" type="modal" contentUrl="apps/internet_position/mapaddress/sendOrder.html"/>
        <action name="batchSendOrder" label="批量派单" label-en="Batch Send Order" type="script">
            <![CDATA[
                var data=_actionContext.source.checkedData;
                   if(data.length==0)
                  {
                     alert("请选择至少一条记录进行操作");
                  }else{
                      var newData=new Array();
                      for(var i=0;i<data.length;i++){
                         newData.push(data[i]);
                      }
                      if(newData){
                          _context.doAction({type:'modal',contentUrl:"apps/internet_position/mapaddress/sendOrder.html",style:"width:900px;height:400px"},{sendOrderData:newData});
                      }
                  }
            ]]>
        </action>
        <action name="downloadManualEnsureTemplate" label="人工确认模板下载" label-en="Download Manual Ensure Template" type="script" url="apps/internet_position/mapaddress/js/downloadMapAddressFile.js"/>
        <action name="ManualEnsureImport"           label="人工确认导入"     label-en="Manual Ensure Import"            type="script" url="apps/internet_position/mapaddress/js/importMapAddressFile.js"/>
    </ObjMeta>

</metas>