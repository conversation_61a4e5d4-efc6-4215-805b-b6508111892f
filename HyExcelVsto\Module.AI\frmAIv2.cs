/*
 * ============================================================================
 * 功能模块：AI辅助工具v2窗体
 * ============================================================================
 *
 * 模块作用：提供增强版AI辅助功能的用户界面窗体，支持Excel数据处理和AI分析
 *
 * 主要功能：
 * - 提供Excel数据源和目标区域的可视化选择
 * - 支持行模式和列模式两种数据处理方式
 * - 支持文件上传和本地读取两种文件处理模式
 * - 提供进度显示和日志记录功能
 * - 支持处理过程的取消操作
 *
 * 执行逻辑：
 * 1. 初始化窗体界面和AI助手组件
 * 2. 加载模型配置和规则文件到下拉框
 * 3. 设置默认的Excel范围选择区域
 * 4. 处理用户执行请求，验证输入参数
 * 5. 调用AI助手进行数据处理并显示结果
 * 6. 管理窗体关闭时的资源清理
 *
 * 注意事项：
 * - 需要正确处理异步操作和取消令牌
 * - 注意跨线程UI更新需要使用Invoke方法
 * - AI模型和规则文件需要按指定格式命名和组织
 * - 需要妥善处理异常情况和用户取消操作
 * ============================================================================
 */

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.IO;
using System.Diagnostics;
using Microsoft.Office.Interop.Excel;
using Common.Utility;
using ET;
using ET.ETAIv2;
using ET.ETAIv2.Models;

namespace HyExcelVsto.Module.AI
{
    /// <summary>
    /// AI辅助工具v2窗体类
    /// </summary>
    /// <remarks>提供增强版AI辅助功能的用户界面窗体，支持Excel数据处理和AI分析。 包含数据源选择、处理模式配置、进度显示和结果展示等功能。</remarks>
    public partial class frmAIv2 : Form
    {
        /// <summary>
        /// AI助手实例
        /// </summary>
        private AIExcelAssistant _aiAssistant;

        /// <summary>
        /// 取消令牌源，用于取消正在进行的操作
        /// </summary>
        private CancellationTokenSource _cancellationTokenSource;

        /// <summary>
        /// 配置文件路径
        /// </summary>
        private string _configPath;

        /// <summary>
        /// 初始化frmAIv2窗体实例
        /// </summary>
        /// <remarks>构造函数，初始化窗体组件和AI助手。</remarks>
        public frmAIv2()
        {
            InitializeComponent();
            InitializeAIAssistant();
        }

        /// <summary>
        /// 初始化AI助手
        /// </summary>
        /// <remarks>创建AIExcelAssistant实例并处理初始化过程中可能出现的异常。</remarks>
        private void InitializeAIAssistant()
        {
            try
            {
                _aiAssistant = new AIExcelAssistant();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                MessageBox.Show($"初始化AI助手失败：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>在窗体加载时初始化配置路径，加载模型配置和规则文件到下拉框， 设置默认范围和选项，并初始化UI状态。</remarks>
        private void frmAIv2_Load(object sender, EventArgs e)
        {
            try
            {
                // 获取配置文件目录路径
                _configPath = Path.GetDirectoryName(ETConfig.GetConfigDirectory("dummy.txt", ".ai"));

                // 加载模型配置文件
                ETForm.LoadComboBox(comboModelConfig, _configPath, "*-json.ai");
                if (comboModelConfig.Items.Count > 0)
                    comboModelConfig.Text = "gemini-gemini-2-local-json.ai";

                // 加载规则文件（全局提示词）
                ETForm.LoadComboBox(comboGlobalPrompt, _configPath, "*.rule");
                if (comboGlobalPrompt.Items.Count > 0)
                    comboGlobalPrompt.Text = "无线通用.rule";

                // 设置默认Range
                SetDefaultRanges();

                // 设置默认选项
                radioRowMode.Checked = true;
                checkFillNullValues.Checked = false;

                // 初始化UI状态
                SetExecutingState(false);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                MessageBox.Show($"加载配置时发生错误：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 设置默认Range
        /// </summary>
        /// <remarks>设置Excel范围选择控件的默认值为当前活动工作表的指定区域。</remarks>
        private void SetDefaultRanges()
        {
            try
            {
                dynamic activeSheet = Globals.ThisAddIn.Application.ActiveSheet;

                // 设置默认区域
                ucDataSource.SelectedRange = activeSheet.Range["A2:A26"];      // 数据源
                ucTargetRange.SelectedRange = activeSheet.Range["D2:D26"];     // 回填区域
                ucPromptRange.SelectedRange = activeSheet.Range["D1:E1"];      // 提示词区域
                //ucFileSource.SelectedRange = activeSheet.Range["D2:D3"];      // 文件源
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                // 设置默认Range失败不影响主要功能
            }
        }

        /// <summary>
        /// 执行AI处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>处理用户执行请求，包括输入验证、进度初始化、AI调用和结果展示。 支持异步处理和取消操作。</remarks>
        private async void ButtonExecute_Click(object sender, EventArgs e)
        {
            try
            {
                // 更新UI状态
                SetExecutingState(true);

                // 验证输入
                ValidateInputs();

                // 清空日志和重置进度
                textBoxLog.Clear();
                progressBar.Value = 0;
                UpdateProgressLabel("准备开始...");

                // 创建取消令牌
                _cancellationTokenSource = new CancellationTokenSource();

                // 创建进度报告器
                Progress<ProcessingProgress> progress = new Progress<ProcessingProgress>(UpdateProgress);

                // 确定处理模式
                DataSourceMode mode = radioRowMode.Checked ? DataSourceMode.ByRow : DataSourceMode.ByColumn;
                // 统一使用ReadLocally模式，读取文件内容并嵌入到AI消息中
                FileProcessingMode fileMode = FileProcessingMode.ReadLocally;

                // 调用ETAIv2库进行处理
                AIResponse response = await _aiAssistant.ProcessExcelDataAsync(
                    ucDataSource.SelectedRange,
                    ucTargetRange.SelectedRange,
                    ucPromptRange.SelectedRange,
                    ucFileSource.SelectedRange,
                    comboModelConfig.Text,
                    comboGlobalPrompt.Text,
                    mode,
                    fileMode,
                    checkFillNullValues.Checked,
                    progress,
                    _cancellationTokenSource.Token);

                // 显示结果
                ShowResults(response);
            }
            catch (OperationCanceledException)
            {
                AppendLog("操作已取消。");
                UpdateProgressLabel("已取消");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                AppendLog($"错误：{ex.Message}");
                UpdateProgressLabel("处理失败");

                if (ex.InnerException != null)
                {
                    AppendLog($"详细错误：{ex.InnerException.Message}");
                }
            }
            finally
            {
                SetExecutingState(false);
            }
        }

        /// <summary>
        /// 停止处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>取消正在进行的AI处理操作。</remarks>
        private void ButtonStop_Click(object sender, EventArgs e)
        {
            try
            {
                _cancellationTokenSource?.Cancel();
                AppendLog("正在取消操作...");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                AppendLog($"取消操作时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 验证输入参数
        /// </summary>
        /// <remarks>验证用户输入的必要参数是否完整和有效。</remarks>
        /// <exception cref="ArgumentException">当必要参数缺失或无效时抛出</exception>
        private void ValidateInputs()
        {
            if (ucDataSource.SelectedRange == null)
                throw new ArgumentException("请选择数据源区域！");

            if (ucTargetRange.SelectedRange == null)
                throw new ArgumentException("请选择回填区域！");

            if (ucPromptRange.SelectedRange == null)
                throw new ArgumentException("请选择提示词区域！");

            if (string.IsNullOrEmpty(comboModelConfig.Text))
                throw new ArgumentException("请选择模型配置！");

            if (string.IsNullOrEmpty(comboGlobalPrompt.Text))
                throw new ArgumentException("请选择全局提示词文件！");
        }

        /// <summary>
        /// 更新进度显示
        /// </summary>
        /// <param name="progress">处理进度信息</param>
        /// <remarks>更新进度条和进度标签的显示，并在日志中记录进度信息。 处理跨线程调用的情况。</remarks>
        private void UpdateProgress(ProcessingProgress progress)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<ProcessingProgress>(UpdateProgress), progress);
                return;
            }

            progressBar.Value = Math.Min(progress.Percentage, 100);
            UpdateProgressLabel(progress.Message);
            AppendLog($"{DateTime.Now:HH:mm:ss} {progress.Message}");
        }

        /// <summary>
        /// 显示处理结果
        /// </summary>
        /// <param name="response">AI处理响应</param>
        /// <remarks>显示AI处理的最终结果，包括成功或失败信息、处理统计等。 处理跨线程调用的情况。</remarks>
        private void ShowResults(AIResponse response)
        {
            // 检查是否需要跨线程调用
            if (InvokeRequired)
            {
                Invoke(new Action<AIResponse>(ShowResults), response);
                return;
            }

            if (response.Success)
            {
                AppendLog($"处理完成！共处理 {response.Results?.Count ?? 0} 个数据组。");

                // 简化显示：不显示每个单独的数据组回填信息 只显示总体统计信息
                if (response.Results?.Any() == true)
                {
                    var totalValues = response.Results.Sum(r => r.Values?.Count ?? 0);
                    var batchCount = response.Metadata.ContainsKey("batchCount") ? (int)response.Metadata["batchCount"] : 1;

                    if (batchCount > 1)
                    {
                        AppendLog($"分 {batchCount} 个批次处理完成，共回填 {totalValues} 个值");
                    }
                    else
                    {
                        AppendLog($"共回填 {totalValues} 个值");
                    }

                    // 显示处理信息（取第一个结果的处理信息作为代表）
                    var firstResult = response.Results.FirstOrDefault();
                    if (firstResult != null && !string.IsNullOrEmpty(firstResult.ProcessingInfo))
                    {
                        AppendLog($"处理信息: {firstResult.ProcessingInfo}");
                    }
                }

                progressBar.Value = 100;
                UpdateProgressLabel("处理完成");
            }
            else
            {
                AppendLog($"处理失败：{response.ErrorMessage}");
                UpdateProgressLabel("处理失败");
            }
        }

        /// <summary>
        /// 设置执行状态
        /// </summary>
        /// <param name="executing">是否正在执行</param>
        /// <remarks>根据执行状态启用或禁用相关UI控件。 处理跨线程调用的情况。</remarks>
        private void SetExecutingState(bool executing)
        {
            // 检查是否需要跨线程调用
            if (InvokeRequired)
            {
                Invoke(new Action<bool>(SetExecutingState), executing);
                return;
            }

            buttonExecute.Enabled = !executing;
            buttonStop.Enabled = executing;

            // 禁用/启用配置控件
            comboModelConfig.Enabled = !executing;
            comboGlobalPrompt.Enabled = !executing;
            radioRowMode.Enabled = !executing;
            radioColumnMode.Enabled = !executing;
            checkFillNullValues.Enabled = !executing;
        }

        /// <summary>
        /// 添加日志
        /// </summary>
        /// <param name="message">要添加的日志消息</param>
        /// <remarks>向日志文本框中添加消息并滚动到最新内容。 处理跨线程调用的情况。</remarks>
        private void AppendLog(string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string>(AppendLog), message);
                return;
            }

            try
            {
                textBoxLog.AppendText($"{message}\r\n");
                textBoxLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
            }
        }

        /// <summary>
        /// 安全更新进度标签
        /// </summary>
        /// <param name="text">要显示的文本</param>
        /// <remarks>更新进度标签的显示文本。 处理跨线程调用的情况。</remarks>
        private void UpdateProgressLabel(string text)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string>(UpdateProgressLabel), text);
                return;
            }

            labelProgress.Text = text;
        }

        /// <summary>
        /// 打开配置目录
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>使用系统资源管理器打开配置文件所在目录。</remarks>
        private void ButtonOpenConfigDir_Click(object sender, EventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(_configPath) && Directory.Exists(_configPath))
                {
                    Process.Start("explorer.exe", _configPath);
                }
                else
                {
                    MessageBox.Show($"配置目录不存在：{_configPath}", "错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                MessageBox.Show($"打开配置目录失败：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        /// <param name="e">窗体关闭事件参数</param>
        /// <remarks>在窗体关闭时取消正在进行的操作并释放AI助手资源。</remarks>
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            try
            {
                // 取消正在进行的操作
                _cancellationTokenSource?.Cancel();

                // 释放AI助手资源
                _aiAssistant?.Dispose();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
            }

            base.OnFormClosing(e);
        }
    }
}