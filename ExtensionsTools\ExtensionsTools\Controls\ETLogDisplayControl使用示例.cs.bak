using System;
using System.Drawing;
using System.Windows.Forms;
using ExtensionsTools.ETExcel.Controls;

namespace ExtensionsTools.ETExcel.Controls.Examples
{
    /// <summary>
    /// ETLogDisplayControl 使用示例
    /// 演示如何使用 ETLogDisplayControl 进行日志显示
    /// </summary>
    public partial class ETLogDisplayControlExample : Form
    {
        private ETLogDisplayControl logControl;
        private Button btnInfo;
        private Button btnWarning;
        private Button btnError;
        private Button btnDebug;
        private Button btnClear;
        private Button btnSave;
        private ComboBox cmbLogLevel;
        private CheckBox chkTimestamp;
        private CheckBox chkLogLevel;
        private CheckBox chkAutoScroll;

        public ETLogDisplayControlExample()
        {
            InitializeComponent();
            SetupLogControl();
        }

        /// <summary>
        /// 设置日志显示控件
        /// </summary>
        private void SetupLogControl()
        {
            // 创建控件实例
            logControl = new ETLogDisplayControl();
            logControl.Location = new Point(12, 80);
            logControl.Size = new Size(560, 300);
            logControl.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;

            // 设置控件属性
            logControl.ShowTimestamp = true;
            logControl.ShowLogLevel = true;
            logControl.AutoScrollToBottom = true;
            logControl.CurrentLogLevel = ETLogDisplayControl.LogLevel.Debug;
            logControl.MaxLogLines = 500;
            logControl.WordWrap = true; // 启用自动换行，解决长文本不换行问题

            // 自定义样式
            logControl.LogFont = new Font("Consolas", 9F);
            logControl.LogBackColor = Color.Black;
            logControl.LogForeColor = Color.LimeGreen;

            // 添加到窗体
            this.Controls.Add(logControl);

            // 设置初始化消息
            logControl.SetInitialMessage(@"## ETLogDisplayControl 使用示例
## 功能演示：多级别日志记录和显示
## 操作说明：
	- 点击不同按钮测试各级别日志
	- 调整过滤级别查看效果
	- 可自定义显示格式和样式

----------------------------------------", true);
        }

        /// <summary>
        /// 信息日志按钮点击
        /// </summary>
        private void BtnInfo_Click(object sender, EventArgs e)
        {
            logControl.WriteInfo("这是一条信息级别的日志消息");
            logControl.WriteInfo($"当前时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");

            // 测试长文本换行功能
            logControl.WriteInfo("2025-07-31 14:34:23 [INFO] 方向角/下倾角提取器已就绪。并提供方向角和下倾角提取功能。并提供方向角和下倾角提取功能。只有1小区的数据无法识别，该功能目前有Bug，不是很准确的方法。1. 选择包含原始数据的来源");
        }

        /// <summary>
        /// 警告日志按钮点击
        /// </summary>
        private void BtnWarning_Click(object sender, EventArgs e)
        {
            logControl.WriteWarning("这是一条警告级别的日志消息");
            logControl.WriteWarning("请注意：某些操作可能存在风险");
        }

        /// <summary>
        /// 错误日志按钮点击
        /// </summary>
        private void BtnError_Click(object sender, EventArgs e)
        {
            try
            {
                // 模拟一个异常
                throw new InvalidOperationException("这是一个模拟的异常");
            }
            catch (Exception ex)
            {
                logControl.WriteError("发生了一个错误", ex);
            }

            logControl.WriteError("这是一条错误级别的日志消息（无异常）");
        }

        /// <summary>
        /// 调试日志按钮点击
        /// </summary>
        private void BtnDebug_Click(object sender, EventArgs e)
        {
            logControl.WriteDebug("这是一条调试级别的日志消息");
            logControl.WriteDebug($"内存使用情况：{GC.GetTotalMemory(false) / 1024 / 1024} MB");
        }

        /// <summary>
        /// 清空日志按钮点击
        /// </summary>
        private void BtnClear_Click(object sender, EventArgs e)
        {
            logControl.Clear();
            logControl.WriteInfo("日志已清空");
        }

        /// <summary>
        /// 保存日志按钮点击
        /// </summary>
        private void BtnSave_Click(object sender, EventArgs e)
        {
            using (var saveDialog = new SaveFileDialog())
            {
                saveDialog.Filter = "文本文件|*.txt|所有文件|*.*";
                saveDialog.DefaultExt = "txt";
                saveDialog.FileName = $"日志_{DateTime.Now:yyyyMMdd_HHmmss}.txt";

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    if (logControl.SaveLogToFile(saveDialog.FileName))
                    {
                        MessageBox.Show($"日志已保存到：{saveDialog.FileName}", "保存成功", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("保存日志失败", "错误", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        /// <summary>
        /// 日志级别选择变化
        /// </summary>
        private void CmbLogLevel_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbLogLevel.SelectedItem != null)
            {
                var selectedLevel = (ETLogDisplayControl.LogLevel)cmbLogLevel.SelectedItem;
                logControl.CurrentLogLevel = selectedLevel;
                logControl.WriteInfo($"日志过滤级别已设置为：{selectedLevel}");
            }
        }

        /// <summary>
        /// 时间戳显示选项变化
        /// </summary>
        private void ChkTimestamp_CheckedChanged(object sender, EventArgs e)
        {
            logControl.ShowTimestamp = chkTimestamp.Checked;
            logControl.WriteInfo($"时间戳显示：{(chkTimestamp.Checked ? "开启" : "关闭")}");
        }

        /// <summary>
        /// 日志级别显示选项变化
        /// </summary>
        private void ChkLogLevel_CheckedChanged(object sender, EventArgs e)
        {
            logControl.ShowLogLevel = chkLogLevel.Checked;
            logControl.WriteInfo($"日志级别显示：{(chkLogLevel.Checked ? "开启" : "关闭")}");
        }

        /// <summary>
        /// 自动滚动选项变化
        /// </summary>
        private void ChkAutoScroll_CheckedChanged(object sender, EventArgs e)
        {
            logControl.AutoScrollToBottom = chkAutoScroll.Checked;
            logControl.WriteInfo($"自动滚动：{(chkAutoScroll.Checked ? "开启" : "关闭")}");
        }

        /// <summary>
        /// 初始化窗体控件
        /// </summary>
        private void InitializeComponent()
        {
            this.btnInfo = new Button();
            this.btnWarning = new Button();
            this.btnError = new Button();
            this.btnDebug = new Button();
            this.btnClear = new Button();
            this.btnSave = new Button();
            this.cmbLogLevel = new ComboBox();
            this.chkTimestamp = new CheckBox();
            this.chkLogLevel = new CheckBox();
            this.chkAutoScroll = new CheckBox();
            this.SuspendLayout();

            // 按钮布局
            int buttonY = 12;
            int buttonWidth = 80;
            int buttonHeight = 25;
            int buttonSpacing = 90;

            // btnInfo
            this.btnInfo.Location = new Point(12, buttonY);
            this.btnInfo.Size = new Size(buttonWidth, buttonHeight);
            this.btnInfo.Text = "信息日志";
            this.btnInfo.UseVisualStyleBackColor = true;
            this.btnInfo.Click += new EventHandler(this.BtnInfo_Click);

            // btnWarning
            this.btnWarning.Location = new Point(12 + buttonSpacing, buttonY);
            this.btnWarning.Size = new Size(buttonWidth, buttonHeight);
            this.btnWarning.Text = "警告日志";
            this.btnWarning.UseVisualStyleBackColor = true;
            this.btnWarning.Click += new EventHandler(this.BtnWarning_Click);

            // btnError
            this.btnError.Location = new Point(12 + buttonSpacing * 2, buttonY);
            this.btnError.Size = new Size(buttonWidth, buttonHeight);
            this.btnError.Text = "错误日志";
            this.btnError.UseVisualStyleBackColor = true;
            this.btnError.Click += new EventHandler(this.BtnError_Click);

            // btnDebug
            this.btnDebug.Location = new Point(12 + buttonSpacing * 3, buttonY);
            this.btnDebug.Size = new Size(buttonWidth, buttonHeight);
            this.btnDebug.Text = "调试日志";
            this.btnDebug.UseVisualStyleBackColor = true;
            this.btnDebug.Click += new EventHandler(this.BtnDebug_Click);

            // btnClear
            this.btnClear.Location = new Point(12 + buttonSpacing * 4, buttonY);
            this.btnClear.Size = new Size(buttonWidth, buttonHeight);
            this.btnClear.Text = "清空日志";
            this.btnClear.UseVisualStyleBackColor = true;
            this.btnClear.Click += new EventHandler(this.BtnClear_Click);

            // btnSave
            this.btnSave.Location = new Point(12 + buttonSpacing * 5, buttonY);
            this.btnSave.Size = new Size(buttonWidth, buttonHeight);
            this.btnSave.Text = "保存日志";
            this.btnSave.UseVisualStyleBackColor = true;
            this.btnSave.Click += new EventHandler(this.BtnSave_Click);

            // 第二行控件
            int secondRowY = buttonY + buttonHeight + 10;

            // cmbLogLevel
            this.cmbLogLevel.Location = new Point(12, secondRowY);
            this.cmbLogLevel.Size = new Size(100, 21);
            this.cmbLogLevel.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbLogLevel.Items.AddRange(Enum.GetValues(typeof(ETLogDisplayControl.LogLevel)));
            this.cmbLogLevel.SelectedItem = ETLogDisplayControl.LogLevel.Debug;
            this.cmbLogLevel.SelectedIndexChanged += new EventHandler(this.CmbLogLevel_SelectedIndexChanged);

            // chkTimestamp
            this.chkTimestamp.Location = new Point(130, secondRowY);
            this.chkTimestamp.Size = new Size(80, 21);
            this.chkTimestamp.Text = "显示时间";
            this.chkTimestamp.Checked = true;
            this.chkTimestamp.CheckedChanged += new EventHandler(this.ChkTimestamp_CheckedChanged);

            // chkLogLevel
            this.chkLogLevel.Location = new Point(220, secondRowY);
            this.chkLogLevel.Size = new Size(80, 21);
            this.chkLogLevel.Text = "显示级别";
            this.chkLogLevel.Checked = true;
            this.chkLogLevel.CheckedChanged += new EventHandler(this.ChkLogLevel_CheckedChanged);

            // chkAutoScroll
            this.chkAutoScroll.Location = new Point(310, secondRowY);
            this.chkAutoScroll.Size = new Size(80, 21);
            this.chkAutoScroll.Text = "自动滚动";
            this.chkAutoScroll.Checked = true;
            this.chkAutoScroll.CheckedChanged += new EventHandler(this.ChkAutoScroll_CheckedChanged);

            // Form
            this.ClientSize = new Size(584, 391);
            this.Controls.Add(this.btnInfo);
            this.Controls.Add(this.btnWarning);
            this.Controls.Add(this.btnError);
            this.Controls.Add(this.btnDebug);
            this.Controls.Add(this.btnClear);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.cmbLogLevel);
            this.Controls.Add(this.chkTimestamp);
            this.Controls.Add(this.chkLogLevel);
            this.Controls.Add(this.chkAutoScroll);
            this.Text = "ETLogDisplayControl 使用示例";
            this.ResumeLayout(false);
        }
    }
}
