{"extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.121\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.121\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "protection": {"macs": {"browser": {"show_home_button": "F73A3B4518F0AB3C9007D64A3168718E4837FAF0910D0EB57055BEC71A6894AD"}, "default_search_provider_data": {"template_url_data": "6E1E8153DECF8F10E3F932C9355276B07EA49B0E5F9430D27296E4CD202C7A93"}, "edge": {"services": {"account_id": "0EDA34A5030280AA5FEC0184AF52C947E8DB5FF1EFBDA5AE2B1449B512FDF65E", "last_username": "4E18D1774716A7ED88D3134595884A4437D864A097008C68FF39CA6F9E696D69"}}, "enterprise_signin": {"policy_recovery_token": "BE11D720E086CF2504680A58A8D8E926CB17828EFECDFD866111ECD8549FF9FE"}, "extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": "248B6F8A4F5AADD435F71CBA1C9F9198B381E770D5EE857BAF8533494265D9F1", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "38D09B464A95DE46452E0899ABB315656B6386D843104FE6872E3489A6784DE3"}, "ui": {"developer_mode": "74DE3BAF7E7EC12EFF6569C7E81F7DC7F9C537B0F27885FF069C029E22671427"}}, "google": {"services": {"last_signed_in_username": "B42EFD9BB5E1C13549E8A34C2C92AC3BD143D615BC8CA23EB92A6DF5DBEAFD93"}}, "homepage": "8F2184FB3D49191C90D85CCE70333D024F0CED305C3AE4F9E60E5C36449914E5", "homepage_is_newtabpage": "48E08E2D1CD2205B9221CBEB4B213C6FABFB89415C621F1FA8B1F553CC4884BC", "media": {"cdm": {"origin_data": "59006E8BE23AE36209BB71B23AD164E2A9CE96405B9DE0C92B3E762C29E0127E"}, "storage_id_salt": "866D0D77330B15AC3A2FDF3B90D2C6961DBE4BACFE2063E17ED6C570B0AFDD56"}, "pinned_tabs": "ACF1B1CC70F296AFE81756702F70075F657C002454A5F4BA269C36858AEFEF3B", "prefs": {"preference_reset_time": "5010136C972EFDB53126DD92189317DE51F2F1FEE2A6F120B76AE432B69B0600"}, "safebrowsing": {"incidents_sent": "C1BE11ADC75AB1EF9F302F63E936E6B3F909F722D98985A17FD559776E059481"}, "search_provider_overrides": "A0AB872D22B87C3D13034BB30B7ABDEA1B413B775C8D283CC0F8921BF73C2150", "session": {"restore_on_startup": "F8E39BEF3A26F0DD2361E2F69448AB3166AE8DFE87FC4B0F71C1E67A8B7DB4EF", "startup_urls": "F43013954A3BD9C91FBE1254AA357268185502F88489A12586F4875C0F00A13C"}}, "super_mac": "9F6D87F677F64DD749021AAA2B595D0B34B415DD92E9B1523E7DB556636C4CD1"}}