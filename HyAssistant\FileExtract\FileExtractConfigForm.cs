/**
 * 文件名：FileExtractConfigForm.cs
 * 窗体名称：文件提取配置窗体
 * 
 * 功能说明：
 * 该窗体用于管理文件提取任务的配置信息。用户可以创建、编辑、删除不同的文件提取任务，
 * 每个任务可以设置源路径、目标路径、文件匹配模式、提取时间间隔以及文件时间限制等条件。
 * 
 * 实现细节：
 * 1. 配置存储：使用INI文件（fileExtract.ini）存储所有配置信息
 * 2. 配置格式：每个提取任务使用唯一UUID标识，格式为"FileExtract-{UUID}"
 * 3. 主要功能：
 *    - 加载现有配置：读取INI文件中的所有任务配置并显示在列表中
 *    - 添加新配置：创建新的任务配置并分配唯一UUID
 *    - 删除配置：从INI文件中移除指定的任务配置
 *    - 保存配置：将修改后的任务配置保存到INI文件
 *    - 配置项设置：包括任务名称、源路径（支持多路径）、目标路径、文件模式（支持多模式）、
 *      执行间隔、文件创建时间和修改时间限制、启用状态等
 * 4. 兼容性处理：支持旧格式配置的自动升级，将非UUID格式的配置更新为新格式
 * 5. 数据结构：
 *    - _sectionUuids：字典，存储显示名到UUID的映射
 *    - _sectionControls：字典，存储UUID到对应控件的映射
 * 
 * 用户交互流程：
 * 1. 用户打开窗体，系统自动加载所有已有配置并显示在左侧列表中
 * 2. 用户可以选择已有配置进行查看或编辑，也可以添加新配置或删除现有配置
 * 3. 当选择配置后，右侧表单显示该配置的详细信息供编辑
 * 4. 编辑完成后，用户点击保存按钮将更改写入INI文件
 */

using ET;
using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;

namespace HyAssistant
{
    /// <summary>
    /// 文件提取配置窗体类，用于管理文件提取任务的配置信息
    /// </summary>
    /// <remarks>
    /// 该类提供用户界面，允许用户创建、编辑、删除和保存文件提取任务的配置。
    /// 每个配置包含源路径、目标路径、文件匹配模式、执行间隔和时间限制等参数。
    /// 所有配置信息存储在INI文件中，使用UUID作为唯一标识符。
    /// </remarks>
    public partial class FileExtractConfigForm : Form
    {
        /// <summary>
        /// INI配置文件的完整路径
        /// </summary>
        readonly string _iniFilePath;

        /// <summary>
        /// INI文件操作对象，用于读写配置
        /// </summary>
        readonly ETIniFile _iniFile;

        /// <summary>
        /// 存储显示名称到UUID的映射字典
        /// </summary>
        readonly Dictionary<string, string> _sectionUuids = new Dictionary<string, string>();

        /// <summary>
        /// 存储UUID到对应控件的映射字典
        /// </summary>
        readonly Dictionary<string, Control> _sectionControls = new Dictionary<string, Control>();

        /// <summary>
        /// 配置组名前缀常量，用于标识文件提取配置节
        /// </summary>
        const string SECTION_PREFIX = "FileExtract-";

        /// <summary>
        /// 标记是否正在更新列表项，防止触发不必要的重新加载
        /// </summary>
        bool _isUpdatingItem = false;

        /// <summary>
        /// 初始化文件提取配置窗体
        /// </summary>
        /// <remarks>
        /// 构造函数初始化窗体组件，设置INI文件路径，并加载现有配置
        /// </remarks>
        public FileExtractConfigForm()
        {
            InitializeComponent();
            _iniFilePath = ETConfig.GetConfigDirectory("fileExtract.ini");

            // 如果配置文件不存在，创建新的INI文件对象
            if (!File.Exists(_iniFilePath))
            {
                _iniFile = new ETIniFile(_iniFilePath);
            }
            else
            {
                _iniFile = new ETIniFile(_iniFilePath);
            }

            LoadSections();
        }

        /// <summary>
        /// 加载INI文件中的所有配置节，并显示在列表框中
        /// </summary>
        /// <remarks>
        /// 该方法执行以下操作：
        /// 1. 清空列表框和相关字典
        /// 2. 检查并升级旧格式的配置为UUID格式
        /// 3. 处理重复的显示名称
        /// 4. 将所有配置添加到列表框中
        /// </remarks>
        void LoadSections()
        {
            try
            {
                listBoxSections.Items.Clear();
                _sectionUuids.Clear();
                _sectionControls.Clear();

                // 如果配置文件不存在，直接返回
                if (!File.Exists(_iniFilePath))
                {
                    return;
                }

                // 获取所有节名
                List<string> sectionNames = _iniFile.GetSectionNames();

                // 检查是否有旧格式的配置需要更新为UUID格式
                bool needUpdate = false;
                foreach (string sectionName in sectionNames)
                {
                    if (!sectionName.StartsWith(SECTION_PREFIX, StringComparison.OrdinalIgnoreCase)
                        && !string.IsNullOrEmpty(_iniFile.GetValue(sectionName, "name")))
                    {
                        try
                        {
                            // 这是旧格式的配置节，需要更新
                            string newSectionName = $"{SECTION_PREFIX}{Guid.NewGuid().ToString("N")}";

                            // 复制所有键值对
                            Dictionary<string, string> keyValues = _iniFile.GetSection(sectionName);
                            foreach (KeyValuePair<string, string> kv in keyValues)
                            {
                                _iniFile.SetValue(newSectionName, kv.Key, kv.Value);
                            }

                            // 删除旧节
                            _iniFile.DeleteSection(sectionName);
                            needUpdate = true;
                        }
                        catch (Exception ex)
                        {
                            // 记录错误但继续处理其他节
                            MessageBox.Show($"更新配置格式时出错：{ex.Message}", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                }

                // 如果有更新，保存文件
                if (needUpdate)
                {
                    _iniFile.IniWriteFile();
                    sectionNames = _iniFile.GetSectionNames(); // 重新获取节名
                }

                // 用于检测重复的显示名
                HashSet<string> usedDisplayNames = new HashSet<string>();

                foreach (string sectionName in sectionNames)
                {
                    if (sectionName.StartsWith(SECTION_PREFIX, StringComparison.OrdinalIgnoreCase))
                    {
                        string displayName = _iniFile.GetValue(sectionName, "name", sectionName);

                        // 处理空白显示名
                        if (string.IsNullOrWhiteSpace(displayName))
                        {
                            displayName = sectionName;
                        }

                        // 处理重复的显示名
                        string originalDisplayName = displayName;
                        int counter = 1;
                        while (usedDisplayNames.Contains(displayName))
                        {
                            displayName = $"{originalDisplayName} ({counter})";
                            counter++;
                        }

                        usedDisplayNames.Add(displayName);
                        _sectionUuids[displayName] = sectionName;
                        listBoxSections.Items.Add(displayName);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载配置列表时出错：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 处理列表框选择项变更事件
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 当用户选择列表框中的配置项时，加载并显示该配置的详细信息
        /// </remarks>
        void listBoxSections_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 如果正在更新列表项，不执行加载
            if (_isUpdatingItem) return;

            try
            {
                if (listBoxSections.SelectedItem == null) return;

                string selectedDisplayName = listBoxSections.SelectedItem.ToString();

                // 检查选择的项是否有效
                if (string.IsNullOrEmpty(selectedDisplayName))
                {
                    return;
                }

                // 安全地从字典获取UUID，避免KeyNotFoundException
                if (!_sectionUuids.TryGetValue(selectedDisplayName, out string selectedUuid))
                {
                    MessageBox.Show($"找不到配置 '{selectedDisplayName}' 的对应数据，可能配置已损坏", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                LoadSectionData(selectedUuid);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择配置时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                ClearForm(); // 清空表单以避免显示不一致的数据
            }
        }

        /// <summary>
        /// 加载指定配置节的数据到表单控件
        /// </summary>
        /// <param name="sectionName">配置节名称（UUID）</param>
        /// <remarks>
        /// 从INI文件读取指定配置节的所有参数值，并设置到对应的表单控件中
        /// </remarks>
        void LoadSectionData(string sectionName)
        {
            try
            {
                textBoxName.Text = _iniFile.GetValue(sectionName, "name", string.Empty);
                textBoxSourcePaths.Text = _iniFile.GetValue(sectionName, "SourcePaths", string.Empty).Replace("|", Environment.NewLine);
                textBoxTargetPath.Text = _iniFile.GetValue(sectionName, "TargetPath", string.Empty);
                textBoxFilePattern.Text = _iniFile.GetValue(sectionName, "FilePattern", "*.*").Replace("|", Environment.NewLine);

                // 添加异常处理
                if (int.TryParse(_iniFile.GetValue(sectionName, "IntervalInMinutes", "60"), out int interval))
                {
                    numericUpDownInterval.Value = Math.Min(Math.Max(interval, numericUpDownInterval.Minimum), numericUpDownInterval.Maximum);
                }
                else
                {
                    numericUpDownInterval.Value = 60;
                }

                // 安全解析日期，使用TryParse
                if (DateTime.TryParse(_iniFile.GetValue(sectionName, "CreationTimeLimit", DateTime.MinValue.ToString()), out DateTime creationTime))
                {
                    dateTimePickerCreationTime.Value = creationTime;
                }
                else
                {
                    dateTimePickerCreationTime.Value = DateTime.Now;
                }

                if (DateTime.TryParse(_iniFile.GetValue(sectionName, "ModificationTimeLimit", DateTime.MinValue.ToString()), out DateTime modificationTime))
                {
                    dateTimePickerModificationTime.Value = modificationTime;
                }
                else
                {
                    dateTimePickerModificationTime.Value = DateTime.Now;
                }

                checkBoxEnable.Checked = _iniFile.GetValue(sectionName, "enable", "0") == "1";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载配置数据时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                ClearForm();
            }
        }

        /// <summary>
        /// 处理保存按钮点击事件
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 验证表单数据，保存当前配置到INI文件，并更新列表显示
        /// </remarks>
        void buttonSave_Click(object sender, EventArgs e)
        {
            if (listBoxSections.SelectedItem == null) return;

            try
            {
                // 验证必填字段
                if (string.IsNullOrWhiteSpace(textBoxName.Text))
                {
                    MessageBox.Show("请输入配置名称", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    textBoxName.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(textBoxSourcePaths.Text))
                {
                    MessageBox.Show("请至少输入一个源路径", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    textBoxSourcePaths.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(textBoxTargetPath.Text))
                {
                    MessageBox.Show("请输入目标路径", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    textBoxTargetPath.Focus();
                    return;
                }

                string selectedDisplayName = listBoxSections.SelectedItem.ToString();

                // 检查选择的项是否有效
                if (string.IsNullOrEmpty(selectedDisplayName))
                {
                    MessageBox.Show("无效的配置名称", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 安全地从字典获取UUID
                if (!_sectionUuids.TryGetValue(selectedDisplayName, out string selectedUuid))
                {
                    MessageBox.Show($"找不到配置 '{selectedDisplayName}' 的对应数据，可能配置已损坏", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                string newDisplayName = textBoxName.Text.Trim();

                // 检查名称是否重复
                if (newDisplayName != selectedDisplayName && _sectionUuids.ContainsKey(newDisplayName))
                {
                    MessageBox.Show("配置名称已存在，请使用其他名称", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 先保存数据
                SaveSectionData(selectedUuid);

                // 更新显示名称，设置标志防止触发不必要的加载
                if (newDisplayName != selectedDisplayName)
                {
                    try
                    {
                        _isUpdatingItem = true;
                        _sectionUuids.Remove(selectedDisplayName);
                        _sectionUuids[newDisplayName] = selectedUuid;
                        int selectedIndex = listBoxSections.SelectedIndex;
                        listBoxSections.Items[selectedIndex] = newDisplayName;
                    }
                    finally
                    {
                        _isUpdatingItem = false;
                    }
                }

                // 确保配置文件目录存在
                string configDir = Path.GetDirectoryName(_iniFilePath);
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }

                _iniFile.IniWriteFile();
                MessageBox.Show("配置已保存", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存配置时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 将表单数据保存到指定的配置节
        /// </summary>
        /// <param name="sectionName">配置节名称（UUID）</param>
        /// <remarks>
        /// 从表单控件获取所有参数值，并保存到INI文件的指定配置节中
        /// </remarks>
        /// <exception cref="Exception">保存配置数据失败时抛出异常</exception>
        void SaveSectionData(string sectionName)
        {
            try
            {
                _iniFile.SetValue(sectionName, "name", textBoxName.Text.Trim());
                _iniFile.SetValue(sectionName, "SourcePaths", textBoxSourcePaths.Text.Replace(Environment.NewLine, "|").TrimEnd('|'));
                _iniFile.SetValue(sectionName, "TargetPath", textBoxTargetPath.Text.Trim());
                _iniFile.SetValue(sectionName, "FilePattern", textBoxFilePattern.Text.Replace(Environment.NewLine, "|").TrimEnd('|'));
                _iniFile.SetValue(sectionName, "IntervalInMinutes", numericUpDownInterval.Value.ToString());

                // 使用特定格式保存日期，以确保跨区域一致性
                _iniFile.SetValue(sectionName, "CreationTimeLimit", dateTimePickerCreationTime.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                _iniFile.SetValue(sectionName, "ModificationTimeLimit", dateTimePickerModificationTime.Value.ToString("yyyy-MM-dd HH:mm:ss"));

                _iniFile.SetValue(sectionName, "enable", checkBoxEnable.Checked ? "1" : "0");
            }
            catch (Exception ex)
            {
                throw new Exception($"保存配置数据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 处理添加按钮点击事件
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 创建新的配置节，生成唯一UUID，初始化默认值，并添加到列表中
        /// </remarks>
        void buttonAdd_Click(object sender, EventArgs e)
        {
            try
            {
                string newDisplayName = $"新配置{_sectionUuids.Count + 1}";

                // 检查名称是否重复
                while (_sectionUuids.ContainsKey(newDisplayName))
                {
                    newDisplayName = $"新配置{_sectionUuids.Count + 1}";
                }

                string newUuid = $"{SECTION_PREFIX}{Guid.NewGuid().ToString("N")}";
                _sectionUuids[newDisplayName] = newUuid;

                // 保存配置数据
                _iniFile.SetValue(newUuid, "name", newDisplayName);
                _iniFile.SetValue(newUuid, "enable", "1");
                _iniFile.SetValue(newUuid, "IntervalInMinutes", "60");

                // 使用统一的日期格式
                _iniFile.SetValue(newUuid, "CreationTimeLimit", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                _iniFile.SetValue(newUuid, "ModificationTimeLimit", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

                _iniFile.SetValue(newUuid, "FilePattern", "*.*");

                // 确保配置文件目录存在
                string configDir = Path.GetDirectoryName(_iniFilePath);
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }

                _iniFile.IniWriteFile();

                // 添加到列表并选中，使用_isUpdatingItem标志防止触发不必要的重新加载
                try
                {
                    _isUpdatingItem = true;
                    listBoxSections.Items.Add(newDisplayName);
                    listBoxSections.SelectedItem = newDisplayName;
                }
                finally
                {
                    _isUpdatingItem = false;
                }

                // 手动加载配置数据
                LoadSectionData(newUuid);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加新配置时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 处理删除按钮点击事件
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 删除选定的配置节，从INI文件和列表中移除，并更新UI显示
        /// </remarks>
        void buttonDelete_Click(object sender, EventArgs e)
        {
            if (listBoxSections.SelectedItem == null) return;

            string selectedDisplayName = listBoxSections.SelectedItem.ToString();

            // 检查选择的项是否有效
            if (string.IsNullOrEmpty(selectedDisplayName))
            {
                return;
            }

            // 安全地从字典获取UUID
            if (!_sectionUuids.TryGetValue(selectedDisplayName, out string selectedUuid))
            {
                MessageBox.Show($"找不到配置 '{selectedDisplayName}' 的对应数据，可能配置已损坏", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (MessageBox.Show($"确认要删除 \"{selectedDisplayName}\" 配置吗？", "确认删除",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
            {
                return;
            }

            try
            {
                _iniFile.DeleteSection(selectedUuid);
                _sectionUuids.Remove(selectedDisplayName);
                int selectedIndex = listBoxSections.SelectedIndex;

                try
                {
                    _isUpdatingItem = true;
                    listBoxSections.Items.Remove(selectedDisplayName);

                    // 如果删除后还有项目，选择下一个项目
                    if (listBoxSections.Items.Count > 0)
                    {
                        int newIndex = Math.Min(selectedIndex, listBoxSections.Items.Count - 1);
                        listBoxSections.SelectedIndex = newIndex;
                    }
                    else
                    {
                        ClearForm();
                    }
                }
                finally
                {
                    _isUpdatingItem = false;
                }

                // 如果选择了新项目，手动加载数据
                if (listBoxSections.SelectedItem != null)
                {
                    string newSelectedName = listBoxSections.SelectedItem.ToString();
                    if (_sectionUuids.TryGetValue(newSelectedName, out string newSelectedUuid))
                    {
                        LoadSectionData(newSelectedUuid);
                    }
                }

                _iniFile.IniWriteFile();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除配置时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空表单控件内容
        /// </summary>
        /// <remarks>
        /// 重置所有表单控件为默认值或空值
        /// </remarks>
        void ClearForm()
        {
            textBoxName.Clear();
            textBoxSourcePaths.Clear();
            textBoxTargetPath.Clear();
            textBoxFilePattern.Clear();
            numericUpDownInterval.Value = 60;
            dateTimePickerCreationTime.Value = DateTime.Now;
            dateTimePickerModificationTime.Value = DateTime.Now;
            checkBoxEnable.Checked = false;
        }

        /// <summary>
        /// 处理源路径浏览按钮点击事件
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 打开文件夹浏览对话框，选择源路径并添加到源路径文本框
        /// </remarks>
        void buttonBrowseSource_Click(object sender, EventArgs e)
        {
            using (FolderBrowserDialog dialog = new FolderBrowserDialog())
            {
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    if (string.IsNullOrEmpty(textBoxSourcePaths.Text))
                    {
                        textBoxSourcePaths.Text = dialog.SelectedPath;
                    }
                    else
                    {
                        textBoxSourcePaths.Text += $"{Environment.NewLine}{dialog.SelectedPath}";
                    }
                }
            }
        }

        /// <summary>
        /// 处理目标路径浏览按钮点击事件
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 打开文件夹浏览对话框，选择目标路径并设置到目标路径文本框
        /// </remarks>
        void buttonBrowseTarget_Click(object sender, EventArgs e)
        {
            using (FolderBrowserDialog dialog = new FolderBrowserDialog())
            {
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    textBoxTargetPath.Text = dialog.SelectedPath;
                }
            }
        }
    }
}