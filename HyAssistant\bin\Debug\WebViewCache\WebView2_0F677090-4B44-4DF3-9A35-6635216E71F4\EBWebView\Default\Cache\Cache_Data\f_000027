var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var _bean;
(function (_bean) {
    _bean.dataRootUrl = _context ? _context.dataRootUrl : '';
    _bean.EVENT_OBJ = 'EVENT_OBJ';
    _bean.EVENT_ACTION_START = 'EVENT_ACTION_START';
    _bean.EVENT_ACTION_END = 'EVENT_ACTION_END';
    _bean.EVENT_OBJ_ADDING = 'EVENT_OBJ_ADDING';
    _bean.EVENT_OBJ_UPDATEING = 'EVENT_OBJ_UPDATEING';
    _bean.defaultObjFormOpenMode = 'modal';
    _bean.defaultGridPageSize = 20;
    _bean.defaultComboPageSize = 10;
    _bean.defaultPostLoader = undefined;
    _bean.autoLastInput = false;
    _bean.autoInputDataFns = [];
    _bean.formValidateFns = [];
    _bean.fieldsNoTemplate = ['NAME', 'CODE', 'METACATEGORY', 'CREATOR', 'CREATOR_ID', 'MODIFIER', 'MODIFIER_ID', 'CREATE_DATE', 'MODIFY_DATE', 'TIME_STAMP', 'SHARDING_ID', 'VERSION', 'SPEC_ID', 'shape', 'LONGITUDE', 'LATITUDE'];
    _bean.templateHasConnview = ['OBD', 'ODF', 'GJ', 'GF', 'GB', 'ZHX', 'IDF', 'GT', 'OCABLE', 'OCABLESECTION'];
    _bean.simpleGridActionParams = { type: 'window', width: 800, height: 600 };
    _bean.simpleFormActionParams = { type: 'window', width: 800, height: 600 };
    _sui.defaultUploadOptions = function () {
        return {
            headers: buildPostHeaders()
        };
    };
    if (window['_context']) {
        var pb = void 0;
        if (_context.parentContext)
            pb = _context.parentContext.window._bean;
        else if (window !== _context.window)
            pb = _context.window._bean;
        if (pb) {
            _bean.postBeforeFn = pb.postBeforeFn;
            _bean.autoLastInput = pb.autoLastInput;
            _bean.autoInputData = pb.autoInputData;
            _bean.autoInputDataFns = pb.autoInputDataFns.slice();
            _bean.formValidateFns = pb.formValidateFns.slice();
            _bean.fieldsNoTemplate = pb.fieldsNoTemplate;
        }
    }
    _bean.strings = {
        keywordSearch: '关键字搜索...', objCreate: '新增', objModify: '属性', objView: '查看', objRemove: '删除', objExp: '导出', objExpAll: '导出全部', objExpCurrent: '导出当前页', objImp: '导入', objLocate: '地图定位',
        templateCreatePrompt: '请输入模板名称（相同名称的模板将被覆盖）', inputCopyFromLast: '从最近查看的对象复制属性', saveAsTemplate: '将当前输入保存为模板'
    };
    if (_context && !_context.bean) {
        _context.events.on('EVENT_SHAPE_UPDATED', function (e, params) {
            if (!_context.map || _context.map !== params.map)
                return;
            if (Array.isArray(params.data)) {
                var bytype_1 = {};
                var batchList = params.data.map(function (d) {
                    var objtype = d.objectType;
                    var ls = bytype_1[objtype];
                    if (!ls)
                        ls = bytype_1[objtype] = [];
                    ls.push(d);
                    return d;
                });
                if (batchList.length) {
                    if (_context.map)
                        _context.map.setWaiting();
                    _bean.action('pipe.shape', 'batchUpdateShape', { batchList: batchList }).then(function () {
                        if (_context.map) {
                            _context.map.setWaiting(false);
                            _context.map.refresh();
                        }
                        if (_context.listener && _context.listener.afterUpdateShape) {
                            for (var objectType in bytype_1) {
                                var fn = _context.listener.afterUpdateShape[objectType];
                                if (!fn && typeof _context.listener.afterUpdateShape === 'function')
                                    fn = _context.listener.afterUpdateShape;
                                if (fn)
                                    fn({ data: bytype_1[objectType] }, objectType);
                            }
                        }
                    }, function () {
                        if (_context.map)
                            _context.map.setWaiting(false);
                    });
                }
                return;
            }
            var obj = params.data, crs = params.crs;
            if (obj && obj.objectType && obj.id) {
                _bean.getGeomField(obj.objectType).then(function (geomField) {
                    var ps = { id: obj.id, crs: crs };
                    ps[geomField.name] = params.shape;
                    if (_context.map)
                        _context.map.setWaiting();
                    _bean.update(obj.objectType, ps).then(function () {
                        if (_context.map)
                            _context.map.setWaiting(false);
                        _context.map.refresh();
                    }, function () {
                        if (_context.map)
                            _context.map.setWaiting(false);
                    });
                });
            }
        });
        _context.bean = {
            add: function (params, action, source) {
                var data = Object.assign({}, action.data);
                params = params || {};
                var parentField = action.parentField;
                if (parentField && params && params.id) {
                    data[parentField] = params.id;
                }
                if (action.baseParams) {
                    var abp = action.baseParams;
                    if (typeof abp === 'string')
                        abp = JSON.parse(abp);
                    Object.assign(data, abp);
                }
                if (params.baseParams)
                    Object.assign(data, params.baseParams);
                if (source && source.baseParams) {
                    Object.assign(data, source.baseParams);
                    if (source.baseParams._query_start_with && data.parent === undefined)
                        data.parent = source.baseParams._query_start_with;
                }
                if (action.external)
                    window.open(_util.appendUrlParam('bean.form.html', $.extend({ name: 'add', data: encodeURIComponent(JSON.stringify(data)) }, action)), '_blank');
                else {
                    return _bean.showAdd(action.objectType || params.objectType, data, action.title, action.formName, Object.assign({ source: source }, action.options || action));
                }
            },
            modify: function (params, action, source) {
                if (Array.isArray(params))
                    params = params[0];
                if (action.external)
                    window.open(_util.appendUrlParam('bean.form.html', { objectType: params.objectType, id: params.id, name: 'modify' }), '_blank');
                else {
                    return _bean.showModify(params.objectType, params.id, action.title, action.formName || 'modify', Object.assign({ source: source }, action.options || action));
                }
            },
            batchmodify: function (params, action) {
                if (!Array.isArray(params))
                    params = [params];
                if (params.length) {
                    var objectType_1 = action.objectType, bmf_1 = action.metaForm;
                    var opt_1 = action.options;
                    if (typeof opt_1 === 'string')
                        opt_1 = JSON.parse(opt_1);
                    _bean.getMeta(objectType_1).then(function (om) {
                        if (!bmf_1)
                            bmf_1 = om.forms.find(function (f) { return f.name === 'batchmodify'; });
                        if (typeof bmf_1 === 'string')
                            bmf_1 = JSON.parse(bmf_1);
                        _bean.showModify(action.objectType, {}, action.title || action.label, 'batchmodify', Object.assign({
                            okSubmit: false, showTemplates: false, disableSubmitFirst: true, okText: '批量修改', _is_adding: false,
                            formOptions: {
                                metaForm: bmf_1,
                                formOptions: {
                                    fieldFilter: bmf_1 ? undefined : function (f) {
                                        return _bean.fieldsNoTemplate.indexOf(f.name) === -1;
                                    }
                                }
                            }
                        }, opt_1)).then(function (data) {
                            return _bean.update(objectType_1, Object.assign(data, { id: params.map(function (i) { return i.id; }) }));
                        });
                    });
                }
            },
            view: function (params, action) {
                return _bean.showModify(params.objectType, params.id, '', '', { readOnly: true });
            },
            remove: function (params, action, source) {
                var objtype = params.objectType;
                if (!objtype && Array.isArray(params) && params.length)
                    objtype = params[0].objectType;
                var removeConfirmText = _context.getSetting('removeConfirmText.' + objtype) || _context.getSetting('removeConfirmText.default') || _locale.bean.removeConfirmText;
                if (params.removeConfirmText)
                    removeConfirmText = params.removeConfirmText;
                if (!Array.isArray(params))
                    params = [params];
                if (params.length) {
                    var labels = params.map(function (o) {
                        var om = getMetaForce(o.objectType);
                        if (om && om.labelField && o[om.labelField])
                            return o[om.labelField];
                        return o.CODE || o.NAME || o.code || o.name || o.label || o.title;
                    });
                    return _sui.confirm(removeConfirmText + ': ' + labels.join(',')).then(function () {
                        return _bean.remove(params[0].objectType, { id: params.map(function (p) { return p.id; }).join(','), _showLoading: { success: _locale.bean.removedTip } }, { source: source });
                    });
                }
                return Promise.reject();
            },
            imp: function (params, action, grid) {
                var url = action.url || 'bean/imp.do';
                var actionPs = Object.assign({}, action);
                delete actionPs.el;
                url = _util.appendUrlParam(url, actionPs);
                if (grid) {
                    var ps = { _metagrid: grid.gridName };
                    if (action.withFieldsJson !== false) {
                        var cols = grid.columns.map(function (c) {
                            return { name: c.name, label: c.label };
                        });
                        ps.fieldsJson = JSON.stringify(cols);
                    }
                    url = _util.appendUrlParam(url, ps);
                }
                _sui.showUploadModal(_context.fullDataUrl(url), {
                    success: function (data, file) {
                        if (_context.getSetting('showImpResult')) {
                            if (data.success > 0 && data.fail == 0) {
                                _sui.alert("导入成功：" + data.success + "条");
                            }
                            else if (data.success == 0 && data.fail > 0) {
                                _sui.alert("导入失败：" + data.fail + "条");
                            }
                            else {
                                _sui.alert("导入成功：" + data.success + "条\r\n导入失败：" + data.fail + "条");
                            }
                        }
                    }
                }).on('EVENT_UPLOAD_SUCCESS', function () {
                    _context.events.trigger(_bean.EVENT_OBJ, { method: 'add', objectType: action.objectType });
                });
            },
            exp: function (data, action, grid) {
                var expdata;
                if (grid) {
                    if (0 === grid.checkedData.length && data && data.length < 200) {
                        expdata = data;
                    }
                    else {
                        expdata = grid.checkedData;
                    }
                }
                var ids = [];
                $.each(expdata, function (i, d) {
                    if (d.id)
                        ids.push(d.id);
                });
                var ps = ids.length ? { id: ids.join(',') } : {}, basePs = Object.assign({}, action);
                delete basePs.name;
                delete basePs.id;
                delete basePs.label;
                if (action.baseParams) {
                    basePs = parsekv(action.baseParams);
                }
                ps = Object.assign({}, basePs, ps);
                if (grid && action.withFieldsJson !== false)
                    ps = putFieldsJson(ps, grid);
                delete ps.el;
                var url = _util.appendUrlParam('bean/exp.do', ps);
                url = _context.fullDataUrl(url);
                window.open(url, '_blank');
            },
            expcurrent: function (data, action, grid) {
                var ids = [];
                $.each(data, function (i, d) { ids.push(d.id); });
                var ps = { id: ids.join(',') }, basePs = action;
                if (action.baseParams) {
                    basePs = parsekv(action.baseParams);
                }
                ps = Object.assign({}, basePs, ps);
                if (grid)
                    ps = putFieldsJson(ps, grid);
                delete ps.el;
                var url = _util.appendUrlParam('bean/exp.do', ps);
                url = _context.fullDataUrl(url);
                window.open(url, '_blank');
            },
            expall: function (data, action, grid) {
                _sui.confirm('确定要导出全部记录么?').then(function () {
                    var ps = { limit: 10000 }, basePs;
                    if (action.baseParams) {
                        basePs = parsekv(action.baseParams);
                    }
                    else {
                        basePs = Object.assign({}, action);
                        delete basePs.id;
                    }
                    ps = Object.assign(ps, basePs);
                    if (grid)
                        ps = putFieldsJson(ps, grid);
                    delete ps.start;
                    var url = _util.appendUrlParam('bean/exp.do', ps);
                    url = _context.fullDataUrl(url);
                    window.open(url, '_blank');
                });
            }
        };
    }
    function putFieldsJson(ps, grid, getLabelMark) {
        if (getLabelMark === void 0) { getLabelMark = false; }
        ps = Object.assign(grid.getQueringParams(true), ps);
        if (grid.queryActionName)
            ps.queryActionName = grid.queryActionName;
        if (grid.columns) {
            var fs_1 = [], keys_1 = ['name', 'label'];
            grid.columns.forEach(function (c) {
                if (c._exp == false)
                    return;
                var f = {};
                for (var k in c) {
                    if (keys_1.indexOf(k) !== -1) {
                        if (getLabelMark && c.getLabel && k == 'name') {
                            if (c.getLabel.indexOf('-') != -1) {
                                f["getLabel"] = c.getLabel;
                                f[k] = c[k];
                            }
                            else {
                                if (c.getLabel.indexOf('?') != -1)
                                    f[k] = c.getLabel.split(' ').join('').split('?row.')[1].split(':')[0];
                                else
                                    f[k] = c.getLabel.split(' ').join('').split('row.')[1];
                            }
                        }
                        else {
                            f[k] = c[k];
                        }
                    }
                }
                fs_1.push(f);
            });
            ps.fieldsJson = JSON.stringify(fs_1);
        }
        return ps;
    }
    _bean.putFieldsJson = putFieldsJson;
    _bean.TemplateObjectTypeSuffix = ['.TEMPLATE', '_TEMPLATE'];
    function isTempalateObjectType(t) {
        t = t.objectType || t;
        for (var i = 0; i < _bean.TemplateObjectTypeSuffix.length; i++) {
            if (t.endsWith(_bean.TemplateObjectTypeSuffix[i]))
                return true;
        }
        return false;
    }
    _bean.isTempalateObjectType = isTempalateObjectType;
    function getTopBean() {
        var b = window['_bean'];
        try {
            var w = window;
            while (w.parent && w.parent !== w && w.parent['_bean']) {
                b = window.parent['_bean'];
                w = w.parent;
            }
        }
        catch (err) {
        }
        return b;
    }
    _bean.getTopBean = getTopBean;
    function getDataRootUrl() {
        return _context ? _context.dataRootUrl : _bean.dataRootUrl;
    }
    _bean.getDataRootUrl = getDataRootUrl;
    function fullDataUrl(url) {
        if (url.startsWith('http'))
            return url;
        return getDataRootUrl() + url;
    }
    _bean.fullDataUrl = fullDataUrl;
    function getGridUrl(item, objectType, options) {
        if (options === void 0) { options = undefined; }
        item = item || {};
        item.objectType = item.objectType || objectType;
        var url = 'bean.grid.html?';
        function mk(k, v) {
            if (v && k !== 'id' && k !== 'style' && k !== 'class' && k !== 'urlParams') {
                if (typeof v === 'object')
                    try {
                        v = JSON.stringify(v);
                    }
                    catch (e) {
                        v = null;
                    }
                if (v !== null)
                    url += k + '=' + encodeURIComponent(v) + '&';
            }
        }
        for (var k in item) {
            mk(k, item[k]);
        }
        if (options)
            for (var k in options)
                mk(k, options[k]);
        return url;
    }
    _bean.getGridUrl = getGridUrl;
    function parsekv(s) {
        return _util.parsekv(s);
    }
    var objecttype_settings = _util.getStorage('objtype_setting') || {};
    function setObjectTypeSetting(objectType, key, value) {
        var t = objecttype_settings[objectType];
        if (!t) {
            t = {};
            objecttype_settings[objectType] = t;
        }
        t[key] = value;
        _util.setStorage('objtype_setting', objecttype_settings);
    }
    _bean.setObjectTypeSetting = setObjectTypeSetting;
    function getObjectTypeSetting(objectType, key) {
        var t = objecttype_settings[objectType], value;
        if (t) {
            value = t[key];
        }
        if (value === undefined) {
            t = _context.getSetting('objtype_setting') || {};
            if (t[objectType])
                value = t[objectType][key];
            if (value === undefined)
                value = t[key];
        }
        return value;
    }
    _bean.getObjectTypeSetting = getObjectTypeSetting;
    function equals(o1, o2) {
        if (o1 === o2)
            return true;
        return o1.id && o1.id == o2.id && o1.objectType === o2.objectType;
    }
    _bean.equals = equals;
    function contains(list, o) {
        return list.find(function (l) { return equals(l, o); });
    }
    _bean.contains = contains;
    function showAdd(objectType, data, title, formName, options) {
        if (data === void 0) { data = {}; }
        if (title === void 0) { title = ''; }
        if (formName === void 0) { formName = 'add'; }
        if (options === void 0) { options = {}; }
        return _bean.getMeta(objectType).then(function (om) {
            if (!title)
                title = "<i class=\"write icon\"></i>".concat(_bean.strings.objCreate, " ").concat(om.label);
            if (!formName)
                formName = 'add';
            return _bean.showPostModal(objectType, title, data, formName, Object.assign({ _is_adding: true }, options)).then(function (data) {
                return data;
            });
        });
    }
    _bean.showAdd = showAdd;
    function showModify(objectType, id, title, formName, options) {
        if (title === void 0) { title = ''; }
        if (formName === void 0) { formName = 'modify'; }
        if (options === void 0) { options = {}; }
        return _bean.getMeta(objectType).then(function (om) {
            var form = om.findForm(formName) || {};
            for (var k in form) {
                var v = form[k];
                if (k !== 'name' && k !== 'label' && options[k] === undefined) {
                    if (typeof v === 'string' || typeof v === 'number' || typeof v === 'boolean')
                        options[k] = v;
                }
            }
            if (!title)
                title = "".concat(_bean.strings.objModify, " ").concat(om.label);
            options.icon = options.icon || 'write';
            if (options.disableSubmitFirst === undefined)
                options.disableSubmitFirst = false;
            if (typeof id === 'string' || typeof id === 'number')
                return _bean.findByForm(objectType, formName, id, options).then(function (data) {
                    return _bean.showPostModal(objectType, title, data, formName, options).catch(function () { });
                });
            else
                return _bean.showPostModal(objectType, title, id, formName, options).catch(function () { });
        });
    }
    _bean.showModify = showModify;
    function showView(objectType, id, title, formName, options) {
        if (title === void 0) { title = ''; }
        if (formName === void 0) { formName = 'view'; }
        if (options === void 0) { options = {}; }
        options.readOnly = true;
        return showModify(objectType, id, title, formName, options);
    }
    _bean.showView = showView;
    function showSelects(objectType, title, selected, baseParams, gridOptions) {
        if (selected === void 0) { selected = null; }
        if (baseParams === void 0) { baseParams = null; }
        if (gridOptions === void 0) { gridOptions = null; }
        return showSelect(objectType, title, 'search', baseParams, gridOptions, selected);
    }
    _bean.showSelects = showSelects;
    function showSelect(objectType, title, gridName, baseParams, gridOptions, selected, labelField) {
        if (gridName === void 0) { gridName = 'search'; }
        if (gridOptions === void 0) { gridOptions = null; }
        if (selected === void 0) { selected = undefined; }
        if (labelField === void 0) { labelField = undefined; }
        var defaultGridOptions = { actions: [], extraActions: [], rowDetailField: false };
        var searchTypeChildren;
        if (gridOptions) {
            if (gridOptions.data && gridOptions.showSearch === undefined)
                defaultGridOptions.toolbar = false;
            if (gridOptions.showSearch === 'expand' || gridOptions.showSearch === 'collapse')
                defaultGridOptions.toolbar = '<div></div>';
            searchTypeChildren = gridOptions.searchTypeChildren;
            delete gridOptions.searchTypeChildren;
        }
        var gridFactory;
        if (searchTypeChildren === undefined)
            searchTypeChildren = _context.getSetting('searchTypeChildren');
        if (!searchTypeChildren)
            searchTypeChildren = {};
        var mainTypes = objectType.split(','), childrenTypes = [];
        mainTypes.forEach(function (t) {
            t = t.trim();
            var chtypes = searchTypeChildren[t];
            if (typeof chtypes === 'string')
                chtypes = chtypes.split(',');
            if (chtypes && chtypes.length)
                Array.prototype.push.apply(childrenTypes, chtypes);
        });
        var allTypes = mainTypes.concat(childrenTypes);
        var allTypeStrings = allTypes.map(function (t) {
            if (typeof t === 'string')
                return t.trim();
            return t.objectType;
        });
        if (allTypes.length > 1) {
            gridFactory = function (el) {
                return _bean.getMetas(allTypeStrings).then(function () {
                    var tabs = _sui.tabs(el, { headerClass: 'compact text', headerCss: { flex: 'none' } });
                    return tabs.rendered().then(function () {
                        return allTypes.map(function (t, i) {
                            var typeString = allTypeStrings[i];
                            var om = getMetaForce(typeString);
                            var gridEl = $('<div>').css({ flex: 'auto', overflow: 'auto', paddingBottom: '5px' });
                            tabs.addTab(typeString, gridEl, { closable: false, active: false, title: t.label || om.label });
                            var gridOpt = Object.assign({}, defaultGridOptions, gridOptions);
                            if (typeof t === 'object')
                                gridOpt = Object.assign(gridOpt, t);
                            else if (gridOpt.baseParamsInChildrenType !== false)
                                gridOpt.baseParams = baseParams;
                            if (baseParams && baseParams._assemble && !gridOpt.baseParams) {
                                gridOpt.baseParams = { _assemble: baseParams._assemble };
                            }
                            if (gridOpt.autoLoad === undefined && i !== 0) {
                                gridOpt.autoLoad = false;
                            }
                            return _bean.grid(t, gridName, gridEl, gridOpt);
                        });
                    });
                });
            };
        }
        else
            gridFactory = function (el) {
                return _bean.grid(objectType, gridName, el, Object.assign({ baseParams: baseParams }, defaultGridOptions, gridOptions));
            };
        return _sui.showSelect(gridFactory, labelField || getTitle, title, selected);
    }
    _bean.showSelect = showSelect;
    function showSingleSelect(objectType, title, gridName, baseParams, gridOptions, selected) {
        if (gridName === void 0) { gridName = 'search'; }
        if (baseParams === void 0) { baseParams = null; }
        if (gridOptions === void 0) { gridOptions = null; }
        if (selected === void 0) { selected = undefined; }
        return showSelect(objectType, title, gridName, baseParams, Object.assign({ rowCheckable: 'single' }, gridOptions), selected ? [selected] : null).then(function (ss) {
            if (Array.isArray(ss))
                ss = ss[0];
            return ss;
        });
    }
    _bean.showSingleSelect = showSingleSelect;
    function showDictSelect(objectType, field, title, single, selected) {
        if (single === void 0) { single = false; }
        if (selected === void 0) { selected = undefined; }
        return _bean.dict(objectType, field).then(function (dictItems) {
            return _sui.showSelect(function (el) { return _sui.grid(el, { rowCheckable: single ? 'single' : true, data: dictItems, columns: [{ title: '', name: 'name' }] }); }, 'name', title, selected, { modalClass: 'small' });
        });
    }
    _bean.showDictSelect = showDictSelect;
    function buildPostHeaders() {
        var serviceUrl = getLocationUrl();
        var customHeader = '', accessToken = _context.access_token || sessionStorage.getItem('access_token');
        if (_context.clientConf.indexUrl) {
            customHeader += 'service=' + encodeURIComponent(serviceUrl) + ';';
        }
        var extraCustomHeaders = _context.getSetting('custom-headers');
        if (extraCustomHeaders) {
            for (var k in extraCustomHeaders) {
                customHeader += (k + '=' + extraCustomHeaders[k] + ';');
            }
        }
        var headers = { 'X-Custom-Header': customHeader };
        if (accessToken) {
            headers.Authorization = 'base64:' + btoa(accessToken);
        }
        try {
            if (_context.clientConf['cityMark'] && _context.clientConf['cityMark'] == 'gdy' && _context.clientConf["disableRouteMark"] != true) {
                var user = _context._currentUser;
                if (user && user.SHARDING_ID) {
                    headers['route-mark'] = user.SHARDING_ID;
                }
            }
        }
        catch (e) {
        }
        return headers;
    }
    _bean.buildPostHeaders = buildPostHeaders;
    function getLocationUrl() {
        var w = top === window ? window : _context.window;
        var serviceUrl = w.location.href, pos = serviceUrl.indexOf('?ticket=');
        if (pos === -1)
            pos = serviceUrl.indexOf('&ticket=');
        if (pos !== -1)
            serviceUrl = serviceUrl.substring(0, pos);
        return serviceUrl;
    }
    function object2idstr(v) {
        if (Array.isArray(v))
            return v.map(function (v) { return object2idstr(v); }).join(',');
        if (v !== undefined && v !== null && typeof v === 'object' && v.objectType && v.id !== undefined && v.id !== null)
            return v.id;
        return v;
    }
    _bean.object2idstr = object2idstr;
    function post(url, data, ajaxConfig) {
        if (data === void 0) { data = {}; }
        if (ajaxConfig === void 0) { ajaxConfig = undefined; }
        var loader = data._showLoading, quiet = data._quiet === true, method = 'post';
        if (data._method === 'get' || data._method === 'GET')
            method = 'get';
        delete data.loader;
        delete data._quiet;
        delete data._method;
        delete data._showLoading;
        if (quiet)
            loader = null;
        else if (!loader && _bean.defaultPostLoader)
            loader = _bean.defaultPostLoader;
        if (loader) {
            if (loader === true)
                loader = { success: _locale.bean.successMsg, loading: _locale.bean.loadingMsg };
            else if (typeof loader === 'string')
                loader = { loading: loader };
            else if (_util.isjquery(loader))
                loader = { el: loader };
        }
        if (_bean.postBeforeFn) {
            var ret = _bean.postBeforeFn(data, url);
            if (ret === false)
                return;
            if (typeof ret === 'string')
                url = ret;
        }
        for (var k in data) {
            var v = data[k];
            if ('_paramsJson' === k && typeof v !== 'string')
                data[k] = JSON.stringify(v);
            else
                data[k] = object2idstr(v);
        }
        if (_context.shouldEncodeParams) {
            data = JSON.stringify(data);
            data = encodeURIComponent(data);
            data = btoa(data);
            data = { __encodedParams: data };
        }
        var promise = new Promise(function (resolve, reject) {
            url = fullDataUrl(url);
            var messageInstance;
            if (loader) {
                if (loader.el)
                    _sui.showLoading(loader.el, loader.message || loader.loading);
                else {
                    var msg = loader.message;
                    if (msg === undefined)
                        msg = loader.loading;
                    if (msg === undefined)
                        msg = _locale.bean.loadingMsg;
                    if (msg)
                        messageInstance = _context.message(msg, null, false, loader.hideInfoIcon);
                }
            }
            var serviceUrl = getLocationUrl();
            $.ajax(Object.assign({
                type: method.toUpperCase(),
                url: url, data: data, crossDomain: true,
                headers: buildPostHeaders(),
                success: function (ret, status, xhr) {
                    var successMessage;
                    if (loader) {
                        successMessage = loader.success;
                        if (loader.el)
                            _sui.showLoading(loader.el, false);
                    }
                    var userMessage = xhr.getResponseHeader('UserResponseMessage') || xhr.getResponseHeader("SuccessInfo");
                    if (userMessage) {
                        userMessage = decodeURIComponent(userMessage);
                        userMessage = userMessage.replace(/\r\n/g, '<br>');
                        var level = xhr.getResponseHeader('UserResponseLevel');
                        if (level === 'alert' && !quiet) {
                            window.alert(userMessage);
                        }
                        else {
                            if (successMessage)
                                successMessage += '<br>' + userMessage;
                            else
                                successMessage = userMessage;
                        }
                    }
                    if (messageInstance)
                        _context.removeMessage(messageInstance);
                    if (successMessage)
                        _context.message(successMessage);
                    if (ret && typeof ret.href === 'string') {
                        window.open(ret.href, '_blank');
                    }
                    var disposition = xhr.getResponseHeader("Content-Disposition");
                    if (disposition && disposition.split(";").length > 1) {
                        try {
                            var fileName = disposition.split(";")[1].split("filename=")[1];
                            fileName = decodeURI(fileName);
                            resolve({ data: ret, fileName: fileName });
                        }
                        catch (e) {
                            resolve(ret);
                        }
                    }
                    else {
                        resolve(ret);
                    }
                }
            }, ajaxConfig)).fail(function (err) {
                if (err.status === 403) {
                    sessionStorage.removeItem('access_token');
                    try {
                        var msg = JSON.parse(err.responseText);
                        if (msg.errorCode === 'USER_MULTI_LOGIN') {
                            window.alert(_locale.getString('security.error.userMultiLogin', '您的账号在别处登录，您被强制下线！'));
                        }
                    }
                    catch (e) { }
                    console.log('request not allowed');
                    setTimeout(function () {
                        var loginUrl = _context.clientConf.loginUrl;
                        var customHeader = err.getResponseHeader('X-Custom-Header');
                        if (customHeader) {
                            var xo_1 = {}, flag = ';';
                            if (customHeader.indexOf('\\;') !== -1)
                                flag = '\\;';
                            customHeader.split(flag).forEach(function (ch) {
                                var pos = ch.indexOf('=');
                                if (pos !== -1)
                                    xo_1[ch.substring(0, pos)] = ch.substring(pos + 1);
                            });
                            if (xo_1.loginUrl)
                                loginUrl = xo_1.loginUrl;
                        }
                        if (loginUrl) {
                            var t = loginUrl, s = serviceUrl;
                            if (loginUrl.indexOf('redirect_url') !== -1) {
                                t += '=' + encodeURIComponent(s);
                            }
                            else {
                                t += t.indexOf('?') === -1 ? '?' : '&';
                                t += 'service=' + encodeURIComponent(s);
                            }
                            (_context.rootContext || _context).window.location = t;
                        }
                        else
                            (_context.rootContext || _context).window.location.reload();
                    }, 1000);
                    return;
                }
                _context.message();
                if (loader) {
                    if (loader.el)
                        _sui.showLoading(loader.el, false);
                }
                var errText = err.responseText;
                if (!quiet) {
                    if (!errText && err.status === 0)
                        errText = _locale.getString('error.connectFail', '数据服务访问失败!');
                    _sui.alertError(errText || err, _context.getSetting('exceptionBrowserAlert'));
                }
                try {
                    errText = JSON.parse(errText);
                    errText = errText.message || errText.text || errText.content || errText.error;
                }
                catch (e) { }
                err.errorMessage = errText;
                reject(err);
            });
        });
        return promise;
    }
    _bean.post = post;
    function postDownload(url, data, fileName) {
        if (data === void 0) { data = {}; }
        if (fileName === void 0) { fileName = undefined; }
        return new Promise(function (resolve, reject) {
            post(url, data, {
                mimeType: 'application/octet-stream',
                xhrFields: {
                    responseType: 'blob'
                }
            }).then(function (res) {
                var blob = res.data;
                var alink = document.createElement("a");
                var href = alink.href = URL.createObjectURL(blob);
                if (fileName) {
                    alink.download = fileName;
                }
                else if (res.fileName) {
                    alink.download = res.fileName;
                }
                else {
                    alink.target = '_blank';
                }
                alink.click();
                alink.remove();
                URL.revokeObjectURL(href);
                resolve(null);
            }).catch(function (err) {
                reject(err);
            });
        });
    }
    _bean.postDownload = postDownload;
    function get(url, data) {
        if (data === void 0) { data = {}; }
        url = _context.fixUrl(url);
        return new Promise(function (resolve, reject) {
            url = fullDataUrl(url);
            $.get(url, data, function (ret) { return resolve(ret); }, 'text').fail(function (err) {
                if (!data || !data._quiet)
                    alert(err.responseText || err);
                reject(err);
            });
        });
    }
    _bean.get = get;
    var metas = _context.metas;
    var getting_metas = {};
    function registerMeta(objectType, meta, overwrite) {
        if (!metas[objectType] || overwrite)
            metas[objectType] = meta;
        if (_context.updateParentMeta && _context.parentContext) {
            overwrite = overwrite || _context.updateParentMeta === 'force';
            _context.parentContext.window['_bean'].registerMeta(objectType, meta, overwrite);
        }
    }
    _bean.registerMeta = registerMeta;
    function getMeta(objectType) {
        if (typeof objectType === 'string' && objectType.indexOf(',') !== -1)
            objectType = objectType.split(',');
        if (Array.isArray(objectType))
            return getMetas(objectType);
        if (metas[objectType] !== undefined)
            return Promise.resolve(metas[objectType]);
        var typekey = objectType;
        var pending = getting_metas[typekey];
        if (pending) {
            return pending;
        }
        pending = _callParent(function (pb) {
            return pb.getMeta(objectType).then(function (om) {
                return cloneObjMeta(om);
            });
        }, function () { return post('bean/getMeta.do', { _type: objectType }); }).then(function (om) {
            return objMetaLoaded(objectType, om);
        });
        pending.then(function (om) {
            delete getting_metas[typekey];
        }, function (e) {
            delete getting_metas[typekey];
        });
        getting_metas[typekey] = pending;
        return pending;
    }
    _bean.getMeta = getMeta;
    function cloneObjMeta(om) {
        if (!om)
            return om;
        om = Object.assign({}, om);
        if (om.actions)
            om.actions = om.actions.map(function (a) { return Object.assign({}, a); });
        if (om.forms)
            om.forms = om.forms.map(function (a) { return Object.assign({}, a); });
        if (om.grids)
            om.grids = om.grids.map(function (a) { return Object.assign({}, a); });
        return om;
    }
    function objMetaLoaded(objectType, om) {
        if (!om) {
            om = { fields: [], __om_found: false };
        }
        om.objectType = objectType;
        om.findField = function (name) {
            return om.fields.find(function (f) { return f.name === name; });
        };
        om.findGrid = function (name) {
            return om.grids.find(function (f) { return !name ? true : f.name === name; });
        };
        om.findForm = function (name) {
            return om.forms.find(function (f) { return !name ? true : f.name === name; });
        };
        function _findfield(fs, name) {
            var ret, fd;
            for (var i = 0; i < fs.length; i++) {
                fd = fs[i];
                if (fd.name === name) {
                    ret = fd;
                }
                else if (fd.fields) {
                    ret = _findfield(fd.fields, name);
                }
                if (ret)
                    break;
            }
            return ret;
        }
        om.findGridField = function (gridName, fieldName) {
            var g = om.findGrid(gridName);
            if (!g)
                return null;
            return _findfield(g.fields, fieldName);
        };
        om.findFormField = function (formName, fieldName) {
            var f = om.findForm(formName);
            if (!f)
                return null;
            return _findfield(f.fields, fieldName);
        };
        _util.traval(om, function (n) {
            if (n.extras) {
                Object.assign(n, n.extras);
                delete n.extras;
            }
        });
        om.grids = om.grids || [];
        om.forms = om.forms || [];
        om.actions = om.actions || [];
        om.actions.forEach(function (a) {
            if (a.isRemoteAction === undefined)
                a.isRemoteAction = true;
        });
        var localeBeanMeta, _c = _context;
        while (!localeBeanMeta && _c) {
            if (_c.locale && _c.locale.bean && _c.locale.bean.meta)
                localeBeanMeta = _c.locale.bean.meta;
            _c = _c.parentContext;
        }
        if (localeBeanMeta && localeBeanMeta[objectType]) {
            var lm_1 = localeBeanMeta[objectType];
            if (lm_1.label)
                om.label = lm_1.label;
            if (lm_1.fields)
                om.fields.forEach(function (of) {
                    var lf = lm_1.fields[of.name];
                    if (lf !== undefined)
                        of.label = lf;
                });
            if (lm_1.forms)
                om.forms.forEach(function (of) {
                    var lf = lm_1.forms[of.name];
                    if (lf !== undefined)
                        of.label = lf;
                });
            if (lm_1.actions)
                om.actions.forEach(function (of) {
                    var lf = lm_1.actions[of.name];
                    if (lf !== undefined)
                        of.label = lf;
                });
            if (lm_1.grids)
                om.grids.forEach(function (of) {
                    var lf = lm_1.grids[of.name];
                    if (lf !== undefined)
                        of.label = lf;
                });
        }
        if (_context && _context.configXml) {
            if (_context.objMetaXmls)
                _context.objMetaXmls.forEach(function (metaFile) { return mergeom(metaFile); });
            mergeom(_context.configXml);
            if (_context.objMetaXmls)
                _context.objMetaXmls.forEach(function (metaFile) { return mergeomdefault(metaFile); });
            mergeomdefault(_context.configXml);
            mergeObjMeta(_context.objMetaDefault, false);
            om.objectType = objectType;
        }
        function mergeom(metaFile) {
            if (_context.useParentMeta && _context.parentContext) {
                if (_context.parentContext.configXml === metaFile)
                    return;
                if (_context.parentContext.objMetaXmls && _context.parentContext.objMetaXmls.indexOf(metaFile) !== -1)
                    return;
            }
            mergeObjMeta(metaFile.find("ObjMeta[objectType='".concat(objectType, "']")), true);
        }
        function mergeomdefault(metaFile) {
            if (_context.useParentMeta && _context.parentContext) {
                if (_context.parentContext.configXml === metaFile)
                    return;
                if (_context.parentContext.objMetaXmls && _context.parentContext.objMetaXmls.indexOf(metaFile) !== -1)
                    return;
            }
            mergeObjMeta(metaFile.find('ObjMeta[objectType="*"]'), false);
        }
        function cpplain(t, s, overwrite) {
            for (var k in s) {
                var tk = t[k], sk = s[k];
                if (typeof sk !== 'object') {
                    if (overwrite || (tk === null || typeof tk === 'undefined'))
                        t[k] = sk;
                }
            }
        }
        function fillField(node) {
            var tag = node.tagName;
            if (tag === 'field' && node.name) {
                var omField = om.fields.find(function (f) { return f.name === node.name; });
                if (omField)
                    cpplain(node, omField, false);
            }
        }
        function mergeObjMeta(omm, overwrite) {
            if (!omm)
                return;
            if (_util.isjquery(omm) && !omm.length)
                return;
            if (_util.isjquery(omm) && omm.length) {
                omm = _util.jQueryToPlain(omm[0], function (tagName) {
                    if (tagName === 'row')
                        tagName = 'field';
                    return tagName + 's';
                }, false, function (node) {
                    if (_locale)
                        _locale.configNode(node);
                });
            }
            if (overwrite && om.__om_found === false)
                delete om.__om_found;
            _mergeObjMeta(omm, overwrite);
        }
        function _mergeObjMeta(omm, overwrite) {
            cpplain(om, omm, overwrite !== false);
            if (omm.fields)
                omm.fields.forEach(function (f) {
                    var omField = om.fields.find(function (of) { return of.name === f.name; });
                    if (omField)
                        cpplain(omField, f, overwrite);
                    else if (overwrite) {
                        f._from = 'appconfig';
                        om.fields.push(f);
                    }
                });
            if (omm.actions)
                omm.actions.forEach(function (a) {
                    a.id = a.id || a.name;
                    var omAction = om.actions.find(function (oa) { return oa.id === a.id || oa.name === a.id; });
                    if (omAction)
                        cpplain(omAction, a, overwrite);
                    else
                        om.actions.push(a);
                });
            if (omm.forms)
                omm.forms.forEach(function (f) {
                    f._from = 'appconfig';
                    var idx = om.forms.findIndex(function (of) { return (!of.name && !f.name) || of.name === f.name; });
                    if (idx === -1) {
                        om.forms.push(f);
                    }
                    else {
                        if (!f.fields)
                            cpplain(om.forms[idx], f, overwrite);
                        else if (overwrite)
                            om.forms.splice(idx, 1, f);
                    }
                    _util.traval(f, fillField);
                });
            if (omm.grids)
                omm.grids.forEach(function (g) {
                    var idx = om.grids.findIndex(function (og) { return (!og.name && !g.name) || og.name === g.name; });
                    if (idx === -1) {
                        om.grids.push(g);
                    }
                    else {
                        if (!g.fields)
                            cpplain(om.grids[idx], g, overwrite);
                        else if (overwrite)
                            om.grids.splice(idx, 1, g);
                    }
                    _util.traval(g, fillField);
                });
            if (omm.scripts) {
                omm.scripts.forEach(function (s) {
                    eval(s.content);
                });
            }
        }
        om.actions.forEach(function (a) { return a.objectType = a.objectType || objectType; });
        if (!om.grids.length)
            om.grids.push({ fields: om.fields.filter(function (f) { return f.defaultInGrid !== false && f.label; }) });
        if (!om.forms.length)
            om.forms.push({ fields: om.fields.filter(function (f) { return f.defaultInForm !== false && f.label; }) });
        if (_context.objMetaDefaultFn)
            _context.objMetaDefaultFn(om);
        if (_context.objMetaDefaultFns)
            _context.objMetaDefaultFns.forEach(function (fn) {
                fn(om);
            });
        if (om.__om_found === false) {
            om = null;
        }
        registerMeta(objectType, om, false);
        return om;
    }
    function getParentBeanUtil() {
        if (window['_context'] && window['_context'].parentContext && window['_context'].parentContext.window['_bean'])
            return window['_context'].parentContext.window['_bean'];
    }
    function _callParent(fn, fallback) {
        if (window['_context'] && window['_context'].useParentMeta === false)
            return fallback();
        var pb = getParentBeanUtil();
        if (pb)
            return fn(pb);
        return fallback();
    }
    function getMetas(types, logIfFail) {
        if (logIfFail === void 0) { logIfFail = true; }
        types = types.map(function (t) { return t.trim(); });
        var toQuery = types.filter(function (t) { return !getMetaForce(t); });
        if (!toQuery.length)
            return Promise.resolve(types.map(function (t) { return getMetaForce(t); }));
        return _callParent(function (pb) {
            return pb.getMetas(toQuery).then(function (oms) {
                return oms.map(function (om) { return cloneObjMeta(om); });
            });
        }, function () { return post('bean/getMeta.do', { _type: toQuery.join(',') }); }).then(function (oms) {
            oms = Array.isArray(oms) ? oms : [oms];
            oms.forEach(function (om, i) {
                if (!om && logIfFail)
                    console.error('objmeta fail:' + toQuery[i]);
                objMetaLoaded(toQuery[i], om);
            });
            return types.map(function (t) { return getMetaForce(t); });
        });
    }
    _bean.getMetas = getMetas;
    function getMetaChildren(parentType) {
        return post('bean/getMeta.do', { parentType: parentType }).then(function (oms) {
            oms = oms.map(function (om) {
                var al = getMetaForce(om.objectType);
                if (!al) {
                    objMetaLoaded(om.objectType, om);
                    al = om;
                }
                return al;
            });
            return oms;
        });
    }
    _bean.getMetaChildren = getMetaChildren;
    function getTitle(o) {
        if (!o)
            return '';
        var fs = ['title', 'label', 'NAME', 'CODE'];
        if (o.objectType) {
            var om = getMetaForce(o.objectType);
            if (om && om.labelField)
                fs.unshift(om.labelField);
        }
        return _util.fetchField(o, fs);
    }
    _bean.getTitle = getTitle;
    function isMetaChildren(childType, parentType, ignoreTypes) {
        if (ignoreTypes === void 0) { ignoreTypes = []; }
        if (childType === parentType)
            return Promise.resolve(true);
        return getMeta(childType).then(function (cm) {
            if (!cm)
                return false;
            var extendsTypes = cm.extendFrom ? cm.extendFrom.split(',').map(function (t) { return t.trim(); }) : null;
            if (!extendsTypes)
                return false;
            if (extendsTypes.indexOf(parentType) !== -1)
                return true;
            var ptype = extendsTypes.pop();
            if (ptype && ignoreTypes.indexOf(ptype) === -1) {
                ignoreTypes.push(ptype);
                return isMetaChildren(ptype, parentType, ignoreTypes).then(function (has) {
                    if (!has && extendsTypes.length)
                        return isMetaChildren(extendsTypes.pop(), parentType, ignoreTypes);
                    return has;
                });
            }
            return false;
        });
    }
    _bean.isMetaChildren = isMetaChildren;
    function getMetaForce(objectType) {
        return metas[objectType];
    }
    _bean.getMetaForce = getMetaForce;
    function hasGeomType(om) {
        return findFieldByType(om, 'geometry') || om._mapType;
    }
    _bean.hasGeomType = hasGeomType;
    function getGeomField(objectType) {
        return getMeta(objectType).then(function (om) {
            if (om._mapGeomField)
                return { type: 'geometry', name: om._mapGeomField };
            if (om._mapType)
                return getGeomField(om._mapType);
            var geomField = findFieldByType(om, 'geometry');
            return geomField;
        });
    }
    _bean.getGeomField = getGeomField;
    function getSimpleQueryTypes() {
        return post('bean/getSimpleQueryTypes.do');
    }
    _bean.getSimpleQueryTypes = getSimpleQueryTypes;
    function add(objectType, data, triggerEvent) {
        if (triggerEvent === void 0) { triggerEvent = true; }
        if (Array.isArray(data)) {
            data = { _paramsJson: JSON.stringify(data) };
        }
        var codesn = data._codesn;
        delete data._codesn;
        data = Object.assign({ _type: objectType }, data);
        return post('bean/add.do', data).then(function (e) {
            if (triggerEvent)
                try {
                    _context.events.trigger(_bean.EVENT_OBJ, { method: 'add', objectType: objectType, data: e || data, eventParams: triggerEvent });
                }
                catch (e) {
                    console.error(e);
                }
            if (codesn)
                saveCodeSernos(codesn, data.CODE);
            return e;
        });
    }
    _bean.add = add;
    function saveCodeSernos(sn, code, codeNumFn) {
        if (codeNumFn === void 0) { codeNumFn = undefined; }
        var pre, snVal;
        for (var k in sn) {
            pre = k;
            snVal = sn[k];
        }
        if (code) {
            var n = void 0, i = code.length - 1;
            for (; i > 0; i--) {
                var nn = parseInt(code.substring(i));
                if (!isNaN(nn))
                    n = nn;
                else {
                    if (!isNaN(n))
                        break;
                }
            }
            if (codeNumFn)
                n = codeNumFn(n);
            var codePre = code.substring(0, i + 1);
            if (n !== undefined && !isNaN(n) && codePre)
                sn[codePre] = n;
        }
        var sns = getCodeSernos();
        Object.assign(sns, sn);
        _util.setStorage('_codeserno', sns);
    }
    _bean.saveCodeSernos = saveCodeSernos;
    function getCodeSernos() {
        return _util.getStorage('_codeserno') || {};
    }
    _bean.getCodeSernos = getCodeSernos;
    function getCodeSerno(pre) {
        return getCodeSernos()[pre];
    }
    _bean.getCodeSerno = getCodeSerno;
    function genCode(type, data, remote, exceptionIfRemoteFail) {
        if (remote === void 0) { remote = false; }
        if (exceptionIfRemoteFail === void 0) { exceptionIfRemoteFail = false; }
        if (remote) {
            var ps = void 0;
            var fail = void 0;
            if (exceptionIfRemoteFail) {
                data = Object.assign({ exceptionIfFail: true }, data);
            }
            else {
                ps = { _quiet: true };
                fail = function () {
                    return genCode(type, data, false);
                };
            }
            return _bean.action(type, 'genCode', data, ps).then(function (code) {
                var _a;
                if (code) {
                    var num = NaN, pre = void 0;
                    for (var i = code.length - 1; i >= 0; i--) {
                        var tryNum = parseInt(code.substring(i));
                        if (!isNaN(tryNum)) {
                            num = tryNum;
                            pre = code.substring(0, i);
                        }
                        else {
                            if (!isNaN(num))
                                break;
                        }
                    }
                    saveCodeSernos((_a = {}, _a[pre] = num, _a), code);
                    return code;
                }
                return genCode(type, data, false);
            }, fail);
        }
        return getMeta(type).then(function (om) {
            var a = om.actions.find(function (i) { return i.name === 'genCode' && i.type === 'script'; });
            if (a) {
                var code_1 = '';
                for (var k in data)
                    code_1 += 'var ' + k + '=data["' + k + '"] || {};';
                if (a.url)
                    return _bean.get(a.url).then(function (content) {
                        return eval(code_1 + content);
                    });
                else
                    return eval(code_1 + a.content);
            }
        });
    }
    _bean.genCode = genCode;
    function autoCode(form, pre, snLength, snInitType, initParams) {
        if (snLength === void 0) { snLength = 3; }
        if (snInitType === void 0) { snInitType = null; }
        if (initParams === void 0) { initParams = {}; }
        if (arguments[0].getValue && arguments[0].formInstance) {
            return autoCodeIf.apply(this, arguments);
        }
        var sn = getCodeSerno(pre);
        if (!sn && snInitType)
            return find(snInitType, Object.assign({ CODE: pre, '_op.CODE': 'rlike', _orderBy: 'CREATE_DATE DESC' }, initParams)).then(function (o) {
                if (!o)
                    sn = 0;
                else
                    try {
                        sn = parseInt(o.CODE.replace(pre, '')) || 0;
                    }
                    catch (e) {
                        sn = 0;
                    }
                return _autoCode(form, pre, ++sn, snLength);
            });
        else {
            sn = sn || 0;
            var c = _autoCode(form, pre, ++sn, snLength);
            return Promise.resolve(c);
        }
    }
    _bean.autoCode = autoCode;
    function autoCodeIf(field, form, pre, snLength, snInitType, initParams) {
        if (snLength === void 0) { snLength = 3; }
        if (snInitType === void 0) { snInitType = null; }
        if (initParams === void 0) { initParams = {}; }
        var modified = field.isDirty(), v = field.getValue();
        if (modified && (!v || v == field.__lastCodeGen || !v.trim()))
            modified = false;
        if (modified)
            return Promise.resolve(v);
        return autoCode(form, pre, snLength, snInitType, initParams).then(function (c) {
            field.__lastCodeGen = c;
            return c;
        });
    }
    _bean.autoCodeIf = autoCodeIf;
    function _autoCode(form, pre, sno, snLength) {
        if (snLength === void 0) { snLength = 3; }
        var codesn = {};
        codesn[pre] = sno;
        if (form && form.baseData)
            form.baseData._codesn = codesn;
        var sn = sno + '';
        while (sn.length < snLength)
            sn = 0 + sn;
        return pre + sn;
    }
    function update(objectType, data, triggerEvent) {
        if (triggerEvent === void 0) { triggerEvent = true; }
        var showLoading = data._showLoading;
        if (Array.isArray(data)) {
            data = { _paramsJson: JSON.stringify(data) };
        }
        else if (typeof data._where === 'object') {
            data = { _paramsJson: JSON.stringify(data) };
        }
        data = Object.assign({ _type: objectType }, data);
        if (showLoading !== undefined && showLoading != false)
            data._showLoading = true;
        return post('bean/update.do', data).then(function (e) {
            if (triggerEvent)
                _context.events.trigger(_bean.EVENT_OBJ, { method: 'update', objectType: objectType, data: e || data, eventParams: triggerEvent });
            return e;
        });
    }
    _bean.update = update;
    function remove(objectType, id, triggerEvent) {
        if (triggerEvent === void 0) { triggerEvent = true; }
        if (Array.isArray(id)) {
            id = id.map(function (i) { return i.id || i; });
            id = id.join(',');
        }
        var params = typeof id === 'object' ? id : { id: id };
        params = Object.assign({ _type: objectType, _showLoading: true }, params);
        return post('bean/delete.do', params).then(function (e) {
            if (triggerEvent)
                _context.events.trigger(_bean.EVENT_OBJ, { method: 'remove', objectType: objectType, data: e || id, eventParams: triggerEvent });
            return e;
        });
    }
    _bean.remove = remove;
    function query(objectType, params, uiSource) {
        if (uiSource === void 0) { uiSource = undefined; }
        params = params || {};
        if (params._assemble === undefined) {
            if (params._metagrid !== undefined || (uiSource && uiSource.metaGrid)) {
                return getMeta(objectType).then(function (om) {
                    var grid, queryFields;
                    if (uiSource) {
                        grid = uiSource.metaGrid;
                        queryFields = uiSource.queryFields;
                    }
                    if (!grid) {
                        grid = findMetaByName(om.grids, params._metagrid);
                        if (queryFields === undefined)
                            queryFields = grid.queryFields;
                    }
                    var _assemble = null, assembles = [], fieldNames = [];
                    grid.fields.forEach(function (f) {
                        var fname = f.name, needAssemble = false;
                        if (f.type === 'obj' || f.type === 'expression')
                            needAssemble = true;
                        if (fname.indexOf('.') !== -1) {
                            needAssemble = true;
                            fname = fname.split('.')[0];
                            if (fname.endsWith('_value')) {
                                fieldNames.push(fname);
                                fname = fname.substring(0, fname.lastIndexOf('_value'));
                            }
                        }
                        if (needAssemble && assembles.indexOf(fname) === -1)
                            assembles.push(fname);
                        fieldNames.push(fname);
                    });
                    if (assembles.length)
                        _assemble = assembles.join(',');
                    else
                        _assemble = null;
                    params._assemble = _assemble;
                    if (params._queryFields === undefined && queryFields) {
                        if (queryFields === true)
                            queryFields = fieldNames;
                        params._queryFields = queryFields;
                    }
                    return query(objectType, params);
                });
            }
        }
        return post('bean/query.do', Object.assign(params, { _type: objectType })).then(function (data) {
            if (_context.objQueryFixFn)
                data = _context.objQueryFixFn(objectType, data, params);
            return data;
        });
    }
    _bean.query = query;
    function page(objectType, params, start, limit, uiSource) {
        if (uiSource === void 0) { uiSource = undefined; }
        return query(objectType, Object.assign({}, params, { start: start, limit: limit }), uiSource);
    }
    _bean.page = page;
    function geojson(objectTypes, params) {
        var types = JSON.stringify(objectTypes);
        params = Object.assign(params, { _type: types });
        if (params.split === undefined)
            params.split = true;
        var geojsonUrl = 'bean/geojson.do';
        if (window['_context'])
            geojsonUrl = window['_context'].getSetting('geojson.url') || geojsonUrl;
        return post(geojsonUrl, params);
    }
    _bean.geojson = geojson;
    function find(objectType, id) {
        var params = typeof id === 'object' ? id : { id: id };
        params = Object.assign(params, { _type: objectType });
        return post('bean/find.do', params);
    }
    _bean.find = find;
    function findByForm(objectType, formName, id, options) {
        if (options === void 0) { options = {}; }
        return getMeta(objectType).then(function (om) {
            var rels = [];
            var fm = om.forms.find(function (f) { return f.name === formName; });
            if (!fm)
                fm = om.forms.find(function (f) { return !f.name; });
            if (!fm && om.forms.length)
                fm = om.forms[0];
            var targetProperty = fm.targetProperty;
            var queryActionName = options.queryActionName || fm.queryActionName;
            if (queryActionName) {
                var idKey = fm.paramIdKey || 'id', params_1 = { _metaform: formName };
                params_1[idKey] = id;
                return action(objectType, queryActionName, params_1).then(function (o) { return o && targetProperty ? o[targetProperty] : o; });
            }
            function dealfield(f) {
                if (!f)
                    return;
                if (f.fields)
                    f.fields.forEach(function (ff) { return dealfield(ff); });
                else {
                    if (f.queryAssemble)
                        rels.push(f.queryAssemble);
                    if (f.name) {
                        var relname = void 0;
                        if (f.name.indexOf('.') !== -1)
                            relname = f.name.substring(0, f.name.indexOf('.'));
                        else if (f.type === 'obj' || f.type === 'expression')
                            relname = f.name;
                        if (relname && rels.indexOf(relname) === -1)
                            rels.push(relname);
                    }
                }
            }
            dealfield(fm);
            var params = typeof id === 'object' ? id : { id: id };
            params._metaform = formName;
            if (rels.length)
                params._assemble = rels.join();
            return find(objectType, params).then(function (o) { return o && targetProperty ? o[targetProperty] : o; });
        });
    }
    _bean.findByForm = findByForm;
    function action(objectType, name, params, extraParams) {
        if (params === void 0) { params = null; }
        if (extraParams === void 0) { extraParams = null; }
        var ps = Object.assign({ _type: objectType, name: name }, extraParams);
        if (params) {
            if (params._showLoading !== undefined) {
                ps._showLoading = params._showLoading;
                delete params._showLoading;
            }
            ps._paramsJson = JSON.stringify(params);
        }
        if (_context)
            _context.events.trigger(_bean.EVENT_ACTION_START);
        return post('bean/action.do', ps).then(function (e) {
            if (_context)
                _context.events.trigger(_bean.EVENT_ACTION_END);
            return e;
        }, function (e) {
            if (_context)
                _context.events.trigger(_bean.EVENT_ACTION_END);
            return e;
        });
    }
    _bean.action = action;
    function batch(batchList, extraParams) {
        if (extraParams === void 0) { extraParams = undefined; }
        var ps = Object.assign({}, extraParams);
        ps._paramsJson = JSON.stringify({ batchList: batchList });
        return post('bean/batch.do', ps);
    }
    _bean.batch = batch;
    function callService(serviceName, methodName, methodParams, extraParams) {
        if (methodParams === void 0) { methodParams = null; }
        if (extraParams === void 0) { extraParams = null; }
        var ps = Object.assign({}, extraParams);
        if (methodParams) {
            ps.methodParams = JSON.stringify(methodParams);
        }
        if (_context) {
            _context.events.trigger(_bean.EVENT_ACTION_START);
        }
        return post('api/call-service.do?serviceName=' + serviceName + '&methodName=' + methodName, ps).then(function (e) {
            if (_context) {
                _context.events.trigger(_bean.EVENT_ACTION_END);
            }
            return e;
        }, function (e) {
            if (_context) {
                _context.events.trigger(_bean.EVENT_ACTION_END);
            }
            return e;
        });
    }
    _bean.callService = callService;
    function count(objectType, params) {
        params = Object.assign({ _type: objectType }, params);
        return post('bean/count.do', params);
    }
    _bean.count = count;
    function sum(objectType, field, params) {
        if (params === void 0) { params = {}; }
        params = Object.assign({ _type: objectType, field: field }, params);
        return post('bean/sum.do', params);
    }
    _bean.sum = sum;
    function max(objectType, field, params) {
        if (params === void 0) { params = {}; }
        params = Object.assign({ _type: objectType, field: field }, params);
        return post('bean/max.do', params);
    }
    _bean.max = max;
    function min(objectType, field, params) {
        if (params === void 0) { params = {}; }
        params = Object.assign({ _type: objectType, field: field }, params);
        return post('bean/min.do', params);
    }
    _bean.min = min;
    function maxcode(objectType, field, pre, params) {
        if (params === void 0) { params = {}; }
        params = Object.assign({ _type: objectType, field: field, preCode: pre }, params);
        return post('bean/maxCode.do', params);
    }
    _bean.maxcode = maxcode;
    function groupby(objectType, field, groupOp, groupField, params) {
        if (params === void 0) { params = {}; }
        params = Object.assign(params, { _type: objectType, field: field, _groupOp: groupOp, _groupField: groupField });
        return post('bean/groupby.do', params);
    }
    _bean.groupby = groupby;
    var dicts = _context.dicts;
    function getDictsCacheKey() {
        return _context.getSetting('dictionaryCache') === false ? null : 'om_dicts_' + _context.lang;
    }
    function dict(objectType, field, params) {
        if (params === void 0) { params = null; }
        var cachkey = getDictsCacheKey();
        if (!dicts) {
            if (cachkey)
                dicts = _util.getStorage(cachkey);
            dicts = _context.dicts = dicts || {};
        }
        if (!params && dicts[objectType] && dicts[objectType][field])
            return Promise.resolve(dicts[objectType][field]);
        return post('bean/dict.do', Object.assign({}, params, { _type: objectType, field: field })).then(function (data) {
            if (!params) {
                dicts[objectType] = dicts[objectType] || {};
                dicts[objectType][field] = data;
                if (cachkey)
                    _util.setStorage(cachkey, dicts);
            }
            return data;
        });
    }
    _bean.dict = dict;
    function dictTextForce(objectType, field, value) {
        var cachkey = getDictsCacheKey();
        if (!dicts) {
            if (cachkey)
                dicts = _util.getStorage(cachkey);
            dicts = _context.dicts = dicts || {};
        }
        if (dicts[objectType] && dicts[objectType][field]) {
            var vs_1;
            if (typeof value === 'string')
                vs_1 = value.split(',');
            else if (!Array.isArray(value))
                vs_1 = [value];
            else
                vs_1 = value;
            var items = dicts[objectType][field].filter(function (d) {
                return vs_1.find(function (v) { return v == d.value; });
            });
            if (items.length)
                return items.map(function (i) { return i.name; }).join(',');
        }
        return value;
    }
    _bean.dictTextForce = dictTextForce;
    function dictRenderer(objectType, field) {
        return function (o) {
            return _util.loadingRender(dict(objectType, field).then(function (dictItems) {
                var dictValue = o[field + '_value'] || o[field];
                if (dictValue === undefined || dictValue === null)
                    return '';
                dictValue = dictValue + '';
                dictValue = dictValue.split(',');
                var dictNames = dictValue.map(function (val) {
                    val = val.trim();
                    var item = dictItems.find(function (di) { return di.value == val; });
                    return item ? item.name : val;
                });
                return dictNames.join(',');
            }));
        };
    }
    _bean.dictRenderer = dictRenderer;
    function clearCache() {
        _util.setStorage(getDictsCacheKey(), null);
        dicts = _context.dicts = {};
        return post('bean/cache.do', { _showLoading: '正在清除对象查询缓存...' });
    }
    _bean.clearCache = clearCache;
    function locate(objectType, id, nullIfEmpty) {
        if (nullIfEmpty === void 0) { nullIfEmpty = false; }
        var params = typeof id === 'object' ? id : { id: id };
        params._type = objectType;
        if (nullIfEmpty)
            params._no_exception = true;
        return post('bean/locate.do', params);
    }
    _bean.locate = locate;
    function findFieldByType(om, type) {
        var fields = om.fields;
        return fields.find(function (f) { return f.type === type; });
    }
    _bean.findFieldByType = findFieldByType;
    function findMetaByName(metas, name) {
        if (!metas)
            return null;
        var defaul = metas[0];
        var found = metas.find(function (item) {
            if (!item.name)
                defaul = item;
            return item.name === name;
        });
        return found || defaul;
    }
    function findAction(name, om, uimeta, source) {
        if (uimeta === void 0) { uimeta = null; }
        if (source === void 0) { source = undefined; }
        if (typeof name === 'function')
            name = name();
        if (typeof name !== 'string')
            return name;
        var action, rootAction = _context.findAction(name);
        if (uimeta && uimeta.actions)
            action = uimeta.actions.find(function (a) { return a.id === name || a.name === name; });
        if (!action)
            action = om.actions.find(function (a) { return a.id === name || a.name === name; });
        if (action || rootAction)
            action = Object.assign({ objectType: om.objectType }, rootAction, action);
        else if (name.indexOf('form:') !== -1) {
            var formName = name.substring(5), fm = om.findForm(formName), targetObjectType = om.objectType;
            if (formName.indexOf('@') !== -1) {
                targetObjectType = formName.substring(formName.indexOf('@') + 1);
                formName = formName.substring(0, formName.indexOf('@'));
            }
            action = { objectType: targetObjectType, formName: formName, type: 'obj', method: 'modify' };
            for (var k in fm) {
                var v = fm[k];
                if (typeof v === 'string' || typeof v === 'number' || typeof v === 'boolean')
                    action[k] = v;
            }
        }
        else if (name.indexOf('grid:') !== -1) {
            var gridName = name.substring(5), fm = om.findGrid(gridName), targetObjectType = om.objectType;
            if (gridName.indexOf('@') !== -1) {
                targetObjectType = gridName.substring(gridName.indexOf('@') + 1);
                gridName = gridName.substring(0, gridName.indexOf('@'));
            }
            action = Object.assign({}, _bean.simpleGridActionParams, { objectType: targetObjectType, gridName: gridName });
            for (var k in fm) {
                var v = fm[k];
                if (k === 'actionOptions') {
                    v = parsekv(v);
                    Object.assign(action, v);
                }
                else if (typeof v === 'string' || typeof v === 'number' || typeof v === 'boolean')
                    action[k] = v;
            }
        }
        if (action) {
            if (_context.hasPermission(action, source || uimeta))
                return action;
            else
                _context.log('action not allowed: ' + name);
        }
        else
            _context.log('action not found: ' + name);
    }
    function nameToActions(str, om, uimeta, source) {
        if (uimeta === void 0) { uimeta = null; }
        if (source === void 0) { source = undefined; }
        if (!str)
            return [];
        if (typeof str === 'string')
            str = str.split(',');
        if (!Array.isArray(str))
            str = [str];
        return str.map(function (actionName) {
            if (typeof actionName === 'string')
                actionName = actionName.trim();
            var a = findAction(actionName, om, uimeta, source);
            if (a && a.type === 'obj')
                a.objectType = a.objectType || om.objectType;
            return a;
        }).filter(function (a) { return a; });
    }
    function getObjActions(om, uimeta, obj, sourceui, options) {
        if (uimeta === void 0) { uimeta = null; }
        if (obj === void 0) { obj = null; }
        var as;
        if (obj && (!options || !options.isMoreItemActions))
            as = obj._itemActionField;
        if (typeof as === 'function')
            as = as(obj, uimeta, sourceui, options);
        if (!as && as !== false) {
            if (typeof om === 'string')
                om = getMetaForce(om);
            var objId = void 0, af = 'itemActions';
            if (typeof obj === 'string' || typeof obj === 'number')
                objId = obj;
            else if (obj)
                objId = obj.id;
            if (!obj)
                af = 'typeActions';
            var isMoreItemActions = false;
            if (options && options.isMoreItemActions !== undefined)
                isMoreItemActions = options.isMoreItemActions;
            if (isMoreItemActions)
                af = 'moreItemActions';
            as = om[af];
            if (isMoreItemActions && as === undefined)
                as = [];
            if (uimeta && uimeta[af] !== undefined)
                as = uimeta[af];
            if (typeof as === 'function')
                as = as(obj, om, uimeta);
            if (typeof as === 'string')
                as = nameToActions(as, om, uimeta, sourceui);
            if (as === undefined)
                as = [];
            if (options && options.appendItemActions)
                Array.prototype.push.apply(as, options.appendItemActions);
        }
        if (as && as.length) {
            as = as.map(function (a) {
                if (typeof a === 'string')
                    a = nameToActions(a, om, uimeta, sourceui)[0];
                return a;
            });
            as = as.filter(function (a) {
                if (!a)
                    return false;
                return canAction(a, obj, uimeta, sourceui, options);
            });
        }
        return as;
    }
    _bean.getObjActions = getObjActions;
    function canAction(action, params, uimeta, sourceui, options) {
        if (uimeta === void 0) { uimeta = {}; }
        if (sourceui === void 0) { sourceui = {}; }
        if (options === void 0) { options = {}; }
        if (!params)
            return true;
        var obj = params, a = action;
        if (typeof a.if === 'function' && !a.if(obj))
            return false;
        else if (typeof a.if === 'string' && !eval(a.if))
            return false;
        return true;
    }
    _bean.canAction = canAction;
    function findObjAction(objectType, name, uimeta, source) {
        if (source === void 0) { source = undefined; }
        return getMeta(objectType).then(function (om) {
            return findAction(name, om, uimeta, source);
        });
    }
    _bean.findObjAction = findObjAction;
    function findObjActions(objectType, uimeta, obj, sourceui, options) {
        return getMeta(objectType).then(function (om) {
            return getObjActions(om, uimeta, obj, sourceui, options);
        });
    }
    _bean.findObjActions = findObjActions;
    function doObjAction(objectType, name, params, source, uimeta) {
        return findObjAction(objectType, name, uimeta).then(function (a) {
            a = a || _context.findAction(name);
            return _context.doAction(a, params, source);
        });
    }
    _bean.doObjAction = doObjAction;
    function form(objectType, formName, el, options) {
        return new ObjForm(objectType, formName, el, options).render();
    }
    _bean.form = form;
    function grid(objectType, gridName, el, options) {
        if (options === void 0) { options = {}; }
        return new Table(objectType, gridName, el, options).render();
    }
    _bean.grid = grid;
    function itemPopup(el, obj, options) {
        if (options === void 0) { options = {}; }
        var popupContent = $('<div class="ui items"></div>');
        new Item(obj.objectType, obj.id, null, popupContent, options).render();
        $(el).popup(Object.assign({ html: popupContent, preserve: true, hoverable: true, on: 'hover', position: 'right center' }, options));
    }
    _bean.itemPopup = itemPopup;
    function menuPopup(el, obj, options) {
        if (options === void 0) { options = {}; }
        var popupContent = $('<div class="ui items"></div>');
        new Menu(obj.objectType, obj.id, null, popupContent, options).render();
        $(el).popup(Object.assign({ html: popupContent, preserve: true, hoverable: true, on: 'hover', position: 'right center', delay: { hide: 900 } }, options));
        popupContent.parent().css("padding", "0px");
    }
    _bean.menuPopup = menuPopup;
    function menuDropdown(el, obj, objectType, options, source) {
        if (options === void 0) { options = {}; }
        var onShow = options.onShow, onHide = options.onHide;
        delete options.onShow;
        delete options.onHide;
        return getMeta(objectType || obj.objectType).then(function (om) {
            var actmap = {};
            var dsMap = function (a) {
                if (typeof a === 'string') {
                    a = a.trim();
                    a = findAction(a, om);
                }
                a.id = a.id || a.name || a.label;
                if (a.id)
                    actmap[a.id] = a;
                if (a.children && Array.isArray(a.children))
                    a.children = a.children.map(dsMap);
                return a;
            };
            var opt = Object.assign({ template: undefined, templateEl: el, allowEmpty: false, iconField: 'icon', titleField: 'label' }, options);
            if (!opt.dataProvider && !opt.data) {
                opt.data = function () {
                    var group = function (ls) {
                        var cats = {}, items = [];
                        ls.forEach(function (a) {
                            var cat = a.category;
                            if (cat) {
                                cats[cat] = cats[cat] || [];
                                cats[cat].push(a);
                            }
                            else
                                items.push(a);
                        });
                        for (var k in cats) {
                            items.push({
                                label: k, children: cats[k]
                            });
                        }
                        return items;
                    };
                    var itemActions = getObjActions(om, undefined, obj, undefined, {});
                    itemActions = group(itemActions);
                    var moreActions = getObjActions(om, undefined, obj, undefined, { isMoreItemActions: true });
                    moreActions = group(moreActions);
                    if (moreActions.length) {
                        itemActions.push({
                            label: '更多...',
                            children: moreActions
                        });
                    }
                    if (options.showTitle !== false) {
                        var titleText_1 = getTitle(obj), title = $('<div>').text(titleText_1).attr('title', titleText_1);
                        $('<i class="icon copy outline">').css({ position: 'absolute', right: '5px' }).appendTo(title).click(function () {
                            _util.copy(titleText_1, '复制成功');
                        });
                        itemActions.unshift({
                            label: title, class: 'header'
                        });
                    }
                    return itemActions.map(dsMap);
                };
            }
            if (opt.dataProvider && Array.isArray(opt.dataProvider))
                opt.dataProvider = opt.dataProvider.map(dsMap);
            if (opt.data && Array.isArray(opt.data))
                opt.data = opt.data.map(dsMap);
            var c = _sui.combo(el.parent(), opt);
            if (onShow)
                c.on('show', onShow);
            if (onHide)
                c.on('hide', onHide);
            c.on('change', function (e, actionId) {
                if (actionId && actmap[actionId])
                    _context.doAction(actmap[actionId], obj, source);
                else
                    return false;
            });
            return c;
        });
    }
    _bean.menuDropdown = menuDropdown;
    function showFormModal(objectType, formName, title, actions, formOptions, modalOptions) {
        if (formOptions === void 0) { formOptions = {}; }
        if (modalOptions === void 0) { modalOptions = {}; }
        var content = $('<div>');
        var fm = form(objectType, formName, content, formOptions);
        fm.rendered().then(function (e) {
            fm.modal = _sui.showCommonModal(title, content, actions, modalOptions);
        });
        return fm;
    }
    _bean.showFormModal = showFormModal;
    function getLastFormInput(objectType, formName) {
        formName = formName || '';
        return _util.getStorage("lastinput_".concat(objectType, "_").concat(formName));
    }
    function setLastFormInput(objectType, formName, data) {
        formName = formName || '';
        return _util.setStorage("lastinput_".concat(objectType, "_").concat(formName), data);
    }
    function findFormData(objectType, templateId, formName, templateType) {
        if (formName === void 0) { formName = null; }
        if (templateType === void 0) { templateType = null; }
        if (templateId === 'last')
            return getLastFormInput(objectType, formName);
        templateType = templateType + '.TEMPLATE';
        return findByForm(templateType, formName, templateId);
    }
    function getTemplateTypeMeta(objectType) {
        return getMetas(_bean.TemplateObjectTypeSuffix.map(function (s) { return objectType + s; }), false).then(function (oms) {
            return oms.find(function (om) { return om; });
        });
    }
    _bean.getTemplateTypeMeta = getTemplateTypeMeta;
    var viewedObjs = {};
    function showPostModal(objectType, title, data, formName, options) {
        if (data === void 0) { data = {}; }
        if (formName === void 0) { formName = null; }
        if (options === void 0) { options = {}; }
        data = data || {};
        var openMode = options.open_mode ? options.open_mode : _bean.defaultObjFormOpenMode;
        var icon = options.icon;
        var objFormModalEl;
        var toupdate = data.id;
        if (!toupdate && data._init === undefined)
            data._init = false;
        var readOnly = (options.readOnly === true);
        var closable = options.closable === true;
        var recentObjs = viewedObjs[objectType];
        var isAdding = options._is_adding || formName == 'add', showTemplates = options.showTemplates;
        if (showTemplates === undefined)
            showTemplates = !!isAdding;
        var okSubmit = options.okSubmit;
        if (okSubmit === undefined)
            okSubmit = isAdding || data.id;
        if (data.id) {
            if (!recentObjs)
                recentObjs = viewedObjs[objectType] = [];
            var ex = recentObjs.findIndex(function (i) { return i.id === data.id; });
            if (ex !== -1)
                recentObjs.splice(ex, 1);
            recentObjs.unshift(data);
            if (recentObjs.length > 10)
                recentObjs.length = 10;
            _context.events.trigger(_bean.EVENT_OBJ_UPDATEING, { objectType: objectType, data: data });
        }
        else if (isAdding) {
            if (_bean.autoInputData) {
                var d = _bean.autoInputData;
                if (typeof d === 'function')
                    d = d(objectType, data);
                if (d) {
                    data = Object.assign({}, d, data);
                }
            }
            _bean.autoInputDataFns.forEach(function (fn) {
                if (typeof fn === 'function')
                    fn = fn(objectType, data);
                if (fn) {
                    data = Object.assign({}, fn, data);
                }
            });
            _context.events.trigger(_bean.EVENT_OBJ_ADDING, { objectType: objectType, data: data });
        }
        var autoLastInput = !toupdate && _bean.autoLastInput;
        if (options.keepLast)
            data = Object.assign({}, getLastFormInput(objectType, formName), data);
        return new Promise(function (resolve, reject) {
            objFormModalEl = $('<div class="">');
            var content, actions;
            if (openMode === 'window') {
                objFormModalEl.css({ display: 'flex', "flex-direction": 'column', width: '100%', padding: '10px' });
                content = $('<div class="" style="flex:auto;overflow:auto;overflow-x:hidden;">').appendTo(objFormModalEl);
                actions = $('<div class="actions" style="flex:none;">').appendTo(objFormModalEl);
                if (options.actionTip)
                    actions.append(options.actionTip);
            }
            else {
                if (options.modalClass)
                    objFormModalEl.addClass(options.modalClass);
                if (title || icon) {
                    var h = $('<div class="header">').appendTo(objFormModalEl);
                    if (icon) {
                        if (typeof icon === 'string')
                            icon = '<i class="icon ' + icon + "></i>";
                        h.append(icon);
                    }
                    if (title)
                        h.append(title);
                }
                content = $('<div class="content" style="overflow:auto;">').appendTo(objFormModalEl);
                content.css('max-height', (parseInt($('body').height()) - 200) + 'px');
                actions = $('<div class="actions">').appendTo(objFormModalEl);
                if (options.actionTip)
                    actions.append(options.actionTip);
            }
            var theForm;
            if (options.extraActions) {
                options.extraActions.forEach(function (a) {
                    if (!_util.isjquery(a)) {
                        var btn = $('<div class="ui mini compact icon button">').attr('title', a.label).text(a.label).appendTo(actions);
                        if (a.icon) {
                            var iconEl = $('<i class="icon">').addClass(a.icon).prependTo(btn);
                            if (iconEl.hasClass('fa'))
                                iconEl.removeClass('icon');
                        }
                        if (a._ui_class)
                            btn.addClass(a._ui_class);
                        _sui.api(btn, function () {
                            return _context.doAction(a, theForm);
                        });
                    }
                    else
                        a.appendTo(actions);
                });
            }
            var ok, cancel;
            if (!readOnly) {
                ok = $('<div class="ui small primary compact button">').text(options.okText || _locale.bean.saveText).appendTo(actions);
                cancel = $('<div class="ui small cancel compact button">').text(options.cancelText || _locale.bean.cancelText).appendTo(actions);
            }
            else {
                if (!closable)
                    cancel = $('<div class="ui small cancel compact button">').text(options.cancelText || _locale.common.close).appendTo(actions);
            }
            if (closable && actions.is(':empty'))
                actions.hide();
            var formEl = $('<div>').appendTo(content);
            var objFormOptions = options.formOptions;
            if (data.id) {
                objFormOptions = objFormOptions || {};
                objFormOptions.formOptions = objFormOptions.formOptions || {};
                objFormOptions.formOptions.canRuleActionWork = function (name) {
                    if (name === 'genCode' && !this.dataLoaded)
                        return false;
                    return true;
                };
                objFormOptions.formOptions.applyDefaultIf = !toupdate;
            }
            var f = theForm = form(objectType, formName, formEl, Object.assign({ obj: data, readOnly: readOnly }, objFormOptions));
            if (ok && options.disableSubmitFirst !== false) {
                var disableSubmitFirst = options.disableSubmitFirst;
                if (disableSubmitFirst === undefined)
                    disableSubmitFirst = !!data.id;
                if (disableSubmitFirst) {
                    ok.addClass('disabled');
                    f.rendered().then(function () {
                        f.onChange(function () {
                            ok.removeClass('disabled');
                        });
                    });
                }
            }
            if (showTemplates !== false && !readOnly) {
                getTemplateTypeMeta(objectType).then(function (templateOm) {
                    var om = getMetaForce(objectType);
                    var cpbar = $('<div>').css({ position: 'absolute', top: '40px', right: '40px', zIndex: '99' }).prependTo(objFormModalEl);
                    objFormModalEl.hover(function (e) { return cpbar.show(); }, function (e) { return cpbar.hide(); });
                    var lastObj = getLastFormInput(objectType, formName);
                    var templateType = templateOm && templateOm.objectType;
                    var setformdata = function (t, isTemplate) {
                        if (isTemplate === void 0) { isTemplate = true; }
                        if (!t)
                            return;
                        var to = Object.assign({}, t);
                        delete to.id;
                        var needSkip = _context.getSetting('skipInputCopyFields');
                        for (var i in needSkip) {
                            var parentType = om.extendFrom;
                            if (i === "global" || parentType === i || i === objectType) {
                                needSkip[i].forEach(function (n) { return delete to[n]; });
                            }
                        }
                        _bean.fieldsNoTemplate.forEach(function (f) { return delete to[f]; });
                        for (var k in to) {
                            var formField = f.getField(k);
                            if (!formField || (formField.type !== 'text' && to[k] === ''))
                                delete to[k];
                        }
                        if (!isTemplate) {
                            for (var k in data) {
                                if (data[k] !== undefined)
                                    delete to[k];
                            }
                        }
                        var relationObjs = [];
                        for (var k in to) {
                            var objval = to[k];
                            var f_1 = om.findFormField(formName, k) || om.findField(k);
                            if (f_1.type === 'obj' && f_1.rtype && objval && (typeof objval === 'string' || typeof objval === 'number'))
                                relationObjs.push({ fieldName: k, objectType: f_1.rtype, id: objval });
                        }
                        Promise.all(relationObjs.map(function (r) {
                            return query(r.objectType, { id: r.id }).then(function (data) {
                                to[r.fieldName] = data;
                            });
                        })).then(function () {
                            f.forms.forEach(function (f) {
                                var newdata = Object.assign({}, to);
                                delete newdata._init;
                                f.setData(newdata);
                                if (isTemplate)
                                    f.baseData._templateId = t.id;
                            });
                        });
                    };
                    var onChange = function (item) {
                        if (item.indexOf('|') !== -1) {
                            var ss = item.split('|');
                            var recentObjType_1 = ss[0], recentId_1 = ss[1];
                            var o = recentObjs.find(function (i) { return i.objectType === recentObjType_1 && i.id === recentId_1; });
                            if (o)
                                setformdata(o, false);
                            return;
                        }
                        if (item === 'last')
                            setformdata(lastObj, false);
                        else if (item === 'save') {
                            var d_1 = f.getData();
                            _sui.prompt({ title: "".concat(_bean.strings.templateCreatePrompt), value: d_1.name || d_1.NAME }, function (name) {
                                d_1 = Object.assign({}, d_1, { NAME: name, name: name });
                                _bean.add(templateType, d_1);
                            });
                        }
                        else
                            findByForm(templateType, formName || 'add', { _templating: true, id: item, _showLoading: '正在查询模板数据...' }).then(function (t) {
                                setformdata(t);
                            });
                    };
                    f.rendered().then(function (e) {
                        if (options.templateId)
                            onChange(options.templateId);
                        else if (autoLastInput && lastObj)
                            onChange('last');
                    });
                    if (showTemplates !== false) {
                        if (lastObj) {
                            $("\n                            <i class=\"small clipboard icon\" style=\"cursor:pointer;\"></i>\n                        ").attr('title', _locale.bean.templateLastTime).prependTo(cpbar).click(function (e) { return onChange('last'); });
                        }
                        if (templateType)
                            query(templateType, { _queryFields: [templateOm.labelField, 'CODE', 'NAME'].join(',') }).then(function (templates) {
                                var templateDropdown = $("\n                                <div class=\"ui inline dropdown\">\n                                    <i class=\"small clone icon\" title=\"".concat(_locale.bean.templateChoose, "\"></i>\n                                    <div class=\"menu\" style=\"overflow: auto !important;height: 500px ;\"></div>\n                                </div>\n                            ")).prependTo(cpbar);
                                var p = templateDropdown.find('.menu');
                                $('<div class="ui input"><input class="searchtmp" type="text" /></div>').appendTo(p);
                                var fun = function (needJudge) {
                                    if (needJudge === void 0) { needJudge = false; }
                                    if (needJudge)
                                        templateDropdown.find('.menu .item').remove();
                                    templates.forEach(function (t) {
                                        var judgeResult = needJudge ? t[templateOm.labelField].startsWith(p.find('.searchtmp').val()) : true;
                                        if (judgeResult) {
                                            var item_1 = $('<div class="item">').text(t[templateOm.labelField]).attr('data-value', t.id).appendTo(p);
                                            var rmTemplate_1;
                                            if (_bean.templateHasConnview.indexOf(objectType) !== -1) {
                                                rmTemplate_1 = $("<i class=\"columns icon\" title=\"".concat(_locale.bean.connview, "\">")).appendTo(item_1).click(function (e) {
                                                    _context.doAction({ type: 'modal', name: 'connview', style: 'width: 1400px;height:850px' }, { objectType: templateType, id: t.id });
                                                    return false;
                                                }).hide();
                                            }
                                            if (rmTemplate_1) {
                                                item_1.on('mouseover', function (e) {
                                                    rmTemplate_1.show();
                                                });
                                                item_1.on('mouseout', function (e) {
                                                    rmTemplate_1.hide();
                                                });
                                            }
                                            else {
                                            }
                                        }
                                    });
                                    $('<div class="item">').attr('data-value', 'save').append('<i class="paste icon">').append("".concat(_bean.strings.saveAsTemplate)).css('font-size', 'smaller').appendTo(p);
                                    templateDropdown.dropdown({ onChange: onChange, action: 'hide', showOnFocus: false });
                                };
                                fun();
                                p.find('.searchtmp').on('input', fun);
                            });
                        if (recentObjs && recentObjs.length) {
                            var recentDropdown = $("\n                            <div class=\"ui inline dropdown\">\n                                <i class=\"small copy icon\" title=\"".concat(_bean.strings.inputCopyFromLast, "\"></i>\n                                <div class=\"menu\"></div>\n                            </div>\n                        ")).prependTo(cpbar), recentMenu_1 = recentDropdown.find('.menu');
                            recentObjs.forEach(function (t) {
                                $('<div class="item">').text(t.name || t.NAME || t.CODE).attr('data-value', t.objectType + '|' + t.id).appendTo(recentMenu_1);
                            });
                            recentDropdown.dropdown({ onChange: onChange, action: 'hide', showOnFocus: false });
                        }
                    }
                });
            }
            if (options.formCallback)
                options.formCallback(f);
            if (cancel)
                cancel.click(function (e) {
                    if (openMode === 'window') {
                        objFormModalEl.parents('.mdi')[0].__componentInstance.close();
                    }
                    if (reject)
                        reject();
                });
            if (!readOnly) {
                var successCallback_1 = function (e) {
                    if (openMode === 'window') {
                        objFormModalEl.parents('.mdi')[0].__componentInstance.close();
                    }
                    else {
                        _sui.closeModal(objFormModalEl);
                        objFormModalEl.remove();
                    }
                    objFormModalEl = null;
                    resolve(e);
                };
                if (ok) {
                    var okfn_1 = function () {
                        return f.validatePromise().then(function () {
                            var formData = toupdate ? f.getData(true) : f.getData(true, false);
                            setLastFormInput(objectType, formName, formData);
                            var ps = Object.assign({}, (toupdate ? { id: data.id } : data), formData);
                            ps._showLoading = true;
                            if (okSubmit) {
                                if (!_bean.formValidateFns.length)
                                    return toupdate ? update(objectType, ps) : add(objectType, ps);
                                return Promise.all(_bean.formValidateFns.map(function (fn) {
                                    try {
                                        var r = fn(objectType, ps, theForm, formName);
                                        return r;
                                    }
                                    catch (e) {
                                        _sui.alertError(e);
                                        return Promise.reject(e);
                                    }
                                })).then(function () {
                                    return toupdate ? update(objectType, ps, { source: options.source }) : add(objectType, ps, { source: options.source });
                                });
                            }
                            else
                                return ps;
                        });
                    };
                    _sui.api(ok, function () {
                        return okfn_1().then(successCallback_1, function () { });
                    }, true);
                }
            }
            if (openMode === 'window') {
                _sui.mdi({ width: '800px', style: 'max-height:90vh;', icon: icon, title: title, content: objFormModalEl, resizable: true, closable: closable }, false);
            }
            else
                _sui.showModal(objFormModalEl, { closable: closable, allowMultiple: true, padding: 0 });
        });
    }
    _bean.showPostModal = showPostModal;
    function categoryObjTabs(el, cats, actions, params) {
        if (actions === void 0) { actions = undefined; }
        if (params === void 0) { params = undefined; }
        var otitles = {}, tables = [];
        var objTypes = [];
        var activeType = '';
        var activeTableIndex = 0;
        cats.forEach(function (cat) {
            cat.children.forEach(function (item) {
                objTypes.push(item);
                if (item.tabActive && !activeType) {
                    activeType = item.objectType;
                }
                if (item.objectType) {
                    otitles[item.name || item.tagName] = item;
                    item.content = item.content || $('<div>');
                    var objgrid = grid(item.objectType, 'mini', item.content, Object.assign({ autoLoad: false, toolbar: false, showHead: true, rowCheckable: false }, item));
                    if (_context.map)
                        objgrid.on('rowclick', function (e, data) {
                            data = data.row;
                            _context.map.locate({ id: data.id, objectType: data.objectType, notify: false, zoomto: false, highlight: 5000, nullIfEmpty: true });
                        });
                    tables.push(objgrid);
                    item._tableInstance = objgrid;
                }
            });
        });
        var ctabs = new _sui.CategroyTabs(el, {
            data: cats, tabOptions: {
                onFirstLoad: function (e) {
                    var o = otitles[e] || otitles[e.replace('_', '/')];
                    if (o && o._tableInstance) {
                        o._tableInstance.rendered().then(function (t) {
                            if (!t.data)
                                t.load();
                        });
                    }
                }
            }
        });
        ctabs['getTables'] = function () {
            return tables;
        };
        ctabs['loadAllTables'] = function () {
            return Promise.all(tables.map(function (t) {
                if (t.data)
                    return Promise.resolve(t);
                return t.load();
            })).then(function () { return tables; });
        };
        ctabs.render().rendered().then(function (pane) {
            if (actions) {
                var menu = $("<div class=\"ui top right pointing dropdown item\">\n                    <i class=\"dropdown icon\"></i>\n                    <div class=\"menu\">\n                    </div>\n                </div>").css({ position: 'absolute', right: '20px', top: '3px' }).appendTo(pane.getEl());
                addGridMoreMenu(menu);
            }
        });
        function addGridMoreMenu(menu) {
            ctabs['moreMenu'] = menu;
            if (!actions) {
                return;
            }
            var menus = menu.find('.menu');
            menus.empty();
            actions.forEach(function (a) {
                if (a.gridMenuShowObjTypes) {
                    if (a.gridMenuShowObjTypes.split(',').indexOf(activeType) == -1) {
                        return;
                    }
                }
                var item = $('<div class="item">').text(a.title || a.label || a.text).appendTo(menus).click(function (e) {
                    _context.doAction(a, params, ctabs);
                });
                if (a.icon)
                    $('<i class="icon">').addClass(a.icon).prependTo(item);
            });
            menu.dropdown();
        }
        var tabMenus = ctabs.getTabMenus();
        if (tabMenus) {
            ctabs.on('change', function (e) {
                var moreMenu = ctabs['moreMenu'];
                activeTableIndex = tabMenus[0].getActiveTabIndex();
                if (objTypes[activeTableIndex]) {
                    activeType = objTypes[activeTableIndex].objectType;
                    addGridMoreMenu(moreMenu);
                }
            });
        }
        ctabs['getActiveTableIndex'] = function () {
            return activeTableIndex;
        };
        ctabs['getActiveTable'] = function () {
            return tables[activeTableIndex];
        };
        return ctabs;
    }
    _bean.categoryObjTabs = categoryObjTabs;
    var objectsModalEl;
    function showCategoryObjTabsModal(title, cats) {
        if (!objectsModalEl) {
            objectsModalEl = $('<div class="ui long modal"><div class="header"></div><div class="content" style="height: 60vh;overflow:auto"></div><div>').appendTo('body');
        }
        objectsModalEl.find('.header').html(title);
        var w = objectsModalEl.find('.content').empty();
        categoryObjTabs(w, cats);
        _sui.showModal(objectsModalEl);
    }
    _bean.showCategoryObjTabsModal = showCategoryObjTabsModal;
    var objectsMessageEl;
    function showObjectsMessage(callback) {
        if (callback === false) {
            if (objectsMessageEl) {
                objectsMessageEl.remove();
                objectsMessageEl = null;
            }
            return;
        }
        if (!objectsMessageEl) {
            objectsMessageEl = $("\n                <div class=\"ui message\" style=\"display:none;min-width:60vw;position:absolute;left:50%;bottom:0;margin:1em 0;transform:translateX(-50%);max-height:50vh;overflow-y:auto;\">\n                    <div class=\"content ui items\" style=\"margin-bottom: 0;\"></div>\n                    <i class=\"small close icon\"></i>\n                </div>\n            ").appendTo('body');
            objectsMessageEl.children('.close').click(function (e) { return objectsMessageEl.transition('fade'); });
        }
        var _show = function () {
            var toshow = callback;
            if (typeof callback === 'function')
                toshow = callback(objectsMessageEl.children('.content').empty(), objectsMessageEl);
            if (_util.isPromise(toshow))
                toshow.then(function (e) { return objectsMessageEl.transition('fade'); });
            else if (toshow !== false)
                objectsMessageEl.transition('fade');
        };
        if (objectsMessageEl.is(':visible'))
            objectsMessageEl.transition({ onComplete: _show });
        else
            _show();
    }
    _bean.showObjectsMessage = showObjectsMessage;
    function showCategoryObjTabsPane(cats, params) {
        if (params === void 0) { params = undefined; }
        showObjectsMessage(function (c) { return categoryObjTabs(c, cats, _context.findActions(function (a) { return a.target === "*"; }), params); });
    }
    _bean.showCategoryObjTabsPane = showCategoryObjTabsPane;
    function showObjectPane(objectType, id, obj) {
        if (obj === void 0) { obj = null; }
        showObjectsMessage(function (c) {
            return getMeta(objectType).then(function (om) {
                return new Promise(function (resolve) {
                    var opt = {};
                    if (obj && om.reloadOnObjTip === false)
                        opt.data = obj;
                    var item = new Item(objectType, id, null, c, opt);
                    item.on(_sui.EVENT_COMPONENT_RENDERED, function (e) { return resolve(item); });
                    item.render();
                });
            });
        });
    }
    _bean.showObjectPane = showObjectPane;
    function showGridModal(objectType, gridName, gridOptions, header, modalOptions, actions) {
        if (actions === void 0) { actions = undefined; }
        var el = $('<div>');
        _bean.grid(objectType, gridName, el, gridOptions);
        if (actions === undefined && modalOptions && modalOptions.closable === false) {
            actions = [
                { label: '关闭', class: 'cancel' }
            ];
        }
        return _sui.showCommonModal(header, el, actions, modalOptions);
    }
    _bean.showGridModal = showGridModal;
    function showObjPane(obj, catsNode) {
        if (catsNode === void 0) { catsNode = null; }
        if (Array.isArray(obj))
            obj = obj.filter(function (o) { return o.objectType; });
        if (!obj || (Array.isArray(obj) && !obj.length)) {
            return showObjectsMessage(false);
        }
        if (obj.length === 1)
            obj = obj[0];
        if (Array.isArray(obj)) {
            var typeobjs_1 = {};
            var needLoadOm_1 = [];
            obj.forEach(function (o) {
                var om = getMetaForce(o.objectType);
                if (!om && needLoadOm_1.indexOf(o.objectType) === -1)
                    needLoadOm_1.push(o.objectType);
                var data = typeobjs_1[o.objectType] = typeobjs_1[o.objectType] || [];
                data.push(o);
            });
            if (needLoadOm_1.length) {
                return getMetas(needLoadOm_1).then(function (oms) {
                    var pass = true;
                    needLoadOm_1.forEach(function (t) {
                        if (!getMetaForce(t)) {
                            console.error('objmeta not found: ', t);
                            pass = false;
                        }
                    });
                    if (pass)
                        showObjPane(obj);
                });
            }
            catsNode = catsNode || _context.configXml.find('ObjCategory');
            if (!catsNode.length) {
                catsNode = [{ name: '所有', children: [] }];
                for (var k in typeobjs_1) {
                    var om = getMetaForce(k);
                    var label = om ? om.label : k;
                    catsNode[0].children.push({ objectType: k, name: label });
                }
            }
            else
                catsNode = _util.jQueryToPlain(catsNode)[0].children;
            catsNode.forEach(function (cat) {
                cat.name = cat.name || cat.tagName;
                var catnum = 0;
                cat.children.forEach(function (item) {
                    item.name = item.name || item.tagName;
                    item.footer = false;
                    item.data = typeobjs_1[item.objectType];
                    if (item.data) {
                        var ds_1 = item.data, isParam_1 = ds_1.length === 1 && ds_1[0].baseParams;
                        var label_1 = item.title || item.name;
                        var title = label_1;
                        var count_1 = ds_1.length;
                        if (isParam_1)
                            count_1 = ds_1[0].count;
                        if (count_1 || count_1 === 0) {
                            title = label_1 + '(' + count_1 + ')';
                            catnum += count_1;
                        }
                        var icon = $('<i class="table icon">').click(function (e) {
                            var ac = { type: 'workspace', objectType: item.objectType, title: label_1, name: item.objectType + Math.random() };
                            if (isParam_1)
                                ac.options = ds_1[0];
                            else
                                ac.ids = ds_1.map(function (d) { return d.id; }).join(',');
                            _context.doAction(ac);
                            return false;
                        });
                        item.title = $('<div>').append(icon).append(title);
                        item.baseParams = isParam_1 ? ds_1[0].baseParams : { id: ds_1.map(function (d) { return d.id; }).join(',') };
                        delete item.data;
                        item.tabActive = true;
                    }
                    else
                        item.data = [];
                });
                if (catnum > 0) {
                    cat.title = (cat.title || cat.name) + '(' + catnum + ')';
                    cat.tabActive = true;
                }
            });
            showCategoryObjTabsPane(catsNode, obj);
        }
        else
            this.showObjectPane(obj.objectType, obj.id, obj);
    }
    _bean.showObjPane = showObjPane;
    if (window['_context']) {
        var conf = window['_context'].clientConf;
        if (conf && conf._beanDefaults)
            Object.assign(_bean, conf._beanDefaults);
    }
    var Item = (function (_super) {
        __extends(Item, _super);
        function Item(objectType, id, uimeta, parentEl, options) {
            if (options === void 0) { options = {}; }
            var _this = _super.call(this, parentEl, options) || this;
            _this.objectType = objectType;
            _this.id = id;
            _this.uimeta = uimeta;
            _this.human = true;
            return _this;
        }
        Item.prototype.prepare = function () {
            var _this = this;
            if (this.objectType)
                return getMeta(this.objectType).then(function (om) {
                    if (typeof _this.uimeta === 'string')
                        _this.uimeta = findMetaByName(om.grids, _this.uimeta);
                    var uimeta = _this.uimeta || {};
                    if (_this.titleField === undefined)
                        _this.titleField = uimeta.titleField;
                    if (_this.titleField === undefined)
                        _this.titleField = om.titleField;
                    if (_this.titleField === undefined)
                        _this.titleField = om.labelField;
                    if (!_this.emptyHeaderText && _locale)
                        _this.emptyHeaderText = _locale.unknownText;
                    _this.descriptionField = _this.descriptionField || uimeta.descriptionField || om.descriptionField;
                    var fs = _this.options.itemDescriptionFields || om.itemDescriptionFields;
                    if (_this.descriptionField === undefined && fs) {
                        if (typeof fs === 'string')
                            fs = fs.split(',');
                        _this.descriptionField = function (obj) {
                            var div = $('<div class="ui grid">');
                            fs.forEach(function (f) {
                                if (typeof f === 'string')
                                    f = om.findField(f.trim());
                                if (f) {
                                    var row = $('<div>').css({}).appendTo(div);
                                    $('<label>').css({ fontWeight: 'bold', fontSize: 'smaller', padding: '2px' }).text(f.label || f.name).appendTo(row);
                                    $('<span>').text(obj[f.name]).appendTo(row);
                                }
                            });
                            return div;
                        };
                    }
                    _this.imageField = _this.imageField || uimeta.imageField || om.imageField;
                    if (_this.imageField === undefined)
                        _this.imageField = false;
                    if (_this.imageField && !_this.imageAction) {
                        _this.imageAction = uimeta.imageAction || om.imageAction;
                    }
                    _this.metasField = _this.metasField || uimeta.itemMetasField || om.itemMetasField;
                    _this.labelsField = _this.labelsField || uimeta.labelsField || om.itemLabelsField;
                    if (_this.options.sourceObject && !_this.data) {
                        var reloadOnObjTip = uimeta.reloadOnObjTip;
                        if (reloadOnObjTip === undefined)
                            reloadOnObjTip = om.reloadOnObjTip;
                        if (reloadOnObjTip === false) {
                            _this.data = _this.options.sourceObject.properties || _this.options.sourceObject;
                        }
                    }
                    if (!_this.doActionFn)
                        _this.doActionFn = _context.doAction;
                    if (!_this.headerAction)
                        _this.headerAction = om.headerAction ? nameToActions(om.headerAction, om, uimeta, _this)[0] : undefined;
                    var itemAssemble = om.itemAssemble;
                    if (uimeta && uimeta.itemAssemble)
                        itemAssemble = uimeta.itemAssemble;
                    if (!_this.data && _this.id)
                        return find(_this.objectType, { id: _this.id, _assemble: itemAssemble, _human: _this.human }).then(function (obj) {
                            _this.data = obj;
                            if (!_this.actionsField)
                                _this.actionsField = getObjActions(om, uimeta, obj, _this, _this.options);
                            if (!_this.moreActionsField)
                                _this.moreActionsField = getObjActions(om, uimeta, obj, _this, Object.assign({ isMoreItemActions: true }, _this.options));
                            return obj;
                        });
                    else if (!_this.actionsField)
                        _this.actionsField = getObjActions(om, uimeta, _this.data || { id: _this.id }, _this, _this.options);
                });
        };
        return Item;
    }(_sui.Item));
    _bean.Item = Item;
    function item(objectType, id, uimeta, el, options) {
        if (options === void 0) { options = {}; }
        return new Item(objectType, id, uimeta, el, options).render();
    }
    _bean.item = item;
    var Menu = (function (_super) {
        __extends(Menu, _super);
        function Menu(objectType, id, uimeta, parentEl, options) {
            if (options === void 0) { options = {}; }
            var _this = _super.call(this, parentEl, options) || this;
            _this.objectType = objectType;
            _this.id = id;
            _this.uimeta = uimeta;
            _this.human = true;
            return _this;
        }
        Menu.prototype.prepare = function () {
            var _this = this;
            if (this.objectType)
                return getMeta(this.objectType).then(function (om) {
                    if (!_this.doActionFn)
                        _this.doActionFn = _context.doAction;
                    var itemAssemble = om.itemAssemble;
                    if (!_this.data && _this.id)
                        return find(_this.objectType, { id: _this.id, _assemble: itemAssemble, _human: _this.human }).then(function (obj) {
                            _this.data = obj;
                            if (!_this.actionsField)
                                _this.actionsField = getObjActions(om, null, obj, _this, _this.options);
                            return obj;
                        });
                    else if (!_this.actionsField)
                        _this.actionsField = getObjActions(om, null, _this.data, _this, _this.options);
                });
        };
        return Menu;
    }(_sui.Menu));
    _bean.Menu = Menu;
    var Table = (function (_super) {
        __extends(Table, _super);
        function Table(objectType, gridName, parentEl, options) {
            if (options === void 0) { options = {}; }
            var _this = _super.call(this, parentEl, options) || this;
            _this.objectType = objectType;
            _this.gridName = gridName;
            _this.autoLoad = true;
            _this.pageSize = _bean.defaultGridPageSize;
            _this.remoteSort = true;
            _this.showSearch = undefined;
            _this.searchFormName = 'search';
            _this.dataLoaded = false;
            _this.toolbar = "\n            <div class=\"ui attached menu\" style=\"z-index: 2\">\n                <div class=\"ui search item\" style=\"width: 40em;\">\n                    <div class=\"ui icon input\">\n                        <input style=\"border-color:transparent\" type=\"text\" placeholder=\"".concat(_bean.strings.keywordSearch, "\">\n                        <i class=\"search link icon\" style=\"right:2em\" id=\"_start_search_icon\" style=\"cursor:pointer;\"></i>\n                        <i class=\"keyboard link icon\" id=\"_advance_search_icon\" style=\"cursor:pointer;\"></i>\n                    </div>\n                    <div class=\"results\"></div>\n                </div>\n                <div class=\"menu actionbar\"></div>\n                <div class=\"ui right dropdown item\">\n                    <i class=\"list icon\"></i>\n                    <i class=\"dropdown icon\"></i>\n                    <div class=\"menu\">\n                    </div>\n                </div>\n            </div>\n        ");
            _this.footer = "\n            <span class=\"pagesizediv\" style=\"font-size:10px;color:gray;\">\n                <span id=\"a_jump\" style=\"cursor:pointer\">\u8DF3\u8F6C</span>\n                <label>\u6BCF\u9875\u8BB0\u5F55\u6570</label>\n                <div id=\"a_choose\" class=\"ui inline dropdown\">\n                    <div class=\"text\"></div>\n                    <i class=\"dropdown icon\"></i>\n                </div>\n            </span>\n            <label class=\"pagingtext\" style=\"font-size:10px;color:gray;\"></label>\n\n            <div class=\"ui mini compact basic buttons\" style=\"float: right;padding-left:20px;border:0;\">\n                <button class=\"ui compact icon button prepage\" style=\"border:0;\">\n                    <i class=\"left chevron icon\"></i>\n                </button>\n                <button class=\"ui compact icon button nextpage\" style=\"border:0;\">\n                    <i class=\"right chevron icon\"></i>\n                </button>\n                <button class=\"ui compact icon button reloadpage\" style=\"border:0;\">\n                    <i class=\"refresh icon\"></i>\n                </button>\n            </div>\n        ";
            _this.rowDetailField = function (r) { return '<div class="ui items _objpopup" style="padding:5px">'; };
            _this.detailQuery = true;
            _this.currentPageNum = 0;
            _this.listMode = false;
            _this.searchPopupPosition = 'bottom left';
            _this.pagable = undefined;
            _this.queryFields = undefined;
            _this._objCache = {};
            return _this;
        }
        Table.prototype.prepare = function () {
            var _this = this;
            if (this.listMode && this.options.showHead === undefined)
                this.showHead = false;
            return getMeta(this.objectType).then(function (om) {
                _this.om = om;
                if (_this.metaGrid) {
                    if (!_this.metaGrid.fields) {
                        _this.metaGrid.fields = findMetaByName(om.grids, _this.gridName).fields;
                    }
                    else
                        _this.metaGrid.fields = _this.metaGrid.fields.map(function (f) {
                            var ff = om.findField(f.name);
                            if (ff)
                                for (var k in ff) {
                                    if (f[k] === undefined)
                                        f[k] = ff[k];
                                }
                            return f;
                        });
                }
                else
                    _this.metaGrid = findMetaByName(om.grids, _this.gridName);
                var grid = _this.metaGrid;
                if (_this.queryObjectType === undefined)
                    _this.queryObjectType = grid.queryObjectType;
                var scripts = grid.scripts;
                if (scripts && scripts.length) {
                    _this.script = scripts[0].content;
                }
                var fields = grid.fields;
                _this.columns = fields.filter(function (f) { return f.visible !== false; });
                _this.itemAssemble = _this.itemAssemble || grid.itemAssemble || om.itemAssemble;
                _this.tableStyle = _this.tableStyle || grid.tableStyle || om.tableStyle;
                _this.tableClass = _this.tableClass || grid.tableClass || om.tableClass;
                for (var k in grid) {
                    var v = grid[k];
                    if (k !== 'style' && k !== 'class' && _this.options[k] === undefined)
                        _this[k] = v;
                }
                if (_this.pagable === false)
                    _this.pageSize = -1;
                if (_this.actions === undefined) {
                    var typeActions = _this.options.typeActions;
                    if (typeActions === undefined)
                        typeActions = grid.typeActions;
                    if (typeActions === undefined)
                        typeActions = om.typeActions;
                    _this.actions = nameToActions(typeActions, om, _this.metaGrid, _this);
                }
                if (_this.reloadByEventObjType === undefined) {
                    _this.reloadByEventObjType = om.autoReload;
                }
                if (_this.actions)
                    _this.actions.forEach(function (a) { return a._metagrid = _this.gridName || ''; });
                if (_this.extraActions === undefined) {
                    var extraActions = grid.extraActions;
                    if (!extraActions && extraActions !== '')
                        extraActions = om.extraActions;
                    _this.extraActions = extraActions;
                }
                if (typeof _this.extraActions === 'string')
                    _this.extraActions = nameToActions(_this.extraActions, om, _this.metaGrid, _this);
                if (_this.extraActions)
                    _this.extraActions.forEach(function (a) { return a._metagrid = _this.gridName || ''; });
                if (_this.data) {
                    _this.autoLoad = false;
                    _this.pageSize = -1;
                    if (_this.showSearch === undefined)
                        _this.showSearch = false;
                }
                if (_this.showSearch === undefined)
                    _this.showSearch = true;
                if (_this.pageSize <= 0)
                    _this.footer = null;
                if (_this.queryActionName) {
                    if (_this.human === undefined)
                        _this.human = false;
                }
                if (_this.human === undefined)
                    _this.human = true;
                _this.doActionFn = _this.doActionFn || _context.doAction;
                if (_this.queryObjectType === undefined)
                    _this.queryObjectType = _this.objectType;
                if (_this.orderBy === false)
                    _this.sortable = false;
                if (!_this.toolbar && _this.showSearch === 'expand' || _this.showSearch === 'collapse')
                    _this.toolbar = '<div>';
                return om;
            });
        };
        Table.prototype.computeNextSortDir = function (currentDir, col) {
            if (currentDir === 'desc'
                && this.remoteSort !== false && this.pageSize !== -1 && col.remoteSort !== false && col.name && col.name.indexOf('.') === -1 && !col.getLabel)
                return null;
            return _super.prototype.computeNextSortDir.call(this, currentDir, col);
        };
        Table.prototype.doSortRows = function (col, dir, th) {
            var shouldRemote = this.remoteSort;
            if (shouldRemote && this.lastPageResult) {
                if (!this.lastPageResult.total || this.lastPageResult.total <= this.lastPageResult.data.length)
                    shouldRemote = false;
            }
            if (shouldRemote)
                shouldRemote = this.pageSize !== -1 && col.remoteSort !== false && col.name && col.name.indexOf('.') === -1 && !col.getLabel;
            if (shouldRemote) {
                this.reload();
            }
            else
                _super.prototype.doSortRows.call(this, col, dir, th);
        };
        Table.prototype.getLabel = function (row, col, rowNum) {
            var _this = this;
            if (col.type === 'dict' && col.fixNumber === undefined)
                col.fixNumber = false;
            var label = _super.prototype.getLabel.call(this, row, col, rowNum);
            if (label && typeof label === 'object' && label.objectType && label.id)
                label = getTitle(label);
            var val = row[col.name];
            if (val === undefined && col.name && col.name.indexOf('.') !== -1) {
                try {
                    val = eval('row.' + col.name);
                }
                catch (e) { }
            }
            if (col.type === 'dict' && (row[col.name + '_value'] === undefined || row[col.name + '_value'] == label) && val == label) {
                var dictValue_1 = label;
                if (dictValue_1 || dictValue_1 === 0) {
                    label = $('<span>').text(label);
                    dict(col.dictObjectType || col.dtype || this.objectType, col.dattr || col.name).then(function (items) {
                        var v = items.find(function (i) { return i.value == dictValue_1; });
                        if (v)
                            label.text(v.name);
                        else if (typeof dictValue_1 === 'string' && dictValue_1.indexOf(',') !== -1) {
                            var ss = '', ds_2 = dictValue_1.split(',');
                            var _loop_1 = function (i) {
                                v = items.find(function (it) { return it.value == ds_2[i].trim(); });
                                ss += v ? v.name : ds_2[i].trim();
                                if (i !== ds_2.length - 1)
                                    ss += ',';
                            };
                            for (var i = 0; i < ds_2.length; i++) {
                                _loop_1(i);
                            }
                            if (ss)
                                label.text(ss);
                        }
                        else
                            label.text(dictValue_1);
                    });
                }
            }
            else if (col.type === 'obj' && row[col.name + '_value'] === undefined && val == label) {
                var objId_1 = label;
                if (objId_1 && this.testStringId(objId_1 + '')) {
                    label = $('<span>').text(label);
                    var ro_1 = function (obj) {
                        if (obj)
                            label.text(col.labelField ? obj[col.labelField] : getTitle(obj));
                    };
                    var obj = this._objCache[objId_1];
                    var evtname_1 = 'findobj_' + objId_1;
                    if (obj === true)
                        this.on(evtname_1, function (e, o) { return ro_1(o); });
                    else if (obj)
                        ro_1(obj);
                    else if (obj !== false) {
                        this._objCache[objId_1] = true;
                        find(col.rtype, objId_1).then(function (obj) {
                            if (obj) {
                                _this._objCache[objId_1] = obj;
                                ro_1(obj);
                                _this.trigger(evtname_1, obj);
                            }
                            else
                                _this._objCache[objId_1] = false;
                        });
                    }
                }
            }
            return label;
        };
        Table.prototype.testStringId = function (idString) {
            return idString && /^[0-9]*$/.test(idString);
        };
        Table.prototype.getComponentForEdit = function (row, col, td) {
            var fieldType = col.type;
            if (fieldType == "dict") {
                this.getDictComponentForEdit(row, col, td);
            }
            else if (fieldType == "obj") {
                this.getObjComponentForEdit(row, col, td);
            }
            else {
                _super.prototype.getComponentForEdit.call(this, row, col, td);
            }
        };
        Table.prototype.getDictComponentForEdit = function (row, col, td) {
            var _this = this;
            var dtype = col.dtype, self = this;
            if (typeof (dtype) == "undefined") {
                dtype = this.objectType;
            }
            var dattr = col.dattr;
            if (typeof (dattr) == "undefined") {
                dattr = col.name;
            }
            td.dblclick(function (e) {
                var editTr = "<div class=\"inner-dictCombo\"></div>";
                if (td.find(".inner-dictCombo").length > 0) {
                    e.preventDefault();
                    return;
                }
                if (_context.lastActiveObjCombo) {
                    _context.lastActiveObjCombo.callback();
                    _context.lastActiveObjCombo = null;
                }
                var replaceEl = $(editTr);
                var fieldname = col.name;
                var thizHuman = _this.human;
                if (thizHuman) {
                    fieldname = col.name + "_value";
                }
                var originValue = row[fieldname] || row[col.name];
                replaceEl.empty();
                var dictCombo = _bean.dictcombo(dtype, dattr, replaceEl, { value: originValue });
                dictCombo.rendered().then(function () {
                    _context.lastActiveObjCombo = { combo: dictCombo, callback: endEditTd };
                    replaceEl.find(".ui.selection").focus();
                    replaceEl.find(".ui.selection").css("min-width", 0);
                    dictCombo.on('change', function () {
                        endEditTd(false);
                    });
                });
                td.empty();
                td.append(replaceEl);
                td.toggleClass("editable");
                td.keydown(function (e) {
                    if (e.keyCode == "13" || e.keyCode == "27") {
                        endEditTd(e.keyCode == "27");
                    }
                });
                function endEditTd(giveup) {
                    if (td.hasClass("editable")) {
                        var selectItem = dictCombo.getSelectedItem();
                        td.toggleClass("editable");
                        td.empty();
                        if (giveup) {
                            td.text(td._originText);
                        }
                        else {
                            if (selectItem != null) {
                                var selectText = selectItem.name;
                                td.text(selectText);
                                row[col.name] = selectText;
                                row[fieldname] = selectItem.id;
                            }
                            else {
                                row[col.name] = '';
                                row[fieldname] = null;
                            }
                        }
                    }
                    self.markCellDirty(td, col.cellEditDirtyClass);
                }
            });
        };
        Table.prototype.getObjComponentForEdit = function (row, col, td) {
            var _this = this;
            var rtype = col.rtype, self = this;
            td.dblclick(function (e) {
                var editTr = "<div class=\"inner-objCombo\"></div>";
                if (td.find(".inner-objCombo").length > 0) {
                    e.preventDefault();
                    return;
                }
                if (_context.lastActiveObjCombo) {
                    _context.lastActiveObjCombo.callback();
                    _context.lastActiveObjCombo = null;
                }
                var replaceEl = $(editTr);
                var fieldname = col.name;
                var thizHuman = _this.human;
                if (thizHuman) {
                    fieldname = col.name + "_value";
                }
                var originObj = row[fieldname] || row[col.name];
                replaceEl.empty();
                function getObj(value) {
                    var valueIsNull = typeof value == "undefined" || value == null || value == '';
                    var isObj = typeof value == "object";
                    var pattern = /^[0-9]{15,}$/;
                    var isId = pattern.test(value);
                    if (valueIsNull) {
                        value = null;
                    }
                    if (isObj || isId || valueIsNull) {
                        return new Promise(function (resolve, reject) {
                            resolve(value);
                        });
                    }
                    else {
                        var defaultSearchField = "CODE";
                        var findParam = {};
                        if (col.searchField) {
                            defaultSearchField = col.searchField;
                        }
                        findParam[defaultSearchField] = value;
                        findParam["_op." + defaultSearchField] = "=";
                        return find(col.rtype, findParam);
                    }
                }
                var objCombo;
                getObj(originObj).then(function (obj) {
                    var dropdownOptions = {};
                    if (obj) {
                        dropdownOptions['value'] = obj;
                    }
                    objCombo = new ObjCombo(rtype, replaceEl, Object.assign({ inputName: col.name }, dropdownOptions)).render();
                    objCombo.rendered().then(function () {
                        replaceEl.find(".ui.selection").css("min-width", 0);
                        _context.lastActiveObjCombo = { combo: objCombo, callback: endEditTd };
                        replaceEl.focus();
                        setTimeout(function () {
                            objCombo.on('change', function () {
                                endEditTd(false);
                            });
                        }, 300);
                    });
                });
                td.empty();
                td.append(replaceEl);
                td.toggleClass("editable");
                td.keydown(function (e) {
                    if (e.keyCode == "27") {
                        endEditTd(true);
                    }
                });
                function endEditTd(giveup) {
                    if (td.hasClass("editable") && objCombo) {
                        var selectItem_1 = objCombo.getSelectedItem();
                        td.toggleClass("editable");
                        td.empty();
                        if (giveup) {
                            td.text(td._originText);
                        }
                        else {
                            if (selectItem_1 != null) {
                                getMeta(rtype).then(function (om) {
                                    var defaultLabelField = 'NAME';
                                    if (om.labelField)
                                        defaultLabelField = om.labelField;
                                    var selectText = selectItem_1[defaultLabelField];
                                    td.text(selectText);
                                    row[col.name] = selectText;
                                    row[fieldname] = selectItem_1;
                                    self.markCellDirty(td, col.cellEditDirtyClass);
                                });
                            }
                            else {
                                row[col.name] = '';
                                row[fieldname] = null;
                            }
                        }
                    }
                    self.markCellDirty(td, col.cellEditDirtyClass);
                }
            });
        };
        Table.prototype.doRender = function (el, pre) {
            var _this = this;
            _super.prototype.doRender.call(this, el, pre);
            var self = this;
            if (typeof this.baseParams === 'string')
                this.baseParams = parsekv(this.baseParams);
            if (this.toolbar) {
                if (this.actions)
                    this.actions.forEach(function (action) { return _this.appendAction(action, _this.toolbar.find('.actionbar')); });
                var extraActionsEl_1 = this.toolbar.find('.dropdown .menu');
                if (this.extraActions && this.extraActions.length) {
                    this.extraActions.forEach(function (action) { return _this.appendAction(action, extraActionsEl_1); });
                    this.toolbar.find('.dropdown').dropdown({ on: 'hover' });
                }
                else
                    extraActionsEl_1.parent().hide();
                this.on('checkchange', function (e) { return _this.resetActions(); });
                if (this.showSearch) {
                    var search_1 = this.toolbar.find('.search input');
                    var searchIcon_1 = this.toolbar.find('#_advance_search_icon'), startSearchIcon = this.toolbar.find('#_start_search_icon');
                    var l_1 = function () {
                        _this.baseParams = _this.baseParams || {};
                        var query = search_1.val().trim();
                        _this.baseParams.queryText = query;
                        var ps = { _fastsearch: true, queryTextEqFirst: true };
                        var nocase = getObjectTypeSetting(_this.objectType, 'search_nocase');
                        if (nocase !== undefined)
                            ps._nocase = nocase;
                        var searchlike = getObjectTypeSetting(_this.objectType, 'search_like');
                        if (searchlike !== undefined)
                            ps._op = 'like';
                        _this.load(ps);
                    };
                    search_1.keypress(function (e) {
                        if (e.which == 13)
                            l_1();
                    });
                    startSearchIcon.click(l_1);
                    var searchPopup_1;
                    if (this.showSearch === 'expand' || this.showSearch === 'collapse') {
                        this.toolbar.find('.search').remove();
                        searchPopup_1 = $('<div>').prependTo(this.toolbar.parent());
                        var btnbar = $('<div><label style="font-size:8px;cursor:pointer;padding:2px;">查询条件</label></div>').css({ textAlign: 'right', color: 'gray', cursor: 'pointer' }).prependTo(searchPopup_1.parent());
                        var btn_1 = $('<i class="icon up angle">').appendTo(btnbar);
                        btnbar.click(function (e) {
                            if (searchPopup_1.is(':visible')) {
                                searchPopup_1.hide();
                                btn_1.addClass('down').removeClass('up');
                            }
                            else {
                                searchPopup_1.show();
                                btn_1.addClass('up').removeClass('down');
                            }
                        });
                        if (this.showSearch === 'collapse')
                            btnbar.click();
                    }
                    else {
                        searchPopup_1 = $('<div class="ui popup grid-search-popup">');
                        Promise.resolve().then(function () {
                            searchPopup_1.appendTo(searchIcon_1.parents('.modal').length > 0 ? searchIcon_1.parents('.modal') : searchIcon_1.parent());
                        });
                    }
                    var searchFormEl_1 = $('<div>').appendTo(searchPopup_1).css({ maxHeight: '60vh', overflow: 'auto' });
                    var searchBar = $('<div class="form_search_bar">').appendTo(searchPopup_1);
                    var ops_1 = {};
                    var searchOption = {
                        showAssembles: false, ignoreFormTab: true, formOptions: {
                            ignoreRequired: true, fieldDecorator: function (field) {
                                var nf = Object.assign({}, field);
                                nf.title = nf.title || nf.label;
                                nf.label = $('<span>').text(field.label);
                                if (nf.type === 'date' || nf.type === 'datetime') {
                                    if (nf.daterange === undefined)
                                        nf.daterange = true;
                                }
                                var dp = $('<select>').css({
                                    float: 'right', width: 'unset', appearance: 'none', display: 'inline', border: 'none', padding: '2px', fontSize: '8px', userSelect: 'none', outline: 'none'
                                }).appendTo(nf.label).change(function () {
                                    dp.attr('title', dp.find('option:selected').attr('title'));
                                });
                                ops_1[field.name] = dp.hide();
                                return nf;
                            }
                        }
                    };
                    if (this.options.searchOption) {
                        searchOption = Object.assign(searchOption, this.options.searchOption);
                    }
                    if (this.searchBaseParam) {
                        this.searchBaseParam = this.searchBaseParam.replace(/'/g, '"');
                        searchOption['obj'] = JSON.parse(this.searchBaseParam);
                    }
                    var metaSearchForm = findMetaByName(this.om.forms, this.searchFormName);
                    var formClass = this.om.formSearchBarClass, formStyle = this.om.formSearchBarStyle;
                    if (metaSearchForm) {
                        if (metaSearchForm.searchBarClass)
                            formClass = metaSearchForm.searchBarClass;
                        if (metaSearchForm.searchBarStyle)
                            formStyle = metaSearchForm.searchBarStyle;
                    }
                    if (formClass)
                        searchBar.addClass(formClass);
                    if (formStyle)
                        _sui.setStyle(searchBar, formStyle);
                    var f_2 = form(this.objectType, this.searchFormName, searchFormEl_1, searchOption);
                    setTimeout(function () {
                        searchIcon_1.popup({
                            popup: searchPopup_1,
                            lastResort: _this.searchPopupPosition, movePopup: false, on: 'click', delay: { hide: 2000 }, inline: false, closable: false, position: _this.searchPopupPosition, onShow: function () {
                                searchFormEl_1.css('max-height', _this.getParentEl().height() * 0.8);
                            }
                        });
                    }, 1200);
                    f_2.onChange(function (e, uiField) {
                        var opSelect = ops_1[uiField.field.name];
                        if (opSelect) {
                            if (opSelect.find('option').length === 0) {
                                var supportOps = [{ label: '等于', value: '=' }, { label: '不等于', value: '!=' }];
                                var fieldType = uiField.field.formType || uiField.field.type;
                                if (fieldType == 'number' || fieldType === 'date' || fieldType === 'datetime') {
                                    supportOps.push({ label: '小于', value: '<' });
                                    supportOps.push({ label: '大于', value: '>' });
                                }
                                if (uiField.field.daterange == true) {
                                    supportOps.length = 0;
                                }
                                if (fieldType == 'text') {
                                    supportOps.push({ label: '模糊匹配', value: '≈' });
                                    supportOps.push({ label: '开头匹配', value: '≈*' });
                                    supportOps.push({ label: '结尾匹配', value: '*≈' });
                                }
                                if (fieldType == 'check') {
                                    supportOps.length = 0;
                                    supportOps.push({ label: '包含', value: '[]' });
                                    supportOps.push({ label: '不包含', value: '![]' });
                                }
                                supportOps.forEach(function (o) {
                                    $('<option>').val(o.value).text(o.value).attr('title', o.label).appendTo(opSelect);
                                });
                                var dft = _context.getSetting('beanSearchFormDefaultOpt');
                                if (dft && dft[fieldType])
                                    opSelect.val(dft[fieldType]);
                            }
                            opSelect.attr('title', opSelect.find('option:selected').attr('title'));
                            uiField.isDirty() ?
                                opSelect.show() : opSelect.hide();
                        }
                    });
                    var searchoptbar = $('<form name="ignore_case_form" class="form_search_option_bar">').appendTo(searchBar);
                    $('<input id="ignore_case" type="checkbox">').appendTo(searchoptbar).change(function () {
                        setObjectTypeSetting(self.objectType, 'search_nocase', $(this).is(':checked'));
                    }).prop('checked', getObjectTypeSetting(self.objectType, 'search_nocase'));
                    $('<label for="ignore_case" title="文本忽略大小写">').text('文本忽略大小写').appendTo(searchoptbar);
                    if (this.showSearch !== 'expand' && this.showSearch !== 'collapse')
                        $("<button class=\"ui button mini compact\">".concat(_locale.common.close, "</button>")).click(function (e) {
                            searchIcon_1.click();
                        }).appendTo(searchBar);
                    $("<button class=\"ui button mini compact\">".concat(_locale.common.reset, "</button>")).click(function (e) {
                        f_2.reset();
                        for (var k in ops_1) {
                            ops_1[k].hide();
                        }
                    }).appendTo(searchBar);
                    this.searchForm = f_2;
                    $("<button class=\"ui left labeled icon button mini compact primary\">\n                        <i class=\"search icon\"></i>".concat(_locale.common.query, "\n                        </button>")).click(function (e) {
                        var ps = f_2.getData(true);
                        for (var k in ops_1) {
                            var opSelect = ops_1[k], op = opSelect.is(':visible') ? opSelect.val() : '';
                            if (op === '≈')
                                op = 'like';
                            else if (op === '≈*')
                                op = 'rlike';
                            else if (op === '*≈')
                                op = 'llike';
                            else if (op === '[]')
                                op = 'in';
                            else if (op === '![]')
                                op = 'not in';
                            if (op)
                                ps['_op.' + k] = op;
                        }
                        ps._fastsearch = false;
                        if (_this.baseParams)
                            delete _this.baseParams.queryText;
                        var nocase = getObjectTypeSetting(_this.objectType, 'search_nocase');
                        if (nocase !== undefined)
                            ps._nocase = nocase;
                        _this.load(ps);
                    }).appendTo(searchBar);
                }
                else {
                    this.toolbar.find('.search').remove();
                }
            }
            if (this.footer) {
                this.prePageBtn = this.footer.find('.prepage').click(function () { return _this.doPrePage(); });
                this.nextPageBtn = this.footer.find('.nextpage').click(function () { return _this.doNextPage(); });
                this.footer.find('.reloadpage').click(function (e) { return _this.reload({ _fastsearch: false }); });
                var sizes = this.metaGrid.pageSizes || this.om.pageSizes || _context.getSetting('pageSizes') || '20,100,500,1000,5000';
                sizes = sizes.split(',').map(function (n) {
                    n = n.trim();
                    return { name: n, value: parseInt(n) };
                });
                var self_1 = this;
                sizes.some(function (s) {
                    if (s.value === self_1.pageSize) {
                        s.selected = true;
                        return true;
                    }
                });
                var pagesizediv = this.footer.parent().find('.pagesizediv');
                pagesizediv.find('#a_choose.dropdown').dropdown({
                    direction: 'upward', values: sizes, onChange: function (v) {
                        if (v && self_1.pageSize != v) {
                            self_1.pageSize = v;
                            self_1.load();
                        }
                    }
                });
                if (_locale.bean.pageSize)
                    pagesizediv.find('label').text(_locale.bean.pageSize);
                var jump_1 = pagesizediv.find('#a_jump');
                if (_locale.bean.pageJumpText)
                    jump_1.text(_locale.bean.pageJumpText);
                var jumpInput_1;
                jump_1.click(function (e) {
                    if (jumpInput_1) {
                        jumpInput_1.remove();
                        jumpInput_1 = null;
                    }
                    else
                        jumpInput_1 = $('<input>').css({ width: '3em' }).insertAfter(jump_1).keypress(function (e) {
                            if (e.which === 13) {
                                var num = $(this).val();
                                num = parseInt(num);
                                if (num > 0)
                                    self_1.gotoPage(num - 1);
                                jumpInput_1.remove();
                                jumpInput_1 = null;
                            }
                        });
                });
            }
            if (this.autoLoad) {
                this.load(this.autoLoad);
            }
            if (this.autoReloadInterval) {
                setInterval(function () { return _this.reload(); }, this.autoReloadInterval * 1000);
            }
            if (this.reloadByEventObjType === undefined) {
                this.reloadByEventObjType = !!this.autoLoad;
            }
            if (this.reloadByEventObjType !== false) {
                if (typeof this.reloadByEventObjType === 'string') {
                    this.reloadByEventObjType = this.reloadByEventObjType.split(',');
                }
                else if (this.reloadByEventObjType === true) {
                    this.reloadByEventObjType = [this.objectType];
                }
            }
            _context.events.on(_bean.EVENT_OBJ, function (e, params) {
                var method = params.method;
                var source = params && params.eventParams && params.eventParams.source;
                if (source && source.sourceui)
                    source = source.sourceui;
                if (source === _this) {
                    var dataId_1 = params.data;
                    if (dataId_1.id)
                        dataId_1 = dataId_1.id;
                    var findAndSet = function (id, cb) {
                        query(params.objectType, { id: id, _human: _this.human, start: 0, limit: 1 }, _this).then(function (data) {
                            data = data.data || data;
                            if (data.length)
                                cb(data[0]);
                        });
                    };
                    if (method === 'add' && _this.objectType === params.objectType) {
                        findAndSet(dataId_1, function (d) {
                            _this.addRow(d, 0);
                        });
                        return;
                    }
                    var dataPos_1 = -1;
                    if ((typeof dataId_1 === 'string' || typeof dataId_1 === 'number') && _this.data)
                        dataPos_1 = _this.data.findIndex(function (a) { return a.id == dataId_1; });
                    if (dataPos_1 !== -1) {
                        if (method === 'update') {
                            findAndSet(dataId_1, function (d) {
                                _this.removeRowAt(dataPos_1);
                                _this.addRow(d, dataPos_1);
                            });
                            return;
                        }
                        if (method === 'remove') {
                            _this.removeRowAt(dataPos_1);
                            return;
                        }
                    }
                }
                if (_this.reloadByEventObjType !== false && _this.dataLoaded) {
                    var types_1 = _this.reloadByEventObjType;
                    if (types_1.indexOf(params.objectType) !== -1 || (_this.data && _this.data.some(function (r) { return r.objectType === params.objectType; })))
                        _this.reload();
                    else {
                        types_1 = types_1.slice();
                        var found_1 = false, self_2 = _this;
                        var seed = function () {
                            if (!found_1 && types_1.length) {
                                isMetaChildren(params.objectType, types_1[0]).then(function (isChildren) {
                                    if (isChildren) {
                                        found_1 = true;
                                        self_2.reload();
                                    }
                                });
                            }
                        };
                        seed();
                    }
                }
            });
        };
        Table.prototype.resetActions = function () {
            var rows = this.getChecked();
            function checkaction(action) {
                if (action.itemsable && action.el && rows) {
                    rows.length ? action.el.removeClass('disabled') : action.el.addClass('disabled');
                }
            }
            if (this.actions)
                this.actions.forEach(function (action) { return checkaction(action); });
            if (this.extraActions)
                this.extraActions.forEach(function (action) { return checkaction(action); });
        };
        Table.prototype.appendAction = function (action, el) {
            var _this = this;
            var itemsable = action.itemsable;
            var a = $('<a>').addClass('item').appendTo(el).click(function () {
                if (a.hasClass('disabled'))
                    return;
                var ps = action.itemsable ? _this.getChecked() : _this.data;
                if (action.fn instanceof Function)
                    action.fn(ps, _this);
                else if (_this.doActionFn)
                    _this.doActionFn(action, ps, _this, a);
                else if (_context)
                    _context.doAction(action, ps, _this, a);
            });
            if (itemsable)
                a.addClass('disabled');
            var text = action.label || action.title || action.name;
            if (action.icon)
                $('<i>').addClass('icon').addClass(action.icon).appendTo(a);
            a.append(text);
            action.el = a;
        };
        Table.prototype.doPrePage = function () {
            if (this.currentPageNum === 0)
                return;
            this.prePageBtn.find('.prepage').addClass('loading');
            this.gotoPage(this.currentPageNum - 1);
        };
        Table.prototype.doNextPage = function () {
            if (!this.canNextPage())
                return;
            this.nextPageBtn.find('.nextpage').addClass('loading');
            this.gotoPage(this.currentPageNum + 1);
        };
        Table.prototype.doFilterData = function (callback) {
            var trs = this.templateEl.find("tr.suitablerow");
            trs.each(function (index, tr) {
                var data = $(tr).data("userdata");
                var isFilter = false;
                if (callback != null) {
                    isFilter = callback(data);
                }
                if (isFilter) {
                    $(tr).css("display", "none");
                }
                else {
                    $(tr).css("display", "");
                }
            });
        };
        Table.prototype.gotoPage = function (num) {
            this.load(null, num);
        };
        Table.prototype.reload = function (params) {
            if (params === void 0) { params = null; }
            if (params) {
                if (this.queryParams)
                    Object.assign(this.queryParams, params);
                else
                    this.queryParams = params;
                this.lastPageResult = undefined;
            }
            this.load(null, params ? 0 : this.currentPageNum);
        };
        Table.prototype.load = function (params, pageNum) {
            var _this = this;
            if (params === void 0) { params = null; }
            if (pageNum === void 0) { pageNum = undefined; }
            if (params) {
                this.queryParams = params;
            }
            if (params || this._lastBaseParams !== this.baseParams) {
                pageNum = pageNum || 0;
                this.lastPageResult = undefined;
            }
            if (typeof pageNum === 'number')
                this.currentPageNum = pageNum;
            params = this.getQueringParams(true);
            this._lastBaseParams = this.baseParams;
            var timestamp = (new Date()).getTime();
            this.searchTimeStamp = timestamp;
            var fn = function (result) {
                if (timestamp == _this.searchTimeStamp) {
                    _this.setData(result, params._skip_count === true);
                }
                _this.showLoading(false);
                _this.dataLoaded = true;
                return result;
            };
            this.showLoading();
            var queryType = this.queryObjectType;
            if (this.queryFn)
                return this.queryFn(params, params.start, this.pageSize, this).then(fn);
            if (this.queryActionName) {
                return action(queryType, this.queryActionName, params).then(fn);
            }
            else if (this.pageSize > 0)
                return page(queryType, params, params.start, this.pageSize, this).then(fn);
            else
                return query(queryType, params, this).then(function (data) { return ({ total: 0, data: data }); }).then(fn);
        };
        Table.prototype.getQueringParams = function (withGridCondition) {
            var ps = Object.assign({ _metagrid: this.gridName || '' }, this.baseParams, this.queryParams);
            if (withGridCondition) {
                if (ps._human === undefined) {
                    ps._human = this.human;
                }
                var orderBy = this.orderBy;
                if (orderBy !== false) {
                    if (orderBy === true)
                        orderBy = '';
                    if (this.userOrderBys && this.userOrderBys.length) {
                        var od_1 = '';
                        this.userOrderBys.forEach(function (fo) { return od_1 += fo + ','; });
                        if (orderBy)
                            od_1 += orderBy;
                        else
                            od_1 = od_1.substring(0, od_1.length - 1);
                        orderBy = od_1;
                    }
                }
                if (orderBy)
                    ps._orderBy = orderBy;
                if (this.pageSize > 0) {
                    var start = this.currentPageNum * this.pageSize;
                    ps.start = start;
                    ps.limit = this.pageSize;
                    if (this.lastPageResult && this.lastPageResult.total)
                        ps._skip_count = true;
                }
            }
            return ps;
        };
        Table.prototype.setData = function (data, useLastTotal) {
            if (useLastTotal === void 0) { useLastTotal = false; }
            if (data && data.data) {
                var invalidDataTotal = !data.total || data.total < 0 || data.data.length === data.total;
                if (useLastTotal && this.lastPageResult && invalidDataTotal)
                    data.total = this.lastPageResult.total;
            }
            else
                data = { data: data, total: 0 };
            this.lastPageResult = data;
            this.restorePaging();
            _super.prototype.setData.call(this, data.data);
            this.resetActions();
        };
        Table.prototype.showLoading = function (v) {
            if (v === void 0) { v = true; }
            _super.prototype.showLoading.call(this, v);
            this.setPagingText(!v);
        };
        Table.prototype.setPagingText = function (v) {
            if (v === void 0) { v = true; }
            if (this.pageSize > 0 && this.footer) {
                var msg = '', data = this.lastPageResult;
                if (v && data) {
                    var page_1 = this.currentPageNum, start = page_1 * this.pageSize, end = start + this.pageSize;
                    var pageNum = Math.ceil(data.total / this.pageSize);
                    if (end > data.total)
                        end = data.total;
                    msg = _locale.bean.pagingText;
                    var ps = { total: data.total, page_total: pageNum, page: page_1 + 1, start: start + 1, end: end };
                    msg = _util.replaceVar(msg, ps);
                }
                this.footer.parent().find('.pagingtext').text(msg);
            }
        };
        Table.prototype.rowClick = function (row, tr, rowNum, event) {
            var detailEl = _super.prototype.rowClick.call(this, row, tr, rowNum, event);
            if (!detailEl || !detailEl.hasClass('_objpopup'))
                return;
            this.rowDetailEl.empty();
            var td = this.rowDetailEl.parent();
            td.addClass('ui placeholder');
            var human = this.detailHuman;
            if (human === undefined)
                human = this.human;
            var needQuery = this.detailQuery;
            if (row.id === undefined || row.id === null)
                needQuery = false;
            var item = new Item(row.objectType || this.objectType, needQuery ? row.id : null, this.metaGrid, this.rowDetailEl, { sourceui: this, source: 'grid', human: human, doActionFn: this.doActionFn, actionsField: this.objActions, data: needQuery ? null : row }).render();
            item.rendered().then(function () {
                td.removeClass('ui placeholder');
            });
        };
        Table.prototype.canNextPage = function () {
            return (!this.lastPageResult || this.currentPageNum + 1 < this.lastPageResult.total / this.pageSize);
        };
        Table.prototype.restorePaging = function () {
            if (this.prePageBtn) {
                this.prePageBtn.removeClass('loading disabled');
                if (this.currentPageNum === 0)
                    this.prePageBtn.addClass('disabled');
            }
            if (this.nextPageBtn) {
                this.nextPageBtn.removeClass('loading disabled');
                if (!this.canNextPage())
                    this.nextPageBtn.addClass('disabled');
            }
        };
        Table.prototype.getRowIndex = function (row) {
            var idx = _super.prototype.getRowIndex.call(this, row);
            if (idx === -1) {
                idx = this.data.findIndex(function (d) { return d.objectType === row.objectType && d.id === row.id; });
            }
            return idx;
        };
        Table.prototype.setPageNum = function (pageNum) {
            this.currentPageNum = pageNum;
        };
        return Table;
    }(_sui.Table));
    _bean.Table = Table;
    var ObjField = (function (_super) {
        __extends(ObjField, _super);
        function ObjField() {
            return _super !== null && _super.apply(this, arguments) || this;
        }
        ObjField.prototype.createDropdown = function (el, pre, dropdownOptions) {
            if (this.field.pageSize)
                dropdownOptions.pageSize = this.field.pageSize;
            if (this.field.queryObjectType)
                dropdownOptions.queryObjectType = this.field.queryObjectType;
            if (this.field.queryActionName)
                dropdownOptions.queryActionName = this.field.queryActionName;
            if (this.field.search !== undefined)
                dropdownOptions.search = this.field.search;
            if (this.field.searchWindow !== undefined)
                dropdownOptions.searchWindow = this.field.searchWindow;
            if (this.field.searchGridOptions !== undefined)
                dropdownOptions.searchGridOptions = this.field.searchGridOptions;
            if (this.field.searchGridBaseParams !== undefined)
                dropdownOptions.searchGridBaseParams = this.field.searchGridBaseParams;
            if (this.field.disabled !== undefined) {
                dropdownOptions.disabled = this.field.disabled;
            }
            return new ObjCombo(this.field.rtype, el, Object.assign({ inputName: this.field.name }, dropdownOptions));
        };
        return ObjField;
    }(_sui.DropdownField));
    _bean.ObjField = ObjField;
    var DictField = (function (_super) {
        __extends(DictField, _super);
        function DictField(objectType, field, parentEl, options) {
            if (options === void 0) { options = {}; }
            var _this = _super.call(this, field, parentEl, options) || this;
            _this.objectType = objectType;
            return _this;
        }
        DictField.prototype.createDropdown = function (el, pre, dropdownOptions) {
            var objtype = this.field.dictObjectType || this.field.dtype || this.objectType;
            var fieldName = this.field.dictFieldName || this.field.dattr || this.field.name;
            var dictionItems;
            if (this.field.ditems) {
                if (typeof this.field.ditems === 'string') {
                    var ditems = this.field.ditems, items_1 = [];
                    ditems.split(',').forEach(function (di) {
                        var kv = di.split('-');
                        items_1.push({ id: kv[0].trim(), value: kv[0].trim(), name: kv[1].trim() });
                    });
                    dictionItems = items_1;
                }
                else
                    dictionItems = this.field.ditems;
            }
            var ps = { inputName: this.field.name };
            if (dictionItems)
                ps.dataProvider = dictionItems;
            return new DictCombo(objtype, fieldName, el, Object.assign(ps, dropdownOptions));
        };
        return DictField;
    }(_sui.DropdownField));
    _bean.DictField = DictField;
    var AutoCodeField = (function (_super) {
        __extends(AutoCodeField, _super);
        function AutoCodeField(objectType, field, parentEl, options) {
            var _this = _super.call(this, field, parentEl, options) || this;
            _this.objectType = objectType;
            _this.iconActions = [
                {
                    icon: 'circle notch', id: 'autocode', fn: function (params, action, field) {
                        return _this._doAutoCode();
                    }
                }
            ];
            return _this;
        }
        AutoCodeField.prototype._doAutoCode = function () {
            var _this = this;
            var ps = this.formInstance.getData(false);
            if (this.codeTemplate)
                ps._GEN_TEMPLATE = this.codeTemplate;
            ps.targetObjectType = this.objectType;
            return _bean.genCode(this.objectType, ps, true, false).then(function (code) {
                if (code)
                    _this.setValue(code);
                _this.getParentEl().find('input').transition('glow');
                if (code)
                    saveCodeSernos({}, code, function (n) {
                        return n > 0 ? n - 1 : n;
                    });
            });
        };
        AutoCodeField.prototype.doAutoCode = function () {
            this.doIconAction('autocode');
        };
        return AutoCodeField;
    }(_sui.InputField));
    _bean.AutoCodeField = AutoCodeField;
    var Form = (function (_super) {
        __extends(Form, _super);
        function Form(objectType, formName, parentEl, options) {
            if (options === void 0) { options = {}; }
            var _this = _super.call(this, [], parentEl, options) || this;
            _this.objectType = objectType;
            _this.formName = formName;
            return _this;
        }
        Form.prototype.prepare = function () {
            var _this = this;
            _super.prototype.prepare.call(this);
            return getMeta(this.objectType).then(function (om) {
                _this.om = om;
                _this.metaForm = findMetaByName(om.forms, _this.formName);
                _this.rows = _this.rows || _this.metaForm.fields;
                var retPromises = [];
                var scripts = _this.metaForm.scripts;
                if (scripts && scripts.length) {
                    _this.script = scripts.map(function (scriptNode) {
                        if (scriptNode.content)
                            return scriptNode.content;
                        else if (scriptNode.src || scriptNode.url || scriptNode.contentUrl) {
                            var scriptPromise = new Promise(function (resolve, reject) {
                                $.ajax({
                                    url: _context.fixUrl(scriptNode.src || scriptNode.url || scriptNode.contentUrl), type: "GET", dataType: "text", success: function (data) {
                                        resolve(data);
                                    }
                                });
                            });
                            retPromises.push(scriptPromise);
                            return scriptPromise;
                        }
                    });
                }
                var rtypes = [];
                function pushrtype(field) {
                    if (field.type === 'obj' && field.rtype)
                        rtypes.push(field.rtype);
                    else if (field.fields)
                        field.fields.forEach(function (f) { return pushrtype(f); });
                }
                _this.rows.forEach(function (r) { return pushrtype(r); });
                if (rtypes.length)
                    retPromises.push(_bean.getMetas(rtypes));
                return Promise.all(retPromises);
            });
        };
        Form.prototype.isCodeField = function (field) {
            return field.name === 'CODE' || field.name === 'code';
        };
        Form.prototype.isNameField = function (field) {
            return field.name === 'NAME' || field.name === 'name';
        };
        Form.prototype.canAutoCodeField = function () {
            if (this.canConvertAutoCodeField !== undefined)
                return this.canConvertAutoCodeField;
            return !this.formName || this.formName === 'add' || this.formName === 'modify';
        };
        Form.prototype.createField = function (field, fieldEl, inline) {
            var fieldType = field.formType || field.type;
            if (this.canAutoCodeField()) {
                if (this.isCodeField(field) && (this.metaForm.GEN_CODE_TEMPLATE || this.om.GEN_CODE_TEMPLATE) && (!fieldType || fieldType === 'text'))
                    fieldType = 'autocode';
                if (this.isNameField(field) && (this.metaForm.GEN_NAME_TEMPLATE || this.om.GEN_NAME_TEMPLATE) && (!fieldType || fieldType === 'text'))
                    fieldType = 'autoname';
            }
            var dropdownOptions = field.dropdownOptions;
            if (typeof dropdownOptions === 'string')
                dropdownOptions = parsekv(dropdownOptions);
            if (fieldType === 'obj') {
                var baseParams = { _formName: this.formName };
                if (field.baseParams) {
                    var bp = field.baseParams;
                    if (typeof bp === 'string') {
                        bp = parsekv(field.baseParams);
                    }
                    baseParams = Object.assign(baseParams, bp);
                }
                dropdownOptions = dropdownOptions || {};
                dropdownOptions.baseParams = baseParams;
                return new ObjField(field, fieldEl, { inline: inline, dropdownOptions: dropdownOptions });
            }
            if (fieldType === 'dict')
                return new DictField(this.objectType, field, fieldEl, { inline: inline, dropdownOptions: dropdownOptions });
            if (fieldType === 'check' || fieldType === 'radio') {
                if (field.ditems && !field.items) {
                    var ditems = field.ditems, items_2 = [];
                    ditems.split(',').forEach(function (di) {
                        var kv = di.split('-');
                        items_2.push({ id: kv[0], value: kv[0], name: kv[1] });
                    });
                    field.items = items_2;
                }
                else if (!field.items && !field.data && field.type === 'dict')
                    field.data = dict(field.dtype || this.objectType, field.dattr || field.name);
            }
            if (fieldType === 'autocode')
                return new AutoCodeField(this.objectType, field, fieldEl, { inline: inline, codeTemplate: this.metaForm.GEN_CODE_TEMPLATE || this.om.GEN_CODE_TEMPLATE });
            if (fieldType === 'autoname')
                return new AutoCodeField(this.objectType, field, fieldEl, { inline: inline, codeTemplate: this.metaForm.GEN_NAME_TEMPLATE || this.om.GEN_NAME_TEMPLATE });
            return _super.prototype.createField.call(this, field, fieldEl, inline);
        };
        Form.prototype.getFormComputeContext = function () {
            var d = _super.prototype.getFormComputeContext.call(this);
            for (var k in this.uiFields) {
                var f = this.uiFields[k];
                if (f.dropdown && f.dropdown.getSelectedItem)
                    d[k] = f.dropdown.getSelectedItem();
            }
            return d;
        };
        Form.prototype.canRuleActionWork = function (actionName) {
            return true;
        };
        Form.prototype.computeValueByExp = function (exp) {
            var _this = this;
            if (exp.startsWith('@')) {
                var data_1 = this.getFormComputeContext();
                var actionName_1 = exp.substring(1), pos = actionName_1.indexOf('('), fs = void 0;
                if (pos !== -1) {
                    fs = actionName_1.substring(pos + 1);
                    if (fs.endsWith(')'))
                        fs = fs.substr(0, fs.length - 1);
                    actionName_1 = actionName_1.substring(0, pos);
                }
                if (this.canRuleActionWork(actionName_1) === false)
                    return undefined;
                var can_1 = true;
                if (fs)
                    fs.split(',').forEach(function (f) {
                        if (!data_1[f.trim()])
                            can_1 = false;
                    });
                if (!can_1)
                    return null;
                var scriptAction = this.om.actions.find(function (a) { return a.name === actionName_1 && (a.type === 'script' || !a.type); });
                if (scriptAction) {
                    if (scriptAction.url)
                        return _bean.get(scriptAction.url).then(function (content) {
                            return _super.prototype.computeValueByExp.call(_this, content);
                        });
                    else
                        exp = scriptAction.content;
                }
                else
                    return action(this.objectType, actionName_1, data_1);
            }
            if (exp.indexOf('.') !== -1) {
                var fv = exp.split('.');
                if (fv.length === 2) {
                    var fname_1 = fv[0], attr_1 = fv[1];
                    var field = this.om.fields.find(function (f) { return f.name === fname_1; });
                    if (field && field.type === 'obj' && attr_1.indexOf(' ') === -1) {
                        var uif = this.getFormField(fname_1);
                        var obj = uif.dropdown.getSelectedItem();
                        if (!obj)
                            return null;
                        var id = obj.id;
                        if (obj[attr_1])
                            return obj[attr_1];
                        return find(field.rtype, { id: id, _assemble: attr_1 }).then(function (o) { return o[attr_1]; });
                    }
                }
            }
            return _super.prototype.computeValueByExp.call(this, exp);
        };
        Form.prototype.dealFieldOnChange = function (field, uiField) {
            var _this = this;
            _super.prototype.dealFieldOnChange.call(this, field, uiField);
            var oc = field.onChange;
            if (typeof oc === 'string' && oc.indexOf('@genCode(') !== -1) {
                var rule = oc.split(';').filter(function (r) { return r.indexOf('@genCode(') !== -1; })[0];
                var rs_1 = rule.split('='), names = rs_1[0];
                names.split(',').forEach(function (name) {
                    name = name.trim();
                    if (name.endsWith('?'))
                        name = name.substring(0, name.length - 1);
                    var nrule = name + '=' + rs_1[1];
                    _this.rendered().then(function () {
                        var el = _this.getFieldEl(name), tf = _this.getField(name);
                        if (!el.find('.field-gencode-btn').length && !(tf instanceof AutoCodeField) && (!tf['iconActions'] || tf['iconActions'].length === 0)) {
                            var icon = $('<i class="icon circle notch field-gencode-btn">').attr('title', '自动编码').appendTo(el).click(function () {
                                _this.execRule(nrule, null, field);
                                return false;
                            });
                        }
                    });
                });
            }
        };
        return Form;
    }(_sui.Form));
    _bean.Form = Form;
    var ObjForm = (function (_super) {
        __extends(ObjForm, _super);
        function ObjForm(objectType, formName, el, options) {
            if (options === void 0) { options = {}; }
            var _this = _super.call(this, el, options) || this;
            _this.objectType = objectType;
            _this.formName = formName;
            _this.formOptions = {};
            _this.showAssembles = true;
            _this.forms = [];
            _this.readOnly = false;
            _this.assembles = {};
            return _this;
        }
        ObjForm.prototype.prepare = function () {
            var _this = this;
            var objectType = this.objectType;
            if (typeof this.obj === 'string' || typeof this.obj === 'number') {
                return Promise.all([findByForm(objectType, this.formName || '', this.obj), getMeta(objectType)]).then(function (rets) {
                    _this.obj = rets[0];
                    _this.makeForm(rets[1]);
                });
            }
            else
                return getMeta(objectType).then(function (om) { return _this.makeForm(om); });
        };
        ObjForm.prototype.rendered = function () {
            var _this = this;
            return _super.prototype.rendered.call(this).then(function () {
                return Promise.all(_this.forms.map(function (f) { return f.rendered(); }));
            });
        };
        ObjForm.prototype.destroy = function () {
            _super.prototype.destroy.call(this);
            this.forms.forEach(function (f) { return f.destroy(); });
            for (var k in this.assembles)
                this.assembles[k].destroy();
        };
        ObjForm.prototype.createForm = function (form, meta, active) {
            if (active === void 0) { active = false; }
            var self = this;
            function createFormTab(tabLabel, fields, active, inline) {
                if (!fields || !fields.length)
                    return;
                var el = $('<div>');
                el.addClass('ui equal width form');
                var initData = Object.assign({}, self.obj);
                if (initData._init === undefined)
                    initData._init = true;
                if (inline === false)
                    inline = null;
                else
                    inline = { labelWidth: form.labelWidth || _context.getSetting('formLabelWidth') };
                var formClass = form.formClass || meta.formClass, formStyle = form.formStyle || meta.formStyle;
                var formOpt = { readOnly: form.readOnly !== undefined ? form.readOnly : self.readOnly, rows: fields, inlineFields: inline, data: initData, execRuleAfterDataSet: self.obj && self.obj.id, formClass: formClass, formStyle: formStyle };
                formOpt = Object.assign(formOpt, self.formOptions);
                var formComponent = new Form(self.objectType, form.name, el, formOpt).render();
                var obj = self.obj = self.obj || {};
                var objData = { id: obj.id, objectType: obj.objectType };
                $.extend(obj, objData);
                self.addTab(tabLabel, el, active);
                self.forms.push(formComponent);
                return formComponent;
            }
            var tabs = form.tabs;
            if (!tabs) {
                tabs = [];
                var lastTab_1;
                if (form.fields)
                    form.fields.forEach(function (row) {
                        if (row.type === 'tab') {
                            lastTab_1 = { label: row.label, fields: [], active: row.active };
                            tabs.push(lastTab_1);
                        }
                        else if (lastTab_1)
                            lastTab_1.fields.push(row);
                    });
            }
            if (tabs.length && this.ignoreFormTab) {
                tabs = [tabs[0]];
            }
            var formCreated = false;
            if (tabs.length) {
                var hasActived = tabs.some(function (tab) { return tab.active === true; });
                if (!hasActived)
                    tabs[0].active = active;
                tabs.forEach(function (tab, i) {
                    if (tab.fields) {
                        formCreated = true;
                        createFormTab(tab.label, tab.fields, tab.active, tab.inline !== undefined ? tab.inline : form.inline);
                    }
                });
            }
            else if (form.fields) {
                formCreated = true;
                createFormTab(form.label, form.fields, active, form.inline);
            }
            return formCreated;
        };
        ObjForm.prototype.addTab = function (label, el, active, tabOpt) {
            if (tabOpt === void 0) { tabOpt = {}; }
            var tab = $('<a class="item">').text(label).appendTo(this.tabsEl).attr('data-tab', label);
            el.addClass('ui attached tab').attr('data-tab', label);
            this.parentEl.append(el);
            if (active) {
                tab.addClass('active');
                el.addClass('active');
            }
            tabOpt.context = 'parent';
            tab.tab(tabOpt);
            if (this.tabsEl.find('a.item').length > 1)
                this.tabsEl.show();
        };
        ObjForm.prototype.makeForm = function (meta) {
            var _this = this;
            this.tabsEl = $('<div class="ui top attached pointing secondary menu">').appendTo(this.parentEl).css({ border: 0, fontSize: 'smaller' }).hide();
            if (this.metaForm) {
                var _mergefield_1 = function (fs) {
                    fs = fs || [];
                    var fd;
                    for (var i = 0; i < fs.length; i++) {
                        fd = fs[i];
                        if (fd.fields)
                            _mergefield_1(fd.fields);
                        else if (fd.name) {
                            var already = meta.findField(fd.name);
                            if (already)
                                Object.assign(fd, already, fd);
                        }
                    }
                };
                _mergefield_1(this.metaForm.fields);
            }
            else
                this.metaForm = findMetaByName(meta.forms, this.formName);
            var form = this.metaForm;
            var formCreated = this.createForm(form, meta, true);
            var assembles = form._ui_assemble;
            if (assembles && this.showAssembles !== false)
                assembles.split(',').forEach(function (assemble, idx) {
                    assemble = assemble.trim();
                    var field = meta.fields.find(function (f) { return f.name === assemble; });
                    if (field) {
                        var pane_1 = $('<div>').css('padding', '10px');
                        var readOnly = field.readOnly;
                        if (readOnly === undefined)
                            readOnly = _this.formName !== 'add' && _this.formName !== 'modify';
                        var assembleList_1 = new AssembleFieldList(_this.obj, field, pane_1, { readOnly: readOnly }).render();
                        _this.addTab(field.label, pane_1, !formCreated && idx === 0, {
                            onLoad: function () {
                                assembleList_1.load();
                            }, onFirstLoad: function () {
                                var altabs = _this.parentEl.find('> .tab');
                                if (formCreated)
                                    pane_1.height(altabs.height() - 20);
                            }
                        });
                        _this.assembles[field.name] = assembleList_1;
                        if (!formCreated && idx === 0)
                            assembleList_1.load();
                    }
                    else {
                        var anotherForm = findMetaByName(meta.forms, assemble);
                        if (anotherForm) {
                            _this.createForm(anotherForm, meta, !formCreated && idx === 0);
                        }
                        else {
                            var action_1 = findAction(assemble, meta, form);
                            if (action_1) {
                                var pane_2 = $('<div>').css('padding', '10px');
                                _this.addTab(action_1.title || action_1.label, pane_2, !formCreated && idx === 0, {
                                    onFirstLoad: function () {
                                        _context.doAction(action_1, _this.obj, 'form', undefined, pane_2);
                                    }
                                });
                            }
                        }
                    }
                });
            this.trigger('makeFormEnd', this);
        };
        ObjForm.prototype.validate = function () {
            var ret = true;
            this.forms.forEach(function (f) { return ret = ret && f.validate(); });
            return ret;
        };
        ObjForm.prototype.validatePromise = function () {
            return Promise.all(this.forms.map(function (f) {
                var pass = f.validate();
                if (_util.isPromise(pass))
                    return pass;
                return pass ? Promise.resolve(pass) : Promise.reject(pass);
            }));
        };
        ObjForm.prototype.getData = function (dirty, assembleDirty) {
            if (dirty === void 0) { dirty = false; }
            if (assembleDirty === void 0) { assembleDirty = undefined; }
            var data = {};
            this.forms.forEach(function (f) { return Object.assign(data, f.getData(dirty)); });
            if (assembleDirty === undefined)
                assembleDirty = dirty;
            for (var k in this.assembles) {
                var list = this.assembles[k];
                var ids = list.getIds(assembleDirty);
                if (ids !== null && ids !== undefined)
                    data[k] = ids;
            }
            return data;
        };
        ObjForm.prototype.setDataById = function (id) {
            var _this = this;
            if (id)
                findByForm(this.objectType, this.formName, id).then(function (o) { return _this.setData(o); });
            else
                this.setData({});
        };
        ObjForm.prototype.setData = function (d) {
            if (typeof d === 'number' || typeof d === 'string')
                return this.setDataById(d);
            _super.prototype.setData.call(this, d);
            this.forms.forEach(function (f) { return f.setData(d); });
            for (var k in this.assembles) {
                var l = this.assembles[k];
                l.obj = d;
                if (l.loaded) {
                    l.reload();
                }
            }
        };
        ObjForm.prototype.setBaseData = function (d) {
            var _this = this;
            this.rendered().then(function () {
                _this.forms.forEach(function (f) { return f.setBaseData(d); });
            });
        };
        ObjForm.prototype.setBaseDataValue = function (k, v) {
            var _this = this;
            this.rendered().then(function () {
                _this.forms.forEach(function (f) { return f.setBaseDataValue(k, v); });
            });
        };
        ObjForm.prototype.getField = function (name) {
            var field;
            this.forms.find(function (f) {
                field = f.getField(name);
                return field;
            });
            return field;
        };
        ObjForm.prototype.getFieldEl = function (name) {
            for (var i = 0; i < this.forms.length; i++) {
                var fe = this.forms[i].getFieldEl(name);
                if (fe)
                    return fe;
            }
        };
        ObjForm.prototype.onChange = function (fn) {
            var _this = this;
            this.rendered().then(function () {
                _this.forms.forEach(function (f) {
                    f.onChange(fn);
                });
            });
        };
        ObjForm.prototype.onInputChange = function (fn) {
            var _this = this;
            this.rendered().then(function () {
                _this.forms.forEach(function (f) {
                    f.onInputChange(fn);
                });
            });
        };
        ObjForm.prototype.reset = function () {
            this.forms.forEach(function (f) {
                f.reset();
            });
        };
        ObjForm.prototype.findAssemblesByFieldName = function (fieldName) {
            return this.assembles[fieldName];
        };
        return ObjForm;
    }(_sui.Component));
    _bean.ObjForm = ObjForm;
    var SearchObjForm = (function (_super) {
        __extends(SearchObjForm, _super);
        function SearchObjForm(objectType, formName, el, options) {
            if (options === void 0) { options = {}; }
            var _this = _super.call(this, objectType, formName, el, Object.assign(options, {
                formOptions: {
                    ignoreRequired: true, fieldDecorator: function (field) {
                        var nf = Object.assign({}, field);
                        nf.title = nf.title || nf.label;
                        nf.label = $('<span>').text(field.label);
                        if (nf.type === 'date' || nf.type === 'datetime') {
                            if (nf.daterange === undefined)
                                nf.daterange = true;
                        }
                        var dp = $('<select>').css({
                            float: 'right', width: 'unset', appearance: 'none', display: 'inline', border: 'none', padding: '2px', fontSize: '8px', userSelect: 'none', outline: 'none'
                        }).appendTo(nf.label).change(function () {
                            dp.attr('title', dp.find('option:selected').attr('title'));
                        });
                        _this.ops[field.name] = dp.hide();
                        return nf;
                    }
                }
            })) || this;
            _this.ops = {};
            return _this;
        }
        SearchObjForm.prototype.showFieldOpSelect = function (uiField) {
            var opSelect = this.ops[uiField.field.name];
            if (opSelect) {
                if (opSelect.find('option').length === 0) {
                    var supportOps = [{ label: '等于', value: '=' }, { label: '不等于', value: '!=' }];
                    var fieldType = uiField.field.formType || uiField.field.type;
                    if (fieldType == 'number' || fieldType === 'date' || fieldType === 'datetime') {
                        supportOps.push({ label: '小于', value: '<' });
                        supportOps.push({ label: '大于', value: '>' });
                    }
                    if (uiField.field.daterange == true) {
                        supportOps.length = 0;
                    }
                    if (fieldType == 'text') {
                        supportOps.push({ label: '模糊匹配', value: '≈' });
                        supportOps.push({ label: '开头匹配', value: '≈*' });
                        supportOps.push({ label: '结尾匹配', value: '*≈' });
                    }
                    if (fieldType == 'check') {
                        supportOps.length = 0;
                        supportOps.push({ label: '包含', value: '[]' });
                        supportOps.push({ label: '不包含', value: '![]' });
                    }
                    supportOps.forEach(function (o) {
                        $('<option>').val(o.value).text(o.value).attr('title', o.label).appendTo(opSelect);
                    });
                    var dftOp = uiField.field.defaultSearchOp;
                    if (!dftOp) {
                        var dft = _context.getSetting('beanSearchFormDefaultOpt');
                        if (dft)
                            dftOp = dft[fieldType];
                    }
                    if (dftOp)
                        opSelect.val(dftOp);
                }
                if (this.data && this.data['_op.' + uiField.name])
                    opSelect.val(this.data['_op.' + uiField.name]);
                opSelect.attr('title', opSelect.find('option:selected').attr('title'));
                opSelect.show();
            }
        };
        SearchObjForm.prototype.doRender = function (el, prepare) {
            var _this = this;
            _super.prototype.doRender.call(this, el, prepare);
            this.rendered().then(function () {
                _this.onChange(function (e, uiField) {
                    if (uiField)
                        _this.showFieldOpSelect(uiField);
                });
            });
        };
        SearchObjForm.prototype.setData = function (data) {
            var _this = this;
            _super.prototype.setData.call(this, data);
            var showAllOps = function () {
                for (var k in data) {
                    if (k.startsWith('_op.')) {
                        var name_1 = k.substring(4);
                        var uiField = _this.getField(name_1);
                        if (uiField)
                            _this.showFieldOpSelect(uiField);
                    }
                }
            };
            this.rendered().then(function () {
                var f = _this.forms[0];
                if (f.dataLoaded) {
                    showAllOps();
                }
                else {
                    f.on('dataLoaded', showAllOps);
                }
            });
        };
        SearchObjForm.prototype.getData = function (dirty) {
            if (dirty === void 0) { dirty = true; }
            var ps = _super.prototype.getData.call(this, dirty);
            for (var k in this.ops) {
                var opSelect = this.ops[k], op = opSelect.val();
                if (op === '≈')
                    op = 'like';
                else if (op === '≈*')
                    op = 'rlike';
                else if (op === '*≈')
                    op = 'llike';
                else if (op === '[]')
                    op = 'in';
                else if (op === '![]')
                    op = 'not in';
                if (op)
                    ps['_op.' + k] = op;
            }
            if (ps._fastsearch === undefined)
                ps._fastsearch = false;
            return ps;
        };
        SearchObjForm.prototype.reset = function () {
            for (var k in this.ops) {
                if (this.ops[k])
                    this.ops[k].hide();
            }
            _super.prototype.reset.call(this);
        };
        return SearchObjForm;
    }(ObjForm));
    _bean.SearchObjForm = SearchObjForm;
    function searchform(objectType, formName, el, options) {
        if (formName === void 0) { formName = 'search'; }
        return new SearchObjForm(objectType, formName, el, options).render();
    }
    _bean.searchform = searchform;
    var DictCombo = (function (_super) {
        __extends(DictCombo, _super);
        function DictCombo(objectType, field, el, options) {
            if (options === void 0) { options = {}; }
            var _this = _super.call(this, el, Object.assign({ valueField: 'value', dataProvider: (options.dataProvider || dict(objectType, field).then(function (items) { return items.slice(); })) }, options)) || this;
            _this.objectType = objectType;
            _this.field = field;
            _this.loadingDictData = false;
            if (Array.isArray(_this.dataProvider)) {
                _this.setData(_this.dataProvider);
            }
            return _this;
        }
        DictCombo.prototype.setValue = function (v) {
            var _this = this;
            if ((v || v === 0) && typeof v !== 'object') {
                if (!this.data && !this.loadingDictData) {
                    this.loadingDictData = true;
                    dict(this.objectType, this.field).then(function (data) {
                        _this.loadingDictData = false;
                        if (!data.find(function (d) {
                            return d.value == v;
                        }))
                            data = data.concat([
                                { value: v, name: v }
                            ]);
                        _this.setData(data);
                    });
                }
            }
            _super.prototype.setValue.call(this, v);
        };
        return DictCombo;
    }(_sui.Dropdown));
    _bean.DictCombo = DictCombo;
    function dictcombo(objectType, field, el, options) {
        if (options === void 0) { options = {}; }
        return new DictCombo(objectType, field, el, options).render();
    }
    _bean.dictcombo = dictcombo;
    var ObjCombo = (function (_super) {
        __extends(ObjCombo, _super);
        function ObjCombo(objectType, parentEl, options) {
            if (options === void 0) { options = {}; }
            var _this = _super.call(this, parentEl, options) || this;
            _this.objectType = objectType;
            _this.baseParams = {};
            _this.pageSize = _bean.defaultComboPageSize;
            _this.queryField = 'queryText';
            _this.search = true;
            _this.showByKey = undefined;
            _this.searchWindow = true;
            _this.emptyValue = 'null';
            _this.remoteSort = false;
            _this.dataSort = true;
            _this.disabled = false;
            return _this;
        }
        ObjCombo.prototype.prepare = function () {
            var _this = this;
            if (!this.dataProvider)
                this.dataProvider = function (settings) { return _this.loadData(settings); };
            return getMeta(this.objectType).then(function (om) {
                if (Array.isArray(om))
                    om = om[0];
                if (om.labelField && _this.titleField === 'name')
                    _this.titleField = om.labelField;
                if (_this.showByKey === undefined) {
                    _this.showByKey = _context.getSetting('beanComboShowByKey');
                    if (_this.showByKey === undefined)
                        _this.showByKey = true;
                }
                if (!_this.search)
                    _this.searchWindow = false;
                if (_this.extraIconActions === undefined && _this.searchWindow) {
                    var expandSearchForm = om.findForm('search');
                    var expandSearch_1 = false;
                    if (!expandSearchForm) {
                        var fm = findMetaByName(om.forms, '');
                        if (fm)
                            expandSearch_1 = fm.fields.length < 3;
                        else
                            expandSearch_1 = om.fields.length < 3;
                    }
                    else
                        expandSearch_1 = expandSearchForm.defaultExpand;
                    if (typeof _this.searchGridOptions === 'string')
                        _this.searchGridOptions = JSON.parse(_this.searchGridOptions);
                    if (typeof _this.searchGridBaseParams === 'string')
                        _this.searchGridBaseParams = JSON.parse(_this.searchGridBaseParams);
                    if (_this.disabled) {
                        return;
                    }
                    _this.extraIconActions = [{
                            icon: 'keyboard', fn: function () {
                                if (typeof _this.searchWindow === 'function')
                                    _util.dataCallback(function (o) {
                                        if (o)
                                            _this.setValue(o);
                                    }, _this.searchWindow, [_this], _this);
                                else {
                                    var searchGridOptions_1 = Object.assign({
                                        rowCheckable: _this.multiple ? true : 'single',
                                        showSearch: expandSearch_1 ? 'expand' : true, autoLoad: false, queryActionName: _this.queryActionName
                                    }, _this.searchGridOptions);
                                    var currentObjVal = _this.getValue();
                                    if (currentObjVal && currentObjVal !== 'null') {
                                        searchGridOptions_1.autoLoad = { id: currentObjVal };
                                    }
                                    var pp = _this.queryObjectType ? getMeta(_this.queryObjectType) : Promise.resolve();
                                    pp.then(function (queryObjectTypeOm) {
                                        showSelect(_this.queryObjectType || _this.objectType, _this.options.selectTitle || ('请选择' + (queryObjectTypeOm || om).label), 'search', Object.assign({}, _this.baseParams, _this.searchGridBaseParams), searchGridOptions_1).then(function (os) {
                                            if (!_this.multiple && Array.isArray(os))
                                                os = os[0];
                                            if (!os)
                                                return;
                                            if (Array.isArray(os) && os.length === 0)
                                                return;
                                            if (Array.isArray(os)) {
                                                var newdata_1 = _this.data || [], added_1 = false;
                                                os.forEach(function (o) {
                                                    if (!newdata_1.find(function (o1) {
                                                        return equals(o1, o);
                                                    })) {
                                                        newdata_1.unshift(o);
                                                        added_1 = true;
                                                    }
                                                });
                                                if (added_1)
                                                    _this.setData(newdata_1);
                                                var vs_2 = _this.getValue();
                                                if (typeof vs_2 === 'string') {
                                                    vs_2 = (vs_2 + '').split(',');
                                                }
                                                if (!vs_2)
                                                    vs_2 = [];
                                                if (!Array.isArray(vs_2))
                                                    vs_2 = [vs_2];
                                                os.forEach(function (o) {
                                                    var id = o[_this.valueField];
                                                    if (!vs_2.find(function (v) { return v.trim() == id; }))
                                                        vs_2.push(o[_this.valueField]);
                                                });
                                                os = vs_2;
                                            }
                                            _this.setValue(os);
                                        });
                                    });
                                }
                            }
                        }];
                }
                _this.objmeta = om;
                _this.simpleQueryFields = om.fields && om.fields.filter(function (f) { return f.simpleQuery === true; });
                if (!_this.simpleQueryFields.length)
                    _this.simpleQueryFields = false;
                return om;
            });
        };
        ObjCombo.prototype.doRender = function (el, pre) {
            var r = _super.prototype.doRender.call(this, el, pre);
            if (this.queryField == 'queryText' && this.templateEl) {
                var om = this.objmeta;
                var fs = this.simpleQueryFields;
                if (fs)
                    this.templateEl.attr('title', fs.map(function (f) { return f.label; }).join(','));
            }
            return r;
        };
        ObjCombo.prototype.getItemTooltip = function (item) {
            if (this.simpleQueryFields) {
                var fs = this.simpleQueryFields.map(function (f) {
                    var v = item[f.name];
                    if (v)
                        return f.label + ': ' + v;
                });
                fs = fs.filter(function (f) { return f; });
                return fs.join('\n');
            }
            return _super.prototype.getItemTooltip.call(this, item);
        };
        ObjCombo.prototype.loadData = function (settings) {
            var _a;
            if (settings === void 0) { settings = null; }
            var t = '';
            if (settings && settings.urlData)
                t = settings.urlData.query;
            var ps = { _skip_count: true, queryTextEqFirst: true };
            var nocase = getObjectTypeSetting(this.queryObjectType || this.objectType, 'search_nocase');
            if (nocase !== undefined)
                ps._nocase = nocase;
            var searchlike = getObjectTypeSetting(this.queryObjectType || this.objectType, 'search_like');
            if (searchlike !== undefined)
                ps._op = 'like';
            if (this.remoteSort === false)
                ps._orderBy = '';
            ps = Object.assign(ps, this.baseParams, (_a = {}, _a[this.queryField] = t, _a));
            return this.queryActionName ? action(this.queryObjectType || this.objectType, this.queryActionName, ps)
                : page(this.queryObjectType || this.objectType, ps, 0, this.pageSize);
        };
        ObjCombo.prototype.setValue = function (v) {
            var _this = this;
            if (v) {
                if (typeof v === 'string')
                    v = v.split(',');
                var notExistIds_1 = [];
                if (!Array.isArray(v))
                    v = [v];
                v.forEach(function (v1) {
                    var id = v1[_this.valueField] || v1;
                    if (!_this.data || !_this.data.find(function (d) { return d[_this.valueField] == id; })) {
                        notExistIds_1.push(id);
                    }
                });
                if (notExistIds_1.length) {
                    var callback_1 = function (data) {
                        if (data.length) {
                            var newdata = _this.data || [];
                            newdata = data.concat(newdata);
                            _this.setData(newdata);
                        }
                        var nv = [];
                        v.forEach(function (v1) {
                            var _a;
                            var id = v1[_this.valueField] || v1;
                            if (!_this.data || !_this.data.find(function (d) { return d[_this.valueField] == id; })) {
                                console.log('invalid objcombo value:', v1);
                                v1 = typeof v1 === 'string' || typeof v1 === 'number' ? (_a = {}, _a[_this.valueField] = v1, _a[_this.titleField] = v1, _a) : v1;
                            }
                            nv.push(v1);
                        });
                        _super.prototype.setValue.call(_this, nv);
                    };
                    var ps_1 = Object.assign({}, this.baseParams);
                    ps_1[this.valueField] = notExistIds_1;
                    return query(this.objectType, ps_1).then(function (data) {
                        if (data.length || !_this.baseParams)
                            callback_1(data);
                        else {
                            ps_1 = {};
                            ps_1[_this.valueField] = notExistIds_1;
                            if (_this.baseParams && _this.baseParams._assemble)
                                ps_1._assemble = _this.baseParams._assemble;
                            return query(_this.objectType, ps_1).then(callback_1);
                        }
                    });
                }
            }
            _super.prototype.setValue.call(this, v);
        };
        ObjCombo.prototype.setBaseParam = function (k, v, clear) {
            if (clear === void 0) { clear = true; }
            this.baseParams[k] = v;
            if (clear)
                this.clear();
        };
        return ObjCombo;
    }(_sui.Dropdown));
    _bean.ObjCombo = ObjCombo;
    function combo(objectType, el, options) {
        if (options === void 0) { options = undefined; }
        return new ObjCombo(objectType, el, options).render();
    }
    _bean.combo = combo;
    var AssembleFieldList = (function (_super) {
        __extends(AssembleFieldList, _super);
        function AssembleFieldList(obj, field, parentEl, options) {
            if (options === void 0) { options = {}; }
            var _this = _super.call(this, field.rtype, field.gridName || options.assembleGrid || 'mini', parentEl, options) || this;
            _this.obj = obj;
            _this.field = field;
            _this.showHead = false;
            _this.autoLoad = false;
            _this.pageSize = -1;
            _this.rowCheckable = false;
            _this.readOnly = true;
            _this.toolbar = "<div style=\"text-align:right;\">\n            <span class=\"_dp form compact mini ui\" style=\"display:inline-flex;width:200px;\"></span>\n            <div class=\"ui mini buttons compact\">\n                <button class=\"ui positive button _sel\">".concat(_locale.bean.selectThenAddText, "</button>\n                <div class=\"or\"></div>\n                <button class=\"ui button _add\">").concat(_locale.bean.quickAddText, "</button>\n            </div>");
            _this.toolbarAutoHide = false;
            _this.loadParamsFun = null;
            _this.loadFun = null;
            _this.loaded = false;
            _this.addedIds = [];
            _this.dirty = false;
            if (field.gridName) {
                _this.showHead = true;
            }
            if (field.rowCheckable) {
                _this.rowCheckable = field.rowCheckable;
            }
            return _this;
        }
        AssembleFieldList.prototype.prepare = function () {
            var _this = this;
            var actions = [];
            if (!this.readOnly && !this.field.readOnly && this.field._ui_fastModify !== false) {
                actions.push({
                    label: _locale.bean.modifyActionText, fn: function (item) {
                        _bean.showPostModal(_this.objectType, null, item.id, 'minimodify', { modalClass: 'small' }).then(function (data) {
                            _this.reload();
                        }, function () { });
                    }
                });
            }
            if (!this.readOnly)
                actions.push({
                    label: _locale.bean.moveActionText, fn: function (item) {
                        item = _this.data.find(function (d) { return d.id === item.id; });
                        _this.removeRow(item);
                        if (_this.addedIds.indexOf(item.id) !== -1)
                            _bean.remove(item.objectType, item.id);
                        _this.dirty = true;
                    }
                });
            this.objActions = actions;
            if (this.readOnly && this.options.toolbar === undefined) {
                this.toolbar = null;
            }
            if (this.field.ustomButtons) {
                this.toolbar = $('<div style="text-align:right;"><div class="ui mini buttons compact"></div>');
            }
            return _super.prototype.prepare.call(this);
        };
        AssembleFieldList.prototype.doRender = function (el, pre) {
            var _this = this;
            _super.prototype.doRender.call(this, el, pre);
            if (!this.readOnly) {
                var field = this.field, fieldType = field.formType || field.type;
                var dropdownOptions = field.dropdownOptions;
                if (typeof dropdownOptions === 'string')
                    dropdownOptions = parsekv(dropdownOptions);
                var baseParams = { _human: true };
                if (field.baseParams) {
                    var bp = field.baseParams;
                    if (typeof bp === 'string') {
                        bp = parsekv(field.baseParams);
                    }
                    baseParams = Object.assign(baseParams, bp);
                }
                dropdownOptions = dropdownOptions || {};
                dropdownOptions.baseParams = baseParams;
                var objField_1 = new ObjField(field, this.toolbar.find('._dp'), { inline: true, dropdownOptions: dropdownOptions, templateClass: 'mini', templateCss: { width: '600px' } });
                var combo_1;
                objField_1.render().rendered().then(function () {
                    objField_1.labelEl.hide();
                    combo_1 = objField_1.dropdown;
                });
                var addBtn = this.toolbar.find('button._sel');
                addBtn.click(function () {
                    var selected = combo_1.getSelectedItem();
                    if (selected) {
                        if (_this.data && _this.data.find(function (item) { return item.id === selected.id; }))
                            return;
                        _this.addRow(selected);
                        _this.dirty = true;
                    }
                });
                var newBtn = this.toolbar.find('button._add');
                newBtn.click(function () {
                    _bean.showPostModal(_this.objectType, null, {}, 'miniadd', { modalClass: 'small' }).then(function (data) {
                        _this.addedIds.push(data.id);
                        _this.addRow(data);
                        _this.dirty = true;
                    }, function () { });
                });
                if (this.field._ui_assembleSearchAdd === false) {
                    combo_1.rendered().then(function (e) { return combo_1.getEl().addClass('disabled'); });
                    addBtn.addClass('disabled');
                }
                if (this.field._ui_fastAdd === false)
                    newBtn.addClass('disabled');
            }
        };
        AssembleFieldList.prototype.load = function (params, num) {
            var _this = this;
            if (params === void 0) { params = {}; }
            if (num === void 0) { num = 0; }
            if (this.loadFun) {
                this.loaded = true;
                this.setPageNum(num);
                _super.prototype.showLoading.call(this, true);
                _util.dataCallback(function (res) {
                    _this.setData(res);
                    _super.prototype.showLoading.call(_this, false);
                }, this.loadFun, [this.obj, num, this.pageSize]);
            }
            else if (this.loadParamsFun) {
                this.loaded = true;
                var loadParams = this.loadParamsFun(this.obj);
                if (loadParams) {
                    this.setPageNum(num);
                    _super.prototype.showLoading.call(this, true);
                    _bean.page(loadParams.objectType, loadParams, num, this.pageSize).then(function (res) {
                        _this.setData(res);
                        _super.prototype.showLoading.call(_this, false);
                    });
                }
            }
            else if (!this.loaded) {
                this.loaded = true;
                if (this.obj) {
                    if (this.obj[this.field.name]) {
                        this.setData(this.obj[this.field.name]);
                        return Promise.resolve(this.obj[this.field.name]);
                    }
                    else if (this.obj.id)
                        return find(this.obj.objectType || this.objectType, { id: this.obj.id, _assemble: this.field.name, _human: 'relation' }).then(function (obj) {
                            _this.setData(obj[_this.field.name]);
                            return obj[_this.field.name];
                        });
                }
            }
        };
        AssembleFieldList.prototype.reload = function (params) {
            if (params === void 0) { params = {}; }
            this.loaded = false;
            this.dirty = false;
            this.load(params);
        };
        AssembleFieldList.prototype.getIds = function (dirty) {
            if (dirty === void 0) { dirty = false; }
            if (!this.data)
                return null;
            if (!this.dirty && dirty)
                return null;
            return this.data.map(function (r) { return r.id; }).join(',');
        };
        AssembleFieldList.prototype.addCustomButtons = function (buttonName, icon, fun) {
            var _this = this;
            var buttonsEl = this.toolbar.find('.buttons');
            var button = $('<button>').addClass('ui primary button').appendTo(buttonsEl).click(function () {
                fun(_this.data, _this);
            });
            if (icon) {
                $('<i>').addClass('icon').addClass(icon).appendTo(button);
            }
            button.append(buttonName);
        };
        AssembleFieldList.prototype.addCustomLoadParamsFun = function (loadParamsFun, pageSize) {
            if (pageSize === void 0) { pageSize = 20; }
            this.loadParamsFun = loadParamsFun;
            this.pageSize = pageSize;
        };
        AssembleFieldList.prototype.addCustomLoadFun = function (loadFun, pageSize) {
            if (pageSize === void 0) { pageSize = 20; }
            this.loadFun = loadFun;
            this.pageSize = pageSize;
        };
        return AssembleFieldList;
    }(Table));
    _bean.AssembleFieldList = AssembleFieldList;
    var Tree = (function (_super) {
        __extends(Tree, _super);
        function Tree(parentEl, options) {
            if (options === void 0) { options = {}; }
            var _this = _super.call(this, parentEl, Object.assign({}, options)) || this;
            _this.blankName = _locale.unknowText || 'unknown';
            _this.enablePopupItem = true;
            _this.listenObjUpdate = true;
            _this.showCheck = false;
            _this.titleField = options.titleField || _this.getLabel.bind(_this);
            return _this;
        }
        Tree.prototype._createPopupSpan = function (iconClass) {
            if (iconClass === void 0) { iconClass = undefined; }
            iconClass = iconClass || 'sidebar';
            return $('<span class="_objpopup ui dropdown"><i class="icon ' + iconClass + '"></i><div class="ui menu treemenu"></div></span>').click(function (e) {
                return false;
            });
        };
        Tree.prototype.prepare = function () {
            _super.prototype.prepare.call(this);
            var self = this;
            if (this.actionField === undefined && this.enablePopupItem) {
                this.actionField = function () {
                    return self._createPopupSpan();
                };
            }
            var userLoader = this.loader;
            this.loader = function (item, treeRefreshParams) {
                var ret = userLoader ? userLoader(item, treeRefreshParams) : undefined;
                if (ret)
                    return ret;
                if (item.childrenType && item.baseParams)
                    return _bean.query(item.childrenType, item.baseParams);
            };
        };
        Tree.prototype.doRender = function (el, pre) {
            _super.prototype.doRender.call(this, el, pre);
            var thiz = this;
            if (this.listenObjUpdate)
                _context.events.on(_bean.EVENT_OBJ, function (e, params) {
                    var obj = params;
                    if (obj.data) {
                        if (typeof obj.data === 'string')
                            obj.id = obj.data;
                        else
                            obj = obj.data;
                    }
                    var objtype = obj.objectType;
                    if (obj && obj._paramsJson)
                        obj = JSON.parse(obj._paramsJson);
                    objtype = objtype || obj.objectType || params.objectType;
                    var objid = obj.id || params.id;
                    if (!objid && Array.isArray(obj))
                        objid = obj.map(function (o) { return o.id; });
                    if (objid && typeof objid === 'string' && objid.indexOf(',') !== -1) {
                        objid = objid.split(',');
                        objid = objid.map(function (i) { return i.trim(); });
                    }
                    var objnodes = thiz.templateEl.find('.content').filter(function (i, item) {
                        var contentEl = $(item);
                        var d = contentEl.data('userdata');
                        if (!d)
                            return false;
                        var ch = thiz.getChildren(d);
                        if (ch && ch.find(function (c) { return c.objectType === objtype && (Array.isArray(objid) ? objid.indexOf(c.id) !== -1 : c.id === objid); }))
                            return true;
                        return thiz.hasChildrenType(d, obj);
                    });
                    $.each(objnodes, function (i, p) {
                        thiz.refreshNode(null, $(p));
                    });
                });
        };
        Tree.prototype.redrawNodeTitle = function (node, item) {
            var _this = this;
            _super.prototype.redrawNodeTitle.call(this, node, item);
            var menuIcon = node.find('._objpopup');
            if (this.enablePopupItem && menuIcon.length && item.actions !== false && item.objectType) {
                _bean.menuDropdown(menuIcon, item, item.objectType, {
                    data: this.popupActions,
                    onShow: function () {
                        _this.lockActionHide = true;
                    }, onHide: function () {
                        setTimeout(function () {
                            _this.lockActionHide = false;
                        }, 100);
                        if (_this.actionHide)
                            menuIcon.parent().hide();
                    }
                }, this);
            }
            else
                menuIcon.hide();
        };
        Tree.prototype.getLabel = function (item) {
            var name = item.title || item.name || item.NAME || this.blankName;
            if (!name && item.objectType) {
                var om = getMetaForce(item.objectType);
                if (om)
                    name = item[om.labelField] || name;
            }
            return name;
        };
        Tree.prototype.equalsNode = function (o1, o2) {
            return equals(o1, o2);
        };
        Tree.prototype.findActionByName = function (name, item) {
            if (item.objectType)
                return getMeta(item.objectType).then(function (om) { return findAction(name, om); });
            return _super.prototype.findActionByName.call(this, name, item);
        };
        Tree.prototype.doAction = function (action, item) {
            _context.doAction(action, item);
        };
        Tree.prototype.hasChildrenType = function (item, child) {
            var parentId = this.getParentId(child);
            if (parentId && parentId === item.id)
                return true;
            if (typeof item.childrenType === 'string')
                return item.childrenType === child.objectType;
            if (Array.isArray(item.childrenType))
                return item.childrenType.indexOf(child.objectType) !== -1;
            return false;
        };
        Tree.prototype.getParentId = function (item) {
            var parentId = item.PARENT_ID || item.PARENTID || item.parent;
            if (parentId && typeof parentId === 'object')
                parentId = parentId.id;
            return parentId;
        };
        Tree.prototype.getActions = function (item) {
            var _this = this;
            var ret = _super.prototype.getActions.call(this, item);
            if (ret) {
                if (!Array.isArray(ret))
                    ret = [ret];
                ret = ret.map(function (a) {
                    if (typeof a === 'object' && !_util.isjquery(a) && a.icon && a.icon.indexOf('_objpopup') !== -1) {
                        a = _this._createPopupSpan(a.icon.replace('_objpopup', '').trim());
                    }
                    return a;
                });
            }
            return ret;
        };
        return Tree;
    }(_sui.CheckTree));
    _bean.Tree = Tree;
    function tree(parentEl, options) {
        if (options === void 0) { options = null; }
        return new Tree(parentEl, options).render();
    }
    _bean.tree = tree;
    function checktree(parentEl, options) {
        if (options === void 0) { options = null; }
        options = options || {};
        options.showCheck = true;
        return new Tree(parentEl, options).render();
    }
    _bean.checktree = checktree;
    var ObjectTypeBox = (function (_super) {
        __extends(ObjectTypeBox, _super);
        function ObjectTypeBox(cats, parentEl, options) {
            if (options === void 0) { options = undefined; }
            var _this = _super.call(this, parentEl, options) || this;
            _this.cats = cats;
            _this.template = '<div>';
            _this._items = {};
            return _this;
        }
        ObjectTypeBox.prototype.prepare = function () {
            var types = [];
            this.cats.forEach(function (cat) {
                cat.items.forEach(function (i) {
                    if (i.objectType)
                        types.push(i.objectType);
                });
            });
            return _bean.getMetas(types);
        };
        ObjectTypeBox.prototype.doRender = function (el, pre) {
            this.checkboxObjectTypeGroupByCategory(el, this.cats, this.storageName, this.itemFilter);
        };
        ObjectTypeBox.prototype.getCheckedTypes = function () {
            var nodes = this.templateEl.find('input.__objtype:checked'), types = [];
            nodes.each(function () {
                if (this.id)
                    types.push(this.id);
            });
            return types;
        };
        ObjectTypeBox.prototype.getCheckedTypeItems = function () {
            var _this = this;
            return this.getCheckedTypes().map(function (t) {
                return _this._items[t];
            });
        };
        ObjectTypeBox.prototype.checkboxObjectTypeGroupByCategory = function (el, cats, storageName, itemFilter) {
            var initSelected = {}, itemCache = this._items;
            if (storageName)
                initSelected = _util.getStorage(storageName) || {};
            cats.forEach(function (cat) {
                var items = cat.items.filter(function (i) {
                    if (itemFilter)
                        return itemFilter(i);
                    return i.objectType;
                });
                if (!items.length)
                    return;
                var row = $('<div>').css({ display: 'table-row' }).appendTo(el);
                var tit = $('<div>').css({ display: 'table-cell', fontWeight: 'bold', width: '6em', fontSize: 'smaller' }).html(cat.title).appendTo(row);
                var checkAllEl = $('<input type="checkbox">').css('vertical-align', 'middle').prependTo(tit).change(function () {
                    var checked = $(this).prop('checked');
                    items.forEach(function (i) {
                        var old = i.checkInputEl.prop('checked');
                        if (old !== checked)
                            i.checkInputEl.prop('checked', checked).change();
                    });
                });
                var itemBox = $('<div>').css({ display: 'table-cell' }).appendTo(row);
                cat.updateCheck = function () {
                    checkAllEl.prop('checked', itemBox.find('input:checked').length > 0);
                };
                items.forEach(function (item) {
                    itemCache[item.objectType] = item;
                    var label = _bean.getMetaForce(item.objectType).label;
                    item.title = item.title || label;
                    var selected = initSelected[item.objectType];
                    if (selected !== undefined)
                        item.selected = selected;
                    var ob = $('<div>').css({ display: 'inline-block', padding: '2px', whiteSpace: 'nowrap' }).appendTo(itemBox);
                    item.checkInputEl = $('<input type="checkbox" class="__objtype">').attr('id', item.objectType).css('vertical-align', 'middle').appendTo(ob).prop('checked', item.selected).change(function () {
                        item.check($(this).prop('checked'));
                        cat.updateCheck();
                    });
                    if (item.selected)
                        checkAllEl.prop('checked', true);
                    item.check = function (v) {
                        item.selected = initSelected[item.objectType] = v;
                        if (storageName)
                            _util.setStorage(storageName, initSelected);
                    };
                    var objBox = $('<a style="cursor:pointer;display:inline-block;font-size:smaller;padding:2px;white-space:nowrap;position:relative;">').addClass('').html(item.title).appendTo(ob);
                    if (item.open)
                        objBox.click(function () { return item.open(); });
                });
            });
        };
        return ObjectTypeBox;
    }(_sui.Component));
    _bean.ObjectTypeBox = ObjectTypeBox;
    function objectTypeBox(el, cats, itemFilter, storageName, options) {
        if (options === void 0) { options = undefined; }
        return new ObjectTypeBox(cats, el, Object.assign({ itemFilter: itemFilter, storageName: storageName }, options)).render();
    }
    _bean.objectTypeBox = objectTypeBox;
    function createChartOption(objectType, grid, data, nodedef) {
        var fields = nodedef.fields, metaGrid = grid.metaGrid;
        if (typeof fields === 'string')
            fields = fields.split(',');
        var chartType = nodedef.type || metaGrid.chartType;
        var fs = metaGrid.fields.filter(function (f) { return fields ? fields.indexOf(f.name) !== -1 : f.metric !== undefined; });
        data = data || [];
        var names = [], datas = [], chartTypes = [];
        var catField = metaGrid.chartCategoryField || 'name';
        var dataFn = nodedef.dataFn;
        if (dataFn) {
            if (dataFn === 'sum' || dataFn === 'avg') {
                var r_1 = {};
                r_1[catField] = nodedef.sumText || '';
                fs.forEach(function (f) {
                    r_1[f.name] = 0;
                });
                data.forEach(function (d) {
                    fs.forEach(function (f) {
                        r_1[f.name] += d[f.name] || 0;
                    });
                });
                if (dataFn === 'avg')
                    fs.forEach(function (f) {
                        r_1[f.name] /= data.length;
                    });
                data = [r_1];
            }
            else if (dataFn === 'reverse')
                data = data.reverse();
            else if (typeof dataFn === 'function')
                data = dataFn(data);
            else if (window[dataFn]) {
                var w = window;
                data = w[dataFn](data);
            }
        }
        var cats = data.map(function (row) { return row[catField]; });
        var option = {
            title: { text: metaGrid.chartTitle }, tooltip: {},
            legend: { data: names },
            xAxis: { data: cats }, yAxis: {},
            series: []
        };
        if (chartType === 'pie') {
            delete option.xAxis;
            delete option.yAxis;
        }
        var customOptions = nodedef.options;
        if (typeof customOptions === 'string')
            customOptions = JSON.parse(customOptions);
        if (customOptions) {
            _util.merge(option, customOptions);
        }
        if (typeof nodedef.seriesOptions === 'string') {
            nodedef.seriesOptions = JSON.parse(nodedef.seriesOptions);
        }
        var promises = [];
        fs.forEach(function (f) {
            names.push(f.label);
            chartTypes.push(f.chartType || chartType);
            var chartData, isGroupData;
            isGroupData = nodedef.groupData || f.chartGroupData || f.type === 'dict';
            if (isGroupData)
                chartData = _bean.groupby(objectType, f.name + ' as name', 'count', '1 as value', Object.assign({ _human: true }, grid.getQueringParams(false)));
            if (!chartData)
                chartData = data.map(function (row) { return row[f.name]; });
            datas.push(chartData);
        });
        var ps = [];
        var _loop_2 = function () {
            var d = datas[i];
            var field = fs[i];
            var s = {};
            if (_util.isPromise(d)) {
                var j_1 = i;
                ps.push(d.then(function (gd) {
                    var colors;
                    var defaultColors = ['#c23531', '#2f4554', '#61a0a8', '#d48265', '#91c7ae', '#749f83', '#ca8622', '#bda29a', '#6e7074', '#546570', '#c4ccd3'];
                    if (nodedef.items) {
                        colors = gd.map(function (it) {
                            var colorItem = nodedef.items.find(function (item) { return item.value == it.name_value; });
                            return colorItem ? colorItem.color : defaultColors[Math.floor(Math.random() * defaultColors.length)];
                        });
                    }
                    option.series[j_1].data = gd;
                    option.series[j_1].color = colors;
                }));
            }
            if (field.chartSeries)
                field.chartSeries.forEach(function (se) {
                    for (var k in se) {
                        var v = se[k];
                        if (Array.isArray(v) && v.length === 1 && k.endsWith('s')) {
                            v = v[0];
                            k = k.substr(0, k.length - 1);
                        }
                        s[k] = v;
                    }
                });
            s = Object.assign(s, nodedef, nodedef.seriesOptions, { name: names[i], type: chartTypes[i], data: datas[i] });
            if (field.chartCenter)
                s.center = field.chartCenter.split(',');
            option.series[i] = s;
        };
        for (var i = 0; i < names.length; i++) {
            _loop_2();
        }
        return [option, ps.length ? Promise.all(ps) : null];
    }
    function charts(container, grid) {
        var metaGrid = grid.metaGrid, objectType = grid.objectType, om = getMetaForce(objectType);
        var charts = metaGrid.charts;
        if (charts) {
            charts.forEach(function (ch) {
                var chartDiv = $('<div>').appendTo($(container)).css({ width: '100%' });
                if (ch.style)
                    chartDiv.attr('style', ch.style);
                chart(chartDiv, objectType, grid, ch, null);
            });
        }
    }
    _bean.charts = charts;
    function chart(container, objectType, grid, nodedef, data) {
        if (data === void 0) { data = null; }
        if (_util.isjquery(container))
            container = container[0];
        var isDataGrid = false, metaGrid;
        if (grid.metaGrid) {
            metaGrid = grid.metaGrid;
            isDataGrid = true;
        }
        else
            metaGrid = grid;
        _util.loadScript('lib/echarts/echarts.common.min.js', 'echarts').then(function () {
            var chart = echarts.init(container);
            var chartData = data || grid.data;
            function setchartoption(nd) {
                var _a = createChartOption(objectType, grid, nd, nodedef), o = _a[0], pm = _a[1];
                if (pm)
                    pm.then(function (no) {
                        chart.setOption(o);
                    });
                else
                    chart.setOption(o);
            }
            if (isDataGrid && metaGrid.chartAllData) {
                var params = Object.assign({ _metagrid: grid.gridName || '' }, grid.baseParams);
                query(grid.objectType, params).then(function (nd) { return setchartoption(nd); });
            }
            else {
                setchartoption(chartData);
                if (isDataGrid)
                    grid.on('setdata', function (e, nd) { return setchartoption(nd.data); });
            }
        });
    }
    _bean.chart = chart;
})(_bean || (_bean = {}));
//# sourceMappingURL=_bean.js.map