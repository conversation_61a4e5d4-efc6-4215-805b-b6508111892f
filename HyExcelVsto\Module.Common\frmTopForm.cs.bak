﻿/*
 * ========================================================================
 * VSTO插件窗体顶层显示逻辑实现说明
 * ========================================================================
 *
 * 【核心原理】
 * VSTO插件中的窗体管理采用"参照窗体"模式，通过创建一个TopMostForm作为所有插件窗体的
 * 统一父窗体，然后将TopMostForm设置为Excel主窗体的子窗体，从而实现所有插件窗体都能
 * 显示在Excel前景的效果。
 *
 * 【实现架构】
 * Excel主窗体
 *   └── TopMostForm (frmTopMostForm) ← 参照窗体，Excel的子窗体
 *       ├── LongStringDisplayForm ← 长字符串显示窗体
 *       ├── DropdownInputForm ← 下拉选择输入窗体
 *       ├── CrosshairOverlayForm ← 十字光标覆盖层窗体
 *       └── 其他插件窗体...
 *
 * 【技术实现流程】
 * 1. 创建TopMostForm实例
 * 2. 获取Excel主窗体句柄 (xlApp.Hwnd)
 * 3. 使用Win32 API SetParent将TopMostForm设为Excel子窗体
 * 4. 其他插件窗体使用TopMostForm作为父窗体显示
 * 5. 持续监控Excel窗体句柄变化，自动修复父子关系
 *
 * 【关键Win32 API】
 * - SetParent(hWndChild, hWndNewParent): 设置父子窗体关系
 * - GetAncestor(hwnd, GA_PARENT): 获取父窗体句柄
 * - IsWindow(hWnd): 验证窗体句柄有效性
 * - GetWindowLongPtr/SetWindowLongPtr: 设置窗体样式
 *
 * 【失效原因分析】
 * 1. Excel窗体句柄变化：Excel在某些操作后会重新创建主窗体
 *    - 窗体最大化/最小化/还原
 *    - 多Excel实例切换
 *    - 进入/退出全屏模式
 *    - DPI变化或显示器配置改变
 *
 * 2. 系统级事件影响：
 *    - 系统睡眠/恢复
 *    - 用户会话切换
 *    - 显示设置变更
 *    - 第三方窗体管理软件干预
 *
 * 3. 父子关系丢失：
 *    - Windows系统自动断开关系
 *    - Excel内部窗体重建
 *    - COM组件异常
 *
 * 【解决方案架构】
 * 1. 多层监控机制：
 *    - Excel窗体句柄监控 (5秒间隔定时检查)
 *    - Excel事件监控 (窗体激活、工作簿切换等)
 *    - 系统事件监控 (电源、显示、会话事件)
 *
 * 2. 智能检测算法：
 *    - 句柄变化检测：对比当前句柄与缓存句柄
 *    - 父子关系验证：使用GetAncestor验证关系正确性
 *    - 窗体有效性检查：使用IsWindow验证句柄有效性
 *
 * 3. 自动修复机制：
 *    - 检测到问题立即重新设置父子关系
 *    - 异常情况下的错误恢复
 *    - 延迟验证确保系统状态稳定
 *
 * 【WPS兼容性处理】
 * WPS Office与Microsoft Excel的窗体管理机制不同，需要特殊处理：
 * - WPS环境下直接使用WPS主窗体作为父窗体
 * - 检测GlobalSettings.IsWps标志进行分支处理
 * - 提供回退机制确保兼容性
 *
 * 【性能优化策略】
 * 1. 最小化监控开销：
 *    - 5秒间隔的定时检查（平衡及时性和性能）
 *    - 智能跳过不必要的操作
 *    - 异步处理避免阻塞Excel界面
 *
 * 2. 内存管理：
 *    - 及时释放无效的窗体引用
 *    - 正确注销事件监听器
 *    - 避免内存泄漏
 *
 * 3. 错误处理：
 *    - 分类异常处理（COM、Win32、通用）
 *    - 详细日志记录便于诊断
 *    - 优雅降级和错误恢复
 *
 * 【调试和诊断】
 * 1. 日志记录：
 *    - 使用ETLogManager记录所有关键操作
 *    - 包含句柄值、时间戳、操作结果
 *    - 分级记录（Info、Warning、Error）
 *
 * 2. 状态监控：
 *    - 实时监控Excel窗体句柄
 *    - 跟踪父子关系状态
 *    - 记录系统事件影响
 *
 * 3. 手动修复：
 *    - ForceRestoreParentRelation()方法
 *    - 支持外部调用强制修复
 *    - 适用于特殊情况下的手动干预
 *
 * 【使用示例】
 * // 启动窗体管理
 * TopMostForm.Start();
 *
 * // 显示插件窗体
 * form.Show(TopMostForm);
 *
 * // 手动修复关系（如需要）
 * TopMostForm.ForceRestoreParentRelation();
 *
 * // 停止窗体管理
 * TopMostForm.Stop();
 *
 * 【注意事项】
 * 1. 确保在Excel完全加载后再启动TopMostForm
 * 2. 在Excel关闭前正确停止监控机制
 * 3. 异常情况下要有适当的错误恢复
 * 4. 考虑多Excel实例的情况
 * 5. 注意WPS环境的特殊处理
 *
 * ========================================================================
 */

using ET;
using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// 顶层窗体类，用于管理所有子窗体与Excel的层级关系
    /// </summary>
    /// <remarks>此窗体作为Excel的子窗体，确保所有插件窗体都显示在Excel前景。 包含Excel窗口句柄监控机制，自动检测并修复父子关系失效问题。</remarks>
    public partial class frmTopMostForm : Form
    {
        #region Win32 API声明

        // Win32 API常量定义
        private const int GA_PARENT = 1;

        private const int GA_ROOT = 2;
        private const int GA_ROOTOWNER = 3;

        [DllImport("user32.dll")]
        private static extern IntPtr GetAncestor(IntPtr hwnd, int flags);

        #endregion Win32 API声明

        #region 窗口句柄监控字段

        /// <summary>
        /// Excel窗口句柄监控定时器
        /// </summary>
        private Timer _excelHwndMonitor;

        /// <summary>
        /// 上次记录的Excel窗口句柄
        /// </summary>
        private IntPtr _lastExcelHwnd = IntPtr.Zero;

        /// <summary>
        /// 监控是否已启动
        /// </summary>
        private bool _monitoringStarted = false;

        /// <summary>
        /// 监控间隔（毫秒）
        /// </summary>
        private const int MONITOR_INTERVAL = 5000;

        /// <summary>
        /// 系统事件监控是否已启动
        /// </summary>
        private bool _systemEventMonitoringStarted = false;

        #endregion 窗口句柄监控字段

        #region 构造函数和初始化

        /// <summary>
        /// 构造函数
        /// </summary>
        public frmTopMostForm()
        {
            InitializeComponent();
            InitializeExcelHwndMonitor();
        }

        /// <summary>
        /// 初始化Excel窗口句柄监控器
        /// </summary>
        private void InitializeExcelHwndMonitor()
        {
            try
            {
                _excelHwndMonitor = new Timer();
                _excelHwndMonitor.Interval = MONITOR_INTERVAL;
                _excelHwndMonitor.Tick += ExcelHwndMonitor_Tick;

                ETLogManager.Info("Excel窗口句柄监控器初始化完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"初始化Excel窗口句柄监控器失败: {ex.Message}", ex);
            }
        }

        #endregion 构造函数和初始化

        #region Excel窗口句柄监控机制

        /// <summary>
        /// 启动Excel窗口句柄监控
        /// </summary>
        private void StartExcelHwndMonitoring()
        {
            try
            {
                if (_monitoringStarted || _excelHwndMonitor == null)
                    return;

                // 记录当前Excel窗口句柄
                _lastExcelHwnd = GetCurrentExcelHwnd();

                _excelHwndMonitor.Start();
                _monitoringStarted = true;

                ETLogManager.Info($"Excel窗口句柄监控已启动，监控间隔: {MONITOR_INTERVAL}ms，初始句柄: {_lastExcelHwnd}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"启动Excel窗口句柄监控失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 停止Excel窗口句柄监控
        /// </summary>
        private void StopExcelHwndMonitoring()
        {
            try
            {
                if (!_monitoringStarted || _excelHwndMonitor == null)
                    return;

                _excelHwndMonitor.Stop();
                _monitoringStarted = false;

                ETLogManager.Info("Excel窗口句柄监控已停止");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"停止Excel窗口句柄监控失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Excel窗口句柄监控定时器事件
        /// </summary>
        private void ExcelHwndMonitor_Tick(object sender, EventArgs e)
        {
            try
            {
                IntPtr currentExcelHwnd = GetCurrentExcelHwnd();

                // 检查Excel窗口句柄是否发生变化
                if (currentExcelHwnd != _lastExcelHwnd)
                {
                    ETLogManager.Warning($"检测到Excel窗口句柄变化: {_lastExcelHwnd} -> {currentExcelHwnd}");

                    _lastExcelHwnd = currentExcelHwnd;

                    // 重新设置父子窗口关系
                    SetTop();
                }

                // 验证当前父子关系是否正确
                if (!ValidateParentChildRelation())
                {
                    ETLogManager.Warning("检测到父子窗口关系异常，尝试修复");
                    SetTop();
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"Excel窗口句柄监控检查失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取当前Excel窗口句柄
        /// </summary>
        /// <returns>Excel窗口句柄，失败时返回IntPtr.Zero</returns>
        private IntPtr GetCurrentExcelHwnd()
        {
            try
            {
                Microsoft.Office.Interop.Excel.Application xlApp = Globals.ThisAddIn.Application;
                if (xlApp == null)
                    return IntPtr.Zero;

                return new IntPtr(xlApp.Hwnd);
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"获取Excel窗口句柄失败: {ex.Message}", ex);
                return IntPtr.Zero;
            }
        }

        /// <summary>
        /// 验证父子窗口关系是否正确
        /// </summary>
        /// <returns>关系正确返回true，否则返回false</returns>
        private bool ValidateParentChildRelation()
        {
            try
            {
                IntPtr currentParent = GetAncestor(Handle, GA_PARENT);
                IntPtr excelHwnd = GetCurrentExcelHwnd();

                // 检查父窗口关系是否正确
                if (currentParent != excelHwnd)
                {
                    ETLogManager.Warning($"父子窗口关系不正确. 当前父窗口: {currentParent}, 期望父窗口: {excelHwnd}");
                    return false;
                }

                // 检查Excel窗口是否仍然有效
                if (excelHwnd != IntPtr.Zero && !WinAPI.IsWindow(excelHwnd))
                {
                    ETLogManager.Warning($"Excel窗口句柄无效: {excelHwnd}");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"验证父子窗口关系失败: {ex.Message}", ex);
                return false;
            }
        }

        #endregion Excel窗口句柄监控机制

        #region 系统事件监控机制

        /// <summary>
        /// 启动系统事件监控
        /// </summary>
        private void StartSystemEventMonitoring()
        {
            try
            {
                if (_systemEventMonitoringStarted)
                    return;

                // 监控系统电源事件
                Microsoft.Win32.SystemEvents.PowerModeChanged += SystemEvents_PowerModeChanged;

                // 监控显示设置变更事件
                Microsoft.Win32.SystemEvents.DisplaySettingsChanged += SystemEvents_DisplaySettingsChanged;

                // 监控会话切换事件
                Microsoft.Win32.SystemEvents.SessionSwitch += SystemEvents_SessionSwitch;

                _systemEventMonitoringStarted = true;
                ETLogManager.Info("系统事件监控已启动");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"启动系统事件监控失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 停止系统事件监控
        /// </summary>
        private void StopSystemEventMonitoring()
        {
            try
            {
                if (!_systemEventMonitoringStarted)
                    return;

                Microsoft.Win32.SystemEvents.PowerModeChanged -= SystemEvents_PowerModeChanged;
                Microsoft.Win32.SystemEvents.DisplaySettingsChanged -= SystemEvents_DisplaySettingsChanged;
                Microsoft.Win32.SystemEvents.SessionSwitch -= SystemEvents_SessionSwitch;

                _systemEventMonitoringStarted = false;
                ETLogManager.Info("系统事件监控已停止");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"停止系统事件监控失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 系统电源模式变更事件处理
        /// </summary>
        private void SystemEvents_PowerModeChanged(object sender, Microsoft.Win32.PowerModeChangedEventArgs e)
        {
            try
            {
                ETLogManager.Info($"系统电源模式变更: {e.Mode}");

                if (e.Mode == Microsoft.Win32.PowerModes.Resume)
                {
                    // 系统从睡眠/休眠恢复，延迟重新建立窗口关系
                    System.Threading.Tasks.Task.Run(async () =>
                    {
                        await System.Threading.Tasks.Task.Delay(2000); // 等待系统完全恢复
                        ForceRestoreParentRelation();
                        ETLogManager.Info("系统恢复后TopForm关系已重建");
                    });
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"处理系统电源事件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 显示设置变更事件处理
        /// </summary>
        private void SystemEvents_DisplaySettingsChanged(object sender, EventArgs e)
        {
            try
            {
                ETLogManager.Info("显示设置已变更");

                // 显示设置变更可能影响窗口位置和层级关系
                System.Threading.Tasks.Task.Run(async () =>
                {
                    await System.Threading.Tasks.Task.Delay(1000); // 等待显示设置生效
                    ForceRestoreParentRelation();
                    ETLogManager.Info("显示设置变更后TopForm关系已重建");
                });
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"处理显示设置变更事件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 会话切换事件处理
        /// </summary>
        private void SystemEvents_SessionSwitch(object sender, Microsoft.Win32.SessionSwitchEventArgs e)
        {
            try
            {
                ETLogManager.Info($"会话切换事件: {e.Reason}");

                if (e.Reason == Microsoft.Win32.SessionSwitchReason.SessionUnlock ||
                    e.Reason == Microsoft.Win32.SessionSwitchReason.SessionLogon)
                {
                    // 会话解锁或登录后重新建立窗口关系
                    System.Threading.Tasks.Task.Run(async () =>
                    {
                        await System.Threading.Tasks.Task.Delay(1500); // 等待会话完全恢复
                        ForceRestoreParentRelation();
                        ETLogManager.Info("会话恢复后TopForm关系已重建");
                    });
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"处理会话切换事件失败: {ex.Message}", ex);
            }
        }

        #endregion 系统事件监控机制

        #region 窗体事件处理

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void frmTopForm_Load(object sender, EventArgs e)
        {
            // 不在Load事件中立即调用Start()，避免阻塞Excel界面 Start()方法将由ThisAddIn在适当的时机调用
            ETLogManager.Info("TopMostForm窗体加载完成");
        }

        /// <summary>
        /// 原有的定时器事件（已废弃，由新的监控机制替代）
        /// </summary>
        private void CheckExcelActiveTimer_Tick(object sender, EventArgs e)
        {
            // 此方法已被新的Excel窗口句柄监控机制替代 保留此方法以维持向后兼容性
        }

        #endregion 窗体事件处理

        #region 核心窗体管理方法

        /// <summary>
        /// 设置TopForm为Excel的子窗体
        /// </summary>
        /// <remarks>此方法是窗体层级管理的核心，确保TopForm始终作为Excel的子窗体显示。 增强了错误处理、日志记录和异常恢复机制。</remarks>
        private void SetTop()
        {
            try
            {
                // 获取Excel应用程序的句柄
                Microsoft.Office.Interop.Excel.Application xlApp = Globals.ThisAddIn.Application;
                if (xlApp == null)
                {
                    ETLogManager.Warning("SetTop: Excel应用程序对象为空");
                    return;
                }

                IntPtr excelHwnd = new(xlApp.Hwnd);
                if (excelHwnd == IntPtr.Zero)
                {
                    ETLogManager.Warning("SetTop: Excel窗口句柄无效");
                    return;
                }

                // 检查Excel窗口是否有效
                if (!WinAPI.IsWindow(excelHwnd))
                {
                    ETLogManager.Warning($"SetTop: Excel窗口无效 (句柄: {excelHwnd})");
                    return;
                }

                // 如果当前已经是正确的父窗口，则不需要重新设置
                IntPtr currentParent = GetAncestor(Handle, GA_PARENT);
                if (currentParent == excelHwnd)
                {
                    ETLogManager.Info($"SetTop: 父子窗口关系已正确 (Excel句柄: {excelHwnd})");
                    return;
                }

                // 记录父窗口变更信息
                ETLogManager.Info($"SetTop: 更新父窗口关系 - 当前父窗口: {currentParent}, 新父窗口: {excelHwnd}");

                // 设置为Excel窗口的子窗口
                ETForm.AsChildWindow(this, excelHwnd);

                // 验证设置是否成功
                IntPtr newParent = GetAncestor(Handle, GA_PARENT);
                if (newParent == excelHwnd)
                {
                    ETLogManager.Info($"SetTop: 成功设置父窗口关系 (Excel句柄: {excelHwnd})");
                }
                else
                {
                    ETLogManager.Error($"SetTop: 设置父窗口关系失败 - 期望: {excelHwnd}, 实际: {newParent}");
                }
            }
            catch (System.Runtime.InteropServices.COMException comEx)
            {
                ETLogManager.Error($"SetTop: COM异常 - {comEx.Message} (错误代码: 0x{comEx.ErrorCode:X})", comEx);
                TryRecoverFromError();
            }
            catch (System.ComponentModel.Win32Exception win32Ex)
            {
                ETLogManager.Error($"SetTop: Win32异常 - {win32Ex.Message} (错误代码: {win32Ex.NativeErrorCode})", win32Ex);
                TryRecoverFromError();
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"SetTop: 一般异常 - {ex.Message}", ex);
                TryRecoverFromError();
            }
        }

        /// <summary>
        /// 尝试从错误中恢复
        /// </summary>
        private void TryRecoverFromError()
        {
            try
            {
                ETLogManager.Info("SetTop: 尝试错误恢复 - 重置父窗口关系");
                ETForm.AsChildWindow(this, IntPtr.Zero);

                // 等待短暂时间后重试
                System.Threading.Thread.Sleep(100);

                // 重新尝试设置
                IntPtr excelHwnd = GetCurrentExcelHwnd();
                if (excelHwnd != IntPtr.Zero && WinAPI.IsWindow(excelHwnd))
                {
                    ETForm.AsChildWindow(this, excelHwnd);
                    ETLogManager.Info("SetTop: 错误恢复成功");
                }
            }
            catch (Exception recoveryEx)
            {
                ETLogManager.Error($"SetTop: 错误恢复失败 - {recoveryEx.Message}", recoveryEx);
            }
        }

        #endregion 核心窗体管理方法

        #region 公共接口方法

        /// <summary>
        /// 启动TopForm管理功能
        /// </summary>
        /// <remarks>启动窗体层级管理，包括设置父子关系和启动监控机制。 此方法由ThisAddIn在适当时机调用。</remarks>
        public void Start()
        {
            try
            {
                if (!GlobalSettings.EnableHelpForm)
                {
                    ETLogManager.Info("TopMostForm.Start: 辅助窗体功能已禁用，跳过启动");
                    return;
                }

                ETLogManager.Info("TopMostForm.Start: 开始启动窗体管理功能");

                // 设置父子窗口关系
                SetTop();

                // 启动Excel窗口句柄监控
                StartExcelHwndMonitoring();

                // 启动系统事件监控
                StartSystemEventMonitoring();

                ETLogManager.Info("TopMostForm.Start: 窗体管理功能启动完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"TopMostForm.Start: 启动失败 - {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 停止TopForm管理功能
        /// </summary>
        /// <remarks>停止窗体层级管理，包括断开父子关系和停止监控机制。 此方法在插件卸载或Excel关闭时调用。</remarks>
        public void Stop()
        {
            try
            {
                ETLogManager.Info("TopMostForm.Stop: 开始停止窗体管理功能");

                // 停止系统事件监控
                StopSystemEventMonitoring();

                // 停止Excel窗口句柄监控
                StopExcelHwndMonitoring();

                // 断开父子窗口关系
                ETForm.AsChildWindow(this, IntPtr.Zero);

                ETLogManager.Info("TopMostForm.Stop: 窗体管理功能停止完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"TopMostForm.Stop: 停止失败 - {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 强制恢复父子窗口关系
        /// </summary>
        /// <remarks>当检测到窗体层级关系异常时，可调用此方法强制恢复。 此方法会先断开当前关系，然后重新建立正确的父子关系。</remarks>
        public void ForceRestoreParentRelation()
        {
            try
            {
                ETLogManager.Info("TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系");

                // 强制断开当前父子关系
                ETForm.AsChildWindow(this, IntPtr.Zero);

                // 等待一小段时间让系统处理
                System.Threading.Thread.Sleep(100);

                // 重新建立父子关系
                SetTop();

                // 重置监控状态
                _lastExcelHwnd = GetCurrentExcelHwnd();

                ETLogManager.Info("TopMostForm.ForceRestoreParentRelation: 强制恢复完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - {ex.Message}", ex);
            }
        }

        #endregion 公共接口方法
    }
}