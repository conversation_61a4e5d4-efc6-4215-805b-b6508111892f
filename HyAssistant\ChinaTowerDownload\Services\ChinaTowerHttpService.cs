using ET;
using HyAssistant.ChinaTowerDownload.Configuration;
using HyAssistant.ChinaTowerDownload.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace HyAssistant.ChinaTowerDownload.Services
{
    /// <summary>
    /// 中国铁塔HTTP服务实现类，提供与中国铁塔API进行交互的完整功能。
    /// <para>支持照片下载、站点信息获取、分页查询等功能，具备重试机制、并发控制和SSL安全配置。</para>
    /// <para>实现了 <see cref="IDisposable"/> 接口，支持资源清理和内存管理。</para>
    /// </summary>
    public class ChinaTowerHttpService : IDisposable
    {
        #region 私有字段

        private readonly ChinaTowerConfig _config;
        private readonly SemaphoreSlim _semaphore;
        private bool _disposed = false;

        #endregion 私有字段

        #region 构造函数

        /// <summary>
        /// 初始化 <see cref="ChinaTowerHttpService"/> 类的新实例，配置HTTP客户端和并发控制。
        /// </summary>
        /// <param name="config">中国铁塔API配置信息，不能为空</param>
        /// <param name="maxConcurrency">最大并发请求数，默认为5</param>
        /// <exception cref="ArgumentNullException">当 <paramref name="config"/> 为 <c>null</c> 时抛出</exception>
        public ChinaTowerHttpService(ChinaTowerConfig config, int maxConcurrency = 5)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _semaphore = new SemaphoreSlim(maxConcurrency);

            // 配置SSL/TLS安全协议
            ConfigureSecurityProtocol();
        }

        #endregion 构造函数

        #region 私有方法

        /// <summary>
        /// 配置SSL/TLS安全协议，启用对多种TLS版本的支持并设置证书验证策略。
        /// <para>配置支持TLS 1.0、1.1和1.2版本，确保与各种服务器环境的兼容性。</para>
        /// <para><strong>注意：</strong>当前配置会忽略SSL证书验证错误，仅适用于测试环境，生产环境应谨慎使用。</para>
        /// </summary>
        private void ConfigureSecurityProtocol()
        {
            try
            {
                // 配置SSL/TLS安全协议，支持多种TLS版本
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;

                // 忽略SSL证书验证错误（仅用于测试环境，生产环境应谨慎使用）
                ServicePointManager.ServerCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;

                System.Diagnostics.Debug.WriteLine($"SSL/TLS协议配置完成: {ServicePointManager.SecurityProtocol}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"配置SSL/TLS协议失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 创建并配置 <see cref="HttpWebRequest"/> 实例，设置所有必要的请求头和安全参数。
        /// <para>配置包括：用户代理、认证信息、Cookie、压缩支持、超时设置等。</para>
        /// <para>自动处理重定向、保持连接、代理设置等底层网络配置。</para>
        /// </summary>
        /// <param name="url">请求的完整URL地址</param>
        /// <param name="method">HTTP请求方法，支持GET和POST</param>
        /// <param name="referer">引用页面的URL，用于设置Referer头</param>
        /// <returns>完全配置好的 <see cref="HttpWebRequest"/> 实例</returns>
        private HttpWebRequest CreateAndConfigureRequest(string url, string method, string referer = null)
        {
            var request = (HttpWebRequest)WebRequest.Create(url);

            // 基本配置
            request.Method = method;
            request.AllowAutoRedirect = true;
            request.MaximumAutomaticRedirections = 3;
            request.KeepAlive = true;
            request.Timeout = 30000; // 30 seconds
            request.ReadWriteTimeout = 30000; // 30 seconds
            request.ContinueTimeout = 30000; // 30 seconds

            // 设置请求头（使用正确的属性方式）
            request.UserAgent = _config.UserAgent;
            request.Accept = "application/json, text/plain, */*";
            request.Headers["Accept-Language"] = "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,es;q=0.5,ja;q=0.4";
            request.Headers["Accept-Encoding"] = _config.AcceptEncoding;

            // 添加认证相关头信息
            if (!string.IsNullOrEmpty(_config.Authorization))
            {
                request.Headers["Authorization"] = _config.Authorization;
                // 添加token头，与Authorization值相同
                request.Headers["Token"] = _config.Authorization;
                // 添加rolestr头
                request.Headers["rolestr"] = "3";
            }

            // 添加Cookie
            if (!string.IsNullOrEmpty(_config.Cookie))
            {
                request.Headers["Cookie"] = _config.Cookie;
            }

            // 添加其他头信息
            if (!string.IsNullOrEmpty(referer))
            {
                request.Referer = referer;
            }
            if (!string.IsNullOrEmpty(_config.Origin))
            {
                request.Headers["Origin"] = _config.Origin;
            }
            if (!string.IsNullOrEmpty(_config.Host))
            {
                request.Host = _config.Host;
            }

            // Content-Type设置
            if (method.Equals("POST", StringComparison.OrdinalIgnoreCase))
            {
                request.ContentType = "application/json";
            }

            // 压缩支持
            request.AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate;

            // 代理设置（如果需要）
            request.Proxy = WebRequest.DefaultWebProxy;
            if (request.Proxy != null)
            {
                request.Proxy.Credentials = CredentialCache.DefaultCredentials;
            }

            // 连接优化
            request.ServicePoint.Expect100Continue = false;
            request.ServicePoint.UseNagleAlgorithm = false;
            request.ServicePoint.SetTcpKeepAlive(true, 30000, 1000);

            return request;
        }

        /// <summary>
        /// 异步执行HTTP GET请求，使用 <see cref="HttpWebRequest"/> 获取指定URL的内容。
        /// <para>采用同步包装异步的方式执行，保持与老版本代码的兼容性。</para>
        /// <para>自动处理响应流读取和异常捕获，返回原始响应内容字符串。</para>
        /// </summary>
        /// <param name="url">请求的完整URL地址</param>
        /// <param name="referer">引用页面的URL，用于设置Referer头</param>
        /// <returns>服务器返回的响应内容字符串</returns>
        private async Task<string> GetAsync(string url, string referer = null)
        {
            return await Task.Run(() =>
            {
                try
                {
                    // 创建并配置请求
                    var request = CreateAndConfigureRequest(url, "GET", referer);

                    // 发送请求并获取响应（模仿老版本代码）
                    var response = (HttpWebResponse)request.GetResponse();

                    // 读取响应内容
                    using (var reader = new StreamReader(response.GetResponseStream()))
                    {
                        var responseContent = reader.ReadToEnd();
                        return responseContent;
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"GET请求失败: {ex.Message}");
                    throw;
                }
            });
        }

        /// <summary>
        /// 异步执行HTTP POST请求，使用 <see cref="HttpWebRequest"/> 发送JSON数据到指定URL。
        /// <para>自动将JSON内容转换为UTF-8编码的字节数组并写入请求体。</para>
        /// <para>处理请求配置、内容发送和响应读取的完整流程。</para>
        /// </summary>
        /// <param name="url">请求的完整URL地址</param>
        /// <param name="jsonContent">要发送的JSON格式请求内容</param>
        /// <param name="referer">引用页面的URL，用于设置Referer头</param>
        /// <returns>服务器返回的响应内容字符串，失败时返回 <c>null</c></returns>
        private async Task<string> PostAsync(string url, string jsonContent, string referer = null)
        {
            return await Task.Run(() =>
            {
                try
                {
                    // 创建并配置请求
                    var request = CreateAndConfigureRequest(url, "POST", referer);

                    // 设置Content-Type（模仿老版本代码）
                    request.ContentType = "application/json";

                    // 如果有JSON内容，写入请求体（模仿老版本代码）
                    if (!string.IsNullOrEmpty(jsonContent))
                    {
                        // 将JSON转换为字节数组
                        var byteData = Encoding.UTF8.GetBytes(jsonContent);
                        // 设置ContentLength
                        request.ContentLength = byteData.Length;

                        // 将字节数组写入request.GetRequestStream()
                        using (var postStream = request.GetRequestStream())
                        {
                            postStream.Write(byteData, 0, byteData.Length);
                        }
                    }

                    // 发送请求并获取响应（模仿老版本代码）
                    var response = (HttpWebResponse)request.GetResponse();

                    // 读取响应内容
                    using (var reader = new StreamReader(response.GetResponseStream()))
                    {
                        var responseContent = reader.ReadToEnd();
                        return responseContent;
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"POST请求失败: {ex.Message}");
                    return null;
                    //throw;
                }
            });
        }

        /// <summary>
        /// 使用指数退避策略执行指定操作的重试机制。
        /// <para>在操作失败时自动重试，每次重试间隔呈指数增长，直到达到最大重试次数。</para>
        /// <para>适用于网络请求、API调用等可能因临时故障失败的操作。</para>
        /// </summary>
        /// <typeparam name="T">操作返回的数据类型</typeparam>
        /// <param name="operation">要执行的异步操作函数</param>
        /// <param name="maxRetries">最大重试次数，默认为3次</param>
        /// <param name="delay">初始重试延迟时间，默认为1000毫秒</param>
        /// <returns>操作成功时的返回结果</returns>
        /// <exception cref="Exception">当所有重试尝试都失败时，抛出最后一次的异常</exception>
        private async Task<T> ExecuteWithRetryAsync<T>(
            Func<Task<T>> operation,
            int maxRetries = 3,
            TimeSpan delay = default)
        {
            if (delay == default)
                delay = TimeSpan.FromMilliseconds(_config.Sleep);

            for (int i = 0; i < maxRetries; i++)
            {
                try
                {
                    return await operation();
                }
                catch (Exception ex) when (i < maxRetries - 1)
                {
                    // 记录重试日志
                    ETLogManager.Warning(this, $"操作失败，正在重试 ({i + 1}/{maxRetries}): {ex.Message}");
                    await Task.Delay(delay);
                }
            }

            // 最后一次尝试，不捕获异常
            return await operation();
        }

        #endregion 私有方法

        #region 公共方法

        /// <summary>
        /// 验证Token有效性
        /// </summary>
        public async Task<bool> ValidateTokenAsync()
        {
            try
            {
                var count = await GetStationListCountAsync();
                return count > 0;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取站点列表总数
        /// </summary>
        public async Task<int> GetStationListCountAsync()
        {
            try
            {
                // 构建POST请求的JSON数据（与原代码保持一致）
                var postJson = @"{""resTypeId"":""9224"",""params"":""[]""}";

                var response = await ExecuteWithRetryAsync(async () =>
                {
                    return await PostAsync(_config.ListCount, postJson, _config.ListReferer);
                });

                // 解析响应获取数量（使用正确的字段名 PAGE_COUNT）
                dynamic result = JsonConvert.DeserializeObject(response);
                if (result?.PAGE_COUNT != null)
                {
                    return Convert.ToInt32(result.PAGE_COUNT.Value);
                }

                return -1;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取站点列表总数失败: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// 获取所有站点信息
        /// </summary>
        public async Task<List<StationInfoExcerpt>> GetAllStationsAsync()
        {
            var stations = new List<StationInfoExcerpt>();

            try
            {
                // 构建POST请求的JSON数据（获取所有站点，与原代码保持一致）
                var postJson = @"{""resTypeId"":""9224"",""params"":""[]"",""pageSize"":1,""pageNum"":10000,""select"":true}";

                var response = await ExecuteWithRetryAsync(async () =>
                {
                    return await PostAsync(_config.ListUrl, postJson, _config.ListReferer);
                });

                // 解析JSON响应（直接解析为站点数组，与原代码保持一致）
                dynamic result = JsonConvert.DeserializeObject(response);
                if (result != null)
                {
                    foreach (var station in result)
                    {
                        var stationInfo = new StationInfoExcerpt
                        {
                            STATION_ID = station.STATION_ID?.ToString() ?? "",
                            STATION_NAME = station.STATION_NAME?.ToString() ?? "",
                            STATION_CODE = station.STATION_CODE?.ToString() ?? "",
                            LastCrawlTime = 0
                        };

                        if (!string.IsNullOrEmpty(stationInfo.STATION_ID))
                        {
                            stations.Add(stationInfo);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取所有站点信息失败: {ex.Message}");
            }

            return stations;
        }

        /// <summary>
        /// 获取指定站点的照片信息
        /// </summary>
        public async Task<List<PhotoInfoExcerpt>> GetStationPhotosAsync(StationInfoExcerpt station)
        {
            var photos = new List<PhotoInfoExcerpt>();

            if (station == null || string.IsNullOrEmpty(station.STATION_ID))
                return photos;

            try
            {
                // 构建请求URL，替换参数
                var photoUrl = _config.PhotoUrl
                    .Replace("{stationCode}", station.STATION_CODE)
                    .Replace("{stationId}", station.STATION_ID)
                    .Replace("{stationName}", HttpUtility.UrlEncode(station.STATION_NAME));

                var photoReferer = _config.PhotoReferer
                    .Replace("{stationCode}", station.STATION_CODE)
                    .Replace("{stationId}", station.STATION_ID)
                    .Replace("{stationName}", HttpUtility.UrlEncode(station.STATION_NAME));

                var response = await ExecuteWithRetryAsync(async () =>
                {
                    return await GetAsync(photoUrl, photoReferer);
                });

                // 解析JSON响应
                photos = ParsePhotoResponse(response, station.STATION_ID, station.STATION_NAME);
            }
            catch
            {
                // 记录错误但不抛出异常
            }

            return photos;
        }

        /// <summary>
        /// 解析照片响应JSON
        /// </summary>
        private List<PhotoInfoExcerpt> ParsePhotoResponse(string jsonResponse, string stationId, string stationName)
        {
            var photos = new List<PhotoInfoExcerpt>();

            try
            {
                dynamic result = JsonConvert.DeserializeObject(jsonResponse);

                // 处理data.list中的照片
                if (result?.data?.list != null)
                {
                    foreach (var photo in result.data.list)
                    {
                        var photoInfo = CreatePhotoInfo(stationId, stationName, photo);
                        if (photoInfo != null)
                            photos.Add(photoInfo);
                    }
                }

                // 处理data.stationInfoVOList.list中的照片
                if (result?.data?.stationInfoVOList != null)
                {
                    foreach (var stationInfoVo in result.data.stationInfoVOList)
                    {
                        if (stationInfoVo.list != null)
                        {
                            foreach (var photo in stationInfoVo.list)
                            {
                                var photoInfo = CreatePhotoInfo(stationId, stationName, photo);
                                if (photoInfo != null)
                                    photos.Add(photoInfo);
                            }
                        }
                    }
                }
            }
            catch
            {
                // 解析失败，返回空列表
            }

            return photos;
        }

        /// <summary>
        /// 创建照片信息对象
        /// </summary>
        private PhotoInfoExcerpt CreatePhotoInfo(string stationId, string stationName, dynamic photoData)
        {
            try
            {
                var photoId = photoData.photoId?.ToString();
                var filePath = photoData.filePath?.ToString();
                var fileName = photoData.fileName?.ToString();
                var createTimeStr = photoData.createTime?.ToString();

                if (string.IsNullOrEmpty(photoId) || string.IsNullOrEmpty(filePath))
                    return null;

                // 如果photoId为空，使用文件名
                if (string.IsNullOrEmpty(photoId))
                {
                    photoId = Path.GetFileNameWithoutExtension(new Uri(filePath).LocalPath);
                }

                // 解析创建时间
                long createTime = 0;
                DateTime dt = DateTime.MinValue;
                if (!string.IsNullOrEmpty(createTimeStr) && DateTime.TryParse(createTimeStr, out dt))
                {
                    createTime = ((DateTimeOffset)dt).ToUnixTimeSeconds();
                }

                return new PhotoInfoExcerpt
                {
                    station_id = stationId,
                    station_name = stationName,
                    url = filePath,
                    photoId = photoId,
                    serverfileName = string.IsNullOrEmpty(fileName) ? photoId : fileName,
                    createTime = createTime,
                    downloadTime = 0
                };
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 下载单个照片文件
        /// </summary>
        public async Task<bool> DownloadPhotoAsync(PhotoInfoExcerpt photo, string savePath)
        {
            if (photo == null || string.IsNullOrEmpty(photo.url) || string.IsNullOrEmpty(savePath))
                return false;

            try
            {
                // 检查文件是否已存在
                if (File.Exists(savePath))
                    return true;

                // 确保目录存在
                var directory = Path.GetDirectoryName(savePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 下载文件（使用HttpWebRequest）
                return await Task.Run(() =>
                {
                    try
                    {
                        // 创建HttpWebRequest对象
                        var request = (HttpWebRequest)WebRequest.Create(photo.url);
                        request.Method = "GET";

                        // 设置请求头
                        request.UserAgent = _config.UserAgent;
                        if (!string.IsNullOrEmpty(_config.Authorization))
                        {
                            request.Headers["Authorization"] = _config.Authorization;
                        }
                        if (!string.IsNullOrEmpty(_config.Cookie))
                        {
                            request.Headers["Cookie"] = _config.Cookie;
                        }
                        if (!string.IsNullOrEmpty(_config.Token))
                        {
                            request.Headers["TOKEN"] = _config.Token;
                        }

                        // 获取响应
                        var response = (HttpWebResponse)request.GetResponse();

                        // 下载文件内容
                        using (var responseStream = response.GetResponseStream())
                        using (var fileStream = new FileStream(savePath, FileMode.Create, FileAccess.Write))
                        {
                            responseStream.CopyTo(fileStream);
                        }

                        return true;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"下载文件失败: {ex.Message}");
                        throw;
                    }
                });
            }
            catch
            {
                // 删除可能创建的不完整文件
                try
                {
                    if (File.Exists(savePath))
                        File.Delete(savePath);
                }
                catch { }

                return false;
            }
        }

        /// <summary>
        /// 批量下载照片文件
        /// </summary>
        public async Task<DownloadResult> BatchDownloadPhotosAsync(
            List<PhotoInfoExcerpt> photos,
            string baseSavePath,
            int maxConcurrency = 8,
            IProgress<DownloadProgress> progressCallback = null)
        {
            var result = new DownloadResult
            {
                TotalPhotos = photos?.Count ?? 0
            };

            if (photos == null || photos.Count == 0)
                return result;

            var stopwatch = Stopwatch.StartNew();
            var semaphore = new SemaphoreSlim(maxConcurrency);
            var tasks = new List<Task>();

            foreach (var photo in photos)
            {
                tasks.Add(DownloadSinglePhotoAsync(photo, baseSavePath, semaphore, result, progressCallback));
            }

            await Task.WhenAll(tasks);
            stopwatch.Stop();

            result.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
            return result;
        }

        /// <summary>
        /// 下载单个照片的异步任务
        /// </summary>
        private async Task DownloadSinglePhotoAsync(
            PhotoInfoExcerpt photo,
            string baseSavePath,
            SemaphoreSlim semaphore,
            DownloadResult result,
            IProgress<DownloadProgress> progressCallback)
        {
            await semaphore.WaitAsync();

            try
            {
                // 构建保存路径
                var stationDir = Path.Combine(baseSavePath, "站点照片", photo.station_name);
                var fileName = photo.GenerateLocalFileName();
                var savePath = Path.Combine(stationDir, fileName);

                // 检查文件是否已存在
                if (File.Exists(savePath))
                {
                    Interlocked.Increment(ref result.SkippedPhotos);
                    return;
                }

                // 下载照片
                var success = await DownloadPhotoAsync(photo, savePath);

                if (success)
                {
                    Interlocked.Increment(ref result.SuccessfulPhotos);
                }
                else
                {
                    lock (result.FailedPhotosList)
                    {
                        result.FailedPhotosList.Add(photo);
                    }
                }

                // 报告进度
                progressCallback?.Report(new DownloadProgress
                {
                    TotalPhotos = result.TotalPhotos,
                    CompletedPhotos = result.SuccessfulPhotos + result.FailedPhotos + result.SkippedPhotos,
                    SuccessfulPhotos = result.SuccessfulPhotos,
                    FailedPhotos = result.FailedPhotos,
                    SkippedPhotos = result.SkippedPhotos,
                    CurrentPhoto = photo
                });
            }
            finally
            {
                semaphore.Release();
            }
        }

        /// <summary>
        /// 设置认证信息
        /// </summary>
        public void SetAuthentication(string authorization, string cookie)
        {
            _config.Authorization = authorization;
            _config.Cookie = cookie;
            _config.Token = authorization;

            // 不再重新配置HTTP客户端，认证信息将在每个请求中单独添加
        }

        /// <summary>
        /// 清除认证信息
        /// </summary>
        public void ClearAuthentication()
        {
            _config.ClearAuthentication();
            // 不再重新配置HTTP客户端，认证信息将在每个请求中单独添加
        }

        #endregion 公共方法

        #region IDisposable

        public void Dispose()
        {
            if (!_disposed)
            {
                _semaphore?.Dispose();
                _disposed = true;
            }
        }

        #endregion IDisposable
    }
}