/**
 * 通用方法封装
 */
(function ($) {
    // 当前table相关信息
    let table = {
        config: {},
        // 当前实例配置
        options: {},
        /**
         * 获取tableId
         * @param tableId
         */
        getTableId: function (tableId) {
            let currentId = $.common.isEmpty(tableId) ? table.options.id : tableId
            return currentId
        },
        // 设置实例配置
        set: function (id) {
            if ($.common.getLength(table.config) > 1) {
                let tableId = $.common.isEmpty(id) ? $(event.currentTarget).parents(".bootstrap-table").find(".table").attr("id") : id;
                if ($.common.isNotEmpty(tableId)) {
                    table.options = table.get(tableId)
                }
            }
        },
        // 获取实例配置
        get: function (id) {
            return table.config[id];
        },
        // 记住选择实例组
        rememberSelecteds: {},
        // 记住选择ID组
        rememberSelectedIds: {}
    }
    /**
     * 列表监听
     * @type {string}
     */
    let TABLE_EVENTS = 'all.bs.table click-cell.bs.table dbl-click-cell.bs.table click-row.bs.table dbl-click-row.bs.table sort.bs.table check.bs.table uncheck.bs.table onUncheck check-all.bs.table uncheck-all.bs.table check-some.bs.table uncheck-some.bs.table load-success.bs.table load-error.bs.table column-switch.bs.table page-change.bs.table search.bs.table toggle.bs.table show-search.bs.table expand-row.bs.table collapse-row.bs.table refresh-options.bs.table reset-view.bs.table refresh.bs.table'

    /**
     * 表格类型
     * */
    let table_type = {
        bootstrapTable: 0,
        bootstrapTreeTable: 1
    }

    /**
     * 消息状态码
     * */
    let web_status = {
        SUCCESS: 0,
        FAIL: 500,
        WARNING: 301
    }

    /**
     * 弹窗状态码
     * */
    let modal_status = {
        SUCCESS: "success",
        FAIL: "error",
        WARNING: "warning"
    }
    $.extend({
        common: {
            /**
             * 获取context
             * @returns {any | {configPath: null, loadConfigXml: (function(*=): Promise<unknown>), xmlsByUrl: {}, findActions: (function(*): []), setSetting: setSetting, useParentMeta: boolean, useParentMetaConfig: boolean, fixUrl: fixUrl, fullDataUrl: (function(*, *=, *=): string), events: {once: (function(): context.events), one: (function(): context.events), trigger: trigger, off: (function(): context.events), on: (function(): context.events)}, metas: {}, settings: {}, clientConf: {}, loadConfigXmlPlain: (function(*=, *=): Promise<unknown>), getText: (function(*=, *=): Promise<unknown>), version: string, access_token, loadTheme: loadTheme, eventbus, getSetting: (function(*=): *), hasPermission: hasPermission, debugMode, actionDefs: {}, rootContext: null, actionHandler: null, hasMetaDefined: (function(*): boolean|*), objMetaDefault: null, minimizeAllMdiWindow: minimizeAllMdiWindow, configLoaded: boolean, log: log, objQueryFixFn: null, getUser: getUser, getAddUrl: (function(*): string), mapReady: mapReady, openMain: openMain, objMetaDefaultFns: [], configReady: configReady, dicts: null, findAction: (function(*=): *), dataRootUrl: string, lang: string, objMetaDefaultFn: null, closeLastActionModal: closeLastActionModal, lastActionData: null, _currentUser: null, objMetaXmls: [], urlParams, getXml: (function(*=, *=): Promise<unknown>), shouldEncodeParams: undefined, message: message, parseActionFile: parseActionFile, findConfig: (function(*=, *=): *), parseActionNode: parseActionNode, parentContext: null, configXml: null, setup: (function(): Promise<unknown>), progress: progress, window: null, doAction: doAction, setConfigXml: (function(*=): Promise<unknown>), getUrlParam: (function(*): *)}}
             */
            getContext: () => {
                let context = _context.rootContext
                if (!context) {
                    context = _context
                }
                return context
            },
            /**
             * 获取sui
             * @returns {_sui}
             */
            getSui: () => {
                let sui = null
                try {
                    sui = top._sui
                } catch (e) {
                    console.error(e)
                }
                if (!sui) {
                    sui = _sui
                }
                return sui
            },
            /**
             * 获取城市标记
             * @param isRoot 是否取根
             * @returns {string}
             */
            getCityMark: (isRoot = true) => {
                let cityMark = ''
                try {
                    let context = null
                    try {
                        context = top._context
                    } catch (e) {
                        console.error(e)
                    }
                    if (!context) {
                        context = _context
                    }
                    if (!isRoot) {
                        context = _context
                    }
                    cityMark = $.common.toString(context.clientConf['cityMark'])
                } catch (e) {
                    console.error(e)
                }
                return cityMark
            },
            /**
             * 是否广东域
             * @param isRoot 是否取根
             * @returns {boolean}
             */
            isGdy: (isRoot = true) => {
                return $.common.getCityMark(isRoot) == 'gdy'
            },
            /**
             * 获取用户本地id
             */
            getShardingId: () => {
                const user = $.common.getContext()._currentUser
                if (user != null) {
                    return $.common.toNumber(user.SHARDING_ID)
                }
                return 0
            },
            /**
             * 判断用户角色权限
             */
            hasRole: (name='') => {
                let roleName = []
                roleName.push(name)
                //用户拥有的角色
                roles = $.common.getContext()._currentUser.roles
                let permission = false;
                if (roles) {
                    for (let role of roles) {
                        if (roleName.indexOf(role.NAME) != -1) {
                            permission = true
                            break;
                        }
                    }
                }
                return permission
            },
            /**
             * 判断当前用户是否与本地id相同
             */
            isShardingId: (shardingId) => {
                const id = $.common.getShardingId()
                return id === $.common.toNumber(shardingId)
            },
            /**
             * 判断当前登录用户是否为深圳
             */
            isSz: () => {
                return $.common.isShardingId(755)
            },
            /**
             * 转字符串
             * @param val
             * @returns {string}
             */
            toString: (val) => {
                if ($.common.isEmpty(val)) {
                    return "";
                }
                return val.toString();
            },
            /**
             * 判断否为空
             * @param value
             * @returns {boolean}
             */
            isEmpty: (value) => {
                if (value == null || $.common.trim(value) == '' || $.common.trim(value) == 'null' || $.common.trim(value) == 'undefined') {
                    return true;
                }
                return false;
            },
            /**
             * 空格截取
             * @param value
             * @returns {string}
             */
            trim: (value) => {
                if (value == null) {
                    return "";
                }
                return value.toString().replace(/(^\s*)|(\s*$)|\r|\n/g, "");
            },
            /**
             * 判断是否为非空串
             * @param value
             * @returns {boolean}
             */
            isNotEmpty: (value) => {
                return !$.common.isEmpty(value);
            },
            /**
             * 获取数组某个对象属性逗号分隔的字符串
             * @param datas
             * @param field
             * @returns {string}
             */
            getValueStrs: (datas = [], field = '') => {
                let value = ''
                datas.forEach(data => {
                    if (value.length > 0) {
                        value += ','
                    }
                    value += $.common.toString(data[field])
                })
                return value
            },
            /**
             * 转数字
             * @param val
             * @param defVal
             */
            toNumber: (val, defVal = -1) => {
                try {
                    const n = Number(val)
                    if (isNaN(n)) {
                        return defVal
                    }
                    return n
                } catch (e) {
                    return defVal
                }
            },
            /**
             * 获取当前tab页index
             * @returns {any}
             */
            getTabActive: () => {
                return $.common.getContext().window['getWorkspace']().getActiveTabIndex()
            },
            /**
             * 根据tabActive激活tab页
             * @param tabActive
             */
            activeTab: (tabActive) => {
                $.common.getContext().window['getWorkspace']().activeTab(tabActive)
            },
            /**
             * 是否为Promise对象
             * @param o
             * @returns {*|boolean}
             */
            isPromise: (o) => {
                return o && typeof o.then === 'function'
            },
            /**
             * 是否为方法
             * @param o
             * @returns {*|boolean}
             */
            isFunction: (o) => {
                return o && typeof o === 'function'
            },
            /**
             * 把集合对象包含，属性_value还原到对象中
             * @param list
             */
            resetMetaObjects: (list) => {
                if (!list) {
                    return
                }
                list.forEach(o => {
                    $.common.resetMetaObject(o)
                })
            },
            /**
             * 把对象包含，属性_value还原到对象中
             * @param o
             */
            resetMetaObject: (o) => {
                if (!o) {
                    return
                }
                // 遍历属性
                for (let key in o) {
                    // _value结束的
                    if (key.endsWith('_value')) {
                        o[key.substring(0, key.indexOf('_value'))] = o[key]
                    }
                }
            },
            /**
             * 获取obj对象长度
             * @param obj
             * @returns {number}
             */
            getLength: function (obj) {
                let count = 0;
                for (let i in obj) {
                    if (obj.hasOwnProperty(i)) {
                        count++
                    }
                }
                return count
            },
            /**
             * 判断移动端
             * @returns {RegExpMatchArray}
             */
            isMobile: function () {
                return navigator.userAgent.match(/(Android|iPhone|SymbianOS|Windows Phone|iPad|iPod)/i);
            },
            /**
             * 数组去重
             * @param array
             * @returns {[]}
             */
            uniqueFn: function (array) {
                var result = [];
                var hashObj = {};
                for (var i = 0; i < array.length; i++) {
                    if (!hashObj[array[i]]) {
                        hashObj[array[i]] = true;
                        result.push(array[i]);
                    }
                }
                return result;
            },
        },
        uiOperation: {
            /**
             * 广东域是否显示
             * @param elementId
             */
            isGdyShow: (elementId) => {
                $.uiOperation.isShow('gdy', elementId)
            },
            /**
             * 是否显示组件
             * @param cityMark
             * @param elementId
             */
            isShow: (cityMark, elementId) => {
                let elementIds = []
                if (elementId instanceof Array) {
                    elementIds = elementId
                } else {
                    elementIds.push(elementId)
                }
                elementIds.forEach(elementId => {
                    // 显示
                    if (cityMark === $.common.getCityMark()) {
                        $('#' + elementId).show()
                    } else {// 隐藏
                        $('#' + elementId).hide()
                    }
                })
            },
            /**
             * 显示地址树
             * 传入参数addressId可自动定位
             */
            showAddressTree: (params = {}, selectCallback) => {
                Object.assign(params, {
                    // 返回地址
                    selectCallback: selectCallback
                })
                _context.doAction({
                    contentUrl: 'apps/pipe/address/standardAddressTree.html',
                    type: 'window',
                    style: 'height:500px;min-width: 320px;'
                }, params)
            },
            /**
             * 显示通用数据列表
             * @param datas 数据集合
             * @param headers 列头集合
             * @param fields    显示字段集合
             * @param okFun     确定方法
             * @param cancelFun 取消方法
             * @param options   其他参数
             */
            showDataTable: (datas = [], headers = [], fields = [], okFun, cancelFun, options = {}) => {
                Object.assign(options, {
                    datas: datas,
                    headers: headers,
                    fields: fields,
                    okFun: okFun,
                    cancelFun: cancelFun
                })
                $.common.getContext().doAction({
                    title: options.title ? options.title : '',
                    contentUrl: 'apps/gdo3/common/common-data-table.html',
                    type: 'window',
                    style: 'height:500px;'
                }, options)
            },
            /**
             * 绑定下拉框，返回下拉框对象
             * @param el dom
             * @param data 数据/查询回调
             * @param titleField 标题显示字段
             * @param valueField 值字段
             * @param multiple 是否多选
             * @param options 额外参数
             * @returns {_sui.Dropdown}
             */
            bindDropdown: (el, data, value = null, titleField = 'name', valueField = 'id', multiple = false, options = {}) => {
                Object.assign(options, {
                    titleField: titleField,
                    valueField: valueField,
                    multiple: multiple
                })
                const dropdown = new _sui.Dropdown(el, options)
                // 如果是数据直接回调，如果是异步方法异步回调
                _util.dataCallback((res) => {
                    // 设置数据
                    dropdown.setData(res)
                    // 设置选中值
                    dropdown.setValue(value)
                    // 渲染
                    dropdown.render()
                }, data)
                return dropdown
            },
            /**
             * 根据画布ID打印画布
             * @param canvasId
             */
            printCanvas: (canvasId) => {
                let win = window.open()
                win.document.write("<br><img src='" + $('#' + canvasId)[0].toDataURL() + "'/>")
                setTimeout(() => {
                    win.print()
                }, 500)
            },
            /**
             * 是否显示按钮加载
             * @param el    el对象
             * @param loading   是否加载
             */
            showButtonLoading: (el, loading = true) => {
                if (loading) {
                    $(el).addClass('loading')
                    $(el).addClass('disabled')
                } else {
                    $(el).removeClass('loading')
                    $(el).removeClass('disabled')
                }
            },

            /**
             * 绑定树列表
             * 需要jq,jquery.treetable.css,jquery.treetable.theme.my.css,jquery.treetable.js
             * 不显示子集合，设置数据属性notShowChildren,
             * 不显示复选框，设置数据属性notShowCheckBox
             * @param el el对象
             * @param columns 列数组，[{label:列头名称,field:显示字段key}]
             * @param initData 初始化数据（rowColor：行颜色）
             * @param loadingDataFun 数据加载方法，function或数组
             * @param showCheckBox 显示复选框
             * @param options 其他参数{dataRenderer(数据呈现方法)}
             */
            bindTreeTable: (el, columns = [], initDatas = [], loadingDataFun, options = {}) => {
                el.empty()
                // 所有数据集合
                let allDatas = []
                // 选择数据集合
                let selectDatas = []
                // 列表参数
                const method = {
                    nodeIdAttr: "nodeId",
                    parentIdAttr: "parentId",
                    expandable: true,
                    // 点击节点名称也打开子节点,需要增加复选框所以禁用
                    clickableNodeNames: false,
                    // 每个分支缩进的像素数。
                    indent: 15
                }
                // 复制覆盖参数
                Object.assign(options, method)
                // table el对象
                const tableEl = $('<table>')
                // 把列表 el对象添加进去
                el.append(tableEl)
                // 列头 el
                const theadEl = $('<thead>')
                // 把 列头 el添加进去
                tableEl.append(theadEl)
                // tbody el对象
                const tbodyEl = $('<tbody>')
                // 把 tbody el对象添加进去
                tableEl.append(tbodyEl)
                // 初始化列
                initColumns()

                /**
                 * 初始化列
                 */
                function initColumns() {
                    if (!columns) {
                        return
                    }
                    const trEl = $('<tr>')
                    theadEl.append(trEl)
                    columns.forEach(column => {
                        const thEl = $('<th>' + column.label + '</th>')
                        trEl.append(thEl)
                    })
                    theadEl.append(trEl)
                }

                /**
                 * 返回节点id字段
                 */
                function getNodeIdFeild() {
                    return method.nodeIdAttr
                }

                /**
                 * 返回父节点id字段
                 */
                function getParentNodeIdFeild() {
                    return method.parentIdAttr
                }

                /**
                 * 创建行
                 * @param data 数据
                 * @param index
                 * @returns {*|jQuery|HTMLElement}
                 */
                function createRow(data, index) {
                    // id
                    let id = $.common.toString(data[getNodeIdFeild()])
                    if (!id) {
                        id = data['id']
                    }
                    // 父id
                    const parentId = $.common.toString(data[getParentNodeIdFeild()])
                    // 是否展示,展开
                    let datattbranch = true
                    // 不显示子集合
                    if (data.notShowChildren) {
                        datattbranch = false
                    }
                    // 样式
                    const dataMetaclass = $.common.toString(data.dataMetaclass)
                    // 是否选中
                    let dataChecked = data.dataChecked
                    const trEl = $('<tr data-node-id="' + id + '" id="' + id + '" data-tt-branch="' + datattbranch + '" >')
                    // 设置行颜色
                    if (data.rowColor) {
                        trEl.css("background-color", data.rowColor)
                    }
                    // 设置父id
                    if (parentId) {
                        trEl.attr('data-parent-id', parentId)
                    }
                    // 设置数据样式
                    if (dataMetaclass) {
                        trEl.attr('data-metaclass', dataMetaclass)
                    }
                    // 设置是否选中
                    if (dataChecked != null) {
                        trEl.attr('data-checked', dataChecked)
                    }
                    if (data.children && data.children.length > 0) {
                        trEl.attr('data-children', true)
                    }
                    // 遍历列，拼接td
                    columns.forEach(column => {
                        const tdEl = $('<td>' + getDataText(data, column, index) + '</td>>')
                        // 复选框
                        const checkboxEl = tdEl.find("input[type='checkbox']")
                        // 存在复选框情况
                        if (checkboxEl && checkboxEl.length > 0) {
                            // 把复选框赋值给数据对象
                            data.checkboxEl = checkboxEl
                            // 居中
                            tdEl.css('text-align', 'center')
                            // 设置选中
                            if (dataChecked && dataChecked == true) {
                                checkboxEl.prop('checked', true)
                                selectDatas.push(data)
                            }
                            // 增加复选选择事件
                            checkboxEl.change(e => {
                                const index = selectDatas.indexOf(data)
                                // 选中添加
                                if (checkboxEl.is(":checked")) {
                                    selectDatas.push(data)
                                } else {
                                    selectDatas.splice(index, 1)
                                }
                            })
                        }
                        trEl.append(tdEl)
                    })
                    return trEl;
                }

                /**
                 * 获取数据文本列
                 * @param data
                 * @param column
                 * @param index
                 */
                function getDataText(data, column, index) {
                    // 数据呈现方法
                    const dataRenderer = method.dataRenderer
                    if (dataRenderer) {
                        return dataRenderer(data, column, index)
                    }
                    const notShowCheckBox = data.notShowCheckBox
                    // 复选
                    if (column.checkBox && !notShowCheckBox) {
                        return '<input type="checkbox">' + $.common.toString(data[column.field])
                    }
                    return $.common.toString(data[column.field])
                }

                /**
                 * 拼接子数据
                 * @param data
                 * @param els
                 */
                function appendChildren(data, els = []) {
                    if (!data || !data.children) {
                        return
                    }
                    // 父id
                    let parentId = data[getNodeIdFeild()]
                    if (!parentId) {
                        parentId = data.id
                    }
                    data.children.forEach((d, index) => {
                        // 把数据加到全部数据集合里
                        allDatas.push(d)
                        // 设置父id
                        if (parentId) {
                            d[getParentNodeIdFeild()] = parentId
                        }
                        const trEl = createRow(d, index)
                        els.push(trEl)
                        appendChildren(d, els)
                    })
                }

                /**
                 * 查找对象
                 * @param val
                 * @returns {{}}
                 */
                function findData(val) {
                    for (let i = 0; i < allDatas.length; i++) {
                        const data = allDatas[i]
                        if ($.common.toString(data[getNodeIdFeild()]) == $.common.toString(val)) {
                            return data
                        }
                        if ($.common.toString(data.id) == $.common.toString(val)) {
                            return data
                        }
                    }
                    return {}
                }

                function setDatas(node, datas, parentId) {
                    if (!datas) {
                        return
                    }
                    datas.forEach((data, index) => {
                        // 添加到全部集合中
                        allDatas.push(data)
                        if (parentId) {
                            data[getParentNodeIdFeild()] = parentId
                        }
                        // 行
                        const trEl = createRow(data, index)
                        if (!node) {
                            tbodyEl.append(trEl)
                            // 存在子集合情况
                            if (data.children && data.children.length > 0) {
                                const childrenEls = []
                                appendChildren(data, childrenEls)
                                childrenEls.forEach(childrenEl => {
                                    tbodyEl.append(childrenEl)
                                })
                            }
                        } else {
                            // 添加行
                            tableEl.treetable("loadBranch", node, trEl)
                        }
                    })
                }

                // 初始化数据
                if (initDatas) {
                    setDatas(null, initDatas)
                }


                // 展开
                method.onNodeExpand = function () {
                    // 当前节点
                    const node = this
                    let attrData = $(this.row).data();
                    // 是否已经展开过，不再重复展开
                    if (attrData.nodeExpand && attrData.nodeExpand === true) {
                        return
                    }
                    // 存在子集合不用查询
                    if (attrData.children && attrData.children === true) {
                        return
                    }
                    // 设置已展开
                    attrData.nodeExpand = true
                    const id = attrData[getNodeIdFeild()]
                    if ($.common.isFunction(loadingDataFun)) {
                        const loadingData = loadingDataFun(findData(id))
                        if ($.common.isPromise(loadingData)) {
                            loadingData.then(datas => {
                                setDatas(node, datas, id)
                            })
                        } else {
                            setDatas(node, datas, id)
                        }
                    } else {
                        setDatas(node, loadingDataFun, id)
                    }
                }
                // 收缩
                method.onNodeCollapse = function () {

                }
                // 绑定树列表
                const treeTable = tableEl.treetable(method)

                return {
                    treeTable: treeTable,
                    /**
                     * 获取table 的el
                     * @returns {*|jQuery|HTMLElement}
                     */
                    getEl: () => {
                        return tableEl
                    },
                    /**
                     * 查找数据
                     * @param id
                     * @returns {{}}
                     */
                    findData: (id) => {
                        return findData(id)
                    },
                    /**
                     * 查找节点
                     * @param id
                     */
                    findNode: (id) => {
                        return tableEl.treetable('node', id)
                    },
                    /**
                     * 展开节点
                     * @param id
                     */
                    expandNode: (id) => {
                        tableEl.treetable('expandNode', id)
                    },
                    /**
                     * 收缩节点
                     * @param id
                     */
                    collapseNode: (id) => {
                        tableEl.treetable('collapseNode', id)
                    },
                    /**
                     * 返回选择数据
                     * @returns {[]}
                     */
                    getSelectDatas: () => {
                        return selectDatas
                    },
                    /**
                     * 清理选择
                     */
                    clearSelect: () => {
                        if (!allDatas) {
                            return
                        }
                        allDatas.forEach(allData => {
                            // 存在复选框
                            if (allData.checkboxEl) {
                                const checkboxEl = allData.checkboxEl
                                // 清空选中
                                checkboxEl.prop('checked', false)
                                const index = selectDatas.indexOf(allData)
                                // 移除选中对象
                                if (index != -1) {
                                    selectDatas.splice(index, 1)
                                }
                            }
                        })
                    }
                }
            },
            /**
             * 设置combo 宽度
             * @param combo
             * @param width
             */
            setComboMinWidth: (combo, width) => {
                if (combo.getEl()) {
                    combo.getEl().css('minWidth', width)
                }
                // 监听渲染完成
                combo.on('EVENT_COMPONENT_RENDERED', () => {
                    // 设置组件宽度
                    combo.getEl().css('minWidth', width)
                })
            }
        },
        requestUtil: {
            /**
             * 导出文件
             * @param expid 与后端IExpService.getId匹配
             * @param type   类型参数
             * @param id    id
             * @param extraParams 额外参数
             * @param isBlob 是否blob方式下载
             * @param el 对象
             */
            exportFile: (expid, type, id, extraParams, isBlob, el) => {
                return new Promise((resolve, reject) => {
                    let url = 'bean/exp.do?_expid=' + expid + '&serviceName=' + expid + '&_type=' + type
                    if (id) {
                        url += '&id=' + id + ''
                    }
                    url = $.common.getContext().fullDataUrl(url);
                    if (isBlob) {
                        $.common.getSui().showLoading(el, '正在进行数据导出，请耐心等待....')
                        _bean.postDownload(url, extraParams).then(res => {
                            $.common.getSui().showLoading(el, false)
                            resolve('blob方式下载')
                        }).catch(err => {
                            $.common.getSui().showLoading(el, false)
                            reject(err)
                        })
                    } else {
                        if (extraParams) {
                            for (let key in extraParams) {
                                url += '&' + key + '=' + extraParams[key]
                            }
                        }
                        window.open(url, '_blank');
                        resolve('get方式下载')
                    }
                })
            },
            /**
             * 导入文件
             * @param impid 与后端IImpService.getId匹配
             * @param type  类型参数
             * @param extraParams   额外参数
             * @param success 成功方法
             */
            importFile: (impid, type, extraParams, success) => {
                let url = $.common.getContext().fullDataUrl('bean/imp.do?_impid=' + impid + '&_type=' + type)
                if (extraParams) {
                    for (let key in extraParams) {
                        url += '&' + key + '=' + extraParams[key]
                    }
                }
                $.common.getSui().showUploadModal(url, {
                    description: "", success: success
                })
            }
        },
        /**
         * 地图操作相关
         */
        mapOperation: {
            /**
             * 校验经纬度是否合法（含负数和小数点），经度：-180~180，纬度：-90~90
             * @param value
             * @param min
             * @param max
             * @returns {boolean}，返回true表示合法，返回false表示非法
             */
            isCorrectLngLat: (value, min, max) => {
                if (!value) {
                    return false
                }
                if (value.indexOf('-') !== -1) {
                    if (value.indexOf('-') !== 0) {
                        return false
                    }
                    let hCount = (value.split('-')).length - 1
                    if (hCount > 1) {
                        return false
                    }
                    if (value.indexOf('.') !== -1) {
                        if (value.indexOf('.') === 1 || value.indexOf('.') > 4) {
                            return false
                        }
                        let dCount = (value.split('.')).length - 1
                        if (dCount > 1) {
                            return false
                        }
                    }
                }
                if (value.indexOf('.') !== -1) {
                    if (value.indexOf('.') === 0 || value.indexOf('.') > 3) {
                        return false
                    }
                    let dCount = (value.split('.')).length - 1
                    if (dCount > 1) {
                        return false
                    }
                }
                if (parseInt(value) < min || parseInt(value) > max) {
                    return false
                }
                return true
            },
            /**
             * 标准地址定位预览
             * @param ids 地址id集合
             */
            locatePosition: (ids = [], options) => {
                _bean.callService('addressService', 'positionLocateByIds', [ids], {_showLoading: true}).then(res => {
                    if (!res || !res.length) {
                        return _sui.alert('没有坐标信息可定位')
                    }
                    // 缩小弹窗
                    if (!options || options.minimizeWindow !== false) _sui.minimizeAllMdiWindow(true)
                    // 清空画图
                    if (_context.rootContext) {
                        // 清空画图
                        _context.rootContext.map.clear()
                        // 地图显示
                        _context.rootContext.map.locateByWkts(res)
                    } else {
                        // 清空画图
                        _context.map.clear()
                        // 地图显示
                        _context.map.locateByWkts(res)
                    }
                })
            },
            /**
             * 定位光路
             * @param opticRouteId
             */
            locateOpticRoute: (opticRouteId) => {
                return new Promise((resolve, reject) => {
                    _bean.action('OPTICALCIRCUIT', 'search', {}, {
                        needshape: 'true',
                        opticid: opticRouteId
                    }).then(function (optic) {
                        optic = optic.data[0]
                        if (optic.locatecss) {
                            $.mapOperation.activeMapWorkspace()
                            // 缩小弹窗
                            _sui.minimizeAllMdiWindow(true)
                            const context = $.common.getContext()
                            if (context) {
                                // 清空画图
                                context.map.clear()
                                // const wkts = optic.locatecss.map(function (o) {
                                //     return o.shape
                                // })
                                // context.map.locateByWkts(wkts)
                                let dataAndConfig = []
                                for (let o of optic.locatecss) {
                                    if ("MULTILINESTRING())" === o.shape) {
                                        continue;
                                    }
                                    // 光路定位颜色显示红色
                                    dataAndConfig.push({shape: o.shape, options: {color: 'red', opacity: 1, weight: 5}})
                                }
                                context.map.locateByWktsAndConfig(dataAndConfig, true, 10, false);
                            }
                        }
                        resolve(optic)
                    }).catch(err => {
                        reject(err)
                    })
                })
            },
            /**
             * 定位一个对象
             * @param objectType 类型
             * @param id id
             * @param isShowObjPopup 是否显示冒泡框
             * @param options 其他参数
             */
            locateOne: (objectType, id, isShowObjPopup = true, options = {}) => {
                _bean.locate(objectType, {
                    id: id,
                    _showLoading: true
                }).then(res => {
                    if (!res || !res.length) {
                        return _sui.alert('没有坐标信息可定位')
                    }
                    // 缩小弹窗
                    _sui.minimizeAllMdiWindow(true)
                    const context = $.common.getContext()
                    // 清空画图
                    if (context) {
                        // 清空画图
                        context.map.clear()
                        // 地图显示高亮
                        context.map.locate({shape: res, highlight: true}, true)
                        //弹出冒泡信息
                        if (isShowObjPopup) {
                            context.map.showObjPopup(context.map.getBounds(res).getCenter(), objectType, id)
                        }
                    }
                })
            },
            /**
             * 切换到地图页
             * @param clear 是否清理画图
             */
            activeMapWorkspace: (clear = false) => {
                // 切换地图页
                $.common.getContext().window['activeMapWorkspace']()
                // 清理画图
                if (clear) {
                    $.common.getContext().map.clear()
                }
            },
            /**
             * 激活画图选择，返回wkt
             * @param mode
             * @returns {Promise<unknown>}
             */
            activeDrawSelect: (mode) => {
                return new Promise((resolve, reject) => {
                    $.common.getContext().map.activeDrawSelect(mode)
                    // 移除默认选择完成监听
                    $.common.getContext().map.off('draw:created')
                    // 重新添加选择完成监听
                    $.common.getContext().map.on('draw:created', (e) => {
                        resolve($.common.getContext().map.getWkt(e.layer))
                        // 清理画图
                        $.common.getContext().map.clear()
                    });
                })
            }
        },
        /**
         * 图纸操作
         */
        diagramOperation: {
            /**
             * 获取图纸类别集合，复制diagram_create.js代码，做手工同步
             * 参考DiagramDataBuilderFactory.java
             * @returns {any[]}
             */
            getDiagramCategoryList: () => {
                const rc = []
                rc.push({"name": "光缆逻辑图", "value": 1, "entitytype": ["OCABLE"]});
                rc.push({"name": "电缆逻辑图", "value": 2, "entitytype": ["ECABLE"]});
                rc.push({"name": "光缆路由图", "value": 3, "entitytype": ["OCABLE"]});
                rc.push({"name": "电缆路由图", "value": 4, "entitytype": ["ECABLE"]});
                rc.push({"name": "管道路由图", "value": 5, "entitytype": ["WELL"]});
                rc.push({"name": "杆路路由图", "value": 6, "entitytype": ["POLE"]});
                rc.push({"name": "光路路由图", "value": 7});
                rc.push({"name": "光缆分纤图", "value": 8, "entitytype": ["OCABLE"]});
                rc.push({"name": "卡片图", "value": 9});
                rc.push({"name": "光缆管道杆路图", "value": 10});
                rc.push({"name": "电缆管道杆路图", "value": 11});
                rc.push({"name": "光缆光纤分布图", "value": 12});
                rc.push({"name": "局站分布图", "value": 13});
                rc.push({"name": "机房分布图", "value": 14});
                rc.push({"name": "交接箱分布图", "value": 15});
                rc.push({"name": "光交接箱分布图", "value": 16});
                rc.push({"name": "综合箱分布图", "value": 17});
                rc.push({"name": "PON拓扑图", "value": 18, "entitytype": ["OBD", "OLT", "ONU"]});
                rc.push({"name": "MODF逻辑图", "value": 19, "entitytype": ["MODF"], "isrouteview": false});
                rc.push({"name": "MODF路由图", "value": 20, "entitytype": ["MODF"]});
                rc.push({"name": "ODF逻辑图", "value": 21, "entitytype": ["ODF"]});
                rc.push({"name": "ODF路由图", "value": 22, "entitytype": ["ODF"]});
                rc.push({"name": "OJJX逻辑图", "value": 23, "entitytype": ["GJ"], "isrouteview": false});
                rc.push({"name": "OJJX路由图", "value": 24, "entitytype": ["GJ"]});
                rc.push({"name": "CCP逻辑图", "value": 25, "entitytype": ["CCP"], "isrouteview": false});
                rc.push({"name": "CCP路由图", "value": 26, "entitytype": ["CCP"]});
                rc.push({"name": "IDP逻辑图", "value": 27, "isrouteview": false});
                rc.push({"name": "IDP路由图", "value": 28});
                rc.push({"name": "支撑网路由图", "value": 29});
                rc.push({"name": "任意范围路由图", "value": 30});
                rc.push({"name": "DP逻辑图", "value": 31, "entitytype": ["DP"], "isrouteview": false});
                rc.push({"name": "OFXH逻辑图", "value": 32, "entitytype": ["GF"], "isrouteview": false});
                rc.push({"name": "光终端盒逻辑图", "value": 33, "entitytype": ["GB"], "isrouteview": false});
                rc.push({"name": "MDF逻辑图", "value": 34, "entitytype": ["MDF"], "isrouteview": false});
                rc.push({"name": "光网络拓扑图", "value": 40});
                rc.push({"name": "OBD逻辑图", "value": 41, "entitytype": ["OBD"]});
                rc.push({"name": "光缆工程路由图", "value": 42});
                rc.push({"name": "电缆工程路由图", "value": 43});
                rc.push({"name": "光缆工程光纤分布图", "value": 44});
                rc.push({"name": "DP路由图", "value": 45, "entitytype": ["DP"]});
                rc.push({"name": "MDF路由图", "value": 46, "entitytype": ["MDF"]});
                rc.push({"name": "工程路由图（全）", "value": 50});
                return rc;
            },
            /**
             * 根据名称和类型获取对应的图纸类别
             * @param name
             * @param objectType
             */
            findDiagramCategoryList: (name, objectType) => {
                // 全部图纸类别
                const diagramCategoryList = $.diagramOperation.getDiagramCategoryList()
                for (let i = 0; i < diagramCategoryList.length; i++) {
                    const diagramCategory = diagramCategoryList[i]
                    // 名称和entitytype匹配则返回
                    if (diagramCategory.name && diagramCategory.name.indexOf(name) != -1 && diagramCategory.entitytype) {
                        if (diagramCategory.entitytype.indexOf(objectType) != -1) {
                            return diagramCategory.value
                        }
                    }
                }
                return -1
            },
            /**
             * 获取图纸id
             * @param name 传入"逻辑图"，"路由图"等等
             * @param objectType
             * @param entityId
             */
            findDiagramId: (name, objectType, entityId) => {
                return new Promise((resolve, reject) => {
                    // 获取图纸类别
                    const category = $.diagramOperation.findDiagramCategoryList(name, objectType)
                    $.common.getSui().showLoading(null, '正在查询图纸数据')
                    _bean.query('DIAGRAM', {
                        ENTRY_ID: entityId,
                        CATEGORY: category,
                        _showLoading: true
                    }).then(diagrams => {
                        $.common.getSui().showLoading(null, false)
                        // 没有查到图纸，则需要新增
                        if (!diagrams || diagrams.length == 0) {
                            _bean.query('DIAGRAM', {
                                ISFOLDER: 1,
                                CATEGORY: category,
                                source: 3,
                                _showLoading: true
                            }).then(parentDiagrams => {
                                if (!parentDiagrams || parentDiagrams.length == 0) {
                                    $.common.getSui().alert('没有找到合适的图纸类型目录:' + category)
                                    reject()
                                    return
                                }
                                // 图纸目录id
                                const parentDiagramId = parentDiagrams[0].id
                                const url = "bean/action.do?name=makediagram&_type=DIAGRAM&cat=" + category + "&parentid=" + parentDiagramId + "&entitytype=" + objectType + "&entityid=" + entityId;
                                $.common.getSui().showLoading(null, '正在创建图纸数据')
                                _bean.post(url, {_showLoading: true}).then(id => {
                                    $.common.getSui().showLoading(null, false)
                                    resolve(id)
                                }).catch(err1 => {
                                    $.common.getSui().showLoading(null, false)
                                })
                            })
                        } else {
                            resolve(diagrams[0].id)
                        }
                    }).catch(err => {
                        $.common.getSui().showLoading(null, false)
                    })
                })
            },
            /**
             * 打开图纸
             * @param name
             * @param objectType
             * @param entityId
             * @param title
             * @param openType
             */
            openDiagram: (name, objectType, entityId, title, openType = 'workspace') => {
                $.diagramOperation.findDiagramId(name, objectType, entityId).then(diagramId => {
                    $.common.getContext().doAction({
                        title: title,
                        icon: 'edit',
                        type: openType,
                        context: 'top',
                        url: 'modules/diagram/diagram_editor.html?id=' + diagramId
                    });
                })

            }
        },

        /**
         * 表格封装处理
         */
        table: {
            /**
             * 初始化表格参数
             * @param options
             */
            init: function (options) {
                let defaults = {
                    id: "bootstrap-table",
                    type: 0, // 0 代表bootstrapTable 1代表bootstrapTreeTable
                    height: undefined,
                    sidePagination: "server",
                    sortName: "",
                    sortOrder: "asc",
                    pagination: true,
                    pageSize: 10,
                    pageList: [10, 25, 50],
                    toolbar: "toolbar",
                    striped: false,
                    escape: false,
                    firstLoad: true,
                    showFooter: false,
                    search: false,
                    showSearch: true,
                    showPageGo: false,
                    showRefresh: true,
                    showColumns: true,
                    showToggle: true,
                    reorderableColumns: true,
                    resizable: true,
                    showExport: false,
                    clickToSelect: false,
                    singleSelect: false,
                    mobileResponsive: true,
                    rememberSelected: false,
                    fixedColumns: false,
                    fixedNumber: 0,
                    rightFixedColumns: false,
                    rightFixedNumber: 0,
                    queryParams: $.table.queryParams,
                    rowStyle: (item, index) => {
                        return {}
                    },
                };
                options = $.extend(defaults, options);
                table.options = options;
                table.config[options.id] = options;
                $.table.initEvent();
                $('#' + options.id).bootstrapTable({
                    id: options.id,
                    url: options.url,                                   // 请求后台的URL（*）
                    contentType: "application/x-www-form-urlencoded",   // 编码类型
                    method: 'post',                                     // 请求方式（*）
                    cache: false,                                       // 是否使用缓存
                    height: options.height,                             // 表格的高度
                    striped: options.striped,                           // 是否显示行间隔色
                    sortable: true,                                     // 是否启用排序
                    sortStable: true,                                   // 设置为 true 将获得稳定的排序
                    sortName: options.sortName,                         // 排序列名称
                    sortOrder: options.sortOrder,                       // 排序方式  asc 或者 desc
                    pagination: options.pagination,                     // 是否显示分页（*）
                    pageNumber: 1,                                      // 初始化加载第一页，默认第一页
                    pageSize: options.pageSize,                         // 每页的记录行数（*）
                    pageList: options.pageList,                         // 可供选择的每页的行数（*）
                    firstLoad: options.firstLoad,                       // 是否首次请求加载数据，对于数据较大可以配置false
                    escape: options.escape,                             // 转义HTML字符串
                    showFooter: options.showFooter,                     // 是否显示表尾
                    iconSize: 'outline',                                // 图标大小：undefined默认的按钮尺寸 xs超小按钮sm小按钮lg大按钮
                    toolbar: '#' + options.toolbar,                     // 指定工作栏
                    sidePagination: options.sidePagination,             // server启用服务端分页client客户端分页
                    search: options.search,                             // 是否显示搜索框功能
                    searchText: options.searchText,                     // 搜索框初始显示的内容，默认为空
                    showSearch: options.showSearch,                     // 是否显示检索信息
                    showPageGo: options.showPageGo,               		// 是否显示跳转页
                    showRefresh: options.showRefresh,                   // 是否显示刷新按钮
                    showColumns: options.showColumns,                   // 是否显示隐藏某列下拉框
                    showToggle: options.showToggle,                     // 是否显示详细视图和列表视图的切换按钮
                    showExport: options.showExport,                     // 是否支持导出文件
                    reorderableColumns: options.reorderableColumns,     // 是否能拖拽列
                    resizable: options.resizable,                       // 是否能拖动列宽
                    showPrint: options.showPrint,                       // 是否支持打印页面
                    showHeader: options.showHeader,                     // 是否显示表头
                    showFullscreen: options.showFullscreen,             // 是否显示全屏按钮
                    uniqueId: options.uniqueId,                         // 唯 一的标识符
                    clickToSelect: options.clickToSelect,				// 是否启用点击选中行
                    singleSelect: options.singleSelect,                 // 是否单选checkbox
                    mobileResponsive: options.mobileResponsive,         // 是否支持移动端适配
                    detailView: options.detailView,                     // 是否启用显示细节视图
                    onClickRow: options.onClickRow,                     // 点击某行触发的事件
                    onDblClickRow: options.onDblClickRow,               // 双击某行触发的事件
                    onClickCell: options.onClickCell,                   // 单击某格触发的事件
                    onDblClickCell: options.onDblClickCell,             // 双击某格触发的事件
                    onEditableSave: options.onEditableSave,             // 行内编辑保存的事件
                    onExpandRow: options.onExpandRow,                   // 点击详细视图的事件
                    rememberSelected: options.rememberSelected,         // 启用翻页记住前面的选择
                    fixedColumns: options.fixedColumns,                 // 是否启用冻结列（左侧）
                    fixedNumber: options.fixedNumber,                   // 列冻结的个数（左侧）
                    rightFixedColumns: options.rightFixedColumns,       // 是否启用冻结列（右侧）
                    rightFixedNumber: options.rightFixedNumber,         // 列冻结的个数（右侧）
                    onReorderRow: options.onReorderRow,                 // 当行拖拽结束后处理函数
                    onReorderColumn: options.onReorderColumn,           // 当列拖拽结束后处理函数
                    queryParams: options.queryParams,                   // 传递参数（*）
                    rowStyle: options.rowStyle,                         // 通过自定义函数设置行样式
                    columns: options.columns,                           // 显示列信息（*）
                    data: options.data,                                 // 被加载的数据
                    responseHandler: $.table.responseHandler,           // 在加载服务器发送来的数据之前处理函数
                    onLoadSuccess: $.table.onLoadSuccess,               // 当所有数据被加载时触发处理函数
                    exportOptions: options.exportOptions,               // 前端导出忽略列索引
                    detailFormatter: options.detailFormatter,           // 在行下面展示其他数据列表
                    idField: options.idField,                           // 树表格主键
                    treeShowField: options.treeShowField,               // 树表格显示字段
                    parentIdField: options.parentIdField,               // 树表格父主键字段
                });
            },
            // 获取实例ID，如存在多个返回#id1,#id2 delimeter分隔符
            getOptionsIds: function (separator) {
                let _separator = $.common.isEmpty(separator) ? "," : separator;
                let optionsIds = "";
                $.each(table.config, function (key, value) {
                    optionsIds += "#" + key + _separator;
                });
                return optionsIds.substring(0, optionsIds.length - 1);
            },
            // 查询条件
            queryParams: function (params) {
                let curParams = {
                    // 传递参数查询参数
                    pageSize: params.limit,
                    pageNum: params.offset / params.limit + 1,
                    searchValue: params.search,
                    orderByColumn: params.sort,
                    isAsc: params.order
                };
                let currentId = $.common.isEmpty(table.options.formId) ? $('form').attr('id') : table.options.formId;
                return $.extend(curParams, $.common.formToJSON(currentId));
            },
            // 请求获取数据后处理回调函数
            responseHandler: function (res) {
                if (typeof table.options.responseHandler == "function") {
                    table.options.responseHandler(res);
                }
                if (res.code == 0) {
                    if ($.common.isNotEmpty(table.options.sidePagination) && table.options.sidePagination == 'client') {
                        return res.rows;
                    } else {
                        if ($.common.isNotEmpty(table.options.rememberSelected) && table.options.rememberSelected) {
                            let column = $.common.isEmpty(table.options.uniqueId) ? table.options.columns[1].field : table.options.uniqueId;
                            $.each(res.rows, function (i, row) {
                                row.state = $.inArray(row[column], table.rememberSelectedIds[table.options.id]) !== -1;
                            })
                        }
                        return {rows: res.rows, total: res.total};
                    }
                } else {
                    $.modal.alertWarning(res.msg);
                    return {rows: [], total: 0};
                }
            },
            // 初始化事件
            initEvent: function () {
                // 实例ID信息
                let optionsIds = $.table.getOptionsIds();
                // 监听事件处理
                $(optionsIds).on(TABLE_EVENTS, function () {
                    table.set($(this).attr("id"));
                });
                // 选中、取消、全部选中、全部取消（事件）
                $(optionsIds).on("check.bs.table check-all.bs.table uncheck.bs.table uncheck-all.bs.table", function (e, rows) {
                    // 复选框分页保留保存选中数组
                    let rowIds = $.table.affectedRowIds(rows);
                    if ($.common.isNotEmpty(table.options.rememberSelected) && table.options.rememberSelected) {
                        func = $.inArray(e.type, ['check', 'check-all']) > -1 ? 'union' : 'difference';
                        let selectedIds = table.rememberSelectedIds[table.options.id];
                        if ($.common.isNotEmpty(selectedIds)) {
                            table.rememberSelectedIds[table.options.id] = _[func](selectedIds, rowIds);
                        } else {
                            table.rememberSelectedIds[table.options.id] = _[func]([], rowIds);
                        }
                        let selectedRows = table.rememberSelecteds[table.options.id];
                        if ($.common.isNotEmpty(selectedRows)) {
                            table.rememberSelecteds[table.options.id] = _[func](selectedRows, rows);
                        } else {
                            table.rememberSelecteds[table.options.id] = _[func]([], rows);
                        }
                    }
                });
                // 加载成功、选中、取消、全部选中、全部取消（事件）
                $(optionsIds).on("check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table load-success.bs.table", function () {
                    let toolbar = table.options.toolbar;
                    let uniqueId = table.options.uniqueId;
                    // 工具栏按钮控制
                    let rows = $.common.isEmpty(uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(uniqueId);
                    // 非多个禁用
                    $('#' + toolbar + ' .multiple').toggleClass('disabled', !rows.length);
                    // 非单个禁用
                    $('#' + toolbar + ' .single').toggleClass('disabled', rows.length != 1);
                });
                // 图片预览事件
                $(optionsIds).off("click").on("click", '.img-circle', function () {
                    let src = $(this).attr('src');
                    let target = $(this).data('target');
                    if ($.common.equals("self", target)) {
                        let height = $(this).data('height');
                        let width = $(this).data('width');
                        // 如果是移动端，就使用自适应大小弹窗
                        if ($.common.isMobile()) {
                            width = 'auto';
                            height = 'auto';
                        }
                        layer.open({
                            title: false,
                            type: 1,
                            closeBtn: true,
                            shadeClose: true,
                            area: ['auto', 'auto'],
                            content: "<img src='" + src + "' height='" + height + "' width='" + width + "'/>"
                        });
                    } else if ($.common.equals("blank", target)) {
                        window.open(src);
                    }
                });
                // 单击tooltip事件
                $(optionsIds).on("click", '.tooltip-show', function () {
                    let target = $(this).data('target');
                    let input = $(this).prev();
                    if ($.common.equals("copy", target)) {
                        input.select();
                        document.execCommand("copy");
                    } else if ($.common.equals("open", target)) {
                        parent.layer.alert(input.val(), {
                            title: "信息内容",
                            shadeClose: true,
                            btn: ['确认'],
                            btnclass: ['btn btn-primary'],
                        });
                    }
                });
            },
            // 当所有数据被加载时触发
            onLoadSuccess: function (data) {
                if (typeof table.options.onLoadSuccess == "function") {
                    table.options.onLoadSuccess(data);
                }
                // 浮动提示框特效
                $(".table [data-toggle='tooltip']").tooltip();
            },
            // 表格销毁
            destroy: function (tableId) {
                const currentId = table.getTableId(tableId)
                $("#" + currentId).bootstrapTable('destroy');
            },
            // 序列号生成
            serialNumber: function (index, tableId) {
                const currentId = table.getTableId(tableId);
                let tableParams = $("#" + currentId).bootstrapTable('getOptions');
                let pageSize = tableParams.pageSize;
                let pageNumber = tableParams.pageNumber;
                return pageSize * (pageNumber - 1) + index + 1;
            },
            // 列超出指定长度浮动提示 target（copy单击复制文本 open弹窗打开文本）
            tooltip: function (value, length, target) {
                let _length = $.common.isEmpty(length) ? 20 : length;
                let _text = "";
                let _value = $.common.nullToStr(value);
                let _target = $.common.isEmpty(target) ? 'copy' : target;
                if (_value.length > _length) {
                    _text = _value.substr(0, _length) + "...";
                    _value = _value.replace(/\'/g, "&apos;");
                    _value = _value.replace(/\"/g, "&quot;");
                    let actions = [];
                    actions.push($.common.sprintf('<input id="tooltip-show" style="opacity: 0;position: absolute;z-index:-1" type="text" value="%s"/>', _value));
                    actions.push($.common.sprintf('<a href="###" class="tooltip-show" data-toggle="tooltip" data-target="%s" title="%s">%s</a>', _target, _value, _text));
                    return actions.join('');
                } else {
                    _text = _value;
                    return _text;
                }
            },
            // 搜索-默认第一个form
            search: function (formId, tableId, data) {
                table.set(tableId);
                let currentId = $.common.isEmpty(formId) ? $('form').attr('id') : formId;
                let params = $.common.isEmpty(tableId) ? $("#" + table.options.id).bootstrapTable('getOptions') : $("#" + tableId).bootstrapTable('getOptions');
                params.queryParams = function (params) {
                    let search = $.common.formToJSON(currentId);
                    if ($.common.isNotEmpty(data)) {
                        $.each(data, function (key) {
                            search[key] = data[key];
                        });
                    }
                    search.pageSize = params.limit;
                    search.pageNum = params.offset / params.limit + 1;
                    search.searchValue = params.search;
                    search.orderByColumn = params.sort;
                    search.isAsc = params.order;
                    return search;
                }
                if ($.common.isNotEmpty(tableId)) {
                    $("#" + tableId).bootstrapTable('refresh', params);
                } else {
                    $("#" + table.options.id).bootstrapTable('refresh', params);
                }
            },
            // 刷新表格
            refresh: function (tableId) {
                const currentId = table.getTableId(tableId);
                $("#" + currentId).bootstrapTable('refresh', {
                    silent: true
                });
            },
            // 查询表格指定列值
            selectColumns: function (column) {
                let rows = $.map($("#" + table.options.id).bootstrapTable('getSelections'), function (row) {
                    return row[column];
                });
                if ($.common.isNotEmpty(table.options.rememberSelected) && table.options.rememberSelected) {
                    let selectedRows = table.rememberSelecteds[table.options.id];
                    if ($.common.isNotEmpty(selectedRows)) {
                        rows = $.map(table.rememberSelecteds[table.options.id], function (row) {
                            return row[column];
                        });
                    }
                }
                return $.common.uniqueFn(rows);
            },
            // 获取当前页选中或者取消的行ID
            affectedRowIds: function (rows) {
                let column = $.common.isEmpty(table.options.uniqueId) ? table.options.columns[1].field : table.options.uniqueId;
                let rowIds;
                if ($.isArray(rows)) {
                    rowIds = $.map(rows, function (row) {
                        return row[column];
                    });
                } else {
                    rowIds = [rows[column]];
                }
                return rowIds;
            },
            // 查询表格首列值
            selectFirstColumns: function () {
                let rows = $.map($("#" + table.options.id).bootstrapTable('getSelections'), function (row) {
                    return row[table.options.columns[1].field];
                });
                if ($.common.isNotEmpty(table.options.rememberSelected) && table.options.rememberSelected) {
                    let selectedRows = table.rememberSelecteds[table.options.id];
                    if ($.common.isNotEmpty(selectedRows)) {
                        rows = $.map(selectedRows, function (row) {
                            return row[table.options.columns[1].field];
                        });
                    }
                }
                return $.common.uniqueFn(rows);
            },
            /**
             * 获取列表所有数据
             * @param tableId
             * @returns {*|jQuery}
             */
            getAllDatas: function (tableId) {
                const currentId = table.getTableId(tableId);
                return $('#' + currentId).bootstrapTable('getData');
            },
            /**
             * 获取所选数据
             * @param tableId
             * @returns {*|jQuery}
             */
            getSelectDatas: function (tableId) {
                const currentId = table.getTableId(tableId);
                return $('#' + currentId).bootstrapTable('getSelections');
            },
            /**
             * 根据键值获取对应数据
             * @param key
             * @param val
             * @returns {null|*}
             */
            getDataByKeyValue: function (key, val, tableId) {
                const currentId = table.getTableId(tableId)
                let datas = $.table.getAllDatas(currentId)
                if (datas == null || datas.length <= 0)
                    return null;
                for (let i = 0; i < datas.length; i++) {
                    let data = datas[i];
                    if (data[key] == val)
                        return data;
                }
                return null;
            },
            // 显示表格指定列
            showColumn: function (column, tableId) {
                const currentId = table.getTableId(tableId);
                $("#" + currentId).bootstrapTable('showColumn', column);
            },
            // 隐藏表格指定列
            hideColumn: function (column, tableId) {
                const currentId = table.getTableId(tableId);
                $("#" + currentId).bootstrapTable('hideColumn', column);
            },
            /**
             * 加载数据
             * @param datas
             */
            loadDatas: function (datas, tableId) {
                const currentId = table.getTableId(tableId);
                $("#" + currentId).bootstrapTable('load', datas);
            }
        },
        /**
         * 表格树封装处理
         */
        treeTable: {
            // 初始化表格
            init: function (options) {
                // 拖拽结束方法
                const onReorderColumnFun = options.onReorderColumnFun
                // 排序方法
                const onSortFun = options.onSortFun
                let defaults = {
                    id: "bootstrap-tree-table",
                    type: 1, // 0 代表bootstrapTable 1代表bootstrapTreeTable
                    height: 0,
                    rootIdValue: null,
                    ajaxParams: {},
                    toolbar: "toolbar",
                    striped: false,
                    expandColumn: 1,
                    showSearch: true,
                    showRefresh: true,
                    showColumns: true,
                    expandAll: true,
                    expandFirst: true,
                    reorderableColumns: true,
                    resizable: true,
                    bordered: true,
                    idField: 'id',                        // 树表格主键
                    treeShowField: 'name',               // 树表格显示字段
                    parentIdField: 'pid',               // 树表格父主键字段
                    treeColumn: 1,                      // 树使用那个列
                    // 列拖动变更
                    onReorderColumn: () => {
                        $.treeTable.refreshTree(options)
                        if (onReorderColumnFun) {
                            onReorderColumnFun()
                        }
                    },
                };
                options = $.extend(defaults, options);
                $.table.init(options)
                /**
                 * 排序监听
                 */
                $('#' + options.id).on('sort.bs.table', e => {
                    setTimeout(() => {
                        $.treeTable.refreshTree(options)
                        if (onSortFun) {
                            onSortFun()
                        }
                    }, 100)
                })
            },
            /**
             * 刷新树
             * @param options
             */
            refreshTree: function (options) {
                // tree表格参数
                let treeGridOptions = options.treeGridOptions
                if (!treeGridOptions) {
                    treeGridOptions = {
                        initialState: 'collapse',// 默认展开
                        saveState: true,// 保存状态
                    }
                }
                // 暂时对拖动列重新构建树
                $.treeTable.initTree(treeGridOptions, options.id)
            },
            /**
             * 清除树缓存
             */
            clearCache: function () {
                $.cookie('tree-grid-state', '')
            },
            /**
             * 加载数据
             * @param datas
             * @param tableId
             * @param treeOptions={onExpandFun=展开方法，onCollapseFun=收缩方法}
             */
            loadDatas: function (datas, tableId, treeOptions = {}) {
                $.table.loadDatas(datas, tableId)
                $.treeTable.initTree(treeOptions, tableId)
            },
            /**
             * 拼接数据
             * @param datas
             * @param tableId
             * @param treeOptions
             */
            appendDatas: function (datas, tableId, treeOptions = {}) {
                const currentId = table.getTableId(tableId);
                // 唯一字段
                const idField = table.get(currentId).idField
                // 新数据集合
                const newAllDatas = []
                let allDatas = $.table.getAllDatas(currentId)
                allDatas.forEach(allData => {
                    newAllDatas.push(allData)
                })
                datas.forEach(data => {
                    let exists = false
                    allDatas.forEach(allData => {
                        if (data[idField] == allData[idField]) {
                            exists = true
                        }
                    })
                    if (!exists) {
                        newAllDatas.push(data)
                    }
                })
                $.treeTable.loadDatas(newAllDatas, currentId, treeOptions)
            },
            /**
             * 根据节点id获取子集合
             * @param nodeId
             * @param tableId
             * @returns {[]}
             */
            getChildrensByNodeId: function (nodeId, tableId) {
                const currentId = table.getTableId(tableId);
                // 树表格父主键字段
                const parentIdField = table.get(currentId).parentIdField
                let allDatas = $.table.getAllDatas(currentId)
                let result = []
                allDatas.forEach(d => {
                    if (nodeId && nodeId == d[parentIdField]) {
                        result.push(d)
                    }
                })
                return result
            },
            /**
             * 保存状态
             * @param tableId
             */
            saveState: function (tableId) {
                $('#' + tableId).treegrid('saveState')
            },
            /**
             * 定位节点
             * @param nodeId
             * @param tableId
             */
            locationNode: function (nodeId, tableId, difference = 150) {
                // 点击行位置
                const top = $('.treegrid-' + nodeId).offset().top
                // 滚动到点击行位置
                if (top) {
                    $('#' + tableId).parent().scrollTop(top - difference)
                }
            },
            /**
             * 初始化树
             * @param options 参数
             * @param tableId 列表id
             */
            initTree: function (options = {}, tableId) {
                const currentId = table.getTableId(tableId)
                options.onChange = () => {
                    $('#' + currentId).bootstrapTable('resetView')
                }
                // 展开方法
                const onExpandFun = options.onExpandFun
                // 收缩方法
                const onCollapseFun = options.onCollapseFun
                const tableOptions = table.get(currentId)
                options = $.extend(tableOptions, options);
                /**
                 * 展开事件
                 * @param el
                 */
                options.onExpand = function () {
                    // 节点id
                    const nodeId = $(this).treegrid('getNodeId')
                    if (onExpandFun) {
                        onExpandFun(nodeId)
                    }
                }
                /**
                 * 收缩事件
                 * @param el
                 */
                options.onCollapse = function () {
                    // 节点id
                    const nodeId = $(this).treegrid('getNodeId')
                    if (onCollapseFun) {
                        onCollapseFun(nodeId)
                    }
                }
                // 构建树
                $('#' + currentId).treegrid(options)
            },
            /**
             * 查找节点
             * @param value
             * @param tableId
             */
            findNode: function (value, tableId) {
                const currentId = table.getTableId(tableId)
                // 唯一字段
                const idField = table.get(currentId).idField
                return $.table.getDataByKeyValue(idField, value, tableId)
            },
            /**
             * 展开节点
             * @param value
             * @param tableId
             */
            expandNode: function (value, tableId) {
                const currentId = table.getTableId(tableId)
                $('#' + currentId).find('.treegrid-' + value).treegrid('expand')
            },
            /**
             * 收缩节点
             * @param value
             * @param tableId
             */
            collapseNode: function (value, tableId) {
                const currentId = table.getTableId(tableId)
                $('#' + currentId).find('.treegrid-' + value).treegrid('collapse')
            }
        },
        /**
         * 弹出层封装处理
         */
        modal: {
            /**
             * 显示图标
             * @param type
             * @returns {number}
             */
            icon: function (type) {
                let icon = "";
                if (type == modal_status.WARNING) {
                    icon = 0;
                } else if (type == modal_status.SUCCESS) {
                    icon = 1;
                } else if (type == modal_status.FAIL) {
                    icon = 2;
                } else {
                    icon = 3;
                }
                return icon;
            },
            /**
             * 消息提示
             * @param content
             * @param type
             */
            msg: function (content, type) {
                if (content == null)
                    content = '';
                if (type != undefined) {
                    layer.msg(content, {icon: $.modal.icon(type), time: 1000, shift: 5});
                } else {
                    layer.msg(content);
                }
            },
            /**
             * 错误消息
             * @param content
             */
            msgError: function (content) {
                $.modal.msg(content, modal_status.FAIL);
            },
            /**
             * 成功消息
             * @param content
             */
            msgSuccess: function (content) {
                $.modal.msg(content, modal_status.SUCCESS);
            },
            /**
             * 警告消息
             * @param content
             */
            msgWarning: function (content) {
                $.modal.msg(content, modal_status.WARNING);
            },
            /**
             * 弹出提示
             * @param content
             * @param type
             */
            alert: function (content, type) {
                layer.alert(content, {
                    icon: $.modal.icon(type),
                    title: "系统提示",
                    btn: ['确认'],
                    btnclass: ['btn btn-primary'],
                });
            },
            /**
             * 消息提示并刷新父窗体
             * @param msg
             * @param type
             */
            msgReload: function (msg, type) {
                layer.msg(msg, {
                        icon: $.modal.icon(type),
                        time: 500,
                        shade: [0.1, '#8F8F8F']
                    },
                    function () {
                        $.modal.reload();
                    });
            },
            /**
             * 错误提示
             * @param content
             */
            alertError: function (content) {
                $.modal.alert($.common.toString(content), modal_status.FAIL);
            },
            /**
             * 成功提示
             * @param content
             */
            alertSuccess: function (content) {
                $.modal.alert($.common.toString(content), modal_status.SUCCESS);
            },
            /**
             * 警告提示
             * @param content
             */
            alertWarning: function (content) {
                $.modal.alert($.common.toString(content), modal_status.WARNING);
            },
            /**
             * 关闭窗体
             */
            close: function () {
                let index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            },
            /**
             * 关闭全部窗体
             */
            closeAll: function () {
                layer.closeAll();
            },
            /**
             * 确认窗体
             * @param content
             * @param callBack
             */
            confirm: function (content, callBack) {
                layer.confirm(content, {
                    icon: 3,
                    title: "系统提示",
                    btn: ['确认', '取消']
                }, function (index) {
                    layer.close(index);
                    callBack(true);
                });
            },

            /**
             * 打开内容窗口
             * @param title
             * @param content
             * @param width
             * @param height
             * @param callback
             */
            openContentWindow: function (title, content, width, height, callback) {
                //如果是移动端，就使用自适应大小弹窗
                if ($.common.isMobile()) {
                    width = 'auto';
                    height = 'auto';
                }
                if ($.common.isEmpty(title)) {
                    title = false;
                }
                if ($.common.isEmpty(width)) {
                    width = 800;
                }
                if ($.common.isEmpty(height)) {
                    height = ($(window).height() - 50);
                }
                if ($.common.isEmpty(callback)) {
                    callback = function (index, layero) {
                        let iframeWin = layero.find('iframe')[0];
                        iframeWin.contentWindow.submitHandler(index, layero);
                    }
                }
                layer.open({
                    type: 1,
                    area: [width + 'px', height + 'px'],
                    fix: false,
                    //不固定
                    maxmin: true,
                    shade: 0.3,
                    title: title,
                    content: content,
                    btn: ['确定', '关闭'],
                    // 弹层外区域关闭
                    shadeClose: true,
                    yes: callback,
                    cancel: function (index) {
                        return true;
                    }
                })
            },

            /**
             * 弹出层指定宽度
             * @param title
             * @param url
             * @param width
             * @param height
             * @param callback
             */
            open: function (title, url, width, height, callback) {
                //如果是移动端，就使用自适应大小弹窗
                if ($.common.isMobile()) {
                    width = 'auto';
                    height = 'auto';
                }
                if ($.common.isEmpty(title)) {
                    title = false;
                }
                if ($.common.isEmpty(url)) {
                    url = "/404.html";
                }
                if ($.common.isEmpty(width)) {
                    width = 800;
                }
                if ($.common.isEmpty(height)) {
                    height = ($(window).height() - 50);
                }
                if ($.common.isEmpty(callback)) {
                    callback = function (index, layero) {
                        let iframeWin = layero.find('iframe')[0];
                        iframeWin.contentWindow.submitHandler(index, layero);
                    }
                }
                layer.open({
                    type: 2,
                    area: [width + 'px', height + 'px'],
                    fix: false,
                    //不固定
                    maxmin: true,
                    shade: 0.3,
                    title: title,
                    content: url,
                    btn: ['确定', '关闭'],
                    // 弹层外区域关闭
                    shadeClose: true,
                    yes: callback,
                    cancel: function (index) {
                        return true;
                    }
                })
            },
            /**
             * 弹出层指定参数选项
             * @param options
             */
            openOptions: function (options) {
                let _url = $.common.isEmpty(options.url) ? "/404.html" : options.url;
                let _title = $.common.isEmpty(options.title) ? "系统窗口" : options.title;
                let _width = $.common.isEmpty(options.width) ? "800" : options.width;
                let _height = $.common.isEmpty(options.height) ? ($(window).height() - 50) : options.height;
                let _btn = ['<i class="fa fa-check"></i> 确认', '<i class="fa fa-close"></i> 关闭'];
                if ($.common.isEmpty(options.yes)) {
                    options.yes = function (index, layero) {
                        options.callBack(index, layero);
                    }
                }
                layer.open({
                    type: 2,
                    maxmin: true,
                    shade: 0.3,
                    title: _title,
                    fix: false,
                    area: [_width + 'px', _height + 'px'],
                    content: _url,
                    shadeClose: $.common.isEmpty(options.shadeClose) ? true : options.shadeClose,
                    skin: options.skin,
                    btn: $.common.isEmpty(options.btn) ? _btn : options.btn,
                    yes: options.yes,
                    cancel: function () {
                        return true;
                    }
                });
            },
            /**
             * 弹出层全屏
             * @param title
             * @param url
             * @param width
             * @param height
             */
            openFull: function (title, url, width, height) {
                //如果是移动端，就使用自适应大小弹窗
                if ($.common.isMobile()) {
                    width = 'auto';
                    height = 'auto';
                }
                if ($.common.isEmpty(title)) {
                    title = false;
                }
                if ($.common.isEmpty(url)) {
                    url = "/404.html";
                }
                if ($.common.isEmpty(width)) {
                    width = 800;
                }
                if ($.common.isEmpty(height)) {
                    height = ($(window).height() - 50);
                }
                let index = layer.open({
                    type: 2,
                    area: [width + 'px', height + 'px'],
                    fix: false,
                    //不固定
                    maxmin: true,
                    shade: 0.3,
                    title: title,
                    content: url,
                    btn: ['确定', '关闭'],
                    // 弹层外区域关闭
                    shadeClose: true,
                    yes: function (index, layero) {
                        let iframeWin = layero.find('iframe')[0];
                        iframeWin.contentWindow.submitHandler(index, layero);
                    },
                    cancel: function (index) {
                        return true;
                    }
                });
                layer.full(index);
            },
            /**
             * 禁用按钮
             */
            disable: function () {
                let doc = null
                try {
                    doc = window.top == window.parent ? window.document : window.parent.document;
                } catch (e) {
                    doc = window.parent.document;
                    console.error(e)
                }
                $("a[class*=layui-layer-btn]", doc).addClass("layer-disabled");
            },
            /**
             * 启用按钮
             */
            enable: function () {
                let doc = null
                try {
                    doc = window.top == window.parent ? window.document : window.parent.document;
                } catch (e) {
                    doc = window.parent.document;
                    console.error(e)
                }
                $("a[class*=layui-layer-btn]", doc).removeClass("layer-disabled");
            },
            /**
             * 打开遮罩层
             * @param message
             */
            loading: function (message) {
                $.blockUI({message: '<div class="loaderbox"><div class="loading-activity"></div> ' + message + '</div>'});
            },
            /**
             * 关闭遮罩层
             */
            closeLoading: function () {
                setTimeout(function () {
                    $.unblockUI();
                }, 50);
            },
            /**
             * 重新加载
             */
            reload: function () {
                parent.location.reload();
            }
        },
    })
})(jQuery)
