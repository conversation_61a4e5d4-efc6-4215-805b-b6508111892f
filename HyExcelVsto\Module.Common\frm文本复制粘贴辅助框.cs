﻿/*
 * ============================================================================
 * 功能模块：Excel文本复制粘贴辅助工具
 * ============================================================================
 * 
 * 模块作用：为Excel提供便捷的文本复制粘贴辅助功能
 * 
 * 主要功能：
 * - 选区跟踪：实时跟踪Excel中的选中区域变化
 * - 行导航：支持上一行、下一行的快速导航
 * - 文本显示：以列表形式显示当前行的所有单元格内容
 * - 快速复制：点击列表项即可复制对应文本到剪贴板
 * - 筛选支持：自动识别并显示筛选行的标题信息
 * - 隐藏列处理：自动跳过隐藏的列
 * - 数值优化：自动去除数值的多余空格
 * 
 * 执行逻辑：
 * 1. 监听Excel选区变化事件
 * 2. 获取当前选中区域的行数据
 * 3. 检测是否存在筛选行并获取标题
 * 4. 在ListView中显示当前行的所有可见列数据
 * 5. 用户点击列表项时自动复制到剪贴板
 * 6. 支持通过按钮导航到上一行或下一行
 * 
 * 注意事项：
 * - 自动跳过隐藏的行和列
 * - 支持多区域选择的处理
 * - 提供列宽自动调整功能
 * ============================================================================
 */

using ET;
using HyExcelVsto.Interfaces;
using Microsoft.Office.Interop.Excel;
using System;
using System.Windows.Forms;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// Excel文本复制粘贴辅助工具窗体
    /// </summary>
    /// <remarks>
    /// 实现IExcelMessageReceiver接口，用于接收Excel选区变化消息
    /// </remarks>
    public partial class frm文本复制粘贴辅助框 : Form, IExcelMessageReceiver
    {
        /// <summary>
        /// 当前选中的Excel范围
        /// </summary>
        private Range selectedRange;
        
        /// <summary>
        /// 当前显示的行索引
        /// </summary>
        private int currentRowIndex;
        
        /// <summary>
        /// 最大搜索行数限制
        /// </summary>
        private const int MAX_SEARCH_ROWS = 10000;
        
        /// <summary>
        /// 标记用户是否手动调整了列宽
        /// </summary>
        private bool isManuallyResized = false;
        
        /// <summary>
        /// 第一列占总宽度的比例
        /// </summary>
        private float columnRatio = 0.2f;

        /// <summary>
        /// 初始化文本复制粘贴辅助框窗体
        /// </summary>
        /// <remarks>
        /// 执行逻辑：初始化组件 → 配置ListView → 设置按钮状态 → 更新标题
        /// </remarks>
        public frm文本复制粘贴辅助框()
        {
            InitializeComponent();
            InitializeListView();
            InitializeButtons();
            UpdateListViewHeader();
        }

        /// <summary>
        /// 初始化ListView控件的属性和事件
        /// </summary>
        /// <remarks>配置ListView为详细视图模式，支持整行选择和网格线显示</remarks>
        private void InitializeListView()
        {
            // 设置ListView基本属性
            listView1.FullRowSelect = true;  // 整行选择
            listView1.GridLines = true;      // 显示网格线
            listView1.View = View.Details;   // 详细视图模式
            listView1.LabelEdit = true;      // 允许标签编辑
            listView1.HeaderStyle = ColumnHeaderStyle.Nonclickable; // 显示标题栏但不可点击
            
            // 绑定事件处理器
            listView1.Click += ListView1_Click;
            listView1.ColumnWidthChanged += ListView1_ColumnWidthChanged;
        }

        /// <summary>
        /// 初始化导航按钮的状态
        /// </summary>
        /// <remarks>默认禁用上一行和下一行按钮</remarks>
        private void InitializeButtons()
        {
            buttonPreviousRow.Enabled = false;
            buttonNextRow.Enabled = false;
        }

        /// <summary>
        /// 指示当前是否正在更新中
        /// </summary>
        /// <remarks>用于防止递归更新</remarks>
        public bool IsUpdating { get; private set; }

        /// <summary>
        /// 更新选定范围和当前行显示
        /// </summary>
        /// <param name="forceUpdate">是否强制更新，忽略跟踪选择设置</param>
        /// <remarks>
        /// 执行逻辑：检查更新状态 → 获取新选区 → 更新行索引 → 检查筛选 → 更新显示
        /// </remarks>
        public void UpdateSelectedRange(bool forceUpdate = false)
        {
            // 防止递归更新
            if (IsUpdating)
                return;

            IsUpdating = true;

            try
            {
                // 检查是否需要跟踪选择
                if (!forceUpdate && !checkBoxTrackSelection.Checked)
                    return;

                // 获取当前Excel选中范围
                Range newRange = ETExcelExtensions.GetSelectionRange();
                if (newRange == null)
                    return;

                // 更新选中范围和当前行索引
                selectedRange = newRange;
                currentRowIndex = selectedRange.Row;

                // 检查工作表是否存在筛选行
                bool hasFilterRow = CheckForFilterRow(selectedRange.Worksheet);

                // 更新ListView内容和标题
                UpdateListViewContent(hasFilterRow);
                UpdateListViewHeader();
            }
            finally
            {
                IsUpdating = false;
            }
        }

        /// <summary>
        /// 检查工作表是否存在筛选行
        /// </summary>
        /// <param name="worksheet">要检查的工作表</param>
        /// <returns>如果存在筛选行返回true，否则返回false</returns>
        private bool CheckForFilterRow(Worksheet worksheet)
        {
            // 检查工作表是否有自动筛选
            return worksheet.AutoFilterMode;
        }

        /// <summary>
        /// 更新ListView的内容
        /// </summary>
        /// <param name="hasFilterRow">是否存在筛选行</param>
        private void UpdateListViewContent(bool hasFilterRow)
        {
            listView1.BeginUpdate();
            try
            {
                listView1.Items.Clear();

                if (selectedRange == null)
                    return;

                // 只有在列数发生变化时才清除并重新添加列
                if ((hasFilterRow && listView1.Columns.Count != 2) || (!hasFilterRow && listView1.Columns.Count != 1))
                {
                    listView1.Columns.Clear();
                    if (hasFilterRow)
                    {
                        listView1.Columns.Add("标题", -2, System.Windows.Forms.HorizontalAlignment.Left);
                        listView1.Columns.Add("内容", -2, System.Windows.Forms.HorizontalAlignment.Left);
                    }
                    else
                    {
                        listView1.Columns.Add("内容", -2, System.Windows.Forms.HorizontalAlignment.Left);
                    }
                    isManuallyResized = false;
                }

                // 获取筛选行（如果存在）
                Range filterRow = hasFilterRow ? selectedRange.Worksheet.AutoFilter.Range.Rows[1] : null;

                // 遍历所有选中的区域
                foreach (Range area in selectedRange.Areas)
                {
                    // 获取当前区域的当前行
                    Range currentRowArea = area.Worksheet.Range[
                        area.Worksheet.Cells[currentRowIndex, area.Column],
                        area.Worksheet.Cells[currentRowIndex, area.Column + area.Columns.Count - 1]];

                    // 遍历当前区域的所有单元格
                    foreach (Range cell in currentRowArea.Cells)
                    {
                        // 检查列是否隐藏
                        if (!cell.EntireColumn.Hidden)
                        {
                            string cellText = cell.Text;
                            ListViewItem item;

                            if (hasFilterRow)
                            {
                                string headerText = filterRow.Cells[1, cell.Column].Text;
                                item = new ListViewItem(new string[] { headerText, cellText });
                            }
                            else
                            {
                                item = new ListViewItem(cellText);
                            }

                            listView1.Items.Add(item);
                        }
                    }
                }

                UpdateButtonStates();

                // 如果列数发生变化，重新调整列宽
                if (!isManuallyResized)
                {
                    AdjustColumnWidths();
                }
            }
            finally
            {
                listView1.EndUpdate();
            }
        }

        /// <summary>
        /// 更新按钮状态
        /// </summary>
        private void UpdateButtonStates()
        {
            buttonPreviousRow.Enabled = currentRowIndex > 1;
            buttonNextRow.Enabled = true; // 总是启用"下一行"按钮，因为我们不知道下一行是否有数据
        }

        /// <summary>
        /// 处理ListView的单击事件
        /// </summary>
        private void ListView1_Click(object sender, EventArgs e)
        {
            if (listView1.SelectedItems.Count > 0)
            {
                string selectedText;

                if (listView1.Columns.Count == 2)
                {
                    // 如果有两列，复制第二列的值
                    selectedText = listView1.SelectedItems[0].SubItems[1].Text;
                }
                else
                {
                    // 如果只有一列，复制第一列的值
                    selectedText = listView1.SelectedItems[0].Text;
                }

                string trimmedText = selectedText.Trim();
                if (double.TryParse(trimmedText, out _))
                {
                    selectedText = trimmedText;
                }

                Clipboard.SetText(selectedText);
            }
        }

        /// <summary>
        /// 处理窗体大小改变事件
        /// </summary>
        private void frm文本复制粘贴辅助框_SizeChanged(object sender, EventArgs e)
        {
            listView1.Dock = DockStyle.Fill;
            AdjustColumnWidths();
        }

        /// <summary>
        /// 上一行按钮点击事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>导航到上一个可见行并更新显示</remarks>
        private void buttonPreviousRow_Click(object sender, EventArgs e)
        {
            if (currentRowIndex > 1)
            {
                // 查找上一个可见行
                int newRowIndex = FindPreviousVisibleRow(currentRowIndex - 1);
                if (newRowIndex > 0)
                {
                    currentRowIndex = newRowIndex;
                    
                    // 检查筛选状态并更新显示
                    bool hasFilterRow = CheckForFilterRow(selectedRange.Worksheet);
                    UpdateListViewContent(hasFilterRow);
                    UpdateListViewHeader();
                }
            }
        }

        /// <summary>
        /// 下一行按钮点击事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>导航到下一个可见行并更新显示，如果找不到数据则显示提示信息</remarks>
        private void buttonNextRow_Click(object sender, EventArgs e)
        {
            // 查找下一个可见行
            int newRowIndex = FindNextVisibleRow(currentRowIndex + 1);
            if (newRowIndex > 0)
            {
                currentRowIndex = newRowIndex;
                
                // 检查筛选状态并更新显示
                bool hasFilterRow = CheckForFilterRow(selectedRange.Worksheet);
                UpdateListViewContent(hasFilterRow);
                UpdateListViewHeader();
            }
            else
            {
                // 未找到数据时显示提示信息
                listView1.Items.Clear();
                listView1.Items.Add("连续查找1万行，找不到数据。");
            }
        }

        /// <summary>
        /// 查找指定行之前的可见行
        /// </summary>
        /// <param name="startRow">开始查找的行号</param>
        /// <returns>找到的可见行号，未找到时返回-1</returns>
        /// <remarks>从指定行向上查找第一个非隐藏的行</remarks>
        private int FindPreviousVisibleRow(int startRow)
        {
            // 从指定行向上查找
            for (int i = startRow; i > 0; i--)
            {
                if (!selectedRange.Worksheet.Rows[i].Hidden)
                {
                    return i;
                }
            }
            return -1;
        }

        /// <summary>
        /// 查找指定行之后的可见且非空行
        /// </summary>
        /// <param name="startRow">开始查找的行号</param>
        /// <returns>找到的可见行号，未找到时返回-1</returns>
        /// <remarks>
        /// 从指定行向下查找，限制搜索范围以避免性能问题
        /// </remarks>
        private int FindNextVisibleRow(int startRow)
        {
            // 获取工作表的有效工作区域
            Range usedRange = selectedRange.Worksheet.UsedRange;
            int lastUsedRow = usedRange.Row + usedRange.Rows.Count - 1;

            // 计算搜索的结束行（限制搜索范围）
            int endRow = Math.Min(Math.Min(startRow + MAX_SEARCH_ROWS, lastUsedRow), selectedRange.Worksheet.Rows.Count);

            // 从指定行向下查找
            for (int i = startRow; i <= endRow; i++)
            {
                // 检查行是否可见
                if (!selectedRange.Worksheet.Rows[i].Hidden)
                {
                    // 获取当前行在选中范围内的部分
                    Range currentRow = selectedRange.Worksheet.Range[
                        selectedRange.Worksheet.Cells[i, selectedRange.Column],
                        selectedRange.Worksheet.Cells[i, selectedRange.Column + selectedRange.Columns.Count - 1]];

                    // 检查行是否非空
                    if (!IsRowEmpty(currentRow))
                    {
                        return i;
                    }
                }
            }
            return -1;
        }

        /// <summary>
        /// 检查指定行是否为空
        /// </summary>
        /// <param name="row">要检查的行范围</param>
        /// <returns>行为空时返回true，否则返回false</returns>
        /// <remarks>只检查可见列中的单元格内容</remarks>
        private bool IsRowEmpty(Range row)
        {
            foreach (Range cell in row.Cells)
            {
                // 只检查可见列且内容非空的单元格
                if (!cell.EntireColumn.Hidden && !string.IsNullOrWhiteSpace(cell.Text))
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 跟踪选择复选框状态变更事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>当启用跟踪选择时立即更新显示</remarks>
        private void checkBoxTrackSelection_CheckedChanged(object sender, EventArgs e)
        {
            if (checkBoxTrackSelection.Checked)
            {
                UpdateSelectedRange(true);
            }
        }

        /// <summary>
        /// ListView列宽变化事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">列宽变化事件参数</param>
        /// <remarks>记录用户手动调整列宽的操作并保存比例</remarks>
        private void ListView1_ColumnWidthChanged(object sender, ColumnWidthChangedEventArgs e)
        {
            isManuallyResized = true;
            if (listView1.Columns.Count == 2)
            {
                columnRatio = (float)listView1.Columns[0].Width / listView1.ClientSize.Width;
            }
        }

        /// <summary>
        /// 自动调整ListView列宽
        /// </summary>
        /// <remarks>
        /// 单列时占满整个宽度，双列时第一列保持合适宽度，第二列占剩余空间
        /// </remarks>
        private void AdjustColumnWidths()
        {
            if (listView1.Columns.Count > 0)
            {
                if (listView1.Columns.Count == 1)
                {
                    // 单列时占满整个宽度
                    listView1.Columns[0].Width = listView1.ClientSize.Width;
                }
                else if (listView1.Columns.Count == 2)
                {
                    // 计算约6个字符的宽度作为最小宽度
                    int approxSixCharWidth = TextRenderer.MeasureText("XXXXXXXXXXXX", listView1.Font).Width;

                    if (!isManuallyResized)
                    {
                        // 未手动调整时使用默认宽度
                        listView1.Columns[0].Width = approxSixCharWidth;
                        columnRatio = (float)approxSixCharWidth / listView1.ClientSize.Width;
                    }
                    else
                    {
                        // 已手动调整时使用保存的比例，但不小于最小宽度
                        int firstColumnWidth = Math.Max(
                            approxSixCharWidth,
                            (int)(listView1.ClientSize.Width * columnRatio));
                        listView1.Columns[0].Width = firstColumnWidth;
                    }

                    // 第二列占剩余空间
                    listView1.Columns[1].Width = listView1.ClientSize.Width - listView1.Columns[0].Width;
                }
            }
        }

        /// <summary>
        /// 更新ListView标题显示当前行号
        /// </summary>
        /// <remarks>在第一列标题中显示当前行号信息</remarks>
        private void UpdateListViewHeader()
        {
            if (listView1.Columns.Count > 0)
            {
                listView1.Columns[0].Text = $"第 {currentRowIndex} 行";
            }
        }

        /// <summary>
        /// 设置窗体置顶状态
        /// </summary>
        /// <param name="isTopMost">是否置顶显示</param>
        private void SetTopMost(bool isTopMost)
        { 
            TopMost = isTopMost; 
        }

        /// <summary>
        /// 置顶复选框状态变更事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void checkBoxOnTop_CheckedChanged(object sender, EventArgs e)
        { 
            SetTopMost(checkBoxOnTop.Checked); 
        }

        /// <summary>
        /// 窗体加载事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>窗体加载时根据复选框状态设置置顶</remarks>
        private void frm文本复制粘贴辅助框_Load(object sender, EventArgs e)
        { 
            SetTopMost(checkBoxOnTop.Checked); 
        }

        /// <summary>
        /// 窗体标识符
        /// </summary>
        /// <remarks>用于标识此窗体的唯一名称</remarks>
        public string FormIdentifier => "文本复制辅助";

        /// <summary>
        /// 接收Excel选区变化消息
        /// </summary>
        /// <param name="target">目标范围</param>
        /// <param name="message">消息内容</param>
        /// <remarks>实现IExcelMessageReceiver接口，响应Excel选区变化</remarks>
        public void OnExcelSelectionMessage(Range target, string message = null)
        {
            UpdateSelectedRange();
        }
    }
}