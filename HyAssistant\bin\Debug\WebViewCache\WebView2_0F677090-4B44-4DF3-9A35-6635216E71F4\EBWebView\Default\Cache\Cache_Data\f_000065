_locale.g5 = {
  applicationTitle: '管线系统(G5)',
  bean: {
    meta: {}
  },
  coveraddress:{
	updatefrontDisplaySuccess:"群改前台显示成功",
	updatefrontDisplayError:"群改前台显示失败",
	updateAccessTypesSuccess:"群改接入方式成功",
	updateAccessTypesFail:"群改接入方式失败",
	updataSiteDirectionsSuccess:"群改局向成功",
	updataSiteDirectionsFail:"群改局向失败"
  },
  common: {
    objType: '类型',
    createBy: '创建人',
    modifyBy: '最近修改',

    fastSearch: '快速搜索',
    fastSearchTip: '请输入关键字并回车',

    prefix: '前缀',
    serNo: '序号',
    suffix: '后缀',
    querytips: "数据查询中，请稍候...",
    addPointResource: '请选择要录入的资源类型！！！',
    notes: '此坐标已存在资源，不可新新增机房！！',
    shardingSwitchTo:'是否切换到'
  },
  types: {
    WELL: '人手井', POLE: '电杆', SUPPORTPOINT: '撑点', DRAWINGPOINT: '引上点', MARKSTONE: '标石',
    GJ: '光交', GF: '光分', GB: '光终端盒', OBD: '分光器', ONU: 'ONU', GFGROUP: 'GF群',
    CCP: '交接箱', DP: '分线盒', ZHX: '综合配线箱', DPGROUP: 'DP群', UNDERWELL: '地下进线室', UPPERSECTION: '引上段',
    resource: '资源',
    ODF: '光配线架',
    DDF: '数字配线架',
    SITE: '局站',
    ROOM: '机房',
	OUTDOORADDRESS: '安装点',
    GT: '光缆接头',
    HOLE: '管孔',
    DT: '电缆接头',
    MDF: '配线架',
    MODF: '光纤总配线架',
    IDF: '综合配线架',
    AG: 'AG设备',
    AGCF: '接入网关控制设备',
    PSTNDEVICE: "PSTN设备",
    DSLAMDEVICE: "DSLAM设备",
    SWITCH: "交换机",
    SDHDEVICE: "SDH设备",
    MSTPDEVICE: "MSTP设备",
    IPRANDEVICE: "IPRAN设备",
    DWDMDEVICE: "波分设备",
    BAS: "宽带接入设备",
    INFORMATIONPOINT: "信息点",
    ODM: "ODM设备",


    OCABLESECTION: '光缆段',

    PIPESECTION: '管道段',
    HANGLINGSECTION: '吊线段',
    COMMONLINE: '关联线',
    SAFETYSIGN: '安全标识',

    PIPEDEVICE: '管线设备',
    AUXILIARYDEVICE: '辅助设备',
    PONDEVICE: 'PON设备',
    SWITCHDEVICE: '交换设备',
    ACCESSDEVICE: '接入设备',
    DEVICE: '设备',

    OCABLE: '光缆',
    ECABLE: '电缆',
    GRESERVE: '光缆预留',
    CABLECOIL: '光缆盘留',
    DRESERVE: '电缆预留',
    ECABLECOIL: '电缆盘留',
    ECABLESECTION: '电缆段',
    QMQS: '气门气塞',
    SENSOR: '传感器',
    WARNINGEQUIPMENT: '告警器',

    SITEFIBER: '局向光纤',
    SITEFIBERS: '局向光纤组',
    ACCESSPOINT: '接入点',
    POMD: '无源光复用设备',
  },
  //类型全称
  completeTypes: {
    MDF: '配线架',
    IDF: '综合配线架',
    GISWARNINGEQUIPMENT: '告警器'
  },
  fields: {
    NAME: '名称', CODE: '编码', LENGTH: '长度', CAPACITY: '芯数',
    organization: {}
  },
  template: {
    label: '模板', manage: '模板管理'
  },
  actions: {
    connectors: '光端子图',
    econnectors:'电端子图'
  },
  menu: {
    start: '功能菜单',
    map: '地图基础',
    locate: '地图定位',
    draw: '绘制测量',
    mapPrint: '地图打印',
    mapSampleView: '图例展示',
    buildingSupplementaryRecord: '楼宇补录',
    mapExport: '地图导出',
    layerControl: '图层控制',
    lifeControl: '生命周期',
    mapBookmark: '地址收藏',
    addressSearch: '地址查询',
    standardAddress: '标准地址',
    buildingAddress: '楼宇地址',
    measureLine: '缆线测量',

    basics: '应用功能',
    layinput: '边建边敷',

    projects: '工程管理',
    planorder: '规划工单',
    projectlist: '工程列表',
    designverify: '设计审核',
    constructionverify: '施工审核',
    completionverify: '竣工审核',

    resource: '资源管理',
    spaceResource: '空间资源',
    spaceTree: '空间资源',
    site: '局站',
    room: '机房',
    buildmarket: '楼宇',
    outdooraddress: '安装点',
    siteCodingCut: '局站编码割接',
    siteMerger: '局站合并',
    siteSeparation: '局站分拆',


    ocables: '光缆网',
    gj: '光交接箱',
    gb: '光终端盒',
    gf: '光分纤箱',
    gw: '光网络箱',
    gjmgt: '光交接箱模板',
    gfmgt: '光分纤箱模板',
    gbmgt: '光终端盒模板',
    zhxmgt: '综合配线箱模板',
    obdmgt: '分光器模板',
    gtmgt: '光接头模板',
    moduleTemplatemgt: '光模块模板',
    ocablesmgt: '光缆网模版',
    zhx: '综合箱',
    obd: '分光器',
    odm: '板卡',
    onu: 'ONU设备',
    olt: 'OLT设备',
    gt: '光接头',
    greserve: '光缆预留',
    cablecoil: '光缆盘留',
    standardrack: '标准机架',
    ocable: '光缆',
    sitefiber: '局向光纤',
    sitefibers: '局向光纤组',
    moduleTemplate: '光模块',
    optic: '光路',
    opticSearch: '光路配置',
    changeOBD: '换OBD',
    //69077 【浙江】【O3管线第二轮内测】OBD升级降级功能名称修正以及示意图对调
    removeOBD: 'OBD降级',
    addOBD: 'OBD升级',
    // removeOBD: '移除OBD',
    // addOBD: '添加OBD',
    iodf: '智能光纤配线架',

    fttxSolution: "FTTX模块化录入",
    deviceBindMdf: "电缆网工程关联",
    cutover: '割接管理',
    ocablecutover: '光缆网',
    ecablecutover: '电缆网',
    eportcutover: '电端子割接',
    ecablecut: '电缆网分割',
    ecableparallelmerge: '电缆网串行合并',
    ecablelinecutover: '电缆线路割接',
    ecableSpilit: '电缆段拆分',
    ecableSpilitMerge: '电缆段拆分合并',
    ecableMerging: '电缆段合并',
    GJCodecutover: '光交接箱编码割接',
    ZHXCodecutover: '综合箱编码割接',
    CCPCodecutover: '交接箱编码割接',
    cableSectionSplit: '光缆段分拆',
    serialMerging: '光缆段串行合并',
    parallelMerging: '光缆段并行合并',
    odevportcut: '光设备端子割接',
    fiberCutover: '纤芯业务割接',
    ocablecut: "光缆段分割",
    cablelinecut: "光缆线路割接",
    fibergroupcut: "局向光纤组割接",
    sitefiberQuery: "局向光纤查询",


    ecables: '电缆网',
    ccp: '交接箱',
    dp: '分线盒',
    dt: '电接头',
    dpgroup: 'DP群',
    dreserve: '电缆预留',
    ecable: '电缆',
    ocable: '光缆',
    mdf: '配线架',
    ddf: '数字配线架',
    ecablesection: '电缆段',
    ocablesection: '光缆段',
    ecablesmgt: '电缆网模版',
    mdfmgt: '配线架模板',
    ddfmgt: '数字配线架模板',
    ccpmgt: '交接箱模板',
    dpmgt: '分线盒模板',
    dtmgt: '电接头模板',
    ecablemgt: '电缆模板',
    ecablesectionmgt: '电缆段模板',

    ASSISTANCE: '辅助设施',

    supports: '支撑网',
    supportsmgt: '支撑网模版',
    well: '人手井',
    pole: '电杆',
    underwell: '地下进线室',
    markstone: '标石',
    buriedsection: '直埋段',
    supportpoint: '支撑点',
    drawingpoint: '引上点',
    pipesection: '管道段',
    hanglingsection: '吊线段',
    commonline: '关联线',
    batchInput: '批量录入',
    pipe: '管道网',
    hangling: '杆路网',
    hanglingMgt: '吊线管理',
    tubegroupMgt: '管群管理',

    wellmgt: '人手井模板',
    polemgt: '电杆模板',
    supportpointmgt: '支撑点模板',
    drawingpointmgt: '引上点模板',
    pipesectionmgt: '管道段模板',
    hanglingsectionmgt: '吊线段模板',
    commonlinemgt: '关联线模板',
    holemgt: '管孔模版',


    supportApplication: '支撑网',
    interruptSupportNetwork: '打断支撑段',
    mergedSupportNetwork: '连接支撑段',
    spaceResources: '空间资源',
    PONEquipmentCut: 'PON设备',

    publicResource: '公共资源',
    businessResourceQuery: '业务资源查询',
    sameAliasAuditMgt: '同义词审核',
    positionAudit: '标准地址审核',

    powerdevices: '动力设备',
    linkdevices: '连接设备',
    oilengine: '油机',
    directcursys: '直流系统',
    ups: '交流不间断电源设备',
    transformer: '变电设备',
    airconditiondistribox: '空调配电箱',
    districabinet1: '一级配电柜',
    districabinet2: '二级配电柜',
    accumulatorset: '电池设备',
    aircondition: '空调设备',


    templatemgt: '模版管理',
    modelmgt: '类型模板',
    equipmodelmgt: '设备模板',
    rackmgt: '机架模版',
    framemgt: '机框模版',
    slotmgt: '槽道模版',
    cardmgt: '板卡模版',
    vendormgt: '厂家管理',
    ocablemgt: '光缆模板',
    ocablesectionmgt: '光缆段模板',

    others: '其他功能',
    accessarea: '综合接入区',
    gjaccessarea: '光交接入区',
    gjcoverarea: '一级光交覆盖区',
    streetlane: '街坊',
    diagram: '出图管理',
    mapSelectPosition: '地图选址',
    textSelectPosition: '文本选址',

    rackmgt: '机架管理',
    idf: '综合配线架',
    modf: '光纤总配线架',
    odf: '光配线架',
    edf: 'EDF',
    zhx: '综合配线箱',

    gridAreaTree: '网格导航树',
    gridAreaTreeMgt: '管理导航树维护',

    accessarea1: '综合业务区',
    accessarea2: '综合业务接入区',

    planproject: '规划项目',
    plansite: '局站/机房建设需求',

    diagrammanage: '图纸管理',
    labelprint: '标签打印',
    labeltemplates: '标签模板管理',
    opticlabel_dev: '光路标签打印（按设备）',
    opticlabel_optic: '光路标签打印（按光路）',
    ocablesitelabel: '光缆局向信息标签',
    devicelabel: '设备标签打印',
    deviceQRcode: '设备二维码打印',
    roomlabel: '机房标签打印',
    leathercablelabel: '皮缆标签打印',
    customizedLabelPrint: '预制标签打印',

    system: '系统管理',
    visitStatistics: '访问次数统计',
    statistical: '统计应用',
    users: '用户管理',
    menucontrol: '菜单管理',
    syslog: '操作日志',
    cache: '清除缓存',
    manual: '操作手册',
    version: '版本信息',

    ancillaryEquipment: '附属设备',
    belongSupportNetwork:  '所属支撑段',
    standardAddress: '标准地址',
    addressImport: '地址导入',
    templateExport: '模板导出',

    projectBind: '工程关联',
    oltBindObd: 'OLT关联OBD',
    obdBindObd: 'OBD关联OBD',
    odfBindOdevice: 'ODF关联设备',

    deviceManagement: '设备管理',
    wareManagement: '硬件管理',
    popManagement: 'POP点',
    pvlanManagement: 'Pvlan',
    cvlanManagement: 'Cvlan',
    description: {
      diagram: '图纸功能编辑与展示'
    },

    networkSecurityAnalysis: '网络安全分析',
    sameRouteAnalysis: '同路由分析',
    multiOpticLocate: '多光缆光路定位',

    companymgt: '分公司管理',
    estimation: '投资估算工具',
    formula: '公式管理',
    reckon: '估算模型',

    accesspoint: '接入点',
    childaccesspoint: '子接入点',
    outdooraddress: '安装点',
  },
  map: {
    title: '地图图层',
    dynamic: '动态图层',
    cache: '底图图层',
    resources: '业务图层',
    standardaddress: '标准地址',
    addressPolygon: '地址面',
    addressLine: '地址线',
    addressPoint: '地址点',
    supports: '支撑网',
    pole: '电杆',
    supportpoint: '支撑点',
    drawingpoint: '引上点',
    underwell: '地下进线室',
    pipesection: '管道段',
    hanglingsection: '吊线段',
    commonline: '关联线',
    safetysign: '安全标识',
    ecables: '电缆网',
    ecablesection: '电缆段',
    ocables: '光缆网',
    gj: '光交接箱',
    gb: '光终端盒',
    gf: '光分纤箱',
    gfgroup: 'GF群',
    zhx: '综合箱',
    obd: '分光器',
    onu: 'ONU设备',
    ocablesection: '光缆段',
    gt: '光缆接头',
    spaceResource: '空间资源',
    site: '局站',
    room: '机房',
    well: '人手井',
    edevice: '电设备',
    odevice: '光设备',
    ccp: '交接箱',
    dp: '分线盒',
    dt: '电接头',
    dpgroup: 'DP群',
    dreserve: '电缆预留',
    ecable: '电缆',
    idf: '综合配线架',
    outdooraddress: '安装点',
    accessarea: '综合接入区',
    greserve: '光缆预留',

    control: {
      label: '标注',
      resetLabelSetting: '重置标注设置',
    },
    relocate: {
      tips: '定位到这个位置？'
    },
    drawonmap: {
      title: '请确认',
      message: '在地图上画出对应类型的图形',
      native: '暂时不画',
      positive: '确认'
    },
    accesspoint: '接入点',
    childaccesspoint: '子接入点',
    message: {
      nodata: '您所定位的实体无坐标，请核查数据'
    },
    getCurrentPositionCoordinates:'获取当前位置坐标',
    currentPositionCoordinates:'当前位置坐标',
    more:'更多'
  },
  ////
  locateby: {
    longitude: '经度',
    latitude: '纬度',
    locate: '定位',
    inputAndLocate: '经纬度定位',
    longitude_info: '输入经度坐标...',
    latitude_info: '输入纬度坐标...',
  },
  enterdraw: {
    tip: '通过地图左上角的按钮切换绘制方法,点击刷新图标退出绘制模式.',
    start: '开始录入',
    stop: '停止',
    pause: '暂停',
    return: '回退'
  },
  bookmark: {
    bookmarkCurrent: '收藏当前地图范围'
  },
  layinput: {
    title: '边敷边建-管线系统(G5)',
    undoPrompt: '回退到上一步，请确认以下对象将被删除',
    addingNotMapType: '请直接在地图上选择设施之后，再进行录入！',
    noTemplate: '不使用模板',
    pipesectionTemplate: '管道段模板',
    hanglingsectionTemplate: '吊线段模板',
    commonlineTemplate: '关联线模板',
    restart: '重新开始',
    undoTip: '撤销上一步操作',
    selectSite: '请选择局站',
    selectProject: '请选择工程',
    resourceType: '资源类型',
    code: '编码',
    holesView: '截面',
    autoRouteSearch: '路由自动搜索',
    confirmRestoreFromTemp: '是否从临时保存的数据恢复?(恢复后将清除临时保存)',
    saveTemp: '临时保存',
    endlaying: '敷设完成',
    confirmOBDExtend: '是否扩容OBD？'
  },
  cablelaying: {
    title: '缆线敷设',
    table: {
      restype: '资源类型',
      name: '名称',
      code: '编码',
      holenumber: '孔号',
      operate: '操作'
    },
    button: {
      save: '保存敷设',
      perforation: '穿孔'
    },
    autoRouteSearch: '路由自动搜索',
    tips: '请在地图上点击设施点建立缆段路由，两个设施间的路由将进行自动计算。',
    properties: '查看属性',
    terminalpanel: '端子面板',
    savesuccess: '保存成功',
    insuccess: '移入成功',
  },
  organization: {
    title: '用户管理-管线系统(G5)',
    users: '用户管理',
    roles: '角色管理',
    groups: '参数组管理',
    modifyPwd: '设置新密码',
    currentPwdTip: '出于安全考虑，请输入当前登录用户的密码',
    currentPwd: '您的密码',
    newPwd: '新密码',
    newPwdRepeat: '重复新密码',
    passwordsNotSame: '两次输入的密码不相等',
    privilegesetting: '参数配置',
    privilegeTip: '用户或角色在进行数据查询等操作时将会受到指定的参数限制，请谨慎配置。',
    groupsetting: '参数组配置',
    resourcesetting: '权限配置',
    rolesetting: '角色配置',
    ipmanage: '黑白名单管理',
    addIP: '新增',
    modifyIP: '修改',
    IPFormatIncorrect: 'IP格式错误，请输入正确的IP地址格式，示例：***********',
    gridpermissionconfiguration: '网格权限配置',
    onlineUser: '在线用户'
  },
  pipesectionGraph: {
    title: '管道段截面图',
    free: '空闲',
    ocable: '光缆',
    ecable: '电缆',
    mixed: '光电',
    damaged: '损坏',
    ocablesection: '光缆段',
    ecablesection: '电缆段',
    drawHole: '绘制管孔',
    batchHoles: '批量管孔',
    savePosition: '保存位置',
    savePositionTip: '保存管孔位置时映射保存对端',
    addSubHole: '添加子孔',
    circle: '圆孔',
    subHoleNum: '孔数',
    rect: '方孔',
    SubHoleRow: '行数',
    SubHoleCol: '列数',
    switchHole: '换孔',
    alertWhenSwitchNoneHole: '请选择不同的管孔换孔',
    confirm: '确认',
    okbutton: '确定',
    showHopeModify: '修改管孔属性',
    showHopeAdd: '正在新增管孔',
    showHopeChangehole: '缆段换孔成功。',
    showHopeTip: '请先保存当前数据',
    export: '导出',

    roam: '漫游',
    currentShowFace: '当前显示方式:面号',
    currentShowCode: '当前显示方式:孔号',
    plzSelectAHole: '请选择一个管孔',
    plzSelectARightHole: '请选择正确的管孔',
    tubeOverTheBoundary: '管孔位置超出管群边界,无法添加',
    addTugeGroup: '添加管群',
    modifyTubeGroupAttr: '修改管群属性',
    resetSubHole: '重排子孔',
    savesuccess: "保存成功",
    plzSelectAData: '请选择一条数据',
    tubegroupTemplate: '管群模版查询',
  },

  airport: {
    title: '地图图层',
    resources: '业务图层',
    point: '点资源',
    line: '线资源',
    polygon: '面资源',
    site: '建筑物',
    room: '机房',
    gj: '光交接箱',
    well: '人手井',
    gt: '光缆接头',
    supportpoint: '支撑点',
    ocable: '光缆',
    ocablesection: '光缆段',
    pipesection: '管道段',
    safetysign: '安全标识',
    commonline: '关联线',
    pipe: '管道系统',
    spaceTree: '空间导航',
    layerControl: '图层控制',
    ductSystem: '管道系统',
    input: '资源录入',
    system: '系统管理',
    start: '功能菜单',
    map: '地图基础',
    locate: '地图定位',
    draw: '绘制测量',
    mapPrint: '地图打印',
    mapBookmark: '地址收藏',
    addressSearch: '地址查询',
    basics: '基础应用',
    layinput: '边建边敷',
    users: '用户管理',
    syslog: '操作日志',
    cache: '清除缓存',
    batchInput: '批量录入',
    moduleTemplate: '模板管理',
    version: '版本信息',
    report: '报表管理',
    report4company: '光缆使用单位',
    report4ocablesection: '光缆使用情况',
    supplycompany: '使用单位管理',
    maptheme: '专题图',
    maptheme4pipesection: '管道占用图',

  },
  netplanning: {
    accessarea: '综合业务区',
    site: '局站(机楼)',
    telebuild: '电信机楼',
    localbuild: '园区内机楼定位',
    accesssite: '接入局所',
    ocable: '光缆',
    relay_ocable: '中继光缆',
    mian_ocable: '主干光缆',
    wiring_ocable: '配线光缆',
    contact_ocable: '联络光缆',
    station_ocable: '局内光缆',
    longdistance_ocable: '长途光缆',
    building_ocable: '楼间光缆',
    main_devices: '主干光设施',
    gj: '光交接箱',
    gf: '光分纤箱',
    gt: '光缆接头',
    gw: '光网络箱',
    sub_devices: '配线光设施',
    add: '新增',
    modify: '修改',
    copy: '复制',
    remove: '删除',
    locate: '地图定位',
    imp: '导入',
    exp: '导出',
    expall: '导出全部',
    relocate: '重定位',
    reline: '缆段调整',
    change: '边界调整',
    locateNet: '光缆定位',
    merge: '接入区合并',
    search: {
      provance: '省份', prefectural: '地市', county: '区(县)', now: '现状', target: '目标', construct: '建设',
      buildtype: '机楼类型', buildcore: '核心机楼', buildhub: '枢纽机楼', buildnormal: '一般机楼', entdc: "入驻DC",
      regionsearch: '输入区域名称', loaclOnNav: '导航树定位',
    },
    link: {
      currentLink: '当前环节:',
      current: '资源现状',
      plan: '目标规划',
      construction: '建设需求',
      plzCloseWindow: '请先关闭目标规划面板',
      initLink: '当前环节:资源现状',
      selInfo: '请在地图上选择目标机房、设备数据进行录入,双击地图取消录入',
    },
    planmenu: {
      access: '接入区',
      site: '局站/机房',
      gj: '光交接箱',
      ocable: '光缆段',
      gf: '光分纤箱',
      gt: '光缆接头',
      stop: '停止录入',
    },
    application: {
      project: {
        confirmDel: '该规划项目下关联建设需求，是否确认删除？',
      },
      plan: {
        common: {
          lifeplancandem: '只有规划的才能建设需求',
          linkplancandem: '只有建设需求环节才可以操作',
          modifySuccess: '修改成功',
          queryError: '查询失败',
          modifySiteRoom: '修改 局站/机房建设需求',
          modifyOcable: '修改 光缆段建设需求',
          lifedemcammodify: '只有建设需求的才能修改',
          lifecurrentcantransfer: '只有现状的才可以改造',
          linkplandemcantransfer: '只有规划环节才可以改造',
          confirmZgBak: '终稿备份在备份时会复制出下一年的现状数据，请确认当年规划已全部完成，是否继续执行终稿备份？',
          confirmCopy: '数据操作完成，请确认数据修改信息',
        },
        site: {
          siteAttr: '局站属性',
          title: '新增 局站/机房规划',
          newRooms: '添加下属机房',
          ok: '确定',
          cancel: '取消',
          room: '下属机房',
          siteNeedRooms: '局站下必须有机房',
          validateError: '表格验证不通过，请检查',
          addsuccess: '添加成功',
          plangoal: '目标规划',
          exitplan: '退网方案',
        },
        ocable: {
          basicAttr: '基本属性',
          sameAzGrid: '同AZ端光缆段',
          newNet: '添加归属光缆',
          construction: '光缆段建设需求',
          chromatographic: '色谱管理',
          cablestructure: '光缆构造',
          fibertype: '成束类型',
          fibertotalnum: '纤芯总数',
          fibernum: '每束芯数',
          num: '束数',
          rownum: '每束行数',
          colnum: '每束列数',
          print: '打印',
          presplitname: '待拆分光缆段',
          splitnum: '拆分光缆段条数',
          perfiber: '纤芯数',
          persectioncode_per: '第',
          persectioncode_suf: '条光缆段',
          sectionsplit: '拆分',
          perfibernull: '纤芯数不能为空',
          perfibererror: '新纤芯数之和不等于总纤芯数',
          evennumber: '纤芯数须为偶数',
          cablesection_1: '光缆段1',
          cablesection_2: '光缆段2',
          retain: '保留',
          fusepoint: '熔接点',
          merge: '合并',
          viewfuse: '查看熔接',
          selectcable: '请选择光缆段进行合并',
          mergeerror: '操作失败，请检查数据',
          mergesuccess: '操作成功',
          inserttolist: '加入列表',
          ocablesectionname: '光缆段名称',
          ocablesectioncode: '光缆段编码',
          adevname: 'A端节点',
          zdevname: 'Z端节点',
          gtcode: '接头编码',
          cablenetcode: '光缆编码',
          cutpointtype: '割接点类型',
          cutpoint: '割接点',
          cablelength: '缆段长度',
          oldcablesection: '原光缆段',
          targetcable: '目标光缆段',
          oldcablenet: '原光缆',
          targetcablenet: '目标光缆',
          newlessthanold: '目标光缆段纤芯总数不能小于原所有光缆段纤芯数之和',
          sameaorz: '原光缆段必须具有相同的起始和终止端',
          havenotgt: '目标光缆段两端均不为接头',
          notsame: '目标光缆段两端与原光缆段无相同端',
          gtfacilitynull: "要熔接的接头所在支撑设施为空",
          premergeroute: "合并前的光缆段路由不完整",
          premergeaz: "合并前的光缆段两端信息不完整",
          samedevnotin: "合并前后的光缆段不相同端的设备不能位于同一个设施内!",
          splitnumlessthantotal: '拆分光缆段数不能大于总芯数除以2',
          gtnotfuse: '该接头内无纤芯进行熔接',
          straightGT: '选中接头不是直接头,不允许割接',
          currentfacility: '当前设施',
          targetfacility: '目标设施',
          cutoveroperation: '割接操作',
          cuttingscene: '割接场景',
          startCutting: '开始割接',
          currentlogfibergroup: '当前局向光纤组',
          targetlogfibergroup: '目标局向光纤组',
          reverse: '反向',
          cutover: '割接',
          cutpreview: '割接预览',
          message_1: '待割接和割接到的端子数量必须一致',
          message_2: '端子状态不为空闲和实占，不允许割接',
          message_3: '请选择待割接的端子！',
          message_4: '请选择目标端子！',
          message_5: '确认所对应的光缆段成端是否全部选中！',
          message_6: '目标端子必须为空闲且未成端！',
          message_7: 'OBD只能割接到OBD端子上',
          message_8: '割接前的OBD上所有有业务占用的端子要一起割',
          message_9: 'OBD上联口必须割接到上联口，下联口必须割接到下联口',
          message_10: '割接前的OBD上存在多条上联光路',
          message_11: '割接到的OBD上存在上联光路',
          fibermessage_1: '请选择待割接的纤芯！',
          fibermessage_2: '请选择目标纤芯！',
          fibermessage_3: '目标纤芯必须为空闲',
          fibermessage_4: '待割接和割接到的纤芯数量必须一致',
          fibermessage_5: '待割接和割接到的纤芯所属光缆段两端设施必须一致',
          fibermessage_6: '请选择光缆段！',
          newcablesection_1: '新光缆段1',
          newcablesection_2: '新光缆段2',
          selectsplitcable: "请选择要分割的光缆段",
          selectsplitgt: "请选择接头",
          newnamenull: "新光缆段名称编码不能为空",
          newcodesame: "新光缆段的编码不能相同",
          routeerror: "光缆段路由不完整，不允许进行割接！",
          passgt: "光缆段未经过接头所在支撑设施，不允许进行割接！",
          fibergroupmsg_1: "请选择待割接的局向光纤组",
          fibergroupmsg_2: "请选择目标局向光纤组",
          fibergroupmsg_3: '目标局向光纤必须为空闲',
          fibergroupmsg_4: '待割接和割接到的局向光纤数量必须一致',
        },
        labelprint: {
          basic: '常规',
          bandwidth: '带宽型（二维码）',
          belongCity: '所属地市',
          templatename: '模板名称',
          devicetype: '设备类型',
          belongdevice: '所属设备',
          belongOBD: 'OBD设备信息',
          belongoptical: '所属光路',
          belongmodule: '所属模块',
          belongroom: '所属机房',
          belongproject: '所属工程',
          incrementquery: '增量查询',
          queryOptical: '查询光路',
          clear: '清除',
          export: '导出',
          exportlabel: '导出标签',
          print: '打印',
          addtolist: "加入列表",
          roombelongs: "所属机房",
          regionbelongs: "所属区域",
          previewlabel: "预览",
          downport: "下联端口",
          City: '地市',
          districtCamp: '区县/营服',
          labelType: '类型格式',
          max_barcode: '当前条码已导出最大序列',
          barcode_num_city: '本市已导出条码总数量',
          binded_barcode_num_city: '本市已导出且已绑定总数量',
          barcode_num_country: '当前(区县/营服)已导出条码总数量',
          binded_barcode_num_country: '当前(区县/营服)已导出且已绑定总数量',
          cmbox_print_num: '数量(张/对)',
          txt_print_num_white: '单跳点_白色  数量(张/对)',
          txt_print_num_yellow: '多跳点_黄色  数量(张/对)',
          physicstatus: '物理状态',
          businessStatus: '业务状态',
          labeling: '打标',
          Type: '类型'
        },
        project: {
          inneeds: '移入建设需求',
          query: '查询',
          result: '查询结果',
          ok: '确认选择',
          validateError: '请填写必填项',
          plzSelectOnedata: '请至少选择一条数据',
          addsuccess: '添加成功',
          alertmsg1: ' 建设需求已经属于 ',
          alertmsg2: ' 规划项目,不允许移入',

          outneeds: '移出建设需求',
          delsuccess: '移除成功',
          delfail: '移除失败'
        }
      }


    },
    action: {
      releNet: '关联光缆段', view: '查看', viewMgt: '图片管理', viewAll: '浏览全部', viewList: '图片浏览', close: '关闭',
      frontFace: '正面', backFace: '背面', uploadtips: '请选择要上传的图片为正面或背面', viewPrjPro: '查看工程进度',
    },
    label: {
      resourcelabel: '资源图层', labeltar: '目标视图', labeldem: '建设视图', labelgaodeimage: '高德影像', labelgaodeplane: '高德平面',
      labelbuildstate: '机楼状态', labelpropertytype: '产权属性', labelisidc: 'IDC/DC标志', labeladdress: '通信地址', buildarea: '建筑面积(m2)',
      totalrack: '机架规模', canaddrack: '可扩展机架', cantotalrack: '可安装机架', dctype: 'DC类型', coop: '合作模式', park: '所属IDC园区',
      idclevel: 'IDC星级', tctime: '投产时间', allrack: '规划满配机架', buildrack: '已建机架', usedrack: '已用机架',
    },
    time: {
      beforeLastYear_num: '2017', lastYear_num: '2018', currentYear_num: '2019', nextYear_num: '2020', twoAfterYear_num: '2021', threeAfterYear_num: '2022', fourAfterYear_num: '2023', fiveAfterYear_num: '2024', sixAfterYear_num: '2025'
    }

  },
  bjdx: {
    common: {
      odevices: '光设施', presentop: '状态为实占的资源不允许操作'
    },
    map: {
      plann: '规划图层', usersite: '用户局站', govsite: '局用局站', accesssite: '接入点局站',
      considerResource: '可研', preoccupyResource: '预占', occupyResource: '实占', site: '局站',
      mapresource: '资源图层'
    },
    menu: {
      dynnamicTemp: '动力设备模板', deviceopera: '网元模板', typerack: '机架模板', linkregionsmgt: '关联区县管理', manuFactor: "生产厂家",
    },
    planorder: {
      ordernodel: '已归档的工单不允许删除', orderlinks: '工单下有关联的资源，不允许删除',
      stepPlan: '数据匹配成功，确认流转至规划状态吗', stepOnFile: '工单归档后，关联资源将转为实占，请确认',
      stepOk: '流转成功', noOutDateOrder: '未查询到超期或即将超期的工单', noOutDateResource: '未查询到超期或即将超期的资源'
    }
  },
  room3d: {
    cabinetEditingMode: '机柜编辑模式',
    importModel: '导入模型',
    exteriorWallEditing: '外墙编辑',
  },
  application: {
    common: {
      alert: {
        del: '删除',
        cancel: '取消',
        save: '保存',
        clear: '清空',
        cascadedel: '级联删除',
        delsuccess: '删除成功',
        plzselect: '请选择',
        plzconfirm: '请确认',
        locatesuccess: '落图成功',
      }
    },
    position: {
      nullname: '[空]',
      action: {
        list: '列表',
        refresh: '刷新',
      },
      green: {
        slectExistGreen: '选择地址中已存在绿标地址',
        slectExistNotGreen: '选择地址中已存在非绿标地址',
        plzSelectANotGreenData: '请至少选择一条非绿标数据',
        plzSelectAGreenData: '请至少选择一条绿标数据',
        parentIsNotGreen: '选中地址中存在父地址不是绿标地址的，请核查',
        childHasGreen: '选中地址中存在子地址是绿标地址的，请核查'
      },
      leatherCableMaintenance: {
        plzSelectAData: '请至少选择一条数据',
        plzSelectLevel10: '请选择10级地址',
        hasLeatherCable: '有皮缆',
        enterHourse: '已入户',
        title: '皮缆标识维护',
        unknownError: '未知错误',
        modifySuccess: '修改成功',
      },
      message: {
        searchplaceholder: '请输入地址关键字并回车',
        permissiondenied: '当前节点层级没有该权限操作',
        chooseWhetherdelete: '请选择是否级联删除',
        chooselocategraphic: '请选择需要落图的图形',
        choosedestinationaddr: '请选择目标地址',
        chooseMoveToAddr: '请选择需要移动的地址',
        chooseMergeToAddr: '请选择需要移动的地址',
        diffLevelDeniedOpt: '地址级别不同，不允许批量移动',
        chooseDiffAddress: '请选择不同的地址',
        configmsynonymaddress: '原地址是否作为目标地址的同义词存在',
        plzchooseadata: '请选择一条数据',
        plzchooseatleastdata: '请选择至少一条数据',
        nodataneeddel: '没有需要删除的数据',
        delExistMapMsg: '该地址存在落图,请确定是否一起删除?',
        delExistMapMsg: '该地址存在落图,请确定是否一起删除?',
        hasChild:'该地址下挂了子级地址，请删除子级地址后在进行该地址删除',
        hasConService:'该地址存在关联的开通业务，不允许删除地址',
        hasConBuilding:'该地址关联了楼宇，不允许删除'
      },
      alert: {
        delconfirmsplice1: '确定要',
        delconfirmsplice2: '选中的地址?',
        batchaddsplice1: '批量新增:当前新增等级 ',
        batchaddsplice2: '父地址',
        point: '点',
        line: '线',
        polygon: '面',
        //69515 TASK 【浙江】【国庆O3管线验证】移动地址描述优化
        moveaddrsplice1: '请选择要移动到本',
        moveaddrsplice2: '下的其他源地址',
        moveaddryes: '确定',
        moveaddroptsplice1: '不能小于3级',
        moveaddroptsplice2: '不能选择目标地址',
        moveaddroptsplice3: '的父地址',
        moveaddroptsplice4: '不能小于5级',
        moveaddroptsplice5: '目标地址的父级地址级别与需移动地址的地址级别不一致,请重新选择',
        moveaddroptsplice6: '目标地址的地址级别与待合并地址的地址级别不一致,请重新选择',
        mergeaddrsplice1: '请选择要合并到',
        mergeaddrsplice2: '的地址',
      },
      window: {
        devicecoveradd: {
          devicecovertitle: '正在添加 设备覆盖',
          portcovertitle: '正在添加 端子覆盖',
          assemblename: '地址拼装名称',
          devicename: '设备名称',
          devicecode: '设备编码',
          devicetype: '设备类型',
          query: '查询',
          devicecover: '设备覆盖',
          portcover: '端子覆盖',
          save: '保存',
          validatemessage1: '设备类型必填,设备名称和编码选填一个',
          validatemessage2: '请选择端子数据',
          addsuccess: '添加成功',


        },
        batchadd: {
          prefixname: '前置名称',
          intervalsetting: '连续区间设置',
          zimubig: '字母(大写)',
          zimusmall: '字母(小写)',
          numbig: '数字(大写)',
          numsmall: '数字(小写)',
          beginnum: '起始序号',
          endnum: '终止序号',
          createnum: '生成号码',
          allnum: '全号',
          oddnum: '单号',
          evennum: '双号',
          suffixname: '后置名称',
          cp_shape: '继承地图对象',
          cp_cover: '继承地址覆盖',
          cp_site: '继承地址局向',
          cp_zipcode: '继承邮政编码',


          beginnumrequired: '起始序号必填',
          endnumrequired: '终止序号必填',
          intervalsettingrequired: '连续区间设置必选一个',
          err_upper_text: '起始序号或终止序号的格式不对,应为A-Z',
          err_lower_text: '起始序号或终止序号的格式不对,应为a-z',
          err_num: '起始序号或终止序号的格式不对,应为阿拉伯数字,并且小于五位数',
          err_begin_end: '起始序号要小于终止序号',
        },
        export: {
          exportPostion: '导出 标准地址',
          exportThisLevel: '导出本级及下级地址',
          exportNoDP: '导出无下挂DP的地址',
          exportFacility: '导出地址关联设施',
          exportOBD: '导出关联OBD及DP信息',
          exportAll: '导出所有地址及关联设施',
          defaultText: '请选择导出的类型',
          ok: '确定导出',
          export: '导出',
        }
      }
    },
    smallunit: {
      message: {
        searchplaceholder: '请输入网格关键字并回车'
      }
    },
    device: {
      window: {
        devicemgtCU: {
          addonutitle: '正在添加 ONU设备',
          addalttitle: '正在添加 OLT设备',
          editonutitle: '正在修改 ONU设备',
          editolttitle: '正在修改 OLT设备',
          addsvlantitle: '正在添加 PVLAN',
          addcvlantitle: '正在添加 CVLAN',

          basicattr: '基本属性',
          configurationInfo: '配置信息',
          type: '型号',
          spec: '规格',
          option: '操作',
          thislevel: '本级(只做显示用)',
          relateDeviceTemplate: '关联设备模版',
          relateFrameTemplate: '添加机框模版',
          del: '删除',
          save: '保存',
          cancel: '取消',
          templatename: '模版名称',
          query: '查询',
          associate: '正在关联',
          associatesub: '正在关联下属模版',
          addChildNode: '增加子节点',

          templatenamerequired: '请先输入模版名称查询',
          plzselectonedate: '请选择一条数据',
          confirmDel: '请确认是否要删除所选中的节点?该操作不可回退。',
          plzConfirm: '请确定',
          confitm: '确定',

          devicetemp: '设备模版',
          racktemp: '机架模版',
          frametemp: '机框模版',
          slottemp: '槽道模版',
          cardtemp: '板卡模版',
          porttemp: '端口模版',
          oltname: 'OLT名称',
          obdname: 'OBD名称',
          onuname: 'ONU名称',
          odfname: 'ODF名称',
          oppositename: '对端名称',
          framename: '机框名称',
          slotname: '槽道名称',
          cardname: '板卡名称',
          childcardname: '子板卡名称',
          modulename: '模块名称',
          devicename: '设备名称',
          oltnull: 'OLT名称为空',
          oltportnull: 'OLT端口为空',
          obdnull: 'OBD名称为空',
          obdportnull: 'OBD端口为空',
          oppositenull: '对端名称为空',
          oppositeportnull: '对端端口为空',
          portisusing: '端口非空闲',
          selectoltport: '请选择一条OLT端口记录',
          selectobdport: '请选择一条OBD端口记录',
          selectodfport: '请选择一条ODF端口记录',
          selectoppositeport: '请选择一条对端端口记录',

          pvlanstart: '起始PVLAN',
          pvlanend: '终止PVLAN',
          cvlanstart: '起始CVLAN',
          cvlanend: '终止CVLAN',
          popbelong: '所属POP点',
          pvlanbelong: '所属PVLAN',
          network: '应用网络',
          businesswork: '业务用途',
          oltbelong: '所属OLT',
          customerwork: '客户类型',
          comment: '备注',
          startnumnotnull: '起始不能为空',
          endnumnotnull: '终止不能为空',
          endlessthanstart: '起始不能大于终止',

          port: '端子',
          STANDARDRACK: '机架',
          FRAME: '机框',
          SLOT: '槽道',
          CARD: '板卡',
          portPrefix: '端子前缀',
          beginPort: '起始端子',
          associated: '建立关联',
          boundAssociated: '已绑定关联',
          moveIn: '移入',
          moveOut: '移出',
          selfDevice: '本端设备',
          selectselfport: '请选择一条本端端口记录',
          selfnamenull: '本端名称为空',
          oppositeDevice: '对端设备',
          oppositeModule: '对端模块'

        }
      }
    },
    optic: {
      projectsuccess: '工程关联成功，已生成光路：',
      createOpticOnDo: '正在生成，请稍候···',
      hasjumplink: '该设备端子已有跳接'
    },
    attachdevices: {
      plzselectonu: '请选择一个或多个ONU',
    },
    sitefiber: {
      title: '局向光纤管理',
      nocablesection: '此光缆下没有光缆段',
      nofiber: '此光缆下没有纤芯',
      nositefiber: '此光缆下没有局向光纤',
    },
    project: {
      currentname: '当前工程',
      exitcurrentproject: '请先退出当前资源录入的工程',
      cannotaudit: '该工程已归档，不能提交审核',
      projectstatistics: '资源统计',
      cannotinput: '该工程已归档，不能录入资源',
      needMapLocate: '该工程需要先落图,才能录入资源',
    },
    order: {
      currentname: '当前工单',
      exitcurrentproject: '请先退出当前资源录入的工单',
      cannotaudit: '该工单已归档，不能提交审核',
      projectstatistics: '资源统计',
      cannotinput: '该工单已归档，不能录入资源',
    },
    secview: {
      confirmDelSelHole: '确定要删除选中的管孔么?',
      confirm_str_1: '确定要将',
      fromhole: ' 从管孔',
      tohole: ' 更换到管孔'
    },
    expandview: {
      cablelist: '缆线列表',
      expandview: '展开图',
    },
    selectposition: {
      layercontrol: '图层控制',
      accesscapability: '接入能力',
      setting: '设置',
      amap: '高德',
      confirmtext: '确认搜索该位置周边光设备?',
      yes: '确认',
      cancel: '取消',
      alert: {
        nodevicefound: '未找到附近光设施',
        errormsg: '查询失败，请稍候再试',
        title: '提示',
        nocoverdevice: '该地址下无覆盖设备',
        noaddressfound: '未找到相关地址'
      },
      table: {
        name: '名称',
        type: '类型',
        totalportnum: '总端口数',
        unusedportnum: '空闲端口数',
        addressnum: '覆盖地址数',
        addressfullname: '地址全称',
        addresslevel: '级别',
        addressopt: '操作',
        select: '选择',
        fullname: '地址全称',
        level: '级别',
        devicenum: '覆盖设备数',
      },
      menu: {
        title: '功能菜单',
        locate: '地图定位',
        queryradius: '查询半径',
        selectmodel: '选择模式',
        device: '设备',
        standardAddress: '标准地址',
        coverAddress: '覆盖地址',
      },
      searchoninternet: '互联网选址',
      searchbystandardaddress: '标准地址',
      searchplaceholder: '请输入地址...',
      addressList: '覆盖地址详情',
      text: {
        businessType: '业务类型',
        installCapability: '安装能力',
        bandwidth: '带宽',
        pon: 'PON类型',
        siteDirection: '局向',
        currentValue: '宽测值',
        theoryValue: '理论值',
        tel: '电话业务',
        dsl: '宽带业务',
        selSiteDirection: '选择局向',
        addressType: '地址级别',
        fullname: '地址名称',
        relateSearch: '相关搜索',
        query: '查询',
        msg: '备注:安装能力和带宽值是线路加端口的全程能力',
        mapSelect: '地图选址',
        accesscode: '接入号',
        accesscodenoaddress: '该接入号不存在地址',
        textSelect: '文本选址',
        accessCodeSelect: '接入号选址',
        allports: '总端子数',
        idleports: '空闲端子数',
        useRate: '端子利用率',
      }
    },
    lifecontrol: {
      theexistingnetwork: '现网',
      design: '设计',
      construction: '施工',
      completion: '竣工'
    },
    batchInput: {
      info: '请在左下角选择局站，设置自动编码规则, 然后选择您要新建的资源类型，并在地图上对应的位置点击开始绘制，绘制完成后在预览表格中进行调整，确认后进行提交。(按住ctrl点击地图进行强制新增)',
      noTemp: '不使用模板',
      autoCode: '自动编码',
      codepre: '前缀',
      startsn: '序号',
      codesuf: '后缀',
      codestep: '步进数,编码中的序号将从起始序号递增,可为负数',
      startsnBtn: '查询起始序号',
      pipesectionTemp: '管道段模板',
      hangLineTemp: '吊线段模板',
      commonlineTemp: '关联线模板',
      confirmAndSubmit: '确认并批量提交',
      sureAddData: '确定批量新增数据么?',
      templatename: '模板名称',

      messgae1: '请选择录入类型并输入前缀',
      plzSelectSite: '请选择局站',
      plzSelectRoom: '请选择机房',

      addSuccess: '新增成功',

      content1: '更改为当前选择的类型',
      content2: '或选择附近的已有设施',
      search: '立即搜索',
      searchNull: '搜索结果为空',

      resourceType: '资源类型',
      code: '编码',
      tempName: '模板',
      edit: '编辑',
      creating: '正在创建',
      modify: '更改',
      delete: '删除',
      quitinput: '结束录入',
    },
    gridarea: {
      title: '网格导航树',
      zhijuTreeTitle: '装维区域导航树',
      action: {
        list: '下属列表',
        mergeZhiju: '支局合并',
      },
      select: '选择',
      in: '移入',
      out: '移除',
      exp: '导出',

      noFilterDevice: '不存在过滤关系设备',
      filterDevice: '存在过滤关系的设备',
      confirmRemoveFilter: '确定要解除网格单元与设备过滤关联吗？解除后设备和网格单元直接建立关联关系!',
      optSuccess: '操作成功',
      plzSelectData: '请至少选择一条数据',
      mergeSmallunit: '合并网格',
      fromSmallunit: '目标网格:',
      plzSelectMerge: '请先选择待合并的网格',
      moveSmallunit: '网格单元归属支局调整',
      fromZhiju: '目标支局:',
      plzSelectFromZhiju: '请先选择待合并的支局',
      plzSelectToZhiju: '请选择目标支局',
      queryDeviceHeader: '关联设备',
      coverDeviceTableName: '存在覆盖地址设备',
      noCoverDeviceTableName: '不存在覆盖地址设备',
      checkRelationHeader: '网格关联检查:未关联的设备',
      autoRelation: '一键入网格',
      autoRelationSuccess: '恭喜你,遗漏设备关联网格单元成功',

      gridDeviceMgtHeader: '网格设备管理',
      dp: '分线盒',
      gf: '光分纤盒',
      zhx: '综合配线箱',
      gb: '光终端盒',
      obd: '分光器',
      refresh: '刷新',
      autoConfig: '自动配置设备关联',
      save: '保存',
      removeConfirmPart1: '你是否需要将移出的设备和网格单元【',
      removeConfirmPart2: '】建立过滤关系?建立过滤关系后设备再入网格就需要必须要手动维护',

      plzDrawAfterCreate: '网格单元属性保存成功，请绘制该网格单元的图形范围',
      continueDraw: '是否在同一网格单元下继续新增图形？',
      tips: '提示',
      plzSelectReject: '请选择-是否要抢占以下网格内实体',
      nolatlng: '该对象无坐标',

      onlyUsedTree: '是否只用于网格单元导航树？',
      removeDrawConfirmText: '你确定要删除该网格单元图形吗?删除后网格单元和设备关联关系一并删除',
      removeRelationConfirmText: '该操作将会删除网格与设备关联关系、网格图形和网格本身，是否确认删除',

      plzSelectGrid: '请选择网格单元',
      plzSelectAdata: '请选择一条数据',
      viewGroupCode: '查看集团编码',
      higgdancertips: '请选择新增的网格数据!!',
      deletehiggdancertips: '请选择需要删除的高危网格!!',
      deledatas: '请选择要删除的数据！！！',
      inputparams: '请至少输入一个查询条件',
      gridUpdateSuccess: '更新成功',
      gridUpdateFail: '更新失败',
    },
    sitedirection: {
      deletedata: '是否删除局向数据',
      viewdata: '请选择需要查看的局向！！',
      viewmoredata: '请勿选择多条局向数据!!',

    },
    templateMgt: {
      types: {
        card: '板卡模版',
        port: '端子',
      },
      cardOpt: {
        addTitle: '新增 板卡模板',
        modifyTitle: '修改 板卡模板',
        plzSelectData: '请选择一条数据',
        plzConfirm: '请确认',
        confirmContent: '此操作不可以回退,请确认是否删除?',
        success: '操作成功',
        failed: '操作失败',
        validateFailure: '基本属性验证不通过',
      },
      btns: {
        add: '新增',
        modify: '修改',
        del: '删除',
      },
      commonApp: {
        browseFile: '附件浏览',
        uploadingtip: '正在上传附件中，请耐心等候',
        modifyVendorMsg: '该厂商存在设备引用,是否继续修改',
        plzConfirm: '请确认',

      }
    },
    assetCard: {
      title: '资产卡片',
      notFoundAssetCard: '未找到资产卡片'
    },
    abilityAndBusiness: {
      title: '能力和业务信息',
      ocablePhysicalInfo: '光缆段物理信息',
      ocableBusinessInfo: '光缆段业务信息',

      abilityInfo: '能力信息',
      select: '选择',
      ocableNodeInfo: '光缆段成端信息',
      opticalInfo: '光路信息',
      queryBusiness: '查看业务',
      export:'导出全部',
      ecableInfo: '电路信息',

      cableNotFoundSection: '该缆段下不存在光缆段',
      plzSelectAData: '请选择一条数据'
    },
    aliasAuditMgt: {
      title: '同义词审核',

      queryCondition: '查询条件',
      auditOk: '审核通过',
      auditCancel: '审核不通过',

      fullnameAndAliasAtLeastOne: '完整名称和同义词名称至少输入一个',
      plzSelectAData: '请至少选择一条数据',
      auditOkConfirm: '是否确认审核通过当期选择的所有同义词?(批量审核时会自动跳过已审核通过的地址)',
      auditOkSuccessMsg: '所有选中的地址已审核完成通过',
      auditCancelConfirm: '是否确认审核不通过当期选择的所有同义词?(批量审核时会自动跳过已审核通过的地址)',
      auditCancelSuccessMsg: '所有选中的地址已审核完成不通过',

    },
    positionAuditMgt: {
      title: '标准地址审核',

      queryCondition: '查询条件',
      auditOk: '审核通过',
      auditCancel: '审核不通过',
      export: '导出',

      beginTimeLessThanEndTime: '起始时间要小于终止时间',
      nameRequired: '名称必填',
      plzSelectAData: '请选择一条数据',
      reason: '原因',
      nameIsNotStandard: '命名不规范',
      levelIsNotRight: '地址级别不正确',
      AddressDuplication: '地址重复',
      otherReason: '其他(请填写具体原因)',
      specificReason: '具体原因',
      auditCancelReason: '审核不通过原因',
      plzInputSpecificReason: '请输入具体原因',

    },
    guidedBatchAddPosition: {
      title: '向导式批量新增',

      addressType: '地址类型',
      view: '预览',
      positionIsNotFound: '该地址不存在，请核查',
      attrTitle: '向导式新增属性界面',
      isAddingState: '[正在新增]',

      currentAddLevel: '当前新增级别:${currentAddLevel}级',
      saveAddLevel: '保存并新增${saveLevel}级地址',
      save: '保存',
      unknownError: '发生未知错误'

    },
    measureLine: {
      pipesection: '管道段',
      hanglingsection: '吊线段',
      ocablesection: '光缆段',
      ecablesection: '电缆段',

      multiAdd: '多边形添加',
      mapAdd: '从地图添加',
      clear: '清除',
      clearAll: '清除所有',
      mapTotalLength: '地图总长度:',
      actualTotalLength: '米,实际总长度:',
      meter: '米',
      stopMapAdd: '停止地图添加',
      stopMultiAdd: '停止多边形添加',
      plzSelectAData: '请至少选择一条数据',
      areaNotSelectResource: '划定的区域中没有所选定的资源'
    },
    ocableApplication: {
      exportRoute: {
        confirm: '确定是否导出路由?',
        ocablecode: '所属光缆',
        ocablesectioncode: '所属光缆段',
        routecode: '路由编码',
        routetype: '路由类型',
        beginDeviceCode: '起始设备编码',
        beginDeviceType: '起始设备类型',
        beginFacilityCode: '起始设施编码',
        beginFacilityType: '起始设施类型',
        endDeviceCode: '终止设备编码',
        endDeviceType: '终止设备类型',
        endFacilityCode: '终止设施编码',
        endFacilityType: '终止设施类型',
        routeInfo: '路由信息',
      },
      showOCableDevice: {
        title: '光缆下相关设备',
        treeTitle: '光缆【${title}】下相关设备',
        gt: '光缆接头',
        yuliu: '预留设备',
        panliu: '盘留设备',
        otherdevice: '交接设备',
        devicename: '设备名称',
        devicecode: '设备编码',
        devicetype: '设备种类',
      },
      showOCableFacility: {
        title: '光缆下相关设施',
        treeTitle: '光缆【${title}】下相关设施',
        supportpoint: '撑点',
        well: '人(手)井',
        pole: '电杆',
        drawingpoint: '引上点',
        pipesection: '管道段',
        hanglingsection: '吊线段',
        commonline: '关联线',
        exportFile: '光缆相关设施',
        facilityCode: '设施编码',
        facilityType: '设施种类',
        facilityName: '设施名称',
        longitude: '经度',
        latitude: '纬度'
      },
      viewOCableDistribute: {
        title: '光缆分布'
      }
    },
    supportApplication: {
      tips: '提示',
      plzSelectSupportType: '请选择插入类型',
      supportpoint: '支撑点',
      pole: '电杆',
      plzSelectPipeline: '请选择管线段',
      plzSelectOnePipeline: '请选择一个支撑段',
      PipeLineLength: '支撑段距离',
      distanceToA: '到A端距离：',
      distanceToZ: '到Z端距离：',
      confirm: '确定',
      dblClickSelFac: '双击选择支撑设施',
      dblClickSelLine: '双击选择支撑段',
      FacilityNotFound: '未找到支撑设施,请重新选择',
      loadingmsg: '后台处理中，请稍后...',
      loadFaild: '处理失败，请重试',
      plzSelectSupport: '请选择支撑网设施',
      pipeLineNotTwo: '该设施连接的支撑段不是2个，不允许合并',
      pipeLineNotPoleWellSupportpoint: '支撑设施不是人手井、支撑点、电杆！',
      dblNotComHangPipe: '支撑段不是关联线、吊线段、管道段！',
      InconsistentSupportSegmentTypes: '支撑段类型不一致！',
      pipeLineA: 'A端支撑段：',
      pipeLineZ: 'Z端支撑段：',
      retain: '保留',
      plzSelectRetain: '选择保留段'

    },
    siteApplication: {
      siteCodeCutOver: '局站编码割接',
      siteMerger: '局站合并',
      selSite: '选择局站',
      sourceSite: '源局站',
      targetSite: '目标局站',
      remove: '移除',
      confirm: '确定',
      cancel: '取消',
      siteSelected: '该局站已选择',
      tip: '提示',
      modifySuccess: '修改成功',
      notice:'不同区的局站无法合并',
      alreadyInTarget: '该局站已在目标局站',
      alreadyInSource: '该局站已在源局站请先移除',
      onlySelectOne: '只能选择一个目标局站',
      sourceCode: '原编码：',
      newCode: '新编码：',
      plzInputNewCode: '请输入新编码',
      type: '类型',
      search: '查找',
      allFacilities: "选中该类型所有设施",
      addTypes: '所有类型',
      alreadySelectAll: "您已选择该局站下的全部类型设备",
      name: '名称',
      jumpPage: '跳转到：',
      toRight: '右移',
      transfer: '确认割接',
      outOfPageRange: '超出总页数',
      plzSelectFacilities: '请选择设备再进行迁移',
      plzSelectTargetSite: '请选择目标局站',
      plzSelectSite: '请选择局站',
      plzSelectType: '请选择类型',
      plzSelectSourceSite: '请选择源局站',
      targetAndSourceIsSame: '源局站与目标局站一致',
      code: '编码',
      alreadySelected: '已全选',
      pagingText: '当前页：${currPage}/${totalPage}    记录数：${total}',
    },
    datacheck: {
      title: '数据检查',
      widget: {
        cableNodeCheck: '缆线成端检查',
        ecableResourceCheck: '电缆资源检查',
        cableCompleteCheck: '缆线路由完整性检查',
        devicePortCheck: '设备端子检查',
        consistencyCheck: '物理纤芯和局向光纤的一致性检查',
        oppositeEndCheck: '纤芯对端检查',
        singleCheck: '查询孤立实体',
      },
      common: {
        //divider or title
        checkType: '检查类型',
        queryCondition: '查询条件',
        queryResult: '查询结果',
        checkResult: '检查结果',
        routeDetail: '路径详情',
        portList: '端子列表',
        cableList: '缆线列表',

        //button or label:
        query: '查询',
        check: '检查',
        select: '选择',
        conditionQuery: '条件检索',
        clear: '清除',
        consistencyCheck: '一致性检查',
        type: '类型',
        detail: '详情',
        regionName: '区域名称',
        childRegionName: '子区域名称',
        siteCode: '局站编码',
        deviceCode: '设备编码',
        device: '设备',
        module: '模块',

        //alert
        plzSelectAData: '请选择一条数据',
        plzSelectAtLeastAData: '请至少选择一条数据',
        plzInputACondition: '请至少输入一个条件',
        noFaultAfterCheck: '经检查,所选择的数据无异常',
        deviceRequired: '设备必填',
        typeRequired: '请选择实体类型',
        childRegionRequired: '请选择子区域',
        plzSelectCorrectEntityData: '请选择正确实体数据',
      }
    },
    lineResourceQuery: {
      title: '线路资源查询',
      ONUQueryOptic: 'ONU光路查询',
      OLTQueryOptic: 'OLT光路查询',
      OBDportChecked: 'OBD端口查询',
      OltOnuQueryOptic:'OLT光路查询',
      OpticalOLTRelayONU: 'OLT-ONU光路查询',
      lineResourceQuery: '线路清单查询',

      deviceSelect: '检查设备',
      query: '查询',
      attr: '详细属性',
      moveIn:'移入',
      shiftOut:'移出',
      empty:'清空',
      queryResult: '查询结果',

      ecable: '电缆',
      dp: '分线盒',
      ccp: '交接箱',
      switch: '交换机',
      dslamdevice: 'DSLAM设备',
      accesscode: '接入号',

      plzSelectAtLeastAData: '请至少选择一条数据',
    },
    opticalResourceQuery: {
      title: '光资源查询',
      entityResource: {
        title: '实体资源查询',
        ODFCODE: 'ODF编码',
        ODFNAME: 'ODF名称',
        GJCODE: '光交接箱编码',
        GJNAME: '光交接箱名称',
        OBDCODE: '分光器编码',
        OBDNAME: '分光器名称',
        OTHERCODE: '其他设备编码',
        OTHERNAME: '其他设备名称'
      },
      ocableResource: {
        title: '缆线资源查询',
        OCABLECODE: '光缆编码',
        OCABLENAME: '光缆名称',
        WELLCODE: '人手井编码',
        WELLNAME: '人手井名称',
        POLECODE: '电杆编码',
        POLENAME: '电杆名称'
      },
      opticalResource: {
        title: '光路资源查询',
        OPTICALCODE: '光路编码',
        OPTICALENAME: '光路名称',
        CIRCUITQUERY: '查询电路',
        FTTHCUSTOMER: 'FTTH客户资料',
        FUZZYQUERY: '模糊查询',
        CIRCUITCODE: '电路编号',
        OPTICALQUERY: '查询光路',
        ORDERCODE: '订单编号',
        ORI_ORDERCODE: '原光路编码'
      }


    },
    building: {
      addBuildingAddress: {
        title: '新增政企楼宇地址',
        coverType: '覆盖类型',
        addressType: '地址种类',
        levelAddress1: '一级地址',
        levelAddress2: '二级地址',
        levelAddress3: '三级地址',
        levelAddress4: '四级地址',
        levelAddress5: '五级地址',
        levelAddress6: '六级地址',
        levelAddress7: '七级地址',
        levelAddress8: '八级地址',
        levelAddress9: '九级地址',
        levelAddress10: '十级地址',
        levelAddress11: '十一级地址',
        buttonConfirm: '确定',
        buttonCancel: '取消'
      },
      addJQcode: {
        title: '选择局代码组合',
        title2: '已关联局向',
        sitedirectionCode: '局向编码',
        sitedirectionName: '局向名称',
        search: '查询',
        buttonSure: '确定',
        buttonCancel: '取消',
        alert: '请输入编码或名称'
      }
    },
    PONEquipmentCut: {
      originalOBD: '原OBD',
      newOBD: '目标OBD',
      routingNumber: '路由序号',
      Number: '序号',
      opticName: '光路名称',
      viewOptic: '查看光路',
      opticCode: '光路编码',
      routingType: '路由类型',
      ATerminalDeviceCode: 'A端设备编码',
      terminalNumberA: 'A端端子号',
      ZTerminalDeviceCode: 'Z端设备编码',
      terminalNumberZ: 'Z端端子号',
      confirm: '确定',
      cancel: '取消',
      tip: '提示',
      remove: '移除',
      analysis: '分析',
      preView: '割接预览',
      cut: '割接',
      selectOptic: '选择光路',
      upPort: '上联端口',
      upLink: '上联光路：',
      newUpLink: '新上联光路：',
      downPort: '下联端口',
      downLink: '下联光路：',
      newOpticLink: '新光路：',
      newDownLink: '新下联光路：',
      afterCut: '割接之后生成',
      modifySuccess: '修改成功',
      plzSelectOBDDevice: '请选择OBD设备',
      plzSelectPort: '请选择端口',
      plzSelectJumpLink: '请选择跳接',
      onlyOneJumpLink: '只能选择一个跳接',
      PortIsUsed: '设备有端口被占用,不允许增加',
      plzSelectOptic: '请选择光路',
      originalDevSameAsTarget: '原设备与目标设备相同,不允许更换',
      originalDevHasNoOptic: '原设备没有光路,不允许更换',
      portNotEnough: '目标设备端口不足,不允许更换',
      portIsUsing: '目标设备端口被占用,不允许更换',
      hasPort: '存在成端，不允许移除',
      downPortNoOptic: '下联端口没有光路，不允许移除',
      upPortNoOptic: '上联口没有光路，不允许移除',
      downPortOpticNotOne: '下联端口存在多条光路，不允许移除',
      downPortOnlyConnectport: '下联光路跳接对端都是尾纤端子，不允许移除',
      downOpticNotOnlyJumpLink: '下联光路不止有跳纤，不允许移除',
      plzSelectDevice: '请选择OBD设备'
    },
    transmissionSystem: {
      title: {
        applicationTitle: "网络安全分析",
        safeAnaly: "安全隐患分析",
        backfill: "人工整治回填",
        transmissionDiagram: "传输环路图",
        backfillHistory: "人工整治回填历史记录",
        historyResult: "历史详情",
        transmissionCheckResult: "传输环检查结果"
      },
      field: {
        analySurvey: "分析概况",
        opticComplete: "光路完整性",
        testResult: "传输环检测结果",
        basicAttr: "基础信息",
        screenNum: "甄别次数:",
        creator: "维护人:",
        maintanceDepartment: "维护部门:",
        systemScreenResult: "系统甄别结果",
        sameCable: "同缆",
        sameRoute: "同沟",
        opticIsnotComlete: "光路不完整",
        lostOpticlink: "传输段缺失光路",
        other: "其他",
        systemScreenDetail: "系统甄别详情",
        thisResult: "本次整治结果",
        safe: "安全",
        danger: "存在隐患",

        name: "名称",
        opticCode: '光路编码',
        color: "颜色",
        option: "操作",
        show: "演示",
      },
      buttons: {
        save: "保存",
        showHistory: "查看历史记录",
        mapLocate: "地图定位",
        transmissionRingDetection: "传输环检测",
        falseRingScreen: "假环甄别",
        exportTextRoute: "导出文本路由",
        showDetail: "查看详情",
        export: "导出",
        queryroute: "查看路由",
      },
      tips: {
        plzSelectDanger: "请选择整治后存在的隐患",
        plzFillQueryCondition: "请输入查询条件",
        plzSelectAData: "请选择一条数据",
        linkLessThan2: "该传输环的光路少于两条,无需进行甄别",
        plzSelectThisResult: "请选择本次整治的结果",
        plzSelectStillExistDanger: '请选择还存在的隐患',
        transmissionNoData: "该传输系统没有光路,无法进行检测!",
        transmissionNoError: "没有检测到错误!",
        transmissionCheckFail: "检查失败",
      },
      message: {
        outDistance: "出局同沟",
        meter: "米",
      }
    },
    sameRouteAnalysis: {
      buttons: {
        moveInCable: "移入光缆",
        moveOutCable: "移除光缆",
        moveInOpticlink: "移入光路",
        moveOutOpticlink: "移除光路",
        viewroute: "查看路由",
        opticlinkMapLocate: "光路定位",
        opticGroupLocate:"光路组定位",
        compareAnalysis: "比对分析",
        ocableMapLocate: "光缆定位",
        exportXls: "导出EXCEL",
//        facilityLocate: "设施定位",
//        deviceLocate: "设备定位",
//        pipelineLocate: "支撑段定位",
//        ocableLocate: "光缆定位",
//        ocablesectionLocate: "光缆段定位",
        exportModel: "导出模板",
        exportTable:"传统表格导出",
        importOpticPath: "导入光路",
        mapLocate: "定位",
        introduceLocation:"引入第三方辅助数据定位",
        opticCorrectionAdjustment: "光路优化调整",
      },
      title: {
        routeList: "路由列表",
        cableList: "光缆列表",
        cablesectionList: "光缆段列表",
        opticlinkQuery: "光路查询",
        ocableQuery: "光缆查询",
        facilityList: "设施列表",
        deviceList: "设备列表",
        treeCableSections:'光路/光缆段树形展示',
        opticPipeLineTrees:'光路/支撑段树形展示',
      },
      tips: {
        plzSelectAData: "请至少选择一条数据",
        plzSelectAtLeastTwoData: "请至少选择两条数据",
        plzSelectfacilityData: "请至少选择一条设施数据",
        plzSelectdeviceData: "请至少选择一条设备数据",
        plzSelectpipelineData: "请至少选择一条支撑段",
        plzSelectOCableData: "请至少选择一条光缆",
        plzSelectOCableSectionData: "请至少选择一条光缆段",
        plzSelectOpticalcircuitData:'请至少选择一条光路',
      }
    },
    fttxSolution: {
      solutionName: "方案名称：",
      solutionCode: "方案编码：",
      startDevice: "起始设备：",
      deviceType: "设备类型：",
      belongProject: "所属工程：",
      solutionStatus: "方案状态：",
      preEntry: "预录入",
      completed: "已完工",
      pointToPoint: "点对点",
      welding: "熔接",
      digOut: "掏接",
      wizardInput: "向导式录入",
      addfttxSolution: "添加方案",
      modifyfttxSolution: "修改方案",
      enterToSearch: "机房(安装点)编码回车查询",
      tip: "提示",
      belongTemplate: "所属模板：",
      model: "规格型号：",
      facilityStatus: "设施状态：",
      manuFactor: "生产厂家：",
      longLocal: "长本属性：",
      smallCountry: "划小区县：",
      propertyType: "产权性质：",
      marketingArea: "营销区：",
      serviceArea: "营业区：",
      mntPerson: "维护人：",
      mntWay: "维护方式：",
      mntDept: "维护单位：",
      propertyOwner: "产权归属：",
      projectStatus: "工程状态：",
      projectCode: "工程编码：",
      propertyTitle: "光缆公共属性(接头为起始设备时,不再生成光缆,用接头所属光缆!)",
      site: "所属局站：",
      gtType: "接头类型：",
      buttons: {
        query: "查询",
        nextStep: "下一步",
        prevStep: "上一步",
        add: "新增",
        modify: "修改",
        clear: "清空",
        reset: "重置",
        delete: "删除",
        batchAdd: "批量添加安装点",
        addOutDoor: "添加安装点",
        confirm: "确定",
        cancel: "取消",
        save: "保存",
        close: "关闭",
        entryProject: "进入工程",
        refresh: "刷新",
        property: "属性",
        portBord: "端子面板",
        fuseBord: "熔接面板",
        removeDevice: "删除设备",
        removeOcable: "删除光缆",
        laying: "敷设",
        removeOcableSection: "删除光缆段",
        dropMap: "资源落图",
      },
      tableHead: {
        projectCode: "工程编码",
        solutionCode: "方案编码",
        projectName: "工程名称",
        solutionName: "方案名称",
        startDeviceType: "起始设施类型",
        startDeviceCode: "起始设施编码",
        startDeviceName: "起始设施名称",
        fttxModel: "FTTX工程模块方案",
        solutionStatus: "方案状态",
      },
      alertInfo: {
        plzSelectSolution: "请选择案例",
        tip: "提示",
        modifySuccess: "修改成功",
        plzSelectDevice: "请选择设备",
        plzSelectOcable: "请选择一个光缆",
        plzSelectOcableSection: "请选择一个光缆段",
        plzInputDeviceCode: "请输入设备编码",
        plzInputOcableCode: "请输入光缆编码",
        plzInputOcableSectionCode: "请输入光缆段编码",
        plzInputOcableCardsNO: "请输入光缆牌号",
        plzInputDeviceName: "请输入设备名称",
        plzInputOcableName: "请输入光缆名称",
        plzInputOcableSectionName: "请输入光缆段名称",
        plzInputAddress: "请输入标准地址",
        alreadyFinish: "已完工,不允许修改",
        savaSuccessful: "保存成功",
        plzInputSolutionCode: "请输入方案编号",
        plzInputOBDNum: "请填写OBD数量",
        plzSelectionStartDevice: "请选择起始设备",
        plzRemoveToModify: "方案已经录入光缆，不允许修改模块方案。如需修改模块方案，请先删除光缆后，再进行修改!",
        deleteSuccessful: "删除成功!",
        plzSelectFacility: "请选择一个实体!",
        plzSelectSite: "请选择局站!",
        plzSelectDownDeviceType: "请选择下级设备类型!",
        plzInputDownDeviceNum: "请输入下级设备数量!",
        plzSelectOcablesectionTempalte: "请选择光缆段模板!",
        plzInputOcablesectionFibers: "请选输入光缆段成端芯数!",
        plzSelectRoom: "请选择机房!",
        ocableTpmplateFiberNumIsNull: "模板纤芯数量为空，请确认!",
        fiberLessThenFuse: "‘熔纤芯数’要大于或者等于 ‘光缆成端芯数’",
        FuseAndFiberNum: "请填写‘熔纤芯数’和‘光缆成端芯数’",
        FuseMoreThanFiberNum: "‘熔纤芯数’不能大于 ‘光缆段模版’对应的纤芯总数!",
        digfiberNeedMoreThenfiber: "‘掏接芯数’要大于或者等于 ‘光缆成端芯数’",
        NeedLessThenFiber: "‘掏接芯数’ 与‘下级设备数量’字段值相乘的积不能大于 ‘光缆段模版’对应的纤芯总数!",
        plzInputFiberNum: "请填写‘掏接芯数’和‘光缆成端芯数’",
        plzSelectFacilityStatus: "请选择设施状态",
        plzInputCapacity: "请输入标称容量",
        plzSelectSmallCountry: "请选择划小区域",
        plzSelectMarketingArea: "请选择营销区",
        plzSelectPropertyOwner: "请选择产权归属",
        jointbelongOcableIsrequired: "接头的所属光缆是必填项",
        belongOcableIsrequired: "所属模板是必填项!",
        plzSelectAccessMode: "请选择接入方式",
        plzSelectOpticalCableModel: "请选择光缆模式",
        plzSelectCableType: "请选择光缆级别",
        plzSelectTopo: "请选择拓扑结构",
        plzSelectLifeStateid: "请选择生命周期状态",
        plzSelectBuildWay: "请选择建设方式",
        plzSelectPropertyType: "请选择产权类型",
        plzSelectLayFashion: "请选择敷设方式",
        plzSelectLayingWay: "请选择敷设模式",
        plzFromOcable: "请从光缆公共属性->下一步，新增或修改光缆段公共属性!",
        plzSelectJTType: "请选择接头类型",
        plzSelectObdLevel: "请选择分光器级别",
        plzSelectIsEnd: "请选择是否末级OBD",
        plzInputNum: "请输入牌号",
        plzFromStep2: "请从第二步开始操作",
        plzInputOcableSectionLength: "请输入光缆段长度",
        deviceNotCd: "有设备未成端，是否继续？",
        isAutoPut: "是否把多余的空闲纤芯自动放置尾纤？",
      },
      step1: {
        name: "第一步：选择起始设备及方案",
        startODevice: "起始光交设施",
        downOBDNum: "下挂OBD数量：",
      },
      step2: {
        name: "第二步：录入模块化参数",
        region: "所属区域：",
        downDeviceType: "下级设备类型：",
        downDeviceNum: "下级设备数量：",
        downDeviceOBDNum: "每设备挂OBD数：",
        ocablectionTemplate: "光缆段模板：",
        ocablectionFiberNum: "光缆段成端芯数：",
        entryClerk: "录入员：",
        entryTime: "录入时间：",
        fuseNum: "熔纤芯数：",
        passThrought: "掏接芯数：",
        isGKFacility: "是否挂靠机房(安装点)",
        useSameRoom: "对新旧设施使用旧同一机房(安装点)",
        belongFacility: "所属机房(安装点)：",
        useDiffRoom: "对新设施新建不同机房(安装点)",
        seq: "序号",
        facilityName: "机房(安装点)名称",
        failityCode: "机房(安装点)编码",
        failityNum: "安装点数量：",
        failityNum2: "安装点数量",
      },
      step3: {
        name: "第三步：光交设施公共属性",
        capacity: "标称容量：",
        maxCapacity: "安装容量：",
        installWay: "安装方式：",
        isIntegFlag: "一体化标志：",
        assignedtotal: "满配OBD数：",
      },
      step4: {
        name: "第四步：OBD公共属性",
        splitIn: "标称合路数：",
        splitOut: "标称分路数：",
        accessMode: "接入方式：",
        isEndObd: "是否末级分光器：",
        m_buildmodeid: "建设模式：",
        adsteststatus: "宽带挂测状态：",
      },
      step5: {
        name: "第五步：光缆公共属性",
        opticalCableModel: "光缆模式：",
        cableType: "光缆级别：",
        buildWay: "建设方式：",
        lifestateid: "生命周期状态：",
        topo: "拓扑结构：",
        mntlevelid: "维护等级：",
        layfashionid: "敷设方式：",
        netProperty: "光缆属性："
      },
      step6: {
        name: "第六步：光缆段公共属性",
        opticalFiberMode: "光纤模式：",
        opticalFiberType: "光纤类型：",
        layingWay: "敷设模式：",
        cableStructure: "光缆结构：",
        cableType: "光缆类型：",
        capacity: "纤芯数：",
      },
      step7: {
        name: "第七步：光接头公共属性",
        height: "高度：",
        attenuation: "接头损耗：",
      },
      step8: {
        name: "第八步：私有属性调整",
        gtcode: '接头编码：',
        GJDevice: "光交设施",
        addressDesc: "具体位置：",
        belongRoom: "所属机房：",
        deviceCode: "设备编码：",
        deviceName: "设备名称：",
        XCoordinate: "X轴坐标：",
        YCoordinate: "Y轴坐标：",
        Ocablelength: "光缆皮长：",
        m_addressitem: "标准地址：",
        addressID: "地址ID：",
        metaCategory: "设备类型：",
        obdLevel: "分光器级别：",
        m_pardevice: "父设备：",
        ocableCode: "光缆编码：",
        ocableSectionCode: "光缆段编码：",
        ocableName: "光缆名称：",
        ocableSectionName: "光缆段名称：",
        ocableCardsNO: "光缆牌号：",
        ocableSectionLength: "光缆段长度：",
        opticalCableCardsNo: "牌号：",
        gtName: "接头名称：",
        supportFacility: "支撑设施：",
        facilityTypeDiv: "支撑设施类型：",
        connectFibernum: "接续芯数：",
      },
      step9: {
        name: "第九步：拓扑图预览",
        autocd: "自动成端",
        autoDrawOnMap: "光设备资源落图",
        autoDL: "缆段落图敷设",
      },
      step10: {
        name: "第十步：提交",
        moSolution: "模块方案：",
        roomList: "设施机房列表",
        oDeviceList: "光设施列表",
        obdList: "OBD列表",
        oCableList: "光缆列表",
        oCableSectionList: "光缆段列表",
        gtList: "光接头列表",
        done: "完工",

      },

    },

    menucontrol: {
      deleteAlert: '删除${nodeName}节点，子节点也会被删除，是否确定',
      tip: '提示',
      saveOperation: '保存当前操作？',
    },
    JJXCodecutover: {
      beforeName: "割接前名称",
      beforeCut: "割接前编码",
      afterName: '割接后名称',
      newSite: "新局站",
      afterCut: '割接后编码',
      check: '统计检查',
      cut: '割接',
      reset: '重置',
      getDetial: '查看详情',
      export: '导出',
      plzSelectDevice: '请选择交接箱',
      plzSelectZHX: '请选择综合箱',
      thisCodeIsAlreadyExist: '系统已存在同名的交接箱编码，请重新修改新交接箱编码',
      successful: '修改成功！！！',
      success: '成功',
      type: '类型',
      oldCode: '旧编码',
      newCode: '新编码',
      thisSiteNotExist: '该交接箱所属局站不存在，请重新输入',
      thisZHXSiteNotExist: '该综合箱所属局站不存在，请重新输入',
      cancel: '取消',
      details: '详细信息'
    },
    MDFBindMDF: {
      fromDevice: '本端设备',
      oppositeDevice: '对端设备',
      fromModule: '本端模块',
      oppositeModule: '对端模块',
      bindModule: '关联模块',
      unBindModule: '解除关联',
      refresh: '刷新数据',
      fromModuleCode: '本端模块编码',
      fromModuleName: '本端模块名称',
      oppositeModuleCode: '对端模块编码',
      oppositeModuleName: '对端模块名称',
      plzSelectData: '请选择数据',
      unBindResult: '${successNum}对关联模块解除成功。br' +
        '${failedNum}对关联模块解除失败，失败原因如下：br' +
        '如果模块下端口状态为预占、占用、割接封锁则不允许删除。br' +
        '失败模块为：${failedModuleCode}',
      canNotBind: '指定模块已关联，不能重复关联。',
      bindSuccessful: '关联成功'
    },
    MDFPortsBindProject: {
      moduleType: '模块类型',
      loopModule: 'LOOP模块',
      pstnModule: 'PSTN模块',
      moduleCode: '模块编码',
      buildBind: '建立关联',
      alreadyBind: '已绑定关联',
      moveIn: '移入',
      moveOut: '移出',
      moveDown: '下移',
      moveUp: '上移',
      bind: '关联',
      unbind: '解关联',
      noRelatedPort: '无相关端子',
      plzSelectPort: '请选择一个端子',
      alreadyTop: '已经移动到最上层，不能继续上移',
      alreadyBottom: '已经移动到最下层，不能继续下移',
      portsNumDifferent: '两边端子数量不一致',
      bindSuccessful: '关联成功',
      plzSelectRecord: '请选择需要解除关联的记录',
      bindResult: '${successNum}对端子解除关联成功,${failedCode},共${failedNum}对端子解除关联失败',
    },
    deviceBindMdf: {
      fromDevice: '本端设备',
      oppositeDevice: '对端设备',
      oppositeModule: '对端模块',
      deviceType: '设备类型',
      dslam: 'DSLAM设备',
      pstn: 'PSTN设备',
      ag: 'AG设备',
      onu: 'ONU设备',
      ddf: '数字配线架',
      buildBind: '建立关联',
      alreadyBind: '已绑定关联',
      moveIn: '移入',
      moveOut: '移出',
      moveDown: '下移',
      moveUp: '上移',
      bind: '关联',
      unbind: '解关联',
      unbindByModule: '按模块解关联',
      fromFrame: '本端机框',
      fromCard: '本端板卡',
      startPort: '起始端口',
      endPort: '终止端口',
      portPrefix: '端子前缀',
      query: '查询',
      num: '序号',
      moduleCode: '模块编码',
      portCode: '端子编码',
      rowNum: '行号',
      colNum: '列号',
      frameCode: '机框编码',
      cardCode: '板卡编码',
      portNetCode: '端口网管编码',
      portSeq: '端子序号',
      thisDevHasNoMdf: '该设备的设施下没有MDF设备',
      onuDevCardTypeNotNull: '当前为ONU设备，板卡不能为空',
      plzSelectedModule: '请选择模块',
      plzSelectPort: '请选择一个端子',
      alreadyTop: '已经移动到最上层，不能继续上移',
      alreadyBottom: '已经移动到最下层，不能继续下移',
      portsNumDifferent: '两边端子数量不一致',
      bindSuccessful: '关联成功',
      plzSelectRecord: '请选择需要解除关联的记录',
      plzSelectCardAndModule: '请选择板卡和模块进行解除关联',
      plzSelectOnuCard: '请选择ONU设备的板卡',
      bindResult: '${successNum}对端子解除关联成功,${failedCode},共${failedNum}对端子解除关联失败',
    },
    ePortCutOver: {
      orgDevice: '当前设备',
      devCode: '设备编码',
      devType: '设备类型',
      cutRange: '请输入割接范围',
      startPort: '起始端子',
      endPort: '终止端子',
      colNum: '列框号',
      tarDev: '目标设备',
      tarStartPortNum: '请输入目标起始端子号',
      reverse: '反向',
      cut: '割接',
      reset: '重置',
      num: '序号',
      code: '编码',
      portSeq: '端子序号',
      busState: '业务状态',
      isConnected: '是否成端',
      neeAllCut: '换设备割接时，一条电缆段成端区间需全部割接！',
      notHaveColNum: '成端区间没有列号，请检查数据！',
      rangWrong: '输入的起始,终止端子成端区间不正确，不允许割接!',
      notLessZero: '割接范围或列框号不能输入负值',
      freePortNotEnough: '割接到的设备上不存在足够数量的空闲端子',
      cutSuccess: '割接成功',
      plzSlectedRange: '请在表格中选择一段成端区间',
      enterDev: '请输入待割接与割接到的设备',
      orgNoConnected: '待割接的设备上不存在成端',
      tagNoConnected: '割接到的设备上不存在成端',
      plzEnterRange: '请输入待割接与割接到的端子区间',
      colNotNull: '如果待割接的设备为MDF时，列框号不能为空',
      tips1: "温馨提示:</br>1、须按整条成端电缆进行割接，特别是一条电缆存在多个区间时，起始和终止根据成端区间的最小和最大值确定</br>2、不支持一条电缆跨MDF直列列框成端的场景",
      tips2: "温馨提示:</br>1、只须输入起始端子号，系统会根据起始端子号自动获取终止端子</br>2、如果目标设备为MDF，需要输入割接到的列框号"
    },
    eCableSectionCut: {
      orgSection: '原电缆段',
      eJt: '电接头',
      newSection1: '新电缆段1',
      newSection2: '新电缆段2',
      sectionCode: '电缆段编码',
      sectionName: '电缆段名称',
      aDevice: 'A段节点',
      zDevice: 'Z段节点',
      totalLine: '线对总数',
      lineLength: '线缆长度',
      cut: '割接',
      code: '编码',
      name: '名称',
      reset: '重置',
      cutSuccessful: '割接成功',
      sameJT: '电缆段已有一端为此电接头设备!',
      plzSelectSection: '请选择要割接的电缆段!!',
      plzSelectJt: '请选择要割接的接头!!',
      plzEnterNameAndCode: '请输入新电缆段的编码及名称!!',
      codeAndNameNeedDiff: '新电缆段编码不能相同!!',
      notPassFacility: '电缆段未经过接头所在支撑设施，不允许进行割接!!',
    },
    eCableSectionMerger: {
      eJt: '电接头',
      merger: '合并',
      cancel: '取消',
      reset: '重置',
      save: '保留',
      section1: '电缆段1',
      section2: '电缆段2',
      plzSelectTwoData: '请选择两条数据！！！',
      twoSectionRangeDiff: '两条电缆段的线序范围不一致！！！',
      mergerSuccessful: '合并成功！！！',
    },
    eCableLineCutOver: {
      cableCode: '电缆编码',
      cutPoint: '割接点',
      cutPointType: '割接点类型',
      query: '查询',
      startSeq: '起始线序',
      endSeq: '终止线序',
      cutPreview: '割接预览',
      cutOver: '割接',
      isEmpty: '电缆或割接点实体为空!',
      tarCableNotBelong: '割接到的电缆未经过选定的支撑点设施或支撑设施不存在属于割接到的电缆接头!',
      plzSelectRecord: '请选择一条需要割接的记录!',
      plzSelectDT: '请选择一个割接到的接头!',
      allOutIsZero: '该接头处的总出线序为0，不能进行割接!',
      startSeqOrEndSeqIsEmpty: '起始线序或终止线序为空!',
      seqRangeIsEmpty: '该缆段的线序区间为空!',
      freeSeqIsEmpty: '该接头处的空闲线序为空，不能进行割接!',
      cableTypeWrong: '待割接的电缆类型不正确!',
      endSeqLessThanStartSeq: '终止线序数必须大于起始线序数!',
      rangeDiff: '输入的区间与待割接接头处的总出线序数不相等，不能割接!',
      notInSameRange: '输入的区间不在接头处的同一个空闲线序区间内!',
      typeWrong1: '主干电缆不能割接到中继电缆!',
      typeWrong2: '配线电缆只能割接到配线电缆或主干电缆!',
      typeWrong3: '中继电缆只能割接到中继电缆!',
      typeWrong4: '联络电缆只能割接到联络电缆或主干电缆!',
      confirm: '确定',
      cancel: '取消',
      onlyCutShow: '只有展示出来的资源会被割接到新的电缆下，没展示的资源不会被割接，是否确认割接?',
      resultIsEmpty: '割接预览结果为空，不能进行割接!',
      cutSuccessful: '割接成功！！!',
    },
    ecableSectionSplit: {
      cutSection: '待拆分电缆段',
      startLineSeq: '起始线序',
      endLineSeq: '终止线序',
      lineNumber: '拆分电缆段条数',
      sectionCode: '电缆段编码',
      startPort: '起始端子',
      endPort: '终止端子',
      cutOver: '割接',
      reset: '重置',
      cutNumNeedGreater1: '拆分数目需大于1！',
      allIsDT: '所选电缆段两端都是接头，不符合拆分条件！',
      freePort: '空闲端子：',
      rangeNeedInside: '拆分电缆段线序区间应该在原区间内!',
      lessThanNum: '第${num}条电缆段的成端终止线序小于所填值，不允许操作!',
      greaterThanNum: '第${num}条电缆段的成端起始线序大于所填值，不允许操作!',
      dValueNeedEqual: '第${num}条电缆段的线序与端子不匹配，起始线序与终止线序的差值需要和起始端子与终止端子的差值相等，不允许操作！',
      cutNumNotSame: '拆分后电缆段线序总数与待拆分电缆段线序数不相等，不允许操作！',
      cutSuccess: '拆分成功！',
      notDT: '电缆段端点不为接头，不允许操作！',
      sameDTNotExist: '拆分后的电缆段与待拆分的电缆段不存在相同的接头端点，不允许操作！',
      lineSeqNotFree: '拆分后电缆段所选线对存在占用，不允许操作！',
      notInSameSection: '拆分后的电缆段不在同一条电缆下，不允许操作！',
      portNotFree: '拆分后电缆段所选端子被占用，不允许操作！',
      deviceWrong: '待拆分的电缆段应该是一端为接头，一端为其他设备，待拆分电缆段不满足拆分条件！',
      distributionWrong: '拆分后电缆段线序区间分配不对，不允许操作！',
      rangeNotSame: '缆段成段区间与实际线对区间不相符，不允许操作！',
      tip: '提示',
      confirm: '确定',
      canNotReturn: '该操作不能回退，是否确认割接',
      cancel: '取消'
    },
    ecableSectionMerge: {
      sectionCodeOrName: '电缆段编码(名称)',
      query: '查询',
      moveIn: '移入',
      moveOut: '移出',
      tarSection: '目标电缆段',
      sectionCode: '电缆段编码',
      sectionName: '电缆段名称',
      aDev: 'A端节点',
      zDev: 'Z端节点',
      lineNum: '线对数',
      merge: '合并',
      reset: '重置',
      plzSelectData: '请选择数据',
      plzSelectHasDT: '请选择一端为接头的电缆段！',
      tip: '提示',
      plzSelectTarSection: '请选择目标电缆段！',
      connectedRangeNotEnough: '合并目标电缆段成端区间不足！',
      plzSelectSectionToMerge: '请选择待合并电缆段！',
      confirm: '确定',
      cancel: '取消',
      canNotReturn: '该操作不能回退，是否确认割接',
      mergeSuccess: '合并成功',
      belongSectionNotSame: '待合并电缆段所属电缆不同，不允许合并！',
      belongSectionNotSame2: '目标电缆段与待合并的电缆段所属电缆不同，不允许合并！',
      notThroughSection: '待合并的电缆段没有经过接头所属设施，不允许合并！',
      plzSelectSection: '请移入电缆段！',
      sectionNotAllowedSame: '待割接电缆段与目标电缆段不允许相同，请移除！',
      samePointSection: '请选择有共同端的电缆段！',
    },
    ecableSectionSplitMerge: {
      section: '待合并电缆段',
      sectionCodeOrName: '电缆段编码(名称)',
      query: '查询',
      moveIn: '移入',
      moveOut: '移出',
      moveUp: '上移',
      moveDown: '下移',
      mergeSection: '合并电缆段',
      cutOver: '割接',
      reset: '重置',
      plzSelectData: '请选择数据',
      plzSelectAData: '请选择一条数据',
      alreadyFirst: '已经是第一条数据，不能上移',
      alreadyLast: '已经是最后一条数据，不能下移',
      plzMoveTwo: '请移入两条待合并的电缆段！',
      plzSelectNeedMergeSection: '请选择合并的电缆段！',
      notLessThanLineNum: '合并的电缆段线对数必须不少于待合并的电缆段线对数！',
      confirm: '确定',
      cancel: '取消',
      canNotReturn: '该操作不能回退，是否确认割接',
      cutSuccess: '割接成功！',
      tip: '提示',
      notComplete: '电缆段路由不完整，不允许进行割接！',
      notPassThrough: '待合并电缆段:${num},路由不经过合并电缆段端点接头所在设施！',
    },
    eCableSectionAbility: {
      title: '业务能力查看'
    },
    dpGroupManage: {
      dpNameOrCode: '分线盒编码(名称)',
      query: '查询',
      moveMount: '移除下挂',
      addMount: '添加下挂',
      addDevice: '新增设备',
      addSuccess: '添加成功！！',
      mountSuccess: '下挂成功！！',
      moveSuccess: '移除成功！！',
      tip: '提示',
      confirm: '确定',
      cancel: '取消',
      addMountDevice: '添加下挂设备',
      plzSelectDevice: '请选择一个设备',
    },
    ecableApplication: {
      connectedNum: '成端数',
      freeNum: '空闲数',
      notFreeNum: '非空闲数',
      chartTitle: '占用信息统计图',
      num: '数量',
      code: '编码',
      plzSelectAData: '请选择一条数据',
      unPerforated: '未穿孔详情',
      layState: '敷设状态',
      layLength: '长度',
      crossHole: '敷设已穿孔',
      notLay: '未敷设',
      notCrossHole: '敷设未穿孔',
      under: '下的',
      lineRange: '电缆线序(区间)',
      asideColNum: 'A设施列号',
      asidePortRange: 'A端成端（端序区间)',
      aDevType: 'A端设备类型',
      aDevName: 'A端设备名称',
      aDevCode: 'A端设备编码',
      aAddress: 'A端安装地址',
      zsideColNum: 'Z设施列号',
      zsidePortRange: 'Z端成端（端序区间）',
      zDevType: 'Z端设备类型',
      zDevName: 'Z端设备名称',
      zDevCode: 'Z端设备编码',
      zAddress: 'Z端安装地址',
      belongCable: '所属电缆',
      belongSection: '所属电缆段',
      routeCode: '路由编码',
      routeType: '路由类型',
      length: '支撑段长度',
      startDevCode: '起始设备编码',
      startDevType: '起始设备类型',
      startFacCode: '起始设施编码',
      startFacType: '起始设施类型',
      endDevCode: '终止设备编码',
      endDevType: '终止设备类型',
      endFacCode: '终止设施编码',
      endFacType: '终止设施类型',
    },
    sitefiberQuery: {
      adevice: "A端设备",
      aroom: "A端机房",
      asite: "A端局站",
      zdevice: "Z端设备",
      zroom: "Z端机房",
      zsite: "Z端局站",
      deviceType: "设备类型",
      query: "查询",
      noResult: "没有查询到数据",
    },
    fiberComprehensiveQuery: {
      adevice: "A端设备",
      aroom: "A端机房",
      asite: "A端局站",
      zdevice: "Z端设备",
      zroom: "Z端机房",
      zsite: "Z端局站",
      query: "查询",
      bySitefiber: "按局向光纤",
      sitefiber: "局向光纤",
      siteInfo: "局向信息",
      siteFiberGroupInfo: "局向光纤组信息",
      OCableSectionInfo: "光缆段信息",
      sitefiberInfo: "局向光纤信息",
      getSiteFiberGroupInfo: "查看局向光纤组信息",
      getSiteFiberInfo: "查看局向光纤",
      fiberCoreResurrection: "直达缆段有部分未成端纤芯本/对端信息",
      singleInfoPush: "信息推送单点",
      getOCableSectionInfo: "查看光缆段信息",
      getFiberInfo: "查看纤芯",
      fiberInfo: "纤芯信息",
      byOCableSection: "按光缆段",
      OCableSectionCode: "光缆段编码",
      noSiteFiberGroup: "未查询到局向光纤组",
      noOcableSection: "未查询到光缆段",
      plzSelectedGroup: "请选择一个局向光纤组！！",
      plzSelectedSiteInfo: "请选择一个局向信息！！",
      plzSelectedSiteFiber: "请选择一个局向光纤！！",
      plzSelectedOCableSection: "请选择一个光缆段！！",
      cableAlertInfo: "包括 ${sections} 条光缆段,共 ${all} 芯,其中占用 ${busy} 芯,预占 ${prev} 芯,空闲 ${free} 芯,坏芯 ${bad} 芯,缆段部分未成端纤芯 ${core} 芯",
      siteFiberAlertInfo: "包括 ${sitefibers} 个局向光纤组,共 ${all} 芯,其中占用 ${busy} 芯,预占 ${prev} 芯,空闲 ${free} 芯,坏芯 ${bad} 芯,缆段部分未成端纤芯 ${core} 芯",
    },
    accessareaApplication: {
      exportData: {
        sitecount: '站点数量',
        childaccesspointcount: '资源点数量',
        wellcount: '人井数量',
        gjcount: '光交数量',
        bandareacount: '小区数',
        accesspointcount: '集团客户数',
        pipesectionlength: '管道段长度（m）',
        length: '光缆段在区域内铺设长度（m）',
        dataInfo: '数据统计'
      },
      exportDetailData: {
        city: '地市',
        region: '区县',
        objecttype: '资源类型',
        entityid: '资源ID',
        entityname: '资源名称',
        accessareaid: '综合业务区ID',
        accessareaname: '综合业务区名称',
        creator: '资源创建人',
        createdate: '资源创建时间',
        dataInfo: '数据明细'
      },
      exportClassData: {
        city: '地市',
        region: '区县',
        accessareaname: '综合业务区名称',
        mainfiberareaname: '主干分纤区名称',
        termainfiberareaname: '末端分纤区名称',
        dataInfo: '分级表'
      }
    },
    accessareazjz: {
      window: {
        query: {
          view: '查询综合接入区',
          code: '综合接入区编码',
          name: '综合接入区名称',
          citysuburb: '综合接入区类型',
          region: '所属区域',
          area: '综合接入面积',
          search: '查询',
          clear: '清空',
          add: '添加',
          plsCheckAdd: '请勾选需要添加的网格',
          addConfirmPart1: '您选择了',
          addConfirmPart2: '个网格单元，确定要添加所选网格单元到已关联网格单元吗？',
          addSuccessful1: '共添加',
          addSuccessful2: '条数据到已关联网格',
          addFail: '添加失败',
          total: '统计总数'
        },
        relatedgird: {
          accessareazjz: '综合接入区',
          view: '网格单元查询',
          code: '网格单元编码',
          name: '网格单元名称',
          branch: '所属支局',
          assetAttr: '网格资产属性',
          search: '查询',
          add: '添加',
          delete: '删除',
          resourceSearch: '关联资源查询',
          site: '局站',
          room: '机房',
          device: '设备',
          facility: '设施',
          siteCode: '局站编码',
          siteName: '局站名称',
          siteType: '局站类型',
          citySuburb: '属地性质',
          gridCode: '所属网格',
          roomCode: '机房编码',
          roomName: '机房名称',
          roomCate: '机房种类',
          addressDesc: '具体位置',
          roomlevel: '机房分类等级',
          deviceType: '设备类型',
          deviceCode: '设备编码',
          deviceName: '设备名称',
          portNum: '端子总数',
          portUseNum: '端子占用数',
          facilityType: '设施类型',
          facilityCode: '设施编码',
          facilityName: '设施名称',
          export: '导出',
          totalPage: '总页数',
          totalRecord: '总记录数',
          pageNo: '页码',
          viewAll: '查看所有',
          plzSelectData: '请勾选需要删除的网格',
          removeConfirmPart1: '您选择了',
          removeConfirmPart2: '个网格单元，确定要删除关联网格单元吗？',
          deleteSuccessful: '删除成功',
          deleteFail: '删除失败',
          deleteAccessAreaConfirm: '该操作将会删除接入区图形和接入区本身数据，是否确认删除？'
        }
      }
    }
  }
};