/*
 * ============================================================================
 * 功能模块：HyAssistant权限管理模块
 * ============================================================================
 * 
 * 模块作用：定义和管理HyAssistant应用程序的所有权限标识符，提供权限验证和描述功能
 * 
 * 主要功能：
 * - 定义核心权限常量（如授权管理）
 * - 定义各功能模块的权限标识符
 * - 维护权限列表和描述映射
 * - 提供权限验证和描述获取的辅助方法
 * 
 * 执行逻辑：
 * 1. 静态类HaPermissionKeys包含所有权限常量定义
 * 2. RequiredPermissions数组列出所有必需权限
 * 3. PermissionDescriptions字典提供权限中文描述映射
 * 4. GetPermissionDescription方法根据权限名获取描述
 * 5. IsValidPermission方法验证权限名是否有效
 * 
 * 注意事项：
 * - 所有权限常量均为public const string类型
 * - 权限描述映射需与权限常量保持同步
 * ============================================================================
 */

using System;

namespace HyAssistant
{
    /// <summary>
    /// HyAssistant权限常量类，包含所有权限标识符
    /// </summary>
    public static class HaPermissionKeys
    {
        #region 核心权限

        /// <summary>
        /// 授权管理权限
        /// </summary>
        public const string License = "license";

        #endregion 核心权限

        #region 功能权限

        /// <summary>
        /// 文件复制助手权限
        /// </summary>
        public const string FileCopier = "ha_filecopier";

        /// <summary>
        /// 邮箱附件接收助手权限
        /// </summary>
        public const string Mail = "ha_mail";

        /// <summary>
        /// 文件解压助手权限
        /// </summary>
        public const string FileExtract = "ha_fileextract";

        /// <summary>
        /// 文件分析监控权限
        /// </summary>
        public const string FileAnalyzer = "ha_fileanalyzer";

        /// <summary>
        /// Visio文件转PDF监控权限
        /// </summary>
        public const string VisioPdf = "ha_visiopdf";

        /// <summary>
        /// 网页常挂助手权限
        /// </summary>
        public const string WebBrowser = "ha_webbrowser";

        /// <summary>
        /// 网页常挂助手权限v2
        /// </summary>
        public const string WebBrowserV2 = "ha_webbrowserv2";

        /// <summary>
        /// 中国铁塔照片下载助手权限
        /// </summary>
        public const string ChinaTowerDownload = "ha_ttpic";

        #endregion 功能权限

        #region 权限列表

        /// <summary>
        /// 所有需要检查的权限列表
        /// </summary>
        public static readonly string[] RequiredPermissions =
        {
            License,
            FileCopier,
            Mail,
            FileExtract,
            FileAnalyzer,
            VisioPdf,
            WebBrowser,
            ChinaTowerDownload
        };

        #endregion 权限列表

        #region 权限描述映射

        /// <summary>
        /// 权限名称到中文描述的映射
        /// </summary>
        public static readonly System.Collections.Generic.Dictionary<string, string> PermissionDescriptions =
            new System.Collections.Generic.Dictionary<string, string>
            {
                { License, "授权管理" },
                { FileCopier, "文件复制助手" },
                { Mail, "邮箱附件接收助手" },
                { FileExtract, "文件解压助手" },
                { FileAnalyzer, "文件分析监控" },
                { VisioPdf, "Visio文件转PDF监控" },
                { WebBrowser, "网页常挂助手" },
                { ChinaTowerDownload, "中国铁塔照片下载助手" }
            };

        #endregion 权限描述映射

        #region 辅助方法

        /// <summary>
        /// 获取权限的中文描述
        /// </summary>
        /// <param name="permissionName">权限名称</param>
        /// <returns>中文描述，如果未找到则返回权限名称本身</returns>
        public static string GetPermissionDescription(string permissionName)
        {
            return PermissionDescriptions.TryGetValue(permissionName, out string description)
                ? description
                : permissionName;
        }

        /// <summary>
        /// 检查是否为有效的权限名称
        /// </summary>
        /// <param name="permissionName">权限名称</param>
        /// <returns>如果是有效权限返回true，否则返回false</returns>
        public static bool IsValidPermission(string permissionName)
        {
            return Array.Exists(RequiredPermissions, p => string.Equals(p, permissionName, StringComparison.OrdinalIgnoreCase));
        }

        #endregion 辅助方法
    }
}