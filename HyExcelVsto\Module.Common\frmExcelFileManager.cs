/*
 * ============================================================================
 * 功能模块：Excel文件记录管理窗体
 * ============================================================================
 *
 * 模块作用：管理和记录Excel文件信息，提供文件记录的增删改查功能
 *
 * 主要功能：
 * - 记录当前打开的Excel文件信息
 * - 显示已记录的Excel文件列表
 * - 支持打开和删除选定的文件记录
 * - 提供一键清空所有记录功能
 * - 支持双击打开文件和批量操作
 *
 * 执行逻辑：
 * 1. 初始化窗体界面和ListView控件
 * 2. 加载已保存的文件记录并显示在列表中
 * 3. 提供菜单项执行不同的文件管理操作
 * 4. 支持记录所有打开文件或仅记录激活文件
 * 5. 实现文件的打开、删除和清空功能
 *
 * 注意事项：
 * - 需要正确处理Excel应用程序对象的访问
 * - 文件路径比较使用忽略大小写的字符串比较
 * - 需要检查文件是否已打开以避免重复打开
 * - 异常处理需要区分可见和不可见状态下的消息显示
 * ============================================================================
 */

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using Microsoft.Office.Interop.Excel;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// Excel文件记录管理窗体
    /// </summary>
    /// <remarks>提供Excel文件记录的管理功能，包括文件信息的记录、显示、打开和删除。 支持记录当前打开的所有Excel文件或仅记录激活的文件。 提供友好的用户界面和便捷的操作方式。</remarks>
    public partial class frmExcelFileManager : Form
    {
        /// <summary>
        /// 当前加载的文件记录列表
        /// </summary>
        private List<ExcelFileRecord> _currentRecords;

        /// <summary>
        /// 初始化frmExcelFileManager窗体实例
        /// </summary>
        /// <remarks>构造函数，初始化窗体组件并调用窗体初始化方法。</remarks>
        public frmExcelFileManager()
        {
            InitializeComponent();
            InitializeForm();
        }

        /// <summary>
        /// 初始化窗体
        /// </summary>
        /// <remarks>
        /// 设置窗体的基本配置和初始状态：
        /// 1. 配置ListView控件的显示属性
        /// 2. 加载已保存的文件记录
        /// 3. 更新状态标签显示
        /// </remarks>
        private void InitializeForm()
        {
            // 设置ListView
            SetupListView();

            // 加载现有记录
            LoadFileRecords();
        }

        /// <summary>
        /// 设置ListView控件
        /// </summary>
        /// <remarks>
        /// 配置ListView控件的显示样式和列结构：
        /// 1. 设置详细信息视图模式
        /// 2. 启用整行选择和网格线显示
        /// 3. 添加文件名、文件路径和最后修改时间三列
        /// </remarks>
        private void SetupListView()
        {
            listViewFiles.View = View.Details;
            listViewFiles.FullRowSelect = true;
            listViewFiles.GridLines = true;
            listViewFiles.MultiSelect = true;

            // 添加列
            listViewFiles.Columns.Add("文件名", 200);
            listViewFiles.Columns.Add("文件路径", 300);
            listViewFiles.Columns.Add("最后修改时间", 150);
        }

        /// <summary>
        /// 加载文件记录到ListView
        /// </summary>
        /// <remarks>
        /// 从文件记录管理器加载所有记录并显示在ListView中：
        /// 1. 按记录时间倒序排列记录
        /// 2. 为每条记录创建对应的ListView项
        /// 3. 设置项的标签数据和子项内容
        /// 4. 自动调整列宽以适应内容
        /// 5. 更新状态标签显示记录总数
        /// </remarks>
        private void LoadFileRecords()
        {
            try
            {
                _currentRecords = ExcelFileRecordManager.LoadRecords();
                RefreshListView();
                UpdateStatusLabel();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载文件记录失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 刷新ListView显示
        /// </summary>
        /// <remarks>
        /// 清空现有列表项并重新加载所有记录：
        /// 1. 按记录时间倒序排列所有记录
        /// 2. 为每条记录创建包含文件名、路径和修改时间的列表项
        /// 3. 将记录对象作为项的标签数据存储
        /// 4. 自动调整各列宽度以适应内容
        /// </remarks>
        private void RefreshListView()
        {
            listViewFiles.Items.Clear();

            foreach (var record in _currentRecords.OrderByDescending(r => r.RecordTime))
            {
                var item = new ListViewItem(record.FileName);
                item.SubItems.Add(record.FilePath);
                item.SubItems.Add(record.LastModified.ToString("yyyy-MM-dd HH:mm:ss"));
                item.Tag = record;
                listViewFiles.Items.Add(item);
            }

            // 自动调整列宽
            foreach (ColumnHeader column in listViewFiles.Columns)
            {
                column.AutoResize(ColumnHeaderAutoResizeStyle.ColumnContent);
            }
        }

        /// <summary>
        /// 更新状态标签
        /// </summary>
        /// <remarks>更新窗体底部状态栏的文本内容，显示当前记录总数。</remarks>
        private void UpdateStatusLabel()
        {
            toolStripStatusLabel1.Text = $"共 {_currentRecords.Count} 个文件记录";
        }

        /// <summary>
        /// 更新状态信息
        /// </summary>
        /// <param name="message">状态信息</param>
        /// <remarks>更新窗体底部状态栏的文本内容为指定消息。</remarks>
        private void UpdateStatus(string message)
        {
            toolStripStatusLabel1.Text = message;
        }

        /// <summary>
        /// 记录所有打开的Excel文件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>响应"记录所有文件"菜单项点击事件，调用记录方法记录所有当前打开的Excel文件。 通过传递true参数指示记录所有文件。</remarks>
        private void 记录所有文件ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // 调用公共方法执行记录操作 - 记录所有文件
            RecordCurrentFiles(true);
        }

        /// <summary>
        /// 仅记录当前激活的Excel文件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>响应"记录激活文件"菜单项点击事件，调用记录方法仅记录当前激活的Excel文件。 通过传递false参数指示仅记录激活文件。</remarks>
        private void 记录激活文件ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // 调用公共方法执行记录操作 - 仅记录激活文件
            RecordCurrentFiles(false);
        }

        /// <summary>
        /// 打开选定的文件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 响应"打开选定文件"菜单项点击事件：
        /// 1. 检查是否选择了文件
        /// 2. 收集所有选定文件的路径
        /// 3. 逐个打开选定文件
        /// 4. 统计成功和失败次数
        /// 5. 更新状态信息显示操作结果
        /// </remarks>
        private void 打开选定文件ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (listViewFiles.SelectedItems.Count == 0)
            {
                MessageBox.Show("请先选择要打开的文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                var filesToOpen = new List<string>();
                var successCount = 0;
                var failCount = 0;

                // 收集所有选定的文件路径
                foreach (ListViewItem item in listViewFiles.SelectedItems)
                {
                    if (item.Tag is ExcelFileRecord record)
                    {
                        filesToOpen.Add(record.FilePath);
                    }
                }

                // 逐个打开文件
                foreach (string filePath in filesToOpen)
                {
                    try
                    {
                        if (OpenExcelFile(filePath))
                        {
                            successCount++;
                        }
                        else
                        {
                            failCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        failCount++;
                        System.Diagnostics.Debug.WriteLine($"打开文件失败 {filePath}: {ex.Message}");
                    }
                }

                // 更新状态信息
                if (filesToOpen.Count == 1)
                {
                    if (successCount == 1)
                    {
                        UpdateStatus($"成功打开文件: {Path.GetFileName(filesToOpen[0])}");
                    }
                    else
                    {
                        UpdateStatus($"打开文件失败: {Path.GetFileName(filesToOpen[0])}");
                    }
                }
                else
                {
                    UpdateStatus($"打开完成 - 成功: {successCount} 个，失败: {failCount} 个");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开文件时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 删除选定的文件记录
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 响应"删除选定记录"菜单项点击事件：
        /// 1. 检查是否选择了记录
        /// 2. 收集所有选定记录的对象
        /// 3. 调用记录管理器删除选定记录
        /// 4. 重新加载文件记录列表
        /// 5. 更新状态信息显示删除结果
        /// </remarks>
        private void 删除选定记录ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (listViewFiles.SelectedItems.Count == 0)
            {
                MessageBox.Show("请先选择要删除的文件记录", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                var recordsToRemove = new List<ExcelFileRecord>();
                foreach (ListViewItem item in listViewFiles.SelectedItems)
                {
                    if (item.Tag is ExcelFileRecord record)
                    {
                        recordsToRemove.Add(record);
                    }
                }

                ExcelFileRecordManager.RemoveRecords(recordsToRemove);
                LoadFileRecords();
                UpdateStatus($"成功删除 {recordsToRemove.Count} 个文件记录");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除记录失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 一键清空所有记录
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 响应"一键清空记录"菜单项点击事件：
        /// 1. 检查当前是否有记录
        /// 2. 调用记录管理器清空所有记录
        /// 3. 重新加载文件记录列表
        /// 4. 更新状态信息显示操作结果
        /// </remarks>
        private void 一键清空记录ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (_currentRecords.Count == 0)
            {
                MessageBox.Show("当前没有文件记录", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                ExcelFileRecordManager.ClearAllRecords();
                LoadFileRecords();
                UpdateStatus("已清空所有文件记录");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清空记录失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 双击列表项打开文件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 响应ListView双击事件，打开选定的文件记录对应的Excel文件：
        /// 1. 检查是否单选了一个列表项
        /// 2. 获取该项关联的文件记录对象
        /// 3. 调用文件打开方法打开对应文件
        /// </remarks>
        private void listViewFiles_DoubleClick(object sender, EventArgs e)
        {
            if (listViewFiles.SelectedItems.Count == 1)
            {
                var selectedItem = listViewFiles.SelectedItems[0];
                if (selectedItem.Tag is ExcelFileRecord record)
                {
                    OpenExcelFile(record.FilePath);
                }
            }
        }

        /// <summary>
        /// 打开Excel文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功打开文件</returns>
        /// <remarks>
        /// 打开指定路径的Excel文件：
        /// 1. 检查文件是否存在
        /// 2. 检查文件是否已打开，如果已打开则激活该文件
        /// 3. 如果未打开则调用Excel应用程序打开文件
        /// 4. 处理可能发生的异常并显示错误信息
        /// </remarks>
        private bool OpenExcelFile(string filePath)
        {
            try
            {
                // 检查文件是否存在
                if (!File.Exists(filePath))
                {
                    MessageBox.Show($"文件不存在: {filePath}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }

                var application = Globals.ThisAddIn.Application;

                // 检查文件是否已经打开
                foreach (Workbook workbook in application.Workbooks)
                {
                    try
                    {
                        if (string.Equals(workbook.FullName, filePath, StringComparison.OrdinalIgnoreCase))
                        {
                            // 文件已打开，激活该工作簿
                            workbook.Activate();
                            UpdateStatus($"文件已打开: {Path.GetFileName(filePath)}");
                            return true;
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"检查工作簿失败: {ex.Message}");
                    }
                }

                // 文件未打开，打开文件
                application.Workbooks.Open(filePath);
                UpdateStatus($"成功打开文件: {Path.GetFileName(filePath)}");
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开文件失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 关闭窗体
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>响应"关闭"菜单项点击事件，关闭当前窗体。</remarks>
        private void 关闭ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// 公共方法：记录当前打开的Excel文件（供外部调用）
        /// </summary>
        /// <param name="recordAllFiles">true：记录所有已打开的Excel文件；false：只记录当前前台激活的Excel文件</param>
        /// <returns>返回记录的文件数量</returns>
        /// <remarks>
        /// 记录当前打开的Excel文件信息：
        /// 1. 根据参数决定记录所有文件还是仅记录激活文件
        /// 2. 获取Excel应用程序对象和工作簿信息
        /// 3. 创建文件记录对象并添加到记录管理器
        /// 4. 如果窗体可见则刷新显示并更新状态信息
        /// 5. 返回记录的文件数量或错误代码
        /// </remarks>
        public int RecordCurrentFiles(bool recordAllFiles = false)
        {
            try
            {
                var application = Globals.ThisAddIn.Application;
                var newRecords = new List<ExcelFileRecord>();

                if (recordAllFiles)
                {
                    // 记录所有已打开的Excel文件
                    foreach (Workbook workbook in application.Workbooks)
                    {
                        try
                        {
                            // 只处理已保存的文件（有完整路径）
                            if (!string.IsNullOrEmpty(workbook.FullName) && File.Exists(workbook.FullName))
                            {
                                var fileInfo = new FileInfo(workbook.FullName);
                                var record = new ExcelFileRecord(workbook.FullName, fileInfo.LastWriteTime);
                                newRecords.Add(record);
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"处理工作簿失败: {ex.Message}");
                        }
                    }
                }
                else
                {
                    // 只记录当前前台激活的Excel文件
                    try
                    {
                        Workbook activeWorkbook = application.ActiveWorkbook;
                        if (activeWorkbook != null && !string.IsNullOrEmpty(activeWorkbook.FullName) && File.Exists(activeWorkbook.FullName))
                        {
                            var fileInfo = new FileInfo(activeWorkbook.FullName);
                            var record = new ExcelFileRecord(activeWorkbook.FullName, fileInfo.LastWriteTime);
                            newRecords.Add(record);
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"处理当前激活工作簿失败: {ex.Message}");
                    }
                }

                if (newRecords.Count > 0)
                {
                    ExcelFileRecordManager.AddRecords(newRecords);
                    // 如果窗体已显示，则刷新显示
                    if (this.Visible)
                    {
                        LoadFileRecords();
                        string statusMessage = recordAllFiles
                            ? $"成功记录 {newRecords.Count} 个文件"
                            : $"成功记录当前激活文件：{newRecords[0].FileName}";
                        UpdateStatus(statusMessage);
                    }
                    return newRecords.Count;
                }
                else
                {
                    if (this.Visible)
                    {
                        string statusMessage = recordAllFiles
                            ? "没有找到可记录的Excel文件"
                            : "当前没有激活的Excel文件或文件未保存";
                        UpdateStatus(statusMessage);
                    }
                    return 0;
                }
            }
            catch (Exception ex)
            {
                if (this.Visible)
                {
                    MessageBox.Show($"记录文件失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                else
                {
                    // 如果窗体未显示，则显示简单的消息框
                    MessageBox.Show($"记录当前文件失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                return -1;
            }
        }
    }
}