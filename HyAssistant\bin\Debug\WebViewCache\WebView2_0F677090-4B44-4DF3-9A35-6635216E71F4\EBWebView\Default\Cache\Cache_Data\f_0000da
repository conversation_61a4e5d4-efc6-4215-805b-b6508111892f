(function () {
    let tabledata=[];
    console.log("opposite end check...")
    let el = $(".resourceEntityImp");
    let table;
    let tableHead;
    let tableData;
    // 缆段落图复选框
    let ocsDm = el.find('#ocsDrawOnMap')
    let typeEl = el.find("#entityMajor");
    _bean.callService('ResourceTemplateImp', 'getEntityResources', [{type:"entityResources"}]).then(function (data) {
        for (var key in data ){
            var newOpt=document.createElement("option");
            newOpt.setAttribute("value",key);
            newOpt.setAttribute("text",data[key]);
            newOpt.innerHTML=data[key];
            if (typeEl[0].children.length==0){
                newOpt.setAttribute("selected",true);
                deviceEl.empty();
                _bean.callService('ResourceTemplateImp', 'getEntityDevice', [{deviceKey: key}]).then(function (data) {
                    for (var key in data ){
                        var newOpt=document.createElement("option");
                        newOpt.setAttribute("value",key);
                        newOpt.setAttribute("text",data[key]);
                        newOpt.innerHTML=data[key];
                        if (deviceEl[0].children.length==0){
                            newOpt.setAttribute("selected",true);
                        }
                        deviceEl.append(newOpt);
                    }
                })
            }
            typeEl.append(newOpt);
        }
    })
    typeEl.dropdown({on: 'hover', onChange: typeSelectChange});

    let deviceEl = el.find("#entityDevice");
    deviceEl.dropdown({on: 'hover', onChange: entityDeviceChange});

    let moduleEl = el.find("#localArea");
    moduleEl.dropdown({on: 'hover', });
    _bean.callService('ResourceTemplateImp', 'getLocalArea', []).then(function (data) {
        for (var key in data ) {
            var newOpt = document.createElement("option");
            newOpt.setAttribute("value", key);
            newOpt.setAttribute("text", data[key]);
            newOpt.innerHTML = data[key];
            if (moduleEl[0].children.length == 0) {
                newOpt.setAttribute("selected", true);
            }
            moduleEl.append(newOpt);
        }
    })
    let dataEl = $(".entityDataList");//初始化表单
    tableHead=[{label: '(校验/生成)结果', name: 'RESULT'},{label: '资源ID', name: 'RESOURCEID'}];
    table = new _sui.Table(dataEl, {
        // 设置字段
        columns: tableHead,
        // 设置数据
        data: null,
        // 不显示工具条
        showToolbar: false,
        // 是否多选
        rowCheckable: false
    })
    table.render()
    function typeSelectChange(value, text) {
        deviceEl.empty();
        _bean.callService('ResourceTemplateImp', 'getEntityDevice', [{deviceKey: value}]).then(function (data) {
            for (var key in data ){
                var newOpt=document.createElement("option");
                newOpt.setAttribute("value",key);
                newOpt.setAttribute("text",data[key]);
                newOpt.innerHTML=data[key];
                if (deviceEl[0].children.length==0){
                    newOpt.setAttribute("selected",true);
                }
                deviceEl.append(newOpt);
            }
            el.find("#ocsDrawOnMap").hide()
            ocsDm.checkbox("uncheck")
        })
    }

    function entityDeviceChange(value, text) {
        if(value === 'OCABLESECTION') {
            el.find("#ocsDrawOnMap").show()
        } else {
            el.find("#ocsDrawOnMap").hide()
        }
        // ocsDm.checkbox("set checked")
        ocsDm.checkbox("uncheck")
    }

    addListener();
    function addListener() {
        el.find("#restEntity").click(function (e) {
            dataEl.empty();
            e.preventDefault();
            debugger
            tableHead=[{label: '(校验/生成)结果', name: 'RESULT'},{label: '资源ID', name: 'RESOURCEID'}];
            table = new _sui.Table(dataEl, {
                // 设置字段
                columns: tableHead,
                // 设置数据
                data: null,
                // 不显示工具条
                showToolbar: false,
                // 是否多选
                rowCheckable: false
            })
            table.render()
        })
        el.find("#expTemplateBtn").click(function (e) {
            e.preventDefault();
            debugger;
            var deviceValue=deviceEl.dropdown('get value')//获取当前选择的值
            if (deviceValue){
                var moduleValue=moduleEl.dropdown('get value')
                _bean.callService('ResourceTemplateImp', 'exportEntityTemp', [{deviceValue: deviceValue,moduleValue:moduleValue}],{_showLoading: true}).then(function (rstr) {
                    if(rstr!="1"&&Object.keys(rstr).length>0) {
                        let alink = document.createElement("a");
                        console.log(base64ToByteArray(rstr.EXCETBYTES));
                        let blob = new Blob(base64ToByteArray(rstr.EXCETBYTES), {type: 'application/vnd.ms-excel'});
                        alink.download = rstr.TITLE;
                        alink.href = URL.createObjectURL(blob);
                        alink.click();
                    }
                })
            }
        })
        $('#impDataBtn').click(e=>{
            var deviceValue=deviceEl.dropdown('get value')//获取当前选择的值
            var moduleValue=moduleEl.dropdown('get value')
            $.requestUtil.importFile('ResourceTemplateImp', 'impTemplate', {deviceValue: deviceValue,moduleValue:moduleValue}, (res) => {
                debugger;
                if ((typeof res=='string')&&res.constructor==String){//返回字符串 就展示错误信息
                    alert(res);
                    return;
                }else if(res.length>0){
                    dataEl.empty();
                    let data=[];
                    data =res;
                    let ma=data[0];
                    tableHead=ma.head;
                    tableHead.unshift({label: '(校验/生成)结果', name: 'RESULT'},{label: '资源ID', name: 'RESOURCEID'});
                    data.splice(0,1);
                    tabledata=data;
                    table = new _sui.Table(dataEl, {
                        // 设置字段
                        columns: tableHead,
                        // 设置数据
                        data: tabledata,
                        // 不显示工具条
                        showToolbar: false,
                        // 显示行首序号
                        showRowNum: true,
                        // 是否多选
                        rowCheckable: true,
                        // 允许单元格编辑
                        allowedEdit: true
                    })
                    table.render()
                }
            })
        })
        el.find("#resetCheckBtn").click(function (e) {//重置校验
            debugger
            if (table.checkedData.length>0){
                for (let i = 0; i <table.checkedData.length ; i++) {
                    table.checkedData[i].RESULT="";
                    table.checkedData[i].RESOURCEID="";
                    table.checkedData[i].PASS=false;
                    table.redrawRow(table.checkedData[i]);
                }
            }
        })
        el.find("#dataCheckBtn").click(function (e) {//数据校验

            debugger
            var deviceValue=deviceEl.dropdown('get value')//获取当前选择的值
            var moduleValue=moduleEl.dropdown('get value')
            if(table.checkedData.length>0){
                var postData=table.checkedData;
                for (let i = 0; i <postData.length ; i++) {
                    postData[i].isIndex=i;
                    postData[i].RESULT="";
                    postData[i].RESOURCEID="";
                    postData[i].PASS=false;
                }
                _bean.callService('ResourceTemplateImp', 'checkImportResources', [{moduleValue:moduleValue,deviceValue: deviceValue,datas:postData}],{_showLoading: true}).then(function (rstr) {
                    debugger
                    for (let i = 0; i < rstr.length; i++) {
                        table.checkedData[rstr[i].isIndex].RESULT=rstr[i].RESULT;
                        table.checkedData[rstr[i].isIndex].RESOURCEID=rstr[i].RESOURCEID;
                        table.checkedData[rstr[i].isIndex].PASS=rstr[i].PASS;
                        table.redrawRow(table.checkedData[rstr[i].isIndex]);
                    }
                })
            }

        })
        el.find("#generateDataBtn").click(function (e) {//生成数据
            debugger
            const isOcsDropMap = ocsDm.checkbox("is checked")
            var deviceValue=deviceEl.dropdown('get value')//获取当前选择的值
            var moduleValue=moduleEl.dropdown('get value')
            if(table.checkedData.length>0){
                var sendData=[];
                var altterStr="";
                for (let i = 0; i < table.checkedData.length; i++) {
                    table.checkedData[i].isIndex=i;
                    if (table.checkedData[i].PASS){//校验通过数据进行存储 生成
                        if (deviceValue=="CHILDREN_HOLE"){
                            table.checkedData[i].ductSizeRadio=_context.getSetting('ductSizeRadio');//子孔的时候使用
                        }
                        sendData.push(table.checkedData[i]);
                    }else {//提示请先校验通过
                        altterStr=altterStr+"【"+table.checkedData[i].RESULT+"请先校验通过,再生成数据】"
                    }
                }
                if(altterStr.includes('【光缆/缆段牌号重复】')) {
                    // pms 74923 4、导入光缆、光缆段的牌号重复时只需弹出提示框“光缆/缆段牌号重复，是否继续保存”，可以许保存，参照正常录入时光缆牌号重复的提示。
                    _sui.confirm('光缆/缆段牌号存在重复记录，允许保存请点击是').then(isOk => {
                        // 允许重复，就从前端去掉牌号校验，手动再次执行数据生成
                        if(table.checkedData.length>0){
                            for (let i = 0; i < table.checkedData.length; i++) {
                                table.checkedData[i].RESULT = table.checkedData[i].RESULT.replace('【光缆/缆段牌号重复】', '')
                                if(table.checkedData[i].RESULT.trim().length === 0) {
                                    table.checkedData[i].RESULT = '【校验通过】'
                                    table.checkedData[i].PASS = true
                                }
                                // table.checkedData[i].RESULT = table.checkedData[i].RESULT.replace('【【光缆/缆段牌号重复】请先校验通过,再生成数据】', '')
                                table.redrawRow(table.checkedData[i])
                            }
                        }
                        el.find("#generateDataBtn").click()
                    })
                } else if (altterStr.length > 0) {
                    _sui.alert(altterStr);
                    return
                } else {
                    _bean.callService('ResourceTemplateImp', 'saveEntityObj', [{
                        ocsDropMap: isOcsDropMap,
                        moduleValue: moduleValue,
                        deviceValue: deviceValue,
                        datas: sendData
                    }], {_showLoading: true}).then(function (rstr) {
                        for (let i = 0; i < rstr.length; i++) {
                            table.checkedData[rstr[i].isIndex].RESULT = rstr[i].RESULT;
                            table.checkedData[rstr[i].isIndex].RESOURCEID = rstr[i].RESOURCEID;
                            table.checkedData[rstr[i].isIndex].PASS = rstr[i].PASS;
                            table.redrawRow(table.checkedData[rstr[i].isIndex]);
                        }

                    })
                }
            }

        })
        el.find("#attributeBtn").click(function (e) {//属性
            if(table.checkedData.length>0){
                if (table.checkedData.length==1){
                    if (table.checkedData[0].RESOURCEID.length>0) {
                        var deviceValue = deviceEl.dropdown('get value')//获取当前选择的值
                        _context.doAction({type: 'obj', name: 'modify'}, {
                            objectType: deviceValue,
                            id: table.checkedData[0].RESOURCEID
                        });
                    }else {
                        _sui.alert("无资源ID,请先生成数据");
                    }
                }else{
                    _sui.alert("你选择了多条数据,请选择一条数据");
                }
            }else{
                _sui.alert("请选择一条数据");
            }
        })
        el.find("#locationBtn").click(function (e) {//定位
            debugger
            if(table.checkedData.length>0){
                if (table.checkedData.length==1){
                    if (table.checkedData[0].RESOURCEID.length>0){
                        var deviceValue=deviceEl.dropdown('get value')//获取当前选择的值
                        $.mapOperation.locateOne(deviceValue, table.checkedData[0].RESOURCEID, true);
                    }else {
                        _sui.alert("无资源ID,请先生成数据");
                    }
                }else{
                    _sui.alert("你选择了多条数据,请选择一条数据再进行定位");
                }
            }else{
                _sui.alert("请选择一条数据再进行定位");
            }
        })
        el.find("#expListBtn").click(function (e) {//导出列表
            debugger
            if(table.checkedData.length>0){
                var isResoureId="true";
                for (let i = 0; i < table.checkedData.length; i++) {
                    if (table.checkedData[i].RESOURCEID.length==0){
                        isResoureId="false";
                    }
                }
                if (isResoureId=="true"){
                    _bean.callService('ResourceTemplateImp', 'exportExcelTable', [{columns:table.columns,datas:table.checkedData}],{_showLoading: true}).then(function (rstr) {
                        if(rstr!="1"&&Object.keys(rstr).length>0) {
                            let alink = document.createElement("a");
                            let blob = new Blob(base64ToByteArray(rstr.EXCETBYTES), {type: 'application/vnd.ms-excel'});
                            alink.download = rstr.TITLE;
                            alink.href = URL.createObjectURL(blob);
                            alink.click();
                        }
                    })
                }else{
                    _sui.alert("请选择生成之后的数据导出");
                }
            }else{
                _sui.alert("请选择一条数据再进行导出");
            }

        })
        el.find("#expHangResourceBtn").click(function (e) {//导出下挂资源
            if(table.checkedData.length>0){
                _bean.callService('ResourceTemplateImp', 'exportChildrenResources', [table.checkedData],{_showLoading: true}).then(function (rstr) {
                    if(rstr!="1"&&Object.keys(rstr).length>0) {
                        debugger
                        let alink = document.createElement("a");
                        let blob = new Blob(base64ToByteArray(rstr.EXCETBYTES), {type: 'application/vnd.ms-excel'});
                        alink.download = rstr.TITLE;
                        alink.href = URL.createObjectURL(blob);
                        alink.click();
                    }else if (rstr==""){
                        _sui.alert("无下挂资源");
                    }
                })
            }else{
                _sui.alert("请选择一条数据再进行导出");
            }
        })
        el.find("#getResourceInfoBtn").click(function (e) {//获取资源信息
            debugger
        })

    }
    function base64ToByteArray(base64String) {
        try {
            var sliceSize = 1024;
            var byteCharacters = atob(base64String);
            var bytesLength = byteCharacters.length;
            var slicesCount = Math.ceil(bytesLength / sliceSize);
            var byteArrays = new Array(slicesCount);

            for (var sliceIndex = 0; sliceIndex < slicesCount; ++sliceIndex) {
                var begin = sliceIndex * sliceSize;
                var end = Math.min(begin + sliceSize, bytesLength);

                var bytes = new Array(end - begin);
                for (var offset = begin, i = 0; offset < end; ++i, ++offset) {
                    bytes[i] = byteCharacters[offset].charCodeAt(0);
                }
                byteArrays[sliceIndex] = new Uint8Array(bytes);
            }
            return byteArrays;
        } catch (e) {
            console.log("Couldn't convert to byte array: " + e);
            return undefined;
        }
    }
})();