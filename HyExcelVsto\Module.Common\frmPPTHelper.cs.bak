﻿/*
 * ============================================================================
 * 功能模块：PPT辅助操作工具窗体
 * ============================================================================
 * 
 * 模块作用：提供PPT内容替换、生成表格、转换PDF等功能的辅助操作工具
 * 
 * 主要功能：
 * - 根据Excel数据替换PPT中的关键字
 * - 将PPT文件批量转换为PDF格式
 * - 支持多种PPT操作模式（替换、复制、删除等）
 * - 提供文件路径处理和批量操作功能
 * - 支持界面配置和历史记录管理
 * 
 * 执行逻辑：
 * 1. 初始化窗体界面和配置设置
 * 2. 处理用户选择的Excel数据范围
 * 3. 根据操作模式执行PPT关键字替换
 * 4. 批量处理PPT文件转换为PDF
 * 5. 管理界面控件状态和用户交互
 * 
 * 注意事项：
 * - 需要正确安装和配置PowerPoint应用程序
 * - 大量文件处理时需注意内存和性能问题
 * - 需要适当的文件系统读写权限
 * - 异常处理需要区分不同类型的异常并记录详细信息
 * ============================================================================
 */

using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.IO;
using System.Threading;
using System.Windows.Forms;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// PPT辅助操作工具窗体，提供PPT内容替换、生成表格、转换PDF等功能
    /// </summary>
    /// <remarks>
    /// 此窗体提供以下主要功能：
    /// 1. 根据Excel数据批量替换PPT中的关键字
    /// 2. 将PPT文件转换为PDF格式
    /// 3. 支持多种PPT操作模式和配置选项
    /// 4. 提供文件导入和批量处理功能
    /// 
    /// 使用场景：
    /// - 批量生成个性化PPT文档
    /// - 将PPT文档标准化转换为PDF格式
    /// - 自动化PPT内容更新和维护
    /// 
    /// 注意事项：
    /// - 需要安装PowerPoint应用程序支持
    /// - 大量文件处理时建议分批进行
    /// - 注意文件路径和权限设置
    /// </remarks>
    public partial class frmPPTHelper : Form
    {
        #region 替换PPT关键字功能

        /// <summary>
        /// 根据Excel数据替换PPT中的关键字
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 执行PPT关键字替换操作：
        /// 1. 保存模板文件路径历史记录
        /// 2. 获取用户选择的Excel数据范围
        /// 3. 根据用户选项确定操作模式
        /// 4. 调用PPT处理方法执行替换操作
        /// 5. 显示操作进度和完成状态
        /// </remarks>
        private void button替换PPT关键字_Click(object sender, EventArgs e)
        {
            ucFileSelectpptTemplatePath.SavePathHistoryToFile();

            // 获取可见范围的数据
            Range dataRange = ucExcelRangeSelectData1.SelectedRange.GetVisibleRange();
            if (dataRange == null)
                return;

            // 根据选项确定操作模式
            HHPowerPoint.PptOperationMode mode = GetOperationMode();

            // 执行替换操作
            HHPowerPoint.ReplaceTextInPptPerRows(
                ucFileSelectpptTemplatePath.Text,
                dataRange,
                textboxSavePath输出路径.Text,
                textBoxProgress,
                mode,
                comboBoxPage处理页面.Text.Trim());

            textBoxProgress.WriteLog($"{DateTime.Now:HH:mm:ss} 完成");
        }

        /// <summary>
        /// 根据用户选择的选项获取PPT操作模式
        /// </summary>
        /// <returns>PPT操作模式</returns>
        /// <remarks>
        /// 根据用户界面的复选框状态确定PPT操作模式：
        /// 1. 如果选中"复制前先把源文本删除"，返回DeleteAndCopyText模式
        /// 2. 如果选中"从模板复制新文本到原PPT"，返回MoveAndCopyText模式
        /// 3. 否则返回默认的ReplaceText模式
        /// </remarks>
        private HHPowerPoint.PptOperationMode GetOperationMode()
        {
            if (checkBox复制前先把源文本删除.Checked)
                return HHPowerPoint.PptOperationMode.DeleteAndCopyText;

            if (checkBox从模板复制新文本到原PPT.Checked)
                return HHPowerPoint.PptOperationMode.MoveAndCopyText;

            return HHPowerPoint.PptOperationMode.ReplaceText;
        }

        /// <summary>
        /// 处理复制模式选项变更
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 当用户更改复制模式选项时调用此方法：
        /// 1. 如果取消"从模板复制新文本到原PPT"选项，则取消"复制前先把源文本删除"选项
        /// 2. 根据主选项状态启用或禁用子选项
        /// </remarks>
        private void checkBox从模板复制新文本到原PPT_CheckedChanged(object sender, EventArgs e)
        {
            if (!checkBox从模板复制新文本到原PPT.Checked)
            {
                checkBox复制前先把源文本删除.Checked = false;
            }

            checkBox复制前先把源文本删除.Enabled = checkBox从模板复制新文本到原PPT.Checked;
        }

        #endregion 替换PPT关键字功能

        #region 转换为PDF功能

        /// <summary>
        /// 将PPT文件批量转换为PDF
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 执行PPT转PDF批量操作：
        /// 1. 获取用户选择的文件路径范围
        /// 2. 优化范围大小以提高处理效率
        /// 3. 调用线程方法执行转换操作
        /// 4. 显示操作进度和错误信息
        /// </remarks>
        private void button执行_转换为PDF_Click(object sender, EventArgs e)
        {
            // 获取文件路径范围
            Range filePathRange = ucERS文件路径_转换为PDF.SelectedRange?.OptimizeRangeSize();

            // 调用PPT转PDF的方法
            PptToPdfPerRowsThread(
                ds输出路径_转换为PDF.Text,
                ds原根路径_转换为PDF.Text,
                filePathRange,
                textBoxProgress,
                textBoxError);
        }

        /// <summary>
        /// 按行处理PPT转PDF的线程方法
        /// </summary>
        /// <param name="outputPath">输出路径</param>
        /// <param name="originalRootPath">原始根路径</param>
        /// <param name="filePathRange">文件路径范围</param>
        /// <param name="textBoxProgress">进度显示文本框</param>
        /// <param name="textBoxError">错误显示文本框</param>
        /// <remarks>
        /// 在后台线程中执行PPT转PDF操作：
        /// 1. 检查文件路径范围是否有效
        /// 2. 创建新线程执行转换操作
        /// 3. 处理范围内的所有文件路径
        /// 4. 捕获并记录转换过程中的异常
        /// </remarks>
        private void PptToPdfPerRowsThread(
            string outputPath,
            string originalRootPath,
            Range filePathRange,
            System.Windows.Forms.TextBox textBoxProgress,
            System.Windows.Forms.TextBox textBoxError)
        {
            if (filePathRange == null)
            {
                textBoxProgress.WriteLog($"{DateTime.Now:HH:mm:ss} 请选择文件路径所在的列");
                return;
            }

            // 在后台线程中执行转换操作
            Thread thread = new(
                () =>
                {
                    try
                    {
                        ProcessFilePathsInRange(filePathRange, outputPath, textBoxProgress, textBoxError);
                        textBoxProgress.WriteLog($"{DateTime.Now:HH:mm:ss} 所有文件处理完成");
                    }
                    catch (Exception ex)
                    {
                        textBoxError.WriteLog($"{DateTime.Now:HH:mm:ss} 转换过程中发生错误: {ex.Message}");
                    }
                });

            thread.Start();
        }

        /// <summary>
        /// 处理范围内的所有文件路径
        /// </summary>
        /// <param name="filePathRange">文件路径范围</param>
        /// <param name="outputPath">输出路径</param>
        /// <param name="textBoxProgress">进度显示文本框</param>
        /// <param name="textBoxError">错误显示文本框</param>
        /// <remarks>
        /// 遍历文件路径范围内的所有单元格：
        /// 1. 跳过隐藏行的单元格
        /// 2. 检查文件路径是否有效
        /// 3. 验证文件是否存在
        /// 4. 构建输出文件路径
        /// 5. 执行PPT转PDF操作
        /// 6. 记录处理进度和错误信息
        /// </remarks>
        private void ProcessFilePathsInRange(Range filePathRange, string outputPath,
            System.Windows.Forms.TextBox textBoxProgress, System.Windows.Forms.TextBox textBoxError)
        {
            foreach (Range cell in filePathRange.Cells)
            {
                // 跳过隐藏的行
                if (cell.EntireRow.Hidden)
                    continue;

                string filePath = cell.Value2?.ToString();
                if (string.IsNullOrEmpty(filePath))
                    continue;

                try
                {
                    if (!File.Exists(filePath))
                    {
                        // 获取当前行号和标题行行号
                        int currentRowNumber = ((Range)cell.Parent).Row;
                        int headerRowNumber = filePathRange.GetAutoFilterRowNumber();

                        if (currentRowNumber > headerRowNumber)
                        {
                            textBoxProgress.WriteLog($"{DateTime.Now:HH:mm:ss} 找不到文件: {filePath}");
                            continue;
                        }
                    }

                    // 构建输出文件路径
                    string outputFilePath = Path.Combine(
                        outputPath,
                        $"{Path.GetFileNameWithoutExtension(filePath)}.pdf");

                    // 执行PPT转PDF操作
                    ConvertPptToPdf(filePath, outputFilePath, textBoxProgress);

                    textBoxProgress.WriteLog($"{DateTime.Now:HH:mm:ss} 已处理: {filePath}");
                }
                catch (Exception ex)
                {
                    textBoxError.WriteLog(
                        $"{DateTime.Now:HH:mm:ss} 处理文件 {filePath} 时出错: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 将PPT转换为PDF
        /// </summary>
        /// <param name="inputPath">输入PPT文件路径</param>
        /// <param name="outputPath">输出PDF文件路径</param>
        /// <param name="textBoxProgress">进度显示文本框</param>
        /// <remarks>
        /// 使用PowerPoint COM对象将PPT文件转换为PDF：
        /// 1. 检查输入文件是否存在
        /// 2. 创建PowerPoint应用程序实例
        /// 3. 以只读模式打开PPT文件
        /// 4. 导出为PDF格式
        /// 5. 正确释放COM对象资源
        /// 6. 记录操作进度和错误信息
        /// </remarks>
        private void ConvertPptToPdf(string inputPath, string outputPath, System.Windows.Forms.TextBox textBoxProgress)
        {
            Microsoft.Office.Interop.PowerPoint.Application pptApplication = null;
            Microsoft.Office.Interop.PowerPoint.Presentation pptPresentation = null;

            try
            {
                // 判断PPT文件是否存在
                if (!File.Exists(inputPath))
                {
                    return;
                }

                // 创建PowerPoint应用程序实例
                pptApplication = new Microsoft.Office.Interop.PowerPoint.Application();

                // 打开PPT文件
                pptPresentation = pptApplication.Presentations
                    .Open(
                        inputPath,
                        Microsoft.Office.Core.MsoTriState.msoTrue,  // 只读模式
                        Microsoft.Office.Core.MsoTriState.msoFalse, // 无标题
                        Microsoft.Office.Core.MsoTriState.msoFalse  // 不显示窗口
                    );

                // 转换为PDF
                pptPresentation.ExportAsFixedFormat(
                    outputPath,
                    Microsoft.Office.Interop.PowerPoint.PpFixedFormatType.ppFixedFormatTypePDF);

                textBoxProgress.WriteLog($"{DateTime.Now:HH:mm:ss} 已将 {inputPath} 转换为PDF: {outputPath}");
            }
            catch (Exception ex)
            {
                textBoxProgress.WriteLog($"{DateTime.Now:HH:mm:ss} 转换 {inputPath} 时出错: {ex.Message}");
            }
            finally
            {
                // 清理资源
                if (pptPresentation != null)
                {
                    pptPresentation.Close();
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(pptPresentation);
                }

                if (pptApplication != null)
                {
                    pptApplication.Quit();
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(pptApplication);
                }
            }
        }

        #endregion 转换为PDF功能

        #region 界面控制

        /// <summary>
        /// 初始化PPT辅助工具窗体
        /// </summary>
        /// <remarks>
        /// 构造函数，初始化窗体组件和配置设置：
        /// 1. 初始化窗体界面组件
        /// 2. 绑定ComboBox历史记录管理
        /// </remarks>
        public frmPPTHelper()
        {
            InitializeComponent();

            // 绑定ComboBox历史记录管理
            ETForm.BindComboBox(comboBoxPage处理页面);
        }

        /// <summary>
        /// 窗体加载事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 窗体加载时执行初始化操作：
        /// 1. 设置初始Excel范围选择为当前选中区域
        /// 2. 绑定配置设置到控件
        /// </remarks>
        private void frmHelper_Load(object sender, EventArgs e)
        {
            // 设置初始Excel范围选择
            ucExcelRangeSelectData1.SelectedRange = ETExcelExtensions.GetSelectionRange();

            // 绑定配置设置
            BindConfigurationSettings();
        }

        /// <summary>
        /// 绑定所有控件到配置设置
        /// </summary>
        /// <remarks>
        /// 将窗体上的控件绑定到配置设置：
        /// 1. 绑定PPT模板文件选择控件
        /// 2. 绑定输出路径文本框
        /// 3. 绑定页面处理下拉框
        /// 4. 绑定PDF转换输出路径控件
        /// </remarks>
        private void BindConfigurationSettings()
        {
            string section = "PPT辅助填写";

            ETForm.BindWindowsFormControl(
                ucFileSelectpptTemplatePath,
                ThisAddIn.ConfigurationSettings,
                section,
                "ucFileSelectpptTemplatePath");

            ETForm.BindWindowsFormControl(
                textboxSavePath输出路径,
                ThisAddIn.ConfigurationSettings,
                section,
                "textboxSavePath输出路径");

            ETForm.BindWindowsFormControl(
                comboBoxPage处理页面,
                ThisAddIn.ConfigurationSettings,
                section,
                "comboBoxPage处理页面");

            ETForm.BindWindowsFormControl(
                ds输出路径_转换为PDF,
                ThisAddIn.ConfigurationSettings,
                section,
                "ds输出路径_转换为PDF");
        }

        /// <summary>
        /// Tab页切换事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 根据不同的Tab页设置splitContainer1的SplitterDistance：
        /// 1. 如果选择"替换PPT"页，调整分割器距离以适应选项控件
        /// 2. 如果选择"转换为PDF"页，调整分割器距离以适应路径控件
        /// </remarks>
        private void tabControl1_SelectedIndexChanged(object sender, EventArgs e)
        {
            const int addDistance = 46;

            if (tabControl1.SelectedTab == tabPage替换PPT) // 根据数据表替换PPT
            {
                splitContainer1.SplitterDistance = checkBox从模板复制新文本到原PPT.Top +
                    checkBox从模板复制新文本到原PPT.Height +
                    addDistance;
            }
            else if (tabControl1.SelectedTab == tabPage转换为PDF) // 转换为PDF
            {
                splitContainer1.SplitterDistance = ds原根路径_转换为PDF.Top + ds原根路径_转换为PDF.Height + addDistance;
            }
        }

        #endregion 界面控制

        #region 文件导入

        /// <summary>
        /// 导入文件清单按钮点击事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 响应导入文件清单按钮点击事件：
        /// 1. 创建并打开文件操作窗体
        /// 2. 设置文件操作窗体的初始状态
        /// 3. 配置文件导入选项（文件、子目录等）
        /// 4. 触发添加文件夹操作
        /// </remarks>
        private void button导入文件清单_Click(object sender, EventArgs e)
        {
            // 创建并打开文件操作窗体
            frm文件操作 frm = new(false);
            ThisAddIn.OpenForm(frm);

            // 设置文件操作窗体的初始状态
            frm.tabControl1.SelectedTab = frm.tabPage导入文件名;
            frm.checkBox导入文件_文件.Checked = true;
            frm.checkBox含子目录.Checked = true;
            frm.checkBox导入文件_文件夹.Checked = false;

            // 触发添加文件夹操作
            frm.导入文件_添加文件夹ToolStripMenuItem_Click(null, null);
        }

        #endregion 文件导入
    }
}