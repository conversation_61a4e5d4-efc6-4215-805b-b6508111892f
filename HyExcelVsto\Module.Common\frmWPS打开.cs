﻿/*
 * ============================================================================
 * 功能模块：WPS与Excel应用程序切换窗体
 * ============================================================================
 * 
 * 模块作用：提供在WPS Office和Microsoft Excel之间切换打开文件的用户界面
 * 
 * 主要功能：
 * - 应用程序检测：自动检测当前运行的是WPS还是Excel
 * - 文件切换：关闭当前文件并用另一个应用程序重新打开
 * - 保存选项：提供保存或不保存当前更改的选择
 * - 界面适配：根据当前应用程序动态调整界面显示
 * 
 * 执行逻辑：
 * 1. 窗体加载时检测当前应用程序类型（WPS/Excel）
 * 2. 验证当前工作簿的可用性和保存状态
 * 3. 根据应用程序类型显示相应的操作按钮
 * 4. 用户选择保存选项后关闭当前文件
 * 5. 使用目标应用程序重新打开文件
 * 
 * 注意事项：
 * - 需要同时安装WPS Office和Microsoft Excel
 * - 文件必须已保存且路径有效才能进行切换
 * - 切换过程中会关闭当前应用程序中的文件
 * ============================================================================
 */

using ET;
using System;
using System.Windows.Forms;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// WPS与Excel应用程序切换窗体
    /// 提供在WPS Office和Microsoft Excel之间切换打开文件的功能
    /// </summary>
    /// <remarks>
    /// 此窗体用于处理在WPS和Excel之间切换打开文件的操作，
    /// 支持保存选项和应用程序自动检测功能
    /// </remarks>
    public partial class frmWpsExce切换 : Form
    {
        /// <summary>
        /// 初始化WPS与Excel切换窗体
        /// </summary>
        public frmWpsExce切换()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 窗体加载事件处理程序，初始化界面并检测应用程序类型
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 执行逻辑：
        /// 1. 检查当前工作簿是否可用
        /// 2. 调整界面控件位置
        /// 3. 检测当前运行的应用程序类型
        /// 4. 根据应用程序类型显示相应的操作界面
        /// </remarks>
        void frmWPS打开_Load(object sender, EventArgs e)
        {
            try
            {
                // 检查当前工作簿是否可用，如果不可用则关闭窗体
                if (!CheckWorkbookIsAvailable())
                {
                    MessageBox.Show(@"文件未保存或者不存在。", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    Close();
                    return;
                }

                // 调整GroupBox控件位置使其对齐
                AdjustGroupBoxPosition(groupBoxExcel, groupBoxWPS);

                // 检测当前运行的是否为WPS应用程序
                bool isWpsApplication = ETExcelExtensions.IsWPS();
                // 根据应用程序类型显示相应的操作界面
                groupBoxWPS.Visible = !isWpsApplication;    // 当前是Excel时显示WPS选项
                groupBoxExcel.Visible = isWpsApplication;   // 当前是WPS时显示Excel选项
            }
            catch (Exception ex)
            {
                throw new ETException("窗体加载失败", ex);
            }
        }

        /// <summary>
        /// 检查当前工作簿是否可用且已保存
        /// </summary>
        /// <returns>如果工作簿存在且路径有效返回true，否则返回false</returns>
        /// <remarks>
        /// 验证条件：
        /// - 活动工作簿不为空
        /// - 工作簿具有有效的完整路径（包含目录分隔符）
        /// 这确保了文件已经保存到磁盘上，而不是临时的新建文件
        /// </remarks>
        bool CheckWorkbookIsAvailable()
        {
            try
            {
                // 获取当前活动的工作簿
                Microsoft.Office.Interop.Excel.Workbook activeWorkbook = ThisAddIn.ExcelApplication.ActiveWorkbook;
                // 检查工作簿是否存在且具有有效路径（路径包含目录分隔符表示已保存）
                return activeWorkbook != null && activeWorkbook.FullName.Split('\\').Length > 1;
            }
            catch (Exception ex)
            {
                throw new ETException("检查工作簿状态失败", ex);
            }
        }

        /// <summary>
        /// 关闭当前工作簿并使用目标应用程序重新打开
        /// </summary>
        /// <param name="saveChanges">是否保存当前更改</param>
        /// <param name="workbookPath">要重新打开的工作簿完整路径</param>
        /// <remarks>
        /// 执行逻辑：
        /// 1. 验证工作簿可用性
        /// 2. 关闭当前工作簿（根据saveChanges参数决定是否保存）
        /// 3. 根据当前应用程序类型选择目标应用程序：
        ///    - 当前是Excel → 使用WPS打开
        ///    - 当前是WPS → 使用Excel打开
        /// 4. 关闭切换窗体
        /// </remarks>
        void CloseAndReopenWorkbook(bool saveChanges, string workbookPath)
        {
            try
            {
                // 再次检查工作簿可用性
                if (!CheckWorkbookIsAvailable()) return;

                // 获取当前工作簿并关闭
                Microsoft.Office.Interop.Excel.Workbook currentWorkbook = ThisAddIn.ExcelApplication.ActiveWorkbook;
                currentWorkbook.Close(saveChanges);

                // 根据当前应用程序类型选择目标应用程序打开文件
                if (!ETExcelExtensions.IsWPS())
                    // 当前是Excel，使用WPS打开
                    ETExcelExtensions.OpenFileByWps(workbookPath);
                else
                    // 当前是WPS，使用默认Office（Excel）打开
                    ETExcelExtensions.OpenFileByDefaultOffice(workbookPath);

                // 关闭切换窗体
                Close();
            }
            catch (Exception ex)
            {
                throw new ETException("关闭并重新打开工作簿失败", ex);
            }
        }

        /// <summary>
        /// 调整两个GroupBox控件的位置使其对齐
        /// </summary>
        /// <param name="targetGroupBox">要调整位置的目标GroupBox控件</param>
        /// <param name="sourceGroupBox">作为参考位置的源GroupBox控件</param>
        /// <remarks>
        /// 将目标GroupBox的Top和Left属性设置为与源GroupBox相同，
        /// 确保两个控件在界面上占据相同的位置
        /// </remarks>
        void AdjustGroupBoxPosition(GroupBox targetGroupBox, GroupBox sourceGroupBox)
        {
            // 设置目标控件的垂直位置
            targetGroupBox.Top = sourceGroupBox.Top;
            // 设置目标控件的水平位置
            targetGroupBox.Left = sourceGroupBox.Left;
        }

        /// <summary>
        /// 处理按钮点击事件的通用方法
        /// </summary>
        /// <param name="saveChanges">是否保存当前更改</param>
        /// <remarks>
        /// 统一处理所有切换按钮的点击事件，
        /// 获取当前工作簿路径并执行应用程序切换操作
        /// </remarks>
        void HandleButtonClick(bool saveChanges)
        {
            try
            {
                // 获取当前活动工作簿的完整路径
                string workbookPath = ThisAddIn.ExcelApplication.ActiveWorkbook.FullName;
                // 执行关闭并重新打开操作
                CloseAndReopenWorkbook(saveChanges, workbookPath);
            }
            catch (Exception ex)
            {
                throw new ETException("处理按钮点击事件失败", ex);
            }
        }

        /// <summary>
        /// WPS组保存后关闭按钮点击事件处理程序
        /// </summary>
        void button保存后关闭_WPS_Click(object sender, EventArgs e)
        {
            HandleButtonClick(true);
        }

        /// <summary>
        /// WPS组不保存并关闭按钮点击事件处理程序
        /// </summary>
        void button不保存并关闭_WPS_Click(object sender, EventArgs e)
        {
            HandleButtonClick(false);
        }

        /// <summary>
        /// Excel组保存后关闭按钮点击事件处理程序
        /// </summary>
        void button保存后关闭_Excel_Click(object sender, EventArgs e)
        {
            HandleButtonClick(true);
        }

        /// <summary>
        /// Excel组不保存并关闭按钮点击事件处理程序
        /// </summary>
        void button不保存并关闭_Excel_Click(object sender, EventArgs e)
        {
            HandleButtonClick(false);
        }
    }
}