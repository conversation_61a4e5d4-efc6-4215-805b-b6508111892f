using HyAssistant.ChinaTowerDownload.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HyAssistant.ChinaTowerDownload.Data
{
    /// <summary>
    /// 站点信息数据访问接口
    /// </summary>
    public interface IStationRepository
    {
        /// <summary>
        /// 异步获取所有站点的摘要信息列表。
        /// </summary>
        /// <returns>一个表示异步操作的任务，其结果是包含所有站点摘要信息的 <see cref="T:System.Collections.Generic.List`1"/> 集合。如果没有任何站点，则返回一个空列表。</returns>
        Task<List<StationInfoExcerpt>> GetAllStationsAsync();

        /// <summary>
        /// 根据提供的关键词异步搜索站点信息。搜索会匹配站点编码或站点名称。
        /// </summary>
        /// <param name="keyword">用于搜索的关键词，可以是站点编码或站点名称的一部分。</param>
        /// <returns>一个表示异步操作的任务，其结果是包含匹配站点摘要信息的 <see cref="T:System.Collections.Generic.List`1"/> 集合。如果没有找到匹配的站点，则返回一个空列表。</returns>
        Task<List<StationInfoExcerpt>> SearchStationsAsync(string keyword);

        /// <summary>
        /// 根据给定的站点编码异步获取站点的详细信息。
        /// </summary>
        /// <param name="stationCode">要查询的站点的唯一编码。</param>
        /// <returns>一个表示异步操作的任务，其结果是 <see cref="T:HyAssistant.ChinaTowerDownload.Models.StationInfoExcerpt"/> 对象，包含指定编码的站点信息。如果未找到匹配的站点，则返回 <c>null</c>。</returns>
        Task<StationInfoExcerpt> GetStationByCodeAsync(string stationCode);

        /// <summary>
        /// 根据给定的站点ID异步获取站点的详细信息。
        /// </summary>
        /// <param name="stationId">要查询的站点的唯一标识符。</param>
        /// <returns>一个表示异步操作的任务，其结果是 <see cref="T:HyAssistant.ChinaTowerDownload.Models.StationInfoExcerpt"/> 对象，包含指定ID的站点信息。如果未找到匹配的站点，则返回 <c>null</c>。</returns>
        Task<StationInfoExcerpt> GetStationByIdAsync(string stationId);

        /// <summary>
        /// 异步批量插入站点摘要信息到数据存储中。
        /// </summary>
        /// <param name="stations">要插入的站点摘要信息 <see cref="T:System.Collections.Generic.List`1"/> 集合。</param>
        /// <returns>一个表示异步操作的任务，其结果是成功插入的记录数量。</returns>
        Task<int> InsertStationsAsync(List<StationInfoExcerpt> stations);

        /// <summary>
        /// 异步更新指定站点的最后抓取时间。
        /// </summary>
        /// <param name="stationId">要更新的站点的唯一标识符。</param>
        /// <param name="lastCrawlTime">站点的最新抓取时间戳。</param>
        /// <returns>一个表示异步操作的任务，其结果为一个布尔值，指示最后抓取时间是否成功更新。</returns>
        Task<bool> UpdateLastCrawlTimeAsync(string stationId, long lastCrawlTime);

        /// <summary>
        /// 异步获取数据存储中的站点总数。
        /// </summary>
        /// <returns>一个表示异步操作的任务，其结果是当前数据存储中的站点总数。</returns>
        Task<int> GetStationCountAsync();

        /// <summary>
        /// 异步检查具有指定站点ID的站点是否存在于数据存储中。
        /// </summary>
        /// <param name="stationId">要检查的站点的唯一标识符。</param>
        /// <returns>一个表示异步操作的任务，其结果为一个布尔值，如果站点存在则为 <c>true</c>，否则为 <c>false</c>。</returns>
        Task<bool> StationExistsAsync(string stationId);

        /// <summary>
        /// 异步删除指定站点ID的站点信息。
        /// </summary>
        /// <param name="stationId">要删除的站点的唯一标识符。</param>
        /// <returns>一个表示异步操作的任务，其结果为一个布尔值，指示站点信息是否成功删除。</returns>
        Task<bool> DeleteStationAsync(string stationId);

        /// <summary>
        /// 异步初始化数据存储的表结构，确保所有必要的表都已创建。
        /// </summary>
        /// <returns>一个表示异步操作的任务，其结果为一个布尔值，指示数据库表结构是否成功初始化。</returns>
        Task<bool> InitializeDatabaseAsync();
    }
}
