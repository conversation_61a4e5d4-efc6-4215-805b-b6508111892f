var _map;
(function (_map) {
    _map.EVENT_MAP = 'EVENT_MAP';
    _map.EVENT_MAP_CONFIG_LOADED = 'EVENT_MAP_CONFIG_LOADED';
    _map.EVENT_MAP_CONTEXT_MENU = 'EVENT_MAP_CONTEXT_MENU';
    _map.EVENT_MAP_CREATED = 'EVENT_MAP_CREATED';
    _map.EVENT_SHAPE_UPDATED = 'EVENT_SHAPE_UPDATED';
    _map.EVENT_FEATURE_SELECTED = 'EVENT_FEATURE_SELECTED';
    _map.EVENT_MAP_REFRESH = 'EVENT_MAP_REFRESH';
    _map.EVENT_BATCH_MOVE_END = 'EVENT_BATCH_MOVE_END';
    _map.FORCE_CANVAS = true;
    _map.layerFactory = {};
    L.Icon.Default.imagePath = 'lib/leaflet/images/';
    L.Browser.touch = L.Browser.touch && L.Browser.mobile;
    var global = window;
    global._locale = global._locale || { map: {} };
    L.Map.addInitHook(function () {
        var that = this, h;
        if (that.on) {
            that.on('click', check_later);
            that.on('dblclick', function () {
                setTimeout(clear_h, 0);
            });
        }
        function check_later(e) {
            clear_h();
            h = setTimeout(check, 300);
            function check() {
                that.fire('singleclick', L.Util.extend(e, { type: 'singleclick' }));
            }
        }
        function clear_h() {
            if (h != null) {
                clearTimeout(h);
                h = null;
            }
        }
    });
    L.Circle.SECTIONS_COUNT = 64;
    L.Circle.toPolygon = function (circle, vertices, map) {
        map = map || circle._map || _context.map.lmap;
        var points = [], crs = map.options.crs, DOUBLE_PI = Math.PI * 2, angle = 0.0, projectedCentroid, radius, radiusY, point, project, unproject;
        if (crs.distance === L.CRS.Earth.distance) {
            project = map.latLngToLayerPoint.bind(map);
            unproject = map.layerPointToLatLng.bind(map);
            radius = circle._radius;
            radiusY = circle._radiusY;
        }
        else {
            project = crs.projection.project.bind(crs.projection);
            unproject = crs.projection.unproject.bind(crs.projection);
            radius = circle._mRadius;
            radiusY = circle._mRadiusY;
        }
        radiusY = radiusY || radius;
        projectedCentroid = project(circle._latlng);
        vertices = vertices || L.Circle.SECTIONS_COUNT;
        for (var i = 0; i < vertices; i++) {
            angle -= (DOUBLE_PI / vertices);
            point = new L.Point(projectedCentroid.x + (radius * Math.cos(angle)), projectedCentroid.y + (radiusY * Math.sin(angle)));
            if (i > 0 && point.equals(points[i - 1])) {
                continue;
            }
            points.push(unproject(point));
        }
        return points;
    };
    L.Circle.prototype.toPolygon = function (vertices, map) {
        return L.Circle.toPolygon(this, vertices, map || this._map);
    };
    function toWkt(layer) {
        if (layer instanceof L.Marker)
            layer = layer.getLatLng();
        if (layer.lat !== undefined && layer.lng !== undefined)
            return "POINT(" + layer.lng + " " + layer.lat + ")";
        var latlngs, lng, lat, coords = [], isPolygon;
        if (layer instanceof L.Polygon) {
            isPolygon = true;
            latlngs = layer.getLatLngs()[0];
            if (latlngs && latlngs.length == 1 && latlngs[0] && Array.isArray(latlngs[0])) {
                latlngs = latlngs[0];
            }
        }
        else if (layer instanceof L.Circle) {
            isPolygon = true;
            latlngs = layer.toPolygon();
        }
        else
            latlngs = layer.getLatLngs();
        for (var i = 0; i < latlngs.length; i++) {
            coords.push(latlngs[i].lng + " " + latlngs[i].lat);
            if (i === 0) {
                lng = latlngs[i].lng;
                lat = latlngs[i].lat;
            }
        }
        ;
        if (isPolygon) {
            return "POLYGON((" + coords.join(",") + "," + lng + " " + lat + "))";
        }
        else if (layer instanceof L.Polyline) {
            return "LINESTRING(" + coords.join(",") + ")";
        }
    }
    _map.toWkt = toWkt;
    var MouseCoordControl = L.Control.extend({
        options: { position: 'bottomright' },
        onAdd: function (map) {
            var div = L.DomUtil.create('div');
            div.style.marginRight = '50px';
            div.style.opacity = '0.8';
            div.style.fontWeight = 'bold';
            var num;
            if (map.options.mouseCoordsFixNum !== undefined)
                num = parseInt(map.options.mouseCoordsFixNum);
            map.on('mousemove', function (e) {
                var x = e.latlng.lng, y = e.latlng.lat;
                if (num !== undefined) {
                    x = x.toFixed(num);
                    y = y.toFixed(num);
                }
                div.innerHTML = x + '&nbsp;&nbsp;' + y;
            });
            return div;
        }
    });
    var ObjectsLayer = L.Layer.extend({
        options: {
            updateInterval: 800,
            mapTypes: [], render: null,
            baseParams: null
        },
        pixiLayer: null, pixiLoader: null, pixiStage: null,
        objs: {},
        pixiUtils: null, pixiResources: null,
        lastMapLevel: -1, lastMapCenter: null,
        preloadIcons: [],
        queryThreadsNum: 4,
        preparePromise: null,
        ignoreSelectableFilter: false,
        tileExcludeLevel: 5,
        canTile: undefined,
        mtsByObjectType: {},
        selectTypesFilter: null,
        initialize: function (options) {
            var _this = this;
            L.Util.setOptions(this, options);
            if (PIXI.Loader)
                this.pixiLoader = PIXI.Loader.shared;
            if (!this.pixiLoader)
                this.pixiLoader = new PIXI.loaders.Loader();
            this.pixiStage = new PIXI.Container({ interactive: false, interactiveChildren: false });
            this.objs = {};
            var icons = this.preloadIcons;
            this.options.mapTypes.forEach(function (mt) {
                if (mt.icon && icons.indexOf(mt.icon) === -1)
                    icons.push(mt.icon);
                _this.addMapType(mt);
            });
            icons.forEach(function (i) { return _this.pixiLoader.add(i); });
            this.resizeListener = function (e) {
                _this.refreshGraphics();
            };
            if (this.options.queryThreadsNum)
                this.queryThreadsNum = parseInt(this.options.queryThreadsNum);
            if (this.options.tileExcludeLevel)
                this.tileExcludeLevel = parseInt(this.options.tileExcludeLevel);
            if (this.options.canTile !== undefined)
                this.canTile = this.options.canTile;
            if (this.options.selectTypesFilter !== undefined)
                this.selectTypesFilter = this.options.selectTypesFilter;
        },
        addMapType: function (mt) {
            if (this.options.mapTypes.indexOf(mt) === -1)
                this.options.mapTypes.push(mt);
            if (!mt.id) {
                var idx = this.options.mapTypes.filter(function (m) { return m.objectType === mt.objectType; }).findIndex(function (m) { return m === mt; });
                mt.id = idx === 0 ? mt.objectType : mt.objectType + idx;
            }
            var def = mt, lo = this.options;
            function attr(name, defaul, os) {
                if (defaul === void 0) { defaul = null; }
                if (os === void 0) { os = null; }
                var val;
                if (Array.isArray(name))
                    name.some(function (n) {
                        val = attr(n, null, os);
                        return !(!val && val !== 0 && val !== false);
                    });
                else {
                    if (os)
                        val = os[name];
                    if (!val && val !== 0 && val !== false)
                        val = def[name];
                    if (!val && val !== 0 && val !== false)
                        val = lo[name];
                }
                if (!val && val !== 0 && val !== false)
                    val = defaul;
                return val;
            }
            function intattr(name, defaul, os) {
                if (os === void 0) { os = null; }
                var val = attr(name, null, os);
                return val !== undefined && val !== null ? parseInt(val) : defaul;
            }
            function floatattr(name, defaul, os) {
                if (os === void 0) { os = null; }
                var val = attr(name, null, os);
                return val !== undefined && val !== null ? parseFloat(val) : defaul;
            }
            var pixiContainer = new PIXI.Container();
            this.pixiStage.addChild(pixiContainer);
            this.objs[mt.id] = { graphics: [], graphicsById: {}, def: mt, container: pixiContainer };
            if (!mt.where && mt.whereJson)
                mt.where = JSON.parse(mt.whereJson);
            mt.minZoom = intattr('minZoom', null);
            mt.maxZoom = intattr('maxZoom', null);
            mt.fill = intattr(['fill', 'color'], 0);
            mt.lineColor = intattr(['lineColor', 'color'], 0);
            mt.lineWidth = intattr('lineWidth', 1);
            mt.alpha = floatattr('alpha', 1);
            mt.labelField = attr('labelField', 'label');
            mt.labelStyle = attr('labelStyle') || attr('labelStyles');
            if (mt.labelStyle) {
                if (Array.isArray(mt.labelStyle))
                    mt.labelStyle = mt.labelStyle[0];
                if (typeof mt.labelStyle.strokeThickness === 'string')
                    mt.labelStyle.strokeThickness = parseFloat(mt.labelStyle.strokeThickness);
                if (typeof mt.labelStyle.fontSize === 'string')
                    mt.labelStyle.fontSize = parseFloat(mt.labelStyle.fontSize);
            }
            mt.labelMinLevel = intattr(['labelMinLevel', 'labelMinZoom'], undefined);
            mt.labelMaxLevel = intattr(['labelMaxLevel', 'labelMaxZoom'], undefined);
            mt.attr = attr;
            mt.intattr = intattr;
            mt.floatattr = floatattr;
            mt.objectLayerInstance = this;
        },
        removeMapType: function (mt) {
            var id = mt;
            if (typeof mt === 'object')
                id = mt.id;
            var pos = this.options.mapTypes.findIndex(function (m) { return m.id == id; });
            if (pos !== -1) {
                this.setMapTypeVisible(id, false);
                this.options.mapTypes.splice(pos, 1);
                delete this.objs[id];
            }
        },
        getMapTypeById: function (id) {
            return this.options.mapTypes.find(function (m) { return m.id === id; });
        },
        getMapType: function (objectType) {
            return this.options.mapTypes.find(function (m) { return m.objectType === objectType; });
        },
        hasMapType: function (objectType) {
            return !!this.getMapType(objectType);
        },
        getMapTypes: function () {
            return this.options.mapTypes;
        },
        isMapTypeVisible: function (mt) {
            if (typeof mt === 'string')
                mt = this.getMapTypeById(mt);
            return mt.visible !== false;
        },
        isObjectTypeVisible: function (objectType) {
            var o = typeof objectType === 'string' ? { objectType: objectType } : objectType;
            var mt = o.id ? this.getMapTypeById(o.id) : this.getMapType(o.objectType);
            return mt && mt.visible !== false;
        },
        setMapTypeVisible: function (mt, v) {
            var _this = this;
            if (v === void 0) { v = true; }
            if (typeof mt === 'string')
                mt = this.getMapTypeById(mt);
            if (typeof mt.doSetVisible === 'function') {
                v = mt.doSetVisible(v);
                if (v === undefined)
                    return;
            }
            var os = this.objs[mt.id];
            os.container.visible = v;
            os.def.visible = v;
            if (typeof mt.labelField === 'function')
                os.graphics.forEach(function (g) {
                    mt.labelField(g.geojson, v, os.def, _this);
                });
            if (this.pixiUtils)
                this.pixiUtils.getRenderer().render(this.pixiStage);
            if (v && this.pixiUtils) {
                this.doLoad(this.pixiUtils, this.pixiResources);
            }
            if (!v) {
                this.clearTypeGraphics(mt.id);
            }
        },
        setObjectTypeVisible: function (objectType, v) {
            if (v === void 0) { v = true; }
            var mt = this.getMapType(objectType);
            this.setMapTypeVisible(mt, v);
        },
        saveMapTypeVisible: function (mt, v) {
            var _this = this;
            if (v === void 0) { v = true; }
            if (typeof mt === 'string')
                mt = this.getMapTypeById(mt);
            this.setMapTypeVisible(mt, v);
            if (this._maptypeStorageTimer)
                clearTimeout(this._maptypeStorageTimer);
            var user = _context.getUser(true);
            if (user)
                this._maptypeStorageTimer = setTimeout(function () {
                    var mts = {};
                    _this.options.mapTypes.forEach(function (mt) {
                        mts[mt.id] = _this.isMapTypeVisible(mt);
                    });
                    _util.setStorage('MapTypeVisible-' + user.id, mts, true);
                }, 2000);
        },
        saveObjectTypeVisible: function (objectType, v) {
            if (v === void 0) { v = true; }
            var mt = this.getMapType(objectType);
            this.saveMapTypeVisible(mt, v);
        },
        onRemove: function (map) {
            if (this.pixiLayer) {
                this.pixiLayer.remove();
            }
            map.off('resize', this.resizeListener);
        },
        onAdd: function (map) {
            var _this = this;
            var pixilayer = this.pixiLayer, maxZoom = map.getMaxZoom(), self = this;
            if (!this.pixiLayer) {
                var self = this;
                function init(resources) {
                    if (self.preparePromise)
                        self.preparePromise.then(function () { return _init(resources); }, function () { return _init(resources); });
                    else
                        _init(resources);
                }
                var doLoadCallback = _util.delayFn(self.doLoad, this.options.updateInterval, self);
                function _init(resources) {
                    pixilayer = self.pixiLayer = L.pixiLayer(function (utils) {
                        self.doLoad(utils, null, true);
                        doLoadCallback(utils, null);
                    }, self.pixiStage, { forceCanvas: _map.FORCE_CANVAS }).addTo(map);
                    maxZoom = map.getMaxZoom();
                }
                if (this.preloadIcons.length)
                    this.pixiLoader.load(function (loader, resources) {
                        init(resources);
                    });
                else
                    init(null);
            }
            else
                this.pixiLayer.addTo(map);
            map.on('resize', this.resizeListener);
            function readd() {
                var reload = false;
                if (map.getMaxZoom() !== maxZoom && pixilayer) {
                    pixilayer.remove();
                    map.off('layeradd', readd);
                    pixilayer.addTo(map);
                    reload = true;
                }
                self.redraw(reload);
            }
            map.on('layeradd', readd);
            var user = _context.getUser(true);
            if (this.options.websocket === true && !this.stompClient && user && window['StompJs']) {
                this.req_instance_id = map.id + '-' + new Date().getTime();
                var StompJs = window['StompJs'];
                var wsUrl = this.options.websocketUrl;
                if (!wsUrl) {
                    wsUrl = (location.protocol == 'https:' ? 'wss:' : 'ws:') + '//' + location.host + '/';
                    var dataUrl = _bean.getDataRootUrl();
                    if (dataUrl)
                        wsUrl = dataUrl.replace('http', 'ws');
                    wsUrl += 'g5message';
                }
                try {
                    var client_1 = this.stompClient = new StompJs.Client({
                        brokerURL: wsUrl,
                        reconnectDelay: 15000,
                    });
                    client_1.onConnect = function () {
                        var userName = user[_this.options.userNameField || 'USERNAME'];
                        client_1.subscribe('/user/' + userName + '/geojson/response', function (e) {
                            var body = JSON.parse(e.body), mapTypeId = body.mapTypeId || body.objectType, mt = _this.getMapTypeById(mapTypeId);
                            if (mt && body.features.length && body.requestInstanceId === _this.req_instance_id) {
                                requestAnimationFrame(function () {
                                    _this.dealFeatures(_this.objs[mt.id], body.features);
                                    _this._askRefreshGraphs();
                                });
                            }
                        });
                    };
                    client_1.onStompError = function (frame) {
                        console.log('Broker reported error: ' + frame.headers['message']);
                        console.log('Additional details: ' + frame.body);
                        client_1.deactivate();
                        this.stompClient = null;
                    };
                    client_1.onWebSocketError = function (e) {
                        console.error(e);
                    };
                    client_1.activate();
                }
                catch (e) {
                    console.error(e);
                }
            }
        },
        _graphRefreshTime: null,
        _askRefreshGraphs: function () {
            var _this = this;
            clearTimeout(this._graphRefreshTime);
            this._graphRefreshTime = setTimeout(function () {
                requestAnimationFrame(function () { return _this.refreshGraphics(); });
            }, 800);
        },
        clear: function () {
            this.lastMapLevel = -1;
            this.lastMapCenter = null;
            for (var k in this.objs) {
                this.clearTypeGraphics(k);
            }
            if (this.pixiUtils)
                this.pixiUtils.getRenderer().render(this.pixiStage);
        },
        clearTypeGraphics: function (mapTypeId) {
            var _this = this;
            var o = this.objs[mapTypeId];
            delete o.graphicsById;
            delete o.lastQueryBbox;
            if (typeof o.def.labelField === 'function') {
                o.graphics.forEach(function (g) {
                    o.def.labelField(g.geojson, false, o.def, _this);
                    g.displayObject.destroy();
                });
            }
            delete o.graphics;
            o.graphics = [];
            o.graphicsById = {};
            o.container.removeChildren();
        },
        redraw: function (load) {
            if (load === void 0) { load = true; }
            if (load) {
                this.clear();
                if (this.pixiUtils)
                    this.doLoad(this.pixiUtils, this.pixiResources);
            }
            else
                this.refreshGraphics(true);
        },
        refreshStageTimer: null,
        refreshStage: function () {
            if (this.pixiUtils) {
                clearTimeout(this.refreshStageTimer);
                var thiz_1 = this;
                thiz_1.refreshStageTimer = setTimeout(function () {
                    thiz_1.pixiUtils.getRenderer().render(thiz_1.pixiStage);
                }, 30);
            }
        },
        refreshGraphics: function (force, justRevisible) {
            if (force === void 0) { force = false; }
            if (justRevisible === void 0) { justRevisible = false; }
            if (!this.pixiUtils || !this._map)
                return;
            this._labelxys = {};
            var map = this._map, zoom = map.getZoom();
            var _loop_1 = function (k) {
                var mt = this_1.objs[k], def = mt.def;
                var layerVisible = this_1.isMapTypeVisible(def);
                if (def.minZoom && zoom < def.minZoom)
                    layerVisible = false;
                if (layerVisible && def.maxZoom && zoom > def.maxZoom)
                    layerVisible = false;
                mt.container.visible = layerVisible;
                if (layerVisible && !justRevisible) {
                    var labelVisible_1 = this_1.isLabelVisible(k, def);
                    var xb_1, yb_1, xyb = def.labelBlockSize || this_1.options.labelBlockSize;
                    if (Array.isArray(xyb)) {
                        xb_1 = xyb[0];
                        yb_1 = xyb[1];
                    }
                    else if (typeof xyb === 'string') {
                        xyb = xyb.split(',');
                        xb_1 = parseInt(xyb[0]);
                        yb_1 = parseInt(xyb[1]);
                        def.labelBlockSize = [xb_1, yb_1];
                    }
                    else if (xyb === false) {
                        xb_1 = false;
                        def.labelBlockSize = [false, undefined];
                    }
                    mt.graphics.forEach(function (g) {
                        if (g && g._update) {
                            g._update(force);
                        }
                        if (g && g._updateLabel) {
                            g._updateLabel(labelVisible_1, xb_1, yb_1);
                        }
                    });
                }
            };
            var this_1 = this;
            for (var k in this.objs) {
                _loop_1(k);
            }
            this.pixiUtils.getRenderer().render(this.pixiStage);
        },
        isLabelVisible: function (objectType, def) {
            var map = this._map || _context.map.lmap, zoom = map.getZoom();
            def = def || this.getMapType(objectType);
            if (def.labelField === false)
                return false;
            var minLevel = def.labelMinLevel;
            var maxLevel = def.labelMaxLevel;
            var labelVisible = true;
            if (def.forceLabelVisible || def.forceLabelVisible === false) {
                labelVisible = def.forceLabelVisible;
            }
            else {
                if (minLevel <= 0)
                    minLevel = map.getMaxZoom() + minLevel;
                if (minLevel !== undefined && zoom < minLevel)
                    labelVisible = false;
                if (maxLevel <= 0)
                    maxLevel = map.getMaxZoom() + maxLevel;
                if (labelVisible && maxLevel !== undefined && zoom > maxLevel)
                    labelVisible = false;
            }
            return labelVisible;
        },
        setLabelVisible: function (v) {
            if (typeof v === 'undefined')
                v = true;
            this.options.mapTypes.forEach(function (mt) { return mt.forceLabelVisible = v; });
            this.refreshGraphics();
        },
        resetLabelVisible: function () {
            this.options.mapTypes.forEach(function (mt) { return delete mt.forceLabelVisible; });
            this.refreshGraphics();
        },
        dealFeatures: function (mt, features) {
            var _this = this;
            var pixiContainer = mt.container;
            features.forEach(function (l) {
                var g = mt.graphicsById[l.properties.id];
                if (!g) {
                    g = _this.createGraphic(l, mt, _this.pixiUtils, _this.pixiResources, _this.pixiStage);
                    if (g) {
                        var ele = g.displayObject;
                        if (ele)
                            pixiContainer.addChild(ele);
                        g.geojson = l;
                        mt.graphics.push(g);
                        mt.graphicsById[l.properties.id] = g;
                    }
                }
            });
        },
        _getTiledPixelBounds: function (center) {
            var map = this._map, mapZoom = map._animatingZoom ? Math.max(map._animateToZoom, map.getZoom()) : map.getZoom(), scale = map.getZoomScale(mapZoom, this._tileZoom), pixelCenter = map.project(center, this._tileZoom).floor(), halfSize = map.getSize().divideBy(scale * 2);
            return new L.Bounds(pixelCenter.subtract(halfSize), pixelCenter.add(halfSize));
        },
        _pxBoundsToTileRange: function (bounds, tileSize) {
            if (!tileSize)
                tileSize = new L.Point(512, 512);
            return new L.Bounds(bounds.min.unscaleBy(tileSize).floor(), bounds.max.unscaleBy(tileSize).ceil().subtract([1, 1]));
        },
        doLoad: function (utils, resources, justRender) {
            var _this = this;
            this.pixiUtils = utils;
            this.pixiResources = resources;
            var map = this._map;
            if (!map)
                return;
            if (this._animatingZoom)
                return;
            if (map._panTransition && map._panTransition._inProgress)
                return;
            var zoom = map.getZoom(), bounds = map.getBounds(), center = map.getCenter();
            var zoomChanged = (zoom !== this.lastMapLevel);
            this.lastMapLevel = zoom;
            this.lastMapCenter = center;
            var min = bounds.getSouthWest(), max = bounds.getNorthEast();
            var bbox = "POLYGON((".concat(min.lng, " ").concat(min.lat, ",").concat(min.lng, " ").concat(max.lat, ",").concat(max.lng, " ").concat(max.lat, ",").concat(max.lng, " ").concat(min.lat, ",").concat(min.lng, " ").concat(min.lat, "))");
            var objs = this.objs;
            this.refreshGraphics(true);
            if (justRender)
                return;
            var excludeExists = (this.options.incrementLoad === true);
            var typeGraphicLimit = this.options.typeGraphicLimit !== undefined ? this.options.typeGraphicLimit : 3000;
            if (typeGraphicLimit)
                for (var k in objs) {
                    var mt = objs[k], gs = mt.graphics;
                    if (gs.length > typeGraphicLimit && !(mt.def.doLoad || mt.def.doLoadOnce || mt.def.url)) {
                        this.clearTypeGraphics(k);
                    }
                }
            var queryTypes = this.getQueryingTypes(zoom, excludeExists, bbox);
            if (queryTypes.length) {
                var tiles;
                if (this.tileExcludeLevel != false && queryTypes.some(function (q) { return q.canTile === true; })) {
                    tiles = [];
                    var tileSize = new L.Point(512, 512);
                    var pixelBounds = this._getTiledPixelBounds(center), tileRange = this._pxBoundsToTileRange(pixelBounds, tileSize);
                    for (var tileX = tileRange.min.x; tileX <= tileRange.max.x; tileX++) {
                        for (var tileY = tileRange.min.y; tileY <= tileRange.max.y; tileY++) {
                            var coords = new L.Point(tileX, tileY);
                            var nwPoint = coords.scaleBy(tileSize), sePoint = nwPoint.add(tileSize), nw = map.unproject(nwPoint, coords.z), se = map.unproject(sePoint, coords.z);
                            bounds = new L.LatLngBounds(nw, se);
                            var latlngs = [bounds.getNorthWest(), bounds.getNorthEast(), bounds.getSouthEast(), bounds.getSouthWest()];
                            tiles.push({ x: tileX, y: tileY, z: zoom, bounds: toWkt(new L.Polygon(latlngs)) });
                        }
                    }
                }
                var reqs_1 = [], needRefresh = zoomChanged, self_1 = this;
                var mtOnce_1 = [], mtLoadBySelf_1 = [], qts_1 = [];
                queryTypes.forEach(function (mt) {
                    if (typeof mt.doLoad === 'function' || typeof mt.doLoadOnce === 'function')
                        mtLoadBySelf_1.push(mt);
                    else if (mt.url)
                        mtOnce_1.push(mt);
                    else
                        qts_1.push(mt);
                });
                if (mtLoadBySelf_1.length) {
                    mtLoadBySelf_1.forEach(function (mt) {
                        var loadRet = (mt.doLoad || mt.doLoadOnce)(Object.assign({
                            shape: bbox,
                            zoomLevel: map.getMaxZoom() - zoom, req_instance_id: _this.req_instance_id
                        }, mt, self_1.options.baseParams), mt);
                        if (_util.isPromise(loadRet))
                            reqs_1.push(loadRet.then(function (data) {
                                for (var k in data) {
                                    var mt_1 = _this.objs[k];
                                    mt_1.lastQueryBbox = bbox;
                                    _this.dealFeatures(mt_1, data[k].features);
                                    if (data[k].features.length)
                                        _this.refreshGraphics();
                                }
                            }));
                        else if (loadRet && loadRet.length) {
                            _this.dealFeatures(_this.objs[mt.id], loadRet);
                            _this.refreshGraphics();
                        }
                    });
                }
                if (mtOnce_1.length) {
                    mtOnce_1.forEach(function (mt) {
                        reqs_1.push(_util.get(mt.url).then(function (data) {
                            _util.xls.exec(function (xlsx, utils) {
                                var ws = xlsx.read(data, { type: 'binary' });
                                data = utils.sheet_to_json(ws.Sheets["Sheet1"]);
                                var fs = [], wkt = new Wkt.Wkt();
                                ;
                                data.forEach(function (row, i) {
                                    row.id = row.id || i;
                                    var f, shapek;
                                    for (var k in row) {
                                        try {
                                            f = { geometry: wkt.read(row[k]).toJson() };
                                            shapek = k;
                                            break;
                                        }
                                        catch (error) {
                                        }
                                    }
                                    if (f) {
                                        delete row[shapek];
                                        f.properties = row;
                                        fs.push(f);
                                    }
                                });
                                data = { features: fs };
                                _this.dealFeatures(_this.objs[mt._mtid || mt.id], data.features);
                                if (data.features.length)
                                    _this.refreshGraphics();
                            });
                        }));
                    });
                }
                if (qts_1.length) {
                    var crs_1 = map.options.crs;
                    if (crs_1)
                        crs_1 = crs_1.code;
                    var basetype_1;
                    map.eachLayer(function (l) {
                        if (!basetype_1 && l.options.baseLayer)
                            basetype_1 = l.options.type;
                    });
                    if (this.stompClient && this.stompClient.connected) {
                        qts_1.forEach(function (q) {
                            q = Object.assign({
                                shape: bbox,
                                zoomLevel: map.getMaxZoom() - zoom, crs: crs_1, base_type: basetype_1, req_instance_id: _this.req_instance_id
                            }, q, self_1.options.baseParams);
                            _this.stompClient.publish({ destination: '/bean/geojson', body: JSON.stringify(q) });
                            _this.objs[q._mtid].lastQueryBbox = bbox;
                        });
                        return;
                    }
                    var threads = self_1.queryThreadsNum;
                    var group = [], qtgroups = [group];
                    while (qts_1.length) {
                        group.push(qts_1.shift());
                        if (group.length === threads && qts_1.length) {
                            group = [];
                            qtgroups.push(group);
                        }
                    }
                    qtgroups.forEach(function (g) {
                        var ps = {
                            shape: bbox,
                            zoomLevel: map.getMaxZoom() - zoom, crs: crs_1, base_type: basetype_1
                        };
                        if (tiles) {
                            ps._tiles = JSON.stringify(tiles);
                            ps._tilesCategory = _this.options.tilesCategory;
                        }
                        reqs_1.push(_bean.geojson(g, Object.assign(ps, self_1.options.baseParams)).then(function (data) {
                            var _loop_2 = function (k) {
                                var mt = _this.objs[k];
                                if (mt) {
                                    mt.lastQueryBbox = bbox;
                                    _this.dealFeatures(mt, data[k].features);
                                }
                                else {
                                    var allfs_1 = data[k].features;
                                    k.split(',').forEach(function (realMtId) {
                                        mt = _this.objs[realMtId];
                                        if (mt) {
                                            mt.lastQueryBbox = bbox;
                                            var where_1 = mt.def.where;
                                            _this.dealFeatures(mt, allfs_1.filter(function (f) {
                                                for (var k_1 in where_1) {
                                                    if (f.properties[k_1] != where_1[k_1])
                                                        return false;
                                                }
                                                return true;
                                            }));
                                        }
                                    });
                                }
                                if (data[k].features.length || zoomChanged)
                                    _this.refreshGraphics();
                            };
                            for (var k in data) {
                                _loop_2(k);
                            }
                        }));
                    });
                }
                if (!reqs_1.length && zoomChanged)
                    this.refreshGraphics();
            }
            else {
                if (zoomChanged)
                    this.refreshGraphics();
            }
        },
        _labelxys: {},
        _computeScale: function (def) {
            var scale = 1.0;
            if (def.autoscale) {
                var rate = parseFloat(def.autoscale);
                var levelOffset = this._map.getMaxZoom() - this._map.getZoom();
                while (levelOffset--)
                    scale *= rate;
            }
            return scale;
        },
        _getSymbol: function (def, l, force) {
            if (force === void 0) { force = false; }
            var map = this._map;
            var symbol = def._symbol || this.options._symbol || def, zoom = map.getZoom();
            var thiz = this;
            if (!force && l.lastSymbol && l.lastSymbol.zoom === zoom)
                return null;
            var render = def.render || thiz.options.render;
            if (l.properties._symbol)
                symbol = l.properties._symbol;
            else if (render) {
                if (typeof render === 'function') {
                    var ro = render(l.properties, l, def, thiz);
                    if (ro === false)
                        return false;
                    symbol = Object.assign({}, def, ro);
                    l.lastSymbol = { zoom: zoom, symbol: symbol };
                }
                else if (render.content) {
                    var draw = {};
                    var obj = l.properties;
                    eval(render.content);
                    if (draw.symbol)
                        symbol = Object.assign({}, def, draw.symbol);
                    l.lastSymbol = { zoom: zoom, symbol: symbol };
                }
            }
            else if (l.geometry.type === 'Point') {
                var icon = void 0;
                var iconFn = def.iconFn;
                if (iconFn === undefined)
                    iconFn = thiz.options.iconFn;
                if (iconFn) {
                    icon = iconFn(l, this);
                }
                else {
                    var iconExpression = def.iconExpression;
                    if (iconExpression === undefined)
                        iconExpression = thiz.options.iconExpression;
                    if (iconExpression) {
                        icon = _util.replaceVar(iconExpression, Object.assign({ zoom: map.getMaxZoom() - zoom }, l.properties), true);
                    }
                }
                if (icon)
                    symbol = { icon: icon };
            }
            var gtype = 'polygon';
            if (l.geometry.type === 'Point')
                gtype = 'point';
            else if (l.geometry.type === 'LineString' || l.geometry.type === 'MultiLineString')
                gtype = 'line';
            if (symbol[gtype])
                symbol = symbol[gtype];
            l.lastSymbol = { zoom: zoom, symbol: symbol };
            return symbol;
        },
        createGraphic: function (l, mt, utils, resources, pixiStage) {
            var def = mt.def, thiz = this;
            function createLabel(g, position, rotate, scale) {
                if (scale === void 0) { scale = false; }
                var sprite = g.displayObject;
                var ls = sprite._labelSprite;
                if (!ls) {
                    var label = void 0;
                    var labelField = def.labelField;
                    if (typeof labelField === 'function')
                        label = labelField(l, true, def, thiz);
                    else if (typeof labelField === 'string')
                        label = labelField.indexOf('${') !== -1 ? _util.replaceVar(labelField, l.properties) : l.properties[labelField];
                    if (label) {
                        var style = Object.assign({
                            padding: 1,
                            fontFamily: '宋体',
                            fontSize: 11,
                            stroke: 'white',
                            strokeThickness: 2
                        }, thiz.options.labelStyle, def.labelStyle);
                        var sl = new PIXI.Text(label, style);
                        var ax = .5, ay = .5, dir = def.labelPosition || thiz.options.labelPosition;
                        if (typeof dir === 'object') {
                            dir = dir[l.geometry.type] || dir['default'];
                        }
                        if (dir === 'random') {
                            var ds = ['top', 'left', 'bottom', 'right'];
                            dir = ds[Math.ceil(Math.random() * 4)];
                        }
                        if ('top' === dir)
                            ay = 3;
                        else if ('bottom' === dir)
                            ay = -2.5;
                        else if ('left' === dir)
                            ax = 1.5;
                        else if ('right' === dir)
                            ax = -.5;
                        else if (dir && dir.indexOf(',') !== -1) {
                            var xy = dir.split(',');
                            ax = parseFloat(xy[0]);
                            ay = parseFloat(xy[1]);
                        }
                        sl.anchor.set(ax, ay);
                        sprite.addChild(sl);
                        ls = sprite._labelSprite = sl;
                    }
                }
                if (ls) {
                    ls.visible = thiz.isLabelVisible(def.objectType, def);
                    if (position) {
                        ls.position.set(position.x, position.y);
                    }
                    if (rotate)
                        ls.rotation = rotate;
                    if (scale) {
                        ls.scale.x = 1 / thiz.pixiStage.scale.x;
                        ls.scale.y = 1 / thiz.pixiStage.scale.y;
                    }
                }
                g.labelObject = ls;
            }
            var self = this;
            var go = {}, hasRenderer = def.render || thiz.options.render;
            if (!l.geometry) {
                return;
            }
            if (l.geometry.type === 'Point') {
                if ((def.icon || def.iconExpression || thiz.options.iconExpression) && !hasRenderer) {
                    var sprite_1 = new PIXI.Sprite();
                    sprite_1._draw = function (symbol) {
                        if (!symbol)
                            return;
                        var texture = PIXI.Texture.fromImage ? PIXI.Texture.fromImage(symbol.icon, true)
                            : PIXI.Texture.from(symbol.icon, {}, false);
                        texture.baseTexture.once('loaded', function () {
                            thiz.refreshStage();
                        });
                        sprite_1.texture = texture;
                    };
                    sprite_1.anchor.set(0.5, 0.5);
                    go.displayObject = sprite_1;
                    go._update = function (force) {
                        if (force === void 0) { force = false; }
                        var symbol = self._getSymbol(def, l, force);
                        if (symbol !== null) {
                            sprite_1._draw(symbol);
                            var scale = self._computeScale(def);
                            sprite_1.scale.x = scale / pixiStage.scale.x;
                            sprite_1.scale.y = scale / pixiStage.scale.y;
                        }
                    };
                }
                else {
                    var graphic_1 = go.displayObject = new PIXI.Graphics();
                    graphic_1._draw = function (symbol) {
                        if (!symbol)
                            return;
                        graphic_1.clear();
                        var drawType = symbol.drawType;
                        if (symbol.icon && (!drawType || drawType === 'icon')) {
                            var sprite = graphic_1.iconSprite;
                            if (!sprite) {
                                sprite = graphic_1.iconSprite = new PIXI.Sprite();
                                sprite.anchor.set(0.5, 0.5);
                                graphic_1.addChild(sprite);
                            }
                            var texture = PIXI.Texture.fromImage(symbol.icon, true);
                            texture.baseTexture.once('loaded', function () {
                                thiz.refreshStage();
                            });
                            sprite.texture = texture;
                            return;
                        }
                        if (graphic_1.iconSprite)
                            graphic_1.removeChild(graphic_1.iconSprite);
                        var fill = def.intattr(['fill', 'color'], 0, symbol), lineColor = def.intattr(['lineColor', 'color'], 0, symbol);
                        var thickness = def.intattr('lineWidth', 1, symbol), alpha = def.floatattr('alpha', 1, symbol);
                        graphic_1.beginFill(fill, alpha);
                        graphic_1.lineStyle(thickness, lineColor);
                        if (drawType === 'rect') {
                            var w = def.intattr('width', 16, symbol), h = def.intattr('height', 12, symbol);
                            if (symbol.radius)
                                graphic_1.drawRoundedRect(-w / 2, -h / 2, w, h, parseInt(symbol.radius));
                            else
                                graphic_1.drawRect(-w / 2, -h / 2, w, h);
                        }
                        else if (drawType === 'polygon') {
                            var path = symbol.path.split(',').map(function (n) { return parseInt(n); });
                            graphic_1.drawPolygon(path);
                        }
                        else {
                            var r = def.intattr('r', 12, symbol);
                            graphic_1.drawCircle(0, 0, r);
                        }
                        graphic_1.endFill();
                    };
                    go._update = function (force) {
                        if (force === void 0) { force = false; }
                        var symbol = self._getSymbol(def, l, force);
                        if (symbol !== null) {
                            graphic_1._draw(symbol);
                            var scale = self._computeScale(def);
                            graphic_1.scale.x = scale / pixiStage.scale.x;
                            graphic_1.scale.y = scale / pixiStage.scale.y;
                        }
                    };
                }
                var lnglat = l.geometry.coordinates;
                var xy = utils.latLngToLayerPoint([lnglat[1], lnglat[0]]);
                go.displayObject.position.set(xy.x, xy.y);
                createLabel(go, null, null);
            }
            else if (l.geometry.type === 'LineString' || l.geometry.type === 'MultiLineString') {
                var graphic_2 = go.displayObject = new PIXI.Graphics();
                go._update = function (force) {
                    if (force === void 0) { force = false; }
                    var symbol = self._getSymbol(def, l, force);
                    if (symbol === null) {
                        return;
                    }
                    var lineColor = def.intattr(['lineColor', 'color'], 0, symbol);
                    var thickness = def.intattr('lineWidth', 1, symbol);
                    thickness /= pixiStage.scale.x;
                    var scale = self._computeScale(def);
                    thickness *= scale;
                    var dashStyle = def.attr('dash', null, symbol), dash = 6, gap;
                    if (dashStyle) {
                        if (dashStyle === true)
                            dashStyle = [dash];
                        else if (typeof dashStyle === 'string') {
                            dashStyle = dashStyle.split(',');
                        }
                        if (Array.isArray(dashStyle)) {
                            dash = parseInt(dashStyle[0]);
                            gap = parseInt(dashStyle[1]);
                        }
                        if (!gap)
                            gap = dash * 2 / 3;
                        dash /= pixiStage.scale.x;
                        gap /= pixiStage.scale.x;
                        dash *= scale;
                        gap *= scale;
                    }
                    var paths = l.geometry.coordinates;
                    if (l.geometry.type === 'LineString')
                        paths = [paths];
                    var startPoint = utils.latLngToLayerPoint([paths[0][0][1], paths[0][0][0]]);
                    if (symbol !== false) {
                        graphic_2.clear();
                        graphic_2.lineStyle(thickness, lineColor);
                        graphic_2.x = startPoint.x;
                        graphic_2.y = startPoint.y;
                    }
                    var pointsall = [];
                    paths.forEach(function (path) {
                        var latlngs = path;
                        var points = latlngs.map(function (latlng) { return utils.latLngToLayerPoint([latlng[1], latlng[0]]); });
                        if (symbol !== false)
                            points.forEach(function (p, i) {
                                if (i === 0)
                                    graphic_2.moveTo(p.x - graphic_2.x, p.y - graphic_2.y);
                                else {
                                    if (dashStyle)
                                        graphic_2.drawDashLine(p.x - graphic_2.x, p.y - graphic_2.y, dash, gap);
                                    else
                                        graphic_2.lineTo(p.x - graphic_2.x, p.y - graphic_2.y);
                                }
                            });
                        pointsall = pointsall.concat(points);
                    });
                    var labelPosition, labelRotate, labelLatlng;
                    var center = (pointsall.length - 1) / 2.0;
                    var start = Math.floor(center), end = Math.ceil(center);
                    if (start == end) {
                        labelPosition = pointsall[start];
                        labelLatlng = utils.layerPointToLatLng({ x: labelPosition.x, y: labelPosition.y });
                    }
                    else {
                        labelPosition = {
                            x: (pointsall[end].x + pointsall[start].x) / 2 - startPoint.x,
                            y: (pointsall[end].y + pointsall[start].y) / 2 - startPoint.y
                        };
                        labelRotate = Math.atan((pointsall[end].y - pointsall[start].y) / (pointsall[end].x - pointsall[start].x));
                        labelLatlng = utils.layerPointToLatLng({
                            x: startPoint.x + labelPosition.x,
                            y: startPoint.y + labelPosition.y
                        });
                    }
                    createLabel(go, labelPosition, labelRotate, true);
                    if (go.labelObject)
                        go.labelObject.latlng = labelLatlng;
                };
            }
            else if (l.geometry.type === 'Polygon') {
                var graphic_3 = go.displayObject = new PIXI.Graphics();
                go._update = function (force) {
                    if (force === void 0) { force = false; }
                    var symbol = self._getSymbol(def, l, force);
                    if (symbol == null)
                        return;
                    var fill = def.intattr(['fill', 'color'], 0, symbol), lineColor = def.intattr(['lineColor', 'color'], 0, symbol);
                    var thickness = def.intattr('lineWidth', 1, symbol), alpha = def.floatattr('alpha', 1, symbol);
                    thickness /= pixiStage.scale.x;
                    var scale = self._computeScale(def);
                    thickness *= scale;
                    if (symbol !== false) {
                        graphic_3.clear();
                        graphic_3.beginFill(fill, alpha);
                        graphic_3.lineStyle(thickness, lineColor);
                    }
                    var paths = l.geometry.coordinates;
                    var startPoint, maxx = 0, maxy = 0;
                    var ax = 0, ay = 0, pathCnt = 0;
                    paths.forEach(function (path) {
                        path = path.map(function (latlng) { return utils.latLngToLayerPoint([latlng[1], latlng[0]]); });
                        if (!startPoint) {
                            startPoint = { x: path[0].x, y: path[0].y };
                            if (symbol !== false) {
                                graphic_3.x = startPoint.x;
                                graphic_3.y = startPoint.y;
                            }
                        }
                        var poly = [];
                        path.forEach(function (pt) {
                            var x = pt.x - startPoint.x, y = pt.y - startPoint.y;
                            if (Math.abs(x) > Math.abs(maxx))
                                maxx = x;
                            if (Math.abs(y) > Math.abs(maxy))
                                maxy = y;
                            poly.push(x);
                            poly.push(y);
                            ax += pt.x;
                            ay += pt.y;
                        });
                        pathCnt += path.length;
                        if (symbol !== false)
                            graphic_3.drawPolygon(poly);
                    });
                    if (symbol !== false)
                        graphic_3.endFill();
                    var labelPosition = { x: ax / pathCnt - startPoint.x, y: ay / pathCnt - startPoint.y };
                    createLabel(go, labelPosition, null, true);
                    if (go.labelObject)
                        go.labelObject.latlng = utils.layerPointToLatLng({
                            x: startPoint.x + labelPosition.x,
                            y: startPoint.y + labelPosition.y
                        });
                };
            }
            if (go)
                go._updateLabel = function (v, xblock, yblock) {
                    if (typeof def.labelField === 'function')
                        def.labelField(l, v, def, thiz);
                    if (!go.labelObject)
                        return;
                    if (v && xblock !== false) {
                        if (!xblock)
                            xblock = 60;
                        if (!yblock)
                            yblock = 30;
                        if (def.labelBlockX && def.labelBlockY) {
                            xblock = def.labelBlockX;
                            yblock = def.labelBlockY;
                        }
                        var latlng = go.labelObject.latlng;
                        if (!latlng && l.geometry.type === 'Point') {
                            var lnglat = l.geometry.coordinates;
                            latlng = [lnglat[1], lnglat[0]];
                        }
                        if (latlng) {
                            var xy = utils.getMap().latLngToContainerPoint(latlng);
                            var x = xy.x - xy.x % xblock, y = xy.y - xy.y % yblock, k = x + '' + y;
                            if (thiz._labelxys[k]) {
                                v = false;
                            }
                            thiz._labelxys[k] = 1;
                        }
                    }
                    go.labelObject.visible = v;
                };
            return go;
        },
        getQueryingTypes: function (zoom, excludeExists, bbox, mtFilter) {
            var _this = this;
            if (excludeExists === void 0) { excludeExists = false; }
            if (bbox === void 0) { bbox = null; }
            if (mtFilter === void 0) { mtFilter = undefined; }
            var mtbytypes = {};
            var types = this.options.mapTypes.filter(function (mt) {
                var lastBbox = _this.objs[mt.id].lastQueryBbox;
                if (lastBbox && lastBbox === bbox)
                    return false;
                var visible = mt.visible !== false;
                if (mt.minZoom)
                    visible = visible && zoom >= mt.minZoom;
                if (mt.maxZoom)
                    visible = visible && zoom <= mt.maxZoom;
                if (visible && (mt.url || mt.doLoadOnce)) {
                    if (_this.objs[mt.id].graphics.length)
                        visible = false;
                }
                if (visible && mtFilter)
                    visible = mtFilter(mt);
                if (visible) {
                    var mtsbytype = mtbytypes[mt.objectType];
                    if (!mtsbytype) {
                        mtsbytype = mtbytypes[mt.objectType] = { list: [] };
                    }
                    if (mtsbytype.list.indexOf(mt) === -1) {
                        mtsbytype.list.push(mt);
                    }
                }
                return visible;
            });
            var _loop_3 = function (k) {
                var mtsbytype = mtbytypes[k];
                mtsbytype.canMergeQuery = !excludeExists && this_2.options.canMergeQuery !== false && mtsbytype.list.length > 1 && mtsbytype.list.every(function (m) {
                    return m.canMergeQuery !== false && !(m.url || m.doLoadOnce) && typeof m.where === 'object' && Object.keys(m.where).length === 1;
                });
                mtsbytype.canTile = mtsbytype.list.every(function (m) {
                    return (m.canTile && _this.canTile !== false) || (m.canTile === undefined && _this.canTile === true);
                });
                if (mtsbytype.canMergeQuery) {
                    var ws_1 = mtsbytype.mergeQueryWhere = {}, fieldNames_1 = [];
                    mtsbytype.list.forEach(function (m) {
                        for (var k_2 in m.where) {
                            if (fieldNames_1.indexOf(k_2) === -1)
                                fieldNames_1.push(k_2);
                            var v = ws_1[k_2];
                            if (v === undefined)
                                v = m.where[k_2];
                            else {
                                if (!Array.isArray(v))
                                    v = [v];
                                v.push(m.where[k_2]);
                            }
                            ws_1[k_2] = v;
                        }
                    });
                    if (fieldNames_1.length > 1) {
                        ws_1['_opgroup'] = 'or(' + fieldNames_1.join(',') + ')';
                    }
                }
            };
            var this_2 = this;
            for (var k in mtbytypes) {
                _loop_3(k);
            }
            var toQueryTypes = [];
            types.forEach(function (mt) {
                var mtsbytype = mtbytypes[mt.objectType];
                if (!mtsbytype)
                    return;
                var m = { objectType: mt.objectType, _mtid: mt.id, doLoad: mt.doLoad, doLoadOnce: mt.doLoadOnce };
                toQueryTypes.push(m);
                if (mtsbytype.canMergeQuery) {
                    Object.assign(m, mtsbytype.mergeQueryWhere);
                    m._mtid = mtsbytype.list.map(function (mt1) { return mt1.id; }).join(',');
                    delete mtbytypes[mt.objectType];
                    m.canTile = mtsbytype.list.every(function (mt0) {
                        if ((mt0.canTile && _this.canTile !== false) || (mt0.canTile === undefined && _this.canTile === true)) {
                            var exculdeLevel = _this.tileExcludeLevel;
                            if (mt0.tileExcludeLevel)
                                exculdeLevel = parseInt(mt0.tileExcludeLevel);
                            return exculdeLevel < (_this._map.getMaxZoom() - zoom);
                        }
                    });
                }
                else {
                    var where = mt.where;
                    if (where) {
                        if (typeof where === 'function')
                            where = where(zoom, mt, _this, _this.__mapInstance);
                        if (typeof where === 'string')
                            where = { _where: where };
                        Object.assign(m, where);
                    }
                    if ((mt.canTile && _this.canTile !== false) || (mt.canTile === undefined && _this.canTile === true)) {
                        var exculdeLevel = _this.tileExcludeLevel;
                        if (mt.tileExcludeLevel)
                            exculdeLevel = parseInt(mt.tileExcludeLevel);
                        m.canTile = exculdeLevel < (_this._map.getMaxZoom() - zoom);
                    }
                }
                if (mt.url)
                    m.url = mt.url;
                if (mt.encoding)
                    m.encoding = mt.encoding;
                if (mt.geomField)
                    m.geomField = mt.geomField;
                if (excludeExists) {
                    var lo = _this.objs[mt.id];
                    if (lo && lo.graphics && lo.graphics.length) {
                        m.id = [];
                        lo.graphics.every(function (g) {
                            m.id.push(g.geojson.properties.id);
                            return m.id.length < 999;
                        });
                        m['_op.id'] = 'not in';
                    }
                }
            });
            return toQueryTypes;
        },
        doSelect: function (geom, params, types) {
            types = types || this.getQueryingTypes(this._map.getZoom(), false, null, this.ignoreSelectableFilter ? undefined : function (mt) {
                return mt.forceSelectable !== false && mt.selectable !== false;
            });
            if (this.selectTypesFilter)
                types = this.selectTypesFilter(types, this._map.getZoom(), geom);
            if (!types || types.length === 0)
                return Promise.resolve([]);
            var pointSelect = false;
            var theGeom = geom;
            if (geom instanceof L.Marker) {
                geom = geom.getLatLng();
            }
            if (geom.lat && geom.lng) {
                pointSelect = true;
                theGeom = geom;
                geom = this.__mapInstance.pointToPolygon(geom);
            }
            params = Object.assign({ split: false }, params);
            if (geom && !params.shape) {
                geom._map = geom._map || this.lmap;
                params.shape = toWkt(geom);
            }
            params.isPointSelect = pointSelect;
            if (pointSelect)
                params.originSelectPoint = toWkt(theGeom);
            var crs = this._map.options.crs;
            if (crs)
                params.crs = crs.code;
            return _bean.geojson(types, params);
        },
        getGraphicObjs: function (type, geojson) {
            var _this = this;
            if (geojson === void 0) { geojson = true; }
            if (type == undefined) {
                var includeHide = type === true;
                type = [];
                for (var k in this.objs)
                    if (includeHide || this.isObjectTypeVisible(k))
                        type.push(k);
            }
            if (Array.isArray(type)) {
                var ret_1 = [];
                type.forEach(function (t) {
                    Array.prototype.push.apply(ret_1, _this.getGraphicObjs(t, geojson));
                });
                return ret_1;
            }
            var gs = this.objs[type].graphics;
            if (geojson !== false)
                gs = gs.map(function (g) { return g.geojson; });
            return gs;
        }
    });
    var _mouseMarker;
    var Map = (function () {
        function Map(el, options, registerInContext) {
            if (options === void 0) { options = {}; }
            if (registerInContext === void 0) { registerInContext = false; }
            var _this = this;
            this.el = el;
            this.options = options;
            this.registerInContext = registerInContext;
            this.isReady = false;
            this.eventbus = new L.Evented();
            this.L = L;
            this.isDrawing = false;
            this.isDrawSelecting = false;
            this.isEditing = false;
            this.printable = undefined;
            this.traceView = true;
            this.clickSelect = true;
            this.selectionStyle = { color: 'red', weight: 5, opacity: .4 };
            this.selectionMarkerStyle = {
                radius: 8,
                fillColor: "red",
                color: "#000",
                weight: 1,
                opacity: .2,
                fillOpacity: 0.4
            };
            this.refreshable = true;
            this.movable = true;
            this.zoombar = false;
            this.zoombox = false;
            this.mapOptions = {};
            this.showMouseCoords = true;
            this.showLayersControl = false;
            this.showOverview = false;
            this.initFullExtent = false;
            this.updateCrs = false;
            this.layersbyid = {};
            this.layersbytype = {};
            this.subtypes = {};
            this.idtypes = {};
            this.baselayers = {};
            this.overlayers = {};
            this.objectsLayers = [];
            this.selectFeatures = [];
            this.selectHistory = [];
            this.layerCreating = false;
            this.layerIdSeq = 0;
            this.listenObjUpdate = true;
            this.useRootMap = false;
            this.useRootMapBaseLayer = true;
            this.createLayerOnGroup = false;
            this.showZoomControl = true;
            this.defaultControlPosition = 'topleft';
            this.selectObjPopup = true;
            this.isWaiting = false;
            this.minSelectPointDistance = 0.00001;
            this.defaultPointTolPx = 10;
            this.defaultPointTolDistance = .000002;
            this.appendSelecting = false;
            Object.assign(this, options);
            this.prepare().then(function () {
                _this.initByContext().then(function () {
                    if (_this.useRootMap) {
                        _this.useRootMapBaseLayer = false;
                        _this.useRootMapLayerDefs = '';
                    }
                    if (_context && _context.rootContext && _context.rootContext !== _context) {
                        var rc_1 = _context.rootContext, topMap_1 = rc_1.map;
                        if (_this.useRootMapBaseLayer) {
                            var topBaseLayers = topMap_1 ? topMap_1.layerObjs.filter(function (l) { return l.baseLayer; }) : [];
                            var visibleLayer = topBaseLayers.find(function (l) { return topMap_1.isLayerVisibleById(l.id); }) || topBaseLayers[0];
                            if (visibleLayer) {
                                visibleLayer = Object.assign({}, visibleLayer);
                                delete visibleLayer._layer;
                                delete visibleLayer.visible;
                                _this.layerObjs = _this.layerObjs.filter(function (l) { return !l.baseLayer; });
                                _this.layerObjs.unshift(visibleLayer);
                                _this.crs = topMap_1.crs;
                            }
                            if (_this.mapOptions.maxZoom === undefined)
                                _this.mapOptions.maxZoom = topMap_1.lmap.getMaxZoom();
                        }
                        if (_this.useRootMapLayerDefs) {
                            var layerDefs = _this.useRootMapLayerDefs;
                            if (typeof layerDefs === 'string') {
                                if (layerDefs[0] == '[')
                                    try {
                                        layerDefs = JSON.parse(layerDefs);
                                    }
                                    catch (e) { }
                            }
                            if (typeof layerDefs === 'string')
                                layerDefs = layerDefs.split(',');
                            layerDefs.forEach(function (layerdef) {
                                var layerName;
                                if (typeof layerdef === 'string')
                                    layerName = layerdef;
                                else
                                    layerName = layerdef.layerName;
                                var mainMapNode = rc_1.configXml.find('map');
                                var mainLayerNodes = mainMapNode.find('layer');
                                mainLayerNodes = _util.jQueryToPlain(mainLayerNodes, true, true);
                                var resourceLayerDef = mainLayerNodes.find(function (l) { return l.id === layerName; });
                                if (resourceLayerDef) {
                                    if (layerdef.visible !== undefined)
                                        resourceLayerDef.visible = layerdef.visible;
                                    if (resourceLayerDef.mapType && resourceLayerDef.mapType.length)
                                        resourceLayerDef.mapType = resourceLayerDef.mapType.filter(function (mt) {
                                            var defmt;
                                            if (layerdef.mapTypes)
                                                defmt = layerdef.mapTypes.find(function (lmt) {
                                                    return lmt == mt.objectType || lmt.objectType === mt.objectType;
                                                });
                                            if (!defmt && layerdef.mapTypes)
                                                return false;
                                            if (defmt && defmt.visible !== undefined)
                                                mt.visible = defmt.visible;
                                            var editable;
                                            if (defmt && defmt.editable !== undefined)
                                                editable = defmt.editable;
                                            if (editable === undefined && layerdef.editable !== undefined)
                                                editable = layerdef.editable;
                                            if (editable === undefined)
                                                editable = false;
                                            mt.editable = editable;
                                            return true;
                                        });
                                    var idx = _this.layerObjs.findIndex(function (l) { return l.id === layerName; });
                                    if (idx !== -1)
                                        _this.layerObjs.splice(idx, 1, resourceLayerDef);
                                    else {
                                        idx = _this.layerObjs.findIndex(function (lo) { return !lo.baseLayer; });
                                        if (idx !== -1)
                                            _this.layerObjs.splice(idx, 0, resourceLayerDef);
                                        else
                                            _this.layerObjs.push(resourceLayerDef);
                                    }
                                }
                            });
                        }
                    }
                    _this.lmap = _this.createMap(function () {
                        _this.locationGroup = L.layerGroup().addTo(_this.lmap);
                        if (_this.isDrawing)
                            _this.activeDraw(true);
                        if (_this.refreshable)
                            _this.addRefreshControl();
                        if (_this.selectOptions && _context.hasPermission("BASIC_MAP"))
                            _this.addSelectControl();
                        if (_this.editable === undefined)
                            _this.editable = !!_this.selectOptions;
                        if (_this.editable && _context.hasPermission("BASIC_GRAPHEDIT"))
                            _this.addEditControl();
                        if (_context.hasPermission("BASIC_MAP"))
                            _this.addLabelControl();
                        if (_this.movable !== false)
                            _this.addMoveControl();
                        if (_this.zoombox !== false)
                            _this.addZoomBoxControl();
                        if (_this.zoombar !== false)
                            _this.addZoomBarControl();
                        if (_this.showOverview !== false)
                            _this.addOverviewControl();
                        if (_this.legendOptions)
                            _this.showLegend();
                        if (_this.listenObjUpdate) {
                            _context.events.on('EVENT_OBJ', function (e, data) {
                                if (!_this.listenObjUpdate)
                                    return;
                                var ol = _this.getLayerByObjectType(data.objectType);
                                if (ol && ol.redraw && _this.isLayerVisibleByType(data.objectType)) {
                                    _this.lmap.closePopup();
                                    _this.clearSelection();
                                    ol.redraw();
                                }
                                else if (!ol) {
                                    _bean.getMeta(data.objectType).then(function (om) {
                                        if (om._mapType && om.refreshMapWhenUpdate !== false) {
                                            ol = _this.getLayerByObjectType(om._mapType);
                                            if (ol && ol.redraw && _this.isLayerVisibleByType(om._mapType)) {
                                                _this.lmap.closePopup();
                                                _this.clearSelection();
                                                ol.redraw();
                                            }
                                        }
                                    });
                                }
                            });
                        }
                        _this.triggerMapReady();
                        var ccsetting = _context.getSetting('_map');
                        if (ccsetting) {
                            for (var k in ccsetting) {
                                _this[k] = ccsetting[k];
                            }
                        }
                    });
                });
            });
        }
        Map.prototype.prepare = function () {
            var pixijs = _context.getSetting('map.pixijs-path') || 'lib/pixijs/pixi.min.js';
            return _util.loadScripts([pixijs, 'lib/leaflet/proj4-src.js', 'lib/leaflet/leaflet.draw/leaflet.draw-src.js',
                'lib/leaflet/wicket.js', 'lib/leaflet/beautify-marker/leaflet-beautify-marker-icon.js']).then(function () {
                return _util.loadScripts(['lib/leaflet/pixilayer.js', 'lib/leaflet/proj4leaflet.js', 'lib/leaflet/wicket-leaflet.js',
                    'lib/leaflet/beautify-marker/leaflet-beautify-marker.js', 'lib/leaflet/leaflet.contextmenu.min.js', 'lib/leaflet/leaflet-icon-pulse-master/L.Icon.Pulse.js']).then(function () {
                    L.Draw.Polyline.prototype.options.feet = false;
                    L.Draw.Circle.prototype.options.feet = false;
                    L.Draw.Polyline.prototype.options.showLength = true;
                    L.Draw.Polygon.prototype.options.showLength = true;
                    L.Draw.Polygon.prototype.options.allowIntersection = false;
                    L.Draw.Polygon.prototype.options.showArea = true;
                    if (window['_locale'] && window['_locale'].lang === 'zh') {
                        _util.merge(L.drawLocal, {
                            draw: {
                                toolbar: {
                                    actions: { title: '取消绘制', text: '取消' },
                                    finish: { title: '完成绘制', text: '完成' },
                                    undo: { title: '删除上一个节点', text: '删除上一个节点' },
                                    buttons: {
                                        polyline: '画线', polygon: '多边形', rectangle: '矩形', circle: '圆', marker: '画点'
                                    }
                                },
                                handlers: {
                                    marker: {
                                        tooltip: { start: '在地图上点击' }
                                    },
                                    polygon: {
                                        tooltip: { start: '在地图上点击开始绘制', cont: '在地图上点击继续绘制', end: '点击初始点完成多边形绘制' }
                                    },
                                    polyline: {
                                        error: '错误: 不允许线交叉',
                                        tooltip: { start: '在地图上点击开始绘制', cont: '在地图上点击继续绘制', end: '点击最后的节点完成线绘制' }
                                    },
                                    rectangle: {
                                        tooltip: { start: '在地图上点击然后拖动鼠标进行绘制', end: '释放鼠标完成绘制' }
                                    },
                                    circle: {
                                        tooltip: {
                                            start: '在地图上点击然后拖动鼠标进行绘制', end: '释放鼠标完成绘制'
                                        },
                                        radius: '半径'
                                    },
                                    simpleshape: {
                                        tooltip: {
                                            end: '释放鼠标完成绘制'
                                        }
                                    }
                                }
                            },
                            edit: {
                                toolbar: {
                                    actions: {
                                        save: { title: '保存修改', text: '保存' },
                                        cancel: { title: '取消修改', text: '取消' },
                                        clearAll: { title: '清除所有绘制', text: '清除' }
                                    },
                                    buttons: {
                                        edit: '编辑绘制', editDisabled: '没有可编辑对象', remove: '删除图形', removeDisabled: '没有可以删除的图形'
                                    }
                                },
                                handlers: {
                                    edit: {
                                        tooltip: { text: '拖动节点进行编辑', subtext: '点击取消可撤消' }
                                    },
                                    remove: {
                                        tooltip: { text: '在图形上点击可删除' }
                                    }
                                }
                            }
                        });
                    }
                    PIXI.Graphics.prototype._pointDistance = function (x1, y1, x2, y2) {
                        return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
                    };
                    PIXI.Graphics.prototype._lerpVectors = function (x1, y1, x2, y2, alpha) {
                        return [x1 + (x2 - x1) * alpha, y1 + (y2 - y1) * alpha];
                    };
                    PIXI.Graphics.prototype.drawDashLine = function (toX, toY, dash, gap) {
                        if (dash === void 0) { dash = 5; }
                        if (gap === void 0) { gap = 5; }
                        var lastPosition = this.currentPath.shape.points;
                        var fromx = lastPosition[lastPosition.length - 2] || 0, fromy = lastPosition[lastPosition.length - 1] || 0;
                        var distance = this._pointDistance(fromx, fromy, toX, toY);
                        for (var i = dash + gap; i <= distance; i += dash + gap) {
                            var v = this._lerpVectors(fromx, fromy, toX, toY, (i - gap) / distance);
                            this.lineTo(v[0], v[1]);
                            v = this._lerpVectors(fromx, fromy, toX, toY, i / distance);
                            this.moveTo(v[0], v[1]);
                        }
                        this.lineTo(toX, toY);
                    };
                    return _util.loadCss(['lib/leaflet/leaflet.draw/leaflet.draw.css',
                        'lib/leaflet/beautify-marker/leaflet-beautify-marker-icon.css',
                        'lib/leaflet/leaflet.contextmenu.min.css',
                        'lib/leaflet/leaflet-icon-pulse-master/L.Icon.Pulse.css']);
                });
            });
        };
        Map.prototype.triggerMapReady = function () {
            if (this.registerInContext) {
                _context.map = this;
                if (this.lmap && !this.layerCreating)
                    _context.events.trigger(_map.EVENT_MAP_CREATED, this);
            }
            this.isReady = true;
            this.eventbus.fire('mapready');
        };
        Map.prototype.getWindow = function () {
            return window;
        };
        Map.prototype.initByContext = function () {
            var _this = this;
            if (_context.configXml) {
                var mapNode = _context.configXml.find('map');
                if (mapNode.length) {
                    var layerNodes = mapNode.find('layer');
                    layerNodes = _util.jQueryToPlain(layerNodes, true, true);
                    mapNode = _util.jQueryToPlain(mapNode[0], true, true);
                    if (mapNode.useRootMap) {
                        this.useRootMap = true;
                        if (_context && _context.rootContext && _context.rootContext !== _context) {
                            var rootMapNode = _context.rootContext.configXml.find('map');
                            if (rootMapNode.length) {
                                var rootLayerNodes_1 = rootMapNode.find('layer');
                                rootLayerNodes_1 = _util.jQueryToPlain(rootLayerNodes_1, true, true);
                                rootMapNode = _util.jQueryToPlain(rootMapNode[0], true, true);
                                var rootMapNodeOptions = rootMapNode.options, mapNodeOptions = mapNode.options;
                                delete rootMapNode.options;
                                delete mapNode.options;
                                for (var k in mapNode) {
                                    if (mapNode[k] !== undefined)
                                        rootMapNode[k] = mapNode[k];
                                }
                                if (!rootLayerNodes_1)
                                    rootLayerNodes_1 = [];
                                if (!Array.isArray(rootLayerNodes_1))
                                    rootLayerNodes_1 = [rootLayerNodes_1];
                                if (!layerNodes)
                                    layerNodes = [];
                                if (!Array.isArray(layerNodes))
                                    layerNodes = [layerNodes];
                                layerNodes.forEach(function (l) {
                                    if (!rootLayerNodes_1.find(function (rl) {
                                        return (rl.id || rl.title) == (l.id || l.title);
                                    }))
                                        rootLayerNodes_1.push(l);
                                });
                                layerNodes = rootLayerNodes_1;
                                mapNode = rootMapNode;
                                mapNode.options = [];
                                if (rootMapNodeOptions)
                                    mapNode.options.push(rootMapNodeOptions);
                                if (mapNodeOptions)
                                    mapNode.options.push(mapNodeOptions);
                            }
                        }
                    }
                    if (mapNode.options) {
                        var evalOpt_1 = function (o) {
                            var optionScript = o.content, optionScriptUrl = o.script || o.scriptUrl;
                            delete o.content;
                            delete o.script;
                            delete o.scriptUrl;
                            Object.assign(mapNode, o);
                            if (optionScript) {
                                var ro = eval(optionScript);
                                if ($.isPlainObject(ro))
                                    Object.assign(mapNode, ro);
                            }
                            else if (optionScriptUrl) {
                                $.ajax({
                                    url: _context.fixUrl(optionScriptUrl), type: "GET", dataType: "text", async: false, success: function (data) {
                                        var ro = eval(data);
                                        if ($.isPlainObject(ro))
                                            Object.assign(mapNode, ro);
                                    }
                                });
                            }
                        };
                        var opt = mapNode.options;
                        if (!Array.isArray(opt))
                            opt = [opt];
                        opt.forEach(function (o) {
                            evalOpt_1(o);
                        });
                        delete mapNode.options;
                    }
                    if (mapNode.view) {
                        var xy = mapNode.view.split(',');
                        this.defaultView = [parseFloat(xy[1]), parseFloat(xy[0])];
                        if (!this.view) {
                            this.view = [parseFloat(xy[1]), parseFloat(xy[0])];
                        }
                    }
                    if (layerNodes && Array.isArray(layerNodes)) {
                        layerNodes.forEach(function (l) {
                            if (l.type === 'arcgis.tile' && l.fullExtent) {
                                var extentArr = l.fullExtent.split(',');
                                _this.fullExtent = [[extentArr[1], extentArr[0]], [extentArr[3], extentArr[2]]];
                            }
                            if ((l.type === 'arcgis.tile' || l.type === 'tile') && l.center) {
                                var xy = l.center.split(',');
                                _this.defaultView = [parseFloat(xy[1]), parseFloat(xy[0])];
                                if (!_this.view) {
                                    _this.view = [parseFloat(xy[1]), parseFloat(xy[0])];
                                }
                            }
                        });
                    }
                    if (!this.zoom && mapNode.zoom)
                        this.zoom = parseInt(mapNode.zoom);
                    if (mapNode.zoom)
                        this.defaultZoom = parseInt(mapNode.zoom);
                    this.layerObjs = this.layerObjs || layerNodes;
                    if (!Array.isArray(this.layerObjs))
                        this.layerObjs = [this.layerObjs];
                    if (mapNode.printable && this.printable === undefined)
                        this.printable = mapNode.printable;
                    this.drawOptions = this.drawOptions || mapNode.drawOptions;
                    if (!this.isDrawing)
                        this.isDrawing = mapNode.isDrawing;
                    this.selectOptions = this.selectOptions || mapNode.selectOptions;
                    if (!this.selectOptions)
                        this.selectOptions = mapNode.selectable !== false;
                    if (mapNode.editable !== undefined)
                        this.editable = mapNode.editable;
                    if (mapNode.refreshable !== undefined)
                        this.refreshable = mapNode.refreshable;
                    if (mapNode.movable !== undefined)
                        this.movable = mapNode.movable;
                    if (mapNode.zoombar !== undefined)
                        this.zoombar = mapNode.zoombar;
                    if (mapNode.zoombox !== undefined)
                        this.zoombox = mapNode.zoombox;
                    if (mapNode.defaultControlPosition !== undefined)
                        this.defaultControlPosition = mapNode.defaultControlPosition;
                    if (mapNode.traceView !== undefined)
                        this.traceView = mapNode.traceView;
                    if (mapNode.showLayersControl !== undefined)
                        this.showLayersControl = mapNode.showLayersControl;
                    if (mapNode.maxZoom)
                        this.mapOptions.maxZoom = parseInt(mapNode.maxZoom);
                    if (mapNode.minZoom)
                        this.mapOptions.minZoom = parseInt(mapNode.minZoom);
                    if (mapNode.zoomSnap)
                        this.mapOptions.zoomSnap = parseFloat(mapNode.zoomSnap);
                    if (!this.contextMenu && mapNode['context-menu']) {
                        var items = mapNode['context-menu'].item || [];
                        if (!Array.isArray(items))
                            items = [items];
                        this.contextMenu = items;
                        this.contextMenu.forEach(function (item) {
                            item.callback = function () {
                                _context.events.trigger(_map.EVENT_MAP_CONTEXT_MENU, item);
                            };
                        });
                    }
                    if (mapNode.crs) {
                        this.crs = mapNode.crs;
                    }
                    if (mapNode.defaultMaxZoom)
                        this.options.defaultMaxZoom = parseInt(mapNode.defaultMaxZoom);
                    if (mapNode.queryMapTypes)
                        this.queryMapTypes = mapNode.queryMapTypes.split(',');
                    if (mapNode.entityTypeField)
                        this.entityTypeField = mapNode.entityTypeField;
                    if (mapNode.entityIdField)
                        this.entityIdField = mapNode.entityIdField;
                    if (mapNode.defaultZoomLevel) {
                        this.defaultZoomLevel = parseInt(mapNode.defaultZoomLevel);
                    }
                    if (mapNode.zoomControl === false)
                        this.showZoomControl = false;
                    if (mapNode.scaleControl === false)
                        this.scaleControl = false;
                    if (mapNode.showMouseCoords === false)
                        this.showMouseCoords = false;
                    if (mapNode.showOverview !== undefined)
                        this.showOverview = mapNode.showOverview;
                    if (mapNode.initFullExtent !== undefined) {
                        this.initFullExtent = mapNode.initFullExtent;
                    }
                    if (mapNode.updateCrs !== undefined) {
                        this.updateCrs = mapNode.updateCrs;
                    }
                    if (mapNode.style) {
                        var style = document.createElement('style');
                        style.innerHTML = mapNode.style.content;
                        document.head.appendChild(style);
                    }
                    if (mapNode.useRootMapBaseLayer !== undefined) {
                        this.useRootMapBaseLayer = mapNode.useRootMapBaseLayer;
                    }
                    if (mapNode.useRootMapLayerDefs !== undefined) {
                        this.useRootMapLayerDefs = mapNode.useRootMapLayerDefs;
                    }
                    if (!this.labelControl && mapNode.labelControl)
                        this.labelControl = true;
                    if (mapNode.showLabelControl !== undefined)
                        this.labelControl = mapNode.showLabelControl;
                    if (mapNode.mouseCoordsFixNum)
                        this.mapOptions.mouseCoordsFixNum = parseInt(mapNode.mouseCoordsFixNum);
                    if (mapNode.renderEngine)
                        this.renderEngine = mapNode.renderEngine;
                    if (mapNode.fullExtent) {
                        var ss = mapNode.fullExtent.split(' '), st = ss[0].split(','), et = ss[1].split(',');
                        this.fullExtent = [st.reverse().map(function (s) { return parseFloat(s); }), et.reverse().map(function (s) { return parseFloat(s); })];
                    }
                    if (mapNode.listenObjUpdate !== undefined)
                        this.listenObjUpdate = mapNode.listenObjUpdate;
                    if (mapNode.legendOptions) {
                        this.legendOptions = mapNode.legendOptions;
                        if (Array.isArray(this.legendOptions.legend)) {
                            this.legendOptions.legends = this.legendOptions.legend;
                            delete this.legendOptions.legend;
                        }
                    }
                    _context.events.trigger(_map.EVENT_MAP_CONFIG_LOADED, this);
                }
            }
            var user = _context.getUser(true), remote = this.shouldMapLayerVisibleStoreRemote();
            if (user && this.layerObjs) {
                var baseLayer_1 = null;
                this.layerObjs.forEach(function (l) {
                    l.id = l.id || l.title || 'layer' + (_this.layerIdSeq++);
                    if (l.baseLayer) {
                        baseLayer_1 = l;
                    }
                });
                if (this.updateCrs && baseLayer_1) {
                    var layer_1 = this.doCreateLayer(baseLayer_1);
                    if (layer_1 instanceof Promise) {
                        return new Promise(function (resolve, reject) {
                            layer_1.then(function (tl) {
                                var res = _util.getStorage('MapLayerVisible-' + user.id, remote).then(function (lvs) {
                                    var _loop_4 = function (layerId) {
                                        var lo = _this.layerObjs.find(function (l) { return l.id == layerId; });
                                        if (lo && lo.forceVisible === undefined) {
                                            lo.visible = lvs[layerId];
                                        }
                                    };
                                    for (var layerId in lvs) {
                                        _loop_4(layerId);
                                    }
                                });
                                resolve(res);
                            });
                        });
                    }
                }
                return _util.getStorage('MapLayerVisible-' + user.id, remote).then(function (lvs) {
                    var _loop_5 = function (layerId) {
                        var lo = _this.layerObjs.find(function (l) { return l.id == layerId; });
                        if (lo && lo.forceVisible === undefined) {
                            lo.visible = lvs[layerId];
                        }
                    };
                    for (var layerId in lvs) {
                        _loop_5(layerId);
                    }
                });
            }
            return Promise.resolve();
        };
        Map.prototype.showLegend = function (opt) {
            var _this = this;
            if (opt === void 0) { opt = true; }
            if (typeof opt === 'object') {
                this.legendOptions = opt;
                if (this._legend) {
                    this._legend.remove();
                    this._legend = null;
                }
            }
            if (this._legend) {
                opt ? this._legend.addTo(this.lmap) : this._legend.remove();
            }
            else if (opt) {
                _util.loadCss('lib/leaflet/leaflet.legend/leaflet.legend.css');
                _util.loadScript('lib/leaflet/leaflet.legend/leaflet.legend.js', 'L.control.Legend').then(function () {
                    if (_this.legendOptions.legends)
                        _this.legendOptions.legends.forEach(function (l) {
                            if (typeof l.dashArray === 'string')
                                l.dashArray = l.dashArray.split(',');
                            if (l.type === 'polygon')
                                l.sides = l.sides ? parseInt(l.sides) : 5;
                        });
                    _this._legend = new L.control.Legend(Object.assign({ collapsed: true, position: 'topright' }, _this.legendOptions)).addTo(_this.lmap);
                });
            }
        };
        Map.prototype.shouldMapLayerVisibleStoreRemote = function () {
            var remote = _context.getSetting('MapLayerVisibleStoreRemote') !== false;
            if (remote) {
                var hasParentMap = false, pc = _context.parentContext;
                while (pc) {
                    if (pc.map) {
                        hasParentMap = true;
                        break;
                    }
                    pc = pc.parentContext;
                }
                if (hasParentMap)
                    remote = false;
            }
            return remote;
        };
        Map.prototype.readWkt = function (wktString, opt) {
            if (opt === void 0) { opt = null; }
            var wkt = new Wkt.Wkt();
            wkt.read(wktString);
            return wkt.toObject(Object.assign({}, this.lmap.defaults, opt));
        };
        Map.prototype.getWkt = function (layer) {
            return toWkt(layer);
        };
        Map.prototype.removeGraphic = function (graphic) {
            graphic.remove();
        };
        Map.prototype.locateSelf = function (options, locationHandler) {
            if (options === void 0) { options = null; }
            if (locationHandler === void 0) { locationHandler = null; }
            if (typeof options === 'function') {
                locationHandler = options;
                options = null;
            }
            if (options === false) {
                this.lmap.stopLocate();
                if (locationHandler)
                    this.lmap.off('locationfound', locationHandler);
                return;
            }
            this.lmap.locate(Object.assign({ setView: true }, options));
            if (locationHandler)
                this.lmap.on('locationfound', locationHandler);
        };
        Map.prototype.locate = function (params) {
            var _this = this;
            if (_context.getSetting('clearBeforeMapLocate'))
                this.locationGroup.clearLayers();
            if (typeof params === 'string')
                params = { shape: params };
            var highlight = params.highlight, notify = params.notify;
            var zoomto = params.zoomto !== false;
            if (zoomto)
                zoomto = typeof params.zoomto === 'number' ? params.zoomto : this.defaultZoomLevel;
            if (notify === undefined)
                notify = params;
            return new Promise(function (resolve, reject) {
                if (params.id && params.objectType) {
                    _bean.locate(params.objectType, { id: params.id, format: 'geojson' }, params.nullIfEmpty == true).then(function (feature) {
                        if (feature) {
                            feature.objectType = params.objectType;
                            feature.id = params.id;
                            if (params.id != feature.properties.id) {
                                feature.isLocateToOtherType = true;
                            }
                            var layer_2 = L.geoJSON(feature);
                            layer_2.addTo(_this.locationGroup);
                            if (highlight) {
                                if (typeof highlight === 'number')
                                    setTimeout(function () {
                                        layer_2.remove();
                                    }, highlight);
                            }
                            else
                                _this.selectCallback([feature]);
                            _this.locateByLayer(layer_2, zoomto, notify);
                            resolve(layer_2);
                        }
                        else
                            reject();
                    }, function (e) {
                    });
                }
                else if (params.shape) {
                    var layer = _this.locateByWkt(params.shape, highlight, zoomto);
                    layer ? resolve(layer) : reject();
                }
                else if (params.shapes) {
                    var layers = _this.locateByWkts(params.shapes, highlight, zoomto);
                    layers ? resolve(layers) : reject();
                }
                else
                    _this.locateByLayer(params);
            });
        };
        Map.prototype.locateByWkts = function (shapes, highlight, zoomTo) {
            if (highlight === void 0) { highlight = true; }
            if (zoomTo === void 0) { zoomTo = true; }
            if (!shapes)
                return null;
            var layers = [];
            if (shapes.length && shapes.length > 0) {
                var _loop_6 = function () {
                    var layer = this_3.readWkt(shape, { weight: 5 });
                    layers.push(layer);
                    if (highlight)
                        layer.addTo(this_3.locationGroup);
                    if (typeof highlight === 'number')
                        setTimeout(function () {
                            layer.remove();
                        }, highlight);
                    this_3.locateByLayer(layer, zoomTo, true);
                };
                var this_3 = this;
                for (var _i = 0, shapes_1 = shapes; _i < shapes_1.length; _i++) {
                    var shape = shapes_1[_i];
                    _loop_6();
                }
            }
            this.zoomTo(shapes);
            return layers;
        };
        Map.prototype.locateByWkt = function (shape, highlight, zoomTo) {
            if (highlight === void 0) { highlight = true; }
            if (zoomTo === void 0) { zoomTo = true; }
            if (!shape)
                return null;
            var layer = this.readWkt(shape);
            if (highlight)
                layer.addTo(this.locationGroup);
            if (typeof highlight === 'number')
                setTimeout(function () {
                    layer.remove();
                }, highlight);
            this.locateByLayer(layer, zoomTo, true);
            return layer;
        };
        Map.prototype.locateByLayer = function (layer, zoomto, notify) {
            if (zoomto === void 0) { zoomto = undefined; }
            if (notify === void 0) { notify = false; }
            var latlng;
            if (Array.isArray(layer) || layer.lat)
                latlng = layer;
            else if (layer.getLatLng)
                latlng = layer.getLatLng();
            else if (layer.getBounds) {
                var b = layer.getBounds(), ne = b.getNorthEast(), sw = b.getSouthWest();
                if (ne.lat === sw.lat && ne.lng === sw.lng)
                    latlng = ne;
            }
            if (zoomto !== false) {
                if (!latlng && layer.getBounds)
                    this.lmap.fitBounds(layer.getBounds());
                else if (latlng) {
                    var level = this.defaultZoomLevel;
                    if (typeof zoomto === 'number')
                        level = zoomto;
                    if (level !== undefined) {
                        if (level < 0)
                            level = this.lmap.getMaxZoom() + level;
                        if (this.lmap.getZoom() < level)
                            this.lmap.setZoom(level);
                    }
                    this.lmap.panTo(latlng);
                }
            }
            if (notify)
                _context.events.trigger(_map.EVENT_MAP, { method: 'locate', map: this, data: notify === true ? {} : notify });
            return layer;
        };
        Map.prototype.locateDraw = function (params) {
            var _this = this;
            var highlight = params.highlight, notify = params.notify !== false;
            var zoomto = params.zoomto !== false;
            if (zoomto)
                zoomto = typeof params.zoomto === 'number' ? params.zoomto : this.defaultZoomLevel;
            return new Promise(function (resolve, reject) {
                if (params) {
                    var layers = _this.locateByWktsAndConfig(params, highlight, zoomto);
                    layers ? resolve(layers) : reject();
                }
                else
                    _this.locateByLayer(params);
            });
        };
        Map.prototype.locateByWktsAndConfig = function (wktAndConfigs, highlight, zoomTo, clear) {
            if (highlight === void 0) { highlight = true; }
            if (zoomTo === void 0) { zoomTo = true; }
            if (clear === void 0) { clear = true; }
            if (!wktAndConfigs)
                return null;
            var layers = [];
            if (wktAndConfigs.length && wktAndConfigs.length > 0) {
                if (clear && this.locationGroup.getLayers().length)
                    this.locationGroup.clearLayers();
                var _loop_7 = function () {
                    if (wktAndConfig.shape != null) {
                        var layer_3 = this_4.readWkt(wktAndConfig.shape, wktAndConfig.options);
                        layers.push(layer_3);
                        if (highlight)
                            layer_3.addTo(this_4.locationGroup);
                        if (typeof highlight === 'number')
                            setTimeout(function () {
                                layer_3.remove();
                            }, highlight);
                        this_4.locateByLayer(layer_3, zoomTo, true);
                    }
                };
                var this_4 = this;
                for (var _i = 0, wktAndConfigs_1 = wktAndConfigs; _i < wktAndConfigs_1.length; _i++) {
                    var wktAndConfig = wktAndConfigs_1[_i];
                    _loop_7();
                }
            }
            this.zoomTo(layers);
            return layers;
        };
        Map.prototype.zoomTo = function (shape) {
            var bounds = this.getBounds(shape);
            if (bounds)
                this.lmap.fitBounds(bounds);
        };
        Map.prototype.activeDraw = function (options, listener, minimizeAllMdiWindow) {
            if (options === void 0) { options = {}; }
            if (listener === void 0) { listener = null; }
            if (minimizeAllMdiWindow === void 0) { minimizeAllMdiWindow = false; }
            var active = options !== false;
            if (active && this.isDrawing)
                this.activeDraw(false);
            this.bindLayerPopups(!active);
            this.isDrawing = active;
            if (!active && !this.drawControl)
                return;
            if (!listener)
                listener = typeof options === 'function' ? options : null;
            if (typeof options === 'string') {
                var mode = options;
                options = { draw: {}, initMode: mode };
                options.draw[mode] = true;
            }
            if (!this.drawControl) {
                var map = this.lmap;
                var editableLayers = this.drawLayerGroup;
                map.addLayer(editableLayers);
                options = _util.merges({
                    position: this.defaultControlPosition,
                    draw: {
                        circle: false, rectangle: false, marker: false, polyline: false, polygon: false
                    },
                    edit: {
                        featureGroup: editableLayers,
                        remove: false
                    }
                }, [this.drawOptions, options]);
                var drawControl = new L.Control.Draw(options);
                map.addControl(drawControl);
                this.drawControl = drawControl;
            }
            this.lmap.off(L.Draw.Event.CREATED);
            if (!active) {
                this.enableDrawHandler(false);
                if (L.toolbar && L.toolbar._toolbars['draw']) {
                    try {
                        L.toolbar._toolbars['draw'].disable();
                    }
                    catch (e) {
                        console.error('画图工具draw关闭异常');
                    }
                }
                if (L.toolbar && L.toolbar._toolbars['edit']) {
                    try {
                        L.toolbar._toolbars['edit'].disable();
                    }
                    catch (e) {
                        console.error('画图工具edit关闭异常');
                    }
                }
                this.lmap.removeControl(this.drawControl);
                this.lmap.removeLayer(this.drawLayerGroup);
                this.drawControl = null;
                this.isDrawSelecting = false;
            }
            else {
                var mdis_1;
                if (minimizeAllMdiWindow)
                    mdis_1 = _sui.minimizeAllMdiWindow(true);
                this.lmap.addControl(this.drawControl);
                this.lmap.on(L.Draw.Event.CREATED, function (e) {
                    var type = e.layerType, layer = e.layer;
                    editableLayers.addLayer(layer);
                    if (listener)
                        listener(type, layer, editableLayers, e);
                    _context.events.trigger(_map.EVENT_MAP, { method: 'draw', data: e });
                    if (mdis_1)
                        mdis_1.each(function () {
                            this.__componentInstance.restore();
                        });
                });
                if (options.initMode)
                    this.enableDrawHandler(options.initMode);
            }
        };
        Map.prototype.enableDrawHandler = function (type) {
            var tb = L.toolbar._toolbars['draw'];
            if (tb && tb._modes) {
                if (type === false)
                    for (var k in tb._modes)
                        tb._modes[k].handler.disable();
                else if (tb._modes[type])
                    tb._modes[type].handler.enable();
            }
        };
        Map.prototype.enableEditHandler = function (v) {
            if (v === void 0) { v = true; }
            try {
                var tb = L.toolbar._toolbars['edit'];
                if (tb && tb._modes['edit'])
                    tb._modes['edit'].handler[v ? 'enable' : 'disable']();
            }
            catch (e) {
                console.error('enableEditHandler方法异常');
            }
        };
        Map.prototype.changeDrawIcon = function (drawType, iconOpt) {
            if (this.drawControl) {
                var tb = L.toolbar._toolbars['draw'];
                tb._modes[drawType].handler.options.icon = L.icon(iconOpt);
            }
        };
        Map.prototype.addZoomControl = function () {
            if (this._zoomControl)
                return;
            this._zoomControl = L.control.zoom({ position: this.defaultControlPosition, zoomOutTitle: _locale.map.zoomOutTitle || '缩小', zoomInTitle: _locale.map.zoomInTitle || '放大' }).addTo(this.lmap);
        };
        Map.prototype.addSelectControl = function () {
            var _this = this;
            if (this.selectCtr)
                return;
            var thiz = this;
            var SelectControl = L.Control.extend({
                onAdd: function (map) {
                    var c = L.DomUtil.create('div', 'leaflet-control leaflet-bar');
                    var btn = L.DomUtil.create('a', 'fa fa-mouse-pointer', c);
                    btn.href = '#';
                    btn.title = _locale.map.selectModeText;
                    L.DomEvent.on(btn, 'click', L.DomEvent.stop).on(btn, 'click', function () {
                        thiz.toggleDrawSelect();
                    });
                    return c;
                }
            });
            this.selectCtr = new SelectControl({ position: this.defaultControlPosition }).addTo(this.lmap);
            this.lmap.on('singleclick', function (e) {
                if (thiz.isDrawSelecting || thiz.isDrawing)
                    return;
                if (thiz.clickSelect) {
                    if (thiz.isEditing) {
                        if (thiz.drawLayerGroup.getLayers().find(function (l) {
                            return l.feature && l.feature.geometry.type.toLowerCase().indexOf('polygon') !== -1;
                        }))
                            return;
                    }
                    thiz.select(L.marker(e.latlng));
                }
            });
            document.addEventListener('keydown', function (e) {
                if (e.keyCode === 16 || e.keyCode === 17)
                    _this.appendSelecting = true;
            });
            document.addEventListener('keyup', function (e) {
                _this.appendSelecting = false;
            });
        };
        Map.prototype.addEditControl = function () {
            if (this.editCtr)
                return;
            var thiz = this;
            var EditControl = L.Control.extend({
                onAdd: function (map) {
                    var c = L.DomUtil.create('div', 'leaflet-control leaflet-bar');
                    var btn = L.DomUtil.create('a', 'fa fa-lock', c);
                    btn.href = '#';
                    btn.title = _locale.map.editModeText;
                    L.DomEvent.on(btn, 'click', L.DomEvent.stop).on(btn, 'click', function () {
                        thiz.toggleEdit();
                    });
                    return c;
                }
            });
            this.editCtr = new EditControl({ position: this.defaultControlPosition }).addTo(this.lmap);
        };
        Map.prototype.addRefreshControl = function () {
            if (this.refreshCtr)
                return;
            var thiz = this;
            var RefreshControl = L.Control.extend({
                onAdd: function (map) {
                    var c = L.DomUtil.create('div', 'leaflet-control leaflet-bar');
                    var btn = L.DomUtil.create('a', 'fa fa-sync', c);
                    btn.href = '#';
                    btn.title = _locale.map.refreshText;
                    L.DomEvent.on(btn, 'click', L.DomEvent.stop).on(btn, 'click', function () {
                        thiz.resetMode();
                        thiz.refresh();
                    });
                    return c;
                }
            });
            this.refreshCtr = new RefreshControl({ position: this.defaultControlPosition }).addTo(this.lmap);
        };
        Map.prototype.addMoveControl = function () {
            if (this.moveControl)
                return;
            var thiz = this;
            var MoveControl = L.Control.extend({
                onAdd: function (map) {
                    var c = L.DomUtil.create('div', 'leaflet-control leaflet-bar');
                    var btn = L.DomUtil.create('a', 'fa', c);
                    btn.innerHTML = '<svg width="28" height="28" xmlns="http://www.w3.org/2000/svg"><g><line stroke-linecap="undefined" stroke-linejoin="undefined" id="svg_1" y2="3" x2="13.5" y1="6" x1="8" stroke-width="1.5" stroke="#000" fill="none"/><line stroke-linecap="undefined" stroke-linejoin="undefined" id="svg_2" y2="3" x2="13.5" y1="6" x1="19" stroke-width="1.5" stroke="#000" fill="none"/><line stroke-linecap="undefined" stroke-linejoin="undefined" id="svg_3" y2="13.5" x2="3" y1="8" x1="6" stroke-width="1.5" stroke="#000" fill="none"/><line stroke-linecap="undefined" stroke-linejoin="undefined" id="svg_4" y2="13.5" x2="3" y1="19" x1="6" stroke-width="1.5" stroke="#000" fill="none"/><line stroke-linecap="undefined" stroke-linejoin="undefined" id="svg_5" y2="24" x2="13.5" y1="21" x1="8" stroke-width="1.5" stroke="#000" fill="none"/><line stroke-linecap="undefined" stroke-linejoin="undefined" id="svg_6" y2="24" x2="13.5" y1="21" x1="19" stroke-width="1.5" stroke="#000" fill="none"/><line stroke-linecap="undefined" stroke-linejoin="undefined" id="svg_7" y2="13.5" x2="24" y1="8" x1="21" stroke-width="1.5" stroke="#000" fill="none"/><line stroke-linecap="undefined" stroke-linejoin="undefined" id="svg_8" y2="13.5" x2="24" y1="19" x1="21" stroke-width="1.5" stroke="#000" fill="none"/><circle cx="13.5" cy="13.5" r="5" stroke="#c0c0c0" stroke-width="1.5" fill="#808080"/></g></svg>';
                    btn.addEventListener("mousedown", function (e) {
                        e.stopPropagation();
                        e.preventDefault();
                        var x = (e.layerX / 27) - 0.5;
                        var y = (e.layerY / 27) - 0.5;
                        var mx = Math.abs(x);
                        var my = Math.abs(y);
                        if (((mx * mx) + (my * my)) > 0.055) {
                            var point = new L.Point(0, 0);
                            if (Math.abs(x) >= Math.abs(y)) {
                                if (x > 0) {
                                    point.x = map.getContainer().offsetWidth;
                                }
                                else {
                                    point.x = -map.getContainer().offsetWidth;
                                }
                            }
                            else {
                                if (y > 0) {
                                    point.y = map.getContainer().offsetHeight;
                                }
                                else {
                                    point.y = -map.getContainer().offsetHeight;
                                }
                            }
                            map.panBy(point);
                        }
                        else {
                            map.setView(thiz.defaultView, thiz.defaultZoom || 0);
                        }
                    });
                    return c;
                }
            });
            this.moveControl = new MoveControl({ position: this.defaultControlPosition }).addTo(this.lmap);
        };
        Map.prototype.addZoomBoxControl = function () {
            if (this.zoomboxControl)
                return;
            var thiz = this;
            var ZoomBoxControl = L.Control.extend({
                onAdd: function (map) {
                    var c = L.DomUtil.create('div', 'leaflet-control leaflet-bar');
                    var btn = L.DomUtil.create('a', 'fa', c);
                    btn.innerHTML = "⬜";
                    btn.addEventListener("click", function (e) {
                        e.stopPropagation();
                        e.preventDefault();
                        var drawOptions = {
                            draw: {
                                circle: false,
                                rectangle: true,
                                marker: false,
                                polyline: false,
                                polygon: false
                            }
                        };
                        drawOptions.initMode = 'rectangle';
                        var isdown = false;
                        var olddragging = thiz.lmap.dragging;
                        thiz.lmap.dragging.disable();
                        var ptp = null;
                        var lastrect = null;
                        function md(e2) {
                            if (!isdown) {
                                isdown = true;
                                ptp = e2.latlng;
                                lastrect = L.rectangle([[e2.latlng.lat, e2.latlng.lng], [e2.latlng.lat, e2.latlng.lng]], { color: "#00437d", weight: 1 });
                                thiz.addLeafletLayer(lastrect, thiz.getCreateLayerGroup());
                            }
                        }
                        function mo(e2) {
                            if (isdown) {
                                thiz.getCreateLayerGroup().removeLayer(lastrect);
                                lastrect = L.rectangle([[ptp.lat, ptp.lng], [e2.latlng.lat, e2.latlng.lng]], { color: "#00437d", weight: 1 });
                                thiz.addLeafletLayer(lastrect, thiz.getCreateLayerGroup());
                            }
                        }
                        function mu(e2) {
                            if (isdown) {
                                thiz.getCreateLayerGroup().removeLayer(lastrect);
                                thiz.zoomTo(lastrect);
                                thiz.lmap.dragging.enable();
                                thiz.off("mousedown", md);
                                thiz.off("mousemove", mo);
                                thiz.off("mouseup", mu);
                            }
                        }
                        thiz.on("mousedown", md);
                        thiz.on("mousemove", mo);
                        thiz.on("mouseup", mu);
                    });
                    return c;
                }
            });
            this.zoomboxControl = new ZoomBoxControl({ position: this.defaultControlPosition }).addTo(this.lmap);
        };
        Map.prototype.addZoomBarControl = function () {
            if (this.zoombarControl)
                return;
            var thiz = this;
            this.lmap.on('zoomend', function () {
                var dyna = document.getElementById("svg_zbdyna");
                var maxzoom = thiz.lmap.getMaxZoom();
                var ycur = (_context.map.lmap.getZoom()) * 120 / (maxzoom + 1) + 1;
                dyna.setAttribute("y2", ycur.toString());
                dyna.setAttribute("y1", (ycur + 120 / (maxzoom + 1)).toString());
            });
            var ZoomBarControl = L.Control.extend({
                onAdd: function (map) {
                    var c = L.DomUtil.create('div', 'leaflet-control leaflet-bar');
                    var btn = L.DomUtil.create('a', 'fa', c);
                    btn.style.width = "5px";
                    btn.style.height = "122px";
                    var strc = [];
                    strc.push('<svg width="15" height="122" xmlns="http://www.w3.org/2000/svg"><g><line stroke-linecap="undefined" stroke-linejoin="undefined" id="svg_1" y2="121" x2="1" y1="1" x1="1" stroke-width="1.5" stroke="#404040" fill="none"/>');
                    var maxzoom = thiz.lmap.getMaxZoom();
                    for (var i = 0; i < maxzoom + 2; i++) {
                        var yi = 120 / (maxzoom + 1) * i + 1;
                        strc.push('<line stroke-linecap="undefined" stroke-linejoin="undefined" id="svg_l' + i + '" y2="' + yi + '" x2="1" y1="' + yi + '" x1="5" stroke-width="1.5" stroke="#909090" fill="none"/>');
                    }
                    var ycur = (thiz.zoom) * 120 / (maxzoom + 1) + 1;
                    strc.push('<line stroke-linecap="undefined" stroke-linejoin="undefined" id="svg_zbdyna" y2="' + ycur + '" x2="3.5" y1="' + (ycur + 120 / (maxzoom + 1)) + '" x1="3.5" stroke-width="3" stroke="#ffa060" fill="#ffc080"/>');
                    strc.push('</g></svg>');
                    btn.innerHTML = strc.join("");
                    btn.addEventListener("mousedown", function (e) {
                        e.stopPropagation();
                        e.preventDefault();
                        var maxzoom2 = thiz.lmap.getMaxZoom();
                        var yd = Math.floor((e.offsetY - 6) * (maxzoom2 + 1) / 120) + 1;
                        thiz.lmap.setZoom(yd);
                    });
                    return c;
                }
            });
            this.zoombarControl = new ZoomBarControl({ position: this.defaultControlPosition }).addTo(this.lmap);
        };
        Map.prototype.addLabelControl = function () {
            var ctr = this.labelControl;
            if (ctr === undefined)
                ctr = this.objectsLayers.length > 0;
            if (ctr && typeof ctr !== 'object') {
                var self_2 = this;
                var LabelControl = L.Control.extend({
                    onAdd: function (map) {
                        var c = L.DomUtil.create('div', 'leaflet-control leaflet-bar');
                        var btn;
                        if (_context.clientConf.zjYaxinGis) {
                            btn = L.DomUtil.create('a', 'far fa-eye-slash', c);
                            self_2.setLabelVisible(false);
                        }
                        else {
                            btn = L.DomUtil.create('a', 'fab fa-adn', c);
                        }
                        btn.href = '#';
                        btn.title = _locale.map.labelModeText;
                        var states = ['auto', true, false], icons = ['fab fa-adn', 'far fa-eye', 'far fa-eye-slash'];
                        L.DomEvent.on(btn, 'click', L.DomEvent.stop).on(btn, 'click', function () {
                            var state = $(this).data('labelstate');
                            if (state === undefined)
                                state = 'auto';
                            var pos = states.indexOf(state) + 1;
                            if (pos === states.length)
                                pos = 0;
                            state = states[pos];
                            if (state === 'auto')
                                self_2.resetLabelVisible();
                            else
                                self_2.setLabelVisible(state);
                            $(this).removeClass(icons.join(' ')).addClass(icons[pos]);
                            $(this).data('labelstate', state);
                        });
                        return c;
                    }
                });
                ctr = new LabelControl({ position: this.defaultControlPosition });
            }
            if (ctr)
                ctr.addTo(this.lmap);
        };
        Map.prototype.addOverviewControl = function () {
            var _this = this;
            var map = this.lmap, div = $('<div class="leaflet-overview">').appendTo($(map.getContainer()).parent()), i = $('<i id="ovToggleBtn" class="icon arrow up">').appendTo(div), tools = $('<div id="ovTools">').appendTo(div);
            var ovmap = L.map(div[0], { maxZoom: map.options.maxZoom, crs: map.options.crs, attributionControl: false, zoomControl: false, contextmenu: false });
            ovmap.fitBounds(map.getBounds().pad(1.5));
            var rect = L.rectangle(map.getBounds(), { color: "#ff0000", weight: 2, interactive: false }).addTo(ovmap);
            ovmap.on('singleclick', function (e) {
                map.setView(e.latlng);
            });
            function _updaterect() {
                var size = ovmap.getSize(), bounds = ovmap.getBounds();
                var boundsWidth = bounds.getNorthEast().lng - bounds.getSouthWest().lng;
                var sizeWidth = size.x;
                var buffer = boundsWidth / sizeWidth * 4;
                var mapBounds = map.getBounds(), sw = mapBounds.getSouthWest(), ne = mapBounds.getNorthEast();
                mapBounds = new L.LatLngBounds(new L.LatLng(sw.lat - buffer, sw.lng - buffer), new L.LatLng(ne.lat + buffer, ne.lng + buffer));
                rect.setBounds(mapBounds);
            }
            map.on('moveend', function (e) {
                _updaterect();
                if (!ovmap.getBounds().contains(map.getBounds()))
                    ovmap.fitBounds(map.getBounds());
            });
            ovmap.on('moveend', function (e) {
                _updaterect();
            });
            var layerIdx = 0;
            if (typeof this.showOverview === 'number')
                layerIdx = this.showOverview;
            else if (typeof this.showOverview === 'string' && !isNaN(parseInt(this.showOverview)))
                layerIdx = parseInt(this.showOverview);
            var lo = this.layerObjs[layerIdx];
            if (!lo)
                return;
            var layer = this.doCreateLayer(lo);
            if (layer instanceof Promise) {
                layer.then(function (tl) { return addtoov(tl); });
            }
            else if (layer)
                addtoov(layer);
            i.click(function () {
                div.toggleClass('collapsed');
                return false;
            });
            function addtoov(l) {
                l.addTo(ovmap);
                setTimeout(function () {
                    div.addClass('leaflet-overview-collapse-animate');
                    i.click();
                    setTimeout(function () { return div.removeClass('leaflet-overview-collapse-animate'); }, 2000);
                }, 2000);
            }
            var boundStack = [map.getBounds()], pointer = 0, back, forw;
            var fromhis;
            function checkStackBtns() {
                back.toggleClass('disabled', pointer <= 0);
                forw.toggleClass('disabled', pointer >= boundStack.length - 1);
            }
            back = $('<i class="icon ong arrow alternate left">').appendTo(tools).click(function (e) {
                fromhis = true;
                map.fitBounds(boundStack[--pointer], { noMoveStart: true });
                checkStackBtns();
                return false;
            });
            forw = $('<i class="icon ong arrow alternate right">').appendTo(tools).click(function (e) {
                fromhis = true;
                map.fitBounds(boundStack[++pointer], { noMoveStart: true });
                checkStackBtns();
                return false;
            });
            map.on('moveend', function () {
                if (fromhis) {
                    fromhis = false;
                    return;
                }
                while (boundStack.length > 30)
                    boundStack.shift();
                boundStack.push(map.getBounds());
                pointer = boundStack.length - 1;
                checkStackBtns();
            });
            checkStackBtns();
            var addfullbtn = function () {
                $('<i class="icon expand">').appendTo(tools).click(function (e) {
                    map.fitBounds(_this.fullExtent);
                    return false;
                });
            };
            if (this.fullExtent) {
                addfullbtn();
            }
            else {
                var arcgisTileLayer = this.getBaseLayers().find(function (l) { return l.type === 'arcgis.tile'; });
                var configArcgisTileLayer_1 = arcgisTileLayer;
                if (arcgisTileLayer) {
                    arcgisTileLayer = arcgisTileLayer._layer;
                    arcgisTileLayer.metadata(function (e, metadata) {
                        var f = metadata.fullExtent;
                        if (configArcgisTileLayer_1.fullExtent && configArcgisTileLayer_1.fullExtent.split(',').length == 4) {
                            var fullExtentArr = configArcgisTileLayer_1.fullExtent.split(',');
                            _this.fullExtent = [[fullExtentArr[1], fullExtentArr[0]], [fullExtentArr[3], fullExtentArr[2]]];
                        }
                        else {
                            _this.fullExtent = [[f.ymin, f.xmin], [f.ymax, f.xmax]];
                        }
                        addfullbtn();
                        if (_this.initFullExtent && _this.fullExtent) {
                            var center = _this.getCenter();
                            var minx = new Number(_this.fullExtent[0][0]);
                            var miny = new Number(_this.fullExtent[0][1]);
                            var maxx = new Number(_this.fullExtent[1][0]);
                            var maxy = new Number(_this.fullExtent[1][1]);
                            var lat = new Number(center.lat);
                            var lng = new Number(center.lng);
                            if (lat < minx || lng < miny || lat > maxx || lng > maxy) {
                                _this.lmap.fitBounds(_this.fullExtent);
                            }
                        }
                    });
                }
            }
        };
        Map.prototype.setLabelVisible = function (v) {
            this.objectsLayers.forEach(function (ol) { return ol.setLabelVisible(v); });
        };
        Map.prototype.resetLabelVisible = function () {
            this.objectsLayers.forEach(function (ol) { return ol.resetLabelVisible(); });
        };
        Map.prototype.toggleDrawSelect = function () {
            this.activeDrawSelect(!this.isDrawSelecting);
        };
        Map.prototype.resetMode = function () {
            this.activeDrawSelect(false);
            this.activeDraw(false);
            this.activeEdit(false);
        };
        Map.prototype.toggleEdit = function () {
            this.activeEdit(!this.isEditing);
        };
        Map.prototype.getLayerGroup = function (layer) {
            if (layer._originalGroup && layer._originalGroup.hasLayer(layer))
                return layer._originalGroup;
            var group;
            this.lmap.eachLayer(function (l) {
                if (l.hasLayer && l.hasLayer(layer)) {
                    group = l;
                }
            });
            return group;
        };
        Map.prototype.activeBatchMove = function (selectFeatureList) {
            var _this = this;
            var drawGroup = this.drawLayerGroup;
            drawGroup.clearLayers();
            this.tempLayerGroup.clearLayers();
            var markerList = [];
            var pointList = [];
            var polylineList = [];
            var polygonList = [];
            var markerHtml = '<div style="text-align: center;"><span>*</span></div>';
            selectFeatureList.forEach(function (selectFeature) {
                if (selectFeature.geometry.type === 'Point') {
                    var pointArr = selectFeature.geometry.coordinates;
                    var marker = L.marker([pointArr[1], pointArr[0]], { icon: _this.createDivIcon({ html: markerHtml }) });
                    var geoJSON = L.geoJSON(selectFeature, { poly: { allowIntersection: false } });
                    geoJSON.eachLayer(function (l) {
                        pointList.push(l);
                    });
                    drawGroup.addLayer(marker);
                    markerList.push(marker);
                }
                else if (selectFeature.geometry.type === 'LineString') {
                    var geoJSON = L.geoJSON(selectFeature, { poly: { allowIntersection: false } });
                    var polyline_1 = null;
                    geoJSON.eachLayer(function (l) {
                        _this.tempLayerGroup.addLayer(l);
                        polylineList.push(l);
                        polyline_1 = l;
                    });
                    var paths = selectFeature.geometry.coordinates;
                    paths.forEach(function (pointArr, index) {
                        var marker = L.marker([pointArr[1], pointArr[0]], {
                            icon: _this.createDivIcon({ html: markerHtml })
                        });
                        drawGroup.addLayer(marker);
                        markerList.push(marker);
                        marker.belongingPolyline = polyline_1;
                        marker.pointLocationPolylineIndex = index;
                    });
                }
                else if (selectFeature.geometry.type === 'Polygon') {
                    var geoJSON = L.geoJSON(selectFeature, { poly: { allowIntersection: false } });
                    var polygon_1 = null;
                    geoJSON.eachLayer(function (l) {
                        _this.tempLayerGroup.addLayer(l);
                        polygonList.push(l);
                        polygon_1 = l;
                    });
                    var paths = selectFeature.geometry.coordinates;
                    paths.forEach(function (path, xIndex) {
                        path.forEach(function (pointArr, yIndex) {
                            var marker = L.marker([pointArr[1], pointArr[0]], {
                                icon: _this.createDivIcon({ html: markerHtml })
                            });
                            drawGroup.addLayer(marker);
                            markerList.push(marker);
                            marker.belongingPolygon = polygon_1;
                            marker.pointLocationPolygonXindex = xIndex;
                            marker.pointLocationPolygonYindex = yIndex;
                        });
                    });
                }
            });
            markerList.forEach(function (marker) {
                marker.oldLatLng = marker.getLatLng();
                marker.on('drag', function (e) {
                    var offsetx = e.latlng.lng - marker.oldLatLng.lng;
                    var offsety = e.latlng.lat - marker.oldLatLng.lat;
                    if (offsetx !== 0 && offsety !== 0) {
                        markerList.forEach(function (otherMarker) {
                            if (otherMarker !== marker) {
                                var oldLatLng = otherMarker.oldLatLng;
                                otherMarker.setLatLng([oldLatLng.lat + offsety, oldLatLng.lng + offsetx]);
                                otherMarker.edited = true;
                            }
                        });
                        updateAllPolygon();
                        updateAllPolyline();
                    }
                });
            });
            function updateAllPolygon() {
                markerList.forEach(function (marker) {
                    var belongingPolygon = marker.belongingPolygon;
                    var pointLocationPolygonXindex = marker.pointLocationPolygonXindex;
                    var pointLocationPolygonYindex = marker.pointLocationPolygonYindex;
                    if (belongingPolygon) {
                        var latLngList = belongingPolygon.getLatLngs();
                        if (latLngList && pointLocationPolygonXindex < latLngList.length) {
                            var latLngs = latLngList[pointLocationPolygonXindex];
                            if (latLngs && pointLocationPolygonYindex < latLngs.length) {
                                var latLng = latLngs[pointLocationPolygonYindex];
                                var markerLatLng = marker.getLatLng();
                                latLng.lat = markerLatLng.lat;
                                latLng.lng = markerLatLng.lng;
                                belongingPolygon.setLatLngs(latLngList);
                            }
                        }
                    }
                });
            }
            function updateAllPolyline() {
                markerList.forEach(function (marker) {
                    var belongingPolyline = marker.belongingPolyline;
                    var pointLocationPolylineIndex = marker.pointLocationPolylineIndex;
                    if (belongingPolyline) {
                        var latLngList = belongingPolyline.getLatLngs();
                        if (latLngList && pointLocationPolylineIndex < latLngList.length) {
                            var latLng = latLngList[pointLocationPolylineIndex];
                            var markerLatLng = marker.getLatLng();
                            latLng.lat = markerLatLng.lat;
                            latLng.lng = markerLatLng.lng;
                            belongingPolyline.setLatLngs(latLngList);
                        }
                    }
                });
            }
            this.activeDraw(false);
            this.activeDraw({ draw: false });
            this.clearSelection();
            this.enableEditHandler();
            var getResult = function () {
                return { markerList: markerList, pointList: pointList, polylineList: polylineList, polygonList: polygonList };
            };
            var endMove = function () {
                _this.activeDraw(false);
                _this.enableEditHandler(false);
                drawGroup.clearLayers();
                _this.tempLayerGroup.clearLayers();
            };
            this.lmap.off(L.Draw.Event.EDITED);
            this.lmap.on(L.Draw.Event.EDITED, function (e) {
                _context.events.trigger(_map.EVENT_BATCH_MOVE_END, getResult());
            });
            return { getResult: getResult, endMove: endMove };
        };
        Map.prototype.activeEdit = function (active, listener) {
            var _this = this;
            if (active === void 0) { active = true; }
            if (listener === void 0) { listener = null; }
            if (active === false && listener === true) {
                if (L.toolbar && L.toolbar._toolbars && L.toolbar._toolbars.edit && L.toolbar._toolbars.edit.enabled())
                    L.toolbar._toolbars.edit._save();
            }
            this.isEditing = active;
            this.activeDraw(false);
            if (this.editCtr) {
                var c = $(this.editCtr.getContainer());
                c.find('a.fa').removeClass('fa-lock fa-unlock').addClass(active ? 'fa-unlock' : 'fa-lock');
            }
            this.activeDraw(active ? { draw: false } : false);
            this.isDrawing = false;
            var drawGroup = this.drawLayerGroup;
            var newadded = drawGroup.newadded;
            if (!newadded)
                newadded = drawGroup.newadded = [];
            var tempLines;
            var self = this;
            this.lmap.off(L.Draw.Event.EDITED);
            this.lmap.off(L.Draw.Event.EDITSTOP);
            this.lmap.off(L.Draw.Event.EDITMOVE);
            this.lmap.off(L.Draw.Event.EDITSTART);
            this.lmap[active ? 'on' : 'off'](L.Draw.Event.EDITED, edited);
            this.lmap[active ? 'on' : 'off'](L.Draw.Event.EDITSTOP, editstop);
            this.lmap[active ? 'on' : 'off'](L.Draw.Event.EDITMOVE, editmove);
            this.lmap[active ? 'on' : 'off'](L.Draw.Event.EDITSTART, editstart);
            if (active) {
                this.lmap.closePopup();
                var toEdit = void 0, isBatchMove_1 = false;
                if (Array.isArray(active))
                    toEdit = active;
                else if (typeof active === 'object')
                    toEdit = [active];
                else if (this.selectFeatures) {
                    toEdit = this.selectFeatures.filter(function (f) { return _this.isEditableFeature(f); });
                    var featurePoints = toEdit.filter(function (f) { return f.geometry.type === 'Point'; });
                    if (featurePoints.length !== 0 && featurePoints.length < toEdit.length)
                        toEdit = featurePoints;
                    if (toEdit.length && toEdit.length === featurePoints.length) {
                        isBatchMove_1 = (toEdit.length > 1);
                    }
                }
                if (toEdit)
                    toEdit.forEach(function (t) {
                        if (t.addTo) {
                            t._fromGroup_ = _this.getLayerGroup(t);
                            t._fromGroup_.removeLayer(t);
                            drawGroup.addLayer(t);
                            newadded.push(t);
                        }
                        else {
                            var lg = L.geoJSON(t, { poly: { allowIntersection: false } });
                            lg.eachLayer(function (l) {
                                drawGroup.addLayer(l);
                                newadded.push(l);
                            });
                        }
                        if (isBatchMove_1)
                            newadded.forEach(function (l) {
                                l._originll = l.getLatLng();
                                l.on('drag', function (e) {
                                    var offsetx = e.latlng.lng - l._originll.lng, offsety = e.latlng.lat - l._originll.lat;
                                    if (offsetx !== 0 && offsety !== 0)
                                        drawGroup.eachLayer(function (nl) {
                                            if (nl !== l) {
                                                var old = nl._originll;
                                                nl.setLatLng([old.lat + offsety, old.lng + offsetx]);
                                                nl.edited = true;
                                            }
                                        });
                                });
                            });
                    });
                this.clearSelection();
                this.enableEditHandler();
            }
            else
                editstop();
            var crs = this.lmap.options.crs;
            if (crs)
                crs = crs.code;
            function edited(e) {
                var updatedShapeObjs = [];
                e.layers.eachLayer(function (l) {
                    if (typeof listener === 'function')
                        listener(l);
                    if (!l.feature)
                        return;
                    var obj = l.feature, objtype = obj.objectType, id = obj.id;
                    var mapObjectType, mapObjectId;
                    if (id != obj.properties.id) {
                        mapObjectType = obj.properties.objectType, mapObjectId = obj.properties.id;
                    }
                    updatedShapeObjs.push({ source: obj, objectType: objtype, id: id, shape: toWkt(l), crs: crs, _mapObjectType: mapObjectType, _mapObjectId: mapObjectId });
                });
                if (updatedShapeObjs.length)
                    _context.events.trigger(_map.EVENT_SHAPE_UPDATED, { data: updatedShapeObjs, map: self });
                editstop();
                self.activeEdit(false);
            }
            function editmove(e) {
            }
            function editstop() {
                newadded.forEach(function (l) {
                    drawGroup.removeLayer(l);
                    if (l._fromGroup_) {
                        l._fromGroup_.addLayer(l);
                        delete l._fromGroup_;
                    }
                });
                newadded.length = 0;
                if (tempLines)
                    tempLines.forEach(function (l) {
                        l.remove();
                    });
            }
            function editstart() {
                var edittingLayers = drawGroup.getLayers();
                if (edittingLayers.length == 1) {
                    var edittingLayer_1 = edittingLayers[0], t = edittingLayer_1.feature;
                    if (t && t.geometry && t.geometry.type === 'Point') {
                        var cps = self.getEditingConnectPoints(t);
                        if (cps) {
                            _util.dataCallback(function (points) {
                                if (points && points.length) {
                                    tempLines = [];
                                    points.forEach(function (p) {
                                        var l = self.createPolyline([p, p], Object.assign({ interactive: false, clickable: false, opacity: .8, weight: 3, dashArray: [5, 10] }));
                                        tempLines.push(l);
                                    });
                                    edittingLayer_1.on('drag', function (e) {
                                        tempLines.forEach(function (l) {
                                            l.setLatLngs([l.getLatLngs()[0], e.latlng]);
                                            l.bindTooltip(self.length(l, 1) + 'm').openTooltip();
                                        });
                                    });
                                }
                            }, cps);
                        }
                    }
                }
            }
        };
        Map.prototype.getEditingConnectPoints = function (feature) {
            var coords = feature.geometry.coordinates, lat = coords[1], lng = coords[0];
            var gs = [];
            this.objectsLayers.forEach(function (ol) {
                ol.getGraphicObjs().forEach(function (g) {
                    if (g.geometry.type === 'LineString' && g.geometry.coordinates.length === 2) {
                        coords = g.geometry.coordinates;
                        var a = coords[0], z = coords[1];
                        if (lng == a[0] && lat == a[1])
                            gs.push([z[1], z[0]]);
                        else if (lng == z[0] && lat == z[1])
                            gs.push([a[1], a[0]]);
                    }
                });
            });
            return gs;
        };
        Map.prototype.isEditableFeature = function (feature) {
            if (feature.isLocateToOtherType)
                return false;
            var mapType = feature.properties._mapType || feature.properties.objectType;
            if (mapType && this.objectsLayers) {
                for (var i = 0; i < this.objectsLayers.length; i++) {
                    var mt = this.objectsLayers[i].getMapType(mapType);
                    if (mt) {
                        if (mt.editMessage)
                            _context.message(mt.editMessage);
                        if (typeof mt.editable === 'function')
                            return mt.editable(feature, mapType, mt);
                        if (mt.editable !== undefined)
                            return mt.editable;
                        return feature.geometry.type !== 'LineString' && feature.geometry.type !== 'MultiLineString';
                    }
                }
            }
            return false;
        };
        Map.prototype.activeDrawSelect = function (mode) {
            if (mode === void 0) { mode = true; }
            if (this.selectOptions)
                this.addSelectControl();
            this.isDrawSelecting = !!mode;
            var thiz = this, map = this.lmap, currentDrawType;
            var drawed;
            var self = this;
            function enableHandler(type) {
                self.enableDrawHandler(type);
            }
            function drawcreatel(e) {
                drawed = e.layer;
                e.layer.remove();
                self.drawLayerGroup.removeLayer(e.layer);
                thiz.select(e.layer);
            }
            function drawstartl(e) {
                currentDrawType = e.layerType;
                drawed = null;
            }
            function drawstopl(e) {
                setTimeout(function (e) {
                    if (currentDrawType && drawed)
                        enableHandler(currentDrawType);
                }, 50);
            }
            this.drawLayerGroup.clearLayers();
            if (!mode) {
                currentDrawType = null;
                map.off(L.Draw.Event.CREATED, drawcreatel);
                map.off(L.Draw.Event.DRAWSTART, drawstartl);
                map.off(L.Draw.Event.DRAWSTOP, drawstopl);
                thiz.activeDraw(false);
            }
            else {
                thiz.clearSelection();
                thiz.activeDraw({
                    edit: false, draw: {
                        polyline: false, rectangle: true, circle: true, polygon: true,
                        marker: { icon: L.divIcon({ iconSize: [0, 0] }) }
                    }
                });
                var dt = L.toolbar._toolbars['draw'];
                dt.getActions = function () {
                    return [];
                };
                var init = typeof mode === 'string' ? mode : 'marker';
                enableHandler(init);
                currentDrawType = init;
                map.on(L.Draw.Event.CREATED, drawcreatel);
                map.on(L.Draw.Event.DRAWSTART, drawstartl);
                map.on(L.Draw.Event.DRAWSTOP, drawstopl);
            }
        };
        Map.prototype.getDrawLayers = function (wkt) {
            if (wkt === void 0) { wkt = true; }
            var ls = this.drawLayerGroup.getLayers();
            if (wkt)
                ls = ls.map(function (l) { return toWkt(l); });
            return ls;
        };
        Map.prototype.clearDrawLayers = function () {
            if (this.drawLayerGroup)
                this.drawLayerGroup.clearLayers();
            if (!this.isEditing) {
                this.lmap.off(L.Draw.Event.EDITED);
                this.lmap.off(L.Draw.Event.EDITMOVE);
                this.lmap.off(L.Draw.Event.EDITSTOP);
            }
        };
        Map.prototype.clearSelection = function () {
            this.selectionLayerGroup.clearLayers();
            this.selectFeatures = [];
            var b = this.getTopBean();
            if (b)
                b.showObjPane(false);
        };
        Map.prototype.getTopBean = function () {
            var b = _bean;
            try {
                var w = window;
                while (w.parent && w.parent !== w && w.parent['_bean']) {
                    b = window.parent['_bean'];
                    w = w.parent;
                }
            }
            catch (err) {
            }
            return b;
        };
        Map.prototype.clear = function () {
            this.lmap.closePopup();
            this.clearDrawLayers();
            this.clearSelection();
            this.tempLayerGroup.clearLayers();
            this.locationGroup.clearLayers();
            this.setWaiting(false);
        };
        Map.prototype.refresh = function (base) {
            var _this = this;
            if (base === void 0) { base = false; }
            clearTimeout(this._refreshTimer);
            this._refreshTimer = setTimeout(function () {
                _this.refreshForce(base);
            }, 200);
        };
        Map.prototype.refreshForce = function (base) {
            var _this = this;
            if (base === void 0) { base = false; }
            this.lmap.eachLayer(function (l) {
                if (l.options.pane === "tooltipPane")
                    l.removeFrom(_this.lmap);
            });
            this.clear();
            if (base) {
                for (var k in this.baselayers) {
                    var l = this.baselayers[k];
                    if (l._mapToAdd && l.redraw)
                        l.redraw();
                }
            }
            for (var k in this.overlayers) {
                var l = this.overlayers[k];
                if (l._mapToAdd && l.redraw)
                    l.redraw();
            }
            _context.events.trigger(_map.EVENT_MAP_REFRESH);
        };
        Map.prototype.bindLayerPopups = function (yes) {
            if (yes === void 0) { yes = true; }
        };
        Map.prototype.bindLayerPopup = function (l, yes) {
            if (yes === void 0) { yes = true; }
        };
        Map.prototype.pointToPolygon = function (latlng, tolFac) {
            if (tolFac === undefined)
                tolFac = this.defaultPointTolPx;
            var lat = latlng.lat, lng = latlng.lng;
            var size = this.lmap.getSize(), bounds = this.lmap.getBounds();
            var boundsWidth = bounds.getNorthEast().lng - bounds.getSouthWest().lng;
            var sizeWidth = size.x;
            var tol = boundsWidth / sizeWidth * tolFac;
            tol += this.defaultPointTolDistance;
            if (this.minSelectPointDistance !== undefined && tol < this.minSelectPointDistance)
                tol = this.minSelectPointDistance;
            return L.polygon([[lat - tol, lng - tol], [lat + tol, lng - tol], [lat + tol, lng + tol], [lat - tol, lng + tol], [lat - tol, lng - tol]]);
        };
        Map.prototype.selectWaiting = function (geom, callback, types, params) {
            var _this = this;
            if (callback === void 0) { callback = null; }
            if (types === void 0) { types = null; }
            if (params === void 0) { params = null; }
            if (this.isWaiting) {
                return;
            }
            this.setWaiting();
            this.select(geom, function (features, theGeom) {
                if (callback)
                    callback(features, theGeom);
                _this.setWaiting(false);
            }, types, params);
        };
        Map.prototype.select = function (geom, callback, types, params) {
            var _this = this;
            if (callback === void 0) { callback = null; }
            if (types === void 0) { types = null; }
            if (params === void 0) { params = null; }
            var theGeom = geom;
            var pointSelect = false;
            if (geom instanceof L.Marker) {
                geom = geom.getLatLng();
            }
            if (geom.lat && geom.lng)
                pointSelect = true;
            if (params && params.pointSelect !== undefined)
                pointSelect = params.pointSelect;
            var oplayers = [];
            for (var k in this.overlayers) {
                var ol = this.overlayers[k];
                if (!ol.options || ol.options.selectable !== false)
                    oplayers.push(ol);
            }
            if (!types) {
                types = this.queryMapTypes;
            }
            var reqs = [];
            oplayers.forEach(function (l) {
                if (!_this.lmap.hasLayer(l))
                    return;
                if (l.doSelect)
                    reqs.push(l.doSelect(geom, params, types).catch(function (e) {
                        console.error(e);
                        return [];
                    }));
                else if (l._def && l._def.selectable)
                    reqs.push(_this.identifyLayer(theGeom, l).catch(function (e) {
                        console.error(e);
                        return [];
                    }));
            });
            Promise.all(reqs).then(function (rets) {
                var fs = [];
                rets.forEach(function (rs) {
                    if (rs) {
                        if (rs.features)
                            rs = rs.features;
                        fs = fs.concat(rs);
                    }
                });
                if (!fs)
                    return;
                var features = fs;
                if (pointSelect) {
                    features = fs.filter(function (f) { return f.geometry.type === 'Point'; });
                    if (!features || !features.length)
                        features = fs;
                }
                features.forEach(function (f) {
                    var typeField = _this.entityTypeField, idField = _this.entityIdField;
                    if (f._def && f._def.entityTypeField !== undefined)
                        typeField = f._def.entityTypeField;
                    if (f._def && f._def.entityIdField !== undefined)
                        idField = f._def.entityIdField;
                    if (typeField)
                        f.objectType = f.properties[typeField];
                    if (idField)
                        f.id = f.properties[idField];
                    f.id = f.id || f.properties.id;
                    f.objectType = f.objectType || f.properties.objectType;
                });
                callback ? callback(features, theGeom) : _this.selectCallback(features, theGeom);
            });
        };
        Map.prototype.selectCallback = function (features, geom, handler) {
            var _this = this;
            if (geom === void 0) { geom = null; }
            if (handler === void 0) { handler = true; }
            if (this.appendSelecting && this.selectFeatures) {
                this.selectFeatures.forEach(function (e) {
                    var idx = features.findIndex(function (o) { return o.objectType === e.objectType && o.id === e.id; });
                    if (idx === -1)
                        features.push(e);
                    else {
                        features.splice(idx, 1);
                    }
                });
            }
            this.clearSelection();
            if (this.isEditing) {
                var toEdit = features.filter(function (f) { return _this.isEditableFeature(f); });
                if (toEdit.length) {
                    this.drawLayerGroup.clearLayers();
                    this.enableEditHandler(false);
                    L.geoJSON(toEdit[0], { poly: { allowIntersection: false } }).eachLayer(this.drawLayerGroup.addLayer, this.drawLayerGroup);
                    this.enableEditHandler();
                }
                return;
            }
            this.selectFeatures = features;
            _context.events.trigger(_map.EVENT_FEATURE_SELECTED, { features: features });
            if (!features || !features.length)
                return;
            var markedLayer;
            features.forEach(function (feature) {
                markedLayer = _this.markSelectedFeature(feature);
                _this.selectHistory.unshift(feature);
                if (_this.selectHistory.length > 10)
                    _this.selectHistory.pop();
            });
            if (handler !== false)
                this.selectionHandler(features, geom, markedLayer);
        };
        Map.prototype.selectionHandler = function (features, geom, markedLayer) {
            if (this.selectObjPopup === false)
                return;
            if (this.selectionFilter)
                features = this.selectionFilter(features, geom);
            if (features.length > 1) {
                var nf_1 = [];
                features.forEach(function (f) {
                    if (nf_1.find(function (o) { return o.objectType == f.objectType && o.id == f.id; }))
                        return;
                    nf_1.push(f);
                });
                features = nf_1;
            }
            var b = this.getTopBean();
            if (features.length > 1) {
                b.showObjPane(features);
            }
            else {
                var f = features[0];
                if (!f.id || !f.objectType)
                    return;
                var latlng = void 0;
                if (geom) {
                    if (geom.lat && geom.lng)
                        latlng = geom;
                    else if (geom.getLatLng)
                        latlng = geom.getLatLng();
                }
                if (!latlng && markedLayer) {
                    latlng = this.getCentroid(markedLayer);
                }
                if (latlng) {
                    this.showObjPopup(latlng, f.objectType, f.id, f);
                }
            }
        };
        Map.prototype.getCentroid = function (layer, computeBounds) {
            var _this = this;
            if (computeBounds === void 0) { computeBounds = true; }
            var latlng;
            if (layer.eachLayer)
                layer.eachLayer(function (l) {
                    latlng = _this.getCentroid(l, false);
                });
            if (latlng)
                return latlng;
            if (layer._rings && layer instanceof L.Polyline)
                latlng = this.computePolylineCentroid(layer._rings);
            if (latlng)
                return latlng;
            if (layer.getCenter)
                try {
                    latlng = layer.getCenter();
                }
                catch (error) {
                }
            else if (layer.getLatLng)
                latlng = layer.getLatLng();
            else if (layer.lat && layer.lng)
                latlng = layer;
            else if (layer.getBounds && computeBounds)
                latlng = layer.getBounds().getCenter();
            return latlng;
        };
        Map.prototype.computePolylineCentroid = function (rings) {
            var points = [];
            rings.forEach(function (ring) {
                points = points.concat(ring);
            });
            var i, halfDist, segDist, dist, p1, p2, ratio, len = points.length;
            if (!len) {
                return null;
            }
            for (i = 0, halfDist = 0; i < len - 1; i++) {
                halfDist += points[i].distanceTo(points[i + 1]) / 2;
            }
            if (halfDist === 0) {
                return this.lmap.layerPointToLatLng(points[0]);
            }
            for (i = 0, dist = 0; i < len - 1; i++) {
                p1 = points[i];
                p2 = points[i + 1];
                segDist = p1.distanceTo(p2);
                dist += segDist;
                if (dist > halfDist) {
                    ratio = (dist - halfDist) / segDist;
                    return this.lmap.layerPointToLatLng([
                        p2.x - ratio * (p2.x - p1.x),
                        p2.y - ratio * (p2.y - p1.y)
                    ]);
                }
            }
        };
        Map.prototype.showObjPopup = function (latlng, objectType, id, sourceObject) {
            var _this = this;
            var mapType;
            this.objectsLayers.find(function (ol) {
                mapType = ol.getMapType(objectType);
                return mapType;
            });
            var b = this.getTopBean();
            var div = $('<div>').css('minHeight', '32px');
            var opt = { sourceui: this, source: 'map', sourceObject: sourceObject };
            if (mapType && mapType.itemDescriptionFields) {
                opt.itemDescriptionFields = mapType.itemDescriptionFields;
            }
            b.item(objectType, id, 'map', div, opt).rendered().then(function () {
                L.popup({ maxWidth: 1000 }).setLatLng(latlng).openOn(_this.lmap).setContent(div[0]);
            });
        };
        Map.prototype.selectLayer = function (layer, geom) {
            return this.identifyLayer(geom, layer);
        };
        Map.prototype.markSelectedFeature = function (feature) {
            var _this = this;
            return L.geoJSON(feature, {
                style: this.selectionStyle,
                pointToLayer: function (f, latlng) { return L.circleMarker(latlng, _this.selectionMarkerStyle); }
            }).addTo(this.selectionLayerGroup);
        };
        Map.prototype.doCreateObjectLayer = function (lo) {
            if (!lo._symbol && lo.styleJson) {
                lo._symbol = JSON.parse(lo.styleJson);
            }
            var mapTypes = lo.mapTypes || lo.mapType;
            if (!(Array.isArray(mapTypes)))
                mapTypes = [mapTypes];
            mapTypes.forEach(function (t) {
                if (!t)
                    return;
                if (t.minZoom)
                    t.minZoom = parseInt(t.minZoom);
                if (!t._symbol && t.styleJson)
                    t._symbol = JSON.parse(t.styleJson);
            });
            delete lo.mapType;
            lo.mapTypes = mapTypes;
            var ol = new ObjectsLayer(lo);
            this.objectsLayers.push(ol);
            return ol;
        };
        Map.prototype.doCreateLayer = function (lo) {
            var _this = this;
            if (lo.script) {
                var script = lo.script;
                delete lo.script;
                return _util.loadScript(script).then(function () { return _this.doCreateLayer(lo); });
            }
            if (typeof lo.doCreateLayer === 'function') {
                return lo.doCreateLayer(lo);
            }
            if (lo.opacity)
                lo.opacity = parseFloat(lo.opacity);
            var type = lo.type;
            if (_map.layerFactory[type])
                return _map.layerFactory[type].doCreateLayer(lo, this);
            if (type === 'google') {
                type = 'chinaprovider';
                lo.provider = lo.provider || 'Google.Normal.Map';
            }
            else if (type === 'gaode') {
                type = 'chinaprovider';
                lo.provider = lo.provider || 'GaoDe.Normal.Map';
            }
            else if (type === 'tiandi') {
                type = 'chinaprovider';
                lo.provider = lo.provider || 'TianDiTu.Normal.Map';
            }
            if (type.startsWith('arcgis.')) {
                return _util.loadScript('lib/leaflet/esri-leaflet-debug.js').then(function () {
                    if (type === 'arcgis.tile') {
                        var tiledMapLayerPromise = new Promise(function (resolve, reject) {
                            var tiledMapLayer = L.esri.tiledMapLayer(lo);
                            if (!_this.updateCrs) {
                                resolve(tiledMapLayer);
                            }
                            else {
                                tiledMapLayer.metadata(function (e, metadata) {
                                    if (metadata.tileInfo && metadata.tileInfo.lods && metadata.tileInfo.origin) {
                                        var resolutions_1 = [];
                                        var scales_1 = [];
                                        metadata.tileInfo.lods.forEach(function (lod) {
                                            resolutions_1.push(parseFloat(lod.resolution));
                                            scales_1.push(lod.scale);
                                        });
                                        _this.crs = new L.Proj.CRS(_this.crs.code, _this.crs.def, {
                                            origin: [metadata.tileInfo.origin.x, metadata.tileInfo.origin.y],
                                            scales: _this.crs.scales,
                                            resolutions: resolutions_1
                                        });
                                    }
                                    _this.updateCrs = false;
                                    resolve(tiledMapLayer);
                                });
                            }
                        });
                        return tiledMapLayerPromise;
                    }
                    if (type === 'arcgis.dynamic') {
                        lo = Object.assign({ f: 'image', updateInterval: 300, position: 'back' }, lo);
                        if (lo.sublayer && !Array.isArray(lo.sublayer))
                            lo.sublayer = [lo.sublayer];
                        if (typeof lo.layers === 'string')
                            lo.layers = lo.layers.split(',');
                        if (typeof lo.disableCache === 'undefined')
                            lo.disableCache = true;
                        var l_1 = L.esri.dynamicMapLayer(lo);
                        if (lo.f)
                            l_1.options.f = lo.f;
                        if (lo.sublayer)
                            lo.sublayer.forEach(function (sub) {
                                sub.arcgisDynamicLayerInstance = l_1;
                            });
                        l_1._excludingLayers = [];
                        l_1.showLayerBySubId = function (subid, v) {
                            var vls = l_1._excludingLayers;
                            var pos = vls.findIndex(function (i) { return i == subid; });
                            if (!v && pos === -1)
                                vls.push(subid);
                            else if (v && pos !== -1)
                                vls.splice(pos, 1);
                            l_1.setLayers(vls.length ? 'exclude:' + vls.join(',') : false);
                        };
                        l_1.metadata(function (err, meta) {
                            if (err)
                                return console.error(err);
                            var allSubLayers = [];
                            meta.layers.forEach(function (i) {
                                allSubLayers.push(i);
                            });
                            l_1._allSubLayers = allSubLayers;
                        });
                        return l_1;
                    }
                    if (type === 'arcgis.feature') {
                        var l = L.esri.featureLayer(lo);
                        return l;
                    }
                });
            }
            if (type === 'tile') {
                return L.tileLayer(lo.url, lo);
            }
            if (type === 'object') {
                var self_3 = this, pres = [];
                if (lo.websocket === true)
                    pres.push(_util.loadScript('lib/websocket/stomp.umd.min.js', 'StompJs'));
                if (this.lmap.getMaxZoom() === Infinity && Object.keys(this.baselayers).length !== 0) {
                    pres.push(new Promise(function (resolve, reject) {
                        _this.lmap.once('zoomlevelschange', function () {
                            resolve(lo);
                        });
                    }));
                }
                var user = _context.getUser(true);
                if (user) {
                    var mapTypes_1 = lo.mapTypes || lo.mapType || [];
                    if (!Array.isArray(mapTypes_1))
                        mapTypes_1 = [mapTypes_1];
                    mapTypes_1.forEach(function (mt) {
                        if (!mt.id) {
                            var idx = mapTypes_1.filter(function (m) { return m.objectType === mt.objectType; }).findIndex(function (m) { return m === mt; });
                            mt.id = idx === 0 ? mt.objectType : mt.objectType + idx;
                        }
                    });
                    pres.push(_util.getStorage('MapTypeVisible-' + user.id, this.shouldMapLayerVisibleStoreRemote()).then(function (mts) {
                        if (mts) {
                            var _loop_8 = function (mtId) {
                                var mt = mapTypes_1.find(function (o) { return o.id == mtId; });
                                if (mt && mt.forceVisible === undefined)
                                    mt.visible = mts[mtId];
                            };
                            for (var mtId in mts) {
                                _loop_8(mtId);
                            }
                        }
                    }));
                }
                if (!pres.length)
                    return self_3.doCreateObjectLayer(lo);
                else
                    return Promise.all(pres).then(function () {
                        return self_3.doCreateObjectLayer(lo);
                    });
            }
            if (type === 'wms') {
                var opt = Object.assign({ transparent: true, format: 'image/png', version: '1.1.1' }, lo);
                return L.tileLayer.wms(lo.url, opt);
            }
            if (type === 'heat') {
                if (window['HeatmapOverlay'])
                    return this.createHeatmapLayer(lo);
                var promise = new Promise(function (resolve, reject) {
                    _util.loadScript('lib/heatmap.min.js').then(function () {
                        _util.loadScript('lib/leaflet/leaflet-heatmap.js').then(function () { return resolve(_this.createHeatmapLayer(lo)); });
                    });
                });
                return promise;
            }
            if (type === 'cluster') {
                if (L.MarkerCluster)
                    return this.createClusterLayer(lo);
                else {
                    _util.loadCss(['lib/leaflet/MarkerCluster.css', 'lib/leaflet/MarkerCluster.Default.css']);
                    return _util.loadScript('lib/leaflet/leaflet.markercluster.js').then(function (e) {
                        return _this.createClusterLayer(lo);
                    });
                }
            }
            if (type === 'pixi') {
                return new L.PixiLayer({});
            }
            if (type === 'bing') {
                return _util.loadScript('lib/leaflet/leaflet-bing-layer.js').then(function () {
                    var key = lo.key || 'AuhiCJHlGzhg93IqUH_oCpl_-ZUrIE6SPftlyGYUvr9Amx5nzA-WqGcPquyFZl4L';
                    lo.bingMapsKey = lo.bingMapsKey || key;
                    return L.tileLayer.bing(lo);
                });
            }
            if (type === 'chinaprovider') {
                return _util.loadScript('lib/leaflet/leaflet.ChineseTmsProviders.js').then(function () {
                    if ('4326' === lo.iscrs) {
                        var c = L.CRS.CustomEPSG4326;
                        lo.crs = lo.crs || c;
                        _this.lmap.options.crs = c;
                        setTimeout(function () {
                            _this.setCenter(_this.getCenter());
                        }, 200);
                    }
                    return L.tileLayer.chinaProvider(lo.provider, lo);
                });
            }
            if (type === 'qq') {
                return _util.loadScript('lib/leaflet/leaflet.qq.js').then(function () {
                    return new L.TileLayer.QQ(lo.url, lo);
                });
            }
            if (type === 'kml') {
                return _util.loadScript('lib/leaflet/KML.js').then(function () {
                    return new L.KML(lo.url, Object.assign({ async: true }, lo));
                });
            }
            if (type === 'baidu') {
                return _util.loadScripts(['lib/leaflet/baiduMapAPI-2.0-min.js', 'lib/leaflet/leaflet-baidu.js']).then(function () {
                    var c = L.CRS.EPSGB3857;
                    if (!c.distance) {
                        c.distance = L.CRS.Earth.distance;
                        c.R = 6378137;
                    }
                    lo.crs = lo.crs || c;
                    _this.lmap.options.crs = c;
                    setTimeout(function () {
                        _this.setCenter(_this.getCenter());
                    }, 200);
                    return L.tileLayer.baiduLayer(lo.style, lo);
                });
            }
            if (type === 'leaflet.wms') {
                return _util.loadScript('lib/leaflet/leaflet.wms/leaflet.wms.js').then(function () {
                    var opt = Object.assign({ transparent: true, format: 'image/png', version: '1.1.1' }, lo);
                    if (opt.isSource) {
                        return L.WMS.source(lo.url, opt);
                    }
                    else {
                        return L.WMS.tileLayer(lo.url, opt);
                    }
                });
            }
        };
        Map.prototype.createClusterLayer = function (lo) {
            var l = new L.MarkerClusterGroup(lo);
            var self = this;
            var b = this.getTopBean();
            var iconSizes = {};
            function addgraphic(item, icon, wkt) {
                var opt = icon ? { icon: L.icon(icon) } : null;
                var g = self.readWkt(wkt, opt);
                if (self.selectOptions)
                    g.on('click', function () {
                        var obj = item;
                        if (self.entityTypeField && self.entityIdField)
                            obj = {
                                objectType: item[self.entityTypeField],
                                id: item[self.entityIdField]
                            };
                        var itemHandler = l.itemHandler;
                        if (itemHandler === undefined)
                            itemHandler = lo.itemHandler;
                        if (typeof itemHandler === 'function')
                            itemHandler(obj);
                        else if (itemHandler !== false) {
                            var ll = self.toLatLng(g);
                            if (ll)
                                self.showObjPopup(ll, obj.objectType, obj.id, item);
                            else
                                b.showObjPane(obj);
                        }
                    });
                l.addLayer(g);
            }
            function additem(item) {
                var wkt = item[lo.wktField];
                if (wkt) {
                    var icon_1 = item.icon;
                    if (!icon_1) {
                        var iconExpression = l.iconExpression || lo.iconExpression;
                        var iconFn = l.iconFn || lo.iconFn;
                        if (iconFn) {
                            if (typeof iconFn === 'string')
                                icon_1 = window[iconFn](item, self);
                            else
                                icon_1 = iconFn(item, self);
                        }
                        else if (iconExpression)
                            icon_1 = _util.replaceVar(iconExpression, Object.assign({ zoom: this.lmap.getMaxZoom() - this.lmap.getZoom() }, item), true);
                    }
                    if (!icon_1)
                        icon_1 = lo.icon || {};
                    if (typeof icon_1 === 'string')
                        icon_1 = { iconUrl: icon_1 };
                    var iconSize = icon_1.iconSize || item.iconSize || lo.iconSize;
                    if (typeof iconSize === 'string')
                        iconSize = iconSize.split(',');
                    if (!iconSize)
                        iconSize = iconSizes[icon_1.iconUrl];
                    if (iconSize) {
                        icon_1.iconSize = iconSize;
                        addgraphic(item, icon_1, wkt);
                    }
                    else {
                        $('<image style="display:none;" src="' + icon_1.iconUrl + '">').appendTo('body').on('load', function () {
                            var w = this.naturalWidth, h = this.naturalHeight;
                            $(this).remove();
                            icon_1.iconSize = iconSizes[icon_1.iconUrl] = [w, h];
                            addgraphic(item, icon_1, wkt);
                        });
                    }
                }
            }
            l.addObject = additem;
            var rdwing = false;
            function setdata(data) {
                var dataFixFn = l.dataFixFn || lo.dataFixFn;
                if (dataFixFn) {
                    if (typeof dataFixFn === 'string')
                        dataFixFn = window[dataFixFn];
                    data = dataFixFn(data) || data;
                }
                data = data.data || data;
                data.forEach(additem);
                rdwing = false;
            }
            l.redraw = function (data) {
                if (rdwing)
                    return;
                rdwing = true;
                data = data || lo.data;
                var dataUrl = typeof data === 'string' ? data : lo.dataUrl;
                dataUrl = dataUrl || lo.url;
                if (dataUrl)
                    _bean.post(dataUrl).then(function (data) { return setdata(data); });
                else if (data)
                    setdata(data);
            };
            l.on('add', function () {
                if (l.autoLoad !== false && (!l.getLayers() || l.getLayers().length === 0))
                    l.redraw();
            });
            return l;
        };
        Map.prototype.createHeatmapLayer = function (lo) {
            var cfg = lo;
            if (cfg.radius)
                cfg.radius = parseInt(cfg.radius);
            if (cfg.maxOpacity)
                cfg.maxOpacity = parseFloat(cfg.maxOpacity);
            if (typeof cfg.gradient === 'string')
                cfg.gradient = JSON.parse(cfg.gradient);
            var l = new window['HeatmapOverlay'](cfg);
            var thiz = this;
            var rdwing = false, inited = false;
            function setdata(data) {
                inited = true;
                var dataFixFn = l.dataFixFn || cfg.dataFixFn;
                if (dataFixFn) {
                    if (typeof dataFixFn === 'string')
                        dataFixFn = window[dataFixFn];
                    data = dataFixFn(data) || data;
                }
                if (Array.isArray(data))
                    data = { data: data };
                if (lo.dataField)
                    data.data = data[lo.dataField];
                var max = data.max, min = data.min;
                if (lo.maxField)
                    max = data[lo.maxField];
                max = max || lo.max;
                data.max = parseInt(max);
                if (lo.minField)
                    min = data[lo.minField];
                min = min || lo.min;
                if (min)
                    data.min = parseInt(min);
                if (lo.wktField) {
                    var latField_1 = lo.latField || 'lat', lngField_1 = lo.lngField || 'lng';
                    var vf_1 = lo.valueField || 'count';
                    data.data = data.data.filter(function (item) { return item[lo.wktField]; });
                    data.data.forEach(function (item) {
                        item[vf_1] = item[vf_1] || 1;
                        var wkt = item[lo.wktField];
                        if (wkt) {
                            var g = thiz.readWkt(wkt);
                            item[latField_1] = g.getLatLng().lat;
                            item[lngField_1] = g.getLatLng().lng;
                        }
                    });
                }
                l.setData(data);
                rdwing = false;
            }
            l.redraw = function (data, noUpdateData) {
                if (rdwing)
                    return;
                if (noUpdateData && inited)
                    return;
                rdwing = true;
                data = data || lo.data;
                var dataUrl = typeof data === 'string' ? data : lo.dataUrl;
                dataUrl = dataUrl || lo.url;
                if (dataUrl)
                    _bean.post(dataUrl, { _method: lo.dataRequestMethod || 'POST' }).then(function (data) { return setdata(data); });
                else if (data)
                    setdata(data);
            };
            l.beforeAdd = function () {
                l.redraw(null, true);
            };
            if (cfg.preload === true)
                l.redraw(null, true);
            return l;
        };
        Map.prototype.createMap = function (callback) {
            var _this = this;
            var lstorage = localStorage;
            if (this.traceView) {
                var vstr = lstorage.getItem('lastmapview');
                if (vstr)
                    this.view = vstr.split(',').map(function (v) { return parseFloat(v); });
                vstr = lstorage.getItem('lastmapzoom');
                if (vstr)
                    this.zoom = parseInt(vstr);
            }
            var mapOpt = Object.assign(this.mapOptions, {
                zoomControl: false,
                drawControlTooltips: true,
                attributionControl: false,
                contextmenu: !!this.contextMenu,
                contextmenuItems: this.contextMenu || []
            });
            if (this.renderEngine === 'svg')
                mapOpt.renderer = L.svg();
            else if (this.renderEngine === 'canvas')
                mapOpt.renderer = L.canvas();
            if (this.crs) {
                var c = null, cn = this.crs;
                if (typeof cn === 'function')
                    cn = cn();
                if (typeof cn === 'string')
                    c = L.CRS[cn];
                else if ($.isPlainObject(cn) && !cn.latLngToPoint) {
                    var origin_1 = cn.origin.split(',').map(function (v) { return parseInt(v); });
                    var resolutions = cn.resolutions, scales = cn.scales;
                    if (scales) {
                        if (typeof scales === 'string')
                            scales = scales.split(',').map(function (v) { return parseFloat(v); });
                        while (this.mapOptions.maxZoom + 1 > scales.length) {
                            scales.push(scales[scales.length - 1] * 2);
                        }
                    }
                    if (resolutions) {
                        if (typeof resolutions === 'string')
                            resolutions = resolutions.split(',').map(function (v) { return parseFloat(v); });
                        while (this.mapOptions.maxZoom + 1 > resolutions.length) {
                            resolutions.push(resolutions[resolutions.length - 1] / 2);
                        }
                    }
                    c = new L.Proj.CRS(cn.code, cn.def, { origin: origin_1, scales: scales, resolutions: resolutions });
                }
                else
                    c = cn;
                if (!c.distance) {
                    c.distance = L.CRS.Earth.distance;
                    c.R = L.CRS.Earth.R = 6378137;
                }
                if (!this.mapOptions.maxZoom && c.options) {
                    var lods = c.options.resolutions || c.options.scales;
                    if (!this.mapOptions.maxZoom && lods)
                        this.mapOptions.maxZoom = lods.length - 1;
                }
                mapOpt.crs = c;
            }
            var map = this.lmap = L.map($(this.el)[0], mapOpt);
            if (this.options.defaultMaxZoom)
                map._layersMaxZoom = this.options.defaultMaxZoom;
            if (this.view)
                map.setView(this.view, this.zoom || 0);
            else {
                map.locate({ setView: true });
            }
            if (this.traceView) {
                var fn = function (e) {
                    lstorage.setItem('lastmapzoom', map.getZoom());
                    var center = map.getCenter();
                    lstorage.setItem('lastmapview', [center.lat, center.lng].join(','));
                };
                map.on('zoomlevelschange', fn);
                map.on('moveend', fn);
            }
            if (this.showMouseCoords !== false) {
                new MouseCoordControl({}).addTo(map);
            }
            if (this.showZoomControl) {
                this.addZoomControl();
            }
            this.tempLayerGroup = new L.FeatureGroup();
            map.addLayer(this.tempLayerGroup);
            this.drawLayerGroup = new L.FeatureGroup();
            this.selectionLayerGroup = new L.FeatureGroup().addTo(map);
            var self = this, layerObjs = this.layerObjs, layersCreated = 0, layersCreatTotal = layerObjs.length;
            function triggerMapReady() {
                self.layerCreating = false;
                if (callback)
                    callback();
            }
            if (layerObjs && layerObjs.length) {
                this.layerCreating = true;
                layerObjs.forEach(function (l, lidx) {
                    _this.createLayer(l, function () {
                        layersCreated++;
                        if (layersCreated === layersCreatTotal) {
                            self.reOrderLayersByDef();
                            triggerMapReady();
                        }
                    });
                });
                this.bindLayerPopups();
            }
            else
                triggerMapReady();
            if (this.scaleControl !== false)
                L.control.scale({ imperial: false, updateWhenIdle: true }).addTo(map);
            if (this.printable)
                _util.loadScript('lib/leaflet/leaflet.easyPrint.js').then(function () {
                    L.easyPrint().addTo(map);
                });
            if (this.showLayersControl)
                setTimeout(function () {
                    L.control.layers(_this.baselayers, _this.overlayers, { position: 'bottomleft' }).addTo(map);
                }, 1000);
            return map;
        };
        Map.prototype.reOrderLayersByDef = function () {
            for (var i = this.layerObjs.length - 1; i >= 0; i--) {
                var tl = this.layerObjs[i]._layer;
                if (tl && tl.bringToBack)
                    tl.bringToBack();
            }
        };
        Map.prototype.createLayer = function (l, callback, hideFromLayerObjs) {
            if (callback === void 0) { callback = null; }
            if (hideFromLayerObjs === void 0) { hideFromLayerObjs = false; }
            l.id = l.id || l.title || 'layer' + (this.layerIdSeq++);
            if (!hideFromLayerObjs && this.layerObjs.indexOf(l) === -1)
                this.layerObjs.push(l);
            var opls = this.layersbyid, tpls = this.layersbytype, sbs = this.subtypes;
            var bls = this.baselayers, ovs = this.overlayers;
            var idtypes = this.idtypes;
            var self = this;
            var layer = this.doCreateLayer(l), map = this.lmap;
            if (layer instanceof Promise) {
                layer.then(function (tl) { return addlayer(tl); });
            }
            else if (layer) {
                addlayer(layer);
            }
            function addlayer(layer) {
                if (layer) {
                    (l.baseLayer ? bls : ovs)[l.id] = layer;
                    l._layer = layer;
                    layer._def = l;
                    opls[l.id] = layer;
                    if (l.content && typeof l.content === 'string') {
                        try {
                            eval(l.content);
                        }
                        catch (e) {
                        }
                        ;
                    }
                    if (l.objectType) {
                        tpls[l.objectType] = layer;
                        idtypes[l.id] = l.objectType;
                    }
                    if (l.sublayer) {
                        l.sublayer = l.sublayer instanceof Array ? l.sublayer : [l.sublayer];
                        var subs = l.sublayer;
                        subs.forEach(function (s) {
                            tpls[s.objectType] = layer;
                            sbs[s.objectType] = s;
                            idtypes[l.id + '-' + s.id] = s.objectType;
                        });
                    }
                    if (l.minZoom || l.maxZoom) {
                        var min_1 = parseInt(l.minZoom), max_1 = parseInt(l.maxZoom);
                        var lastZoom_1;
                        var zd = function () {
                            if (l.visible === false)
                                return;
                            var z = map.getZoom();
                            if (z === lastZoom_1)
                                return;
                            lastZoom_1 = z;
                            var v = true;
                            if (min_1 && z < min_1)
                                v = false;
                            if (max_1 && z > max_1)
                                v = false;
                            self.setLayerVisible(l.id, v, true);
                        };
                        map.on('zoomlevelschange', zd);
                        map.on('zoomend', zd);
                        zd();
                    }
                    if (l.visible !== false) {
                        layer.addTo(map);
                        layer.__mapInstance = self;
                        if (l.baseLayer)
                            layer.bringToBack();
                    }
                }
                if (callback)
                    callback(layer);
            }
            return l.id;
        };
        Map.prototype.getLayer = function (id) {
            if (typeof id === 'function')
                return this.layerObjs.filter(function (l) { return id(l); });
            if (typeof id === 'object')
                id = id.id || id.title;
            return this.layersbyid[id];
        };
        Map.prototype.getLayerDef = function (id) {
            if (typeof id === 'number')
                return this.layerObjs[id];
            if (typeof id === 'string')
                return this.layerObjs.find(function (l) {
                    return l.id === id || l.title === id;
                });
            if (typeof id === 'function')
                return this.layerObjs.find(function (l) { return id(l); });
            return null;
        };
        Map.prototype.getBaseLayers = function () {
            return this.getLayer(function (l) { return l.baseLayer; });
        };
        Map.prototype.getObjectLayers = function () {
            return this.objectsLayers;
        };
        Map.prototype.getObjectIcon = function (objectType) {
            for (var i = 0; i < this.objectsLayers.length; i++) {
                var mt = this.objectsLayers[i].getMapType(objectType);
                if (mt)
                    return mt.icon;
            }
        };
        Map.prototype.removeLayer = function (id) {
            if (typeof id === 'object')
                id = id.id || id.title;
            var layer = this.layersbyid[id];
            if (layer) {
                this.lmap.removeLayer(layer);
                delete this.layersbyid[id];
            }
        };
        Map.prototype.saveLayerVisible = function (id, v) {
            var _this = this;
            if (v === void 0) { v = true; }
            if (v == this.isLayerVisibleById(id))
                return;
            this.setLayerVisible(id, v);
            var user = _context.getUser(true);
            if (user) {
                if (this._layerVisibleStorageTimer)
                    clearTimeout(this._layerVisibleStorageTimer);
                this._layerVisibleStorageTimer = setTimeout(function () {
                    var lvs = {};
                    _this.layerObjs.forEach(function (lo) {
                        lvs[lo.id] = _this.isLayerVisibleById(lo.id);
                    });
                    _util.setStorage('MapLayerVisible-' + user.id, lvs, true);
                }, 2000);
            }
        };
        Map.prototype.setLayerVisible = function (id, v, inner) {
            if (v === void 0) { v = true; }
            if (inner === void 0) { inner = false; }
            var layer = this.getLayer(id);
            if (layer) {
                if (layer._def && !inner) {
                    layer._def.visible = v;
                }
                if (v) {
                    this.lmap.addLayer(layer);
                    layer.__mapInstance = this;
                    if (this.baselayers[id]) {
                        layer.bringToBack();
                        for (var k in this.baselayers) {
                            if (k !== id) {
                                this.setLayerVisible(k, false);
                            }
                        }
                    }
                    if (layer._def && layer._def.crs && layer._def.crs !== this.lmap.options.crs) {
                        var center = this.getCenter();
                        this.lmap.options.crs = layer._def.crs;
                        this.setCenter(center);
                    }
                    this.reOrderLayersByDef();
                }
                else {
                    this.lmap.removeLayer(layer);
                }
            }
        };
        Map.prototype.getLayerByType = function (type) {
            return this.layersbytype[type];
        };
        Map.prototype.getLayerByObjectType = function (objectType) {
            if (this.objectsLayers) {
                for (var i = 0; i < this.objectsLayers.length; i++) {
                    if (this.objectsLayers[i].hasMapType(objectType))
                        return this.objectsLayers[i];
                }
            }
            var layer = this.getLayerByType(objectType);
            if (layer && this.lmap.hasLayer(layer))
                return layer;
        };
        Map.prototype.setLayerVisibleByType = function (type, v) {
            if (v === void 0) { v = true; }
            if (this.objectsLayers) {
                for (var i = 0; i < this.objectsLayers.length; i++) {
                    if (this.objectsLayers[i].hasMapType(type))
                        return this.objectsLayers[i].setObjectTypeVisible(type, v);
                }
            }
            var layer = this.getLayerByType(type);
            if (!layer)
                return;
            if (this.subtypes[type]) {
                var subid = this.subtypes[type].id;
                if (layer instanceof L.esri.DynamicMapLayer) {
                    var vls = layer.getLayers();
                    if (!vls && layer.options.sublayer) {
                        vls = layer.options.sublayer.map(function (s) { return s.id; });
                    }
                    vls = vls || [];
                    var pos = vls.indexOf(subid);
                    if (v && pos === -1)
                        vls.push(subid);
                    else if (!v && pos !== -1)
                        vls.splice(pos, 1);
                    layer.setLayers(vls);
                }
            }
            else {
                layer.__mapInstance = this;
                v ? this.lmap.addLayer(layer) : this.lmap.removeLayer(layer);
            }
        };
        Map.prototype.isLayerVisibleById = function (id) {
            var def = this.getLayerDef(id);
            if (def) {
                if (def.visible !== undefined)
                    return def.visible;
            }
            var layer = this.getLayer(id);
            if (!layer)
                return false;
            return this.lmap.hasLayer(layer);
        };
        Map.prototype.getSubId = function (type) {
            return this.subtypes[type].id;
        };
        Map.prototype.isLayerVisibleByType = function (type) {
            if (this.objectsLayers) {
                for (var i = 0; i < this.objectsLayers.length; i++) {
                    if (this.objectsLayers[i].hasMapType(type))
                        return this.objectsLayers[i].isObjectTypeVisible(type);
                }
            }
            var layer = this.getLayerByType(type);
            if (!layer)
                return false;
            if (!this.lmap.hasLayer(layer))
                return false;
            if (this.subtypes[type]) {
                var subid = this.subtypes[type].id;
                if (layer instanceof L.esri.DynamicMapLayer) {
                    var vls = layer.getLayers();
                    if (vls)
                        return vls.indexOf(subid) !== -1;
                }
            }
            return true;
        };
        Map.prototype.onReady = function (callback) {
            if (this.isReady)
                callback();
            else
                this.eventbus.on('mapready', callback);
        };
        Map.prototype.on = function (eventType, listener) {
            this.lmap.on(eventType, listener);
        };
        Map.prototype.off = function (eventType, listener) {
            this.lmap.off(eventType, listener);
        };
        Map.prototype.identifyLayer = function (graphic, layer, layers) {
            var _this = this;
            if (layers === void 0) { layers = null; }
            if (typeof layer === 'string')
                layer = this.getLayer(layer);
            return new Promise(function (resolve, reject) {
                if ((layer._def && layer._def.type === 'arcgis.dynamic') || (L.esri && layer instanceof L.esri.DynamicMapLayer)) {
                    var task = layer.identify().on(_this.lmap).at(graphic.getLatLng());
                    if (layers)
                        task.layers(layers);
                    task.run(function (error, featureCollection) {
                        var fs;
                        if (error) {
                            console.log(error);
                            fs = [];
                        }
                        else
                            fs = featureCollection.features;
                        fs = fs.filter(function (f) {
                            return !layer._excludingLayers.find(function (elid) { return elid == f.layerId; });
                        });
                        var def = layer._def;
                        if (fs.length > 1) {
                            var ptfs = fs.filter(function (f) {
                                return f.geometry.type.toLowerCase().indexOf('point') !== -1;
                            });
                            if (ptfs.length)
                                fs = ptfs;
                        }
                        if (fs.length > 1 && def.identifySingle == true)
                            fs.length = 1;
                        fs.forEach(function (f) {
                            f._def = def;
                            f.objectType = def.objectType;
                            if (f.layerId || f.layerId === 0) {
                                var type = _this.idtypes[def.id + '-' + f.layerId];
                                if (type)
                                    f.objectType = type;
                            }
                            if (def.objectTypeField)
                                f.objectType = f.properties[def.objectTypeField];
                            if (def.objectIdField)
                                f.id = f.properties[def.objectIdField];
                        });
                        resolve(fs);
                    });
                }
                else if (layer instanceof L.TileLayer.WMS || (layer._def && layer._def.type === 'wms')) {
                    var pt = _this.lmap.latLngToContainerPoint(graphic.getLatLng(), _this.lmap.getZoom());
                    var size = _this.lmap.getSize();
                    var wmsOpt = layer.wmsParams;
                    var crs = _this.lmap.options.crs, sw = crs.project(_this.lmap.getBounds().getSouthWest()), ne = crs.project(_this.lmap.getBounds().getNorthEast());
                    var bbox = sw.x + ',' + sw.y + ',' + ne.x + ',' + ne.y;
                    var ps = {
                        query_layers: wmsOpt.layers,
                        layers: wmsOpt.layers,
                        bbox: bbox,
                        width: size.x,
                        height: size.y,
                        info_format: 'application/json',
                        feature_count: 500,
                        request: 'GetFeatureInfo',
                        service: 'WMS',
                        srs: wmsOpt.srs,
                        styles: wmsOpt.styles,
                        version: wmsOpt.version
                    };
                    ps[ps.version === '1.3.0' ? 'i' : 'x'] = pt.x;
                    ps[ps.version === '1.3.0' ? 'j' : 'y'] = pt.y;
                    _bean.post(wmsOpt.url, ps).then(function (fs) {
                        resolve(fs);
                    });
                }
                else {
                    resolve([]);
                }
            });
        };
        Map.prototype.createLayerGroup = function () {
            var g = new L.FeatureGroup();
            this.lmap.addLayer(g);
            return g;
        };
        Map.prototype.setCreateOnGroup = function (group) {
            this.createLayerOnGroup = group;
        };
        Map.prototype.getCreateLayerGroup = function () {
            return this.createLayerOnGroup ? this.createLayerOnGroup : this.tempLayerGroup;
        };
        Map.prototype.addLeafletLayer = function (layer, group) {
            if (group === void 0) { group = undefined; }
            if (group === undefined)
                group = this.getCreateLayerGroup();
            (group || this.lmap).addLayer(layer);
            if (group)
                layer._originalGroup = group;
            return this;
        };
        Map.prototype.removeLeafletLayer = function (layer, group) {
            if (group === void 0) { group = undefined; }
            (group || this.lmap).removeLayer(layer);
            return this;
        };
        Map.prototype.createMarker = function (latlng, options, show) {
            if (options === void 0) { options = {}; }
            if (show === void 0) { show = true; }
            if (options.iconOptions)
                options.icon = L.icon(options.iconOptions);
            else if (options.beautifyIconOptions)
                options.icon = L.BeautifyIcon.icon(options.beautifyIconOptions);
            else if (options.pulseIconOptions)
                options.icon = this.createPulseIcon(options.pulseIconOptions);
            var marker = L.marker(latlng, options);
            if (show)
                this.addLeafletLayer(marker, show.addLayer ? show : this.getCreateLayerGroup());
            return marker;
        };
        Map.prototype.createPolyline = function (latlngs, options, show) {
            if (options === void 0) { options = {}; }
            if (show === void 0) { show = true; }
            var line = L.polyline(latlngs, options);
            if (show)
                this.addLeafletLayer(line, show.addLayer ? show : this.getCreateLayerGroup());
            return line;
        };
        Map.prototype.createPolygon = function (paths, options, show) {
            if (options === void 0) { options = {}; }
            if (show === void 0) { show = true; }
            var polygon = L.polygon(paths, options);
            if (show)
                this.addLeafletLayer(polygon, show.addLayer ? show : this.getCreateLayerGroup());
            return polygon;
        };
        Map.prototype.createCircle = function (center, options, show) {
            if (options === void 0) { options = {}; }
            if (show === void 0) { show = true; }
            var polygon = L.circle(center, options);
            if (show)
                this.addLeafletLayer(polygon, show.addLayer ? show : this.getCreateLayerGroup());
            return polygon;
        };
        Map.prototype.createRectangle = function (bounds, options, show) {
            if (options === void 0) { options = {}; }
            if (show === void 0) { show = true; }
            var r = L.rectangle(bounds, options);
            if (show)
                this.addLeafletLayer(r, show.addLayer ? show : this.getCreateLayerGroup());
            return r;
        };
        Map.prototype.createPopup = function (latlng, content, options, show) {
            if (options === void 0) { options = {}; }
            if (show === void 0) { show = true; }
            var popup = L.popup(options).setLatLng(latlng).setContent(content);
            if (show)
                popup.openOn(this.lmap);
            return popup;
        };
        Map.prototype.createIcon = function (options) {
            return L.icon(options);
        };
        Map.prototype.createPulseIcon = function (options) {
            return L.icon.pulse(options);
        };
        Map.prototype.createDivIcon = function (options) {
            return L.divIcon(options);
        };
        Map.prototype.createCircleMarker = function (latlng, options, show) {
            if (options === void 0) { options = {}; }
            if (show === void 0) { show = true; }
            var circle = L.circleMarker(latlng, options);
            if (show)
                this.addLeafletLayer(circle, show.addLayer ? show : this.getCreateLayerGroup());
            return circle;
        };
        Map.prototype.createLink = function (latlngs, lineOptions, markerOptions) {
            var _this = this;
            var lastLatlng, lastMarker;
            return latlngs.map(function (latlng, i) {
                var line;
                if (lastLatlng) {
                    line = _this.createPolyline([lastLatlng, latlng], Array.isArray(lineOptions) ? lineOptions[i - 1] : lineOptions);
                }
                var m = _this.createMarker(latlng, Array.isArray(markerOptions) ? markerOptions[i] : markerOptions).on('drag', function (e) {
                    if (m.__in)
                        m.__in.setLatLngs([m.__in.getLatLngs()[0], e.latlng]);
                    if (m.__out)
                        m.__out.setLatLngs([e.latlng, m.__out.getLatLngs()[1]]);
                });
                m.__in = line;
                if (lastMarker)
                    lastMarker.__out = line;
                lastLatlng = latlng;
                lastMarker = m;
                return m;
            });
        };
        Map.prototype.createNumLink = function (latlngs, lineOptions, markerOptions) {
            if (markerOptions === void 0) { markerOptions = {}; }
            return this.createLink(latlngs, lineOptions, latlngs.map(function (l, i) {
                return Object.assign({
                    beautifyIconOptions: {
                        isAlphaNumericIcon: true,
                        text: i + 1,
                        borderColor: '#00ABDC',
                        textColor: '#00ABDC',
                        innerIconStyle: 'margin-top:0;'
                    }
                }, markerOptions);
            }));
        };
        Map.prototype.doLeaflet = function (fn) {
            fn(this.lmap, L, this.tempLayerGroup);
        };
        Map.prototype.getContextMenu = function () {
            return this.lmap.contextmenu;
        };
        Map.prototype.getCenter = function () {
            return this.lmap.getCenter();
        };
        Map.prototype.setCenter = function (center, zoom) {
            if (zoom === void 0) { zoom = undefined; }
            this.lmap.setView(center, zoom);
        };
        Map.prototype.getBounds = function (layer) {
            var _this = this;
            if (layer === void 0) { layer = null; }
            if (layer) {
                if (layer.getBounds)
                    return layer.getBounds();
                if (layer.toBounds)
                    return layer.toBounds(1);
                if (layer.getLatLng)
                    return layer.getLatLng().toBounds(1);
                if (typeof layer === 'string')
                    return this.getBounds(this.readWkt(layer));
                if (Array.isArray(layer)) {
                    var b_1;
                    layer.forEach(function (l) {
                        var lb = l ? _this.getBounds(l) : null;
                        if (lb)
                            b_1 ? b_1.extend(lb) : b_1 = lb;
                    });
                    return b_1;
                }
            }
            return this.lmap.getBounds();
        };
        Map.prototype.getZoom = function () {
            return this.lmap.getZoom();
        };
        Map.prototype.getBoundsPolygon = function (offset) {
            if (offset === void 0) { offset = 0.0; }
            var bounds = this.getBounds();
            var min = bounds.getSouthWest(), max = bounds.getNorthEast();
            var bbox = "POLYGON((".concat(min.lng - offset, " ").concat(min.lat - offset, ",").concat(min.lng - offset, " ").concat(max.lat + offset, ",").concat(max.lng + offset, " ").concat(max.lat + offset, ",").concat(max.lng + offset, " ").concat(min.lat - offset, ",").concat(min.lng - offset, " ").concat(min.lat - offset, "))");
            return bbox;
        };
        Map.prototype.distance = function (from, to, fix) {
            if (fix === void 0) { fix = undefined; }
            from = this.toLatLng(from);
            to = this.toLatLng(to);
            var dis;
            var crs = this.lmap.options.crs;
            if (crs && crs.distance)
                dis = crs.distance(from, to);
            else
                dis = this.toLatLng(from).distanceTo(this.toLatLng(to));
            if (fix === undefined)
                return dis;
            return dis.toFixed(fix);
        };
        Map.prototype.length = function (line, fix) {
            var _this = this;
            if (fix === void 0) { fix = undefined; }
            if (typeof line === 'string')
                line = this.readWkt(line);
            var last, len = 0;
            if (line.getLatLngs)
                line = line.getLatLngs();
            line.forEach(function (ll) {
                if (Array.isArray(ll) && ll.every(function (l) {
                    return l.getLatLng || (l.lat && l.lng);
                })) {
                    len += _this.length(ll);
                }
                else {
                    if (last)
                        len += _this.distance(ll, last);
                    last = ll;
                }
            });
            if (fix === undefined)
                return len;
            return len.toFixed(fix);
        };
        Map.prototype.geodesicArea = function (latLngs, fix) {
            if (fix === void 0) { fix = undefined; }
            if (typeof latLngs === 'string')
                latLngs = this.readWkt(latLngs);
            if (latLngs.getLatLngs)
                latLngs = latLngs.getLatLngs()[0];
            if (this.mapOptions.crs === L.CRS.Simple)
                return this.simpleArea(latLngs, fix);
            var pointsCount = latLngs.length, area = 0.0, d2r = Math.PI / 180, p1, p2;
            if (pointsCount > 2) {
                for (var i = 0; i < pointsCount; i++) {
                    p1 = latLngs[i];
                    p2 = latLngs[(i + 1) % pointsCount];
                    area += ((p2.lng - p1.lng) * d2r) *
                        (2 + Math.sin(p1.lat * d2r) + Math.sin(p2.lat * d2r));
                }
                area = area * 6378137.0 * 6378137.0 / 2.0;
            }
            area = Math.abs(area);
            if (fix === undefined)
                return area;
            return area.toFixed(fix);
        };
        Map.prototype.simpleDistance = function (p1, p2) {
            p1 = this.toLatLng(p1);
            p2 = this.toLatLng(p2);
            var dy = p1.lat - p2.lat, dx = p1.lng - p2.lng;
            return Math.sqrt(dx * dx + dy * dy);
        };
        Map.prototype.simpleArea = function (latLngs, fix) {
            if (fix === void 0) { fix = undefined; }
            var pointsCount = latLngs.length, area = 0.0, p1, p2, a, b, c, s;
            if (pointsCount > 2) {
                for (var i = 0; i < pointsCount; i++) {
                    p1 = latLngs[i];
                    p2 = latLngs[(i + 1) % pointsCount];
                    a = Math.sqrt(p1.lng * p1.lng + p1.lat * p1.lat);
                    b = Math.sqrt(p2.lng * p2.lng + p2.lat * p2.lat);
                    c = Math.atan(p2.lat / p2.lng) - Math.atan(p1.lat / p1.lng);
                    s = a * b * Math.sin(c) / 2;
                    area += s;
                }
            }
            area = Math.abs(area);
            if (fix === undefined)
                return area;
            return area.toFixed(fix);
        };
        Map.prototype.meterProject = function (meter, isY, position) {
            var c = L.circle(position || this.lmap.getCenter(), { radius: meter });
            c._map = this.lmap;
            c._project();
            return isY ? c._radiusY : c._radius;
        };
        Map.prototype.toLatLng = function (o) {
            if (o instanceof L.LatLng)
                return o;
            if (o.getLatLng)
                return o.getLatLng();
            if (typeof o === 'string')
                return this.readWkt(o).getLatLng();
            if (o.x && o.y)
                return L.latLng(o.y, o.x);
            return L.latLng(o);
        };
        Map.prototype.hilight = function (m, v) {
            if (v === false) {
                if (m._highLayer) {
                    m._highLayer.remove();
                    delete m._highLayer;
                }
            }
            else if (!m._highLayer) {
                var geom = m.geometry;
                if (!geom && m.shape)
                    geom = this.readWkt(m.shape);
                if (geom) {
                    if (geom.type === 'Point' && geom.coordinates)
                        m._highLayer = this.createCircleMarker([geom.coordinates[1], geom.coordinates[0]], {
                            radius: 30,
                            color: 'red',
                            weight: 1
                        });
                    else if (geom.getLatLngs)
                        m._highLayer = this.createPolyline(geom.getLatLngs(), {
                            stroke: true,
                            color: '#aa55ff',
                            weight: 8,
                            opacity: .8,
                            fill: false
                        });
                    else if (geom.getLatLng)
                        m._highLayer = this.createCircleMarker(geom.getLatLng(), {
                            radius: 30,
                            color: 'red',
                            weight: 1
                        });
                }
            }
        };
        Map.prototype.setWaiting = function (v) {
            if (v === void 0) { v = true; }
            this.lmap.getContainer().style.cursor = v ? 'wait' : 'auto';
            this.isWaiting = v;
            return this;
        };
        Map.prototype.setCursor = function (v) {
            if (v === void 0) { v = 'auto'; }
            this.lmap.getContainer().style.cursor = v;
        };
        Map.prototype.setMouseMarker = function (icon) {
            if (typeof icon === 'string') {
                var self_4 = this;
                $('<image style="display:none;" src="' + icon + '">').appendTo('body').on('load', function () {
                    var w = this.naturalWidth, h = this.naturalHeight;
                    $(this).remove();
                    self_4.setMouseMarker({ iconUrl: icon, iconSize: [w, h] });
                });
                return;
            }
            var move = function (e) {
                _mouseMarker.setLatLng(e.latlng);
            };
            if (_mouseMarker)
                _mouseMarker.remove();
            this.off('mousemove', move);
            if (icon !== false) {
                _mouseMarker = this.createMarker([0, 0], { interactive: false, clickable: false, iconOptions: icon });
                this.on('mousemove', move);
            }
        };
        Map.prototype.disableHandler = function (name) {
            this.lmap[name].disable();
        };
        Map.prototype.enableHandler = function (name) {
            this.lmap[name].enable();
        };
        Map.prototype.addControl = function (ctr) {
            if (!ctr.addTo) {
                ctr = L.Control.extend(ctr);
                ctr = new ctr();
            }
            ctr.addTo(this.lmap);
            return ctr;
        };
        return Map;
    }());
    _map.Map = Map;
})(_map || (_map = {}));
//# sourceMappingURL=_map.js.map