﻿using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using Excel = Microsoft.Office.Interop.Excel;


namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// 表单同步数据窗体类
    /// 用于在不同Excel范围之间同步数据，支持多种同步策略和差异处理选项
    /// </summary>
    /// <remarks>
    /// 主要功能：
    /// - 支持索引列和数据列的选择
    /// - 提供多列处理策略
    /// - 支持差异标注和备注
    /// - 支持筛选数据同步
    /// </remarks>
    public partial class frm填表同步数据 : Form
    {
        /// <summary>
        /// Excel更新器实例，用于处理数据同步操作
        /// </summary>
        readonly HHExcelUpdater updater = new();

        /// <summary>
        /// 初始化填表同步数据窗体
        /// </summary>
        public frm填表同步数据()
        {
            InitializeComponent();
            InitializeDefaultSelections();
        }

        /// <summary>
        /// 初始化列表框的默认选项
        /// </summary>
        void InitializeDefaultSelections()
        {
            listBox如发现多列.SelectedIndex = 0;
            listBox差异策略.SelectedIndex = 0;
        }

        /// <summary>
        /// 窗体加载事件处理
        /// </summary>
        void frm填表同步数据_Load(object sender, EventArgs e)
        {
        }

        /// <summary>
        /// 标签页切换事件处理
        /// </summary>
        void tabControl1_SelectedIndexChanged(object sender, EventArgs e)
        {
        }

        /// <summary>
        /// 验证所有必要的输入是否已完成
        /// </summary>
        /// <returns>如果所有必要输入都已完成返回true，否则返回false</returns>
        bool ValidateInputs()
        {
            Dictionary<string, Range> validationChecks = new()
            {
                { "来源索引", ucERS来源索引.SelectedRange },
                { "目标索引", ucERS目标索引.SelectedRange },
                { "来源数据", ucERS来源数据.SelectedRange },
                { "目标数据", ucERS目标数据.SelectedRange }
            };

            foreach (KeyValuePair<string, Range> check in validationChecks)
            {
                if (check.Value == null)
                {
                    MessageBox.Show($"请选择{check.Key}范围", "输入验证", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 同步数据按钮点击事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        void button同步数据_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInputs())
                {
                    return;
                }

                Excel.Application excelApp = Globals.ThisAddIn.Application;
                (bool screenUpdating, bool enableEvents) originalSettings = SaveExcelSettings(excelApp);

                try
                {
                    DisableExcelUpdates(excelApp);
                    ExecuteSyncOperation(excelApp);
                }
                catch (Exception ex)
                {
                    throw new ETException("同步数据操作失败", "Excel数据同步", ex);
                }
                finally
                {
                    RestoreExcelSettings(excelApp, originalSettings);
                }
            }
            catch (ETException hex)
            {
                MessageBox.Show($"同步数据时发生错误：{hex.Message}", "业务错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                throw new ETException("同步数据操作发生未知错误", "Excel数据同步", ex);
            }
        }

        /// <summary>
        /// 保存Excel应用程序的原始设置
        /// </summary>
        /// <param name="app">Excel应用程序实例</param>
        /// <returns>原始设置的元组</returns>
        (bool screenUpdating, bool enableEvents) SaveExcelSettings(Excel.Application app)
        {
            return (app.ScreenUpdating, app.EnableEvents);
        }

        /// <summary>
        /// 禁用Excel更新以提高性能
        /// </summary>
        /// <param name="app">Excel应用程序实例</param>
        void DisableExcelUpdates(Excel.Application app)
        {
            app.ScreenUpdating = false;
            app.EnableEvents = false;
        }

        /// <summary>
        /// 执行数据同步操作
        /// </summary>
        /// <param name="app">Excel应用程序实例</param>
        void ExecuteSyncOperation(Excel.Application app)
        {
            try
            {
                updater.ExecuteRangeUpdate(
                    "表单同步数据",
                    ucERS来源索引.SelectedRange,
                    ucERS来源数据.SelectedRange,
                    ucERS目标索引.SelectedRange,
                    ucERS目标数据.SelectedRange,
                    listBox如发现多列.SelectedItem?.ToString() ?? "取最后一列",
                    listBox差异策略.SelectedItem?.ToString() ?? "以来源表为准(来源为空则不更新)",
                    checkBox差异标注颜色.Checked,
                    checkBox差异原内容改写入备注.Checked,
                    checkBox目标表不存在则来源表标色.Checked,
                    checkBox只匹配来源表已筛选部分.Checked);

                MessageBox.Show("数据同步完成！", "操作成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (ETException)
            {
                // ETException 已经包含了消息框显示逻辑，直接向上抛出
                throw;
            }
            catch (Exception ex)
            {
                StringBuilder detailedError = new();
                detailedError.AppendLine("执行数据同步时发生错误：");
                detailedError.AppendLine();
                detailedError.AppendLine($"错误类型: {ex.GetType().Name}");
                detailedError.AppendLine($"错误消息: {ex.Message}");
                if (ex.InnerException != null)
                {
                    detailedError.AppendLine($"内部错误: {ex.InnerException.Message}");
                }
                detailedError.AppendLine();
                detailedError.AppendLine("技术细节：");
                detailedError.AppendLine(ex.StackTrace);

                throw new ETException(detailedError.ToString(), "Excel数据同步", ex, true);
            }
        }

        /// <summary>
        /// 恢复Excel应用程序的原始设置
        /// </summary>
        /// <param name="app">Excel应用程序实例</param>
        /// <param name="originalSettings">原始设置的元组</param>
        void RestoreExcelSettings(Excel.Application app, (bool screenUpdating, bool enableEvents) originalSettings)
        {
            try
            {
                app.ScreenUpdating = originalSettings.screenUpdating;
                app.EnableEvents = originalSettings.enableEvents;
            }
            catch (Exception ex)
            {
                throw new ETException("还原Excel设置失败", "Excel设置还原", ex);
            }
        }
    }
}