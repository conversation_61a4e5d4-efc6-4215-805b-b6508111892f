<?xml version="1.0" encoding="utf-8"?>
<application title="{{g5.applicationTitle}}"
    version="1.0"
    actionDefUrl="config/g5.action.xml,apps/bimview/action.xml"
    objMetaUrl="config/g5.meta.xml,config/g5.meta.s.xml,config/g5.meta.o.xml,config/g5.meta.e.xml,apps/bimview/meta.xml,
                config/gdo3/g5.meta.xml,config/gdo3/g5.meta.s.xml,config/gdo3/g5.meta.o.xml,config/gdo3/g5.meta.e.xml"
    dictionaryCache="false"
    >
    <script src="apps/pipe/listener.js"></script>
    <script type="text/javascript" src="lib/common/index.js"></script>
    <style>
        .maxwin{width:100%!important;height:100%!important;position:absolute!important;left:0px;top:0px;margin:0px!important;overflow-y:auto}
    </style>
    <script>
        <![CDATA[
            //全局指定对象详细展示里的itemMetasField
            _context.objMetaDefault = {
                itemMetasField: function(data){
                    var om = _bean.getMetaForce(data.objectType);
                    if(data.objectType == 'PIPESECTION' || data.objectType == 'HANGLINGSECTION' || data.objectType == 'COMMONLINE' || data.objectType == 'BURIEDSECTION'){
                        return ['<b>' + om.label + '</b>','  {{g5.fields.CODE}}: ' + data.CODE , '  {{g5.fields.LENGTH}}: '  + data.LENGTH + 'm'];
                    }else if(data.objectType == 'OCABLESECTION' || data.objectType == 'ECABLESECTION'){
                         return ['<b>' + om.label + '</b>','  {{g5.fields.CODE}}: ' + data.CODE , '  {{g5.fields.LENGTH}}: '  + data.LENGTH + 'm', '  {{g5.fields.CAPACITY}}: '  + data.CAPACITY + 'D'];
                    }else if(data.objectType == 'POSITION'){
                        return ['<b>' + om.label + '</b>','  完整名称: ' + data.FULL_NAME ];
                    }else{
                        return ['<b>' + om.label + '</b>','  {{g5.fields.CODE}}: ' + data.CODE ];
                    }
                }
            };
            _context.configReady(()=>
            {
                var params=_util.getUrlParams();
                var func=params.func;
                if(func=="prop")
                {
                    document.getElementById("contentContainer").style.display="none";
                    var id=params["id"];
                    var objType=params["objType"];
                    _bean.showModify(objType, id,null, null,{modalClass:"maxwin"}).then(function (data) 
                    {
                        var selectresult={"success":data!=null,"data":data,"selectcmd":selectcmd};
                        window.parent.window.postMessage(JSON.stringify(selectresult),'*');
                    },function(data)
                    {
                        var selectresult={"success":false,"data":null,"selectcmd":selectcmd};
                        window.parent.window.postMessage(JSON.stringify(selectresult),'*');
                    });
                }                
                else if(func=="actionJSON")
                {

                    debugger
                    var actions=JSON.parse(params["actionjson"]);
                    var withmapAction=JSON.parse(params["withmapAction"]);//判断是否要调标准地址树
                    var isRoot=JSON.parse(params["isRoot"]);
                    if(withmapAction == 0){
                        console.log(actions);
                        if (!Array.isArray(actions)) actions = [actions];
                        actions.forEach(function(a){
                            _context.doAction(a);
                        });
                    }else if(withmapAction == 1){
                    //BUG[22445] 接口调用标准地址确定返回数据
                      $.uiOperation.showAddressTree({addressType1:1,addressId:actions[0].addressId,isRoot:isRoot,deviceOpenMark:true},function (res){
                            let newSuperObj = res;
                            if(newSuperObj != null && newSuperObj.id != null){
                               console.log(newSuperObj);
                               var data = {id:newSuperObj.id,NAME:newSuperObj.NAME,addressLevel:newSuperObj.addressLevel,FULL_NAME:newSuperObj.FULL_NAME};
                               var selectresult={"success":data!=null,"data":data};
                               window.parent.window.postMessage(JSON.stringify(selectresult),'*');
                            }
                        });

                    }else if(withmapAction == 2){
                    //BUG[22445] 接口调用标准地址确定返回数据，页面增加定位按钮
                      $.uiOperation.showAddressTree({addressType1:1,addressId:actions[0].addressId},function (res){
                            let newSuperObj = res;
                            if(newSuperObj != null && newSuperObj.id != null){
                               console.log(newSuperObj);
                               var data = {id:newSuperObj.id,NAME:newSuperObj.NAME,addressLevel:newSuperObj.addressLevel,FULL_NAME:newSuperObj.FULL_NAME};
                               var selectresult={"success":data!=null,"data":data};
                               window.parent.window.postMessage(JSON.stringify(selectresult),'*');
                            }
                        });

                    }
                }
                else if(func=="addResource"){
                   debugger
                   var objtype=params["objtype"];
                   _context.doAction({name: "add",objectType: objtype});

                }
                else if(func=="makeProp")
                {
                    var objType=params["objType"];
                    var selectcmd=params["selectcmd"];
                    var formname=params["formName"];
                    var tmpdata=sessionStorage.getItem("_makeprop_tmp");
                    var noold=params["noold"];
                    if(noold=="1")
                    {
                            debugger;
                        try
                        {
                            localStorage.removeItem("lastinput_"+objType+"_"+formname);
                        }catch(lle){}
                    }
                    if(tmpdata!=null&&tmpdata.length>0)
                        tmpdata=eval("("+tmpdata+")");
                    sessionStorage.removeItem("_makeprop_tmp");
                    if(formname==null)
                        formname="";
                    _bean.showAdd(objType, tmpdata==null?{}:tmpdata, '', formname, { okSubmit: false,modalClass:"maxwin"}).then(function (data) 
                    {
                        var selectresult={"success":data!=null,"data":data,"selectcmd":selectcmd};
                        window.parent.window.postMessage(JSON.stringify(selectresult),'*');
                    },function(data)
                    {
                        var selectresult={"success":false,"data":null,"selectcmd":selectcmd};
                        window.parent.window.postMessage(JSON.stringify(selectresult),'*');
                    });
                }
                else if(func=="cableSection"){
                     _context.doAction({
                        mainConfig: 'apps/gdo3/address/ecableManager/index.xml',
                        title: '光缆',
                        type: "workspace"
                    },{
                    callBackFun:(templeData) => {
                        console.log('XML准备回显的数据:'+templeData)
                        window.parent.window.postMessage(templeData,'*');
                        }
                    })
                }else if(func=="addConnector"){
                var objecttype = params["objecttype"];
                var devid=params["id"];
                var rackid=params["rackid"];
                _context.doAction({
                    type:'workspace',
                    url:'connector.html?objecttype=DEVICE&id='+devid+(rackid==null?"":("&rackid="+rackid))
                })
                }else if(func=="SelectConnector"){
                var objecttype = params["objecttype"];
                var devid=params["id"];
                _context.doAction({
                    type:'workspace',
                    url:'connector.html?objecttype=DEVICE&id='+devid+'&method=select'
                })
                }else if (func=="SelectEConnector"){
                var objecttype = params["objecttype"];
                var devid=params["id"];
                _context.doAction({
                    type:'workspace',
                    url:'econnector.html?objecttype=DEVICE&id='+devid+'&method=select'
                })
                }else if(func=="AddEConnector"){
                var objecttype = params["objecttype"];
                var devid=params["id"];
                var rackid=params["rackid"];
                _context.doAction({
                    type:'workspace',
                    url:'econnector.html?objecttype=DEVICE&id='+devid+(rackid==null?"":("&rackid="+rackid))
                })
                }else if(func=="callPropertyDevice"){
                  var DEVICEID = params["DEVICEID"];
                  var DEVICETYPE = params["DEVICETYPE"];
                  var operation = params["operation"];
                  // 修改属性
                  if(operation=="2"){
                      operation="modify";
                      _bean.showModify(DEVICETYPE, DEVICEID, '', operation,{});
                  }else if(operation=="1"){
                      // 查看属性
                      operation="view";
                      _context.doAction({type:'obj',name:operation,objectType: DEVICETYPE},{objectType: DEVICETYPE,id:DEVICEID});
                  }
                }else if(func=="oNUOroLTQueryOptic"){
                    var id = params["id"];
                    var type = params["type"];
                    var url=null;
                    var title=null;
                    if(type=="ONU"){
                        url="modules/opticsearch/ONUQueryOptic.xml";
                        title="ONU光路查询";
                    }else if(type=="OLT"){
                        url="modules/opticsearch/OLTQueryOptic.xml";
                        title="OLT光路查询";
                    }
                     _context.doAction({
                        mainConfig: url,
                        title: title,
                        type: "workspace"
                    },{
                        ONUOrOltid:id
                    })
                }else if(func=="Labelprinting"){
                    var type = params["type"];
                    var url=null;
                    var title=null;
                    if(type=="Product"){
                    url="apps/gdo3/labelprint/bandwidthLabel/byProduct/index.xml";
                    title="按产品服务";
                    }else if(type=="Device"){
                    url="apps/gdo3/labelprint/bandwidthLabel/byDevice/index.xml";
                    title="按设备";
                    }else if(type=="Engineer"){
                        url="apps/gdo3/labelprint/bandwidthLabel/byEngineer/index.xml";
                       title="按工程";
                    }
                    _context.doAction({
                        mainConfig: url,
                        title: title,
                        type: "workspace"
                    })
                }else if(func=="projectResource"){
                    var projectId=params["projectId"];
                 _context.doAction({
                        mainConfig: "modules/chengduan/projectResource/index.xml",
                        title: "工程资源查询",
                        type: "workspace"
                    },{
                        projectId:projectId
                    })
                 }else if(func=="obdRelateOnu"){
                    var onuId = params["onuId"];
                    var actions=JSON.parse(params["actionjson"]);
                    console.log(actions);
                    if (!Array.isArray(actions)) actions = [actions];
                    actions.forEach(function(a){
                        _context.doAction(a);
                    });
                }
                else if(func=="selectDevice")
                {
                    document.getElementById("contentContainer").style.display="none";
                    var objtype=params["objtype"];
                    var selectcmd=params["selectcmd"];
                    var issinglestr=params["issingle"];
                    var gridname=params["gridname"];
                    var issingle=(issinglestr!="0");
                    if(gridname==null||gridname=="")
                        gridname="mini";
                    document.body.style.background="none";
                    try
                    {
                        document.styleSheets[0].insertRule(".content{max-height:100%!important}",0);
                        document.styleSheets[0].insertRule(".modal{min-height:95%!important;width:90%!important}",0);
                    }
                    catch(docerr){console.log(docerr);}
                    console.log("测试");
                    if(issingle)
                    {
                        _bean.showSingleSelect(objtype, null,gridname,{"_assemble":"address"},{autoLoad:false}).then(function(dev)
                        {
                            console.table(dev);
                            var selectresult={"success":dev!=null,"data":dev,"selectcmd":selectcmd};
                            if (window.parent && window.parent.window) {
                                window.parent.window.postMessage(JSON.stringify(selectresult),'*');
                            } else {
                                window.parent.opener.window.postMessage(JSON.stringify(selectresult),'*')
                            }
                            const returnFunc = _context.getUrlParam('returnfunc')
                            if (window.parent != window && returnFunc != null) {
                                eval("window.parent." + returnFunc).call(window.parent, dev)
                            }
                        });
                    }
                    else
                    {
                        _bean.showSelect(objtype, null,gridname,{"_assemble":"address"},{autoLoad:false}).then(function(devs)
                        {
                            console.table(devs);
                            var selectresult={"success":devs!=null&&devs.length>0,"data":devs,"selectcmd":selectcmd};
                            window.parent.window.postMessage(JSON.stringify(selectresult),'*');
                            const returnFunc = _context.getUrlParam('returnfunc')
                            if (window.parent != window && returnFunc != null) {
                                eval("window.parent." + returnFunc).call(window.parent, devs)
                            }
                        });
                    }
                }
                else if(func=="ObdEquipment")
                {
                    var actions=JSON.parse(params["Obdurl"]);
                    console.log(actions);
                    if (!Array.isArray(actions)) actions = [actions];
                    actions.forEach(function(a){
                        _context.doAction(a);
                    });
                }
                else if(func=="showOConnector")
                {
			        var objecttype = params["objecttype"];
                    var devid=params["id"];
                    var actions={type:'workspace',url:'connector.html?objecttype='+objecttype+'&id='+devid};
                    _context.doAction(actions);
                }
                else if(func=="showEConnector")
                {
			        var objecttype = params["objecttype"];
                    var devid=params["id"];
                    var actions={type:'workspace',url:'econnector.html?objecttype='+objecttype+'&id='+devid};
                    _context.doAction(actions);
                }
                 else if(func=="addressRescover")
                {
                    var deviceId = params["deviceId"];
                     _context.doAction({
                        mainConfig: 'modules/address/devicecoveraddress.xml',
                        title: '覆盖地址管理',
                        type: "workspace",
                        urlParams: {"deviceId":deviceId}  
                    });
                    
                }
                 else if(func=="fiberComprehensiveQuery")
                {
                   
                     _context.doAction({
                        contentUrl: 'apps/pipe/fiberComprehensiveQuery/fiberComprehensiveQuery.html',
                        title: '纤芯综合查询',
                        type: "workspace"
                       
                    });
                    
                }
                else if(func=="provision_provisonLog")
                {

                     _context.doAction({
                        mainConfig: 'modules/opticroute/opticaudit.xml',
                        title: '光路审计查询',
                        type: "workspace"

                    });

                }
                else if(func=="callodfBindOdeviceZjz")
                {

                     _context.doAction({
                        contentUrl: 'modules/odevice/odfBindOdeviceZjz.html',
                        title: '通用工程关联',
                        type: "workspace"

                    });

                }
                else if(func=="diagram_showwindow_42")
                {
			        var filter=params["filter"];
                    var userToken=params["userToken"];
                    var bundlename = params["callDynamicBundle"];
                    addWorkspace("光缆工程路由图", {type:'workspace',url:'modules/diagram/diagram_editor.html?id=42'});
                }
                else if(func=="diagram_showwindow_43")
                {
			         var filter=params["filter"];
                    var userToken=params["userToken"];
                    var bundlename = params["callDynamicBundle"];
                    addWorkspace("电缆工程路由图", {type:'workspace',url:'modules/diagram/diagram_editor.html?id=43'});
                }
                else if(func=="showCableDiagram")
                {
			        var category = params["cat"];
                    if(category==null)
                        category=1;
                    var cableid=params["cableid"];
                    _bean.action("DIAGRAM","findDiagramByCable",{"cableid":cableid,"category":category}).then(function(diagram)
                    {
                        if(diagram)
                        {
                            addWorkspace("逻辑图:"+diagram.NAME, {type:'workspace',url:'modules/diagram/diagram_editor.html?id='+diagram.id});
                        }
                        else
                        {
                            alert("不存在对应的逻辑图");
                            return;
                        }
                    });
                }
                else if(func=="auditAddress")
                {
                    if($.common.getCityMark()=="gdy")
                    {
                        _context.events.on('ADDRESS_TREE_LOCATE', function(e, data){
                            if (_context.doAddressTreeLocate) _context.doAddressTreeLocate(data);
                            _context.doAction({
                                mainConfig: "apps/gdo3/address/man.xml", 
                                type: 'window',
                                style: 'height:600px;width:1000px', context: 'top',
                                title: '标准地址管理',
                                urlParams: {addressId: data.id || data}
                            },{});
                            //_context.doAction({name: 'addressman', urlParams: {addressId: data.id || data}});
                        });
                    }
                    _context.doAction({
                        mainConfig: 'apps/pipe/address/audit/addressAuditMgt.xml', type: 'workspace',
                        title: '地址审核',
                        urlParams: {}
                    },{})
                }
                else if(func=="showPortDiagram")
                {
			        var category = params["cat"];
                    if(category==null)
                        category=1;
                    var portid=params["portid"];
                    _bean.action("DIAGRAM","findDiagramByPort",{"portid":portid,"category":category}).then(function(diagram)
                    {
                        if(diagram)
                        {
                            addWorkspace("逻辑图:"+diagram.NAME, {type:'workspace',url:'modules/diagram/diagram_editor.html?id='+diagram.id});
                        }
                        else
                        {
                            alert("不存在对应的逻辑图");
                            return;
                        }
                    });
                }
                else if(func=="projectDxf")
                {
                    var dxftype=params["dxftype"];
                    var projectid=params["projectid"];
                    var actionname;
                    if(dxftype==1)
                    {
                        actionname="exportDuctViews";
                    }
                    else if(dxftype==2)
                    {
                        actionname="exportCableViews";
                    }
                    _bean.find("PROJECT",{id:projectid}).then(function(project)
                    {
                        if(project!=null)
                        {
                            var divnew=document.createElement("div");
                            divnew.style["z-index"]="999999";
                            divnew.style.height="100%";
                            divnew.style.width="100%";
                            divnew.style.position="absolute";
                            divnew.style.left="0px";
                            divnew.style.top="0px";
                            divnew.style.background="#ffffff";
                            if(dxftype==1)
                            {
                                var url = 'bean/exp.do?_type=PROJECT&_scriptmethod=exportDxf&sourceType=201&entityId=' + project.id+'&_expfilename='+project.NAME+'_pipes.zip';
                                url = encodeURI(_context.fullDataUrl(url));
                                divnew.innerHTML="<a href='"+url+"'><h1>点击下载管道图</h1></a>"
                            }
                            else
                            {
                                var url = 'bean/exp.do?_type=PROJECT&_scriptmethod=exportDxf&sourceType=202&entityId=' + project.id+'&_expfilename='+project.NAME+'_cables.zip';
                                url = encodeURI(_context.fullDataUrl(url));
                                divnew.innerHTML="<a href='"+url+"'><h1>点击下载光缆图</h1></a>"
                            }
                            document.body.appendChild(divnew);
                        }
                        else
                        {
                            alert("查不到要导出的工程");
                        }
                    });
                }
                else if(func=="noResourceDeviceDismantled")
                {
                debugger
                    _context.doAction({
                        type: 'workspace',
                        mainConfig: 'apps/gdo3/noResourceDeviceDismantled/noResourceDeviceDismantled.xml',
                        title: '无源设备退网',
                        urlParams: {type: params.type}
                    });
                }
            });
        ]]>
    </script>
</application>