﻿/*
 * ============================================================================
 * 功能模块：填表同步数据模块
 * ============================================================================
 * 
 * 模块作用：提供Excel表格间数据同步功能，支持基于索引列的智能数据匹配和更新
 * 
 * 主要功能：
 * - 索引匹配：基于指定索引列进行数据行匹配
 * - 数据同步：将来源数据同步到目标数据区域
 * - 多列处理：支持多列数据的不同处理策略
 * - 差异标注：可选择对差异数据进行颜色标注
 * - 备注记录：可将原始内容记录到备注中
 * - 筛选支持：支持只同步已筛选的数据
 * 
 * 执行逻辑：
 * 1. 用户选择来源和目标的索引列、数据列
 * 2. 配置多列处理策略和差异处理选项
 * 3. 系统基于索引列匹配对应行
 * 4. 根据策略更新目标数据并标注差异
 * 
 * 注意事项：
 * - 索引列用于行匹配，必须选择
 * - 数据列是实际要同步的内容
 * - 支持多种差异处理策略
 * ============================================================================
 */

using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using Excel = Microsoft.Office.Interop.Excel;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// 表单同步数据窗体类
    /// 用于在不同Excel范围之间同步数据，支持多种同步策略和差异处理选项
    /// </summary>
    /// <remarks>
    /// 主要功能：
    /// - 支持索引列和数据列的选择
    /// - 提供多列处理策略
    /// - 支持差异标注和备注
    /// - 支持筛选数据同步
    /// </remarks>
    public partial class frm填表同步数据 : Form
    {
        /// <summary>
        /// Excel更新器实例，用于处理数据同步操作
        /// </summary>
        readonly HHExcelUpdater updater = new();

        /// <summary>
        /// 初始化填表同步数据窗体
        /// </summary>
        public frm填表同步数据()
        {
            InitializeComponent();
            InitializeDefaultSelections();
        }

        /// <summary>
        /// 初始化列表框的默认选项
        /// </summary>
        /// <remarks>
        /// 设置多列处理和差异策略的默认选择项，确保界面有合理的初始状态
        /// </remarks>
        void InitializeDefaultSelections()
        {
            // 设置多列处理策略默认选项
            listBox如发现多列.SelectedIndex = 0;
            // 设置差异处理策略默认选项
            listBox差异策略.SelectedIndex = 0;
        }

        /// <summary>
        /// 窗体加载事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 窗体加载时的初始化处理，当前为空实现
        /// </remarks>
        void frm填表同步数据_Load(object sender, EventArgs e)
        {
            // 窗体加载时的处理逻辑（当前为空）
        }

        /// <summary>
        /// 标签页切换事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 处理标签页切换时的逻辑，当前为空实现
        /// </remarks>
        void tabControl1_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 标签页切换时的处理逻辑（当前为空）
        }

        /// <summary>
        /// 验证所有必要的输入是否已完成
        /// </summary>
        /// <returns>如果所有必要输入都已完成返回true，否则返回false</returns>
        /// <remarks>
        /// 检查来源索引、目标索引、来源数据、目标数据四个必要范围是否都已选择
        /// 如果有未选择的范围，会显示提示消息并返回false
        /// </remarks>
        bool ValidateInputs()
        {
            // 定义需要验证的范围选择控件
            Dictionary<string, Range> validationChecks = new()
            {
                { "来源索引", ucERS来源索引.SelectedRange },
                { "目标索引", ucERS目标索引.SelectedRange },
                { "来源数据", ucERS来源数据.SelectedRange },
                { "目标数据", ucERS目标数据.SelectedRange }
            };

            // 逐一检查每个必要的范围选择
            foreach (KeyValuePair<string, Range> check in validationChecks)
            {
                if (check.Value == null)
                {
                    MessageBox.Show($"请选择{check.Key}范围", "输入验证", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 同步数据按钮点击事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 执行数据同步的主要流程：
        /// 1. 验证输入参数的完整性
        /// 2. 保存Excel原始设置并禁用更新以提高性能
        /// 3. 执行数据同步操作
        /// 4. 恢复Excel原始设置
        /// 5. 处理异常并显示相应消息
        /// </remarks>
        void button同步数据_Click(object sender, EventArgs e)
        {
            try
            {
                // 验证必要的输入是否完整
                if (!ValidateInputs())
                {
                    return;
                }

                // 获取Excel应用程序实例
                Excel.Application excelApp = Globals.ThisAddIn.Application;
                // 保存原始设置
                (bool screenUpdating, bool enableEvents) originalSettings = SaveExcelSettings(excelApp);

                try
                {
                    // 禁用Excel更新以提高性能
                    DisableExcelUpdates(excelApp);
                    // 执行数据同步操作
                    ExecuteSyncOperation(excelApp);
                }
                catch (Exception ex)
                {
                    throw new ETException("同步数据操作失败", "Excel数据同步", ex);
                }
                finally
                {
                    // 确保恢复Excel原始设置
                    RestoreExcelSettings(excelApp, originalSettings);
                }
            }
            catch (ETException hex)
            {
                // 处理业务异常
                MessageBox.Show($"同步数据时发生错误：{hex.Message}", "业务错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                // 处理未知异常
                throw new ETException("同步数据操作发生未知错误", "Excel数据同步", ex);
            }
        }

        /// <summary>
        /// 保存Excel应用程序的原始设置
        /// </summary>
        /// <param name="app">Excel应用程序实例</param>
        /// <returns>原始设置的元组，包含屏幕更新和事件启用状态</returns>
        /// <remarks>
        /// 保存ScreenUpdating和EnableEvents的原始状态，以便后续恢复
        /// </remarks>
        (bool screenUpdating, bool enableEvents) SaveExcelSettings(Excel.Application app)
        {
            return (app.ScreenUpdating, app.EnableEvents);
        }

        /// <summary>
        /// 禁用Excel更新以提高性能
        /// </summary>
        /// <param name="app">Excel应用程序实例</param>
        /// <remarks>
        /// 在批量操作前禁用屏幕更新和事件处理，可显著提高执行速度
        /// </remarks>
        void DisableExcelUpdates(Excel.Application app)
        {
            // 禁用屏幕更新
            app.ScreenUpdating = false;
            // 禁用事件处理
            app.EnableEvents = false;
        }

        /// <summary>
        /// 执行数据同步操作
        /// </summary>
        /// <param name="app">Excel应用程序实例</param>
        /// <remarks>
        /// 调用HHExcelUpdater执行实际的数据同步操作，包括：
        /// - 传递所有用户选择的参数
        /// - 处理多列策略和差异处理选项
        /// - 异常处理和详细错误信息构建
        /// </remarks>
        void ExecuteSyncOperation(Excel.Application app)
        {
            try
            {
                // 调用Excel更新器执行范围更新操作
                updater.ExecuteRangeUpdate(
                    "表单同步数据",                                                    // 操作名称
                    ucERS来源索引.SelectedRange,                                      // 来源索引列
                    ucERS来源数据.SelectedRange,                                      // 来源数据列
                    ucERS目标索引.SelectedRange,                                      // 目标索引列
                    ucERS目标数据.SelectedRange,                                      // 目标数据列
                    listBox如发现多列.SelectedItem?.ToString() ?? "取最后一列",        // 多列处理策略
                    listBox差异策略.SelectedItem?.ToString() ?? "以来源表为准(来源为空则不更新)", // 差异处理策略
                    checkBox差异标注颜色.Checked,                                     // 是否标注差异颜色
                    checkBox差异原内容改写入备注.Checked,                             // 是否将原内容写入备注
                    checkBox目标表不存在则来源表标色.Checked,                         // 目标不存在时是否标色来源
                    checkBox只匹配来源表已筛选部分.Checked);                          // 是否只匹配筛选数据

                MessageBox.Show("数据同步完成！", "操作成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (ETException)
            {
                // ETException 已经包含了消息框显示逻辑，直接向上抛出
                throw;
            }
            catch (Exception ex)
            {
                // 构建详细的错误信息
                StringBuilder detailedError = new();
                detailedError.AppendLine("执行数据同步时发生错误：");
                detailedError.AppendLine();
                detailedError.AppendLine($"错误类型: {ex.GetType().Name}");
                detailedError.AppendLine($"错误消息: {ex.Message}");
                if (ex.InnerException != null)
                {
                    detailedError.AppendLine($"内部错误: {ex.InnerException.Message}");
                }
                detailedError.AppendLine();
                detailedError.AppendLine("技术细节：");
                detailedError.AppendLine(ex.StackTrace);

                throw new ETException(detailedError.ToString(), "Excel数据同步", ex, true);
            }
        }

        /// <summary>
        /// 恢复Excel应用程序的原始设置
        /// </summary>
        /// <param name="app">Excel应用程序实例</param>
        /// <param name="originalSettings">原始设置的元组，包含屏幕更新和事件启用状态</param>
        /// <remarks>
        /// 将Excel的ScreenUpdating和EnableEvents恢复到操作前的状态
        /// 这是确保Excel正常运行的重要步骤
        /// </remarks>
        void RestoreExcelSettings(Excel.Application app, (bool screenUpdating, bool enableEvents) originalSettings)
        {
            try
            {
                // 恢复屏幕更新设置
                app.ScreenUpdating = originalSettings.screenUpdating;
                // 恢复事件处理设置
                app.EnableEvents = originalSettings.enableEvents;
            }
            catch (Exception ex)
            {
                throw new ETException("还原Excel设置失败", "Excel设置还原", ex);
            }
        }
    }
}