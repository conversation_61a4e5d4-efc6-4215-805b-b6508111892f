/*
 * ============================================================================
 * 功能模块：WebBrowser状态桥接模块
 * ============================================================================
 * 
 * 模块作用：作为WebBrowser状态通信的中介，负责在不同窗体间传递WebBrowser的显示/隐藏状态，
 *           实现UI界面与WebBrowser状态的同步
 * 
 * 主要功能：
 * - 定义状态变更委托和事件
 * - 提供触发状态变更事件的方法
 * - 实现跨窗体的状态通知机制
 * 
 * 执行逻辑：
 * 1. 定义StatusChangedHandler委托用于处理状态变更
 * 2. 创建StatusChanged事件供订阅者注册
 * 3. 提供OnStatusChanged方法触发状态变更事件
 * 4. 通知所有订阅者WebBrowser的当前状态
 * 
 * 注意事项：
 * - 使用静态类实现全局状态共享
 * - 通过委托和事件机制实现松耦合的状态通知
 * - 使用空条件运算符(?:)安全地调用事件处理器
 * ============================================================================
 */

namespace HyAssistant
{
    /// <summary>
    /// WebBrowser状态桥接类，用于在不同窗体间传递WebBrowser状态
    /// </summary>
    public static class WebBrowserStatusBridge
    {
        /// <summary>
        /// 定义状态变更委托
        /// </summary>
        /// <param name="isHidden">WebBrowser是否处于隐藏状态</param>
        public delegate void StatusChangedHandler(bool isHidden);

        /// <summary>
        /// 创建状态变更事件
        /// </summary>
        public static event StatusChangedHandler StatusChanged;

        /// <summary>
        /// 触发状态变更事件的方法
        /// </summary>
        /// <param name="isHidden">WebBrowser是否处于隐藏状态</param>
        public static void OnStatusChanged(bool isHidden)
        {
            StatusChanged?.Invoke(isHidden);
        }
    }
}