/*
 * ============================================================================
 * 功能模块：文件复制器配置窗体
 * ============================================================================
 * 
 * 模块作用：提供文件复制任务的配置界面，支持添加、编辑、删除复制任务
 * 
 * 主要功能：
 * - 配置管理：添加、编辑、删除文件复制任务配置
 * - 参数设置：设置源路径、目标路径、文件模式、时间间隔等参数
 * - 条件筛选：配置文件创建/修改时间限制
 * - 目录结构：配置是否创建日期时间子目录
 * 
 * 执行逻辑：
 * 1. 从INI文件加载现有配置
 * 2. 通过界面编辑配置参数
 * 3. 保存配置到INI文件
 * 4. 文件复制器根据配置执行复制任务
 * 
 * 注意事项：
 * - 配置名称必须唯一
 * - 源路径和目标路径为必填项
 * - 支持多个源路径和文件模式（每行一个）
 * ============================================================================
 */

using ET;
using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;

namespace HyAssistant
{
    /// <summary>
    /// 文件复制器配置窗体类，提供文件复制任务的配置界面
    /// </summary>
    /// <remarks>
    /// 该类负责管理文件复制任务的配置，包括添加、编辑、删除配置，
    /// 以及设置源路径、目标路径、文件模式、时间间隔等参数
    /// </remarks>
    public partial class FileCopierConfigForm : Form
    {
        /// <summary>
        /// INI配置文件的完整路径
        /// </summary>
        readonly string _iniFilePath;
        
        /// <summary>
        /// INI文件操作对象
        /// </summary>
        readonly ETIniFile _iniFile;
        
        /// <summary>
        /// 存储显示名称到配置节UUID的映射字典
        /// </summary>
        readonly Dictionary<string, string> _sectionUuids = new Dictionary<string, string>();
        
        /// <summary>
        /// 存储配置节与对应控件的映射字典
        /// </summary>
        readonly Dictionary<string, Control> _sectionControls = new Dictionary<string, Control>();
        
        /// <summary>
        /// 配置节前缀常量，用于标识文件复制任务的配置节
        /// </summary>
        const string SECTION_PREFIX = "filesync-";
        
        /// <summary>
        /// 防止保存时触发重新加载的标记
        /// </summary>
        bool _suppressReload = false;

        /// <summary>
        /// 构造函数，初始化配置窗体
        /// </summary>
        /// <remarks>
        /// 初始化组件、准备配置文件路径、创建INI文件对象并加载配置节
        /// </remarks>
        public FileCopierConfigForm()
        {
            try
            {
                InitializeComponent();
                _iniFilePath = ETConfig.GetConfigDirectory("filecopier.ini");

                // 创建INI文件对象
                _iniFile = new ETIniFile(_iniFilePath);

                // 加载配置节
                LoadSections();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化配置表单时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 加载所有配置节到列表框
        /// </summary>
        /// <remarks>
        /// 执行逻辑：清空列表 → 检查配置文件存在 → 遍历配置节 → 添加到列表框
        /// </remarks>
        void LoadSections()
        {
            listBoxSections.Items.Clear();
            _sectionUuids.Clear();
            _sectionControls.Clear();

            // 如果配置文件不存在，直接返回
            if (!File.Exists(_iniFilePath))
            {
                return;
            }

            foreach (string sectionName in _iniFile.GetSectionNames())
            {
                if (sectionName.StartsWith(SECTION_PREFIX, StringComparison.OrdinalIgnoreCase))
                {
                    string displayName = _iniFile.GetValue(sectionName, "name", sectionName);
                    _sectionUuids[displayName] = sectionName;
                    listBoxSections.Items.Add(displayName);
                }
            }
        }

        /// <summary>
        /// 列表框选择项变更事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 当用户选择不同的配置项时，加载对应的配置数据到表单
        /// </remarks>
        void listBoxSections_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listBoxSections.SelectedItem == null) return;

            // 如果标记为true，不执行加载操作
            if (_suppressReload) return;

            string selectedDisplayName = listBoxSections.SelectedItem.ToString();
            string selectedUuid = _sectionUuids[selectedDisplayName];
            LoadSectionData(selectedUuid);
        }

        /// <summary>
        /// 加载指定配置节的数据到表单
        /// </summary>
        /// <param name="sectionName">配置节名称</param>
        /// <remarks>
        /// 执行逻辑：读取配置值 → 设置表单控件值 → 处理特殊字段（如日期时间）
        /// </remarks>
        void LoadSectionData(string sectionName)
        {
            try
            {
                textBoxName.Text = _iniFile.GetValue(sectionName, "name", string.Empty);
                textBoxSourcePaths.Text = _iniFile.GetValue(sectionName, "SourcePaths", string.Empty).Replace("|", Environment.NewLine);
                textBoxTargetPath.Text = _iniFile.GetValue(sectionName, "TargetPath", string.Empty);
                textBoxFilePattern.Text = _iniFile.GetValue(sectionName, "FilePattern", "*.*").Replace("|", Environment.NewLine);

                // 处理IntervalInMinutes，确保它是有效的数字
                int intervalValue;
                if (!int.TryParse(_iniFile.GetValue(sectionName, "IntervalInMinutes", "0"), out intervalValue))
                {
                    intervalValue = 0;
                }
                numericUpDownInterval.Value = intervalValue;

                // 处理日期时间值，使用固定格式并提供默认值
                DateTime defaultDate = DateTime.Now;
                DateTime creationTime, modificationTime;

                if (!DateTime.TryParse(_iniFile.GetValue(sectionName, "CreationTimeLimit", defaultDate.ToString("yyyy-MM-dd HH:mm:ss")), out creationTime))
                {
                    creationTime = defaultDate;
                }

                if (!DateTime.TryParse(_iniFile.GetValue(sectionName, "ModificationTimeLimit", defaultDate.ToString("yyyy-MM-dd HH:mm:ss")), out modificationTime))
                {
                    modificationTime = defaultDate;
                }

                dateTimePickerCreationTime.Value = creationTime;
                dateTimePickerModificationTime.Value = modificationTime;

                checkBoxCreateDateTimeSubDir.Checked = _iniFile.GetValue(sectionName, "CreateDateTimeSubDir", "0") == "1";
                checkBoxUseCreationTime.Checked = _iniFile.GetValue(sectionName, "UseCreationTimeForSubDir", "0") == "1";
                checkBoxEnable.Checked = _iniFile.GetValue(sectionName, "enable", "0") == "1";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载配置数据时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                ClearForm();
            }
        }

        /// <summary>
        /// 保存按钮点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 执行逻辑：验证表单 → 更新配置节 → 保存到INI文件
        /// </remarks>
        void buttonSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (listBoxSections.SelectedItem == null) return;

                // 验证必填字段
                if (string.IsNullOrWhiteSpace(textBoxName.Text))
                {
                    MessageBox.Show("请输入配置名称", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    textBoxName.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(textBoxSourcePaths.Text))
                {
                    MessageBox.Show("请输入源路径", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    textBoxSourcePaths.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(textBoxTargetPath.Text))
                {
                    MessageBox.Show("请输入目标路径", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    textBoxTargetPath.Focus();
                    return;
                }

                string selectedDisplayName = listBoxSections.SelectedItem.ToString();
                string selectedUuid = _sectionUuids[selectedDisplayName];
                string newDisplayName = textBoxName.Text.Trim();

                // 检查名称是否重复
                if (newDisplayName != selectedDisplayName && _sectionUuids.ContainsKey(newDisplayName))
                {
                    MessageBox.Show("配置名称已存在，请使用其他名称", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    textBoxName.Focus();
                    return;
                }

                // 设置标记防止重新加载
                _suppressReload = true;

                // 更新显示名称
                if (newDisplayName != selectedDisplayName)
                {
                    _sectionUuids.Remove(selectedDisplayName);
                    _sectionUuids[newDisplayName] = selectedUuid;
                    int selectedIndex = listBoxSections.SelectedIndex;
                    listBoxSections.Items[selectedIndex] = newDisplayName;
                }

                SaveSectionData(selectedUuid);

                // 确保配置文件目录存在
                string configDir = Path.GetDirectoryName(_iniFilePath);
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }

                _iniFile.IniWriteFile();

                // 重置标记
                _suppressReload = false;

                MessageBox.Show("配置已保存", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                // 确保在异常时也重置标记
                _suppressReload = false;
                MessageBox.Show($"保存配置时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 保存配置节数据到INI文件
        /// </summary>
        /// <param name="sectionName">配置节名称</param>
        /// <remarks>
        /// 执行逻辑：从表单获取值 → 设置INI文件值 → 处理特殊格式（如日期时间）
        /// </remarks>
        void SaveSectionData(string sectionName)
        {
            try
            {
                _iniFile.SetValue(sectionName, "name", textBoxName.Text.Trim());
                _iniFile.SetValue(sectionName, "SourcePaths", textBoxSourcePaths.Text.Replace(Environment.NewLine, "|").TrimEnd('|'));
                _iniFile.SetValue(sectionName, "TargetPath", textBoxTargetPath.Text.Trim());
                _iniFile.SetValue(sectionName, "FilePattern", string.IsNullOrWhiteSpace(textBoxFilePattern.Text) ? "*.*" : textBoxFilePattern.Text.Replace(Environment.NewLine, "|").TrimEnd('|'));
                _iniFile.SetValue(sectionName, "IntervalInMinutes", numericUpDownInterval.Value.ToString());

                // 使用固定格式保存日期时间值，以确保一致性
                string creationTimeFormat = dateTimePickerCreationTime.Value.ToString("yyyy-MM-dd HH:mm:ss");
                string modificationTimeFormat = dateTimePickerModificationTime.Value.ToString("yyyy-MM-dd HH:mm:ss");

                _iniFile.SetValue(sectionName, "CreationTimeLimit", creationTimeFormat);
                _iniFile.SetValue(sectionName, "ModificationTimeLimit", modificationTimeFormat);
                _iniFile.SetValue(sectionName, "CreateDateTimeSubDir", checkBoxCreateDateTimeSubDir.Checked ? "1" : "0");
                _iniFile.SetValue(sectionName, "UseCreationTimeForSubDir", checkBoxUseCreationTime.Checked ? "1" : "0");
                _iniFile.SetValue(sectionName, "enable", checkBoxEnable.Checked ? "1" : "0");
            }
            catch (Exception ex)
            {
                throw new Exception($"保存节点数据时出错: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 添加按钮点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 执行逻辑：创建新配置节 → 设置默认值 → 保存到INI文件 → 选择新配置
        /// </remarks>
        void buttonAdd_Click(object sender, EventArgs e)
        {
            try
            {
                // 设置标记防止重新加载
                _suppressReload = true;

                string newDisplayName = $"新配置{_sectionUuids.Count + 1}";

                // 检查名称是否重复
                while (_sectionUuids.ContainsKey(newDisplayName))
                {
                    newDisplayName = $"新配置{_sectionUuids.Count + 1}";
                }

                string newUuid = $"{SECTION_PREFIX}{Guid.NewGuid().ToString("N")}";
                _sectionUuids[newDisplayName] = newUuid;
                listBoxSections.Items.Add(newDisplayName);

                // 使用与其他地方一致的格式初始化新配置
                _iniFile.SetValue(newUuid, "name", newDisplayName);
                _iniFile.SetValue(newUuid, "enable", "1");
                _iniFile.SetValue(newUuid, "CreateDateTimeSubDir", "0");
                _iniFile.SetValue(newUuid, "IntervalInMinutes", "5");

                // 使用与SaveSectionData一致的日期时间格式
                string nowFormatted = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                _iniFile.SetValue(newUuid, "CreationTimeLimit", nowFormatted);
                _iniFile.SetValue(newUuid, "ModificationTimeLimit", nowFormatted);

                _iniFile.SetValue(newUuid, "FilePattern", "*.*");
                _iniFile.SetValue(newUuid, "UseCreationTimeForSubDir", "1");

                // 确保配置文件目录存在
                string configDir = Path.GetDirectoryName(_iniFilePath);
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }

                _iniFile.IniWriteFile();

                // 选择新项后再重置标记
                listBoxSections.SelectedItem = newDisplayName;

                // 手动加载数据后重置标记
                LoadSectionData(newUuid);
                _suppressReload = false;
            }
            catch (Exception ex)
            {
                // 确保在异常时也重置标记
                _suppressReload = false;
                MessageBox.Show($"添加新配置时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 删除按钮点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 执行逻辑：删除选中配置节 → 更新INI文件 → 选择下一个配置
        /// </remarks>
        void buttonDelete_Click(object sender, EventArgs e)
        {
            if (listBoxSections.SelectedItem == null) return;

            string selectedDisplayName = listBoxSections.SelectedItem.ToString();
            string selectedUuid = _sectionUuids[selectedDisplayName];

            try
            {
                // 设置标记防止重新加载
                _suppressReload = true;

                _iniFile.DeleteSection(selectedUuid);
                _sectionUuids.Remove(selectedDisplayName);
                int selectedIndex = listBoxSections.SelectedIndex;
                listBoxSections.Items.Remove(selectedDisplayName);
                _iniFile.IniWriteFile();

                // 如果删除后还有项目，选择下一个项目
                if (listBoxSections.Items.Count > 0)
                {
                    int newIndex = Math.Min(selectedIndex, listBoxSections.Items.Count - 1);
                    listBoxSections.SelectedIndex = newIndex;

                    // 手动加载新选中项的数据
                    string newSelectedDisplayName = listBoxSections.Items[newIndex].ToString();
                    string newSelectedUuid = _sectionUuids[newSelectedDisplayName];
                    LoadSectionData(newSelectedUuid);
                }
                else
                {
                    ClearForm();
                }

                // 重置标记
                _suppressReload = false;
            }
            catch (Exception ex)
            {
                // 确保在异常时也重置标记
                _suppressReload = false;
                MessageBox.Show($"删除配置时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空表单所有字段
        /// </summary>
        /// <remarks>
        /// 重置所有表单控件到默认值
        /// </remarks>
        void ClearForm()
        {
            textBoxName.Clear();
            textBoxSourcePaths.Clear();
            textBoxTargetPath.Clear();
            textBoxFilePattern.Clear();
            numericUpDownInterval.Value = 0;
            dateTimePickerCreationTime.Value = DateTime.Now;
            dateTimePickerModificationTime.Value = DateTime.Now;
            checkBoxCreateDateTimeSubDir.Checked = false;
            checkBoxUseCreationTime.Checked = false;
            checkBoxEnable.Checked = false;
        }

        /// <summary>
        /// 浏览源路径按钮点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 打开文件夹选择对话框，将选择的路径添加到源路径文本框
        /// </remarks>
        void buttonBrowseSource_Click(object sender, EventArgs e)
        {
            using (FolderBrowserDialog dialog = new FolderBrowserDialog())
            {
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    if (string.IsNullOrEmpty(textBoxSourcePaths.Text))
                    {
                        textBoxSourcePaths.Text = dialog.SelectedPath;
                    }
                    else
                    {
                        textBoxSourcePaths.Text += $"{Environment.NewLine}{dialog.SelectedPath}";
                    }
                }
            }
        }

        /// <summary>
        /// 浏览目标路径按钮点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 打开文件夹选择对话框，将选择的路径设置为目标路径
        /// </remarks>
        void buttonBrowseTarget_Click(object sender, EventArgs e)
        {
            using (FolderBrowserDialog dialog = new FolderBrowserDialog())
            {
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    textBoxTargetPath.Text = dialog.SelectedPath;
                }
            }
        }
    }
}