﻿using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// 批量查找窗体类，提供Excel中的批量查找、筛选功能
    /// </summary>
    /// <remarks>
    /// 此类提供以下主要功能：
    /// - 横向和纵向的文本查找
    /// - 基于条件的数据筛选
    /// - 非空值和非零值的筛选
    /// - 支持多种查找模式：完全匹配、包含、起始于、结束于
    /// </remarks>
    /// <example>
    /// 使用示例：
    /// <code>
    /// var searchForm = new frm批量查找();
    /// searchForm.Run横向查找(range, "searchText");
    /// </code>
    /// </example>
    public partial class frm批量查找 : Form
    {
        #region 私有字段

        /// <summary>
        /// 窗体自动缩放行为控制
        /// </summary>
        //readonly AutoCollapseWindowBehavior collapseBehavior;

        /// <summary>
        /// 标记的单元格范围，用于高亮显示匹配结果
        /// </summary>
        Range _markedRange;

        /// <summary>
        /// 搜索方向（行/列）
        /// </summary>
        /// <remarks>
        /// 决定是在行方向还是列方向进行搜索
        /// </remarks>
        EnumRowColumn _searchDirection;

        /// <summary>
        /// 搜索关键词集合，存储所有需要查找的关键词
        /// </summary>
        HashSet<string> _searchTerms = [];

        /// <summary>
        /// 匹配到的项目集合，存储所有成功匹配的文本
        /// </summary>
        HashSet<string> _matchedItems = [];

        /// <summary>
        /// 匹配到的单元格范围集合，存储所有包含匹配文本的单元格
        /// </summary>
        HashSet<Range> _matchedRanges = [];

        /// <summary>
        /// 未匹配到的项目集合，存储所有未能找到匹配的搜索词
        /// </summary>
        HashSet<string> _unmatchedItems = [];

        /// <summary>
        /// 列可见性状态字典，用于记录筛选前的列显示状态
        /// </summary>
        /// <remarks>
        /// Key: 列索引
        /// Value: 是否可见
        /// </remarks>
        Dictionary<int, bool> _columnVisibility = [];

        /// <summary>
        /// 当前选中的单元格范围，用于确定搜索和筛选的范围
        /// </summary>
        Range _selectedRange;

        /// <summary>
        /// 当前工作表，用于执行工作表级别的操作
        /// </summary>
        Worksheet _currentWorksheet;

        /// <summary>
        /// 后缀配置字典，存储不同类型的后缀配置
        /// </summary>
        /// <remarks>
        /// Key: 后缀类型名称
        /// Value: 该类型包含的后缀数组
        /// </remarks>
        Dictionary<string, string[]> _suffixConfig = [];

        #endregion 私有字段

        #region 初始化方法

        /// <summary>
        /// 初始化搜索相关变量
        /// </summary>
        /// <remarks>
        /// 此方法执行以下操作：
        /// - 清空所有搜索相关的集合
        /// - 重置标记范围
        /// - 获取当前活动工作表
        /// </remarks>
        void InitializeSearchVariables()
        {
            try
            {
                _searchTerms.Clear();
                _matchedItems.Clear();
                _matchedRanges.Clear();
                _unmatchedItems.Clear();
                _columnVisibility.Clear();
                _markedRange = null;
                _currentWorksheet = Globals.ThisAddIn.Application.ActiveSheet;
            }
            catch (Exception ex)
            {
                throw new ETException("初始化搜索变量失败", "初始化Excel搜索", ex);
            }
        }

        #endregion 初始化方法

        #region 搜索和筛选方法

        /// <summary>
        /// 执行搜索和筛选操作
        /// </summary>
        /// <param name="startRange">起始单元格范围，指定搜索的起始位置</param>
        /// <param name="direction">搜索方向，指定是按行还是按列搜索</param>
        /// <exception cref="HyException">当搜索操作失败时抛出，包含详细的错误信息</exception>
        /// <remarks>
        /// 此方法执行以下步骤：
        /// 1. 初始化搜索变量和状态
        /// 2. 获取搜索模式和搜索词
        /// 3. 执行搜索操作并收集结果
        /// 4. 显示搜索结果并应用筛选
        /// 
        /// 搜索过程中会处理以下情况：
        /// - 空的起始范围
        /// - 无效的搜索词
        /// - 搜索过程中的异常
        /// </remarks>
        void PerformSearchAndFilter(Range startRange, EnumRowColumn direction)
        {
            try
            {
                InitializeSearchVariables();

                if (startRange == null)
                    return;
                _searchDirection = direction;

                EnumFindMode searchMode = GetSearchMode();
                string[] searchTerms = GetSearchTerms();
                if (searchTerms.Length == 0)
                    return;

                _matchedRanges = new HashSet<Range>(
                    ETExcelExtensions
                        .FuzzySearch(
                            startRange,
                            _searchTerms,
                            _searchDirection,
                            searchMode,
                            out _matchedItems,
                            out _unmatchedItems,
                            out _selectedRange,
                            out _columnVisibility
                        )
                        .Cast<Range>()
                );

                DisplaySearchResults(startRange);
            }
            catch (Exception ex)
            {
                throw new ETException("执行搜索和筛选操作失败", "Excel搜索筛选", ex);
            }
        }

        /// <summary>
        /// 获取当前选择的搜索模式
        /// </summary>
        /// <returns>搜索模式枚举值</returns>
        /// <remarks>根据用户在界面上选择的单选按钮返回相应的搜索模式</remarks>
        EnumFindMode GetSearchMode()
        {
            if (radioButton相同.Checked)
                return EnumFindMode.Equal;
            if (radioButton包含.Checked)
                return EnumFindMode.Contains;
            if (radioButton起始于.Checked)
                return EnumFindMode.StartsWith;
            if (radioButton结束于.Checked)
                return EnumFindMode.EndsWith;
            return EnumFindMode.Equal;
        }

        /// <summary>
        /// 获取搜索关键词数组
        /// </summary>
        /// <returns>处理后的搜索关键词数组</returns>
        /// <exception cref="HyException">当处理搜索关键词时发生错误</exception>
        /// <remarks>
        /// 此方法执行以下操作：
        /// 1. 处理用户输入的搜索文本，支持多种分隔符
        /// 2. 根据设置处理关键字（可选）
        /// 3. 根据选择的后缀配置处理文本（可选）
        /// 4. 过滤空值并去重
        /// 
        /// 支持的分隔符：
        /// - 换行符
        /// - 制表符
        /// - 分号
        /// - 逗号
        /// - 竖线
        /// - 顿号
        /// </remarks>
        string[] GetSearchTerms()
        {
            try
            {
                string inputText = textBox查找.Text.RegularEnter(true);
                string[] searchTerms;

                // 根据输入格式确定分隔方式
                bool hasNewLine = inputText.IndexOf("\n", StringComparison.Ordinal) >= 0;
                searchTerms = hasNewLine
                    ? inputText.Split('\n')
                    : textBox查找.Text.Split("\t;,|、，；");

                // 处理关键字
                if (ShouldProcessKeywords())
                {
                    searchTerms = ProcessKeywords(searchTerms);
                }

                // 处理后缀
                if (ShouldProcessSuffix())
                {
                    searchTerms = ProcessSuffix(searchTerms);
                }

                // 过滤空值并保存搜索词到集合
                searchTerms = FilterAndSaveSearchTerms(searchTerms);

                return searchTerms;
            }
            catch (Exception ex)
            {
                throw new ETException("处理搜索关键词失败", "Excel关键词处理", ex);
            }
        }

        /// <summary>
        /// 判断是否需要处理关键字
        /// </summary>
        /// <returns>如果需要处理关键字则返回true，否则返回false</returns>
        bool ShouldProcessKeywords()
        {
            return checkBox只取关键字.Enabled && checkBox只取关键字.Checked;
        }

        /// <summary>
        /// 处理搜索词中的关键字
        /// </summary>
        /// <param name="terms">原始搜索词数组</param>
        /// <returns>处理后的搜索词数组</returns>
        string[] ProcessKeywords(string[] terms)
        {
            return terms.Select(term => ETStringPrefixSuffixProcessor.RemovePrefixAndSuffix(term)).ToArray();
        }

        /// <summary>
        /// 判断是否需要处理后缀
        /// </summary>
        /// <returns>如果需要处理后缀则返回true，否则返回false</returns>
        bool ShouldProcessSuffix()
        {
            return comboBoxSuffix.Text != "无"
                && comboBoxSuffix.Text.Length > 0
                && _suffixConfig.ContainsKey(comboBoxSuffix.Text);
        }

        /// <summary>
        /// 处理搜索词中的后缀
        /// </summary>
        /// <param name="terms">原始搜索词数组</param>
        /// <returns>处理后的搜索词数组</returns>
        string[] ProcessSuffix(string[] terms)
        {
            return terms.Select(term => ETStringPrefixSuffixProcessor.RemovePrefixAndSuffix(term)).ToArray();
        }

        /// <summary>
        /// 过滤空值并保存搜索词到集合
        /// </summary>
        /// <param name="terms">原始搜索词数组</param>
        /// <returns>过滤后的搜索词数组</returns>
        string[] FilterAndSaveSearchTerms(string[] terms)
        {
            string[] filteredTerms = terms.Where(term => !string.IsNullOrWhiteSpace(term)).ToArray();
            _searchTerms = filteredTerms.ToHashset();
            return filteredTerms;
        }

        /// <summary>
        /// 显示搜索结果
        /// </summary>
        /// <param name="startRange">起始单元格范围</param>
        /// <remarks>
        /// 此方法执行以下操作：
        /// 1. 标记匹配的范围
        /// 2. 应用筛选
        /// 3. 更新UI显示
        /// 4. 恢复Excel应用程序的正常模式
        /// </remarks>
        void DisplaySearchResults(Range startRange)
        {
            try
            {
                if (_matchedRanges != null && _matchedRanges.Any())
                {
                    _markedRange = ETExcelExtensions.UnionRanges(_matchedRanges);
                    _markedRange?.Format条件格式警示色(EnumWarningColor.筛选色);

                    ApplyFilter(startRange);
                }

                UpdateUI(startRange);
                ETExcelExtensions.SetAppNormalMode(true);
            }
            catch (Exception ex)
            {
                throw new ETException("显示搜索结果失败", "Excel结果显示", ex);
            }
        }

        /// <summary>
        /// 应用筛选
        /// </summary>
        /// <param name="startRange">起始单元格范围</param>
        /// <remarks>
        /// 根据搜索方向执行以下操作：
        /// - 行搜索时应用行筛选
        /// - 列搜索时应用列筛选
        /// </remarks>
        void ApplyFilter(Range startRange)
        {
            try
            {
                switch (_searchDirection)
                {
                    case EnumRowColumn.Row:
                        ApplyRowFilter();
                        break;

                    case EnumRowColumn.Column:
                        ApplyColumnFilter(startRange);
                        break;
                }
            }
            catch (Exception ex)
            {
                throw new ETException("应用筛选失败", "Excel筛选", ex);
            }
        }

        /// <summary>
        /// 应用行筛选
        /// </summary>
        /// <remarks>
        /// 此方法执行以下操作：
        /// - 隐藏选定范围的整列
        /// - 显示所有标记范围的整列
        /// </remarks>
        void ApplyRowFilter()
        {
            try
            {
                if (_selectedRange != null)
                    _selectedRange.EntireColumn.Hidden = true;
                if (_markedRange != null)
                    _markedRange.EntireColumn.Hidden = false;
            }
            catch (Exception ex)
            {
                throw new ETException("应用行筛选失败", "Excel行筛选", ex);
            }
        }

        /// <summary>
        /// 应用列筛选
        /// </summary>
        /// <param name="startRange">起始单元格范围</param>
        /// <remarks>
        /// 此方法执行以下操作：
        /// - 获取工作表的筛选行号
        /// - 在筛选行上应用自动筛选
        /// - 使用单元格颜色作为筛选条件
        /// </remarks>
        void ApplyColumnFilter(Range startRange)
        {
            try
            {
                int filterRow = startRange.Worksheet.GetWorksheetFilterRowNumber();
                if (filterRow == 0)
                {
                    MessageBox.Show("未设置筛选");
                    return;
                }
                int filterColumn = startRange.Cells[1, 1].Column;
                startRange
                    .Worksheet.Rows[filterRow]
                    .AutoFilter(
                        filterColumn,
                        EnumWarningColor.筛选色,
                        XlAutoFilterOperator.xlFilterCellColor
                    );
            }
            catch (Exception ex)
            {
                throw new ETException("应用列筛选失败", "Excel列筛选", ex);
            }
        }

        /// <summary>
        /// 更新用户界面
        /// </summary>
        /// <param name="startRange">起始单元格范围</param>
        /// <remarks>
        /// 此方法执行以下操作：
        /// - 更新搜索位置信息显示
        /// - 更新匹配结果统计
        /// - 更新匹配和未匹配项目的文本显示
        /// </remarks>
        void UpdateUI(Range startRange)
        {
            try
            {
                // 更新搜索位置信息
                label列.Text =
                    _searchDirection == EnumRowColumn.Row
                        ? $"行:{ETExcelExtensions.ConvertNumberToColumnName(startRange.Cells[1, 1].Row).ToUpper()}"
                        : $"列:{ETExcelExtensions.ConvertNumberToColumnName(startRange.Cells[1, 1].Column).ToUpper()}";

                // 更新结果统计
                tabPage存在.Text = $"存在: {_matchedRanges?.Count ?? 0}";
                tabPage不存在.Text = $"不存在: {_unmatchedItems?.Count ?? 0}";
                textBox存在.Text = string.Join(Environment.NewLine, _matchedItems ?? Enumerable.Empty<string>());
                textBox不存在.Text = string.Join(Environment.NewLine, _unmatchedItems ?? Enumerable.Empty<string>());
            }
            catch (Exception ex)
            {
                throw new ETException("更新界面失败", "Excel界面更新", ex);
            }
        }

        /// <summary>
        /// 执行横向查找操作
        /// </summary>
        /// <param name="inputRange">输入的单元格范围</param>
        /// <param name="findText">要查找的文本</param>
        /// <remarks>
        /// 此方法供外部调用，执行以下操作：
        /// - 设置查找文本
        /// - 激活目标工作表
        /// - 执行横向查找
        /// - 清理资源并关闭窗体
        /// </remarks>
        public void Run横向查找(Range inputRange, string findText)
        {
            try
            {
                if (inputRange == null || string.IsNullOrEmpty(findText))
                    return;
                textBox查找.Text = findText;
                inputRange.GetParent().Activate();

                PerformSearchAndFilter(inputRange.Cells[1, 1].Offset[0, 1], EnumRowColumn.Row);

                _matchedRanges.Clear(); //为了关闭的时候不提醒
                Close();
                Dispose();
            }
            catch (Exception ex)
            {
                throw new ETException("横向查找失败", "Excel横向查找", ex);
            }
        }

        /// <summary>
        /// 执行竖向查找操作
        /// </summary>
        /// <param name="inputRange">输入的单元格范围</param>
        /// <param name="findText">要查找的文本</param>
        /// <remarks>
        /// 此方法供外部调用，执行以下操作：
        /// - 设置查找文本
        /// - 激活目标工作表
        /// - 执行竖向查找
        /// - 清理资源并关闭窗体
        /// </remarks>
        public void Run竖向查找(Range inputRange, string findText)
        {
            try
            {
                if (inputRange == null || string.IsNullOrEmpty(findText))
                    return;
                textBox查找.Text = findText;
                inputRange.GetParent().Activate();

                PerformSearchAndFilter(inputRange.Cells[1, 1].Offset[1, 0], EnumRowColumn.Column);

                _matchedRanges.Clear(); //为了关闭的时候不提醒
                Close();
                Dispose();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception($"执行竖向查找失败: {ex.Message}"));
                throw new ETException("执行竖向查找失败", ex);
            }
        }

        /// <summary>
        /// 筛选非空白单元格
        /// </summary>
        /// <param name="findDirection">查找方向（行/列）</param>
        /// <param name="outZero">是否排除零值</param>
        /// <remarks>
        /// 此方法执行以下操作：
        /// - 初始化搜索变量
        /// - 获取目标范围
        /// - 查找非空白单元格
        /// - 显示查找结果
        /// </remarks>
        void 筛选非空白单元格(EnumRowColumn findDirection, bool outZero)
        {
            try
            {
                InitializeSearchVariables();
                Range startRange = ThisAddIn.ExcelApplication.Selection.Cells[1, 1];
                _searchDirection = findDirection;

                // 获取目标范围
                Range targetRange =
                    _searchDirection == EnumRowColumn.Row
                        ? ETExcelExtensions.GetSelectionRow()
                        : ETExcelExtensions.GetSelectionColumn();
                targetRange = targetRange
                    .GetRangeAfterCell(startRange, _searchDirection)
                    .OptimizeRangeSize();
                _selectedRange = targetRange;

                // 查找非空白单元格
                _matchedRanges = new HashSet<Range>(
                    targetRange
                        .FindNonEmptyCells(out _matchedItems, out _columnVisibility, outZero)
                        .Cast<Range>()
                );

                //查找结束，输出显示
                DisplaySearchResults(startRange);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception($"筛选非空白单元格失败: {ex.Message}"));
                throw new ETException("筛选非空白单元格失败", ex);
            }
        }

        #endregion 搜索和筛选方法

        #region 事件处理方法

        /// <summary>
        /// 处理窗体加载事件
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <exception cref="HyException">当窗体加载失败时抛出</exception>
        /// <remarks>
        /// 此方法执行以下操作：
        /// 1. 加载后缀配置文件
        /// 2. 初始化后缀下拉列表
        /// 3. 设置下拉列表的显示样式
        /// </remarks>
        void frm批量查找_Load(object sender, EventArgs e)
        {
            try
            {
                InitializeSuffixConfig();
                InitializeSuffixComboBox();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception($"窗体加载失败: {ex.Message}"));
                throw new ETException("窗体加载失败", ex);
            }
        }

        /// <summary>
        /// 初始化后缀配置
        /// </summary>
        void InitializeSuffixConfig()
        {
            _suffixConfig = ETConfig.ConfigFileToDictionary(
                ETConfig.GetConfigDirectory("批量查找后缀.config")
            );
        }

        /// <summary>
        /// 初始化后缀下拉列表
        /// </summary>
        void InitializeSuffixComboBox()
        {
            comboBoxSuffix.Items.Clear();
            comboBoxSuffix.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBoxSuffix.Items.Add("无");
            foreach (string key in _suffixConfig.Keys)
            {
                comboBoxSuffix.Items.Add(key);
            }
        }

        /// <summary>
        /// 处理窗体关闭事件
        /// </summary>
        void frm批量查找_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                if (_matchedRanges.Count <= 0)
                    return;

                DialogResult result = MessageBox.Show(
                    @"处于筛选状态，是否关闭窗口？",
                    @"是否关闭",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question
                );
                if (result != DialogResult.Yes)
                    e.Cancel = true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception($"窗体关闭失败: {ex.Message}"));
                // 不抛出异常，避免影响窗体关闭
            }
        }

        /// <summary>
        /// 处理"相同"单选按钮状态变更事件
        /// </summary>
        /// <remarks>
        /// 此方法执行以下操作：
        /// - 根据"相同"按钮的选中状态启用或禁用关键字选项
        /// - 重置后缀下拉框选择
        /// </remarks>
        void radioButton相同_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                checkBox只取关键字.Enabled = !radioButton相同.Checked;
                comboBoxSuffix.Text = "无";
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception($"处理单选按钮状态变更失败: {ex.Message}"));
                throw new ETException("处理单选按钮状态变更失败", ex);
            }
        }

        /// <summary>
        /// 处理清空菜单项点击事件
        /// </summary>
        /// <remarks>
        /// 此方法执行以下操作：
        /// - 清空查找文本框
        /// - 选择第一个标签页
        /// - 重置标签页文本
        /// </remarks>
        void 清空ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                textBox查找.Text = string.Empty;
                tabControl设置.SelectTab(0);
                tabPage存在.Text = @"存在";
                tabPage不存在.Text = @"不存在";
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception($"清空操作失败: {ex.Message}"));
                throw new ETException("清空操作失败", ex);
            }
        }

        /// <summary>
        /// 处理取消标色后关闭按钮点击事件
        /// </summary>
        /// <remarks>
        /// 此方法执行以下操作：
        /// - 激活当前工作表
        /// - 清除标记范围的格式
        /// - 清空匹配范围集合
        /// </remarks>
        void button取消标色后关闭_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentWorksheet != null)
                    _currentWorksheet.Activate();
                if (_markedRange != null)
                    _markedRange.FormatConditions.Delete();
                _matchedRanges.Clear();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception($"取消标色失败: {ex.Message}"));
                throw new ETException("取消标色失败", ex);
            }
        }

        /// <summary>
        /// 处理提取选定单元格唯一值菜单项点击事件
        /// </summary>
        /// <remarks>
        /// 此方法执行以下操作：
        /// - 获取选定范围
        /// - 提取非空单元格的唯一值
        /// - 将结果显示在查找文本框中
        /// </remarks>
        void 提取选定单元格唯一值ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                Range selectedRange = ETExcelExtensions.GetSelectionRange().OptimizeRangeSize();
                List<string> uniqueValues = selectedRange.ConvertRangeToStringList(true, true);
                if (uniqueValues == null || uniqueValues.Count == 0)
                {
                    MessageBox.Show(@"全部空白单元格");
                    return;
                }

                textBox查找.Text = string.Join(Environment.NewLine, uniqueValues);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception($"提取唯一值失败: {ex.Message}"));
                throw new ETException("提取唯一值失败", ex);
            }
        }

        /// <summary>
        /// 处理横向筛选非0值单元格菜单项点击事件
        /// </summary>
        void 横向筛选非0值单元格ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                筛选非空白单元格(EnumRowColumn.Row, false);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception("横向筛选非0值失败"));
                throw new ETException("横向筛选非0值失败", ex);
            }
        }

        /// <summary>
        /// 处理横向筛选非空白单元格菜单项点击事件
        /// </summary>
        void 横向筛选非空白单元格ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                筛选非空白单元格(EnumRowColumn.Row, true);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception("横向筛选非空白单元格失败"));
                throw new ETException("横向筛选非空白单元格失败", ex);
            }
        }

        /// <summary>
        /// 处理竖向筛选非0值单元格菜单项点击事件
        /// </summary>
        void 竖向筛选非0值单元格ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                筛选非空白单元格(EnumRowColumn.Column, false);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception("竖向筛选非0值失败"));
                throw new ETException("竖向筛选非0值失败", ex);
            }
        }

        /// <summary>
        /// 处理竖向筛选非空白单元格菜单项点击事件
        /// </summary>
        void 竖向筛选非空白单元格ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                筛选非空白单元格(EnumRowColumn.Column, true);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception("竖向筛选非空白单元格失败"));
                throw new ETException("竖向筛选非空白单元格失败", ex);
            }
        }

        void button竖向筛选_Click(object sender, EventArgs e)
        {
            try
            {
                Range startRange = ThisAddIn.ExcelApplication.Selection.Cells[1, 1];
                PerformSearchAndFilter(startRange, EnumRowColumn.Column);

                // 启用自动缩放功能
                //collapseBehavior.IsEnabled = true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception("竖向筛选失败"));
                MessageBox.Show($"竖向筛选失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);

                // 禁用自动缩放功能
                //collapseBehavior.IsEnabled = false;
            }
        }

        void button横向筛选_Click(object sender, EventArgs e)
        {
            try
            {
                Range startRange = ThisAddIn.ExcelApplication.Selection.Cells[1, 1];
                PerformSearchAndFilter(startRange, EnumRowColumn.Row);

                // 启用自动缩放功能
                //collapseBehavior.IsEnabled = true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception("横向筛选失败"));
                MessageBox.Show($"横向筛选失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);

                // 禁用自动缩放功能
                //collapseBehavior.IsEnabled = false;
            }
        }

        void button去筛选_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentWorksheet == null)
                    return;

                if (_searchDirection == EnumRowColumn.Row)
                {
                    foreach (KeyValuePair<int, bool> kvp in _columnVisibility)
                    {
                        _currentWorksheet.Columns[kvp.Key].Hidden = kvp.Value;
                    }
                }
                else
                {
                    int filterRowIndex = _currentWorksheet.GetWorksheetFilterRowNumber();
                    if (filterRowIndex > 0)
                    {
                        _currentWorksheet.Rows[filterRowIndex].AutoFilter(_selectedRange.Cells[1, 1].Column);
                    }
                }

                if (_markedRange != null)
                {
                    _markedRange.FormatConditions.Delete();
                }
                _matchedRanges.Clear();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception("取消筛选失败"));
                MessageBox.Show($"取消筛选失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion 事件处理方法

        #region 辅助方法

        /// <summary>
        /// 检查筛选数据是否可用
        /// </summary>
        /// <returns>如果筛选数据可用则返回true，否则返回false</returns>
        /// <remarks>验证所需的数据和范围是否已正确初始化</remarks>
        bool IsFilterDataAvailable()
        {
            return _selectedRange != null
                && _currentWorksheet != null
                && _matchedRanges != null
                && _matchedRanges.Count > 0;
        }

        /// <summary>
        /// 移除横向筛选
        /// </summary>
        /// <remarks>根据保存的可见性状态恢复列的显示或隐藏</remarks>
        void RemoveHorizontalFilter()
        {
            foreach (KeyValuePair<int, bool> kvp in _columnVisibility)
                _currentWorksheet.Columns[kvp.Key].Hidden = kvp.Value;
        }

        /// <summary>
        /// 移除纵向筛选
        /// </summary>
        /// <remarks>移除应用于筛选行的自动筛选</remarks>
        void RemoveVerticalFilter()
        {
            int filterRowIndex = _currentWorksheet.GetWorksheetFilterRowNumber();
            if (filterRowIndex > 0)
                _currentWorksheet.Rows[filterRowIndex].AutoFilter(_selectedRange.Cells[1, 1].Column);
        }

        /// <summary>
        /// 清除格式并重置找到的范围
        /// </summary>
        /// <remarks>删除所有标记范围的格式条件，并清空匹配范围集合</remarks>
        void ClearFormatAndResetFoundRanges()
        {
            _markedRange?.FormatConditions.Delete();
            _matchedRanges.Clear();
        }

        #endregion 辅助方法

        #region 初始化及界面操作

        public frm批量查找()
        {
            InitializeComponent();

            // 初始化自动缩放行为
            //collapseBehavior = new AutoCollapseWindowBehavior(this);
            //collapseBehavior.IsEnabled = false;  // 默认禁用自动缩放功能
            //collapseBehavior.SetExpandedSize(305, 457);  // 设置展开时的尺寸
            //collapseBehavior.CollapsedWidth = 150;  // 设置收缩时的宽度
            //Height = 427;
        }

        #endregion 初始化及界面操作
    }
}
