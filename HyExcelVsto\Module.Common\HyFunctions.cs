﻿/*
 * ============================================================================
 * 功能模块：Excel通用功能函数库
 * ============================================================================
 * 
 * 模块作用：提供Excel操作的通用功能函数和工具方法
 * 
 * 主要功能：
 * - 经纬度处理：GPS坐标的粘贴、转换和格式化
 * - 坐标转换：支持GCJ-02到WGS84的坐标系转换
 * - 数据粘贴：智能识别和处理剪贴板中的地理坐标数据
 * - 格式化操作：提供各种数据格式化和处理功能
 * - 异步操作：支持异步执行耗时操作
 * - 用户交互：提供确认对话框和用户提示功能
 * 
 * 执行逻辑：
 * 1. 从剪贴板获取文本数据
 * 2. 解析和提取GPS坐标信息
 * 3. 检测坐标系类型并进行转换
 * 4. 格式化坐标数据并写入Excel
 * 5. 提供用户交互和确认功能
 * 
 * 注意事项：
 * - 支持多种GPS坐标格式的识别
 * - 自动检测GCJ-02坐标系并提示转换
 * - 使用同步上下文确保线程安全
 * ============================================================================
 */

using ET;
using HyExcelVsto.Extensions;
using Microsoft.Office.Core;
using Microsoft.Office.Interop.Excel;
using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Application = Microsoft.Office.Interop.Excel.Application;
using XlHAlign = Microsoft.Office.Interop.Excel.XlHAlign;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// Excel通用功能函数库
    /// </summary>
    /// <remarks>
    /// 提供Excel操作的通用功能函数和工具方法，支持GPS坐标处理等功能
    /// </remarks>
    partial class HyFunctions
    {
        #region 变量常量
        public static Application XlApp;
        static readonly SynchronizationContext _syncContext =
            SynchronizationContext.Current ?? new SynchronizationContext();
        #endregion 变量常量

        #region 按键事件

        #region 粘贴经纬度
        public static void Button合并粘贴经纬度_Click(CommandBarButton ctrl, ref bool cancelDefault)
        {
            string clipText = ETString.GetClipboardText(1000);
            if (clipText == null)
                return;

            string[] gps = ZnWireless.ExtractGpsAndAddress(clipText);
            if (gps == null || gps[0] == null || gps[1] == null)
                return;
            double dlng = Convert.ToDouble(gps[0]);
            double dlat = Convert.ToDouble(gps[1]);

            if (clipText.ToLower().IndexOf("gcj", StringComparison.Ordinal) >= 0 &&
                ETExcelExtensions.ShowConfirmDialog("可能是GCJ-02坐标,是否进行坐标转换"))
            {
                ETGpsConvertUtil pc = new();
                double[] pgps = pc.gcj02towgs84(dlng, dlat);
                XlApp.Selection.Resize[1, 1].Value = $"{pgps[0]:N6},{pgps[1]:N6}";
            }
            else
            {
                XlApp.Selection.Resize[1, 1].Value = $"{dlng:N6},{dlat:N6}";
            }
        }

        public static void Button粘贴经纬度_Click(CommandBarButton ctrl, ref bool cancelDefault)
        {
            string clipText = ETString.GetClipboardText(200);
            if (clipText == null)
                return;

            string[] gps = ZnWireless.ExtractGpsAndAddress(clipText);
            if (gps == null || gps[0] == null || gps[1] == null)
                return;

            转换gcj坐标并粘贴(clipText, gps);
        }

        static void 转换gcj坐标并粘贴(string clipText, string[] gps)
        {
            double dlng = Convert.ToDouble(gps[0]);
            double dlat = Convert.ToDouble(gps[1]);

            if (clipText.ToLower().IndexOf("gcj", StringComparison.Ordinal) >= 0 &&
                ETExcelExtensions.ShowConfirmDialog("可能是GCJ-02坐标,是否进行坐标转换(转换后自动更新粘贴板)"))
            {
                ETGpsConvertUtil pc = new();
                double[] pgps = pc.gcj02towgs84(dlng, dlat);

                XlApp.Selection.Resize[1, 1].Value = Convert.ToDouble($"{pgps[0]:N6}");
                XlApp.Selection.Resize[1, 1].Offset[0, 1].Value = Convert.ToDouble($"{pgps[1]:N6}");

                Clipboard.SetText($"{pgps[0]:N6},{pgps[1]:N6}");
            }
            else
            {
                XlApp.Selection.Resize[1, 1].Value = $"{dlng:N6}";
                XlApp.Selection.Resize[1, 1].Offset[0, 1].Value = $"{dlat:N6}";
            }
        }

        public static void Button粘贴经纬度及地址_Click(CommandBarButton ctrl, ref bool cancelDefault)
        {
            string clipText = ETString.GetClipboardText(200);
            if (clipText == null)
                return;

            string[] gps = ZnWireless.ExtractGpsAndAddress(clipText, true);
            if (gps == null || gps[0] == null || gps[1] == null)
                return;

            转换gcj坐标并粘贴(clipText, gps);

            if (!string.IsNullOrEmpty(gps[2]))
                XlApp.Selection.Resize[1, 1].Offset[0, 2].Value = gps[2];
        }
        #endregion 粘贴经纬度

        #region 工参整理
        public static void Button粘贴工参_Click(CommandBarButton ctrl, ref bool cancelDefault)
        {
            string clipText = ETString.GetClipboardText(400);
            if (clipText == null)
                return;

            string[,] gc = Get工参信息(clipText);
            string allmsg = "[工参信息]:";

            Range startRange = XlApp.Selection.Resize[1, 1];

            for (int i = 0; i < gc.GetLength(0); i++)
            {
                string[] tmp = new string[gc.GetLength(1) - 1];
                for (int j = 0; j < tmp.Length; j++)
                    tmp[j] = gc[i, j + 1];
                string output = string.Join("/", tmp);
                allmsg = $"{allmsg}\n{$"{gc[i, 0]}:{output}"}";

                //查找并填入信息
                Range findRange = ETExcelExtensions.FindFirstCell(XlApp.ActiveSheet.Cells[1, 1], gc[i, 0]);
                if (findRange == null)
                    continue;

                Range writeRange = XlApp.ActiveSheet.Cells[startRange.Row, findRange.Column];
                writeRange.NumberFormatLocal = "@";
                writeRange.Value = output;
            }

            if (startRange.Comment == null)

                startRange.AddComment(allmsg); //填写到备注,以防部分查找不到
            else
                startRange.Comment.Text(allmsg);

            startRange.Comment.Visible = false;
            startRange.Comment.Shape.TextFrame.AutoSize = true;
        }

        public static bool Check是否工参表格(string clipText)
        {
            if (clipText == null)
                return false;
            clipText = clipText.TrimStart();
            if (clipText.StartsWith("扇区"))
                return true;
            return false;
        }

        public static string[,] Get工参信息(string input)
        {
            if (input == null || input.Length > 200)
                return null;
            //全局处理
            input = ETString.RegexReplace(input, @"\n+", "\n");
            input = ETString.RegexReplace(input, @"\s*$", string.Empty);
            input = ETString.RegexReplace(input, @"\r+", string.Empty);

            //处理并识别标题
            if (input.IndexOf('\n') < 0)
                return null;
            string title = ETString.GetLeftPart(input, "\n"); //取得第一个回车前内容作为标题行,标题栏需要特殊处理
            title = ETString.RegexReplace(title, @"[\x20]+", " "); //标题行默认去掉 多个空格
            title = title.Replace("下 倾", "下倾");
            title = title.Replace("倾 角", "倾角");
            title = title.Replace("方 向", "方向");
            title = title.Replace("向 角", "向角");
            title = title.Replace("类 型", "类型");
            title = title.Replace("天 线", "天线");
            title = title.Replace("扇 区", "扇区");
            title = title.Replace("备 注", "Note");
            title = title.Replace(" 倾 ", " ");
            title = title.Replace(" 角 ", " ");
            title = title.Replace(" 注 ", " ");
            title = title.Replace(" 区 ", " ");

            title = ETString.RegexReplace(title, @"扇[\x20\t]?区", "扇区");
            title = ETString.RegexReplace(title, @"天[\x20\t]?线[\x20\t]?类型?", "天线类型");
            title = ETString.RegexReplace(title, @"机[\x20\t]?械[\x20\t]?下?[\x20\t]?倾?角?", "机械下倾");
            title = ETString.RegexReplace(title, @"电[\x20\t]?调[\x20\t]?下?[\x20\t]?倾?角?", "电调下倾");
            title = ETString.RegexReplace(title, @"总[\x20\t]?下[\x20\t]?倾?角?", "总下倾");

            string[] originalRow = input.Split('\n');
            if (originalRow.Length == 0)
                return null;

            char splitChar = title.IndexOf('\t') > 0 ? '\t' : ' ';
            string[] originalCol = title.Split(splitChar); //string[,]是等长数组，列维度一样，只要取任意一行的列维度即可确定整个二维数组的列维度

            int r = originalRow.Length;
            int c = originalCol.Length;
            string[,] twoArray = new string[c, r];

            for (int i = 0; i < r; i++)
            {
                if (splitChar == ' ')
                    originalRow[i] = ETString.RegexReplace(originalRow[i], @"[\x20]+", " ");

                originalCol = i == 0 ? title.Split(splitChar) : originalRow[i].Split(splitChar);

                for (int j = 0; j < Math.Min(originalCol.Length, c); j++)
                    //if (originalCol.Length != c) twoArray[j, i] = "识别出错";
                    //else twoArray[j, i] = originalCol[j];
                    twoArray[j, i] = originalCol[j];
            }

            return twoArray;
        }
        #endregion 工参整理

        public static void Btn复制为无格式表格(CommandBarButton sender, ref bool cancelDefault)
        {
            //如果只是一个单元格，直接复制具体的值
            Range selectionRange = ETExcelExtensions.GetSelectionRange();
            if (selectionRange == null)
                return;
            if (selectionRange.Cells.Count == 1 && !selectionRange.IsCellEmpty())
            {
                Clipboard.SetText(selectionRange.Value.ToString());
                return;
            }

            selectionRange = ETExcelExtensions.GetSelectionRange();
            string str = ETExcelExtensions.ConvertRangeToCSV(selectionRange);
            if (!string.IsNullOrEmpty(str))
                Clipboard.SetText(str);
        }

        public static void Btn复制为摘要信息(CommandBarButton sender, ref bool cancelDefault)
        {
            //如果只是一个单元格，直接复制具体的值
            Range selectionRange = ETExcelExtensions.GetSelectionRange();
            if (selectionRange == null)
                return;
            if (selectionRange.Cells.Count == 1 && !selectionRange.IsCellEmpty())
            {
                Clipboard.SetText(selectionRange.Value.ToString());
                return;
            }

            selectionRange = ETExcelExtensions.GetSelectionRange();
            string str = ETExcelExtensions.ConvertRangeToCSV(selectionRange);
            if (!string.IsNullOrEmpty(str))
                Clipboard.SetText(str);
        }

        public static void Btn跨列居中(CommandBarButton sender, ref bool cancelDefault)
        {
            Range rng = ETExcelExtensions.GetSelectionRange();
            if (rng == null)
                return;

            rng.HorizontalAlignment = XlHAlign.xlHAlignCenterAcrossSelection;
        }

        //public static void Button查多表索引_Click(CommandBarButton sender, ref bool cancelDefault)
        //{
        //    Range rng = XlApp.Selection.Cells[1, 1];
        //    if (rng.IsNullOrEmpty()) return;

        //    if (ThisAddIn.OpenFormNames.Contains("多表索引工具"))
        //        HyFormExtensions.HySendMessageToWindow("多表索引工具", $"SearchAll:{rng.Address}");
        //}

        public static void button清除所选条件格式_Click(CommandBarButton sender, ref bool cancelDefault)
        {
            Range selectionRange = ETExcelExtensions.GetSelectionRange();
            selectionRange?.FormatConditions.Delete();
        }

        public static void button清除全表条件格式_Click(CommandBarButton sender, ref bool cancelDefault)
        {
            Worksheet sh = XlApp.ActiveSheet;
            sh.Cells.FormatConditions.Delete();
        }

        public static void Button条件格式标记_Click(CommandBarButton sender, ref bool cancelDefault)
        {
            Range selectionRange = ETExcelExtensions.GetSelectionRange();
            if (selectionRange == null)
                return;

            EnumWarningColor ewc = ETExcelExtensions.ParseWarningColor(sender.Caption, true);
            selectionRange.Format条件格式警示色(ewc, 0, ewc != EnumWarningColor.黑色);
        }

        public static void Button标记重复值_Click(CommandBarButton sender, ref bool cancelDefault)
        {
            Range selectionRange = ETExcelExtensions.GetSelectionRange();
            if (selectionRange == null)
                return;

            selectionRange.Format标记重复值();
        }

        public static void Button格式标记_Click(CommandBarButton sender, ref bool cancelDefault)
        {
            Range selectionRange = ETExcelExtensions.GetSelectionRange();
            if (selectionRange == null)
                return;

            EnumWarningColor ewc = ETExcelExtensions.ParseWarningColor(sender.Caption, true);
            selectionRange.Format设置警示色(ewc);
        }

        public static void Button设置NumberFormat格式_Click(CommandBarButton sender, ref bool cancelDefault)
        {
            Range selectionRange = ETExcelExtensions.GetSelectionRange();
            if (selectionRange == null)
                return;

            selectionRange.NumberFormatLocal = sender.Caption switch
            {
                "格式:不显示0值" => "G/通用格式;-G/通用格式;\"\"",
                "格式:0.00000" => "0.00000_ ",
                "格式:0.00" => "0.00;-0.00;\"\"",
                "格式:文本" => "@",
                "格式:正常格式" => "G/通用格式",
                "格式:日期" => "yyyy/mm/dd",
                _ => selectionRange.NumberFormatLocal
            };
        }

        public static void Button字符变公式_Click(CommandBarButton sender, ref bool cancelDefault)
        {
            Range selectionRange = ETExcelExtensions.GetSelectionRange();
            if (selectionRange == null)
                return;

            ETExcelExtensions.SetAppFastMode();

            try
            {
                foreach (Range subRng in selectionRange)
                {
                    subRng.NumberFormatLocal = @"G/通用格式";
                    subRng.Formula = subRng.Formula;
                }
            }
            catch (Exception)
            {
                //ingore error
            }

            ETExcelExtensions.SetAppNormalMode(true);
        }

        public static void Button设置为字符格式_Click(CommandBarButton sender, ref bool cancelDefault)
        {
            Range selectionRange = ETExcelExtensions.GetSelectionRange();
            if (selectionRange == null)
                return;
            ETExcelExtensions.SetAppFastMode();

            foreach (Range cell in selectionRange.Cells)
            {
                cell.NumberFormatLocal = "@";
                cell.Formula = cell.Formula;
            }

            ETExcelExtensions.SetAppNormalMode(true);
        }

        public static void Button添加删除字符_Click(CommandBarButton sender, ref bool cancelDefault)
        {
            ETExcelExtensions.SetAppFastMode();

            frm前后添加删除字符 frm = new();
            ThisAddIn.OpenForm(frm, XlFormPosition.Center);

            ETExcelExtensions.SetAppNormalMode(true);
        }

        public static void Button向下填充_Click(CommandBarButton sender, ref bool cancelDefault)
        {
            frm向下填充 frm = new();
            ThisAddIn.OpenForm(frm, XlFormPosition.Center);
        }

        public static void Button合并字符_Click(CommandBarButton sender, ref bool cancelDefault)
        {
            //如果只是一个单元格，直接复制具体的值
            Range selectionRange = ETExcelExtensions.GetSelectionRange();
            if (selectionRange == null)
                return;
            if (selectionRange.Cells.Count == 1 && !selectionRange.IsCellEmpty())
            {
                Clipboard.SetText(selectionRange.Value.ToString());
                return;
            }

            frm复制及合并 frm = new();
            ThisAddIn.OpenForm(frm, XlFormPosition.Center);
        }

        public static void 常用字符处理工具_Click(CommandBarButton sender, ref bool cancelDefault)
        {
            Range selectionRange = XlApp.Selection.Cells[1, 1];
            if (selectionRange == null)
                return;

            frm字符处理 frm = new();
            switch (sender.Caption)
            {
                case "标记重复值":
                    frm.tabControl1.SelectedTab = frm.tabPage标记;
                    break;


                default:
                    frm.tabControl1.SelectedTab = frm.tabPage标记;
                    break;
            }

            ThisAddIn.OpenForm(frm);
        }

        public static void 字符规整_Click(CommandBarButton sender, ref bool cancelDefault)
        {
            frm字符处理 frm = new();

            frm.tabControl1.SelectedTab = frm.tabPage规整字符串;
            frm.checkBox去除所有空格.Checked = true;
            frm.checkBox首尾空格.Checked = true;
            frm.checkBox结尾换行符.Checked = true;
            frm.checkBox制表符.Checked = true;
            frm.checkBox换行符.Checked = true;
            frm.checkBoxToDBC.Checked = true;

            ThisAddIn.OpenForm(frm);
        }


        public static void Btn隐藏范围外内容(bool displayAlert = true)
        {
            Range selectionRange = ETExcelExtensions.GetSelectionRange();
            if (selectionRange == null)
                return;

            if (selectionRange.Areas.Count > 1)
            {
                if (displayAlert)
                    MessageBox.Show("只能是连续单元格");
                return;
            }
            selectionRange.Hide隐藏范围外内容();
        }

        public static void BtnExcel修复()
        {
            if (XlApp.ActiveWindow != null) XlApp.ActiveWindow.Visible = true;
            ETExcelExtensions.SetAppNormalMode(true);

            //设置分辨率
            XlApp.AutoPercentEntry = false;
            XlApp.ReferenceStyle = XlReferenceStyle.xlA1;

            //ThisAddIn.重构菜单All();
        }

        public static void Btn设置倍数行高(CommandBarButton sender, ref bool cancelDefault)
        {
            object input = ETExcelExtensions.GetNumberInput("请输入倍数行高：");
            if (input == null)
                return;

            ETExcelExtensions.SetAppFastMode();

            Range selectionRange = ETExcelExtensions.GetSelectionRange();
            if (selectionRange == null)
                return;

            selectionRange.Set倍数行高(input.ToString());

            ETExcelExtensions.SetAppNormalMode(true);
        }

        public static void Btn设置表格外圈双线(CommandBarButton sender, ref bool cancelDefault)
        { ETExcelExtensions.GetSelectionRange(false).Format设置表格外圈双线(); }

        public static void Btn金额转大写()
        {
            if (!ETExcelExtensions.VerifyCode("确定要金额转中文大写吗?  "))
                return;

            Range selectionRange = ETExcelExtensions.GetSelectionRange();
            if (selectionRange == null)
                return;

            bool bErr = false;
            foreach (Range subRng in selectionRange)
            {
                if (!subRng.IsCellNumber())
                    continue;
                string output = NumberFormater.ToChineseMoney(subRng.Value);
                if (output.IndexOf("非数字", StringComparison.Ordinal) >= 0)
                {
                    if (selectionRange.Count > 1)
                        subRng.Format设置警示色(EnumWarningColor.提醒);
                    bErr = true;
                }
                else
                {
                    subRng.Value = NumberFormater.ToChineseMoney(subRng.Value);
                }
            }

            if (bErr)
                MessageBox.Show(@"存在非数字内容.", @"错误提示");
        }

        public static void Button标签筛选输入工具_Click(CommandBarButton ctrl, ref bool cancelDefault)
        {
            frm设置标签及下拉候选项 frm = new();
            ThisAddIn.OpenForm(frm, XlFormPosition.Right);
        }

        public static void btn工作表管理_Click(CommandBarButton ctrl, ref bool cancelDefault)
        {
            frm工作表管理 frm = new();
            ThisAddIn.OpenForm(frm, XlFormPosition.Center);
        }

        public static void Button批量查找_Click(CommandBarButton ctrl, ref bool cancelDefault)
        {
            frm批量查找 frm = new();
            ThisAddIn.OpenForm(frm);
        }

        //public static void Button横向批量查找_Click(CommandBarButton ctrl, ref bool cancelDefault)
        //{
        //    var frm = new frm批量查找();
        //    ThisAddIn.OpenForm(frm);
        //}

        public static void Button设置下拉可选项_Click(CommandBarButton ctrl, ref bool cancelDefault)
        {
            frm设置标签及下拉候选项 frm = new();

            ThisAddIn.OpenForm(frm, XlFormPosition.Right);
            frm.tabControl1.SelectedTab = frm.tabPage列表方式;

            frm.tabPage标签输入工具_DoubleClick(null, null);
        }

        public static void Button设置隐藏式下拉可选项_Click(CommandBarButton ctrl, ref bool cancelDefault)
        {
            frm设置标签及下拉候选项 frm = new();
            frm.设置隐藏下拉项();
            ThisAddIn.DropdownInputForm.PreEntireColumnAddress = string.Empty;
            frm.Close();
        }

        /// <summary>
        /// 发送单元格内容给助手的事件处理方法
        /// </summary>
        public static void Button发送单元格内容给助手_Click(CommandBarButton ctrl, ref bool cancelDefault)
        {
            try
            {
                // 获取选中的单元格
                Range selection = XlApp.Selection;
                if (selection == null) return;

                // 获取单元格内容
                string content = selection.Text?.ToString() ?? string.Empty;
                if (string.IsNullOrEmpty(content))
                {
                    MessageBox.Show("选中的单元格内容为空", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 在后台发送消息
                Task.Run(async () =>
                {
                    try
                    {
                        // 发送带文本内容的指令消息
                        await Globals.ThisAddIn.CommunicationService.SendCommandWithTextAsync("process_cell_content", content).ConfigureAwait(false);
                    }
                    catch (Exception ex)
                    {
                        // 使用同步上下文在UI线程上显示错误消息
                        _syncContext.Post(_ =>
                        {
                            MessageBox.Show($"发送消息时出错：{ex.Message}", "错误",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }, null);
                    }
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"准备发送消息时出错：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public static void Button文本复制粘贴辅助框_Click(CommandBarButton ctrl, ref bool cancelDefault)
        {
            frm文本复制粘贴辅助框 frm = new();
            frm.Show();
            //ThisAddIn.OpenForm(frm, XlFormPosition.NoControl);
            frm.UpdateSelectedRange(true);
        }

        /// <summary>
        /// 打开程序集追踪器窗体
        /// </summary>
        public static void Button程序集追踪器_Click(CommandBarButton ctrl, ref bool cancelDefault)
        {
            try
            {
                frmAssemblyTracker tracker = new frmAssemblyTracker();
                ThisAddIn.OpenForm(tracker, XlFormPosition.Center);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开程序集追踪器失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        //public static void Button公式转数值_Click(CommandBarButton sender, ref bool cancelDefault)
        //{
        //    Range selectionRange = HHExcelExtensions.HyGetSelectionRange();
        //    selectionRange.HyConvertToNumeric(true);
        //    HHExcelExtensions.SetAppNormalMode(true);
        //}
        #endregion 按键事件
    }
}