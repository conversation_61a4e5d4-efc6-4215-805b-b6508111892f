<?xml version="1.0" encoding="utf-8"?>
<!--光缆网-->
<metas>
    <!-- OBD 分光器 -->
    <ObjMeta objectType="OBD"  autoReload="true" needgeom="true"
             typeActions="add,reset,templates,remove,batchmodify_ftth,batchRelationOldEntityId"
             itemActions="locate,relocate,modify,devicecoveraddress_man,remove,connview,portDiagram,chengduan,portslist,deviceOccupyRate,assetAttribute,filesman,gridElement"
             moreItemActions="invokeLogicDiagram,invokeRouteDiagram">
        <!--端子面板管理-->
        <action type="workspace" name="connview-${id}" id="connview" label="端子图" label-en="Panel Diagram"
                icon="delicious"
                url="connector.html?objecttype=${objectType}&amp;id=${id}" extra="_ui_class:gray" title="端子面板图-${NAME}"
                title-en="Panel Diagram -${NAME}">
        </action>
        <!--批量修改是否FTTH配置-->
        <action name="batchmodify_ftth" method="batchmodify" label="批量修改是否FTTH配置" itemsable="true"
                metaForm='{"fields": [{"name": "IS_FTTHPZ_ID"}]}' options='{"modalClass": "small"}'/>
        <!--批量关联旧实物ID-->
        <action name="batchRelationOldEntityId" label="批量关联旧实物ID" type="script" url="apps/gdo3/device/batchRelationOldEntityId.js"/>
        <!--定位-->
         <action type="script" name="locate">
            <![CDATA[
				try{
                     var obd= _actionContext.params;
                     _bean.locate(obd.objectType, obd.id, true).then(function (shape) {
                        if(shape==''){
                            _bean.find("DEVICE",{devices: obd.id}).then(function(parent){
                                
                                _context.doAction({type: 'obj', name: 'locate'}, {objectType: parent.objectType, id: parent.id});
                            });                        
                        }else{
                            _context.doAction({type: 'obj', name: 'locate'}, {objectType: obd.objectType, id: obd.id});
                        }
                     });
					
				}catch(e){
					window.parent._context.doAction({type: 'obj', name: 'locate'}, {objectType: _actionContext.params.objectType, id: _actionContext.params.id});
					window.parent._sui.minimizeAllMdiWindow();
				}
			]]>
        </action> 
        <!--端口列表-->
        <action type="workspace" class="fullscreen" style="height:600px;" name="portslist-${id}" id="portslist"
                label="端口列表" label-en="Panel Diagram"
                icon="delicious" url="modules/connector/portlist.html?objecttype=${objectType}&amp;id=${id}"
                extra="_ui_class:gray" title="端口列表-${NAME}"
                title-en="Ports List -${NAME}"/>
        <!--OBD模板-->
        <action type="workspace" name="templates" label="模板管理"
                label-en="Template management" title="分光器模板" title-en="OBD Template"
                objectType="OBD.TEMPLATE" icon="columns"/>
        <!--广东的对象修改需要支持重新选择模板-->
        <action name="modify" label="修改" label-en="Modify" type="obj" icon="write" itemsable="true" showTemplates="true"/>
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="parentDevice" label="所属设备"
                       baseParams='{"SPEC_ID":"GB,GJ,GF,ZHX,ODF,IDF","excludeTemplate":"true"}'/>
                <field name="project" label="工程"/>
            </row>
            <row>
                <field name="OLT_CODE" label="所属OLT"/>
                <field name="DEVICE_BIND_PORT" label="所属PON口" dropdownOptions="titleField:ALIAS"/>
            </row>
            <row>
                <field name="XPON_OLT_CODE" label="所属OLT(XPON)"/>
            </row>
            <row>
                <field name="SUPERIOR_PORT" label="所属上级OBD"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="CREATE_DATE" label="入库时间"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" /> <!-- default="80204848"/ modify by pwq@20220510 bug22973 查询时不要带入默认参数 -->
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="OBD_LEVEL" label="OBD等级"/>
                <field name="IS_END_OBD" label="是否末级"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县"/>
                <field name="marketingarea" label="划小营销区"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="region" label="所属区域"/>
            </row>
            <row>
                <field name="IS_HOUSE_SHARE" label="是否户内分光"/>
                <field name="IS_KSKR" label="是否快速扩容"/>
            </row>
            <row>
                <field name="IS_FTTHPZ_ID"/>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
            <row>
                <field name="id" label="实物ID"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            </row>
            <row>
                <field name="IS_FAT" label="是否SUBBOX"/>
                <field name="SUBBOX_LEVEL_ID" label="SUBBOX层级" />
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
              <field name="parentDevice" label="所属设备"
                dropdownOptions="titleField:NAME"
                baseParams='{"SPEC_ID":"GB,GJ,GF,ZHX,ODF,IDF","excludeTemplate":"true"}' required="true" required_city="755"
                searchGridOptions='{"searchTypeChildren":{"DEVICE": [
                  {"objectType": "GB"},{"objectType": "GJ"},{"objectType": "GF"},{"objectType": "ZHX"},{"objectType": "ODF"},{"objectType": "IDF"}
                ]}}'
                       />
                <field name="LIFE_STATE_ID" label="生命周期状态" default="80204848" required="true"/>
            </row>
            <row>
                <field name="upperport" label="上级OBD端口编码" readOnly="true"/>
                <field name="upperobd" label="所属上联OBD" readOnly="true"/>
            </row>
            <row>
                <field name="SPLITIN_ID" label="标称合路数" required="true"/>
                <field name="SPLITOUT_ID" label="标称分路数" required="true"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" required="true" baseParams="searchAddressLevel:&gt;=9,isValid:100383"
                       dropdownOptions="titleField:FULL_NAME"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" default="80204666" required="true" readOnly="true"/>
                <field name="ADSL_TEST_STATUS_ID" label="宽带挂测状态" default="80204954" required="true" readOnly="true"/>
            </row>
            <row>
                <field name="BUILD_MODE_ID" label="建设模式" default="80203898" required="true"/>
                <field name="addressobj.id" label="地址ID" readOnly="true"/>
            </row>
            <row>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
                <field name="DISTRICT" label="所属小区"/>
            </row>
            <row>
                <field name="model" label="型号"/>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
            </row>
            <row>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
                <field name="DEPRECIATION" label="折扣年限(年)"/>
            </row>
            <row>
                <field name="IS_SCENESAME_ID" label="是否与现场一致"/>
                <field name="APPLY_NET_ID" label="网络节点类型"/>
            </row>
            <row>
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="POS_X" label="原X坐标"/>
                <field name="POS_Y" label="原Y坐标"/>
            </row>
            <row>
                <field name="IS_KSKR" label="是否快速扩容" default="80201002" required="true" readOnly="true"/>
                <field name="DATA_SOURCE_ID" label="数据来源" default="80401043"/>
            </row>
            <row>
                <field name="V_CONTRACTOR" queryAssemble="V_CONTRACTOR" label="承包人姓名"/>
                <field name="V_CONTRACTOR_ACC" queryAssemble="V_CONTRACTOR_ACC"  label="承包人账号"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式" default="80204855" required="true"/>
                <field name="IS_END_OBD" label="是否末级OBD" required="true"/>
            </row>
            <row>
                <field name="OBD_LEVEL" label="OBD级别" required="true"/>
                <field name="MIXED_CONFIG_ID" label="OBD是否混放" default="80201001" required="true"/>
            </row>
            <row>
                <field name="IS_INTEGFLAG" label="一体化标志"/>
                <field name="PON_TYPE_ID" label="GPON/EPON标识" readOnly="true" />
            </row>
            <row>
                <field name="SCENE_PARA_ID" label="场景参数" default="80201002"/>
                <field name="MANAGE_MODE_ID" label="管理模式"/>
            </row>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
            </row>
            <row>
                <field name="OLT_CODE" label="所属OLT"/>
                <field name="DEVICE_BIND_PORT" label="所属PON口" dropdownOptions="titleField:ALIAS"/>
            </row>
            <row>
                <field name="XPON_OLT_CODE" label="所属OLT(XPON)"/>
                <field name="DEVICE_RELY_PORT" label="所属XPON口" dropdownOptions="titleField:ALIAS"/>
            </row>
            <row>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
                <field name="IS_PARAMSTATUS" label="OBD-ONU是否可配"/>
            </row>
            <row>
                <field name="BELONG_SPECIALITY_ID" label="所属专业" default="80204675"/>
                <field name="IS_GIGABIT_ID" label="是否千兆" readOnly="true" />
            </row>
            <row>
                <field name="IS_HOUSE_SHARE" label="是否户内分光" default="80201002"/>
                <field name="IS_FAT" label="是否SUBBOX"/>
            </row>
            <row>
                <field name="SUBBOX_LEVEL_ID" label="SUBBOX层级" />
                <field type="empty"/>
            </row>

            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO" required="true"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="asset" label="资产目录" dropdownOptions="titleField:ASSET_CATALOGUE" />
                <field name="PROPERTY_TYPE_ID" label="产权性质" default="80209209" required="true" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" default="80209223" required="true" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间" readOnly="true"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!-- 批量修改表格 -->
        <form name="batchmodify">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME" readOnly="true"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" required="true" readOnly="true"/>
                <field name="NAME" required="true" readOnly="true"/>
            </row>
            <row>
              <field name="parentDevice" label="所属设备"
                dropdownOptions="titleField:NAME"
                baseParams='{"SPEC_ID":"GB,GJ,GF,ZHX,ODF,IDF","excludeTemplate":"true"}' required="true" required_city="755"
                searchGridOptions='{"searchTypeChildren":{"DEVICE": [
                  {"objectType": "GB"},{"objectType": "GJ"},{"objectType": "GF"},{"objectType": "ZHX"},{"objectType": "ODF"},{"objectType": "IDF"}
                ]}}'
                />
                <field name="LIFE_STATE_ID" label="生命周期状态" default="80204848" required="true" readOnly="true"/>
            </row>
            <row>
                <field name="upperport" label="上级OBD端口编码" readOnly="true"/>
                <field name="upperobd" label="所属上联OBD" readOnly="true"/>
            </row>
            <row>
                <field name="SPLITIN_ID" label="标称合路数" required="true" readOnly="true"/>
                <field name="SPLITOUT_ID" label="标称分路数" required="true" readOnly="true"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" required="true" baseParams="{&quot;_solr&quot;:true}"
                       dropdownOptions="titleField:FULL_NAME"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" default="80204666" required="true" readOnly="true"/>
                <field name="ADSL_TEST_STATUS_ID" label="宽带挂测状态" default="80204954" required="true" readOnly="true"/>
            </row>
            <row>
                <field name="BUILD_MODE_ID" label="建设模式" default="80203898" required="true"/>
                <field name="ADDRESS_ID" label="地址ID" readOnly="true"/>
            </row>
            <row>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
                <field name="DISTRICT" label="所属小区"/>
            </row>
            <row>
                <field name="model" label="型号"/>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
            </row>
            <row>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
                <field name="DEPRECIATION" label="折扣年限(年)"/>
            </row>
            <row>
                <field name="IS_SCENESAME_ID" label="是否与现场一致"/>
                <field name="APPLY_NET_ID" label="网络节点类型"/>
            </row>
            <row>
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="POS_X" label="原X坐标" readOnly="true"/>
                <field name="POS_Y" label="原Y坐标" readOnly="true"/>
            </row>
            <row>
                <field name="IS_KSKR" label="是否快速扩容" default="80201002" required="true" readOnly="true"/>
                <field name="DATA_SOURCE_ID" label="数据来源" default="80401043"/>
            </row>
            <row>
                <field name="V_CONTRACTOR" queryAssemble="V_CONTRACTOR" label="承包人姓名"/>
                <field name="V_CONTRACTOR_ACC" queryAssemble="V_CONTRACTOR_ACC" label="承包人账号"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式" default="80204855" required="true"/>
                <field name="IS_END_OBD" label="是否末级OBD" required="true"/>
            </row>
            <row>
                <field name="OBD_LEVEL" label="OBD级别" required="true"/>
                <field name="MIXED_CONFIG_ID" label="OBD是否混放" default="80201001" required="true"/>
            </row>
            <row>
                <field name="IS_INTEGFLAG" label="一体化标志"/>
                <field name="PON_TYPE_ID" label="GPON/EPON标识" readOnly="true" />
            </row>
            <row>
                <field name="SCENE_PARA_ID" label="场景参数" default="80201002"/>
                <field name="MANAGE_MODE_ID" label="管理模式"/>
            </row>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
            </row>
            <row>
                <field name="OLT_CODE" label="所属OLT"/>
                <field name="DEVICE_BIND_PORT" label="所属PON口" dropdownOptions="titleField:ALIAS"/>
            </row>
            <row>
                <field name="XPON_OLT_CODE" label="所属OLT(XPON)"/>
                <field name="DEVICE_RELY_PORT" label="所属XPON口"/>
            </row>
            <row>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
                <field name="IS_PARAMSTATUS" label="OBD-ONU是否可配"/>
            </row>
            <row>
                <field name="BELONG_SPECIALITY_ID" label="所属专业" default="80204675"/>
                <field name="IS_GIGABIT_ID" label="是否千兆" readOnly="true" />
            </row>
            <row>
                <field name="IS_HOUSE_SHARE" label="是否户内分光" default="80201002"/>
                <field name="IS_FAT" label="是否SUBBOX"/>
            </row>
            <row>
                <field name="SUBBOX_LEVEL_ID" label="SUBBOX层级" />
                <field type="empty"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO" required="true"/>
                <field name="project_name" label="工程名称" readOnly="true"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号" readOnly="true"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="asset" label="资产目录" dropdownOptions="titleField:ASSET_CATALOGUE" />
                <field name="PROPERTY_TYPE_ID" label="产权性质" default="80209209" required="true" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" default="80209223" required="true" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间" readOnly="true"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="addressobj" label="标准地址" getLabel="row.addressobj_value.FULL_NAME"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:6000px;" autoLoad="false" baseParams='{"_orderBy":"CODE"}'>
            <field name="CODE" label="分光器编码"/>
            <field name="NAME" label="分光器名称" width="250px"/>
            <field name="site" label="局站" getLabel="row.site_value.NAME"/>
            <field name="room_outdooraddress" label="所属机房" getLabel="row.room_outdooraddress_value.NAME"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="IS_FTTHPZ_ID" label="是否FTTH配置"/>
            <field name="OLT_CODE" label="所属OLT"/>
            <field name="BELONG_OLT_value.NAME" label="所属OLT名称"/>
            <field name="DEVICE_BIND_PORT" label="所属PON口" getLabel="row.DEVICE_BIND_PORT_value.ALIAS" excelCellType="text"/>
                <field name="XPON_OLT_CODE" label="所属OLT(XPON)" excelCellType="text"/>
            <field name="DEVICE_RELY_PORT" label="所属XPON口" getLabel="row.DEVICE_RELY_PORT_value.ALIAS" excelCellType="text"/>
            <field name="upperobd_value.CODE" label="上级OBD编码"/>
            <field name="upperobd_value.NAME" label="上级OBD名称" width="250px"/>
            <field name="upperport" label="上级OBD端口" excelCellType="text"/>
            <field name="terminal_value.PORTS_TOTAL" label="端口总数"/>
            <field name="terminal_value.PORTS_USED" label="端子占用数"/>
            <field name="terminal_value.PORTS_FREE" label="端子空闲数"/>
            <field name="terminal_value.PORTS_CONNECTED" label="已成端端子数" width="100px"/>
            <field name="terminal_value.PORTS_BAD" label="坏端子数"/>
            <field name="terminal_value.PORTS_USEDRATE" label="端子占用率(%)" width="100px"/>
            <field name="parentDevice" label="所属光交设备"/>
            <field name="IS_HOUSE_SHARE" label="是否户内分光"/>
            <field name="IS_KSKR" label="是否快速扩容"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="addressobj" label="标准地址" getLabel="row.addressobj_value.FULL_NAME" width="300px"/>
            <field name="SPLITIN_ID" label="标称合路数"/>
            <field name="SPLITOUT_ID" label="标称分路数"/>
            <field name="OWNER_NET_ID" label="网络类型"/>
            <field name="BUILD_MODE_ID" label="建设模式"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="NOTES" label="备注"/>
            <field name="INSTALL_WAY_ID" label="安装方式"/>
            <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识" width="150px"/>
            <field name="ACCESS_MODE_ID" label="接入方式"/>
            <field name="IS_INTEGFLAG" label="一体化标志"/>
            <field name="PON_TYPE_ID" label="GPON/EPON标识"/>
            <field name="SCENE_PARA_ID" label="场景参数" getLabel="row.SCENE_PARA_ID_value.NAME"/>
            <field name="MANAGE_MODE_ID" label="管理模式" getLabel="row.MANAGE_MODE_ID_value.NAME"/>
            <field name="IS_END_OBD" label="是否末级OBD"/>
            <field name="OBD_LEVEL" label="OBD级别"/>
            <field name="MIXED_CONFIG_ID" label="OBD是否混放"/>
            <field name="IS_PARAMSTATUS" label="OBD-ONU是否可配"/>
            <field name="IS_FAT" label="是否SUBBOX"/>
            <field name="SUBBOX_LEVEL_ID" label="SUBBOX层级" />
            <field name="CREATOR" label="录入人"/>
            <field name="MODIFIER" label="修改人"/>
            <field name="AUDIT_PERSON" label="验收人"/>
            <field name="AUDIT_DATE" label="验收时间"/>
            <field name="PROPERTY_TYPE_ID" label="产权性质"/>
            <field name="PROPERTY_OWNER_ID" label="产权归属"/>
            <field name="MNT_LEVEL_ID" label="维护等级"/>
            <field name="MNT_PERSON" label="维护人"/>
            <field name="MNT_DEPT" label="维护单位"/>
            <field name="DISTRICT" label="所属小区"/>
            <field name="project_value.CODE" label="所属工程"/>
            <field name="project_value.NAME" label="工程编码"/>
            <field name="project_serialno" label="工程流水号" getLabel="row.project_value.SERIALNO"/>
            <field name="CREATE_DATE" label="入库时间"/>
            <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            <field name="NETWORK_LAYER_ID" label="网络层次"/>
            <field name="vendor" label="生产厂家"/>
            <field name="SPECIFICATIONS" label="规格型号"/>
            <field name="IS_FTTHPZ_ID" label="是否FTTH配置"/>
            <field name="id" label="实物ID"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="ADDRESS_ID" label="地址ID"/>
        </grid>
        <!--端口列表-->
        <grid name="portlist">
            <field name="portCode" label="OBD端口" orderBy="false" width="70px" excelCellType="text"/>
            <field name="UP_DOWN_ID" label="上下联" orderBy="false" width="60px"/>
            <field name="portPhyStatic" label="端子物理状态" orderBy="true" width="90px"/><!--port-->
            <field name="portBussStatic" label="端子业务状态" orderBy="true" width="90px"/><!--port-->
            <field name="accessCode" label="接入号" orderBy="false" /><!--service -->
            <field name="customerInfo" label="用户信息" orderBy="false" /><!--customer -->
            <field name="customerAddress" label="装机地址" orderBy="false" /><!--customer -->
            <field name="ONUName" label="ONU名称" orderBy="false" /><!--onu -->
            <field name="ONUCode" label="ONU编码" orderBy="false" /><!--onu -->
            <field name="opticCode" label="光路编码" orderBy="false" /><!--optic -->
            <field name="opticName" label="光路名称" orderBy="false" /><!--optic -->
            <field name="deviceModel" label="设备型号" orderBy="false" /><!--obd -->
            <field name="deviceType" label="设备类型" orderBy="false" /><!--obd -->
            <field name="belongRoom" label="所属机房" orderBy="false" /><!--obd -->
            <field name="accessType" label="接入方式" orderBy="false" /><!--obd -->
            <field name="isTYM" label="是否天翼猫" orderBy="false" width="80px"/> <!--site -->
            <field name="isIMS" label="是否IMS" orderBy="false" width="70px"/>
            <field name="managementIp" label="网管IP" orderBy="false" /><!--obd -->
            <field name="MACAddress" label="MAC地址" orderBy="false" /><!--obd -->
            <field name="LOID" label="LOID" orderBy="false" /><!--ONU -->
            <field name="scenePara" label="OBD场景参数" orderBy="false" /> <!--obd -->
            <field name="isVoiceOnly" label="是否语音独占" orderBy="false" width="90px"/>
            <field name="isAccessRes" label="是否有窄带资源" orderBy="false" width="100px"/><!--site -->
            <field name="BASEIP" label="BAS服务器" orderBy="false" /><!--obd -->
            <field name="OPTIC_CODE_OLD" label="光路旧编码" orderBy="false"/><!--onu-->
            <field name="OLDOPTICCODE" label="旧光路编码" orderBy="false"/><!--onu-->
            <field name="svlan" label="SVLAN" orderBy="false" />
            <field name="eid" label="EID" orderBy="false" />
            <field name="voiceIp" label="语音IP" orderBy="false" />
            <field name="serviceNo" label="业务代号" orderBy="false" /><!--optic -->
            <field name="busiOrderCode" label="调单号" orderBy="false" /><!--optic -->
            <field name="areaOpticName" label="本地网光路名称" orderBy="false" /><!--optic -->
            <field name="barCode" label="条形码" orderBy="false" /><!--optic -->
            <field name="obd2Code" label="二级OBD编码" orderBy="false" />
            <field name="isHouseShare" label="是否户内分光" orderBy="false" /><!--obd -->
            <field name="linkNotes" label="光路备注" orderBy="false" /><!--optic -->
            <field name="manageMode" label="OBD管理模式" orderBy="false" /><!--obd -->
            <field name="MODIFY_DATE" label="更新时间" orderBy="false" /><!--obd -->
        </grid>
        <grid name="changeobdsplitout" rowDetailField="false" autoLoad="false" showRowNum="false" typeActions="" extraActions="">
            <field name="CODE" label="编号"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站编码"/>
            <field name="sitename" label="局站名称" getLabel="row.site_value.NAME"/>
            <field name="terminal_value.PORTS_TOTAL" label="端口总数"/>
            <field name="terminal_value.PORTS_USED" label="端子占用数"/>
            <field name="SPLITOUT_ID" label="标称分路数"/>
        </grid>

        <!-- 查看OBD端口 -->
        <grid name="portlist1" autoLoad="false">
            <field name="CODE" label="分光器编码"/>
            <field name="NAME" label="分光器名称"/>
            <field name="site" label="所属局站" />
            <field name="room" label="所属机房" />
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="OLT_CODE" label="所属OLT"/>
            <field name="DEVICE_BIND_PORT" label="所属PON口" getLabel="row.DEVICE_BIND_PORT_value.ALIAS"/>
            <field name="XPON_OLT_CODE" label="所属OLT(XPON)"/>
            <field name="SUPERIOR_CODE" label="上级OBD编码"/>
            <field name="upperport" label="上级OBD端口编码" />
        </grid>

        <grid name="portlist2" rowDetailField="false">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
        </grid>

        <grid name="portlist3" rowDetailField="false">
            <field name="dname" label="OBD名称"/>
            <field name="dcode" label="OBD编码"/>
            <field name="obdportcode" label="OBD端口"/>
            <field name="updown" label="上下联" />
            <field name="physicalstatus" label="端子物理状态" />
            <field name="servicestatus" label="端子业务状态" />
            <field name="onuname" label="ONU名称" />
            <field name="onucode" label="ONU编码" />
            <field name="opticcode" label="光路编码" />
            <field name="opticname" label="光路名称" />
            <field name="access_code" label="接入号" />
            <field name="serviceno" label="业务代号" />
            <field name="devicemodel" label="设备型号" />
            <field name="devicetype" label="设备类型" />
            <field name="facilityname" label="所属机房" />
            <field name="address_name" label="安装地址" />
            <field name="accessmodel" label="接入方式" />
            <field name="istym" label="是否天翼猫" />
            <field name="isims" label="是否IMS" />
            <field name="nmip" label="网管IP" />
            <field name="macaddress" label="MAC地址" />
            <field name="loid" label="LOID" />
            <field name="scenepara" label="场景参数" />
            <field name="isvoiceonly" label="是否语音独占" />
            <field name="isaccessres" label="是否有窄带资源" />
            <field name="bas" label="BAS服务器" />
            <field name="svlan" label="SVLAN" />
            <field name="eid" label="EID" />
            <field name="voiceip" label="语音IP" />
            <field name="opticoldcode" label="光路旧编码" />
            <field name="oldopticcode" label="旧光路编码" />
            <field name="busiordercode" label="调单号" />
            <field name="reaopticname" label="本地网光路名称" />
        </grid>

        <grid name="FTTX10OBD" filterable="false" sortable="false">
            <field name="parentCode" label="父设施编码" getLabel="row.parentDevice_value ? row.parentDevice_value.CODE : ''"/>
            <field name="CODE" label="光设施编码"/>
            <field name="NAME" label="光设施名称"/>
            <field name="optDivide" label="OBD分光比" getLabel="row.SPLITIN_ID ? row.SPLITOUT_ID ? (row.SPLITIN_ID + ':' + row.SPLITOUT_ID) : '' : ''"/>
        </grid>
    </ObjMeta>
    <!-- OBD模板 -->
    <ObjMeta objectType="OBD.TEMPLATE" autoReload="true" labelField="NAME"
             typeActions="add,remove"
             itemActions="modify,remove,connview,filesman">
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="模板名称" required="true"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="名称" required="true"/>
                <field name="BUILD_MODE_ID" label="建设模式"/>
            </row>
            <row>
                <field name="SPLITIN_ID" label="标称合路数" required="true"/>
                <field name="SPLITOUT_ID" label="标称分路数" required="true"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="region" label="所属区域"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="IS_INTEGFLAG" label="一体化标志"/>
            </row>
            <row>
                <field name="SCENE_PARA_ID" label="场景参数" default="80201002"/>
                <field name="MANAGE_MODE_ID" label="管理模式"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
                <field name="IS_GIGABIT_ID" label="是否千兆"/>
            </row>
            <row>
                <field name="IS_FAT" label="是否SUBBOX" />
                <field type="empty"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" default="80209209" required="true" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" default="80209223" required="true" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:1390;" autoLoad="false">
            <field name="NAME" label="模板名称" width="250px"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
    </ObjMeta>

    <!-- ODF 光配线架 -->
    <ObjMeta objectType="ODF" needgom="false" autoReload="true"
             typeActions="add,reset,templates,remove"
             itemActions="locate,modify,remove,connview,portDiagram,filesman,assetAttribute,relateOCablesection"
             moreItemActions="attachdevice_man,chengduan,fuseview,resRelationCables,biaoqiandayin,deviceOpticalcircuitLabel,siteFiberLabel
             ,showRelateODevice,relateFiberGroup,invokeLogicDiagram,invokeRouteDiagram">
        <action type="workspace" name="connview-${id}" id="connview" label="端子图" label-en="Panel Diagram"
                icon="delicious"
                url="connector.html?objecttype=${objectType}&amp;id=${id}" extra="_ui_class:gray" title="端子面板图-${NAME}"
                title-en="Panel Diagram -${NAME}">
        </action>
        <action type="workspace" name="templates" label="模板管理" label-en="Template management" title="光配线架模板"
                title-en="ODF Template"
                objectType="ODF.TEMPLATE" icon="columns"/>
        <!--广东的对象修改需要支持重新选择模板-->
        <action name="modify" label="修改" label-en="Modify" type="obj" icon="write" itemsable="true" showTemplates="true"/>
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true"/>
            </row>
            <row>
                <field name="region" label="所属区域"/>
                <field name="smallcounty" label="划小区县"/>
            </row>
            <row>
                <field name="marketingarea" label="划小营销区"/>
                <field name="CREATOR" label="录入人"/>
            </row>
            <row>
                <field name="project" label="工程"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="入库时间"/>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form GEN_CODE_TEMPLATE="${site.CODE}/ODF${sn3}" GEN_NAME_TEMPLATE="${site.NAME}/ODF${sn3}" >
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" required="true" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="CAPACITY" label="标称容量" required="true"/>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="ADDRESS_ID" label="地址ID" readOnly="true"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="ROW_NO" label="行号" required="true"/>
                <field name="COL_NO" label="列号" required="true"/>
            </row>
            <row>
                <field name="LENGTH" label="长度(米)" required="true"/>
                <field name="WIDTH" label="宽度(米)" required="true"/>
            </row>
            <row>
                <field name="HEIGHT" label="高度(米)"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" readOnly="true" default="80204666"/>
                <field name="addressobj" label="标准地址" required="true" baseParams="searchAddressLevel:&gt;=9,isValid:100383"
                       dropdownOptions="titleField:FULL_NAME"/>
            </row>
            <row>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
                <field name="DEPRECIATION" label="折扣年限(年)"/>
            </row>
            <row>
                <field name="IS_SCENESAME_ID" label="是否与现场一致"/>
                <field name="APPLY_NET_ID" label="网络节点类型"/>
            </row>
            <row>
                <field name="model" label="型号" required="true"/>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="ASSIGNED_TOTAL" label="满配OBD数"/>
                <field name="ASSIGNED_USED" label="已配OBD数" readOnly="true"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="DISTRICT" label="所属小区"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="DATA_SOURCE_ID" label="数据来源" default="80401043"/>
                <field name="IS_MODF" label="是否MODF" required="true"/>
            </row>
            <row>
                <field name="V_CONTRACTOR" queryAssemble="V_CONTRACTOR" label="承包人姓名"/>
                <field name="V_CONTRACTOR_ACC" queryAssemble="V_CONTRACTOR_ACC" label="承包人账号"/>
            </row>
            <row>
                <field name="V_STICKERLABEL" queryAssemble="V_STICKERLABEL" label="扫描结果"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="IS_INTEGFLAG" label="一体化标志"/>
                <field name="DEVICE_TYPE_ID" label="机架类型"/>
            </row>
            <row>
                <field name="ADAPTTYPE_ID" label="适配类型"/>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
            </row>
            <row>
                <field name="POS_X" label="机房内X坐标" readOnly="true"/>
                <field name="POS_Y" label="机房内Y坐标" readOnly="true"/>
            </row>
            <row>
                <field name="BELONG_SPECIALITY_ID" label="所属专业" readOnly="true" default="80204674"/>
            </row>
            <row>
                <field name="bestroom" label="最优机房"/>
                <field name="bestobd" label="最优OBD"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO" required="true"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" multiple="true" default="80209209"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" multiple="true" default="80209223"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
                <field name="MNT_DEPT" label="维护单位"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>

        <!-- 批量修改表格 -->
        <form name="batchmodify" GEN_CODE_TEMPLATE="${room_outdooraddress.CODE}/ODF${sn3}" GEN_NAME_TEMPLATE="${room_outdooraddress.NAME}/ODF${sn3}" >
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME" readOnly="true"/>
                <field name="room_outdooraddress" label="机房/安装点" required="true" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" required="true" readOnly="true"/>
                <field name="NAME" required="true" readOnly="true"/>
            </row>
            <row>
                <field name="CAPACITY" label="标称容量" required="true"/>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="ADDRESS_ID" label="地址ID" readOnly="true"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848" readOnly="true"/>
            </row>
            <row>
                <field name="ROW_NO" label="行号" required="true"/>
                <field name="COL_NO" label="列号" required="true"/>
            </row>
            <row>
                <field name="LENGTH" label="长度(米)" required="true"/>
                <field name="WIDTH" label="宽度(米)" required="true"/>
            </row>
            <row>
                <field name="HEIGHT" label="高度(米)"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" readOnly="true" default="80204666"/>
                <field name="addressobj" label="标准地址" required="true" baseParams="{&quot;_solr&quot;:true}"
                       dropdownOptions="titleField:FULL_NAME"/>
            </row>
            <row>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
                <field name="DEPRECIATION" label="折扣年限(年)"/>
            </row>
            <row>
                <field name="IS_SCENESAME_ID" label="是否与现场一致"/>
                <field name="APPLY_NET_ID" label="网络节点类型"/>
            </row>
            <row>
                <field name="model" label="型号"/>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="ASSIGNED_TOTAL" label="满配OBD数"/>
                <field name="ASSIGNED_USED" label="已配OBD数" readOnly="true"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="DISTRICT" label="所属小区"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="DATA_SOURCE_ID" label="数据来源" default="80401043"/>
                <field name="IS_MODF" label="是否MODF" required="true"/>
            </row>
            <row>
                <field name="V_CONTRACTOR" queryAssemble="V_CONTRACTOR" label="承包人姓名"/>
                <field name="V_CONTRACTOR_ACC" queryAssemble="V_CONTRACTOR_ACC" label="承包人账号"/>
            </row>
            <row>
                <field name="V_STICKERLABEL" queryAssemble="V_STICKERLABEL" label="扫描结果" readOnly="true"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="IS_INTEGFLAG" label="一体化标志"/>
                <field name="DEVICE_TYPE_ID" label="机架类型"/>
            </row>
            <row>
                <field name="ADAPTTYPE_ID" label="适配类型"/>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
            </row>
            <row>
                <field name="POS_X" label="机房内X坐标" readOnly="true"/>
                <field name="POS_Y" label="机房内Y坐标" readOnly="true"/>
            </row>
            <row>
                <field name="BELONG_SPECIALITY_ID" label="所属专业" readOnly="true" default="80204674"/>
            </row>
            <row>
                <field name="bestroom" label="最优机房"/>
                <field name="bestobd" label="最优OBD"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称" readOnly="true"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号" readOnly="true"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" multiple="true" default="80209209"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" multiple="true" default="80209223"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
                <field name="MNT_DEPT" label="维护单位"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="addressobj" label="标准地址" getLabel="row.addressobj_value.FULL_NAME"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:6000px;" autoLoad="false" baseParams='{"_orderBy":"CODE"}'>
            <field name="CODE" label="光配线架编码"/>
            <field name="NAME" label="光配线架名称" width="250px"/>
            <field name="ROW_NO" label="行号"/>
            <field name="COL_NO" label="列号"/>
            <field name="V_STICKERLABEL" label="扫描结果"/>
            <field name="site" label="所属局站" getLabel="row.site_value?row.site_value.NAME:''"/>
            <field name="room_outdooraddress" label="所属机房" getLabel="row.room_outdooraddress_value.NAME"/>
            <field name="terminal_value.PORTS_TOTAL" label="端口总数"/>
            <field name="terminal_value.PORTS_USED" label="端子占用数"/>
            <field name="terminal_value.PORTS_FREE" label="端子空闲数"/>
            <field name="terminal_value.PORTS_CONNECTED" label="已成端端子数" width="100px"/>
            <field name="terminal_value.PORTS_BAD" label="坏端子数"/>
            <field name="terminal_value.PORTS_USEDRATE" label="端子占用率(%)" width="100px"/>
            <field name="addressobj" label="标准地址" getLabel="row.addressobj_value.FULL_NAME" width="300px"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="OWNER_NET_ID" label="网络类型"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="CREATE_DATE" label="录入时间"/>
            <field name="CAPACITY" label="标称容量"/>
            <field name="NOTES" label="备注"/>
            <field name="IS_MODF" label="是否MODF"/>
            <field name="CONFIG_TYPE_ID" label="配置点类型"/>
            <field name="INSTALL_WAY_ID" label="安装方式"/>
            <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识" width="150px"/>
            <field name="ASSET_CODE" label="资产编码"/>
            <field name="CREATOR" label="录入人"/>
            <field name="MODIFIER" label="修改人"/>
            <field name="AUDIT_PERSON" label="验收人"/>
            <field name="PROPERTY_TYPE_ID" label="产权性质"/>
            <field name="PROPERTY_OWNER_ID" label="产权归属"/>
            <field name="MNT_LEVEL_ID" label="维护等级"/>
            <field name="MNT_PERSON" label="维护人"/>
            <field name="MNT_DEPT" label="维护单位"/>
            <field name="COLLECTION_DEPT" label="数据采集单位" width="100px"/>
            <field name="COLLECTION_PERSON" label="数据采集人"/>
            <field name="LENGTH" label="长度(米)"/>
            <field name="WIDTH" label="宽度(米)"/>
            <field name="HEIGHT" label="高度(米)"/>
            <field name="id" label="实物ID"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="ADDRESS_ID" label="地址ID"/>
            <field name="POS_X" label="原X坐标"/>
            <field name="POS_Y" label="原Y坐标"/>
            <field name="LONGITUDE" label="经度"/>
            <field name="LATITUDE" label="纬度"/>
        </grid>
        <grid name="cdOdfList">
            <field name="CODE" label="光配线架编码"/>
            <field name="NAME" label="光配线架名称" width="250px"/>
            <field name="room_outdooraddress_name" label="机房名称" getLabel="row.room_outdooraddress_value.NAME"/>
            <field name="room_outdooraddress" label="机房编码" />
        </grid>
    </ObjMeta>
    <!-- ODF模板 -->
    <ObjMeta objectType="ODF.TEMPLATE" autoReload="true" labelField="NAME"
             typeActions="add,remove"
             itemActions="modify,remove,fuseview,connview,filesman">
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="模板名称" required="true"/>
            </row>
        </form>
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="CAPACITY" label="标称容量" required="true"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
                <field name="LENGTH" label="长度(米)"/>
            </row>
            <row>
                <field name="WIDTH" label="宽度(米)"/>
                <field name="HEIGHT" label="高度(米)"/>
            </row>
            <row>
                <field name="region" label="所属区域"/>
                <field name="ASSIGNED_TOTAL" label="满配OBD数"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="IS_INTEGFLAG" label="一体化标志"/>
                <field name="DEVICE_TYPE_ID" label="机架类型"/>
            </row>
            <row>
                <field name="ADAPTTYPE_ID" label="适配类型"/>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" default="80209209" required="true" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" default="80209223" required="true" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
                <field name="MNT_DEPT" label="维护单位"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:1390;" autoLoad="false">
            <field name="NAME" label="模板名称" width="250px"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
    </ObjMeta>

    <!-- GJ 光交接箱 -->
    <ObjMeta objectType="GJ" autoReload="true" needgeom="true"
             typeActions="add,reset,templates,remove"
             itemActions="locate,relocate,modify,remove,connview,portDiagram,filesman,assetAttribute,relateOCablesection,relateFiberGroup"
             moreItemActions="attachdevice_man,chengduan,fuseview,resRelationCables,gridElement,deviceOccupyRate,biaoqiandayin,deviceOpticalcircuitLabel,siteFiberLabel,showRelateODevice,invokeLogicDiagram,invokeRouteDiagram">
        <action type="workspace" name="templates" label="模板管理" label-en="Template management" title="光交接箱模板"
                title-en="GJ template" objectType="GJ.TEMPLATE" icon="columns"/>
        <action type="workspace" name="connview-${id}" id="connview" label="端子图" label-en="Panel Diagram"
                icon="delicious"
                url="connector.html?objecttype=${objectType}&amp;id=${id}" extra="_ui_class:gray" title="端子面板图-${NAME}"
                title-en="Panel Diagram -${NAME}">
        </action>
        <action type="workspace" id="connectSection" label="关联光缆" title="${NAME}-关联光缆段" objectType="OCABLESECTION"
                options='{"baseParams": {"A_DEVICE_ID": "${id}","Z_DEVICE_ID":"${id}","_opgroup":"or(A_DEVICE_ID,Z_DEVICE_ID)"}}'/>
        <action type="script" name="listRelationGF" label="关联光分纤盒" url="modules/odevice/js/gjListRelationGF.js"/>
        <action name="clonedev" type="script" label="复制设备" url="apps/pipe/device/js/clonedev.js"/>
        <!--广东的对象修改需要支持重新选择模板-->
        <action name="modify" label="修改" label-en="Modify" type="obj" icon="write" itemsable="true" showTemplates="true"/>
        <script>
            <![CDATA[

            om.descriptionField = function(o) {
              var div = $('<div>');
              var d1 = $('<span>').css({padding: '5px'}).appendTo(div).text('一级OBD数量: '), d2 = $('<span>').css({padding: '5px'}).appendTo(div).text('二级OBD数量: ');
              _util.loadingRender(_bean.count('OBD', {parentDevice: o.id, OBD_LEVEL: 80204944}), d1);
              _util.loadingRender(_bean.count('OBD', {parentDevice: o.id, OBD_LEVEL: 80204945}), d2);
              return div;
            };
          ]]>
        </script>
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="region" label="所属区域"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县"/>
                <field name="marketingarea" label="划小营销区"/>
            </row>
            <row>
                <field name="project" label="工程"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="入库时间"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增面板-->
        <form name="add" GEN_CODE_TEMPLATE="${site.CODE}/GJ${sn3}" GEN_NAME_TEMPLATE="${site.NAME}/GJ${sn3}">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
                <field name="ADDRESS_ID" label="地址ID" readOnly="true"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" required="true" baseParams="searchAddressLevel:&gt;=9,isValid:100383"
                       dropdownOptions="titleField:FULL_NAME"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" default="80204666"/>
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="model" label="型号" required="true"/>
                <field name="DISTRICT" label="所属小区"/>
            </row>
            <row>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
                <field name="DEPRECIATION" label="折扣年限(年)"/>
            </row>
            <row>
                <field name="IS_SCENESAME_ID" label="是否与现场一致"/>
                <field name="APPLY_NET_ID" label="网络节点类型"/>
            </row>
            <row>
                <field name="ASSIGNED_TOTAL" label="满配OBD数"/>
                <field name="ASSIGNED_USED" label="已配OBD数" readOnly="true"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="POS_X" label="原X坐标" readOnly="true"/>
                <field name="POS_Y" label="原Y坐标" readOnly="true"/>
            </row>
            <row>
                <field name="DATA_SOURCE_ID" label="数据来源" default="80401043"/>
                <field name="IS_GREEN_POSITION" label="位置准确绿标"/>
            </row>
            <row>
                <field name="V_CONTRACTOR" queryAssemble="V_CONTRACTOR" label="承包人姓名"/>
                <field name="V_CONTRACTOR_ACC" queryAssemble="V_CONTRACTOR_ACC" label="承包人ACC帐号"/>
            </row>
            <row>
                <field name="" label="扫描结果" readOnly="true"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="CAPACITY" label="标称容量" required="true"/>
            </row>
            <row>
                <field name="CONFIG_TYPE_ID" label="配置点类型" default="80203622"/>
                <field name="IS_INTEGFLAG" label="一体化标志"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="ADAPTTYPE_ID" label="适配类型"/>
            </row>
            <row>
                <field name="bestroom" label="最优机房"/>
                <field name="bestobd" label="最优OBD"/>
            </row>
            <row>
                <field name="BELONG_SPECIALITY_ID" label="所属专业" readOnly="true" default="80204675"/>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO" required="true"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!--修改面板-->
        <form name="modify" GEN_CODE_TEMPLATE="${site.CODE}/GJ${sn3}" GEN_NAME_TEMPLATE="${site.NAME}/GJ${sn3}">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
                <field name="ADDRESS_ID" label="地址ID" readOnly="true"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" required="true" baseParams="{&quot;_solr&quot;:true}"
                       dropdownOptions="titleField:FULL_NAME"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" readOnly="true" required="true" />
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="model" label="型号" required="true"/>
                <field name="DISTRICT" label="所属小区"/>
            </row>
            <row>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
                <field name="DEPRECIATION" label="折扣年限(年)"/>
            </row>
            <row>
                <field name="IS_SCENESAME_ID" label="是否与现场一致"/>
                <field name="APPLY_NET_ID" label="网络节点类型"/>
            </row>
            <row>
                <field name="ASSIGNED_TOTAL" label="满配OBD数"/>
                <field name="ASSIGNED_USED" label="已配OBD数" readOnly="true"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="POS_X" label="原X坐标" readOnly="true"/>
                <field name="POS_Y" label="原Y坐标" readOnly="true"/>
            </row>
            <row>
                <field name="DATA_SOURCE_ID" label="数据来源" />
                <field name="IS_GREEN_POSITION" label="位置准确绿标"/>
            </row>
            <row>
                <field name="V_CONTRACTOR" queryAssemble="V_CONTRACTOR" label="承包人姓名"/>
                <field name="V_CONTRACTOR_ACC" queryAssemble="V_CONTRACTOR_ACC" label="承包人ACC账号"/>
            </row>
            <row>
                <field name="V_STICKERLABEL" queryAssemble="V_STICKERLABEL" label="扫描结果" readOnly="true"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="CAPACITY" label="标称容量" required="true"/>
            </row>
            <row>
                <field name="CONFIG_TYPE_ID" label="配置点类型" default="80203622"/>
                <field name="IS_INTEGFLAG" label="一体化标志"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="ADAPTTYPE_ID" label="适配类型"/>
            </row>
            <row>
                <field name="bestroom" label="最优机房"/>
                <field name="bestobd" label="最优OBD"/>
            </row>
            <row>
                <field name="BELONG_SPECIALITY_ID" label="所属专业" readOnly="true" default="80204675"/>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO" required="true"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" />
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!-- 批量修改表格 -->
        <form name="batchmodify">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME" readOnly="true"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" required="true" readOnly="true"/>
                <field name="NAME" required="true" readOnly="true"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848" readOnly="true"/>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
                <field name="ADDRESS_ID" label="地址ID" readOnly="true"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" required="true" baseParams="{&quot;_solr&quot;:true}"
                       dropdownOptions="titleField:FULL_NAME"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" readOnly="true" required="true" />
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="model" label="型号"/>
                <field name="DISTRICT" label="所属小区"/>
            </row>
            <row>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
                <field name="DEPRECIATION" label="折扣年限(年)"/>
            </row>
            <row>
                <field name="IS_SCENESAME_ID" label="是否与现场一致"/>
                <field name="APPLY_NET_ID" label="网络节点类型"/>
            </row>
            <row>
                <field name="ASSIGNED_TOTAL" label="满配OBD数"/>
                <field name="ASSIGNED_USED" label="已配OBD数" readOnly="true"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="POS_X" label="原X坐标" readOnly="true"/>
                <field name="POS_Y" label="原Y坐标" readOnly="true"/>
            </row>
            <row>
                <field name="DATA_SOURCE_ID" label="数据来源" />
                <field name="IS_GREEN_POSITION" label="位置准确绿标"/>
            </row>
            <row>
                <field name="V_CONTRACTOR" queryAssemble="V_CONTRACTOR" label="承包人姓名"/>
                <field name="V_CONTRACTOR_ACC" queryAssemble="V_CONTRACTOR_ACC" label="承包人ACC账号"/>
            </row>
            <row>
                <field name="V_STICKERLABEL" queryAssemble="V_STICKERLABEL" label="扫描结果" readOnly="true"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="CAPACITY" label="标称容量" required="true"/>
            </row>
            <row>
                <field name="CONFIG_TYPE_ID" label="配置点类型" default="80203622"/>
                <field name="IS_INTEGFLAG" label="一体化标志"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="ADAPTTYPE_ID" label="适配类型"/>
            </row>
            <row>
                <field name="bestroom" label="最优机房"/>
                <field name="bestobd" label="最优OBD"/>
            </row>
            <row>
                <field name="BELONG_SPECIALITY_ID" label="所属专业" readOnly="true" default="80204675"/>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称" readOnly="true"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号" readOnly="true"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" />
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="addressobj" label="标准地址" getLabel="row.addressobj_value.FULL_NAME"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <grid name="GJCodeCutOver">
            <field name="TYPE" label="类型" label-en="Type" />
            <field name="COUNT" label="数量" label-en="Count" />
            <field name="OLDSITE" label="旧所属局站" label-en="oldSite" />
            <field name="NEWSITE" label="新所属局站" label-en="newSite" />
            <field name="RESULT" label="处理结果" label-en="Result" />
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:6000px;" autoLoad="false" baseParams='{"_orderBy":"CODE"}'>
            <field name="CODE" label="光交编码"/>
            <field name="NAME" label="光交名称" width="250px"/>
            <field name="V_STICKERLABEL" label="扫描结果"/>
            <field name="site" label="所属局站" getLabel="row.site_value?row.site_value.NAME:''"/>
            <field name="room_outdooraddress" label="所属机房" getLabel="row.room_outdooraddress_value.NAME"/>
            <field name="terminal_value.PORTS_TOTAL" label="端口总数"/>
            <field name="terminal_value.PORTS_USED" label="端子占用数"/>
            <field name="terminal_value.PORTS_FREE" label="端子空闲数"/>
            <field name="terminal_value.PORTS_CONNECTED" label="已成端端子数" width="100px"/>
            <field name="terminal_value.PORTS_BAD" label="坏端子数"/>
            <field name="terminal_value.PORTS_USEDRATE" label="端子占用率(%)" width="100px"/>
            <field name="addressobj" label="标准地址" getLabel="row.addressobj_value.FULL_NAME" width="300px"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
            <field name="smallcounty" label="划小区县"  getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="OWNER_NET_ID" label="网络类型"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="CAPACITY" label="标称容量"/>
            <field name="NOTES" label="备注"/>
            <field name="IS_INTEGFLAG" label="一体化标志"/>
            <field name="ADAPTTYPE_ID" label="适配类型"/>
            <field name="CONFIG_TYPE_ID" label="配置点类型"/>
            <field name="INSTALL_WAY_ID" label="安装方式"/>
            <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识" width="150px"/>
            <field name="ASSET_CODE" label="资产编码"/>
            <field name="CREATOR" label="录入人"/>
            <field name="MODIFIER" label="修改人"/>
            <field name="AUDIT_PERSON" label="验收人"/>
            <field name="PROPERTY_TYPE_ID" label="产权性质"/>
            <field name="PROPERTY_OWNER_ID" label="产权归属"/>
            <field name="MNT_LEVEL_ID" label="维护等级"/>
            <field name="MNT_PERSON" label="维护人"/>
            <field name="MNT_DEPT" label="维护单位"/>
            <field name="COLLECTION_DEPT" label="数据采集单位" width="100px"/>
            <field name="COLLECTION_PERSON" label="数据采集人"/>
            <field name="project_value.CODE" label="所属工程"/>
            <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            <field name="LONGITUDE" label="经度"/>
            <field name="LATITUDE" label="纬度"/>
            <field name="CREATE_DATE" label="入库时间"/>
            <field name="id" label="实物ID"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="POS_X" label="原X坐标"/>
            <field name="POS_Y" label="原Y坐标"/>
            <field name="model" label="型号"/>
            <field name="IS_GREEN_POSITION" label="位置准确绿标"/>
            <field name="DEVICE_TYPE_ID" label="设备类型"/>
            <field name="ADDRESS_ID" label="标准地址ID"/>
        </grid>
    </ObjMeta>
    <!-- 光交模板 -->
    <ObjMeta objectType="GJ.TEMPLATE" autoReload="true" labelField="NAME"
             typeActions="add,remove"
             itemActions="modify,remove,fuseview,connview,filesman,invokeLogicDiagram,invokeRouteDiagram">
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="模板名称"/>
                <field name="CAPACITY" label="标称容量"/>
            </row>
        </form>
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
                <field name="CAPACITY" label="标称容量" required="true"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="region" label="所属区域"/>
                <field name="ASSIGNED_TOTAL" label="满配OBD数"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="IS_INTEGFLAG" label="一体化标志"/>
                <field name="ADAPTTYPE_ID" label="适配类型"/>
            </row>
            <row>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" default="80209209" required="true" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" default="80209223" required="true" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:1390;" autoLoad="false">
            <field name="NAME" label="模板名称" width="250px"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
    </ObjMeta>

    <!-- GF 光分纤箱 -->
    <ObjMeta objectType="GF" autoReload="true" needgeom="true"
             typeActions="add,reset,templates,remove"
             itemActions="locate,relocate,modify,remove,connview,portDiagram,filesman,assetAttribute,relateOCablesection,relateFiberGroup"
             moreItemActions="attachdevice_man,chengduan,fuseview,resRelationCables,gridElement,deviceOccupyRate,biaoqiandayin,deviceOpticalcircuitLabel,siteFiberLabel,devicecoveraddress_man,showRelateODevice,invokeLogicDiagram,invokeRouteDiagram">
        <action type="workspace" name="templates" label="模板管理" label-en="Template management" title="光分纤箱模板"
                title-en="FDB Template"
                objectType="GF.TEMPLATE" icon="columns"/>
        <action type="workspace" name="connview-${id}" id="connview" label="端子图" label-en="Panel Diagram"
                icon="delicious"
                url="connector.html?objecttype=${objectType}&amp;id=${id}" extra="_ui_class:gray" title="端子面板图-${NAME}"
                title-en="Panel Diagram-${NAME}">
        </action>
        <action type="workspace" id="connectSection" label="关联光缆" title="${NAME}-关联光缆段" objectType="OCABLESECTION"
                options='{"baseParams": {"A_DEVICE_ID": "${id}","Z_DEVICE_ID":"${id}","_opgroup":"or(A_DEVICE_ID,Z_DEVICE_ID)"}}'/>
        <action name="clonedev" type="script" label="复制设备" url="apps/pipe/device/js/clonedev.js"/>
        <!--广东的对象修改需要支持重新选择模板-->
        <action name="modify" label="修改" label-en="Modify" type="obj" icon="write" itemsable="true" showTemplates="true"/>
        <script>
            <![CDATA[
        		om.descriptionField = function(o) {
          		var div = $('<div>');
          		var d1 = $('<span>').css({padding: '5px'}).appendTo(div).text('一级OBD数量: '), d2 = $('<span>').css({padding: '5px'}).appendTo(div).text('二级OBD数量: ');
          		_util.loadingRender(_bean.count('OBD', {parentDevice: o.id, OBD_LEVEL: 80204944}), d1);
          		_util.loadingRender(_bean.count('OBD', {parentDevice: o.id, OBD_LEVEL: 80204945}), d2);
          		return div;
        		};
      		]]>
        </script>
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="region" label="所属区域"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县"/>
                <field name="marketingarea" label="划小营销区"/>
            </row>
            <row>
                <field name="project" label="工程"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="入库时间"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="CAPACITY" label="标称容量" required="true"/>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="ASSIGNED_TOTAL" label="满配OBD数"/>
                <field name="ASSIGNED_USED" label="已配OBD数" readOnly="true"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" required="true" baseParams="searchAddressLevel:&gt;=9,isValid:100383"
                       dropdownOptions="titleField:FULL_NAME"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="ADDRESS_ID" label="地址ID" readOnly="true"/>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" readOnly="true" default="80204666"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="model" label="型号" required="true"/>
                <field name="DISTRICT" label="所属小区"/>
            </row>
            <row>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
                <field name="DEPRECIATION" label="折扣年限(年)"/>
            </row>
            <row>
                <field name="IS_SCENESAME_ID" label="是否与现场一致"/>
                <field name="APPLY_NET_ID" label="网络节点类型" />
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME" required="true"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="POS_X" label="原X坐标" readOnly="true"/>
                <field name="POS_Y" label="原Y坐标" readOnly="true"/>
            </row>
            <row>
                <field name="HOT_CONN_ID" label="是否热熔" default="80201002"/>
                <field name="HOT_CONN_DATE" label="热熔时间" readOnly="true"/>
            </row>
            <row>
                <field name="DATA_SOURCE_ID" label="数据来源" default="80401043"/>
                <field name="IS_GREEN_POSITION" label="位置准确绿标"/>
            </row>
            <row>
                <field name="V_CONTRACTOR" queryAssemble="V_CONTRACTOR" label="承包人姓名"/>
                <field name="V_CONTRACTOR_ACC" queryAssemble="V_CONTRACTOR_ACC" label="承包人账号"/>
            </row>
            <row>
                <field name="V_STICKERLABEL" queryAssemble="V_STICKERLABEL" label="扫描结果"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式" required="true" default="80204855"/>
                <field name="IS_INTEGFLAG" label="一体化标志"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
            </row>
            <row>
                <field name="bestroom" label="最优机房"/>
                <field name="bestobd" label="最优OBD"/>
            </row>
            <row>
                <field name="MIXED_CONFIG_ID" label="是否混放" default="80201001"/>
                <field name="BELONG_SPECIALITY_ID" label="所属专业" readOnly="true" default="80204675"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO" required="true"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" multiple="true" default="80209209"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" multiple="true" default="80209223"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人" readOnly="true"/>
                <field name="AUDIT_DATE" label="验收时间" readOnly="true"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!--批量修改表格-->
        <form name="batchmodify">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME" readOnly="true"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" readOnly="true"/>
                <field name="NAME" readOnly="true"/>
            </row>
            <row>
                <field name="CAPACITY" label="标称容量" />
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="ASSIGNED_TOTAL" label="满配OBD数"/>
                <field name="ASSIGNED_USED" label="已配OBD数" readOnly="true"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" baseParams="{&quot;_solr&quot;:true}"
                       dropdownOptions="titleField:FULL_NAME"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" readOnly="true"/>
            </row>
            <row>
                <field name="ADDRESS_ID" label="地址ID" readOnly="true"/>
                <field name="PROJECT_STATUS_ID" label="工程状态" readOnly="true" />
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" />
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="model" label="型号"/>
                <field name="DISTRICT" label="所属小区"/>
            </row>
            <row>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
                <field name="DEPRECIATION" label="折扣年限(年)"/>
            </row>
            <row>
                <field name="IS_SCENESAME_ID" label="是否与现场一致"/>
                <field name="APPLY_NET_ID" label="网络节点类型" />
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" />
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="POS_X" label="原X坐标" readOnly="true"/>
                <field name="POS_Y" label="原Y坐标" readOnly="true"/>
            </row>
            <row>
                <field name="HOT_CONN_ID" label="是否热熔" />
                <field name="HOT_CONN_DATE" label="热熔时间" readOnly="true"/>
            </row>
            <row>
                <field name="DATA_SOURCE_ID" label="数据来源" />
                <field name="IS_GREEN_POSITION" label="位置准确绿标"/>
            </row>
            <row>
                <field name="V_CONTRACTOR" queryAssemble="V_CONTRACTOR" label="承包人姓名"/>
                <field name="V_CONTRACTOR_ACC" queryAssemble="V_CONTRACTOR_ACC" label="承包人账号"/>
            </row>
            <row>
                <field name="V_STICKERLABEL" queryAssemble="V_STICKERLABEL" label="扫描结果" readOnly="true"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式" />
                <field name="IS_INTEGFLAG" label="一体化标志"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
            </row>
            <row>
                <field name="bestroom" label="最优机房"/>
                <field name="bestobd" label="最优OBD"/>
            </row>
            <row>
                <field name="MIXED_CONFIG_ID" label="是否混放" />
                <field name="BELONG_SPECIALITY_ID" label="所属专业" readOnly="true" />
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称" readOnly="true"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号" readOnly="true"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" />
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" />
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" multiple="true" />
                <field name="PROPERTY_OWNER_ID" label="产权归属" multiple="true" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" />
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="addressobj" label="标准地址" getLabel="row.addressobj_value.FULL_NAME"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:6000px;" autoLoad="false" baseParams='{"_orderBy":"CODE"}'>
            <field name="CODE" label="光分编码"/>
            <field name="NAME" label="光分名称" width="250px"/>
            <field name="V_STICKERLABEL" label="扫描结果"/>
            <field name="qrcode_value.CODE" label="绑定条码"/>
            <field name="site" label="所属局站" getLabel="row.site_value?row.site_value.NAME:''"/>
            <field name="room_outdooraddress" label="所属机房" getLabel="row.room_outdooraddress_value.NAME"/>
            <field name="terminal_value.PORTS_TOTAL" label="端口总数"/>
            <field name="terminal_value.PORTS_USED" label="端子占用数"/>
            <field name="terminal_value.PORTS_FREE" label="端子空闲数"/>
            <field name="terminal_value.PORTS_CONNECTED" label="已成端端子数" width="100px"/>
            <field name="terminal_value.PORTS_BAD" label="坏端子数"/>
            <field name="terminal_value.PORTS_USEDRATE" label="端子占用率(%)" width="100px"/>
            <field name="addressobj" label="标准地址" getLabel="row.addressobj_value.FULL_NAME" width="300px"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="OWNER_NET_ID" label="网络类型"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="CAPACITY" label="标称容量"/>
            <field name="NOTES" label="备注"/>
            <field name="IS_INTEGFLAG" label="一体化标志"/>
            <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识" width="150px"/>
            <field name="DEVICE_TYPE_ID" label="设备类型"/>
            <field name="MIXED_CONFIG_ID" label="是否混放"/>
            <field name="ASSET_CODE" label="资产编码"/>
            <field name="CREATOR" label="录入人"/>
            <field name="MODIFIER" label="修改人"/>
            <field name="AUDIT_PERSON" label="验收人"/>
            <field name="PROPERTY_TYPE_ID" label="产权性质"/>
            <field name="PROPERTY_OWNER_ID" label="产权归属"/>
            <field name="MNT_LEVEL_ID" label="维护等级"/>
            <field name="MNT_PERSON" label="维护人"/>
            <field name="MNT_DEPT" label="维护单位"/>
            <field name="COLLECTION_DEPT" label="数据采集单位" width="100px"/>
            <field name="COLLECTION_PERSON" label="数据采集人"/>
            <field name="project_value.CODE" label="所属工程"/>
            <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            <field name="LONGITUDE" label="经度"/>
            <field name="LATITUDE" label="纬度"/>
            <field name="CREATE_DATE" label="入库时间"/>
            <field name="id" label="实物ID"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="POS_X" label="原X坐标"/>
            <field name="POS_Y" label="原Y坐标"/>
            <field name="model" label="型号"/>
            <field name="IS_GREEN_POSITION" label="位置准确绿标"/>
            <field name="ADDRESS_ID" label="标准地址ID"/>
        </grid>
    </ObjMeta>
    <!-- 光分模板 -->
    <ObjMeta objectType="GF.TEMPLATE" autoReload="true" labelField="NAME"
             typeActions="add,remove"
             itemActions="modify,remove,fuseview,connview,filesman">
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <field name="CAPACITY" label="标称容量"/>
            <row>
                <field name="NAME" label="模板名称" required="true"/>
            </row>
        </form>
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
                <field name="CAPACITY" label="标称容量" required="true"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="region" label="所属区域"/>
                <field name="ASSIGNED_TOTAL" label="满配OBD数"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="IS_INTEGFLAG" label="一体化标志"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" default="80209209" required="true" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" default="80209223" required="true" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:1390;" autoLoad="false">
            <field name="NAME" label="模板名称" width="250px"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
    </ObjMeta>

    <!-- GB 光终端盒 -->
    <ObjMeta objectType="GB" autoReload="true" needgeom="true"
             typeActions="add,reset,templates,remove"
             itemActions="locate,relocate,modify,remove,connview,portDiagram,filesman,assetAttribute,relateOCablesection,relateFiberGroup"
             moreItemActions="attachdevice_man,chengduan,fuseview,resRelationCables,gridElement,deviceOccupyRate,biaoqiandayin,deviceOpticalcircuitLabel,siteFiberLabel,devicecoveraddress_man,showRelateODevice,invokeLogicDiagram,invokeRouteDiagram">
        <action type="workspace" name="templates" label="模板管理" label-en="Template management" title="光终端盒模板"
                title-en="OTB template"
                objectType="GB.TEMPLATE" icon="columns"/>
        <action name="modify" label="修改" label-en="Modify" type="obj" icon="write" itemsable="true" showTemplates="true"/>
        <action type="workspace" name="connview-${id}" id="connview" label="端子图" label-en="Panel Diagram"
                icon="delicious"
                url="connector.html?objecttype=${objectType}&amp;id=${id}" extra="_ui_class:gray" title="端子面板图-${NAME}"
                title-en="Panel Diagram-${NAME}">
        </action>
        <action name="clonedev" type="script" label="复制设备" url="apps/pipe/device/js/clonedev.js"/>
        <script>
            <![CDATA[
        		om.descriptionField = function(o) {
          		var div = $('<div>');
          		var d1 = $('<span>').css({padding: '5px'}).appendTo(div).text('一级OBD数量: '), d2 = $('<span>').css({padding: '5px'}).appendTo(div).text('二级OBD数量: ');
          		_util.loadingRender(_bean.count('OBD', {parentDevice: o.id, OBD_LEVEL: 80204944}), d1);
          		_util.loadingRender(_bean.count('OBD', {parentDevice: o.id, OBD_LEVEL: 80204945}), d2);
          		return div;
        		};
      		]]>
        </script>
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="region" label="所属区域"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县"/>
                <field name="marketingarea" label="划小营销区"/>
            </row>
            <row>
                <field name="project" label="工程"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="入库时间"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form GEN_CODE_TEMPLATE="${site.CODE}/GB${sn3}" GEN_NAME_TEMPLATE="${site.NAME}/GB${sn3}">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="CAPACITY" label="标称容量" required="true"/>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" required="true" baseParams="searchAddressLevel:&gt;=9,isValid:100383"
                       dropdownOptions="titleField:FULL_NAME"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="ASSIGNED_TOTAL" label="满配OBD数"/>
                <field name="ASSIGNED_USED" label="已配OBD数" readOnly="true"/>
            </row>
            <row>
                <field name="ADDRESS_ID" label="地址ID"/>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" readOnly="true" default="80204666"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="model" label="型号" required="true"/>
                <field name="DISTRICT" label="所属小区"/>
            </row>
            <row>
                <field name="IS_CHECKPOINT_ID" label="是否检查点" type="dict"/>
                <field name="DEPRECIATION" label="折扣年限(年)"/>
            </row>
            <row>
                <field name="IS_SCENESAME_ID" label="是否与现场一致"/>
                <field name="APPLY_NET_ID" label="网络节点类型"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME" required="true"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="POS_X" label="原X坐标" readOnly="true"/>
                <field name="POS_Y" label="原Y坐标" readOnly="true"/>
            </row>
            <row>
                <field name="HOT_CONN_ID" label="是否热熔" default="80201002"/>
                <field name="HOT_CONN_DATE" label="热熔时间" readOnly="true"/>
            </row>
            <row>
                <field name="DATA_SOURCE_ID" label="数据来源" default="80401043"/>
                <field name="IS_GREEN_POSITION" label="位置准确绿标"/>
            </row>
            <row>
                <field name="V_CONTRACTOR" queryAssemble="V_CONTRACTOR" label="承包人姓名"/>
                <field name="V_CONTRACTOR_ACC" queryAssemble="V_CONTRACTOR_ACC" label="承包人账号"/>
            </row>
            <row>
                <field name="V_STICKERLABEL" queryAssemble="V_STICKERLABEL" label="扫描结果"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式" required="true" default="80204855"/>
                <field name="IS_INTEGFLAG" label="一体化标志"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
            </row>
            <row>
                <field name="bestroom" label="最优机房"/>
                <field name="bestobd" label="最优OBD"/>
            </row>
            <row>
                <field name="MIXED_CONFIG_ID" label="是否混放" default="80201001"/>
                <field name="BELONG_SPECIALITY_ID" label="所属专业" readOnly="true" default="80204675"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO" required="true"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!-- 批量修改表格 -->
        <form name="batchmodify" GEN_CODE_TEMPLATE="${room_outdooraddress.CODE}/GB${sn3}" GEN_NAME_TEMPLATE="${room_outdooraddress.NAME}/GB${sn3}">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME" readOnly="true"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" readOnly="true"/>
                <field name="NAME" readOnly="true"/>
            </row>
            <row>
                <field name="CAPACITY" label="标称容量" />
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" baseParams="{&quot;_solr&quot;:true}"
                       dropdownOptions="titleField:FULL_NAME"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" readOnly="true"/>
            </row>
            <row>
                <field name="ASSIGNED_TOTAL" label="满配OBD数"/>
                <field name="ASSIGNED_USED" label="已配OBD数" readOnly="true"/>
            </row>
            <row>
                <field name="ADDRESS_ID" label="地址ID" readOnly="true"/>
                <field name="PROJECT_STATUS_ID" label="工程状态" readOnly="true" />
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" />
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="model" label="型号"/>
                <field name="DISTRICT" label="所属小区"/>
            </row>
            <row>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
                <field name="DEPRECIATION" label="折扣年限(年)"/>
            </row>
            <row>
                <field name="IS_SCENESAME_ID" label="是否与现场一致"/>
                <field name="APPLY_NET_ID" label="网络节点类型"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" />
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="POS_X" label="原X坐标" readOnly="true"/>
                <field name="POS_Y" label="原Y坐标" readOnly="true"/>
            </row>
            <row>
                <field name="HOT_CONN_ID" label="是否热熔" />
                <field name="HOT_CONN_DATE" label="热熔时间" readOnly="true"/>
            </row>
            <row>
                <field name="DATA_SOURCE_ID" label="数据来源" />
                <field name="IS_GREEN_POSITION" label="位置准确绿标"/>
            </row>
            <row>
                <field name="V_CONTRACTOR" queryAssemble="V_CONTRACTOR" label="承包人姓名"/>
                <field name="V_CONTRACTOR_ACC" queryAssemble="V_CONTRACTOR_ACC" label="承包人账号"/>
            </row>
            <row>
                <field name="V_STICKERLABEL" queryAssemble="V_STICKERLABEL" label="扫描结果" readOnly="true"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式" />
                <field name="IS_INTEGFLAG" label="一体化标志"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
            </row>
            <row>
                <field name="bestroom" label="最优机房"/>
                <field name="bestobd" label="最优OBD"/>
            </row>
            <row>
                <field name="MIXED_CONFIG_ID" label="是否混放" />
                <field name="BELONG_SPECIALITY_ID" label="所属专业" readOnly="true" />
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称" readOnly="true"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号" readOnly="true"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" />
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" />
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" />
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="addressobj" label="标准地址" getLabel="row.addressobj_value.FULL_NAME"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表--> 
        <grid tableStyle="width:6000px;" autoLoad="false" baseParams='{"_orderBy":"CODE"}'>
            <field name="CODE" label="终端盒编码"/>
            <field name="NAME" label="终端盒名称" width="250px"/>
            <field name="V_STICKERLABEL" label="扫描结果"/><!--找不到扫描结果字段-->
            <field name="qrcode_value.CODE" label="绑定条码"/>
            <field name="site" label="所属局站" getLabel="row.site_value?row.site_value.NAME:''"/>
            <field name="room_outdooraddress" label="所属机房" getLabel="row.room_outdooraddress_value.NAME"/>
            <field name="terminal_value.PORTS_TOTAL" label="端口总数"/>
            <field name="terminal_value.PORTS_USED" label="端子占用数"/>
            <field name="terminal_value.PORTS_FREE" label="端子空闲数"/>
            <field name="terminal_value.PORTS_CONNECTED" label="已成端端子数" width="100px"/>
            <field name="terminal_value.PORTS_BAD" label="坏端子数"/>
            <field name="terminal_value.PORTS_USEDRATE" label="端子占用率(%)" width="100px"/>
            <field name="addressobj" label="标准地址" getLabel="row.addressobj_value.FULL_NAME" width="300px"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="OWNER_NET_ID" label="网络类型"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="CAPACITY" label="标称容量"/>
            <field name="NOTES" label="备注"/>
            <field name="INSTALL_WAY_ID" label="安装方式"/>
            <field name="CONFIG_TYPE_ID" label="配置点类型"/>
            <field name="ACCESS_MODE_ID" label="接入方式"/>
            <field name="IS_INTEGFLAG" label="一体化标志"/>
            <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识" width="150px"/>
            <field name="MIXED_CONFIG_ID" label="是否混放"/>
            <field name="ASSET_CODE" label="资产编码"/>
            <field name="CREATOR" label="录入人"/>
            <field name="MODIFIER" label="修改人"/>
            <field name="AUDIT_PERSON" label="验收人"/>
            <field name="PROPERTY_TYPE_ID" label="产权性质"/>
            <field name="PROPERTY_OWNER_ID" label="产权归属"/>
            <field name="MNT_LEVEL_ID" label="维护等级"/>
            <field name="MNT_PERSON" label="维护人"/>
            <field name="MNT_DEPT" label="维护单位"/>
            <field name="COLLECTION_DEPT" label="数据采集单位" width="100px"/>
            <field name="COLLECTION_PERSON" label="数据采集人"/>
            <field name="project_value.CODE" label="所属工程"/>
            <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            <field name="LONGITUDE" label="经度"/>
            <field name="LATITUDE" label="纬度"/>
            <field name="CREATE_DATE" label="入库时间"/>
            <field name="id" label="实物ID"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="POS_X" label="原X坐标"/>
            <field name="POS_Y" label="原Y坐标"/>
            <field name="model" label="型号"/>
            <field name="IS_GREEN_POSITION" label="位置准确绿标"/>
            <field name="DEVICE_TYPE_ID" label="设备类型"/>
            <field name="ADDRESS_ID" label="标准地址ID"/>
        </grid>
    </ObjMeta>
    <!-- GB模板 -->
    <ObjMeta objectType="GB.TEMPLATE" autoReload="true" labelField="NAME"
             typeActions="add,remove"
             itemActions="modify,remove,fuseview,connview,filesman">
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <field name="CAPACITY" label="标称容量"/>
            <row>
                <field name="NAME" label="模板名称" required="true"/>
            </row>
        </form>
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="CAPACITY" label="标称容量" required="true"/>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="region" label="所属区域"/>
                <field name="ASSIGNED_TOTAL" label="满配OBD数"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="IS_INTEGFLAG" label="一体化标志"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" default="80209209" required="true" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" default="80209223" required="true" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:1390;" autoLoad="false">
            <field name="NAME" label="模板名称" width="250px"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
    </ObjMeta>

    <!-- ZHX 综合配线箱 -->
    <ObjMeta objectType="ZHX" autoReload="true" needgeom="true"
             typeActions="add,reset,templates,remove"
             itemActions="locate,relocate,modify,remove,connview,portDiagram,filesman,assetAttribute,relateOCablesection,relateFiberGroup,deviceMapping"
             moreItemActions="attachdevice_man,chengduan,fuseview,gridElement,deviceOccupyRate,biaoqiandayin,deviceOpticalcircuitLabel,siteFiberLabel,devicecoveraddress_man,showRelateODevice,invokeLogicDiagram,invokeRouteDiagram">
        <action type="workspace" name="templates" label="模板管理" label-en="Template management" title="综合配线箱模板"
                title-en="Integrated Box Template"
                objectType="ZHX.TEMPLATE" icon="columns"/>
        <action type="workspace" name="connview-${id}" id="connview" label="端子图" label-en="Panel Diagram"
                icon="delicious"
                url="connector.html?objecttype=${objectType}&amp;id=${id}" extra="_ui_class:gray" title="端子面板图-${NAME}"
                title-en="Panel Diagram-${NAME}">
        </action>
        <action name="clonedev" type="script" label="复制设备" url="apps/pipe/device/js/clonedev.js"/>
        <!--广东的对象修改需要支持重新选择模板-->
        <grid name="ZHXCodeCutOver">
            <field name="TYPE" label="类型" label-en="Type"/>
            <field name="COUNT" label="数量" label-en="Count"/>
            <field name="OLDSITE" label="旧所属局站" label-en="oldSite"/>
            <field name="NEWSITE" label="新所属局站" label-en="newSite"/>
            <field name="RESULT" label="处理结果" label-en="Result"/>
        </grid>
        <action name="modify" label="修改" label-en="Modify" type="obj" icon="write" itemsable="true" showTemplates="true"/>
        <script>
            <![CDATA[
        		om.descriptionField = function(o) {
          		var div = $('<div>');
          		var d1 = $('<span>').css({padding: '5px'}).appendTo(div).text('一级OBD数量: '), d2 = $('<span>').css({padding: '5px'}).appendTo(div).text('二级OBD数量: ');
          		_util.loadingRender(_bean.count('OBD', {parentDevice: o.id, OBD_LEVEL: 80204944}), d1);
          		_util.loadingRender(_bean.count('OBD', {parentDevice: o.id, OBD_LEVEL: 80204945}), d2);
          		return div;
        		};
      		]]>
        </script>
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="address" label="标准地址"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="region" label="所属区域"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县"/>
                <field name="marketingarea" label="划小营销区"/>
            </row>
            <row>
                <field name="project" label="工程"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="入库时间"/>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
         <!--新增/修改面板-->
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点"  dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="model" label="型号" required="true"/>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
            </row>
            <row>
                <field name="CAPACITY" label="标称容量" required="true"/>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" required="true" baseParams="{&quot;_solr&quot;:true}"
                       dropdownOptions="titleField:FULL_NAME"/>
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="ADDRESS_ID" label="地址ID" readOnly="true"/>
                <field name="DISTRICT" label="所属小区"/>
            </row>
            <row>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
            </row>
            <row>
                <field name="IS_INTEGFLAG" label="一体化标志"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>         
            <row> 
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" readOnly="true" default="80204666"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="POS_X" label="原X坐标" readOnly="true"/>
                <field name="POS_Y" label="原Y坐标" readOnly="true"/>
            </row>
            <row>
                <field name="DATA_SOURCE_ID" label="数据来源" default="80401043"/>
                <field name="IS_GREEN_POSITION" label="位置准确绿标"/>
            </row>
            <row>
                <field name="V_CONTRACTOR" queryAssemble="V_CONTRACTOR" label="承包人姓名"/>
                <field name="V_CONTRACTOR_ACC" queryAssemble="V_CONTRACTOR_ACC" label="承包人账号"/>
            </row>
            <row>
                <field name="V_STICKERLABEL" queryAssemble="V_STICKERLABEL" label="扫描结果"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
            </row>
            <row>
                <field name="WIDE_MEASURE_RATE" label="宽测速率"/>
                <field name="THEORY_RATE" label="理论速率"/>
            </row>
            <row>
                <field name="SITE_RADIO_ABILITY" label="局端语音能力"/>
                <field name="SITE_WIDE_ABILITY" label="局端宽带能力"/>
            </row>
            <row>
                <field name="bestroom" label="最优机房"/>
                <field name="bestobd" label="最优OBD"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式" default="80204855"/>
                <field name="MIXED_CONFIG_ID" label="是否混放" default="80201001"/>
            </row>
            <row>
                <field name="BELONG_SPECIALITY_ID" label="所属专业" readOnly="true" default="80204675"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="asset" label="资产目录" dropdownOptions="titleField:ASSET_CATALOGUE" />
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!-- 批量修改表格 -->
        <form name="batchmodify">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME" readOnly="true"/>
                <field name="room_outdooraddress" label="机房/安装点"  dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" required="true" readOnly="true"/>
                <field name="NAME" required="true" readOnly="true"/>
            </row>
            <row>
                <field name="model" label="型号"/>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848" readOnly="true"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
            </row>
            <row>
                <field name="CAPACITY" label="标称容量" required="true"/>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" required="true" baseParams="{&quot;_solr&quot;:true}"
                       dropdownOptions="titleField:FULL_NAME"/>
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="ADDRESS_ID" label="地址ID" readOnly="true"/>
                <field name="DISTRICT" label="所属小区"/>
            </row>
            <row>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
            </row>
            <row>
                <field name="IS_INTEGFLAG" label="一体化标志"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>         
            <row> 
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" readOnly="true" default="80204666"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="POS_X" label="原X坐标" readOnly="true"/>
                <field name="POS_Y" label="原Y坐标" readOnly="true"/>
            </row>
            <row>
                <field name="DATA_SOURCE_ID" label="数据来源" default="80401043"/>
                <field name="IS_GREEN_POSITION" label="位置准确绿标"/>
            </row>
            <row>
                <field name="V_CONTRACTOR" queryAssemble="V_CONTRACTOR" label="承包人姓名"/>
                <field name="V_CONTRACTOR_ACC" queryAssemble="V_CONTRACTOR_ACC" label="承包人账号"/>
            </row>
            <row>
                <field name="V_STICKERLABEL" queryAssemble="V_STICKERLABEL" label="扫描结果"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
            </row>
            <row>
                <field name="WIDE_MEASURE_RATE" label="宽测速率"/>
                <field name="THEORY_RATE" label="理论速率"/>
            </row>
            <row>
                <field name="SITE_RADIO_ABILITY" label="局端语音能力"/>
                <field name="SITE_WIDE_ABILITY" label="局端宽带能力"/>
            </row>
            <row>
                <field name="bestroom" label="最优机房"/>
                <field name="bestobd" label="最优OBD"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式" required="true" default="80204855"/>
                <field name="MIXED_CONFIG_ID" label="是否混放" required="true" default="80201001"/>
            </row>
            <row>
                <field name="BELONG_SPECIALITY_ID" label="所属专业" readOnly="true" default="80204675"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称" readOnly="true"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号" readOnly="true"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="asset" label="资产目录" dropdownOptions="titleField:ASSET_CATALOGUE" />
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="addressobj" label="标准地址" getLabel="row.addressobj_value.FULL_NAME"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:6000px;" autoLoad="false" baseParams='{"_orderBy":"CODE"}'>
            <field name="CODE" label="综合配线箱编码"/>
            <field name="NAME" label="综合配线箱名称" width="250px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="terminal_value.PORTS_TOTAL" label="端口总数"/>
            <field name="terminal_value.PORTS_USED" label="端子占用数"/>
            <field name="terminal_value.PORTS_FREE" label="端子空闲数"/>
            <field name="terminal_value.PORTS_CONNECTED" label="已成端端子数" width="100px"/>
            <field name="terminal_value.PORTS_BAD" label="坏端子数"/>
            <field name="terminal_value.PORTS_USEDRATE" label="端子占用率(%)" width="100px"/>
            <field name="addressobj" label="标准地址" getLabel="row.addressobj_value.FULL_NAME" width="300px"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="OWNER_NET_ID" label="网络类型"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="CAPACITY" label="标称容量"/>
            <field name="NOTES" label="备注"/>
            <field name="CONFIG_TYPE_ID" label="配置点类型"/>
            <field name="INSTALL_WAY_ID" label="安装方式"/>
            <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识" width="150px"/>
            <field name="WIDE_MEASURE_RATE" label="宽带测率"/>
            <field name="THEORY_RATE" label="理论速率"/>
            <field name="SITE_RADIO_ABILITY" label="局端语音能力"/>
            <field name="SITE_WIDE_ABILITY" label="局端宽带能力"/>
            <field name="ACCESS_MODE_ID" label="接入方式"/>
            <field name="ASSET_CODE" label="资产编码"/>
            <field name="CREATOR" label="录入人"/>
            <field name="MODIFIER" label="修改人"/>
            <field name="AUDIT_PERSON" label="验收人"/>
            <field name="PROPERTY_TYPE_ID" label="产权性质"/>
            <field name="PROPERTY_OWNER_ID" label="产权归属"/>
            <field name="MNT_LEVEL_ID" label="维护等级"/>
            <field name="MNT_PERSON" label="维护人"/>
            <field name="MNT_DEPT" label="维护单位"/>
            <field name="COLLECTION_DEPT" label="数据采集单位" width="100px"/>
            <field name="COLLECTION_PERSON" label="数据采集人"/>
            <field name="project_value.CODE" label="所属工程"/>
            <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            <field name="IS_GREEN_POSITION" label="位置准确绿标"/>
            <field name="LONGITUDE" label="经度"/>
            <field name="LATITUDE" label="纬度"/>
            <field name="id" label="实物ID"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="CREATE_DATE" label="入库时间"/>
        </grid>
    </ObjMeta>
    <!-- ZHX模板 -->
    <ObjMeta objectType="ZHX.TEMPLATE" autoReload="true" labelField="NAME"
             typeActions="add,remove"
             itemActions="modify,remove,fuseview,connview,filesman">
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="模板名称" required="true"/>
            </row>
        </form>
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="CAPACITY" label="标称容量" required="true"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="region" label="所属区域"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
            </row>
            <row>
                <field name="WIDE_MEASURE_RATE" label="宽带测率"/>
                <field name="THEORY_RATE" label="理论速率"/>
            </row>
            <row>
                <field name="SITE_RADIO_ABILITY" label="局端语音能力"/>
                <field name="SITE_WIDE_ABILITY" label="局端宽带能力"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" default="80209209" required="true" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" default="80209223" required="true" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:1390;" autoLoad="false">
            <field name="NAME" label="模板名称" width="250px"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
    </ObjMeta>

    <!-- IDF 综合配线架 -->
    <ObjMeta objectType="IDF" autoReload="true"
             needgeom="false"
             typeActions="add,reset,templates,remove"
             itemActions="locate,modify,remove,connview,econnview,portDiagram,filesman,assetAttribute,relateOCablesection,relateFiberGroup"
             moreItemActions="attachdevice_man,chengduan,fuseview,gridElement,deviceOccupyRate,devicecoveraddress_man,showRelateODevice,invokeLogicDiagram,invokeRouteDiagram">
        <action type="workspace" name="templates" label="模板管理" label-en="Template management" title="综合配线架模板"
                title-en="IDF template"
                objectType="IDF.TEMPLATE" icon="columns"/>
        <action type="workspace" name="connview-${id}" id="connview" label="端子图" label-en="Panel Diagram"
                icon="delicious"
                url="connector.html?objecttype=${objectType}&amp;id=${id}" extra="_ui_class:gray" title="端子面板图-${NAME}"
                title-en="Panel Diagram-${NAME}">
        </action>
        <action type="workspace" name="econnview-${id}" id="econnview"
			label="端子图(电)" label-en="MODF Panel Diagram" icon="delicious"
			url="econnector.html?objecttype=${objectType}&amp;id=${id}"
			extra="_ui_class:gray" title="端子面板图(电)-${NAME}"
			title-en="MODF Panel Diagram-${NAME}">
		</action>
        <!--广东的对象修改需要支持重新选择模板-->
        <action name="modify" label="修改" label-en="Modify" type="obj" icon="write" itemsable="true" showTemplates="true"/>
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="IS_EDF_ID" label="是否EDF"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME" onChange="CODE,NAME=@genCode()"/>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="region" label="所属区域"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县"/>
                <field name="marketingarea" label="划小营销区"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="CREATOR" label="录入人"/>
            </row>
            <row>
                <field name="project" label="工程"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="入库时间"/>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" required="true" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="model" label="型号" required="true"/>
                <field name="IS_EDF_ID" label="是否EDF" required="true" default="80201002"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="CAPACITY" label="标称容量" required="true"/>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" required="true" baseParams="{&quot;_solr&quot;:true}"
                       dropdownOptions="titleField:FULL_NAME"/>
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="ADDRESS_ID" label="地址ID"/>
                <field name="DISTRICT" label="所属小区"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" readOnly="true" default="80204666"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="ROW_NO" label="行号" required="true"/>
                <field name="COL_NO" label="列号" required="true"/>
            </row>
            <row>
                <field name="LENGTH" label="长度(米)"/>
                <field name="WIDTH" label="宽度(米)"/>
            </row>
            <row>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME" required="true"/>
                <field name="HEIGHT" label="高度(米)"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="V_CONTRACTOR" queryAssemble="V_CONTRACTOR" label="承包人姓名"/>
                <field name="V_CONTRACTOR_ACC" queryAssemble="V_CONTRACTOR_ACC" label="承包人账号"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
            </row>
            <row>
                <field name="POS_X" label="机房内X坐标" readOnly="true"/>
                <field name="POS_Y" label="机房内Y坐标" readOnly="true"/>
            </row>
            <row>
                <field name="BELONG_SPECIALITY_ID" label="所属专业" readOnly="true" default="80204674"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="asset" label="资产目录" dropdownOptions="titleField:ASSET_CATALOGUE" />
                <field name="PROPERTY_TYPE_ID" label="产权性质" default="80209209" required="true" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" default="80209223" required="true" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!-- 批量修改表格 -->
        <form name="batchmodify">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME" readOnly="true"/>
                <field name="room_outdooraddress" label="机房/安装点" required="true" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" required="true" readOnly="true"/>
                <field name="NAME" required="true" readOnly="true"/>
            </row>
            <row>
                <field name="model" label="型号"/>
                <field name="IS_EDF_ID" label="是否EDF" required="true" default="80201002"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848" readOnly="true"/>
            </row>
            <row>
                <field name="CAPACITY" label="标称容量" required="true"/>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" required="true" baseParams="{&quot;_solr&quot;:true}"
                       dropdownOptions="titleField:FULL_NAME"/>
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="ADDRESS_ID" label="地址ID" readOnly="true"/>
                <field name="DISTRICT" label="所属小区"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" readOnly="true" default="80204666"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="ROW_NO" label="行号" required="true"/>
                <field name="COL_NO" label="列号" required="true"/>
            </row>
            <row>
                <field name="LENGTH" label="长度(米)"/>
                <field name="WIDTH" label="宽度(米)"/>
            </row>
            <row>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
                <field name="HEIGHT" label="高度(米)"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="V_CONTRACTOR" queryAssemble="V_CONTRACTOR" label="承包人姓名"/>
                <field name="V_CONTRACTOR_ACC" queryAssemble="V_CONTRACTOR_ACC" label="承包人账号"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
            </row>
            <row>
                <field name="POS_X" label="机房内X坐标" readOnly="true"/>
                <field name="POS_Y" label="机房内Y坐标" readOnly="true"/>
            </row>
            <row>
                <field name="BELONG_SPECIALITY_ID" label="所属专业" readOnly="true" default="80204674"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称" readOnly="true"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号" readOnly="true"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="asset" label="资产目录" dropdownOptions="titleField:ASSET_CATALOGUE" />
                <field name="PROPERTY_TYPE_ID" label="产权性质" default="80209209" required="true" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" default="80209223" required="true" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="addressobj" label="标准地址" getLabel="row.addressobj_value.FULL_NAME"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:6000px;" autoLoad="false" baseParams='{"_orderBy":"CODE"}'>
            <field name="CODE" label="综合配线架编码"/>
            <field name="NAME" label="综合配线架名称" width="250px"/>
            <field name="ROW_NO" label="行号"/>
            <field name="COL_NO" label="列号"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="room_outdooraddress" label="所属机房" getLabel="row.room_outdooraddress_value.NAME"/>
            <field name="terminal_value.PORTS_TOTAL" label="端口总数"/>
            <field name="terminal_value.PORTS_USED" label="端子占用数"/>
            <field name="terminal_value.PORTS_FREE" label="端子空闲数"/>
            <field name="terminal_value.PORTS_CONNECTED" label="已成端端子数" width="100px"/>
            <field name="terminal_value.PORTS_BAD" label="坏端子数"/>
            <field name="terminal_value.PORTS_USEDRATE" label="端子占用率(%)" width="100px"/>
            <field name="addressobj" label="标准地址" getLabel="row.addressobj_value.FULL_NAME" width="300px"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="CAPACITY" label="标称容量"/>
            <field name="NOTES" label="备注"/>
            <field name="CONFIG_TYPE_ID" label="配置点类型"/>
            <field name="INSTALL_WAY_ID" label="安装方式"/>
            <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识" width="150px"/>
            <field name="ASSET_CODE" label="资产编码"/>
            <field name="CREATOR" label="录入人"/>
            <field name="MODIFIER" label="修改人"/>
            <field name="AUDIT_PERSON" label="验收人"/>
            <field name="PROPERTY_TYPE_ID" label="产权性质"/>
            <field name="PROPERTY_OWNER_ID" label="产权归属"/>
            <field name="MNT_LEVEL_ID" label="维护等级"/>
            <field name="MNT_PERSON" label="维护人"/>
            <field name="MNT_DEPT" label="维护单位"/>
            <field name="COLLECTION_DEPT" label="数据采集单位" width="100px"/>
            <field name="COLLECTION_PERSON" label="数据采集人"/>
            <field name="project_value.CODE" label="所属工程"/>
            <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            <field name="LONGITUDE" label="经度"/>
            <field name="LATITUDE" label="纬度"/>
            <field name="CREATE_DATE" label="入库时间"/>
            <field name="LENGTH" label="长度(米)"/>
            <field name="WIDTH" label="宽度(米)"/>
            <field name="HEIGHT" label="高度(米)"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
            <field name="POS_X" label="原X坐标"/>
            <field name="POS_Y" label="原Y坐标"/>
        </grid>
    </ObjMeta>
    <!-- IDF模板 -->
    <ObjMeta objectType="IDF.TEMPLATE" autoReload="true" labelField="NAME"
             typeActions="add,remove"
             itemActions="modify,remove,fuseview,connview,filesman">
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="模板名称" required="true"/>
            </row>
        </form>
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="名称" required="true"/>
                <field name="IS_EDF_ID" label="是否EDF" required="true" default="80201002"/>
            </row>
            <row>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
                <field name="CAPACITY" label="标称容量" required="true"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
                <field name="LENGTH" label="长度(米)"/>
            </row>
            <row>
                <field name="WIDTH" label="宽度(米)"/>
                <field name="HEIGHT" label="高度(米)"/>
            </row>
            <row>
                <field name="region" label="所属区域"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:1390;" autoLoad="false">
            <field name="NAME" label="模板名称" width="250px"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--<action name="genCode" type="script">
            <![CDATA[
                var pre = facility.CODE + '/IDF';
                _bean.autoCode(this, pre, 3, 'IDF', {facility: facility.id});
            ]]>
        </action>-->
    </ObjMeta>

    <!-- GT 光缆接头 -->
    <ObjMeta objectType="GT" needgeom="false" autoReload="true"
             typeActions="add,reset,templates,remove"
             itemActions="locate,modify,remove,filesman,fuseview,assetAttribute,relateOCablesection,invokeLogicDiagram,invokeRouteDiagram">
        <action type="workspace" name="templates" label="模板管理" label-en="Template management" title="光缆接头模板"
                title-en="GT Template"
                objectType="GT.TEMPLATE" icon="columns"/>
        <action name="clonedev" type="script" label="复制设备" url="apps/pipe/device/js/clonedev.js"/>
        <!--广东的对象修改需要支持重新选择模板-->
        <action name="modify" label="修改" label-en="Modify" type="obj" icon="write" itemsable="true" showTemplates="true"/>
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME"/>
                <field name="facility" label="所属支撑设施"
                       baseParams='{"SPEC_ID":"UNDERWELL,WELL,POLE,DRAWINGPOINT,SUPPORTPOINT","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            </row>
            <row>
                <field name="region" label="所属区域"/>
                <field name="smallcounty" label="划小区县"/>
            </row>
            <row>
                <field name="marketingarea" label="划小营销区"/>
                <field name="CREATOR" label="录入人"/>
            </row>
            <row>
                <field name="project" label="工程"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="入库时间"/>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="net" label="所属光缆" required="true" dropdownOptions="titleField:NAME"/>
                <field name="facility" label="所属支撑设施" required="true" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"UNDERWELL,WELL,POLE,DRAWINGPOINT,SUPPORTPOINT","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" readOnly="true" default="80204666"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="JOINTMODEL" label="规格型号"/>
                <field name="DEVICE_TYPE_ID" label="接头类型"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
                <field name="model" label="型号" required="true"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="POS_X" label="原X坐标" readOnly="true"/>
                <field name="POS_Y" label="原Y坐标" readOnly="true"/>
            </row>
            <row>
                <field name="DATA_SOURCE_ID" label="数据来源" default="80401043"/>
                <field name="BELONG_SPECIALITY_ID" label="所属专业" readOnly="true" default="80204675"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="LAYMODE_ID" label="敷设方式"/>
            </row>
            <row>
                <field name="ATTENUATION" label="接头衰耗"/>
                <field name="JOINTCOVER" label="套管类型"/>
            </row>
            <row>
                <field name="CONNECT_FIBERNUM" label="接续芯数"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="asset" label="资产目录" dropdownOptions="titleField:ASSET_CATALOGUE" />
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="addressobj" label="标准地址" getLabel="row.addressobj_value.FULL_NAME"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:6000px;" autoLoad="false" baseParams='{"_orderBy":"CODE"}'>
            <field name="CODE" label="光接头编码"/>
            <field name="NAME" label="光接头名称" width="250px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="OWNER_NET_ID" label="网络类型"/>
            <field name="facility" label="所属支撑设施" getLabel="row.facility_value.NAME"/>
            <field name="DEVICE_TYPE_ID" label="接头类型"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="NOTES" label="备注"/>
            <field name="CONFIG_TYPE_ID" label="配置点类型"/>
            <field name="INSTALL_WAY_ID" label="安装方式"/>
            <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识" width="150px"/>
            <field name="LAYMODE_ID" label="敷设方式"/>
            <field name="ATTENUATION" label="接头衰耗"/>
            <field name="CONNECT_FIBERNUM" label="接续芯数"/>
            <field name="ASSET_CODE" label="资产编码"/>
            <field name="CREATOR" label="录入人"/>
            <field name="MODIFIER" label="修改人"/>
            <field name="AUDIT_PERSON" label="验收人"/>
            <field name="PROPERTY_TYPE_ID" label="产权性质"/>
            <field name="PROPERTY_OWNER_ID" label="产权归属"/>
            <field name="MNT_LEVEL_ID" label="维护等级"/>
            <field name="MNT_PERSON" label="维护人"/>
            <field name="MNT_DEPT" label="维护单位"/>
            <field name="COLLECTION_DEPT" label="数据采集单位" width="100px"/>
            <field name="COLLECTION_PERSON" label="数据采集人"/>
            <field name="project_value.CODE" label="所属工程"/>
            <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            <field name="LONGITUDE" label="经度"/>
            <field name="LATITUDE" label="纬度"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
            <field name="CREATE_DATE" label="入库时间"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
        </grid>
        <!-- 资源关联光交接头的列表 -->
        <grid name="resourcesRelationGTGrid"
              typeActions="add" extraActions="expgrid,expall"
              itemActions="modify,locate,remove,fuseview">
            <field name="CODE" label="光接头编码"/>
            <field name="NAME" label="光接头名称" width="250px"/>
            <field name="site" label="局站编码" getLabel="row.site_value.CODE"/>
            <field name="site" label="局站名称" getLabel="row.site_value.NAME"/>
            <field name="DEVICE_TYPE_ID" label="接头类型"/>
            <field name="model" label="型号"/>
            <field name="CREATE_DATE" label="创建日期"/>
        </grid>
        <grid name="FTTXGT" filterable="false" sortable="false" tableStyle="width:900px">
            <field name="room_outdooraddress" label="所属机房"/>
            <field name="CODE" label="接头编码"/>
            <field name="NAME" label="接头名称"/>
            <field name="DEVICE_TYPE_ID" label="接头类型"/>
            <field name="CONNECT_FIBERNUM" label="接续芯数"/>
        </grid>
    </ObjMeta>
    <!-- GT模板 -->
    <ObjMeta objectType="GT.TEMPLATE" autoReload="true" labelField="NAME"
             typeActions="add,remove"
             itemActions="modify,remove,fuseview,filesman">
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="模板名称" required="true"/>
            </row>
        </form>
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="region" label="所属区域"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="LAYMODE_ID" label="敷设方式"/>
            </row>
            <row>
                <field name="DEVICE_TYPE_ID" label="接头类型"/>
                <field name="ATTENUATION" label="接头衰耗"/>
            </row>
            <row>
                <field name="CONNECT_FIBERNUM" label="接续芯数"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:1390;" autoLoad="false">
            <field name="NAME" label="模板名称" width="250px"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
    </ObjMeta>

    <!-- ONU设备 -->
    <ObjMeta objectType="ONU" needgeom="false" autoReload="true"
             typeActions="reset" extraActions="expgrid,expall"
             itemActions="view,locate">
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="parentDevice" label="所属设备" baseParams='{"SPEC_ID":"GJ,GF,ZHX","excludeTemplate":"true"}'/>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="region" label="所属区域"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县"/>
                <field name="marketingarea" label="划小营销区"/>
            </row>
            <row>
                <field name="project_name" label="工程名称"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" required="true" onChange="region=site.REGION_ID;CODE,NAME=@genCode(site)"/>
                <field name="room_outdooraddress" label="机房/安装点" required="true" dropdownOptions="titleField:NAME"
                       onChange="site=room_outdooraddress.TML_ID;address=room_outdooraddress.address"/>
            </row>
            <row>
                <field name="CODE" label="编码" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="CAPACITY" label="标称容量" required="true"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
                <field name="relayport" label="依赖端口"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址"/>
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="DISTRICT" label="所属小区"/>
            </row>
            <row>
                <field name="BELONG_SPECIALITY_ID" label="所属专业" readOnly="true" default="80204674"/>
                <field name="PROJECT_STATUS_ID" label="工程状态" default="80204666"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="POS_X" label="原X坐标" readOnly="true"/>
                <field name="POS_Y" label="原Y坐标" readOnly="true"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="addressobj" label="标准地址" getLabel="row.addressobj_value.FULL_NAME"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:3000px;" autoLoad="false" baseParams='{"_orderBy":"CODE"}'>
            <field name="CODE" label="ONU编码"/>
            <field name="NAME" label="ONU名称"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="room_outdooraddress" label="所属机房"/>
            <field name="BELONG_SPECIALITY_ID" label="所属专业"/>
            <field name="OWNER_NET_ID" label="所属网络"/>
            <field name="NETWORK_LAYER_ID" label="网络层次"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="LIFE_STATE_ID" label="生命周期状态"/>
            <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="CREATE_DATE" label="录入时间"/>
            <field name="NM_IP" label="网管IP"/>
            <field name="ACCESS_MODE_ID" label="接入方式"/>
            <field name="CREATE_TYPE_ID" label="生成标识"/>
            <field name="LOID" label="LOID"/>
            <field name="BUILD_MODE_ID" label="建设模式"/>
            <field name="MANAGE_MODE_ID" label="管理模式"/>
            <field name="SCENE_PARA_ID" label="场景参数"/>
            <field name="IS_TYM_ID" label="是否天翼猫"/>
            <field name="IS_IMS_ID" label="是否IMS"/>
            <field name="IS_VOICEONLY_ID" label="是否语音独占"/>
            <field name="IS_ACCESS_RES_ID" label="是否有窄带资源"/>
            <!--<field name="" label="OBD端子"/>-->
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
        </grid>
        <action type="script" name="view" label="查看" label-en="View">
            <![CDATA[
                var telanturl=_context.clientConf.telantUrl;
                var onurl = "com.ccssoft.inventory.web.equipment.view.DeviceDetail.d7?openMode=modify&metaClassName=ONU&filter=pipe&entityId="+_actionContext.params.id+"&gisCallParam=NDQxMDAwMDAwMDAwMDAxMDA0MTU5OTM5";
                window.open(telanturl+onurl);
            ]]>
        </action>
    </ObjMeta>

    <!-- OLTDEVICE设备 -->
    <ObjMeta objectType="OLTDEVICE" needgeom="false" autoReload="true"
             typeActions="reset" extraActions="expgrid,expall"
             itemActions="view,locate">
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="region" label="所属区域"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县"/>
                <field name="marketingarea" label="划小营销区"/>
            </row>
            <row>
                <field name="project_name" label="工程名称"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" required="true" onChange="region=site.REGION_ID;CODE,NAME=@genCode(site)"/>
                <field name="room"
                       onChange="site=room.TML_ID;${rack}.dropdown.setBaseParam('room',${room}.getValue() || -1)"/>
            </row>
            <row>
                <field name="rack"/>
                <field name="model"/>
            </row>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>

            <row>
                <field name="LIFE_STATE_ID"/>
                <field name="CAPACITY"/>
            </row>
            <row>
                <field name="CREATOR"/>
                <field name="CREATE_DATE"/>
            </row>
            <row>
                <field name="plane" baseParams="PLANE_ID:80206206" label="所属一平面POP点" label-en="Plane 1 POP point"/>
                <field name="plane2" baseParams="PLANE_ID:80206207" label="所属二平面POP点" label-en="Plane 2 POP point"/>
            </row>
            <row>
                <field name="plane3" baseParams="PLANE_ID:80206208" label="所属1.5平面POP点" label-en="Plane 1.5 POP point"/>
                <field name="NOTES"/>
            </row>
            <field type="divider" label="位置信息" label-en="Location information"/>

            <row>
                <field name="LONGITUDE"/>
                <field name="LATITUDE"/>
            </row>
            <!--
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row><field name="smallcounty"/><field name="marketingarea"/></row>
            <row><field name="servicearea"/></row>
            -->

            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID"/>
                <field name="PROPERTY_OWNER_ID"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID"/>
                <field name="MNT_PERSON"/>
            </row>
            <row>
                <field name="MNT_DEPT"/>
                <field name="COLLECTION_DEPT"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON"/>
                <field name="COLLECTION_DATE"/>
            </row>
            <row>
                <field name="AUDIT_PERSON"/>
                <field name="AUDIT_DATE"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project"/>
            </row>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="addressobj" label="标准地址" getLabel="row.addressobj_value.FULL_NAME"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:6000px;" autoLoad="false" baseParams='{"_orderBy":"CODE"}'>
            <field name="CODE" label="OLT编码"/>
            <field name="NAME" label="OLT名称" width="250px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="room_outdooraddress" label="所属机房"/>
            <field name="OWNER_NET_ID" label="所属网络"/>
            <field name="NETWORK_LAYER_ID" label="网络层次"/>
            <field name="SECURITY_LEVEL_ID" label="设备安全等级"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="NM_IP" label="网管IP"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="CREATE_DATE" label="录入时间"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
        </grid>

        <grid name="searchObdOnus">
            <field name="code" label="编码" />
            <field name="opticcode" label="光路编码" />
            <field name="opticname" label="光路名称" />
            <field name="singletypeid_description" label="光路纤数" />
            <field name="usingstateid_description" label="业务状态" />
            <field name="length" label="光路长度" />
            <field name="link2ame_type" label="A端设备类型" />
            <field name="link2ame_name" label="A端设备名称" />
            <field name="link2ame_code" label="A端设备编码" />
            <field name="link2aport_code" label="A端端子" />
            <field name="link2zme_type" label="Z端设备类型" />
            <field name="link2zme_name" label="Z端设备名称" />
            <field name="link2zme_code" label="Z端设备编码" />
            <field name="link2zport_code" label="Z端端子" />
            <field name="serviceno" label="接入号" />
            <field name="columnbusiordercode" label="调单号"/>
            <field name="oldopticcode" label="旧光路编码" />
            <field name="opticcodeold" label="光路旧编码" />
            <field name="areaopticname" label="本地网光路名称" />
        </grid>
        <action type="script" name="view" label="查看" label-en="View">
            <![CDATA[
                var telanturl=_context.clientConf.telantUrl;
                var olturl = "com.ccssoft.inventory.web.equipment.view.DeviceDetail.d7?openMode=modify&metaClassName=OLTDEVICE&filter=pipe&entityId="+_actionContext.params.id+"&gisCallParam=NDQxMDAwMDAwMDAwMDAxMDA0MTU5OTM5";
                window.open(telanturl+olturl);
            ]]>
        </action>
    </ObjMeta>

    <!-- GRESERVE 光预留设备-->
    <ObjMeta objectType="GRESERVE" autoReload="true"
             needgeom="false"
             typeActions="add,reset,remove"
             itemActions="locate,modify,remove,filesman">
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME"/>
                <field name="facility" label="所属支撑设施"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
                <field name="OBLIGATE_NUM" label="预留芯数"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县"/>
                <field name="marketingarea" label="划小营销区"/>
            </row>
            <row>
                <field name="region" label="所属区域"/>
                <field name="CREATOR" label="录入人"/>
            </row>
            <row>
                <field name="project" label="工程"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="id" label="实物ID"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="ocablesection" label="所属缆段" dropdownOptions="titleField:CODE"/>
            </row>
            <row>
                <field name="facility" label="所属支撑设施" required="true" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"UNDERWELL,WELL,POLE,DRAWINGPOINT,SUPPORTPOINT","excludeTemplate":"true"}'/>
                <field name="OBLIGATE_NUM" label="预留纤芯数"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" default="80204666"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
                <field name="LENGTH" label="预留长度"/>
            </row>
            <field name="ASSET_CODE" label="资产编码"/>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="addressobj" label="标准地址" getLabel="row.addressobj_value.FULL_NAME"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:6000px;" autoLoad="false">
            <field name="CODE" label="预留编码"/>
            <field name="NAME" label="预留名称" width="250px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="siteCode" label="所属局站编码" getLabel="row.site_value.CODE"/>
            <field name="facility" label="所属支撑设施"/>
            <field name="model" label="型号"/>
            <field name="OBLIGATE_NUM" label="预留纤芯数"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="ASSET_CODE" label="资产编码"/>
            <field name="LENGTH" label="预留长度"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
    </ObjMeta>

    <!-- OCABLE 光缆 -->
    <ObjMeta objectType="OCABLE" autoReload="true"
             typeActions="add,reset,templates,remove,locates"
             itemActions="locateocable,modify,remove,filesman,cablesections,invokeLogicDiagram,invokeRouteDiagram,ocableOccpyRate"
             moreItemActions="sitefibers,associatedDevice,relateGT"
             extraActions="expgrid,exportAll,importTemplate,exportTemplate">
        <action name="templates" type="workspace" label="模板管理" title="光缆模板" objectType="OCABLE.TEMPLATE" icon="columns"/>
        <action name="layinput" label="敷设" mainConfig="config/gdo3/layinput.xml" urlParams="netId=${id}"/>
        <action name="sitefibers" type="workspace" label="局向光纤管理" label-en="site fiber management" title="${NAME}-局向光纤" title-en="${NAME}-Fiber"
                url="modules/cable/sitefiberManagement.html"/>
        <action name="ocableOccpyRate" type="window" class="small" label="缆线占用率" contentUrl="modules/cable/ocableOccpyRate.html"/>
        <action name="cablesections" type="workspace" objectType="OCABLESECTION" label="光缆段管理" label-en="Cable Segment Management"
                title="${NAME}-光缆段" title-en="${NAME} - Fiber cable segment"
                options='{"baseParams": {"net": "${id}"}, "showSearch": false, "autoLoad":true}'/>
        <action name="locateocable" type="script" label="定位光缆" label-en="Location" icon="map marker alternate">
            <![CDATA[
                var params = _actionContext.params;
				_context.doAction('locateNetCable', params);
			]]>
        </action>
        <action name="showDevices" type="script" label="查看光缆相关设备" url="modules/net/ocable/js/showOcableDeviceTree.js"/>
        <action name="showFacilities" type="script" label="查看光缆支撑设施" url="modules/net/ocable/js/showOcableFacilityTree.js"/>
        <action name="viewOcableDistribute" type="script" label="查看光缆分布" url="modules/net/ocable/js/viewOCableDistribute.js"/>
        <action name="exportOCableRoute" type="script" label="路由导出" url="modules/net/ocable/js/exportOCableRoute.js"/>
        <action name="associatedDevice" type="window" class="small" label="关联设备" contentUrl="modules/cable/associatedDevice.html"/>
        <!--广东的对象修改需要支持重新选择模板-->
        <action name="modify" label="修改" label-en="Modify" type="obj" icon="write" itemsable="true" showTemplates="true"/>
        <!--<action name="genCode" type="script">
            <![CDATA[
                new Promise((resolve, reject) => {
                    let cts = { '80202923': 'ZGG', '80202924': 'PXG', '80202925': 'ZJG', '80202926': 'LLG', '80202927': 'JNG', '80203774': 'LJG', '80203775': 'CTG', '80208181': 'PLG' };
                    let cableType = CABLE_TYPE_ID.value;
                    let ctType = cts[cableType];
                    let pre = '';
                    const _self = this;

                    switch (ctType)
                    {
                        case 'ZJG' :
                            // 中继光缆编码
                            _bean.query("SITE", { 'id': [adevice.TML_ID, zdevice.TML_ID], '_op.id': 'in' }).then(function (data) {
                                if (data.length < 2) {
                                    _sui.alert("A端或者Z端设备查询不到所属局站，请重新确认设备", "提示");
                                    return;
                                }
                                if (data[0].id == adevice.TML_ID) {
                                    pre = data[0].CODE + '-' + data[1].CODE + '/' + ctType;
                                } else {
                                    pre = data[1].CODE + '-' + data[0].CODE + '/' + ctType;
                                }
                                _bean.autoCode(_self, pre, 3, 'OCABLE', { CABLE_TYPE_ID: cableType, site: site.id }).then(function (value) {
                                    resolve(value);
                                });
                            });
                            break;
                        case 'LLG' :
                            // 联络光缆编码
                            if(!adevice || !adevice.CODE){
                                _sui.alert("请选择A端设备", "提示");
                                return;
                            }
                            if(!zdevice || !zdevice.CODE){
                                _sui.alert("请选择Z端设备", "提示");
                                return;
                            }
                            pre = adevice.CODE + '-' + zdevice.CODE + '/' + ctType;
                            _bean.autoCode(_self, pre, 3, 'OCABLE', { CABLE_TYPE_ID: cableType, site: site.id }).then(function (value) {
                                resolve(value);
                            });
                            break;
                        case 'JNG' :
                            // 局内光缆编码
                            _bean.query("FACILITY", { 'id': [adevice.FACILITY_ID, zdevice.FACILITY_ID], '_op.id': 'in' }).then(function (data) {
                                if (data.length < 2) {
                                    _sui.alert("A端或者Z端设备查询不到所属局站，请重新确认设备", "提示");
                                    return;
                                }
                                if (data[0].id == adevice.FACILITY_ID) {
                                    pre = data[0].CODE + '-' + data[1].CODE + '/' + ctType;
                                } else {
                                    pre = data[1].CODE + '-' + data[0].CODE + '/' + ctType;
                                }
                                _bean.autoCode(_self, pre, 3, 'OCABLE', { CABLE_TYPE_ID: cableType }).then(function (value) {
                                    resolve(value);
                                });
                            });
                            break;
                        case 'LJG' :
                            // 楼间光缆编码
                            _bean.query("FACILITY", { 'id': [adevice.FACILITY_ID, zdevice.FACILITY_ID], '_op.id': 'in' }).then(function (data) {
                                if (data.length < 2) {
                                    _sui.alert("A端或者Z端设备查询不到所属局站，请重新确认设备", "提示");
                                    return;
                                }
                                if (data[0].id == adevice.FACILITY_ID) {
                                    pre = data[0].CODE + '-' + data[1].CODE + '/' + ctType;
                                } else {
                                    pre = data[1].CODE + '-' + data[0].CODE + '/' + ctType;
                                }
                                _bean.autoCode(_self, pre, 3, 'OCABLE', { CABLE_TYPE_ID: cableType }).then(function (value) {
                                    resolve(value);
                                });
                            });
                            break;
                        case 'PLG' :
                            // 皮缆编码
                            pre = site.CODE + '/' + adevice.CODE + '/' + ctType;
                            _bean.autoCode(this, pre, 3, 'OCABLE', { CABLE_TYPE_ID: cableType, site: site.id }).then(function (value) {
                                resolve(value);
                            });
                            break;
                        default :
                            // 其他默认编码
                            pre = site.CODE + '/' + ctType;
                            _bean.autoCode(this, pre, 3, 'OCABLE', { CABLE_TYPE_ID: cableType, site: site.id }).then(function (value) {
                                resolve(value);
                            });
                    }
                });
            ]]>
        </action>-->
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" />
            </row>
            <row>
                <field name="LENGTH" label="长度"/>
                <field name="TOPO_ID" label="拓扑结构"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" />
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="CABLE_TYPE_ID" label="光缆类型"/>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县"/>
                <field name="marketingarea" label="划小营销区"/>
            </row>
            <row>
                <field name="region" label="所属区域"/>
                <field name="OPTICAL_CABLE_CARDS_NO" label="光缆牌号"/>
            </row>
            <row>
                <field name="project" label="工程"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="入库时间"/>
            </row>
        </form>
        <!--新增面板-->
        <form name="add" GEN_CODE_TEMPLATE="${site.CODE}/${CABLE_TYPE_ID.CODE}${sn3}" GEN_NAME_TEMPLATE="${site.CODE}/${CABLE_TYPE_ID.CODE}${sn3}">

            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CABLE_TYPE_ID" label="光缆类型" required="true"/>
                <field name="OPTICAL_CABLE_CARDS_NO" label="光缆牌号"/>
            </row>
            <row>
                <field name="site" label="所属局站" required="true" onChange="region=site.REGION_ID" dropdownOptions="titleField:NAME"/>
                <field name="region" label="所属区域" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="adevice" label="起始设备"
                       baseParams='{"SPEC_ID":"IDF,ODF,GJ,ZHX,GF,GB,GRESERVE","excludeTemplate":"true"}' dropdownOptions="titleField:NAME"/>
                <field name="zdevice" label="终止设备"
                       baseParams='{"SPEC_ID":"IDF,ODF,GJ,ZHX,GF,GB,GRESERVE","excludeTemplate":"true"}' dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" required="true" label="光缆编码"/>
            </row>
            <row>
                <field name="NAME" required="true" label="光缆名称"/>
            </row>
            <row>
                <field name="LENGTH" label="光缆皮长(m)"/>
                <field name="MAX_FIBER" label="纤芯数" required="true"/>
            </row>
            <row>
                <field name="MAP_LENGTH" label="地图长度"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="OPTICAL_CABLE_MODEL_ID" label="光纤模式" required="true" default="100196"/>
                <field name="TOPO_ID" label="拓扑结构" required="true" default="80207209"/>
            </row>
            <row>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
                <field name="BREVITY_CODE" label="简码"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="BUILDWAY" label="建设方式" default="80204961"/>
                <field name="IS_SCENESAME_ID" label="是否与现场一致"/>
            </row>
            <row>
                <field name="LAY_FASHION_ID" label="敷设方式" default="100143"/>
                <field name="DEPRECIATION" label="折旧年限(年)"/>
            </row>
            <row>
                <field name="HIREPIPEYEAR" label="租用管道年限"/>
                <field name="HIREPIPECOMPACT" label="租用管道合同书"/>
            </row>
            <row>
                <field name="IS_IMPORTANT_ID" label="是否重要光缆"/>
                <field name="NOTES" label="备注"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;PROJECT_STATUS_ID=project.PROJECT_STATUS_ID;project_states=project.CM_SINGLE_PROJECT_STATUS_ID;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" readOnly="true" default="80204666"/>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                 <field name="ASSET_CODE" label="资产编码"/>
                 <field name="CHILD_ASSCARD_CODE" label="子资产卡片编码"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="100077"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="100063"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" required="true" default="102602"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!--修改面板-->
        <form name="modify" GEN_CODE_TEMPLATE="${site.CODE}/${CABLE_TYPE_ID.CODE}${sn3}" GEN_NAME_TEMPLATE="${site.CODE}/${CABLE_TYPE_ID.CODE}${sn3}">

            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CABLE_TYPE_ID" label="光缆类型" required="true"/>
                <field name="OPTICAL_CABLE_CARDS_NO" label="光缆牌号"/>
            </row>
            <row>
                <field name="site" label="所属局站" required="true" onChange="region=site.REGION_ID" dropdownOptions="titleField:NAME"/>
                <field name="region" label="所属区域" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="adevice" label="起始设备"
                       baseParams='{"SPEC_ID":"IDF,ODF,GJ,ZHX,GF,GB,GRESERVE","excludeTemplate":"true"}'/>
                <field name="zdevice" label="终止设备"
                       baseParams='{"SPEC_ID":"IDF,ODF,GJ,ZHX,GF,GB,GRESERVE","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="CODE" required="true" label="光缆编码"/>
            </row>
            <row>
                <field name="NAME" required="true" label="光缆名称"/>
            </row>
            <row>
                <field name="LENGTH" label="光缆皮长(m)"/>
                <field name="MAX_FIBER" label="纤芯数" required="true"/>
            </row>
            <row>
                <field name="MAP_LENGTH" label="地图长度"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" />
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="OPTICAL_CABLE_MODEL_ID" label="光纤模式" required="true" />
                <field name="TOPO_ID" label="拓扑结构" required="true" />
            </row>
            <row>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
                <field name="BREVITY_CODE" label="简码"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" />
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="BUILDWAY" label="建设方式" />
                <field name="IS_SCENESAME_ID" label="是否与现场一致"/>
            </row>
            <row>
                <field name="LAY_FASHION_ID" label="敷设方式" />
                <field name="DEPRECIATION" label="折旧年限(年)"/>
            </row>
            <row>
                <field name="HIREPIPEYEAR" label="租用管道年限"/>
                <field name="HIREPIPECOMPACT" label="租用管道合同书"/>
            </row>
            <row>
                <field name="IS_IMPORTANT_ID" label="是否重要光缆"/>
                <field name="NOTES" label="备注"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_states=project.CM_SINGLE_PROJECT_STATUS_ID;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" readOnly="true"/>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="ASSET_CODE" label="资产编码"/>
                <field name="CHILD_ASSCARD_CODE" label="子资产卡片编码"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!--批量修改表格-->
        <form name="batchmodify">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CABLE_TYPE_ID" label="光缆类型" />
                <field name="OPTICAL_CABLE_CARDS_NO" label="光缆牌号"/>
            </row>
            <row>
                <field name="site" label="所属局站" onChange="region=site.REGION_ID" dropdownOptions="titleField:NAME"/>
                <field name="region" label="所属区域" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="adevice" label="起始设备"
                       baseParams='{"SPEC_ID":"IDF,ODF,GJ,ZHX,GF,GB,GRESERVE","excludeTemplate":"true"}'/>
                <field name="zdevice" label="终止设备"
                       baseParams='{"SPEC_ID":"IDF,ODF,GJ,ZHX,GF,GB,GRESERVE","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="CODE" label="光缆编码" readOnly="true"/>
            </row>
            <row>
                <field name="NAME" label="光缆名称" readOnly="true"/>
            </row>
            <row>
                <field name="LENGTH" label="光缆皮长(m)"/>
                <field name="MAX_FIBER" label="纤芯数" />
            </row>
            <row>
                <field name="MAP_LENGTH" label="地图长度"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" readOnly="true"/>
            </row>
            <row>
                <field name="OPTICAL_CABLE_MODEL_ID" label="光纤模式" />
                <field name="TOPO_ID" label="拓扑结构" />
            </row>
            <row>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
                <field name="BREVITY_CODE" label="简码"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" />
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="BUILDWAY" label="建设方式" />
                <field name="IS_SCENESAME_ID" label="是否与现场一致"/>
            </row>
            <row>
                <field name="LAY_FASHION_ID" label="敷设方式" />
                <field name="DEPRECIATION" label="折旧年限(年)"/>
            </row>
            <row>
                <field name="HIREPIPEYEAR" label="租用管道年限"/>
                <field name="HIREPIPECOMPACT" label="租用管道合同书"/>
            </row>
            <row>
                <field name="IS_IMPORTANT_ID" label="是否重要光缆"/>
                <field name="NOTES" label="备注"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_states=project.CM_SINGLE_PROJECT_STATUS_ID;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称" readOnly="true"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" readOnly="true"/>
                <field name="project_serialno" label="工程流水号" readOnly="true"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" />
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" />
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="ASSET_CODE" label="资产编码"/>
                <field name="CHILD_ASSCARD_CODE" label="子资产卡片编码"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" multiple="true" />
                <field name="PROPERTY_OWNER_ID" label="产权归属" multiple="true" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <form name="searchCable">
            <row>
                <field name="CODE" label="光缆编码" required="false"/>
                <field name="NAME" label="光缆名称" required="false"/>
                <field name="CABLE_TYPE_ID"/>
            </row>
            <row>
                <field name="site" required="false"/>
                <field name="OPTICAL_CABLE_CARDS_NO"/>
                <field name="project" label="所属工程"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:6000px;" autoLoad="false" baseParams='{"_orderBy":"CODE"}'>
            <field name="CODE" label="光缆编码"/>
            <field name="NAME" label="光缆名称" width="250px"/>
            <field name="CABLE_TYPE_ID" label="光缆类型"/>
            <field name="IS_IMPORTANT_ID" label="是否重要光缆"/>
            <field name="site" label="所属辖局" getLabel="row.site_value.NAME"/>
            <field name="LENGTH" label="长度"/>
            <field name="TOPO_ID" label="拓扑结构"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="OPTICAL_CABLE_CARDS_NO" label="光缆牌号"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="OPTICAL_FIBER_MODE_ID" label="光纤模式"/>
            <field name="MAX_FIBER" label="纤芯数"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            <field name="MAP_LENGTH" label="地图长度"/>
            <field name="NOTES" label="备注"/>
            <field name="ASSET_CODE" label="资产编码"/>
            <field name="CREATOR" label="录入人"/>
            <field name="MODIFIER" label="修改人"/>
            <field name="PROPERTY_TYPE_ID" label="产权性质"/>
            <field name="PROPERTY_OWNER_ID" label="产权归属"/>
            <field name="MNT_LEVEL_ID" label="维护等级"/>
            <field name="project_value.CODE" label="所属工程"/>
            <field name="CREATE_DATE" label="入库时间"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
        </grid>
        <grid name="viewOCableDistribution" rowDetailField="false" toolbar="false">
            <field name="ADEV" label="起始设备编码"/>
            <field name="ACARD" label="起始设备框号"/>
            <field name="APORT" label="起始设备端子"/>
            <field name="FIBERCODE" label="成端纤序"/>
            <field name="ZDEV" label="终止设备编码"/>
            <field name="ZCARD" label="终止设备框号"/>
            <field name="ZPORT" label="终止设备端子"/>
            <field name="ADDR" label="具体位置"/>
        </grid>
        <grid name="ThirdCable" autoLoad="false" rowDetailField="false" filterable="false" sortable="false">
            <field name="CODE" label="光缆编码"/>
            <field name="NAME" label="光缆名称"/>
            <field name="OCABLE_COLOR" label="显示颜色" maxWidth="70px"/>
            <field name="site"/>
            <field name="site_value.NAME" label="局站名称"/>
            <field name="CABLE_TYPE_ID"/>
            <field name="project"/>
            <field name="LONG_LOCAL_ID" />
            <field name="LENGTH" />
            <field name="MAX_FIBER"/>
            <field name="LAY_FASHION_ID"/>
        </grid>
        <!-- 缆段占用率表单 -->
        <grid name="cableOccpyRate" autoLoad="false" rowDetailField="false">
            <field name="METACATEGORY" label="缆线类型"/>
            <field name="NAME" label="缆线名称"/>
            <field name="CODE" label="缆线编码"/> 
            <field name="LINESUM" label="纤芯数"/>
            <field name="PAIRUSE" label="纤芯占用数"/>
            <field name="PAIRFREE" label="纤芯空闲数"/>
            <field name="USERATE" label="占用率" />
            <field name="FREERATE" label="空闲率" />
        </grid>
        <grid name="FTTXOCABLE" filterable="false" sortable="false" tableStyle="width:900px">
            <field name="CODE" label="光缆编码"/>
            <field name="NAME" label="光缆名称" width="250px"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value ? row.smallcounty_value.NAME : ''"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value ? row.marketingarea_value.NAME : ''"/>
            <field name="servicearea" label="划小营业区" getLabel="row.servicearea_value ? row.servicearea_value.NAME : ''"/>
        </grid>
        <grid name="FTTX10OCABLE" filterable="false" sortable="false">
            <field name="CODE" label="光缆编码"/>
            <field name="NAME" label="光缆名称"/>
            <field name="CABLE_TYPE_ID" label="光缆级别"/>
        </grid>
    </ObjMeta>
    <!-- 光缆模板 -->
    <ObjMeta objectType="OCABLE.TEMPLATE" autoReload="true" labelField="NAME"
             typeActions="add,remove"
             itemActions="modify,remove,filesman">
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="模板名称" required="true"/>
            </row>
        </form>
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="光缆名称" required="true"/>
            </row>
            <row>
                <field name="BREVITY_CODE" label="简码"/>
                <field name="CAPACITY" label="标称容量"/>
            </row>
            <row>
                <field name="CABLE_TYPE_ID" label="光缆类型" required="true"/>
                <field name="TOPO_ID" label="拓扑结构" required="true"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="OPTICAL_CABLE_CARDS_NO" label="光缆牌号"/>
                <field name="OPTICAL_FIBER_MODE_ID" label="光纤模式"/>
            </row>
            <row>
                <field name="LENGTH" label="长度(米)"/>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="BUILDWAY" label="建设方式"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
                <field name="region" label="所属区域"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" multiple="true" default="100077"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" multiple="true" default="100063"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="MAX_FIBER" label="纤芯数"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:1390;" autoLoad="false">
            <field name="NAME" label="模板名称" width="250px"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
    </ObjMeta>

    <!-- OCABLESECTION 光缆段 -->
    <ObjMeta objectType="OCABLESECTION" needgeom="false" autoReload="true"
             typeActions="add,reset,templates,remove,locates"
             itemActions="locate,modify,remove,fibers,filesman,assetAttribute,clearLaying"
             moreItemActions="layingByCable,layingByGrid,ocablecoilsByCable,ocableOccpyRate,greservesByCable,chromatographic,logFiberLengthUpdate"
             extraActions="expgrid,exportAll,importTemplate,exportTemplate">
        <action type="workspace" name="templates" label="{{g5.template.manage}}" title="光缆段模板"
                objectType="OCABLESECTION.TEMPLATE" icon="columns"/>
        <action type="workspace" name="fibers" objectType="FIBER" label="纤芯管理" label-en="Optical cable core management"
                title="${NAME}-纤芯" title-en="${NAME}-fiber" options='{"baseParams": {"ocablesection": "${id}"},"showSearch":false,"pagable": false,"gridName":"qxglfibergrid","queryActionName":"queryFiberByOcableSectionIdGdy"}'
                permission="OPTICAL_FIND"/>
        <action name="layingGridByCable" label="路由表格" label-en="Routing table" title="路由-${NAME}"
                title-en="Route - ${NAME}" type="window" contentUrl="modules/cable/layingGridByCable.html"
                permission="OPTICAL_FIND"/>
        <action name="ocableOccpyRate" type="window" class="small" label="缆线占用率" contentUrl="modules/cable/ocableOccpyRate.html"/>
        <action name="chromatographic" label="色谱管理" label-en="Chromatographic" type="workspace"
                mainConfig="modules/cable/chromatographic.xml" urlParams="id=${id}"/>
        <action name="abilityAndBusiness" type="workspace" class="large" label="能力和业务展示" title="${NAME}-能力和业务展示"
                mainConfig="apps/pipe/pipeline/abilityandbusiness/abilityAndBusiness.xml"
                urlParams="ocablesectionId=${id}&amp;type=ocablesection"/>
        <action type="window" name="relevantFacility" label="相关支撑设施" label-en="Relevant Supporting Facilities"
                title="${NAME}-相关支撑设施" title-en="${NAME} -Relevant Supporting Facilities"
                contentUrl="modules/cable/cableRelatedEntity.html"/>
        <action name="oneclick_deletecs" label="一键删除" label-en="onedelete" type="script"
                url="modules/onedelete/onedeletecable.js"/>
        <action type="window" name="ocablecoilsByCable" label="光缆盘留" title="光缆盘留-${NAME}" objectType="OCABLECOIL"
                width="800" height="600"
                options='{"showSearch": false, "actions": "", "extraActions": "expgrid", "pagable": false, "baseParams": {"cable":"${id}"},"autoLoad": true}'/>
        <action type="window" name="greservesByCable" label="光缆预留" title="光缆预留-${NAME}" objectType="GRESERVE"
                width="800" height="600"
                options='{"showSearch": false, "actions": "", "extraActions": "expgrid", "pagable": false,
		                  "baseParams": {"ocablesection":"${id}"}, "autoLoad": true}'/>
        <action type="script" name="logFiberLengthUpdate" label="局纤长度更新" url="apps/gdo3/link/logFiberLengthUpdate.js"/>
        <!--广东的对象修改需要支持重新选择模板-->
        <action name="modify" label="修改" label-en="Modify" type="obj" icon="write" itemsable="true" showTemplates="true"/>
        <!--<action name="genCode" type="script">
            <![CDATA[

                let pre = net.CODE + '/';
                _bean.autoCode(this, pre, 3, 'OCABLESECTION', { net: net.id });
            ]]>
        </action>
        <action name="genCode2" type="script">
            <![CDATA[
                let cts = { '80202923': 'ZGG', '80202924': 'PXG', '80202925': 'ZJG', '80202926': 'LLG', '80202927': 'JNG', '80203774': 'LJG', '80203775': 'CTG', '80208181': 'PLG' };
                let cableType = net.CABLE_TYPE_ID_value || net.CABLE_TYPE_ID;
                let ctType = cts[cableType];
                let pre;
                switch (ctType) {
                    case 'PXG':
                        // 配线光缆段名称
                        pre = adevice.NAME + '-' + zdevice.NAME + '/' + net.CODE + '/';
                        _bean.autoCode(this, pre, 3, 'OCABLESECTION', { net: net.id, CABLE_TYPE_ID: cableType });
                        break;
                    default:
                        // 其他默认名称
                        pre = net.NAME + '/';
                        _bean.autoCode(this, pre, 3, 'OCABLESECTION', { net: net.id });
                }
            ]]>
        </action>-->
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME"/>
                <field name="CABLE_TYPE_ID" label="缆线类型"/>
            </row>
            <row>
                <field name="OPTICAL_CABLE_CARDS_NO" label="光缆段牌号"/>
                <field name="CAPACITY" label="纤芯数"/>
            </row>
            <row>
                <field name="adevice" label="A端设备"
                       baseParams='{"SPEC_ID":"ODF,GJ,GF,GB,OBD,GT,IDF,ZHX,CABLECOIL,GRESERVE,XBOX,HUBBOX","excludeTemplate":"true"}'/>
                <field name="zdevice" label="Z端设备"
                       baseParams='{"SPEC_ID":"ODF,GJ,GF,GB,OBD,GT,IDF,ZHX,CABLECOIL,GRESERVE,XBOX,HUBBOX","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县"/>
                <field name="marketingarea" label="划小营销区"/>
            </row>
            <row>
                <field name="region" label="所属区域"/>
                <field name="CREATOR" label="录入人"/>
            </row>
            <row>
                <field name="project" label="工程"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_SHARE_ID" label="共享信息"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="入库时间"/>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form GEN_CODE_TEMPLATE="${net.CODE}/${sn3}" GEN_NAME_TEMPLATE="${adevice.NAME}==${zdevice.NAME}==${CABLE_TYPE_ID.name}==${net.NAME}/${sn3}">

            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="光缆段编码" required="true"/>
            </row>
            <row>
                <field name="NAME" label="光缆段名称" required="true"/>
            </row>
            <row>
                <field name="OPTICAL_CABLE_CARDS_NO" label="光缆段牌号"/>
            </row>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="net" label="所属光缆" required="true" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="adevice" label="A端设备" required="true" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"ODF,GJ,GF,GB,OBD,GT,IDF,ZHX,CABLECOIL,GRESERVE,XBOX,HUBBOX","excludeTemplate":"true"}'/>
                <field name="zdevice" label="Z端设备" required="true" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"ODF,GJ,GF,GB,OBD,GT,IDF,ZHX,CABLECOIL,GRESERVE,XBOX,HUBBOX","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="LENGTH" label="实际长度(m)" required="true"/>
                <field name="MAP_LENGTH" label="地图长度"/>
            </row>
            <row>
                <field name="CAPACITY" label="纤芯数" required="true"/>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" readOnly="true" default="80204666"/>
            </row>
            <row>
                <field name="CABLE_TYPE_ID" label="缆线类型" required="true" readOnly="true"/>
                <field name="OPTSEGSCORE" label="光缆段分值"/>
            </row>
            <row>
                <field name="CABLE_STRUCTURE_ID" label="光缆结构" default="80203904"/>
                <field name="MNT_LEVEL_ID" label="光缆等级" required="true" default="102602"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="LAYING_WAY_ID" label="敷设方式"/>
            </row>
            <row>
                <field name="IS_RESERVATION_MARK_ID" label="预留标识"/>
                <field name="RESERVENUM" label="预留芯数"/>
            </row>
            <row>
                <field name="RENTEDTIME" label="租用到期时间"/>
                <field name="RENTEDUNIT" label="租借单位"/>
            </row>
            <row>
                <field name="IS_SCENESAME_ID" label="是否与现场一致"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="IS_LAY_ID" label="是否敷设"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_SHARE_ID" label="共享信息"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="OPTICAL_FIBER_TYPE_ID" label="光纤类型" default="100188"/>
                <field name="OPTICAL_FIBER_MODE_ID" label="光纤模式"/>
            </row>
            <row>
                <field name="FIBERTYPE" label="成束类型"/>
            </row>
            <row>
                <field name="FIBERNUM" label="每束芯数"/>
                <field name="ROW_NUM" label="每束行数"/>
            </row>
            <row>
                <field naname="COL_NUM" label="每束列数"/>
                <field name="AVER_ATTENUATION" label="平均衰减"/>
            </row>
            <row>
                <field name="WORK_WAY_ID" label="工作方式"/>
                <field name="WORK_STATE_ID" label="工作状态"/>
            </row>
            <row>
                <field name="COIL_POSITION" label="盘留位置"/>
                <field name="DIAMETER" label="直径"/>
            </row>
            <row>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护部门"/>
                <field name="AGENT_MNT_COMPANY" label="代维单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            </row>
            <row>
                <field name="START_USE_DATE" label="启用时间"/>
                <field name="STOP_USE_DATE" label="停用日期"/>
            </row>
            <row>
                <field name="AGENT_MNT_CONTACT" label="代维联系人"/>
                <field name="AGENT_MNT_PHONE" label="代维电话"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="CHECKING_PERSON" label="检查责任人"/>
            </row>

            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!--批量修改表格-->
        <form name="batchmodify">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="光缆段编码" readOnly="true"/>
            </row>
            <row>
                <field name="NAME" label="光缆段名称" readOnly="true"/>
            </row>
            <row>
                <field name="OPTICAL_CABLE_CARDS_NO" label="光缆段牌号"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME" readOnly="true"/>
                <field name="net" label="所属光缆" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="adevice" label="A端设备" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"ODF,GJ,GF,GB,OBD,GT,IDF,ZHX,CABLECOIL,GRESERVE,XBOX,HUBBOX","excludeTemplate":"true"}'/>
                <field name="zdevice" label="Z端设备" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"ODF,GJ,GF,GB,OBD,GT,IDF,ZHX,CABLECOIL,GRESERVE,XBOX,HUBBOX","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="LENGTH" label="实际长度(m)" />
                <field name="MAP_LENGTH" label="地图长度"/>
            </row>
            <row>
                <field name="CAPACITY" label="纤芯数" />
                <field name="PROJECT_STATUS_ID" label="工程状态" readOnly="true" />
            </row>
            <row>
                <field name="CABLE_TYPE_ID" label="缆线类型" readOnly="true"/>
                <field name="OPTSEGSCORE" label="光缆段分值"/>
            </row>
            <row>
                <field name="CABLE_STRUCTURE_ID" label="光缆结构" />
                <field name="MNT_LEVEL_ID" label="光缆等级" />
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" />
                <field name="LAYING_WAY_ID" label="敷设方式"/>
            </row>
            <row>
                <field name="IS_RESERVATION_MARK_ID" label="预留标识"/>
                <field name="RESERVENUM" label="预留芯数"/>
            </row>
            <row>
                <field name="RENTEDTIME" label="租用到期时间"/>
                <field name="RENTEDUNIT" label="租借单位"/>
            </row>
            <row>
                <field name="IS_SCENESAME_ID" label="是否与现场一致"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="IS_LAY_ID" label="是否敷设"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_SHARE_ID" label="共享信息"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" readOnly="true"/>
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="OPTICAL_FIBER_TYPE_ID" label="光纤类型" />
                <field name="OPTICAL_FIBER_MODE_ID" label="光纤模式"/>
            </row>
            <row>
                <field name="FIBERTYPE" label="成束类型"/>
            </row>
            <row>
                <field name="FIBERNUM" label="每束芯数"/>
                <field name="ROW_NUM" label="每束行数"/>
            </row>
            <row>
                <field naname="COL_NUM" label="每束列数"/>
                <field name="AVER_ATTENUATION" label="平均衰减"/>
            </row>
            <row>
                <field name="WORK_WAY_ID" label="工作方式"/>
                <field name="WORK_STATE_ID" label="工作状态"/>
            </row>
            <row>
                <field name="COIL_POSITION" label="盘留位置"/>
                <field name="DIAMETER" label="直径"/>
            </row>
            <row>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称" readOnly="true"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号" readOnly="true"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" />
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" />
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护部门"/>
                <field name="AGENT_MNT_COMPANY" label="代维单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            </row>
            <row>
                <field name="START_USE_DATE" label="启用时间"/>
                <field name="STOP_USE_DATE" label="停用日期"/>
            </row>
            <row>
                <field name="AGENT_MNT_CONTACT" label="代维联系人"/>
                <field name="AGENT_MNT_PHONE" label="代维电话"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="CHECKING_PERSON" label="检查责任人"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="net" label="所属缆"/>
            <field name="OPTICAL_FIBER_TYPE_ID" label="缆线类型"/>
            <field name="LENGTH" label="长度"/>
            <field name="CAPACITY" label="纤芯数"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:6000px;" autoLoad="false" baseParams='{"_orderBy":"CODE"}'>
            <field name="CODE" label="光缆段编码"/>
            <field name="NAME" label="光缆段名称" width="250px"/>
            <field name="site" label="所属辖局" getLabel="row.site_value.NAME"/>
            <field name="net" label="所属光缆" getLabel="row.net_value.NAME"/>
            <field name="LENGTH" label="长度"/>
            <field name="CAPACITY" label="纤芯数"/>
            <field name="freeFiberCount" label="空闲纤芯数"/>
            <field name="useFiberCount" label="非空闲纤芯数" getLabel="row.CAPACITY - row.freeFiberCount"/>
            <field name="LAYING_WAY_ID" label="敷设方式"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="adevice" label="A端设备"/>
            <field name="adevice_value.METACATEGORYCN" label="起始设备（类型）" width="100px"/>
            <field name="zdevice" label="Z端设备"/>
            <field name="zdevice_value.METACATEGORYCN" label="终止设备（类型）" width="100px"/>
            <field name="region" label="所属区域"  getLabel="row.region_value.NAME"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            <field name="CABLE_STRUCTURE_ID" label="光缆结构"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MAP_LENGTH" label="地图长度"/>
            <field name="NOTES" label="备注"/>
            <field name="OPTICAL_CABLE_CARDS_NO" label="光缆段牌号"/>
            <field name="OPTICAL_CABLE_CARDS_NO_BY_OCABLE" label="所属光缆牌号" getLabel="row.net_value.OPTICAL_CABLE_CARDS_NO"/>
            <field name="OPTICAL_FIBER_TYPE_ID" label="光纤类型"/>
            <field name="OPTICAL_FIBER_MODE_ID" label="光纤模式"/>
            <field name="MNT_LEVEL_ID" label="维护等级"/>
            <field name="ASSET_CODE" label="资产编码"/>
            <field name="CREATOR" label="录入人"/>
            <field name="MODIFIER" label="修改人"/>
            <field name="AUDIT_PERSON" label="验收人"/>
            <field name="PROPERTY_TYPE_ID" label="产权性质"/>
            <field name="PROPERTY_OWNER_ID" label="产权归属"/>
            <field name="MNT_PERSON" label="维护人"/>
            <field name="MNT_DEPT" label="维护单位"/>
            <field name="AGENT_MNT_COMPANY" label="代维单位"/>
            <field name="AGENT_MNT_CONTACT" label="代维联系人"/>
            <field name="AGENT_MNT_PHONE" label="代维电话"/>
            <field name="project_value.CODE" label="所属工程"/>
            <field name="LIFE_STATE_ID" label="生命周期状态"/>
            <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="CREATE_DATE" label="入库时间"/>
            <field name="vendor" label="生产厂家"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
        </grid>
        <!-- 资源关联光缆段的列表 -->
        <grid name="resourcesRelationOcablesectionGrid" toolbar="false" footer="false" rowCheckable="false" showRowNum="true" typeActions=""
              itemActions="fibers">
            <field name="CODE"/>
            <field name="NAME"/>
        </grid>

        <!-- 资源关联光缆段的列表 -->
        <grid name="connPortGrid" showRowNum="true" rowCheckWidth="68px">
            <field name="CODE" width="200px"/>
            <field name="NAME" width="150px"/>
            <field name="OPTICAL_CABLE_CARDS_NO" width="200px"/>
            <field name="CAPACITY" label="纤芯数" width="50px"/>
            <field name="adevice.NAME" label="A端设备名称" width="150px"/>
            <field name="zdevice.NAME" label="Z端设备名称" width="150px"/>
        </grid>

        <grid name="connRGCDGrid" showRowNum="true" rowCheckWidth="48px">
            <field name="CSCODE" width="200px" label="光缆段编码"/>
            <field name="CARDNAME" label="模块号" width="50px"/>
            <field name="FSSEQ" label="纤芯序号" width="150px"/>
            <field name="CONNSEQ" label="端子序号" width="150px"/>
        </grid>
        <grid name="connCDGrid" showRowNum="true" rowCheckWidth="68px">
            <field name="CSCODE" width="200px" label="光缆段编码"/>
            <field name="FIBERSEQ" label="纤芯序号" width="60px"/>
            <field name="CARDNAME" label="模块号" width="60px"/>
            <field name="CONNCODE" label="端子号" width="70px"/>
            <field name="CONNSEQ" label="端子序号" width="60px"/>
            <field name="CONNRN" label="端子行号" width="60px"/>
            <field name="CONNCN" label="端子列号" width="60px"/>
            <field name="CONNSTATE" label="端子状态" width="60px"/>
            <field name="OPTICALCODE" label="光路编码" width="100px"/>
            <field name="SERVICENO" label="接入号" width="200px"/>
            <field name="CUSTOMERNAME" label="客户名称" width="100px"/>
            <field name="SINGLETYPE" label="业务类型" width="60px"/>
        </grid>
        <grid toolbar="false" rowDetailField="false" name="opticGroupLocate">
            <field name="GROUP" label="组别"/>
            <field name="OPTIC_CODES" label="同光缆段的光路编码"/>
            <field name="LENGTH" label="同光缆段长度总计"/>
        </grid>
        <grid name="FTTXOCABLESECTION" filterable="false" sortable="false" tableStyle="width:2000px">
            <field name="CODE" label="光缆段编码"/>
            <field name="NAME" label="光缆段名称" width="250px"/>
            <field name="LENGTH" label="长度" width="100px"/>
            <field name="OPTICAL_CABLE_CARDS_NO" label="牌号" width="100px"/>
            <field name="adeviceCategory" label="起始设备类型" getLabel="row.adevice_value ? row.adevice_value.METACATEGORYCN : ''" width="100px"/>
            <field name="adeviceCode" label="起始设备编码" getLabel="row.adevice_value ? row.adevice_value.CODE : ''"/>
            <field name="zdeviceCategory" label="终止设备类型" getLabel="row.zdevice_value ? row.zdevice_value.METACATEGORYCN : ''" width="100px"/>
            <field name="zdeviceCode" label="终止设备编码" getLabel="row.zdevice_value ? row.zdevice_value.CODE : ''"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value ? row.smallcounty_value.NAME : ''"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value ? row.marketingarea_value.NAME : ''"/>
            <field name="servicearea" label="划小营业区" getLabel="row.servicearea_value ? row.servicearea_value.NAME : ''"/>
        </grid>
        <grid name="FTTX10OCABLESECTION" filterable="false" sortable="false">
            <field name="ocableCode" label="光缆编码" getLabel="row.net_value ? row.net_value.CODE : ''"/>
            <field name="CODE" label="光缆段编码"/>
            <field name="NAME" label="光缆段名称"/>
            <field name="adeviceCode" label="A端设备编码" getLabel="row.adevice_value ? row.adevice_value.CODE : ''"/>
            <field name="zdeviceCode" label="Z端设备编码" getLabel="row.zdevice_value ? row.zdevice_value.CODE : ''"/>
            <field name="CAPACITY" label="纤芯数"/>
        </grid>
    </ObjMeta>
    <!-- 光缆段模板 -->
    <ObjMeta objectType="OCABLESECTION.TEMPLATE" autoReload="true" labelField="NAME"
             typeActions="add,remove"
             itemActions="modify,remove,filesman">
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="模板名称" required="true"/>
            </row>
        </form>
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="光缆段名称" required="true"/>
            </row>
            <row>
                <field name="CABLE_STRUCTURE_ID" label="光缆结构"/>
            </row>
            <row>
                <field name="typecable" label="型号"/>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="LAYING_WAY_ID" label="敷设方式"/>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
            </row>
            <row>
                <field name="CABLE_TYPE_ID" label="光缆类型"/>
                <field name="MAP_LENGTH" label="地图长度"/>
            </row>
            <row>
                <field name="CAPACITY" label="纤芯数"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
                <field name="region" label="所属区域"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="DIAMETER" label="直径"/>
                <field name="OPTICAL_CABLE_CARDS_NO" label="光缆段牌号"/>
            </row>
            <row>
                <field name="OPTICAL_FIBER_TYPE_ID" label="光纤类型"/>
                <field name="OPTICAL_FIBER_MODE_ID" label="光纤模式"/>
            </row>
            <row>
                <field name="FIBER_USED" label="已用纤芯"/>
                <field name="FIBERTYPE" label="成束类型"/>
            </row>
            <row>
                <field name="FIBERNUM" label="每束芯数" required="true"/>
                <field name="ROW_NUM" label="每束行数"/>
            </row>
            <row>
                <field naname="COL_NUM" label="每束列数"/>
                <field name="AVER_ATTENUATION" label="平均衰减"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
                <field name="COIL_POSITION" label="盘留位置"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护部门"/>
                <field name="AGENT_MNT_COMPANY" label="代维单位"/>
            </row>
            <row>
                <field name="AGENT_MNT_CONTACT" label="代维联系人"/>
                <field name="AGENT_MNT_PHONE" label="代维电话"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="CHECKING_PERSON" label="检查责任人"/>
            </row>
            <row>
                <field name="START_USE_DATE" label="启用时间"/>
                <field name="STOP_USE_DATE" label="停用日期"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:1390;" autoLoad="false">
            <field name="NAME" label="模板名称" width="250px"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
    </ObjMeta>

    <!--XBOX 光交箱 -->
    <ObjMeta objectType="XBOX" autoReload="true" needgeom="true"
             typeActions="add,reset,remove"
             itemActions="locate,modify,remove,connview,portDiagram,chengduan,filesman,invokeLogicDiagram,invokeRouteDiagram">
        <action type="workspace" name="templates" label="模板管理" label-en="Template management" title="XBOX光交箱模板"
                title-en="FDB Template"
                objectType="XBOX_TEMPLATE" icon="columns"/>
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="region" label="所属区域"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县"/>
                <field name="marketingarea" label="划小营销区"/>
            </row>
            <row>
                <field name="project" label="工程"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="入库时间"/>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
                <field name="ADDRESS_ID" label="地址ID"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" required="true" baseParams="{&quot;_solr&quot;:true}"
                       dropdownOptions="titleField:FULL_NAME"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" readOnly="true" default="80204666"/>
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="model" label="型号"/>
                <field name="DISTRICT" label="所属小区"/>
            </row>
            <row>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
                <field name="DEPRECIATION" label="折旧年限（年）"/>
            </row>
            <row>
                <field name="IS_SCENESAME_ID" label="是否与现场一致"/>
                <field name="APPLY_NET_ID" label="网络结点类型"/>
            </row>
            <row>
                <field name="ASSIGNED_TOTAL" label="满配OBD数"/>
                <field name="ASSIGNED_USED" label="已配OBD数" readOnly="true"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="POS_X" label="原X坐标" readOnly="true"/>
                <field name="POS_Y" label="原Y坐标" readOnly="true"/>
            </row>
            <row>
                <field name="DATA_SOURCE_ID" label="数据来源" default="80401043"/>
                <field name="IS_GREEN_POSITION" label="位置准确绿标"/>
            </row>
            <row>
                <field name="V_CONTRACTOR" queryAssemble="V_CONTRACTOR" label="承包人姓名"/>
                <field name="V_CONTRACTOR_ACC" queryAssemble="V_CONTRACTOR_ACC" label="承包人账号"/>
            </row>
            <row>
                <field name="V_STICKERLABEL" queryAssemble="V_STICKERLABEL" label="扫描结果"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="CAPACITY" label="标称容量" required="true"/>
            </row>
            <row>
                <field name="CONFIG_TYPE_ID" label="配置点类型" default="80203622"/>
                <field name="IS_INTEGFLAG" label="一体化标志"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="ADAPTTYPE_ID" label="适配类型"/>
            </row>
            <row>
                <field name="bestroom" label="最优机房"/>
                <field name="bestobd" label="最优OBD"/>
            </row>
            <row>
                <field name="BELONG_SPECIALITY_ID" label="所属专业" readOnly="true" default="80204675"/>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:6000px;" autoLoad="false" baseParams='{"_orderBy":"CODE"}'>
            <field name="CODE" label="光交箱编码"/>
            <field name="NAME" label="光交箱名称" width="250px"/>
            <field name="V_STICKERLABEL" label="扫描结果"/><!--找不到扫描结果字段-->
            <field name="site" label="所属局站" getLabel="row.site_value?row.site_value.NAME:''"/>
            <field name="room_outdooraddress" label="所属机房" getLabel="row.room_outdooraddress_value.NAME"/>
            <field name="terminal_value.PORTS_TOTAL" label="端口总数"/>
            <field name="terminal_value.PORTS_USED" label="端子占用数"/>
            <field name="terminal_value.PORTS_FREE" label="端子空闲数"/>
            <field name="terminal_value.PORTS_CONNECTED" label="已成端端子数" width="100px"/>
            <field name="terminal_value.PORTS_BAD" label="坏端子数"/>
            <field name="terminal_value.PORTS_USEDRATE" label="端子占用率(%)" width="100px"/>
            <field name="addressobj" label="标准地址" getLabel="row.addressobj_value.FULL_NAME" width="300px"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="OWNER_NET_ID" label="网络类型"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="CAPACITY" label="标称容量"/>
            <field name="NOTES" label="备注"/>
            <field name="IS_INTEGFLAG" label="一体化标志"/>
            <field name="ADAPTTYPE_ID" label="适配类型"/>
            <field name="CONFIG_TYPE_ID" label="配置点类型"/>
            <field name="INSTALL_WAY_ID" label="安装方式"/>
            <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识" width="150px"/>
            <field name="ASSET_CODE" label="资产编码"/>
            <field name="CREATOR" label="录入人"/>
            <field name="MODIFIER" label="修改人"/>
            <field name="AUDIT_PERSON" label="验收人"/>
            <field name="PROPERTY_TYPE_ID" label="产权性质"/>
            <field name="PROPERTY_OWNER_ID" label="产权归属"/>
            <field name="MNT_LEVEL_ID" label="维护等级"/>
            <field name="MNT_PERSON" label="维护人"/>
            <field name="MNT_DEPT" label="维护单位"/>
            <field name="COLLECTION_DEPT" label="数据采集单位" width="100px"/>
            <field name="COLLECTION_PERSON" label="数据采集人"/>
            <field name="project_value.CODE" label="所属工程"/>
            <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            <field name="LONGITUDE" label="经度"/>
            <field name="LATITUDE" label="纬度"/>
            <field name="CREATE_DATE" label="入库时间"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
            <field name="model" label="型号"/>
            <field name="DEVICE_TYPE_ID" label="设备类型"/>
            <field name="ADDRESS_ID" label="地址ID"/>
        </grid>
    </ObjMeta>

    <!-- XBOX光交模板 -->
    <ObjMeta objectType="XBOX_TEMPLATE" autoReload="true" labelField="NAME"
             typeActions="add,remove"
             itemActions="modify,remove,fuseview,connview,filesman,invokeLogicDiagram,invokeRouteDiagram">
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="模板名称" required="true"/>
            </row>
        </form>
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
                <field name="CAPACITY" label="标称容量" required="true"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="region" label="所属区域"/>
                <field name="ASSIGNED_TOTAL" label="满配OBD数"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="IS_INTEGFLAG" label="一体化标志"/>
                <field name="ADAPTTYPE_ID" label="适配类型"/>
            </row>
            <row>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" default="80209209" required="true" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" default="80209223" required="true" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:1390;" autoLoad="false">
            <field name="NAME" label="模板名称" width="250px"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
    </ObjMeta>

    <!-- HUBBOX 分光箱 -->
    <ObjMeta objectType="HUBBOX" autoReload="true" needgeom="true"
             typeActions="add,reset,templates,remove"
             itemActions="locate,relocate,modify,remove,connview,portDiagram,filesman,assetAttribute,relateOCablesection,relateFiberGroup"
             moreItemActions="attachdevice_man,chengduan,fuseview,resRelationCables,gridElement,deviceOccupyRate,biaoqiandayin,deviceOpticalcircuitLabel,siteFiberLabel,devicecoveraddress_man,showRelateODevice,invokeLogicDiagram,invokeRouteDiagram">
        <script>
            <![CDATA[

            om.descriptionField = function(o) {
              var div = $('<div>');
              var d1 = $('<span>').css({padding: '5px'}).appendTo(div).text('一级OBD数量: '), d2 = $('<span>').css({padding: '5px'}).appendTo(div).text('二级OBD数量: ');
              _util.loadingRender(_bean.count('OBD', {parentDevice: o.id, OBD_LEVEL: 80204944}), d1);
              _util.loadingRender(_bean.count('OBD', {parentDevice: o.id, OBD_LEVEL: 80204945}), d2);
              return div;
            };
          ]]>
        </script>
        <action type="workspace" name="templates" label="模板管理" label-en="Template management" title="HUBBOX分光箱模板"
                title-en="FDB Template"
                objectType="HUBBOX_TEMPLATE" icon="columns"/>
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="region" label="所属区域"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县"/>
                <field name="marketingarea" label="划小营销区"/>
            </row>
            <row>
                <field name="project" label="工程"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="入库时间"/>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" label="所属局站" required="true"  dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="CAPACITY" label="标称容量" required="true"/>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="ASSIGNED_TOTAL" label="满配OBD数"/>
                <field name="ASSIGNED_USED" label="已配OBD数" readOnly="true"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" required="true" baseParams="{&quot;_solr&quot;:true}"
                       dropdownOptions="titleField:FULL_NAME"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="ADDRESS_ID" label="地址ID"/>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" readOnly="true" default="80204666"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="model" label="型号"/>
                <field name="DISTRICT" label="所属小区"/>
            </row>
            <row>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
                <field name="DEPRECIATION" label="折旧年限（年）"/>
            </row>
            <row>
                <field name="IS_SCENESAME_ID" label="是否与现场一致"/>
                <field name="APPLY_NET_ID" label="网络结点类型"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="POS_X" label="原X坐标" readOnly="true"/>
                <field name="POS_Y" label="原Y坐标" readOnly="true"/>
            </row>
            <!--<row>   等配置好元数据等工作后，再调整界面显示
                <field name="HOT_CONN_ID" label="是否热熔" default="80201002"/>
                <field name="HOT_CONN_DATE" label="热熔时间"/>
            </row>-->
            <row>
                <field name="DATA_SOURCE_ID" label="数据来源" default="80401043"/>
                <field name="IS_GREEN_POSITION" label="位置准确绿标"/>
            </row>
            <row>
                <field name="V_STICKERLABEL" queryAssemble="V_STICKERLABEL" label="扫描结果"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式" required="true" default="80204855"/>
                <field name="IS_INTEGFLAG" label="一体化标志"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
            </row>
            <row>
                <field name="bestroom" label="最优机房"/>
                <field name="bestobd" label="最优OBD"/>
            </row>
            <row>
                <field name="MIXED_CONFIG_ID" label="是否混放" default="80201001"/>
                <field name="BELONG_SPECIALITY_ID" label="所属专业" readOnly="true" default="80204675"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" multiple="true" default="80209209"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" multiple="true" default="80209223"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:6000px;" autoLoad="false" baseParams='{"_orderBy":"CODE"}'>
            <field name="CODE" label="光分编码"/>
            <field name="NAME" label="光分名称" width="250px"/>
            <field name="V_STICKERLABEL" label="扫描结果"/><!--找不到扫描结果字段-->
            <field name="site" label="所属局站" getLabel="row.site_value?row.site_value.NAME:''"/>
            <field name="room_outdooraddress" label="所属机房" getLabel="row.room_outdooraddress_value.NAME"/>
            <field name="terminal_value.PORTS_TOTAL" label="端口总数"/>
            <field name="terminal_value.PORTS_USED" label="端子占用数"/>
            <field name="terminal_value.PORTS_FREE" label="端子空闲数"/>
            <field name="terminal_value.PORTS_CONNECTED" label="已成端端子数" width="100px"/>
            <field name="terminal_value.PORTS_BAD" label="坏端子数"/>
            <field name="terminal_value.PORTS_USEDRATE" label="端子占用率(%)" width="100px"/>
            <field name="addressobj" label="标准地址" width="300px"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="OWNER_NET_ID" label="网络类型"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="CAPACITY" label="标称容量"/>
            <field name="NOTES" label="备注"/>
            <field name="CONFIG_TYPE_ID" label="配置点类型"/>
            <field name="INSTALL_WAY_ID" label="安装方式"/>
            <field name="ACCESS_MODE_ID" label="接入方式"/>
            <field name="IS_INTEGFLAG" label="一体化标志"/>
            <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识" width="150px"/>
            <field name="DEVICE_TYPE_ID" label="设备类型"/>
            <field name="MIXED_CONFIG_ID" label="是否混放"/>
            <field name="ASSET_CODE" label="资产编码"/>
            <field name="CREATOR" label="录入人"/>
            <field name="MODIFIER" label="修改人"/>
            <field name="AUDIT_PERSON" label="验收人"/>
            <field name="PROPERTY_TYPE_ID" label="产权性质"/>
            <field name="PROPERTY_OWNER_ID" label="产权归属"/>
            <field name="MNT_LEVEL_ID" label="维护等级"/>
            <field name="MNT_PERSON" label="维护人"/>
            <field name="MNT_DEPT" label="维护单位"/>
            <field name="COLLECTION_DEPT" label="数据采集单位" width="100px"/>
            <field name="COLLECTION_PERSON" label="数据采集人"/>
            <field name="project_value.CODE" label="所属工程"/>
            <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            <field name="LONGITUDE" label="经度"/>
            <field name="LATITUDE" label="纬度"/>
            <field name="CREATE_DATE" label="入库时间"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
            <field name="model" label="型号"/>
            <field name="ADDRESS_ID" label="地址ID"/>
        </grid>
    </ObjMeta>

    <!-- HUBBOX分光箱模板 -->
    <ObjMeta objectType="HUBBOX_TEMPLATE" autoReload="true" labelField="NAME"
             typeActions="add,remove"
             itemActions="modify,remove,fuseview,connview,filesman">
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="模板名称" required="true"/>
            </row>
        </form>
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204546"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
                <field name="CAPACITY" label="标称容量" required="true"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="region" label="所属区域"/>
                <field name="ASSIGNED_TOTAL" label="满配OBD数"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="IS_INTEGFLAG" label="一体化标志"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" default="80209209" required="true" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" default="80209223" required="true" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护单位"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:1390;" autoLoad="false">
            <field name="NAME" label="模板名称" width="250px"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
    </ObjMeta>

    <!--光缆盘留 -->
    <ObjMeta objectType="OCABLECOIL" autoReload="true" itemActions="locate,modify,remove,setidle">
        <field name="ASSISTANCE_TYPE_ID" readOnly="true" />
        <!--查询条件-->
        <form name="search" labelWidth="6em">
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="facility" label="所属支撑设施"/>
            </row>
            <row>
                <field name="ASSISTANCE_TYPE_ID" label="辅助设备类型"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
                <field name="LENGTH" label="长度"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="cable" label="所属缆段" dropdownOptions="titleField:NAME" baseParams='{"SPEC_ID":"OCABLESECTION"}'/>
            </row>
            <row>
                <field name="facility" label="所属支撑设施"/>
                <field name="LENGTH" label="长度（米）"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true"/>
                <field name="ASSISTANCE_TYPE_ID" label="辅助设备类型" readOnly="true"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="ASSET_CODE" label="资产编码"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true"  readOnly="true" default="80204666"/>
                <field name="NOTES" label="备注"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
            </row>
            <row>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
                <field name="CHECKING_PERSON" label="检查责任人"/>
            </row>
            <row>
                <field name="START_USE_DATE" label="启用时间"/>
                <field name="STOP_USE_DATE" label="停用日期"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="" autoLoad="false">
            <field name="CODE" label="盘留编码"/>
            <field name="NAME" label="盘留名称"/>
            <field name="site" label="所属局站"/>
            <field name="facility" label="所属支撑设施"/>
            <field name="LENGTH" label="长度"/>
            <field name="ASSISTANCE_TYPE_ID" label="辅助设备类型"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="CREATE_DATE"/>
        </grid>
    </ObjMeta>

    <!-- GFGROUP 光分群 -->
    <!--<ObjMeta objectType="GFGROUP" label="GF群" needgeom="true"
             itemActions="relocate,locate,modify,filesman,gfgruop_mgr"
             moreItemActions="remove">
        <form name="search" labelWidth="8em">
            <field type="divider" label="基础属性" label-en="Basic Attribute" />
            <row>
                <field name="CODE"  label="编码" />
                <field name="NAME"  label="名称"/>
            </row>
            <row>
                <field name="site"  label="所属局站" />
                &lt;!&ndash;				<field name=""  label="所属子区域"/>&ndash;&gt;
            </row>
            <row>
                <field name="ADDRESS_DESC"  label="具体位置" />
            </row>
        </form>
        <form>
            <field type="divider" label="基础属性" label-en="Basic Attribute" />
            <row>
                <field name="CODE" required="true" />
                <field name="NAME" required="true" />
            </row>
            <row>
                <field name="site" label="所属局站" required="true"
                       onChange="region=site.REGION_ID" />
                <field name="gridcode" label="所属网格"  />
            </row>
            <row>
                <field name="ADDRESS_DESC" />
                <field name="NOTES" />
            </row>
            &lt;!&ndash; <row><field name="AUDIT_PERSON"/><field name="AUDIT_DATE"/></row> &ndash;&gt;
        </form>
        <grid>
            <field name="CODE" />
            <field name="NAME" />
            <field name="site" />
            <field name="NOTES" />
            <filed name="CREATOR" label="录入人" />
            <field name="CREATE_DATE" label="录入时间" width="90px"/>
            <field name="MODIFIER" />
            <field name="LONGITUDE" />
            <field name="LATITUDE" />
        </grid>
    </ObjMeta>-->

    <!-- 纤芯 -->
    <ObjMeta objectType="FIBER" autoReload="true"
             typeActions="modifyPhysicStatus,modifyBusinessStatus"
             itemActions="locateOptic,locate,filesman,modifyPropertyRightAttribute,lightPathProperty">
        <form>
            <row>
                <field name="ocablesection" readOnly="true" />
            </row>
            <row>
                <field name="CODE" />
                <field name="NAME" />
            </row>
            <row>
                <field name="LENGTH" />
                <field name="WORK_WAY_ID" />
            </row>
            <row>
                <field name="PHYSICAL_STATE_ID" />
                <field name="USING_STATE_ID" />
            </row>
            <row>
                <field name="FIBER_MODE_ID" />
            </row>
        </form>
        <grid tableStyle="width:3700px;">
            <chart groupData="true" fields="USING_STATE_ID" type="pie"
                   seriesOptions='{"radius": ["10%", "80%"]}'>
                <item value="100721" color="green" />
                <item value="100723" color="red" />
            </chart>
            <field name="SEQ" width="30pt" />
            <field name="CODE" width="120pt"/>
            <field name="NAME" width="120pt"/>
            <field name="USING_STATE_ID" width="60pt" />
            <field name="PHYSICAL_STATE_ID" width="60pt" />
            <field name="LENGTH" width="60pt" />
            <field name="CHROMATOGRAPHY_ID"  />
            <field name="belongSitefibers"  label="局向光纤编码" getLabel="row.belongSitefibers.CODE" orderBy="false" width="200pt"/>
            <field name="belongSitefibers"  label="局向光纤名称" getLabel="row.belongSitefibers.NAME" orderBy="false" width="200pt"/>
            <field name="parent"  label="光缆段编码" getLabel="row.parent.CODE" orderBy="false" width="200pt"/>
            <field name="adevice_value.FACILITY_ID" type="obj" rtype="FACILITY" label="A端设施" width="200px"/>
            <field name="adevice"  label="A端设备编码" getLabel="row.adevice.CODE" orderBy="false" width="200px"/>
            <field name="adevice"  label="A端设备名称" getLabel="row.adevice.NAME" orderBy="false" width="200px"/>
            <field name="aportEntity"  label="A端端子"  getLabel="row.aportEntity.CODE" width="120pt"/>
            <field name="zdevice_value.FACILITY_ID" type="obj" rtype="FACILITY" label="z端设施" width="200px"/>
            <field name="zdevice"  label="Z端设备编码" getLabel="row.zdevice.CODE" orderBy="false" width="200px"/>
            <field name="zdevice"  label="Z端设备名称" getLabel="row.zdevice.NAME" orderBy="false" width="200px"/>
            <field name="zportEntity"  label="Z端端子" getLabel="row.zportEntity.CODE" width="120pt"/>
            <field name="optical"  label="光路编码"  getLabel="row.optical.CODE" width="120pt"/>
            <field name="optical"  label="光路名称"  getLabel="row.optical.NAME" width="120pt"/>
            <field name="optical"  label="旧光路编码"  getLabel="row.optical.OLD_OPTIC_CODE==null?'':row.optical.OLD_OPTIC_CODE" width="120pt"/>
            <field name="optical"  label="光路旧编码"  getLabel="row.optical.OPTIC_CODE_OLD==null?'':row.optical.OPTIC_CODE_OLD" width="120pt"/>
            <field name="optical"  label="接入号"  getLabel="row.optical.SERVICE_NO==null?'':row.optical.SERVICE_NO" width="120pt"/>
            <field name="optical"  label="调单号"  getLabel="row.optical.BUSI_ORDER_CODE==null?'':row.optical.BUSI_ORDER_CODE" width="120pt"/>
            <field name="optical"  label="备注"  getLabel="row.optical.NOTES==null?'':row.optical.NOTES" width="120pt"/>
            <field name="optical"  label="本地网光路名称"  getLabel="row.optical.AREA_OPTIC_NAME==null?'':row.optical.AREA_OPTIC_NAME" width="120pt"/>
        </grid>
        <grid tableStyle="width:3700px;" name="qxglfibergrid" typeActions="modifyPhysicStatus,modifyBusinessStatus"
             itemActions="locateOptic,locate,filesman,modifyPropertyRightAttribute,lightPathProperty" extraActions="expgrid">
            <field name="id" label="资源ID" width="120pt"/>
            <field name="code" label="编码" width="120pt"/>
            <field name="name" label="名称" width="120pt"/>
            <field name="seq" label="序号" width="30pt" />
            <field name="physicstatus" label="物理状态" width="60pt" />
            <field name="servicestatus" label="业务状态" width="60pt" />
            <field name="propertyowner" label="产权归属" width="60pt" />
            <field name="len" width="60pt" label="长度" />
            <field name="chromatography"  label="色谱" />
            <field name="logfibercode"  label="局向光纤编码" orderBy="false" width="200pt"/>
            <field name="logfibername"  label="局向光纤名称"  orderBy="false" width="200pt"/>
            <field name="sectioncode"  label="光缆段编码"  orderBy="false" width="200pt"/>
            <field name="lf_physics_state"  label="局纤业务状态"  orderBy="false" width="200pt"/>
            <field name="lf_service_state"  label="局纤物理状态"  orderBy="false" width="200pt"/>
            <!-- <field name="usable_lf_count"  label="可用局纤数"  orderBy="false" width="200pt"/> -->
            <field name="lf_afacilityname"  label="A端机房/设施" width="200px"/>
            <field name="lf_adevicecode"  label="A端设备编码"  orderBy="false" width="200px"/>
            <field name="lf_adevicename"  label="A端设备名称"  orderBy="false" width="200px"/>
            <field name="lf_aportcode"  label="A端端子"   width="120pt"/>
            <field name="lf_zfacilityname"  label="Z端机房/设施" width="200px"/>
            <field name="lf_zdevicecode"  label="Z端设备编码"  orderBy="false" width="200px"/>
            <field name="lf_zdevicename"  label="Z端设备名称" orderBy="false" width="200px"/>
            <field name="lf_zportcode"  label="Z端端子"  width="120pt"/>
            <field name="opticcode"  label="光路编码"   width="120pt"/>
            <field name="opticname"  label="光路名称" width="120pt"/>
            <field name="oldopticcode"  label="旧光路编码"   width="120pt"/>
            <field name="opticoldcode"  label="光路旧编码"   width="120pt"/>
            <field name="serviceno"  label="接入号"   width="120pt"/>
            <field name="busiordercode"  label="调单号"   width="120pt"/>
            <field name="notes"  label="备注"   width="120pt"/>
            <field name="areaopticname"  label="本地网光路名称"   width="120pt"/>
        </grid>
        <action name="locate" label="定位光缆段" type="script">
            <![CDATA[
                var params = _actionContext.params;
                _context.doAction('locate',{id:params.PARENT_ID,objectType:'OCABLESECTION'});
            ]]>
        </action>
        <action name="locateOptic" label="定位光路" type="script">
            <![CDATA[
                var fiber = _actionContext.params;
                _bean.find(fiber.objectType, {id: fiber.id, _assemble: 'belongSitefibers[belongOpticlink[belongOpticalcircuits]]'}).then(function(fiber){
                  var optic = fiber.belongSitefibers && fiber.belongSitefibers.belongOpticlink && fiber.belongSitefibers.belongOpticlink.belongOpticalcircuits;
                  if (optic)
                    _bean.action(optic.objectType, 'search', {needshape: 'true', opticid: optic.id}, {needshape: 'true', opticid: optic.id}).then(function(optic){
                      optic = optic.data[0];
                      if (optic.locatecss) _context.map.locateByWkts(optic.locatecss.map(function(o){return o.shape;}));
                    });
                  else alert('该纤芯无光路信息');
                });
            ]]>
        </action>
        <action name="modifyPhysicStatus" label="修改物理状态" label-en="Modify PhysicStatus" type="script" itemsable="true">
            <![CDATA[
                debugger
                let item = _actionContext.params
                let pass = false
                for (let i = 0; i < item.length; i++) {
                    if(item[i].opticid) {
                        _sui.alert('纤芯上存在光路，不允许修改物理状态')
                        pass = true
                        break
                    }
                }
                if(!pass) {
                    let obj = {
                        title: '修改物理状态',
                        type: 'window',
                        icon: 'chart area',
                        maxable: false, style: 'width: 400px',
                        contentUrl: 'apps/gdo3/cable/modifyFiberPhysicStatus.html',
                    }
                    _context.doAction(obj,item,_actionContext.source)
                }
            ]]>
        </action>
        <action name="modifyBusinessStatus" label="修改业务状态" label-en="Modify BusinessStatus" type="window"
                contentUrl="apps/gdo3/cable/modifyFiberBusinessStatus.html" itemsable="true"/>
        
        <action name="modifyPropertyRightAttribute" label="修改产权属性" label-en="Modify PhysicPropertyRightAttribute" type="script"
                url="modules/chengduan/modify/propertyRightAttribute.js"/>

        <action name="lightPathProperty" label="光路信息" type="script">
          <![CDATA[ 
            _bean.find('FIBER', {id: _actionContext.params.id, _assemble: 'optical'}).then(function(fiber){
              if (!fiber.optical) return _sui.alert('该纤芯没有光路信息');
              var random=Math.random();
              _context.doAction({type: 'window', width: 900, height: 550, url: "modules/opticroute/opticroute_editor.html?id="+fiber.optical.id+"&_v="+random+"&readonly=true",title: "光路查看"+fiber.optical.CODE,label: "光路查看"+fiber.optical.CODE});
            });
          ]]>
        </action>
    </ObjMeta>

    <!-- MODF -->
    <ObjMeta objectType="MODF" typeActions="add,templates,clonedev" autoReload="true"
             itemActions="locate,modify,connview,filesman,remove,bindODevice,assetAttribute" moreItemActions="uploadImage3d,setidle,invokeLogicDiagram,invokeRouteDiagram">
        <action type="workspace" name="connview-${id}" id="connview" label="端子图" label-en="Panel Diagram"
                icon="delicious" url="connector.html?objecttype=${objectType}&amp;id=${id}" extra="_ui_class:gray"
                title="端子面板图-${NAME}" title-en="Panel Diagram -${NAME}">
        </action>
        <action type="workspace" name="templates" label="模板管理" label-en="Template management" title="光配线架模板"
                title-en="ODF Template" objectType="ODF.TEMPLATE" icon="columns"/>
        <form>
            <row>
                <field name="site" dropdownOptions="titleField:NAME"/>
                <field name="facility" label="机房" label-en="Room" dropdownOptions="titleField:NAME"
                       onChange="CODE,NAME=@genCode(facility);site=facility.TML_ID"/>
            </row>
            <row>
                <field name="CODE"/>
                <field name="NAME"/>
            </row>
            <row>
                <field name="CAPACITY" required="true"/>
                <field name="model"/>
            </row>
            <row>
                <field name="USING_STATE_ID" lable="业务状态"/>
                <field name="MNT_LEVEL_ID" label="维护状态"/>
            </row>
            <row>
                <field name="LENGTH"/>
                <field name="WIDTH"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID"/>
                <field name="CREATOR"/>
            </row>
            <row>
                <field name="ADDRESS_DESC"/>
                <field name="IS_INTEGFLAG"/>
            </row>
            <!--             <row><field name="GRID_CODE" label="所属网格" label-en="Affiliated grid" /></row> -->
            <row>
                <field name="addressobj" baseParams="{&quot;_solr&quot;:true}" dropdownOptions="titleField:FULL_NAME"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project"/>
            </row>
        </form>
        <action name="genCode" type="script">
            <![CDATA[
            var pre = facility.CODE + '/MODF';
            _bean.autoCode(this, pre, 3, 'MODF', {facility: facility.id});
        ]]>
        </action>
        <action name="clonedev" type="script" label="复制设备" url="apps/pipe/device/js/clonedev.js"/>
    </ObjMeta>

    <ObjMeta objectType="STANDARDRACK" autoReload="true" itemActions="view3D,filesman,uploadImage3d,modify,frameview,filesman,remove">
        <!-- 机柜正视图 -->
        <action type="workspace" name="frameview-${id}" id="frameview" label="机柜正视图" label-en="Cabinet Front View"
                icon="server" url="modules/frameview/frameview.html?frametype=${objectType}&amp;frameid=${id}"
                extra="_ui_class:gray" title="机柜正视图-${NAME}" title-en="Cabinet front view -${NAME}"></action>
        <form>
            <row>
                <field name="NAME"/>
                <field name="CODE"/>
            </row>
            <row>
                <field name="model"/>
                <field name="ROW_NUM"/>
            </row>
            <row>
                <field name="LENGTH"/>
                <field name="WIDTH"/>
            </row>
            <row>
                <field name="HEIGHT"/>
                <field name="LIFE_STATE_ID"/>
            </row>
        </form>
    </ObjMeta>

    <ObjMeta objectType="DEVICE" autoReload="true">
        <grid name="FTTX10GT" filterable="false" sortable="false">
<!--            <field name="net" label="所属光缆"/>-->
            <field name="CODE" label="光接头编码"/>
            <field name="NAME" label="光接头名称"/>
        </grid>
        <grid name="samerouteanaly" toolbar="false" rowDetailField="false">
            <field name="CODE" label="编码" orderBy="false"/>
            <field name="NAME" label="名称" orderBy="false"/>
            <field name="METACATEGORYCN" label="类型" orderBy="false"/>
        </grid>
        <!--        <form>-->
        <!--            <field name="net"/>-->
        <!--            <field name="CODE"/>-->
        <!--            <field name="NAME"/>-->
        <!--        </form>-->
         <grid name="deviceGrid" toolbar="false" rowDetailField="false">
            <field name="CODE" label="编码" orderBy="false"/>
            <field name="NAME" label="名称" orderBy="false"/>
            <field name="METACATEGORYCN" label="类型" orderBy="false"/>
            <field name="site" label="所属局站"  />
            <field name="room" label="所属机房"  />
        </grid>
    </ObjMeta>



    <ObjMeta objectType="OLTDEVICE.TEMPLATE" autoReload="true" needgeom="false" typeActions="add" itemActions="modify,remove" labelField="NAME">
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" required="false"/>
                <field name="room"/>
            </row>
            <row>
                <field name="rack"/>
            </row>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>

            <row>
                <field name="LIFE_STATE_ID"/>
                <field name="CAPACITY"/>
            </row>
            <row>
                <field name="CREATOR"/>
                <field name="CREATE_DATE"/>
            </row>
            <row>
                <field name="plane" baseParams="PLANE_ID:80206206" label="所属一平面POP点" label-en="Plane 1 POP point"/>
                <field name="plane2" baseParams="PLANE_ID:80206207" label="所属二平面POP点" label-en="Plane 2 POP point"/>
            </row>
            <row>
                <field name="plane3" baseParams="PLANE_ID:80206208" label="所属1.5平面POP点" label-en="Plane 1.5 POP point"/>
                <field name="NOTES"/>
            </row>
            <field type="divider" label="位置信息" label-en="Location information"/>

            <row>
                <field name="LONGITUDE"/>
                <field name="LATITUDE"/>
            </row>
            <!--
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row><field name="smallcounty"/><field name="marketingarea"/></row>
            <row><field name="servicearea"/></row>
            -->

            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID"/>
                <field name="PROPERTY_OWNER_ID"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID"/>
                <field name="MNT_PERSON"/>
            </row>
            <row>
                <field name="MNT_DEPT"/>
                <field name="COLLECTION_DEPT"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON"/>
                <field name="COLLECTION_DATE"/>
            </row>
            <row>
                <field name="AUDIT_PERSON"/>
                <field name="AUDIT_DATE"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project"/>
            </row>
        </form>
    </ObjMeta>

    <!-- 光分群 -->
    <ObjMeta objectType="GFGROUP" needgeom="true" autoReload="true" itemActions="locate,modify,filesman,gfgruop_mgr,remove">
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" required="true" onChange="region=site.REGION_ID"/>
            </row>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="model"/>
                <field name="vendor"/>
            </row>
            <row>
                <field name="ADDRESS_DESC"/>
                <field name="DISMANTLED_STATE_ID"/>
            </row>
            <row>
                <field name="NOTES"/>
            </row>
            <field type="divider" label="位置信息" label-en="Location information"/>
            <row>
                <field name="gridcode"/>
            </row>
            <row>
                <field name="LONGITUDE"/>
                <field name="LATITUDE"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty"/>
                <field name="marketingarea"/>
            </row>
            <row>
                <field name="servicearea"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="COLLECTION_DEPT"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON"/>
                <field name="COLLECTION_DATE"/>
            </row>
            <row>
                <field name="AUDIT_PERSON"/>
                <field name="AUDIT_DATE"/>
            </row>
        </form>
    </ObjMeta>


    <!--ONU模板  -->
    <ObjMeta objectType="ONU.TEMPLATE" autoReload="true" typeActions="add" itemActions="modify,remove" labelField="NAME">
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" required="false"/>
                <field name="room"/>
            </row>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="parentDevice" onChange="room=parentDevice.FACILITY_ID;site=parentDevice.TML_ID"/>
                <field name="LONG_LOCAL_ID"/>
            </row>
            <row>
                <field name="ADDRESS_DESC"/>
            </row>

            <row>
                <field name="CREATOR"/>
                <field name="CREATE_DATE"/>
            </row>
            <field type="divider" label="位置信息" label-en="Location information"/>
            <row>
                <field name="LONGITUDE"/>
                <field name="LATITUDE"/>
            </row>
            <!--
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row><field name="smallcounty"/><field name="marketingarea"/></row>
            <row><field name="servicearea"/></row>
            -->
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project"/>
                <field name="COLLECTION_DATE"/>
            </row>
        </form>
    </ObjMeta>

    <!--OLT模板  -->
    <ObjMeta objectType="OLT.TEMPLATE" autoReload="true" typeActions="add" itemActions="modify,remove" labelField="NAME">
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" required="false" onChange="region=site.REGION_ID;CODE,NAME=@genCode(site)"/>
                <field name="room"
                       onChange="site=room.TML_ID;${rack}.dropdown.setBaseParam('room',${room}.getValue() || -1)"/>
            </row>
            <row>
                <field name="rack"/>
            </row>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>

            <row>
                <field name="LIFE_STATE_ID"/>
                <field name="CAPACITY"/>
            </row>
            <row>
                <field name="CREATOR"/>
                <field name="CREATE_DATE"/>
            </row>
            <row>
                <field name="plane" baseParams="PLANE_ID:80206206" label="所属一平面POP点" label-en="Plane 1 POP point"/>
                <field name="plane2" baseParams="PLANE_ID:80206207" label="所属二平面POP点" label-en="Plane 2 POP point"/>
            </row>
            <row>
                <field name="plane3" baseParams="PLANE_ID:80206208" label="所属1.5平面POP点" label-en="Plane 1.5 POP point"/>
                <field name="NOTES"/>
            </row>
            <field type="divider" label="位置信息" label-en="Location information"/>

            <row>
                <field name="LONGITUDE"/>
                <field name="LATITUDE"/>
            </row>
            <!--
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row><field name="smallcounty"/><field name="marketingarea"/></row>
            <row><field name="servicearea"/></row>
            -->

            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID"/>
                <field name="PROPERTY_OWNER_ID"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID"/>
                <field name="MNT_PERSON"/>
            </row>
            <row>
                <field name="MNT_DEPT"/>
                <field name="COLLECTION_DEPT"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON"/>
                <field name="COLLECTION_DATE"/>
            </row>
            <row>
                <field name="AUDIT_PERSON"/>
                <field name="AUDIT_DATE"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project"/>
            </row>
        </form>
    </ObjMeta>



    <ObjMeta objectType="OPTICALCIRCUIT" autoReload="true"
             itemActions="ftth_info,grid:list_circuits,form:view_sysinfo,showCablesByOptic,modify,editroute,remove"
             typeActions="optic_search">
        <form name="search">
            <row>
                <field name="NAME"/>
                <field name="CODE"/>
            </row>
            <row>
                <field name="serviceno" label='接入号'/>
                <field name='AREA_OPTIC_NAME' label='本地网光路名称'/>
            </row>
    		<row>
                <field name="adevice" label="A端设备"/>
                <field name="zdevice" label="Z端设备"/>
    		</row>
        </form>
        <form name="searchLightPath">
            <row>
                <field name="CODE" label="光路编码" required="false"/>
                <field name="NAME" label="光路名称" required="false"/>
                <field name="OPTICAL_TYPE_ID"/>
            </row>
            <row>
                <field name="OLD_OPTIC_CODE"/>
                <field name="customerNAME" column="product.customer.NAME" label="客户名称"/>
                <field name="crmProductId" label="专线编码" />
            </row>
            <row>
                <field name="adev" label="A端设备编码"/>
                <field name="zdev" label="Z端设备编码"/>
                <field name="SERVICE_NO" label='接入号' />
            </row>
            <row>
                <field name="MNT_LEVEL_ID"/>
                <field name="CREATOR" label='创建人' enabled="false" editable="false" />
                <field name="CREATE_DATE" label='创建时间' enabled="false" />
            </row>
        </form>
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field label="光路编码" name="CODE" required="true"/>
            </row>
            <row>
                <field label="光路名称" name="NAME"/>
            </row>
            <row>
                <field label="光路类型" name="BUSINESS_TYPE_ID"/>
                <field label="光路子类型" name="TYPE_ID"/>
            </row>
            <row>
                <field name="OPTROAD_USETYPE_ID" label='光路用途'/>
                <field name="APPLY_NET_ID" label='光路子用途'/>
            </row>
            <row>
                <field name="BUSI_ORDER_CODE" label='调度单号'/>
                <field name="BRKEN_ORDER_NO" label='故障单号'/>
            </row>
            <row>
                <field name="NEED_ODD" label='需求单子号'/>
                <field name="LONG_DIS_CODE" label='长途电路代号'/>
            </row>
            <row>
                <field name="SINGLE_TYPE_ID" label='光路纤数' required="true"/>
                <field name="SERVICE_NO" label='业务代号'/>
            </row>
            <row>
                <field name="RELA_OPT_ROUTE_ID" label='关联的故障光路'/>
                <field name="IS_BROKEN_FLAG" label='是否故障临调'/>
            </row>
            <row>
                <field name="adevice" label='A端设备' readOnly='true' dropdownOptions="titleField:NAME"/>
                <field name="zdevice" label='Z端设备' readOnly='true' dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="aport" label='A端端子编码' readOnly='true' onChange="aportAlias=aport.ALIAS;"/>
                <field name="zport" label='Z端端子编码' readOnly='true' onChange="zportAlias=zport.ALIAS;"/>
            </row>
            <row>
                <field name="aportAlias" label='A端端子别名' readOnly='true'/>
                <field name="zportAlias" label='Z端端子别名' readOnly='true'/>
            </row>
            <row>
                <field name="aaddress" label='A端安装地址'/>
                <field name="zaddress" label='Z端安装地址'/>
            </row>

            <row>
                <field name="A_END_CONTACTOR" label='A端联系人' />
                <field name="Z_END_CONTACTOR" label='Z端联系人' />
            </row>
            <row>
                <field name="A_END_FIXED_TEL" label='A端(或就近)固话' />
                <field name="Z_END_FIXED_TEL" label='Z端(或就近)固话' />
            </row>
            <row>
                <field name="A_END_TEL" label='A端电话' />
                <field name="Z_END_TEL" label='Z端电话' />
            </row>

            <row>
                <field name="BACKGRADE" label='备份等级'/>
                <field name="A_Z_ATTRIBUTE" label='本对端信息' />
            </row>
            <row>
                <field name="CLIENTSERLEVEL" label='客户服务等级'/>
                <field name="IDENTIFIER_ID" label='光路标识'/>
            </row>
            <row>
                <field name="INPUTCASUS" label='录入事由'/>
                <field name="IS_ISADDROAD_ID" label='是否补录光路'/>
            </row>
            <row>
                <field name="IS_CUT_ID" label='是否为割接光路'/>
                <field name="IS_SCENESAME_ID" label='是否与现场一致'/>
            </row>
            <row>
                <field name="STATUS_TYPE_ID" label='光路的主备状态标识' readOnly='true'/>
                <field name="MNT_LEVEL_ID" label='维护等级'/>
            </row>
            <row>
                <field name="REASON" label='配置原因'/>
                <field name="RECORDERDETAIL" label='配置情况'/>
            </row>
            <row>
                <field name="CREATOR" label='录入人' readOnly='true'/>
                <field name="CREATE_DATE" label='入库时间' readOnly='true'/>
            </row>
            <row>
                <field name="MODIFIER" label='修改人' readOnly='true'/>
                <field name="MODIFY_DATE" label='修改时间' readOnly='true'/>
            </row>
            <row>
                <field name="CHAN_STATUS_ID" label='纠错状态' readOnly="true"/>
                <field name="CHAN_NO" label='纠错单号' readOnly="true"/>
            </row>
            <row>
                <field name="NOTES" label='备注'/>
            </row>
            <row>
                <field name="PROTECT_LINE_TYPE_ID" label='线路保护类型'/>
                <field name="IS_INVENTORY_ID" label='是否存量'/>
            </row>

            <field type='divider' label='产品服务属性'/>
            <row>
                <field name="service" label='产品服务' disabled="true" onChange="service_REQUESTER_NAME=service.REQUESTER_NAME;service_REQUESTER_PHONE_NUM=service.REQUESTER_PHONE_NUM;service_ACCESS_CODE=service.ACCESS_CODE;service_LAST_ORDER_CODE=service.LAST_ORDER_CODE;service_dispatchOrderNo=service.dispatchOrderNo;service_A_LONGDIS_CIRCUITCODE=service.A_LONGDIS_CIRCUITCODE"/>
                <field name="service_ACCESS_CODE" label="接入号" readOnly="true"/>
            </row>
            <row>
                <field name="service_A_LONGDIS_CIRCUITCODE" label='业务长途电路代号'/>
            </row>
            <row>
                <field name="service_REQUESTER_NAME" label="上单人姓名" readOnly="true"/>
                <field name="service_REQUESTER_PHONE_NUM" label="上单人联系电话" readOnly="true"/>
            </row>
            <row>
                <field name="service_LAST_ORDER_CODE" label="最近服务订单编码" readOnly="true"/>
                <field name="service_dispatchOrderNo" label="调度文号" readOnly="true"/>
            </row>
            <row>
                <field name="relayservice" label='产品服务实例中继' disabled="true" onChange="service_RELAY_LEVEL_ID=relayservice.RELAY_LEVEL_ID"/>
                <field name="service_RELAY_LEVEL_ID" type="dict" ditems="80209602-A级重要中继,80209603-B级重要中继,80209604-C级重要中继,80209605-其它" label="局内中继等级" readOnly="true"/>
            </row>

            <field type='divider' label='主备属性'/>
            <row>
                <field name='PROTECT_TYPE_ID' label='光路的保护状态标识'/>
            </row>
            <row>
                <field name="BACKOPTROADCODE" label='备份光路编码'/>
                <field name="BACKSERVICENO" label='备份光路业务代号'/>
            </row>
            <field type='divider' label='工程属性'/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type='divider' label='专业属性'/>
            <row>
                <field name='TEXT_ROUTE' label='文本路由'/>
                <field name='USING_STATE_ID' label='业务状态' readOnly='true'/>
            </row>
            <row>
                <field name='LOCK_STATE_ID' label='封锁状态'/>
                <field name='LENGTH' label='长度(m)' />
            </row>
            <row>
                <field name='AREA_OPTIC_NAME' label='本地网光路名称'/>
                <field name='TA_TASK_ID' label='TA带宽型任务' />
            </row>
            <row>
                <field name='OLD_OPTIC_CODE' label='旧光路编码'/>
                <field name='OPTIC_CODE_OLD' label='光路旧编码' />
            </row>
            <row>
                <field name='OPER_SOURCE' label='操作来源'/>
                <field name='BAR_CODE' label='条形码'  readOnly='true'/>
            </row>
        </form>

        <form name="getprop">
            <row>
                <field label="光路编码" name="CODE" required="false" readOnly='true'/>
            </row>
            <row>
                <field label="光路名称" name="NAME" required="false" readOnly='true'/>
            </row>
            <row>
                <field name="BUSINESS_TYPE_ID" label="光路类型"/>
                <field name="TYPE_ID" label="光路子类型"/>
            </row>
            <row>
                <field name="OPTROAD_USETYPE_ID" label='光路用途'/>
                <field name="APPLY_NET_ID" label='光路子用途' />
            </row>
            <row>
                <field name="BUSI_ORDER_CODE" label='调度单号'  />
                <field name="BRKEN_ORDER_NO" label='故障单号' />
            </row>
            <row>
                <field name="NEED_ODD" label='需求单子号'  />
                <field name="LONG_DIS_CODE" label='长途电路代号' />
            </row>
            <row>
                <field name="SINGLE_TYPE_ID" label='配置类型' required="true"/>
                <field name="SERVICE_NO" label='业务代号' />
            </row>
            <row>
                <field name="RELA_OPT_ROUTE_ID" label='关联的故障光路' />
                <field name="IS_BROKEN_FLAG" label='是否故障临调' />
            </row>
            <row>
                <field name="adevice" label='A端设备' readOnly='true' dropdownOptions="titleField:NAME"/>
                <field name="zdevice" label='Z端设备' readOnly='true' dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="aport" label='A端端子编码' readOnly='true' onChange="aportAlias=aport.ALIAS;"/>
                <field name="zport" label='Z端端子编码' readOnly='true' onChange="zportAlias=zport.ALIAS;"/>
            </row>
            <row>
                <field name="aportAlias" label='A端端子别名' readOnly='true'/>
                <field name="zportAlias" label='Z端端子别名' readOnly='true'/>
            </row>
            <row>
                <field name="aaddress" label='A端安装地址' />
                <field name="zaddress" label='Z端安装地址' />
            </row>
            <row>
                <field name="A_END_CONTACTOR" label='A端联系人' />
                <field name="Z_END_CONTACTOR" label='Z端联系人' />
            </row>
            <row>
                <field name="A_END_FIXED_TEL" label='A端(或就近)固话' />
                <field name="Z_END_FIXED_TEL" label='Z端(或就近)固话' />
            </row>
            <row>
                <field name="A_END_TEL" label='A端电话' />
                <field name="Z_END_TEL" label='Z端电话' />
            </row>

            <row>
                <field name="BACKGRADE" label='备份等级' />
                <field name="A_Z_ATTRIBUTE" label='本对端信息' />
            </row>
            <row>
                <field name="CLIENTSERLEVEL" label='客户服务等级' />
                <field name="IDENTIFIER_ID" label='光路标识' />
            </row>
            <row>
                <field name="INPUTCASUS" label='录入事由' />
                <field name="IS_ISADDROAD_ID" label='是否补录光路' />
            </row>
            <row>
                <field name="IS_CUT_ID" label='是否为割接光路' />
                <field name="IS_SCENESAME_ID" label='是否与现场一致' />
            </row>
            <row>
                <field name="STATUS_TYPE_ID" label='光路的主备状态标识' readOnly='true'/>
                <field name="MNT_LEVEL_ID" label='维护等级' />
            </row>
            <row>
                <field name="REASON" label='配置原因' />
                <field name="RECORDERDETAIL" label='配置情况' />
            </row>
            <row>
                <field name="CREATOR" label='录入人' enabled="false" editable="false" />
                <field name="CREATE_DATE" label='入库时间' enabled="false" />
            </row>
            <row>
                <field name="MODIFIER" label='修改人' enabled="false" editable="false" />
                <field name="MODIFY_DATE" label='修改时间' enabled="false" />
            </row>
            <row>
                <field name="service" label='产品服务' enabled="false" />
            </row>
            <row>
                <field name="service_REQUESTER_NAME" label="上单人姓名" readOnly="true"/>
                <field name="service_REQUESTER_PHONE_NUM" label="上单人联系电话" readOnly="true"/>
            </row>
            <row>
                <field name="CHAN_STATUS_ID" label='纠错状态' editable="false"/>
                <field name="CHAN_NO" label='纠错单号' editable="false"/>
            </row>
            <row>
                <field name="NOTES" label='备注'/>
            </row>
            <row>
                <field name="PROTECT_LINE_TYPE_ID" label='线路保护类型'/>
            </row>
            <row>
                <field name="IS_INVENTORY_ID" label='是否存量'/>
            </row>
            <field type='divider' label='主备属性'/>
            <row>
                <field name='PROTECT_TYPE_ID' label='光路的保护状态标识'/>
            </row>
            <row>
                <field name="BACKOPTROADCODE" label='备份光路编码'/>
                <field name="BACKSERVICENO" label='备份光路业务代号'/>
            </row>
            <field type='divider' label='工程属性'/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type='divider' label='专业属性'/>
            <row>
                <field name='TEXT_ROUTE' label='文本路由'/>
                <field name='USING_STATE_ID' label='业务状态' />
            </row>
            <row>
                <field name='LOCK_STATE_ID' label='封锁状态'/>
                <field name='LENGTH' label='长度(m)' />
            </row>
            <row>
                <field name='AREA_OPTIC_NAME' label='本地网光路名称'/>
                <field name='TA_TASK_ID' label='TA带宽型任务' />
            </row>
            <row>
                <field name='OLD_OPTIC_CODE' label='旧光路编码'/>
                <field name='OPTIC_CODE_OLD' label='光路旧编码' />
            </row>
            <row>
                <field name='OPER_SOURCE' label='操作来源'/>
                <field name='BAR_CODE' label='条形码'  readOnly='true'/>
            </row>
        </form>

        <form name="view_sysinfo" label="系统信息" formName="view_sysinfo" closable="true">
            <field name="CODE"/>
            <field name="NAME"/>
            <field name="belongSystem" type="obj" rtype="NET"/>
        </form>
        <grid name="list_circuits" queryActionName="listCircuitsInfo" label="电路信息" baseParams='{"opticId":"${id}"}'
              style="height:320px;"
              toolbar="false" footer="false" class="large" rowDetailField="false" showRowNum="true">
            <field name="opticCode" label="光路编码"/>
            <field name="NAME" label="电路名称"/>
            <field name="METACATEGORYCN" label="电路类型"/>
            <field name="CUST_MANAGER" label="客户经理"/>
            <field name="CUST_MANAGER_TEL" label="客户经理电话"/>
            <field name="MNT_LEVEL_ID" label="维护等级"/>
            <field name="SERVICE_LEVEL_ID" label="客户服务等级"/>
            <field name="RATE_ID" label="速率"/>
            <field name="A_DEVICE" label="A端设备"/>
            <field name="A_PORT" label="A端端口"/>
            <field name="z_DEVICE" label="Z端设备"/>
            <field name="Z_PORT" label="Z端端口"/>
            <field name="aaddress" label="A端地址"/>
            <field name="zaddress" label="Z端地址"/>
            <field name="aregion" label="A端行政区"/>
            <field name="zregion" label="Z端行政区"/>
            <field name="product.customer.NAME" label="客户名称"/>
            <field name="product.customer.PHONE" label="客户联系电话"/>
            <field name="product.INSTALLADDRESS_NAME" label="客户地址"/>
            <field name="product.A_CONTACTOR" label="A端联系人"/>
            <field name="product.Z_CONTACTOR" label="Z端联系人"/>
            <field name="product.A_PHONE" label="A端电话"/>
            <field name="product.Z_PHONE" label="Z端电话"/>
            <field name="BACKUP_ID" label="保护电路"/>
        </grid>
        <grid name="searchLightPathData" autoLoad="false" rowDetailField="false">
            <field name="CODE"/>
            <field name="NAME"/>
            <field name="SINGLE_TYPE_ID"/>
            <field name="LENGTH"/>
            <field name="USING_STATE_ID"/>
            <field name="LOCK_STATE_ID"/>
            <field name="SERVICE_NO"/>
            <field name="BUSI_ORDER_CODE"/>
            <field name="OPTIC_CODE_OLD"/>
<!--            <field name="SHARDING_ID"/>-->
            <field name="TEXT_ROUTE" width="200px"/>
            <field name="NOTES" width="200px"/>
        </grid>
        <!--引入第三方辅助数据-光路列表-->
        <grid name="introduceLocationOpticalGrid" autoLoad="false" rowDetailField="false" filterable="false" sortable="false">
            <field name="CODE"/>
            <field name="NAME"/>
            <field name="SINGLE_TYPE_ID"/>
            <field name="OPTICAL_COLOR" label="显示颜色" maxWidth="70px"/>
            <field name="LENGTH"/>
            <field name="USING_STATE_ID"/>
            <field name="LOCK_STATE_ID"/>
            <field name="SERVICE_NO"/>
            <field name="BUSI_ORDER_CODE"/>
            <field name="OPTIC_CODE_OLD"/>
            <field name="TEXT_ROUTE"/>
            <field name="NOTES"/>
        </grid>
        <grid>
            <field name="CODE"/>
            <field name="NAME"/>
            <field name="SINGLE_TYPE_ID"/>
            <field name="adevice"/>
            <field name="zdevice"/>
        </grid>
        <grid toolbar="false" rowDetailField="false" name="opticGroupLocate">
            <field name="GROUP" label="组别"/>
            <field name="CODE" label="光路编码"/>
            <field name="NAME" label="光路名称" showMaxLength="23"/>
            <field name="SINGLETYPEID_DESCRIPTION" label="光路纤数" getLabel="row.SINGLE_TYPE_ID"/>
            <field name="OPTICAL_COLOR" label="显示颜色" filterable="false" sortable="false" maxWidth="70px"/>
        </grid>
        <grid name="ftth_main" queryActionName="listFtthMain" baseParams='{"opticId":"${id}"}'
              showRowNum="true" toolbar="false" footer="false" rowDetailField="false">
            <field name="CODE" label="主光路编码"/>
            <field name="obdCode" label="OBD编码"/>
            <field name="ponPortName" label="PON口"/>
            <field name="parentDeviceCode" label="所属设备编码"/>
            <field name="parentDevicePortCode" label="所属设备端子号"/>
            <field name="installAddress" label="安装地址"/>
        </grid>
        <grid name="ftth_children" queryActionName="listFtthChildren" baseParams='{"opticId":"${id}"}'
              showRowNum="true" toolbar="false" footer="false" rowDetailField="false">
            <field name="CODE" label="子光路编码"/>
            <field name="customerName" label="客户名称"/>
            <field name="crmProductId" label="专线编码"/>
            <field name="onuLoid" label="SN号"/>
            <field name="installAddress" label="客户地址"/>
            <field name="onuCode" label="ONU编码"/>
            <field name="parentDeviceCode" label="覆盖设备编码"/>
            <field name="parentDevicePortCode" label="覆盖设备端子"/>
        </grid>
        <action type="workspace" name="editroute" objectType="OPTICALCIRCUIT" label="光路编辑" label-en="Light path editing"
                title="${NAME}-光路编辑" title-en="${NAME} - Optical path edit"
                url="modules/opticroute/opticroute_editor.html?id=${id}"/>
        <script>
            <![CDATA[
            om.findGrid('list_circuits').fields.forEach(f => f.width = '100px');
        ]]>
        </script>
    </ObjMeta>

    <ObjMeta objectType="MODULE.TEMPLATE" autoReload="true" itemActions="modify,moduleconnview,remove" labelField="NAME">
        <action type="workspace" name="moduleconnview" label="端子图" label-en="Panel Diagram" icon="delicious"
                extra="_ui_class:gray" title="端子面板图-${NAME}" title-en="Panel Diagram-${NAME}"
                url="modules/connector/moduleTemplateConnector/moduleTemplateConnector.html?id=${id}"/>
        <form>
            <row>
                <field name="NAME"/>
                <field name="CODE"/>
            </row>
            <row>
                <field name="UNITNUM" label="模块内单元数" required="true"/>
                <field name="UNITINNERARRANGE_ID" label="单元排序" required="true" default="60000166"/>
            </row>
            <row>
                <field name="MODSTARTNO" label="起始模块编号" required="true"/>
            </row>
            <row>
                <field name="ROW_NO" required="true"/>
                <field name="COL_NO" required="true"/>
            </row>
            <row>
                <field name="PORT_ROW_NUM" label="端子行数"/>
                <field name="PORT_COL_NUM" label="端子列数"/>
            </row>
            <row>
                <field name="CONNECTORSTARTNO" label="起始端子编号" required="true"/>
                <field name="MODINNERARRANGEID" label="单元内排列方式" required="true" default="80402151"/>
            </row>
            <row>
                <field name="MODNUM" label="每单元内板数" required="true" onChange="PORT_ROW_NUM=MODNUM"/>
                <field name="CONNECTORNUM" label="每板端子数" required="true"
                       onChange="PORT_COL_NUM=MODNUM/UNITNUM*CONNECTORNUM"/>
            </row>
            <row>
                <field name="CONNECTORCODEMODE" label="端子编号模式" required="true" default="60000165"/>
            </row>
            <row>
                <field name="HEIGHT"/>
                <field name="LENGTH"/>
                <field name="WIDTH"/>
            </row>
        </form>
    </ObjMeta>
    <!--PON端口 -->
    <ObjMeta objectType="PONPORT" autoReload="true" needgeom="false" itemActions="modify">
        <grid rowDetailField="false" toolbar="false">
            <field name="STANDARD_NAME"/>
            <field name="USING_STATE_ID"/>
        </grid>
        <grid rowDetailField="false" toolbar="false" name="OLTPORT">
            <field name="card" label="板卡编码"/>
            <field name="SEQ" label="端子序号"/>
            <field name="CODE" label="端子编码"/>
        </grid>
    </ObjMeta>
    <!--连接端子 -->
    <ObjMeta objectType="CONNECTPORT" autoReload="true" needgeom="false" itemActions="modify">
        <grid rowDetailField="false" toolbar="false">
            <field name="STANDARD_NAME"/>
            <field name="USING_STATE_ID"/>
        </grid>
        <grid rowDetailField="false" toolbar="false" name="projectBindGrid">
            <field name="card" label="所属模块"/>
            <field name="SEQ" label="端子序号"/>
            <field name="CODE" label="端子编码"/>
        </grid>
        <grid rowDetailField="false" toolbar="false" name="OBDPORT">
            <field name="device" label="设备名称" getLabel="row.device_value.NAME"/>
            <field name="device" label="设备编码"/>
            <field name="CODE" label="端子编码"/>
        </grid>
    </ObjMeta>

    <ObjMeta objectType="ODEVICE" autoReload="true" typeActions="add,remove" itemActions="locate,modify,devInfo,remove">
        <grid name='devInfo'>
            <field name="id" label="资源ID" width="180px"/>
            <field name="DCODE" label="本端设备编码" width="180px"/>
            <field name="DNAME" label="本端设备名称" width="180px"/>
            <field name='CODE' label="本端端子" label-en="Terminal terminal" orderBy="true"/>
            <field name='ocablesectionName' label="所属光缆名称" label-en="belong Cable Name"/>
            <field name='belongCableSectionName' label="所属光缆段名称" label-en="belong Cable Section Name"/>
            <field name='fiber' label="纤芯" label-en="Fiber"/>
            <field name='jumpLink' label="跳接端子" label-en="Jump Link"/>
            <field name='jumpLinkStandardName' label="跳接端子集团名称"/>
            <field name='jumpOppPort' label="对端设备端子" label-en="Terminal of opposite equipment"/>
            <field name='jumOppDeviceName' label="对端设备名称" label-en="Terminal of opposite equipment"/>
<!--            <field name='oppDevCoverAddress' label="对端设备覆盖地址" label-en="Terminal of opposite equipment cover address"/>-->
            <field name='opticLinkCode' label="光路编码" label-en="Optic link code"/>
            <field name='areaOpticName2' label='本地网光路名称'/>
            <field name='opticLinkAccessCode' label="接入号" label-en="Optic link access code" getLabel="(row.opticLinkAccessCode?row.opticLinkAccessCode:(row.opticLinkServiceNo?row.opticLinkServiceNo:row.GDY_PX_ACCESS_CODE))"/>
<!--            <field name='opticLinkServiceNo' label="业务代号" label-en="Optic link code"/>-->
            <field name='opticLinkType' label="光路类型" label-en="Optic link type"/>
            <field name='opticLinkState' label="光路状态" label-en="Optic link state"/>
            <field name='opticLinkLength' label="光路长度" label-en="Optic link length"/>
            <field name='oldOpticLinkCode' label="原光路编码" label-en="Old optic link code"/>
            <field name='opticlinkName' label="光路名称" label-en="Optic link name"/>
            <field name='customerName' label="客户名称" label-en="Customer name"/>
            <field name='installAddress' label="安装地址" label-en="Install address"/>
            <field name='privateCode' label="专线编码" label-en="Private line coding"/>
            <field name='opticLinkFinishDate' label="光路竣工日期" label-en="Optic link finish date"/>
            <field name='busType' label="业务类型" label-en="Business type"/>
            <field name='siteFiber' label="局向光纤" label-en="Site fiber"/>
            <field name='OBDSectionTag' label="跳纤段标签" label-en="jumpLink section tag"/>
            <field name='ONUSectionTag' label="入户纤标签"/>
            <field name='barCode' label="条形码"/>
            <field name="opticOldCode" label="光路旧编码"/>
            <field name="opticNotes" label="光路备注"/>
            <field name="logfiberName" label="局向光纤名称"/>
            <field name="notes" label="备注"/>
            <field name="modifyDate" label="更新时间"/>
            <field name="sceneParaId" label="OBD场景参数"/>
            <field name="manageModeId" label="OBD管理模式"/>
        </grid>
        <grid name="FTTX10ODEVICE" filterable="false" sortable="false">
            <field name="METACATEGORYCN" label="光设施类型"/>
            <field name="CODE" label="光设施编码"/>
            <field name="NAME" label="光设施名称"/>
            <field name="NAME" label="OBD数量" getLabel="row.devices_value ? row.devices_value.length : ''"/>
        </grid>
        <!--        <form>-->
        <!--            <field name="METACATEGORYCN"/>-->
        <!--            <field name="CODE"/>-->
        <!--            <field name="NAME"/>-->
        <!--        </form>-->

        <!--        <form>-->
        <!--            <field name="wares"/>-->
        <!--            <field name="CODE"/>-->
        <!--            <field name="NAME"/>-->
        <!--            <field name="SPLITRATIO"/>-->
        <!--        </form>-->

        <grid name="FTTXODEVICE" filterable="false" sortable="false" tableStyle="width:2000px">
            <field name="room_outdooraddress" label="所属机房"/>
            <field name="METACATEGORYCN" label="设备类型" width="100px"/>
            <field name="CODE" label="设备编码"/>
            <field name="NAME" label="设备名称" width="250px"/>
            <field name="addressobj" label="标准地址" getLabel="row.addressobj_value ? row.addressobj_value.FULL_NAME : ''" showMaxLength="15" width="250px"/>
            <field name="ADDRESS_DESC" label="具体位置" showMaxLength="15" width="250px"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value ? row.smallcounty_value.NAME : ''"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value ? row.marketingarea_value.NAME : ''"/>
            <field name="servicearea" label="划小营业区" getLabel="row.servicearea_value ? row.servicearea_value.NAME : ''"/>
        </grid>
        <grid name="deviceprojectinfo">
            <field name="acode" label="A端设备编码" orderBy="false"/>
            <field name="apcode" label="A端端子编码" orderBy="false"/>
            <field name="astcode" label="A端集团编码" orderBy="false"/>
            <field name="zcode" label="Z端设备编码" orderBy="false"/>
            <field name="zpcode" label="Z端端子编码" orderBy="false"/>
            <field name="zstcode" label="Z端集团编码" orderBy="false"/>
        </grid>
        <!--        <form>-->
        <!--            <field name="room"/>-->
        <!--            <field name="METACATEGORYCN"/>-->
        <!--            <field name="CODE"/>-->
        <!--            <field name="NAME"/>-->
        <!--            <field name="address"/>-->
        <!--            <field name="ADDRESS_DESC"/>-->
        <!--            <field name="smallcounty"/>-->
        <!--            <field name="servicearea"/>-->
        <!--            <field name="marketingarea"/>-->
        <!--        </form>-->
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="region" label="所属区域"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县"/>
                <field name="marketingarea" label="划小营销区"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="地理位置"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:6000px;" autoLoad="false" baseParams='{"SPEC_ID":"OBD,ODF,GJ,GF,GB,ZHX,IDF,GT,ONU,OLTDEVICE,GRESERVE,XBOX,HUBBOX"}'>
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称" width="250px"/>
            <field name="METACATEGORYCN" label="类型"/>
            <field name="site" label="所属局站" getLabel="row.site_value ? row.site_value.NAME : ''"/>
            <field name="room_outdooraddress" label="所属机房" getLabel="row.room_outdooraddress_value ? row.room_outdooraddress_value.NAME : ''"/>
            <field name="region" label="所属区域" getLabel="row.region_value ? row.region_value.NAME : ''"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value ? row.smallcounty_value.NAME : ''"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value ? row.marketingarea_value.NAME : ''"/>
            <field name="terminal_value.PORTS_TOTAL" label="端口总数"/>
            <field name="terminal_value.PORTS_USED" label="端子占用数"/>
            <field name="terminal_value.PORTS_FREE" label="端子空闲数"/>
            <field name="terminal_value.PORTS_CONNECTED" label="已成端端子数" width="100px"/>
            <field name="terminal_value.PORTS_BAD" label="坏端子数"/>
            <field name="terminal_value.PORTS_USEDRATE" label="端子占用率(%)" width="100px"/>
            <field name="addressobj" label="标准地址" getLabel="row.addressobj_value ? row.addressobj_value.FULL_NAME : ''" width="300px"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="OWNER_NET_ID" label="网络类型"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="CAPACITY" label="标称容量"/>
            <field name="NOTES" label="备注"/>
            <field name="IS_INTEGFLAG" label="一体化标志"/>
            <field name="ADAPTTYPE_ID" label="适配类型"/>
            <field name="CONFIG_TYPE_ID" label="配置点类型"/>
            <field name="INSTALL_WAY_ID" label="安装方式"/>
            <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识" width="150px"/>
            <field name="ASSET_CODE" label="资产编码"/>
            <field name="CREATOR" label="录入人"/>
            <field name="MODIFIER" label="修改人"/>
            <field name="AUDIT_PERSON" label="验收人"/>
            <field name="PROPERTY_TYPE_ID" label="产权性质"/>
            <field name="PROPERTY_OWNER_ID" label="产权归属"/>
            <field name="MNT_LEVEL_ID" label="维护等级"/>
            <field name="MNT_PERSON" label="维护人"/>
            <field name="MNT_DEPT" label="维护单位"/>
            <field name="COLLECTION_DEPT" label="数据采集单位" width="100px"/>
            <field name="COLLECTION_PERSON" label="数据采集人"/>
            <field name="project_value.CODE" label="所属工程"/>
            <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            <field name="CREATE_DATE" label="入库时间"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
            <field name="model" label="型号"/>
            <field name="ADDRESS_ID" label="地址ID"/>
        </grid>
    </ObjMeta>

    <ObjMeta objectType="PORT" needgeom="false">
        <grid rowDetailField="false" toolbar="false" name="OLTPORT" autoLoad="false">
            <field name="OLDCARDCODE" label="板卡名称" width="150px"/>
            <field name="SUBCARDCODE" label="子板卡名称" width="150px"/>
            <field name="card" label="板卡编码" width="150px"/>
            <field name="CODE" label="端口编码" width="150px"/>
            <field name="model" label="板卡型号" width="150px"/>
            <field name="ALIAS" label="端口拼装" width="300px"/>
            <field name="STANDARD_NAME" label="端口集团名称" width="150px"/>

            <field name="maxphyconnections" label="端子成端数" width="80px"/>
        </grid>
        <grid rowDetailField="false" toolbar="false" name="ODFPORT" autoLoad="false">
            <field name="module" label="模块编码"/>
            <field name="SEQ" label="端子序号"/>
            <field name="CODE" label="端子编码"/>
            <field name="model" label="端子型号"/>
        </grid>
    </ObjMeta>

    <ObjMeta objectType="TRANSMISSIONSYSTEM" autoReload="true" needgeom="false">
        <grid>
            <field name="NAME" label="传输系统名称"/>
            <field name="links" label="光路编码" orderBy="false"/>
        </grid>
        <form>
            <field name="NAME"/>
        </form>
        <script>
            <![CDATA[
                om.findGridField('', 'links').getLabel = function(row){
                    var links = [];
                    if(row.sdhms){
                        var sdhms = row.sdhms;
                        if(!Array.isArray(sdhms)){
                            sdhms = row.sdhms_value || [];
                        }
                        for(var sdh of sdhms){
                            if(sdh.links){
                                var tmp = sdh.links;
                                for(var t of tmp){
                                    links.push(t.CODE);
                                }
                            }
                        }
                    }
                    return links.join(',');
                };
            ]]>
        </script>
    </ObjMeta>
    <ObjMeta objectType="SITEFIBER" autoReload="false" needgeom="false">
        <action name="locate" type="script">
            <![CDATA[
                var id=_actionContext.params.id;
            	_context.doAction("opticMapLocate",{ids:[id],type:"SITEFIBER"});
        	]]>
        </action>
        <grid>
          <field name="NAME" label="局向光纤名称" />
          <field name="CODE" label="局向光纤编码" />
          <field name="USING_STATE_ID" label="业务状态" />
          <field name="optical.CODE" label="光路编码" getLabel="row.upperroute_value?row.upperroute_value.belongOpticalcircuits?row.upperroute_value.belongOpticalcircuits.CODE:'':''"/>
          <field name="adevice_value.NAME" label="A端设备名称" />
          <field name="adevice_value.CODE" label="A端设备编码" />
          <field name="aport_value.CODE" label="A端设备端子" type="text" />
          <field name="zdevice_value.NAME" label="Z端设备名称"/>
          <field name="zdevice_value.CODE" label="Z端设备编码"/>
          <field name="zport_value.CODE" label="Z端设备端子" type="text" />
          <field label="本地网光路名称" name="optical.AREA_OPTIC_NAME" getLabel="row.upperroute_value?row.upperroute_value.belongOpticalcircuits?row.upperroute_value.belongOpticalcircuits.AREA_OPTIC_NAME:'':''"/>
          <field label="光路名称" name="optical.NAME" getLabel="row.upperroute_value?row.upperroute_value.belongOpticalcircuits?row.upperroute_value.belongOpticalcircuits.NAME:'':''"/>
          <field label="调单号" name="optical.BUSI_ORDER_CODE" getLabel="row.upperroute_value?row.upperroute_value.belongOpticalcircuits?row.upperroute_value.belongOpticalcircuits.BUSI_ORDER_CODE:'':''"/>
          <field label="接入号" name="optical.SERVICE_NO" getLabel="row.upperroute_value?row.upperroute_value.belongOpticalcircuits?row.upperroute_value.belongOpticalcircuits.SERVICE_NO:'':''"/>
          <field label="长途电路代号" name="optical.LONG_DIS_CODE" getLabel="row.upperroute_value?row.upperroute_value.belongOpticalcircuits?row.upperroute_value.belongOpticalcircuits.LONG_DIS_CODE:'':''"/>
          <field label="光路旧编码" name="optical.OPTIC_CODE_OLD" getLabel="row.upperroute_value?row.upperroute_value.belongOpticalcircuits?row.upperroute_value.belongOpticalcircuits.OPTIC_CODE_OLD:'':''"/>
          <field label="旧光路编码" name="optical.OLD_OPTIC_CODE" getLabel="row.upperroute_value?row.upperroute_value.belongOpticalcircuits?row.upperroute_value.belongOpticalcircuits.OLD_OPTIC_CODE:'':''"/>
        </grid>
        <grid name="sitefiberQuery" typeActions="" itemActions="locate,modify">
            <field name="code" label="局向光纤编码"/>
            <field name="name" label="局向光纤名称"/>
            <field name="seq" label="局向光纤序号"/>
            <field name="length" label="局向光纤长度"/>
            <field name="cable_model" label="光缆模式"/>
            <field name="afacility_name" label="起始机房名称"/>
            <field name="afacility_code" label="起始机房编码"/>
            <field name="a_physic_device_code" label="起始设备编码"/>
            <field name="a_port_code" label="起始端子"/>
            <field name="zfacility_name" label="终止机房名称"/>
            <field name="zfacility_code" label="终止机房编码"/>
            <field name="z_physic_device_code" label="终止设备编码"/>
            <field name="z_port_code" label="终止端子"/>
            <field name="servicestatus" label="业务状态"/>
            <field name="physicalstatus" label="物理状态"/>
            <field name="opticcode" label="光路编码"/>
            <field name="opticname" label="光路名称"/>
            <field name="lfgroup_name" label="局向光纤组名称"/>
            <field name="lfgroup_code" label="局向光纤组编码"/>
            <field name="net_name" label="光缆名称"/>
            <field name="net_code" label="光缆编码"/>
            <field name="createdate" label="创建时间"/>
            <field name="modifydate" label="修改时间"/>
            <field name="creator" label="光路录入人"/>
            <field name="modifier" label="光路修改人"/>
            <field name="id" label="局向光纤id"/>
        </grid>

        <grid name="querySelectGrid" tableStyle="width:1000px">
            <field name="NAME" label="局向光纤名称" />
            <field name="CODE" label="局向光纤编码" />
            <field name="USING_STATE_ID" label="业务状态" />
            <field name="optical.CODE" label="光路编码" getLabel="row.upperroute_value?row.upperroute_value.belongOpticalcircuits?row.upperroute_value.belongOpticalcircuits.CODE:'':''"/>
            <field name="adevice_value.NAME" label="A端设备名称" />
            <field name="adevice_value.CODE" label="A端设备编码" />
            <field name="aport_value.CODE" label="A端设备端子" />
            <field name="zdevice_value.NAME" label="Z端设备名称"/>
            <field name="zdevice_value.CODE" label="Z端设备编码"/>
            <field name="zport_value.CODE" label="Z端设备端子" />
            <field label="本地网光路名称" name="optical.AREA_OPTIC_NAME" getLabel="row.upperroute_value?row.upperroute_value.belongOpticalcircuits?row.upperroute_value.belongOpticalcircuits.AREA_OPTIC_NAME:'':''"/>
            <field label="光路名称" name="optical.NAME" getLabel="row.upperroute_value?row.upperroute_value.belongOpticalcircuits?row.upperroute_value.belongOpticalcircuits.NAME:'':''"/>
            <field label="调单号" name="optical.BUSI_ORDER_CODE" getLabel="row.upperroute_value?row.upperroute_value.belongOpticalcircuits?row.upperroute_value.belongOpticalcircuits.BUSI_ORDER_CODE:'':''"/>
            <field label="接入号" name="optical.SERVICE_NO" getLabel="row.upperroute_value?row.upperroute_value.belongOpticalcircuits?row.upperroute_value.belongOpticalcircuits.SERVICE_NO:'':''"/>
            <field label="长途电路代号" name="optical.LONG_DIS_CODE" getLabel="row.upperroute_value?row.upperroute_value.belongOpticalcircuits?row.upperroute_value.belongOpticalcircuits.LONG_DIS_CODE:'':''"/>
            <field label="光路旧编码" name="optical.OPTIC_CODE_OLD" getLabel="row.upperroute_value?row.upperroute_value.belongOpticalcircuits?row.upperroute_value.belongOpticalcircuits.OPTIC_CODE_OLD:'':''"/>
            <field label="旧光路编码" name="optical.OLD_OPTIC_CODE" getLabel="row.upperroute_value?row.upperroute_value.belongOpticalcircuits?row.upperroute_value.belongOpticalcircuits.OLD_OPTIC_CODE:'':''"/>
        </grid>
        <form>
            <row>
                <field name="SEQ" onChange="CODE=@updateCodeSeqAssemble(SEQ);NAME=@updateNameSeqAssemble(SEQ);"/>
                <field label="旧序列" name="OLDSEQ" readOnly="readonly"/>
            </row>
            <row>
                <field name="CODE"/>
                <field label="旧编码" name="OLDCODE" readOnly="readonly"/>
            </row>
            <row>
                <field name="NAME"/>
                <field label="旧名称" name="OLDNAME" readOnly="readonly"/>
            </row>
            <!--<row>
                <field name="adevice"/>
                <field name="zdevice"/>
            </row>
            <row>
                <field name="aport"/>
                <field name="zport"/>
            </row>
            <row>
                <field name="LENGTH"/>
                <field name="USING_STATE_ID"/>
            </row>
            <row>
                <field name="ATTENUATION"/>
                <field name="LOCK_STATE_ID"/>
            </row>
            <row>
                <field name="TEXT_ROUTE"/>
            </row>
            <row>
                <field name="CREATE_DATE"/>
            </row>
            <row>
                <field name="NOTES"/>
            </row>-->
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!--更新编码序号拼装-->
        <action name="updateCodeSeqAssemble" type="script">
            <![CDATA[
                // 序号
                let seq = SEQ + ''
                // 编码
                let code = CODE
                if(seq){
                    if(seq.length == 1){
                        seq = '00' + seq
                    }else if(seq.length == 2){
                        seq = '0' + seq
                    }
                }
                if(seq && code && code.indexOf('/FX')!=-1){
                    // 切割
                    const codes = code.split('/FX')
                    CODE = codes[0] + '/FX' + seq +codes[1].replace(/[0-9]/g , '')
                }
            ]]>
        </action>
        <!--更新名称序号拼装-->
        <action name="updateNameSeqAssemble" type="script">
            <![CDATA[
                // 序号
                let seq = SEQ + ''
                // 名称
                let name = NAME
                if(seq){
                    if(seq.length == 1){
                        seq = '00' + seq
                    }else if(seq.length == 2){
                        seq = '0' + seq
                    }
                }
                if(seq && name && name.indexOf('/FX')!=-1){
                    // 切割
                    const names = name.split('/FX')
                    NAME = names[0] + '/FX' + seq + names[1].replace(/[0-9]/g , '')
                }
            ]]>
        </action>
    </ObjMeta>
    <ObjMeta objectType="INFORMATIONPOINT" autoReload="true" label="信息点" label-en="INFO POINT" itemActions="modify,remove,connview">
        <form>
            <row>
                <field name="site" required="true" onChange="region=site.REGION_ID"/>
            </row>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="PORT_TYPE_ID"/>
                <field name="CONNECT_LABEL"/>
            </row>
            <row>
                <field name="parentDevice" label="所属设施" onChange="CODE,NAME=@genCode(parentDevice)"/>
            </row>
            <row>
                <field name="NOTES" lable="备注"/>
            </row>
        </form>
        <action name="genCode" type="script">
            <![CDATA[
            var pre = parentDevice.CODE + '/TP';
            _bean.autoCode(this, pre, 3, 'INFORMATIONPOINT', {parentDevice: parentDevice.id});
        ]]>
        </action>
        <action type="workspace" name="connview" label="端子图" label-en="Panel Diagram" icon="delicious"
                extra="_ui_class:gray" title="端子面板图-${NAME}" title-en="Panel Diagram-${NAME}"
                url="econnector.html?objecttype=${objectType}&amp;id=${id}" permission="ELECTRIC_MANAGE"/>
    </ObjMeta>
    <ObjMeta objectType="5GBASESTATION" autoReload="true" itemActions="devicecoveraddress_man,modify,remove">
        <form>
            <field type="tab" label="通用属性" label-en="General property"/>
            <row>
                <field name="CODE" label="编码" label-en="Code"/>
                <field name="NAME" label="名称" label-en="Name"/>
            </row>

            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" label-en="Lifecycle State"/>

                <field name="vendor" label="生产厂家" label-en="Manufacturer" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人" label-en="Maintainer"/>

                <field name="MNT_DEPT" label="维护部门" label-en="Maintenance department"/>
            </row>
            <row>
                <field name="room" label="所属机房" label-en="Affiliated room"/>
                <field name="HEIGHT" label="高度" label-en="Height"/>
            </row>
            <row>
                <field name="NOTES" label="备注" label-en="Comment"/>
            </row>
        </form>
    </ObjMeta>

    <!-- 光缆割接日志 -->
    <ObjMeta objectType="LOG_CUT_PIPE">
      <form name="search">
        <row>
          <field name="CREATOR" />
          <field name="EXECUTOR_NAME" />
          <field name="CODE" label="割接实体编码" />
        </row>
        <row>
          <field name="ACTION_PATH" label="功能菜单" type="dict" multiple="true" dropdownOptions='{"allowEmpty": false}'
            ditems="0-全部,光端子割接-光端子割接,纤芯业务割接-纤芯业务割接,光缆段割接-光缆段割接,光缆段分拆-光缆段分拆,光缆段串行合并-光缆段串行合并,光缆段并行合并-光缆段并行合并,光缆段拆分并行合并-光缆段拆分并行合并,光缆线路割接-光缆线路割接,局向光纤组割接-局向光纤组割接,局向光纤业务对调割接-局向光纤业务对调割接"
          />
          <field name="EXECUTOR_NAME" />
          <field name="CHAN_NO" />
        </row>
      </form>
      <grid showSearch="expand" rowDetailField="false" typeActions="" defaultColumnProperty='{"width": "180px"}'>
        <field name="OPER_SOURCE" />
        <field name="CREATOR" />
        <field name="CHAN_NO" />
        <field name="EXECUTOR_NAME" />
        <field name="EXECUTOR_CODE" />
        <field name="ACTION_TYPE" />
        <field name="SOURCE_ID" />
        <field name="CREATE_DATE" />
        <field name="ACTION_PATH" />

        <field name="SOURCE_SPEC_1" getLabel="row.SOURCE_SPEC_1_METACATEGORYCN" />
        <field name="SOURCE_ID_1" />
        <field name="SOURCE_CODE_1" />
        <field name="SOURCE_SPEC_2" getLabel="row.SOURCE_SPEC_2_METACATEGORYCN" />
        <field name="SOURCE_ID_2" />
        <field name="SOURCE_CODE_2" />
        <field name="HOLD_CABLE_ID_2" />

        <field name="TARGET_SPEC_1" getLabel="row.TARGET_SPEC_1_METACATEGORYCN" />
        <field name="TARGET_ID_1" />
        <field name="TARGET_CODE_1" />
        <field name="SOURCE_LRNGTH_1" />

        <field name="TARGET_SPEC_2" getLabel="row.TARGET_SPEC_2_METACATEGORYCN" />
        <field name="TARGET_ID_2" />
        <field name="TARGET_CODE_2" />
        <field name="SOURCE_LRNGTH_2" />

        <field name="GT_ID" />
        <field name="GT_CODE" />
        <field name="SPLIT_NUM" />
        <field name="TARGET_MESSAGE" />
        <field name="NODE_A_ID" />
        <field name="NODE_A_CODE" />
        <field name="NODE_A_HOLD" />
        <field name="NODE_Z_ID" />
        <field name="NODE_Z_CODE" />
        <field name="NODE_Z_HOLD" />

        <field name="JOINT_START_SEQ" />
        <field name="CUT_POINT_SPEC" getLabel="row.CUT_POINT_SPEC_METACATEGORYCN" />
        <field name="CUT_POINT_ID" />
        <field name="CUT_POINT_CODE" />
      </grid>
    </ObjMeta>
    <!-- 全资源标签打印审计 -->
    <ObjMeta objectType="X_LOG_JTBQ_PRINT.gdy">
        <grid name="logInfoGrid" queryActionName="queryPrintLog">
            <field name="region" label="所属区域" width="100px"/>
            <field name="tml" label="所属局站" width="200px"/>
            <field name="facility" label="所属机房" width="200px"/>
            <field name="source" label="来源" width="100px"/>
            <field name="specid" label="打印设备设施类型" width="100px"/>
            <field name="code" label="设备设施编码" width="200px"/>
            <field name="id" label="设备设施ID" width="100px"/>
            <field name="exporttype" label="导出规格" width="100px"/>
            <field name="projectname" label="工程名称" width="100px"/>
            <field name="projectcode" label="工程编码" width="100px"/>
            <field name="proserialno" label="工程流水号" width="100px"/>
            <field name="creator" label="操作人" width="100px"/>
            <field name="exporttime" label="导出时间" width="100px"/>
        </grid>
        <grid name="statisInfoGrid" queryActionName="queryPrintCount">
            <field name="specid" label="资源类型" width="100px"/>
            <field name="region" label="所属区域" width="100px"/>
            <field name="tmlname" label="所属局站" width="200px"/>
            <field name="facility" label="所属机房" width="200px"/>
            <field name="count" label="导出次数" width="100px"/>
            <field name="creator" label="操作账号" width="100px"/>
        </grid>
        <grid name="rateInfoGrid" queryActionName="queryPrintRate">
            <field name="shardingname" label="本地网" width="100px"/>
            <field name="regionname" label="分区域" width="100px"/>
            <field name="specname" label="设备类型" width="100px"/>
            <field name="printcount" label="已打印数" width="100px"/>
            <field name="count" label="实体总数" width="100px"/>
            <field name="rate" label="标签打印率" width="100px"/>
        </grid>
    </ObjMeta>

    <!--    缆段造价及原值净值配置表-->
    <ObjMeta objectType="X_SZSL_CABLE_COST" typeActions="add,remove" itemActions="modify,remove" autoReload="true">
        <grid autoLoad="true">
            <field name="BEGIN_COUNT" label="缆段纤芯数起始值"/>
            <field name="END_COUNT" label="缆段纤芯数终止值(含)"/>
            <field name="CABLE_KM_COST" label="公里造价(元)"/>
            <field name="DEPRECIATION" label="年原值折旧率(%)"/>
            <field name="MONTH_COST" label="维护成本(元/月/皮长公里)"/>
            <field name="NOTES" label="备注"/>
        </grid>
        <!--新增/修改面板-->
        <form labelWidth="10rem">
            <field type="divider" label="基本属性"/>
            <row>
                <field name="BEGIN_COUNT" label="缆段纤芯数起始值" required="true" type="number" />
                <field name="END_COUNT" label="缆段纤芯数终止值(含)" required="true" type="number"/>
            </row>
            <row>
                <field name="CABLE_KM_COST" label="公里造价(元)" type="number"/>
                <field name="DEPRECIATION" label="年原值折旧率(%)" type="number"/>
            </row>
            <row>
                <field name="MONTH_COST" label="维护成本(元/月/皮长公里)" type="number"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
            </row>
        </form>
    </ObjMeta>

    <!--    设备设施造价及原值净值配置表-->
    <ObjMeta objectType="X_SZSL_ENTITY_COST" typeActions="add,remove" itemActions="modify,remove" autoReload="true">
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="MODE_NAME" label="设备设施型号名"/>
                <field name="SPEC_NAME" label="设备设施类型名"/>
            </row>
        </form>
        <grid autoLoad="true">
            <field name="SPEC_ID" label="设备设施类型ID"/>
            <field name="SPEC_NAME" label="设备设施类型名"/>
            <field name="MODE_ID" label="设备设施型号"/>
            <field name="MODE_NAME" label="设备设施型号名"/>
            <field name="ENTITY_COST" label="造价(元)"/>
            <field name="DEPRECIATION" label="年原值折旧率(%)"/>
            <field name="YEAR_COST" label="年维护成本"/>
            <field name="NOTES" label="备注"/>
        </grid>
        <!--新增/修改面板-->
        <form labelWidth="8rem">
            <field type="divider" label="基本属性"/>
            <row>
                <field name="spec" label="设备设施类型名" onChange="${model}.dropdown.setBaseParam('TYPESPEC_ID',${spec}.getValue() || -1)" required="true"/>
                <field name="model" label="设备设施型号名" required="true"/>
            </row>
            <row>
                <field name="ENTITY_COST" label="造价(元)" type="number"/>
                <field name="YEAR_COST" label="年维护成本" type="number"/>
            </row>
            <row>
                <field name="DEPRECIATION" label="年原值折旧率(%)" type="number"/>
                <field name="NOTES" label="备注"/>
            </row>
        </form>
    </ObjMeta>

    <!-- 设备设施类型 -->
    <ObjMeta objectType="COSTSPEC" autoReload="true" itemActions="locate">
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="名称"/>
                <field name="CODE" label="编码"/>
            </row>
        </form>
        <grid name="search">
            <field name="NAME" label="名称"/>
            <field name="CODE" label="编码"/>
        </grid>
    </ObjMeta>

    <!--型号-->
    <ObjMeta objectType="MODEL" autoReload="true" itemActions="locate">
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="名称"/>
                <field name="CODE" label="编码"/>
            </row>
        </form>
        <grid name="search" appendColumnActions="view">
            <field name="NAME" label="名称"/>
            <field name="CODE" label="编码"/>
        </grid>
    </ObjMeta>

    <!--    局站租赁信息-->
    <ObjMeta objectType="X_SZSL_TML_RENT" typeActions="add,remove" itemActions="modify,remove" autoReload="true">
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="TML_CODE" label="局站编码"/>
                <field name="TML_NAME" label="局站名称"/>
            </row>
        </form>
        <grid autoLoad="true">
            <field name="TML_ID" label="局站ID"/>
            <field name="TML_CODE" label="局站编码"/>
            <field name="TML_NAME" label="局站名称"/>
            <field name="RENT" label="局站租赁费(元/月)"/>
            <field name="CREATE_DATE" label="创建时间"/>
            <field name="NOTES" label="备注"/>
        </grid>
        <!--新增/修改面板-->
        <form labelWidth="8rem">
            <field type="divider" label="基本属性"/>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="RENT" label="局站租赁费(元/月)" type="number"  required="true"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
            </row>
        </form>
    </ObjMeta>

     <!--建筑楼宇租赁信息-->
    <ObjMeta objectType="X_SZSL_BUILDING_RENT" typeActions="add,remove,buildingRentImp" itemActions="modify,remove"
             autoReload="true">
        <action name="buildingRentImp" type="script" label="导入" label-en="Import" icon="upload">
            <![CDATA[
                $.requestUtil.importFile('threeResourcesRate', 'importBuildingRent', {}, (res) => {
                    if (res.length > 0) {
                        _sui.alert(res)
                        _actionContext.source.reload()
                    } else {
                        _sui.alert('导入失败，请联系管理')
                        return
                    }
                })
            ]]>
        </action>
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="BUILDING_NAME" label="所属楼宇名称"/>
                <field name="BUILDING_ADDRESS_NAME" label="所属楼宇地址名称"/>
            </row>
        </form>
        <grid autoLoad="true">
            <field name="BUILDING_ID" label="楼宇ID"/>
            <field name="BUILDING_NAME" label="楼宇名称"/>
            <field name="BUILDING_ADDRESS_ID" label="楼宇地址ID"/>
            <field name="BUILDING_ADDRESS_NAME" label="楼宇地址名称"/>
            <field name="BUILDING_ADDRESS_LEVEL" label="楼宇地址级别"/>
            <field name="BUILDING_TYPE_NAME" label="建筑楼宇物类型"/>
            <field name="RENT" label="建筑楼宇租赁费(元/月)"/>
            <field name="RENT_NOTES" label="租金备注"/>
            <field name="NOTES" label="备注"/>
        </grid>
        <!--新增/修改面板-->
        <form labelWidth="8rem">
            <field type="divider" label="基本属性"/>
            <row>
                <field name="x_szsl_building_address" label="所属建筑楼宇物" required="true"
                       dropdownOptions="titleField:BUILDING_ADDRESS_NAME"
                       baseParams='{"BUILDING_TYPE_NAME":"商场市场,商业办公,服务建筑,宾馆酒店","_op.BUILDING_TYPE_NAME":"in"}'/>
            </row>
            <row>
                <field name="RENT" label="楼宇租赁费(元/月)" type="number" required="true"/>
            </row>
            <row>
                <field name="RENT_NOTES" label="租金备注"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
            </row>
        </form>
    </ObjMeta>

    <!--专业维护单价配置表-->
    <ObjMeta objectType="X_SZSL_SPECIALITY_COST" typeActions="remove" itemActions="modify,remove" autoReload="true">
        <form name="search" labelWidth="10em" formStyle="width:600px" >
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="BELONG_SPECIALITY_ID" label="专业类型" type="dict" required="true"
                       ditems="80204669-传输,80204675-光缆,80204671-交换,80204670-数据,80204676-电缆,80204672-无线,80204674-公共,80204673-动力"
                />
            </row>
            <row>
                <field name="YEAR_COST" label="参考年维护成本"/>
            </row>
            <row>
                <field name="PERCENT" label="维护成本折扣率(%)"/>
            </row>
        </form>
        <grid autoLoad="true">
            <field name="BELONG_SPECIALITY_ID" label="专业类型ID"/>
            <field name="BELONG_SPECIALITY_NAME" label="专业类型名"/>
            <field name="YEAR_COST" label="参考年维护成本（元/年/每专业设备）"/>
            <field name="PERCENT" label="维护成本折扣率(%)，暂默认100%，校验范围：1%~200%"/>
            <field name="REALITY_YEAR_COST" label="实际维护成本（元/年/每专业设备,由参考维护成本*维护成本折扣率自动算出）"/>
            <!--            <field name="DEPRECIATION" label="年原值折旧率(%)"/>-->
            <field name="NOTES" label="备注"/>
            <field name="sharding" label="地市" getLabel="row.sharding_value.NAME"/>
        </grid>
        <!--新增/修改面板-->
        <form labelWidth="10rem">
            <field type="divider" label="基本属性"/>
            <row>
                <field name="BELONG_SPECIALITY_ID" label="专业类型" type="dict"
                       ditems="80204669-传输,80204675-光缆,80204671-交换,80204670-数据,80204676-电缆,80204672-无线,80204674-公共,80204673-动力"
                />
            </row>
            <row>
                <field name="YEAR_COST" label="参考年维护成本" type="number" required="true"/>
            </row>
            <row>
                <field name="PERCENT" label="维护成本折扣率(%)" required="true"/>
            </row>
            <row>
                <field name="REALITY_YEAR_COST" label="实际年维护成本（元/年/每专业设备）" readOnly="true"/>
            </row>
            <row>
                <field name="NOTES" label="备注" width="200px"/>
                <field name="sharding" label="地市" width="200px" dropdownOptions="titleField:NAME"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
    </ObjMeta>
</metas>