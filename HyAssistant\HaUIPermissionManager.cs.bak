using ET;
using ET.ETLicense;
using System;
using System.Drawing;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HyAssistant
{
    /// <summary>
    /// HyAssistant UI权限管理器 - 专门处理菜单权限控制和过期时间显示
    /// </summary>
    internal class HaUIPermissionManager
    {
        #region 字段

        /// <summary>
        /// 权限管理器实例
        /// </summary>
        private readonly ETPermissionManager _permissionManager;

        /// <summary>
        /// 主窗体实例
        /// </summary>
        private readonly MainForm _mainForm;

        #endregion 字段

        #region 构造函数

        /// <summary>
        /// 初始化UI权限管理器
        /// </summary>
        /// <param name="permissionManager">权限管理器实例</param>
        /// <param name="mainForm">主窗体实例</param>
        public HaUIPermissionManager(ETPermissionManager permissionManager, MainForm mainForm)
        {
            _permissionManager = permissionManager ?? throw new ArgumentNullException(nameof(permissionManager));
            _mainForm = mainForm ?? throw new ArgumentNullException(nameof(mainForm));
        }

        #endregion 构造函数

        #region 初始化方法

        /// <summary>
        /// 初始化UI权限管理器
        /// </summary>
        public void Initialize()
        {
            try
            {
                ETLogManager.Info("开始初始化HyAssistant UI权限管理器");

                // 初始化权限管理器
                _permissionManager.InitializePermissions();

                ETLogManager.Info("HyAssistant UI权限管理器初始化完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"初始化HyAssistant UI权限管理器时出错: {ex.Message}", ex);
                throw;
            }
        }

        #endregion 初始化方法

        #region 权限检查方法

        /// <summary>
        /// 检查是否有指定权限
        /// </summary>
        /// <param name="permissionName">权限名称</param>
        /// <returns>是否有权限</returns>
        public bool HasPermission(string permissionName)
        {
            try
            {
                return _permissionManager.HasPermission(permissionName);
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"检查HyAssistant权限时出错: {permissionName}", ex);
                return false; // 出错时默认拒绝权限（安全策略）
            }
        }

        #endregion 权限检查方法

        #region 权限刷新方法

        /// <summary>
        /// 强制刷新权限缓存并更新UI界面
        /// </summary>
        public void ForceRefreshPermissionsAndUI()
        {
            try
            {
                ETLogManager.Info("开始强制刷新HyAssistant权限缓存和UI界面");

                // 强制刷新权限缓存
                _permissionManager.ForceRefreshPermissionsAndUI();

                // 刷新UI界面
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await RefreshMenuPermissionsAsync().ConfigureAwait(false);
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error("刷新HyAssistant菜单权限时出错", ex);
                    }
                });

                ETLogManager.Info("HyAssistant权限缓存和UI界面强制刷新完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"强制刷新HyAssistant权限缓存和UI时出错: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 刷新菜单权限状态
        /// </summary>
        public async Task RefreshMenuPermissionsAsync()
        {
            try
            {
                if (_mainForm.InvokeRequired)
                {
                    await Task.Run(() =>
                    {
                        _mainForm.Invoke(new Action(async () => await RefreshMenuPermissionsInternalAsync().ConfigureAwait(true)));
                    }).ConfigureAwait(false);
                }
                else
                {
                    await RefreshMenuPermissionsInternalAsync().ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"刷新HyAssistant菜单权限时出错: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 内部刷新菜单权限状态的实现
        /// </summary>
        private async Task RefreshMenuPermissionsInternalAsync()
        {
            try
            {
                ETLogManager.Debug("正在刷新HyAssistant菜单权限状态...");

                // 重新检查授权管理权限
                bool hasLicensePermission = await HyAssistantLicenseManager.HasPermissionAsync(HaPermissionKeys.License).ConfigureAwait(true);
                bool backdoorFileExists = File.Exists(@"C:\System\ETLicenseBaseInfo.dat");

                // 通过反射获取菜单项（因为菜单项是private的）
                var authMenuField = _mainForm.GetType().GetField("授权管理ToolStripMenuItem",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (authMenuField?.GetValue(_mainForm) is ToolStripMenuItem authMenuItem)
                {
                    authMenuItem.Visible = hasLicensePermission || backdoorFileExists;
                }

                // 定义需要检查的功能列表
                var features = new[]
                {
                    (HaPermissionKeys.FileCopier, "文件复制助手ToolStripMenuItem"),
                    (HaPermissionKeys.Mail, "邮箱附件接收助手ToolStripMenuItem"),
                    (HaPermissionKeys.FileExtract, "文件解压助手ToolStripMenuItem"),
                    (HaPermissionKeys.FileAnalyzer, "监控文件ToolStripMenuItem"),
                    (HaPermissionKeys.VisioPdf, "监控Visio文件自动转PDFToolStripMenuItem"),
                    (HaPermissionKeys.WebBrowser, "网页常挂助手ToolStripMenuItem"),
                    (HaPermissionKeys.WebBrowserV2, "网页常挂助手V2ToolStripMenuItem"),
                    (HaPermissionKeys.ChinaTowerDownload, "中国铁塔照片下载助手ToolStripMenuItem")
                };

                // 重新检查每个功能的权限
                foreach (var (featureName, menuFieldName) in features)
                {
                    var menuField = _mainForm.GetType().GetField(menuFieldName,
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    if (menuField?.GetValue(_mainForm) is ToolStripMenuItem menuItem)
                    {
                        await UpdateFeatureMenuPermission(featureName, menuItem).ConfigureAwait(true);
                    }
                }

                ETLogManager.Debug("HyAssistant菜单权限状态刷新完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"刷新HyAssistant菜单权限内部实现时出错: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 更新单个功能菜单的权限状态
        /// </summary>
        /// <param name="featureName">功能名称</param>
        /// <param name="menuItem">菜单项</param>
        public async Task UpdateFeatureMenuPermission(string featureName, ToolStripMenuItem menuItem)
        {
            try
            {
                bool hasPermission = await HyAssistantLicenseManager.HasPermissionAsync(featureName).ConfigureAwait(true);

                if (hasPermission)
                {
                    menuItem.Visible = true;

                    // 获取许可过期时间并更新菜单文本
                    var (_, expireTime) = await HyAssistantLicenseManager.CheckPermissionWithExpireTimeAsync(featureName).ConfigureAwait(true);
                    if (expireTime.HasValue)
                    {
                        int daysLeft = (int)(expireTime.Value - DateTime.Now).TotalDays;

                        if (daysLeft <= 0)
                        {
                            menuItem.Visible = false;
                            ETLogManager.Debug($"HyAssistant功能 {featureName} 授权已过期，菜单已隐藏");
                        }
                        else if (daysLeft <= 30)
                        {
                            // 重置菜单文本（移除之前的天数提示）
                            string originalText = menuItem.Text;
                            if (originalText.Contains(" [还剩"))
                            {
                                originalText = originalText.Substring(0, originalText.IndexOf(" [还剩"));
                            }

                            menuItem.Text = $"{originalText} [还剩{daysLeft}天]";

                            if (daysLeft <= 7)
                            {
                                menuItem.ForeColor = Color.Red;
                            }
                            else if (daysLeft <= 15)
                            {
                                menuItem.ForeColor = Color.Orange;
                            }
                        }
                        else
                        {
                            // 重置菜单文本和颜色
                            string originalText = menuItem.Text;
                            if (originalText.Contains(" [还剩"))
                            {
                                menuItem.Text = originalText.Substring(0, originalText.IndexOf(" [还剩"));
                            }
                            menuItem.ForeColor = SystemColors.ControlText; // 恢复默认颜色
                        }
                    }
                }
                else
                {
                    menuItem.Visible = false;
                    // 移除授权相关日志输出，避免在textBoxLog中显示授权信息 ETLogManager.Debug($"HyAssistant功能
                    // {featureName} 无授权，菜单已隐藏");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"更新HyAssistant功能 {featureName} 菜单权限时出错: {ex.Message}", ex);
            }
        }

        #endregion 权限刷新方法
    }
}