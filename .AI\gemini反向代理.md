
配置应用信息
Application Name: 填写一个应用名称（必须是英文，且以小写字母开头）。
Image Name: 输入镜像地址 ghcr.io/wyeeeee/hajimi:latest
配置端口为7860并打开

=========================================

#基础部分
#设置一个你自己的访问密码
PASSWORD=AIzaSyBoDOtB3GrjZXmR0OauYwiBfF7u3SgdmY8

#配置时区
TZ=Asia/Shanghai

#ai studio部分

#将key1,key2,key3等替换为你真正拥有的gemini api key
GEMINI_API_KEYS=AIzaSyBoDOtB3GrjZXmR0OauYwiBfF7u3SgdmY8,AIzaSyAdnx2UY8TBnZ6atTkKraB76gfZUpk-QEg,AIzaSyB8ZHSmqfNiQdWThdv_W0NHzET6_jCbl4U,AIzaSyARrQKndgcluUFQ1ALbk0F5y_PirUEaXhE


#每分钟最大请求数
MAX_REQUESTS_PER_MINUTE=300

#每天每个 IP 最大请求数
MAX_REQUESTS_PER_DAY_PER_IP=6000

#是否启用假流式传输
FAKE_STREAMING=true

#单api 24小时最大使用次数
API_KEY_DAILY_LIMIT=1000

#空响应重试次数
MAX_EMPTY_RESPONSES=5

#是否启用伪装信息
RANDOM_STRING=false

#伪装信息长度
RANDOM_STRING_LENGTH=5

#默认的并发请求数
CONCURRENT_REQUESTS=1

#当请求失败时增加的并发请求数
INCREASE_CONCURRENT_ON_FAILURE=0

允许的最大并发请求数
MAX_CONCURRENT_REQUESTS=3

#是否启用联网模式(联网模式有严格的审核)
SEARCH_MODE=false

#联网模式提示词(用英文单引号包裹提示词)
SEARCH_PROMPT='（使用搜索工具联网搜索，需要在content中结合搜索内容）'

#vertex部分（如果您不需要vertex或不知道vertex是什么，无需配置这些内容）

#是否启用vertex
ENABLE_VERTEX=false

#vertex ai 凭证
GOOGLE_CREDENTIALS_JSON=''