﻿using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Drawing;
using System.Windows.Forms;
using Application = Microsoft.Office.Interop.Excel.Application;


namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// Excel工作表管理窗体，提供工作表显示/隐藏等管理功能
    /// </summary>
    public partial class frm工作表管理 : Form
    {
        #region 变量

        /// <summary>
        /// Excel应用程序实例
        /// </summary>
        public Application XlApp;

        /// <summary>
        /// 是否启用专业版功能（控制深度隐藏等高级功能的访问权限）
        /// </summary>
        readonly bool _proFunctionEnabled = true; // ThisAddIn.AuthorizationManager.IsAuthorized("hhy");

        /// <summary>
        /// 标识是否正在重新加载工作表列表
        /// </summary>
        bool _isReloading;

        #endregion 变量

        /// <summary>
        /// 处理工作表项选中/取消选中事件
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">包含选中项信息的事件参数</param>
        void listView工作表_ItemChecked(object sender, ItemCheckedEventArgs e)
        {
            if (_isReloading) return;
            SetSheetVisible(e.Item, e.Item.Checked ? XlSheetVisibility.xlSheetVisible : XlSheetVisibility.xlSheetHidden);
        }

        /// <summary>
        /// 设置指定工作表的可见性
        /// </summary>
        /// <param name="listItem">要设置的工作表对应的列表项</param>
        /// <param name="visibility">目标可见性状态</param>
        /// <returns>操作是否成功</returns>
        bool SetSheetVisible(ListViewItem listItem, XlSheetVisibility visibility)
        {
            try
            {
                string sheetName = listItem.SubItems[0].Text;
                Worksheet targetSheet = XlApp.ActiveWorkbook.GetWorksheetByName(sheetName);
                if (targetSheet == null)
                {
                    ETLogManager.Error("未找到工作表", new Exception($"工作表不存在：{sheetName}"));
                    return true;
                }

                targetSheet.Visible = visibility;
                listItem.ForeColor = visibility == XlSheetVisibility.xlSheetVeryHidden ? Color.DarkGray : Color.Black;
                ETLogManager.Info($"已设置工作表 {sheetName} 的可见性为: {visibility}");
                return true;
            }
            catch (Exception ex)
            {
                listItem.Checked = !listItem.Checked;
                listItem.ForeColor = visibility == XlSheetVisibility.xlSheetVeryHidden ? Color.DarkGray : Color.Black;
                throw new ETException("设置工作表可见性失败", "工作表操作", ex);
            }
        }

        /// <summary>
        /// 更新工作表列表显示
        /// </summary>
        void UpdateDisplay()
        {
            try
            {
                if (XlApp.Workbooks.Count == 0) return;

                _isReloading = true;
                listView工作表.Items.Clear();
                foreach (Worksheet worksheet in XlApp.ActiveWorkbook.Worksheets)
                {
                    ListViewItem sheetItem = new(worksheet.Name) { UseItemStyleForSubItems = false };

                    switch (worksheet.Visible)
                    {
                        case XlSheetVisibility.xlSheetVisible:
                            sheetItem.SubItems.Add(worksheet.Name);
                            sheetItem.Checked = true;
                            break;

                        case XlSheetVisibility.xlSheetHidden:
                            sheetItem.SubItems.Add(worksheet.Name);
                            sheetItem.Checked = false;
                            break;

                        case XlSheetVisibility.xlSheetVeryHidden:
                            sheetItem.SubItems.Add(worksheet.Name);
                            sheetItem.Checked = false;
                            break;
                    }

                    if (worksheet.Visible != XlSheetVisibility.xlSheetVeryHidden)
                    {
                        listView工作表.Items.Add(sheetItem);
                    }
                    else if (_proFunctionEnabled)
                    {
                        listView工作表.Items.Add(sheetItem);
                        sheetItem.ForeColor = Color.DarkGray;
                    }
                }

                ETLogManager.Info("工作表列表更新完成");
            }
            catch (Exception ex)
            {
                throw new ETException("更新工作表列表失败", "工作表操作", ex);
            }
            finally
            {
                _isReloading = false;
            }
        }

        /// <summary>
        /// 处理深度隐藏菜单项点击事件
        /// </summary>
        void 深度隐藏ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                foreach (ListViewItem selectedItem in listView工作表.SelectedItems)
                {
                    selectedItem.Checked = false;
                    selectedItem.ForeColor = Color.DarkGray;
                    if (SetSheetVisible(selectedItem, XlSheetVisibility.xlSheetVeryHidden)) continue;
                    selectedItem.ForeColor = Color.Black;
                    selectedItem.Checked = true;
                }
            }
            catch (Exception ex)
            {
                throw new ETException("深度隐藏工作表失败", "工作表操作", ex);
            }
        }

        #region 初始化

        /// <summary>
        /// 初始化工作表管理窗体
        /// </summary>
        public frm工作表管理()
        {
            InitializeComponent();
            XlApp = Globals.ThisAddIn.Application;
        }

        /// <summary>
        /// 窗体加载事件处理
        /// </summary>
        void frm工作表管理_Load(object sender, EventArgs e)
        {
            UpdateDisplay();
            listView工作表.ContextMenuStrip = _proFunctionEnabled ? contextMenuStrip1 : null;
        }

        /// <summary>
        /// 刷新按钮点击事件处理
        /// </summary>
        void 刷新ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            UpdateDisplay();
        }

        #endregion 初始化
    }
}