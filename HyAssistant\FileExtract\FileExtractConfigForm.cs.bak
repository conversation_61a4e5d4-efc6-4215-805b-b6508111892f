/**
 * 文件名：FileExtractConfigForm.cs
 * 窗体名称：文件提取配置窗体
 * 
 * 功能说明：
 * 该窗体用于管理文件提取任务的配置信息。用户可以创建、编辑、删除不同的文件提取任务，
 * 每个任务可以设置源路径、目标路径、文件匹配模式、提取时间间隔以及文件时间限制等条件。
 * 
 * 实现细节：
 * 1. 配置存储：使用INI文件（fileExtract.ini）存储所有配置信息
 * 2. 配置格式：每个提取任务使用唯一UUID标识，格式为"FileExtract-{UUID}"
 * 3. 主要功能：
 *    - 加载现有配置：读取INI文件中的所有任务配置并显示在列表中
 *    - 添加新配置：创建新的任务配置并分配唯一UUID
 *    - 删除配置：从INI文件中移除指定的任务配置
 *    - 保存配置：将修改后的任务配置保存到INI文件
 *    - 配置项设置：包括任务名称、源路径（支持多路径）、目标路径、文件模式（支持多模式）、
 *      执行间隔、文件创建时间和修改时间限制、启用状态等
 * 4. 兼容性处理：支持旧格式配置的自动升级，将非UUID格式的配置更新为新格式
 * 5. 数据结构：
 *    - _sectionUuids：字典，存储显示名到UUID的映射
 *    - _sectionControls：字典，存储UUID到对应控件的映射
 * 
 * 用户交互流程：
 * 1. 用户打开窗体，系统自动加载所有已有配置并显示在左侧列表中
 * 2. 用户可以选择已有配置进行查看或编辑，也可以添加新配置或删除现有配置
 * 3. 当选择配置后，右侧表单显示该配置的详细信息供编辑
 * 4. 编辑完成后，用户点击保存按钮将更改写入INI文件
 */

using ET;
using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;

namespace HyAssistant
{
    public partial class FileExtractConfigForm : Form
    {
        readonly string _iniFilePath;
        readonly ETIniFile _iniFile;
        readonly Dictionary<string, string> _sectionUuids = new Dictionary<string, string>(); // 存储显示名到UUID的映射
        readonly Dictionary<string, Control> _sectionControls = new Dictionary<string, Control>();
        const string SECTION_PREFIX = "FileExtract-"; // 配置组名前缀常量
        bool _isUpdatingItem = false; // 新增：标记是否正在更新列表项，防止触发不必要的重新加载

        public FileExtractConfigForm()
        {
            InitializeComponent();
            _iniFilePath = ETConfig.GetConfigDirectory("fileExtract.ini");

            // 如果配置文件不存在，创建新的INI文件对象
            if (!File.Exists(_iniFilePath))
            {
                _iniFile = new ETIniFile(_iniFilePath);
            }
            else
            {
                _iniFile = new ETIniFile(_iniFilePath);
            }

            LoadSections();
        }

        void LoadSections()
        {
            try
            {
                listBoxSections.Items.Clear();
                _sectionUuids.Clear();
                _sectionControls.Clear();

                // 如果配置文件不存在，直接返回
                if (!File.Exists(_iniFilePath))
                {
                    return;
                }

                // 获取所有节名
                List<string> sectionNames = _iniFile.GetSectionNames();

                // 检查是否有旧格式的配置需要更新为UUID格式
                bool needUpdate = false;
                foreach (string sectionName in sectionNames)
                {
                    if (!sectionName.StartsWith(SECTION_PREFIX, StringComparison.OrdinalIgnoreCase)
                        && !string.IsNullOrEmpty(_iniFile.GetValue(sectionName, "name")))
                    {
                        try
                        {
                            // 这是旧格式的配置节，需要更新
                            string newSectionName = $"{SECTION_PREFIX}{Guid.NewGuid().ToString("N")}";

                            // 复制所有键值对
                            Dictionary<string, string> keyValues = _iniFile.GetSection(sectionName);
                            foreach (KeyValuePair<string, string> kv in keyValues)
                            {
                                _iniFile.SetValue(newSectionName, kv.Key, kv.Value);
                            }

                            // 删除旧节
                            _iniFile.DeleteSection(sectionName);
                            needUpdate = true;
                        }
                        catch (Exception ex)
                        {
                            // 记录错误但继续处理其他节
                            MessageBox.Show($"更新配置格式时出错：{ex.Message}", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                }

                // 如果有更新，保存文件
                if (needUpdate)
                {
                    _iniFile.IniWriteFile();
                    sectionNames = _iniFile.GetSectionNames(); // 重新获取节名
                }

                // 用于检测重复的显示名
                HashSet<string> usedDisplayNames = new HashSet<string>();

                foreach (string sectionName in sectionNames)
                {
                    if (sectionName.StartsWith(SECTION_PREFIX, StringComparison.OrdinalIgnoreCase))
                    {
                        string displayName = _iniFile.GetValue(sectionName, "name", sectionName);

                        // 处理空白显示名
                        if (string.IsNullOrWhiteSpace(displayName))
                        {
                            displayName = sectionName;
                        }

                        // 处理重复的显示名
                        string originalDisplayName = displayName;
                        int counter = 1;
                        while (usedDisplayNames.Contains(displayName))
                        {
                            displayName = $"{originalDisplayName} ({counter})";
                            counter++;
                        }

                        usedDisplayNames.Add(displayName);
                        _sectionUuids[displayName] = sectionName;
                        listBoxSections.Items.Add(displayName);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载配置列表时出错：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        void listBoxSections_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 如果正在更新列表项，不执行加载
            if (_isUpdatingItem) return;

            try
            {
                if (listBoxSections.SelectedItem == null) return;

                string selectedDisplayName = listBoxSections.SelectedItem.ToString();

                // 检查选择的项是否有效
                if (string.IsNullOrEmpty(selectedDisplayName))
                {
                    return;
                }

                // 安全地从字典获取UUID，避免KeyNotFoundException
                if (!_sectionUuids.TryGetValue(selectedDisplayName, out string selectedUuid))
                {
                    MessageBox.Show($"找不到配置 '{selectedDisplayName}' 的对应数据，可能配置已损坏", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                LoadSectionData(selectedUuid);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择配置时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                ClearForm(); // 清空表单以避免显示不一致的数据
            }
        }

        void LoadSectionData(string sectionName)
        {
            try
            {
                textBoxName.Text = _iniFile.GetValue(sectionName, "name", string.Empty);
                textBoxSourcePaths.Text = _iniFile.GetValue(sectionName, "SourcePaths", string.Empty).Replace("|", Environment.NewLine);
                textBoxTargetPath.Text = _iniFile.GetValue(sectionName, "TargetPath", string.Empty);
                textBoxFilePattern.Text = _iniFile.GetValue(sectionName, "FilePattern", "*.*").Replace("|", Environment.NewLine);

                // 添加异常处理
                if (int.TryParse(_iniFile.GetValue(sectionName, "IntervalInMinutes", "60"), out int interval))
                {
                    numericUpDownInterval.Value = Math.Min(Math.Max(interval, numericUpDownInterval.Minimum), numericUpDownInterval.Maximum);
                }
                else
                {
                    numericUpDownInterval.Value = 60;
                }

                // 安全解析日期，使用TryParse
                if (DateTime.TryParse(_iniFile.GetValue(sectionName, "CreationTimeLimit", DateTime.MinValue.ToString()), out DateTime creationTime))
                {
                    dateTimePickerCreationTime.Value = creationTime;
                }
                else
                {
                    dateTimePickerCreationTime.Value = DateTime.Now;
                }

                if (DateTime.TryParse(_iniFile.GetValue(sectionName, "ModificationTimeLimit", DateTime.MinValue.ToString()), out DateTime modificationTime))
                {
                    dateTimePickerModificationTime.Value = modificationTime;
                }
                else
                {
                    dateTimePickerModificationTime.Value = DateTime.Now;
                }

                checkBoxEnable.Checked = _iniFile.GetValue(sectionName, "enable", "0") == "1";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载配置数据时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                ClearForm();
            }
        }

        void buttonSave_Click(object sender, EventArgs e)
        {
            if (listBoxSections.SelectedItem == null) return;

            try
            {
                // 验证必填字段
                if (string.IsNullOrWhiteSpace(textBoxName.Text))
                {
                    MessageBox.Show("请输入配置名称", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    textBoxName.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(textBoxSourcePaths.Text))
                {
                    MessageBox.Show("请至少输入一个源路径", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    textBoxSourcePaths.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(textBoxTargetPath.Text))
                {
                    MessageBox.Show("请输入目标路径", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    textBoxTargetPath.Focus();
                    return;
                }

                string selectedDisplayName = listBoxSections.SelectedItem.ToString();

                // 检查选择的项是否有效
                if (string.IsNullOrEmpty(selectedDisplayName))
                {
                    MessageBox.Show("无效的配置名称", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 安全地从字典获取UUID
                if (!_sectionUuids.TryGetValue(selectedDisplayName, out string selectedUuid))
                {
                    MessageBox.Show($"找不到配置 '{selectedDisplayName}' 的对应数据，可能配置已损坏", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                string newDisplayName = textBoxName.Text.Trim();

                // 检查名称是否重复
                if (newDisplayName != selectedDisplayName && _sectionUuids.ContainsKey(newDisplayName))
                {
                    MessageBox.Show("配置名称已存在，请使用其他名称", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 先保存数据
                SaveSectionData(selectedUuid);

                // 更新显示名称，设置标志防止触发不必要的加载
                if (newDisplayName != selectedDisplayName)
                {
                    try
                    {
                        _isUpdatingItem = true;
                        _sectionUuids.Remove(selectedDisplayName);
                        _sectionUuids[newDisplayName] = selectedUuid;
                        int selectedIndex = listBoxSections.SelectedIndex;
                        listBoxSections.Items[selectedIndex] = newDisplayName;
                    }
                    finally
                    {
                        _isUpdatingItem = false;
                    }
                }

                // 确保配置文件目录存在
                string configDir = Path.GetDirectoryName(_iniFilePath);
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }

                _iniFile.IniWriteFile();
                MessageBox.Show("配置已保存", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存配置时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        void SaveSectionData(string sectionName)
        {
            try
            {
                _iniFile.SetValue(sectionName, "name", textBoxName.Text.Trim());
                _iniFile.SetValue(sectionName, "SourcePaths", textBoxSourcePaths.Text.Replace(Environment.NewLine, "|").TrimEnd('|'));
                _iniFile.SetValue(sectionName, "TargetPath", textBoxTargetPath.Text.Trim());
                _iniFile.SetValue(sectionName, "FilePattern", textBoxFilePattern.Text.Replace(Environment.NewLine, "|").TrimEnd('|'));
                _iniFile.SetValue(sectionName, "IntervalInMinutes", numericUpDownInterval.Value.ToString());

                // 使用特定格式保存日期，以确保跨区域一致性
                _iniFile.SetValue(sectionName, "CreationTimeLimit", dateTimePickerCreationTime.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                _iniFile.SetValue(sectionName, "ModificationTimeLimit", dateTimePickerModificationTime.Value.ToString("yyyy-MM-dd HH:mm:ss"));

                _iniFile.SetValue(sectionName, "enable", checkBoxEnable.Checked ? "1" : "0");
            }
            catch (Exception ex)
            {
                throw new Exception($"保存配置数据失败: {ex.Message}", ex);
            }
        }

        void buttonAdd_Click(object sender, EventArgs e)
        {
            try
            {
                string newDisplayName = $"新配置{_sectionUuids.Count + 1}";

                // 检查名称是否重复
                while (_sectionUuids.ContainsKey(newDisplayName))
                {
                    newDisplayName = $"新配置{_sectionUuids.Count + 1}";
                }

                string newUuid = $"{SECTION_PREFIX}{Guid.NewGuid().ToString("N")}";
                _sectionUuids[newDisplayName] = newUuid;

                // 保存配置数据
                _iniFile.SetValue(newUuid, "name", newDisplayName);
                _iniFile.SetValue(newUuid, "enable", "1");
                _iniFile.SetValue(newUuid, "IntervalInMinutes", "60");

                // 使用统一的日期格式
                _iniFile.SetValue(newUuid, "CreationTimeLimit", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                _iniFile.SetValue(newUuid, "ModificationTimeLimit", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

                _iniFile.SetValue(newUuid, "FilePattern", "*.*");

                // 确保配置文件目录存在
                string configDir = Path.GetDirectoryName(_iniFilePath);
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }

                _iniFile.IniWriteFile();

                // 添加到列表并选中，使用_isUpdatingItem标志防止触发不必要的重新加载
                try
                {
                    _isUpdatingItem = true;
                    listBoxSections.Items.Add(newDisplayName);
                    listBoxSections.SelectedItem = newDisplayName;
                }
                finally
                {
                    _isUpdatingItem = false;
                }

                // 手动加载配置数据
                LoadSectionData(newUuid);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加新配置时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        void buttonDelete_Click(object sender, EventArgs e)
        {
            if (listBoxSections.SelectedItem == null) return;

            string selectedDisplayName = listBoxSections.SelectedItem.ToString();

            // 检查选择的项是否有效
            if (string.IsNullOrEmpty(selectedDisplayName))
            {
                return;
            }

            // 安全地从字典获取UUID
            if (!_sectionUuids.TryGetValue(selectedDisplayName, out string selectedUuid))
            {
                MessageBox.Show($"找不到配置 '{selectedDisplayName}' 的对应数据，可能配置已损坏", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (MessageBox.Show($"确认要删除 \"{selectedDisplayName}\" 配置吗？", "确认删除",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
            {
                return;
            }

            try
            {
                _iniFile.DeleteSection(selectedUuid);
                _sectionUuids.Remove(selectedDisplayName);
                int selectedIndex = listBoxSections.SelectedIndex;

                try
                {
                    _isUpdatingItem = true;
                    listBoxSections.Items.Remove(selectedDisplayName);

                    // 如果删除后还有项目，选择下一个项目
                    if (listBoxSections.Items.Count > 0)
                    {
                        int newIndex = Math.Min(selectedIndex, listBoxSections.Items.Count - 1);
                        listBoxSections.SelectedIndex = newIndex;
                    }
                    else
                    {
                        ClearForm();
                    }
                }
                finally
                {
                    _isUpdatingItem = false;
                }

                // 如果选择了新项目，手动加载数据
                if (listBoxSections.SelectedItem != null)
                {
                    string newSelectedName = listBoxSections.SelectedItem.ToString();
                    if (_sectionUuids.TryGetValue(newSelectedName, out string newSelectedUuid))
                    {
                        LoadSectionData(newSelectedUuid);
                    }
                }

                _iniFile.IniWriteFile();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除配置时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        void ClearForm()
        {
            textBoxName.Clear();
            textBoxSourcePaths.Clear();
            textBoxTargetPath.Clear();
            textBoxFilePattern.Clear();
            numericUpDownInterval.Value = 60;
            dateTimePickerCreationTime.Value = DateTime.Now;
            dateTimePickerModificationTime.Value = DateTime.Now;
            checkBoxEnable.Checked = false;
        }

        void buttonBrowseSource_Click(object sender, EventArgs e)
        {
            using (FolderBrowserDialog dialog = new FolderBrowserDialog())
            {
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    if (string.IsNullOrEmpty(textBoxSourcePaths.Text))
                    {
                        textBoxSourcePaths.Text = dialog.SelectedPath;
                    }
                    else
                    {
                        textBoxSourcePaths.Text += $"{Environment.NewLine}{dialog.SelectedPath}";
                    }
                }
            }
        }

        void buttonBrowseTarget_Click(object sender, EventArgs e)
        {
            using (FolderBrowserDialog dialog = new FolderBrowserDialog())
            {
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    textBoxTargetPath.Text = dialog.SelectedPath;
                }
            }
        }
    }
}