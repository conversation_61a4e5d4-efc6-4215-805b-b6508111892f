using ET;
using Microsoft.Office.Interop.Excel;
using Microsoft.Office.Interop.Visio;
using System;
using System.Collections.Generic;
using System.Linq;
using Page = Microsoft.Office.Interop.Visio.Page;
using TextBox = System.Windows.Forms.TextBox;

namespace HyExcelVsto.Module.Common
{
    public static partial class VisioHelperFunctions
    {
        #region 读取图衔
        /// <summary>
        /// 执行仅读取图衔操作
        /// </summary>
        /// <param name="filePathRange">文件路径范围</param>
        /// <param name="textBoxProgress">进度显示文本框</param>
        public static void 执行_仅读取图衔(Range filePathRange, string selectedTuxianName, TextBox textBoxProgress)
        {
            if (filePathRange == null)
            {
                textBoxProgress.WriteLog("请选择页面名称所在的列");
                return;
            }

            filePathRange = filePathRange.OptimizeRangeSize();

            Worksheet newWorksheet = Globals.ThisAddIn.Application.ActiveWorkbook.Worksheets.Add();
            newWorksheet.Activate();

            newWorksheet.Name = $"导出页面信息{DateTime.Now:yyyyMMddHHmm}";

            int newCol = 1;
            newWorksheet.Cells[1, newCol++].Value = "全路径";
            newWorksheet.Cells[1, newCol++].Value = "页面名称";
            newWorksheet.Cells[1, newCol++].Value = "文件名";
            newWorksheet.Cells[1, newCol++].Value = "页面名称";
            newWorksheet.Cells[1, newCol++].Value = "图纸名称";
            newWorksheet.Cells[1, newCol++].Value = "单位比例";
            newWorksheet.Cells[1, newCol++].Value = "出图日期";
            newWorksheet.Cells[1, newCol++].Value = "图纸编号";
            newWorksheet.Cells[1, newCol++].Value = "页面比例尺";

            newCol = 1;
            newWorksheet.Columns[newCol++].ColumnWidth = 20;
            newWorksheet.Columns[newCol++].ColumnWidth = 20;
            newWorksheet.Columns[newCol++].ColumnWidth = 40;
            newWorksheet.Columns[newCol++].ColumnWidth = 20;
            newWorksheet.Columns[newCol++].ColumnWidth = 40;
            newWorksheet.Columns[newCol++].ColumnWidth = 15;
            newWorksheet.Columns[newCol++].ColumnWidth = 15;
            newWorksheet.Columns[newCol++].ColumnWidth = 35;
            newWorksheet.Columns[newCol++].ColumnWidth = 15;

            ETExcelExtensions.ScreenUpdate();

            int newRow = 2;

            Microsoft.Office.Interop.Visio.Application visioApp = ETVisio.CreateApplication(true);
            Document docPrev = null;

            int rowCount = filePathRange.GetVisibleRowCount();
            int currentRow = 1;

            // 获取选定的图衔名称
            Dictionary<string, RectanglPosition> selectedPositions;

            selectedPositions = selectedTuxianName switch
            {
                "上海院" => VisShapePosition.SH相对位置,
                "南方院" => VisShapePosition.NF相对位置,
                _ => null
            };

            if (selectedPositions == null)
            {
                textBoxProgress.WriteLog("请选择正确的图衔名称：上海院或南方院");
                return;
            }

            foreach (Range filePathCell in filePathRange.Cells)
            {
                if (filePathCell.EntireRow.Hidden)
                    continue;
                if (filePathCell.IsCellEmpty())
                    continue;

                string filePath = filePathCell.Value.ToString();
                string fileName = System.IO.Path.GetFileName(filePath);

                Document doc;

                doc = ETVisio.FindOpenDocumentByPath(visioApp, filePath);
                if (doc == null)
                {
                    doc = ETVisio.Open(visioApp, filePath, textBoxProgress, false);
                    docPrev?.Close(false);
                    docPrev = doc;
                }
                else
                {
                    // 文件已经打开，更新docPrev引用
                    docPrev = doc;
                }

                if (doc == null)
                {
                    textBoxProgress.WriteLog($"文件{System.IO.Path.GetFileName(filePath)}不存在(或者打不开)");
                    newCol = 1;
                    newWorksheet.Cells[newRow, newCol++].Value = filePath;
                    newWorksheet.Cells[newRow, newCol++].Value = fileName;
                    newWorksheet.Cells[newRow, newCol++].Value = "文件无法读取";
                    newRow++;
                    continue;
                }

                textBoxProgress.WriteLog($"{currentRow++}/{rowCount}.正在处理 {fileName}");

                int pageCount = doc.Pages.Count;

                for (int i = 1; i <= pageCount; i++)
                {
                    newCol = 1;
                    Page page = (Page)doc.Pages[i];

                    //仅读取前景页面
                    if (ETVisio.IsBackgroundPage(page))
                        continue;

                    newWorksheet.Cells[newRow, newCol++].Value = doc.FullName;
                    newWorksheet.Cells[newRow, newCol++].Value = page.Name;
                    newWorksheet.Cells[newRow, newCol++].Value = doc.Name;
                    newWorksheet.Cells[newRow, newCol++].Value = page.Name;

                    RectangleRange rect图名位置 = ETVisio.Get图衔子图形在页面中的位置_BL(
                        page,
                        selectedPositions["图名"],
                        0.01,
                        0.03,
                        0.006);
                    RectangleRange rect单位比例位置 = ETVisio.Get图衔子图形在页面中的位置_BL(
                        page,
                        selectedPositions["单位比例"],
                        0.01,
                        0.03,
                        0.004,
                        0.006);
                    RectangleRange rect日期位置 = ETVisio.Get图衔子图形在页面中的位置_BL(page, selectedPositions["日期"]);
                    RectangleRange rect图号位置 = ETVisio.Get图衔子图形在页面中的位置_BL(page, selectedPositions["图号"]);

                    Dictionary<string, RectangleRange> regions_BL_Dic = [];

                    if (rect图名位置 != null)
                        regions_BL_Dic.Add("图纸名称", rect图名位置);
                    if (rect单位比例位置 != null)
                        regions_BL_Dic.Add("单位比例", rect单位比例位置);
                    if (rect日期位置 != null)
                        regions_BL_Dic.Add("出图日期", rect日期位置);
                    if (rect图号位置 != null)
                        regions_BL_Dic.Add("图纸编号", rect图号位置);

                    Dictionary<string, List<TuxianSubShapeInfo>> txShapes_Dic = ETVisio.ReadTextFromShapes(
                        page,
                        regions_BL_Dic);

                    ETVisio.ShapeTextToExcel(txShapes_Dic, "图纸名称", newWorksheet.Cells[newRow, newCol++]);
                    ETVisio.ShapeTextToExcel(txShapes_Dic, "单位比例", newWorksheet.Cells[newRow, newCol++]);
                    ETVisio.ShapeTextToExcel(txShapes_Dic, "出图日期", newWorksheet.Cells[newRow, newCol++]);
                    ETVisio.ShapeTextToExcel(txShapes_Dic, "图纸编号", newWorksheet.Cells[newRow, newCol++]);
                    newWorksheet.Cells[newRow, newCol++].Value = ETVisio.GetPaperDrawingScale(page);

                    newRow++;
                }

                textBoxProgress.WriteLog($"，读取完成", false);
                ETExcelExtensions.Format条件格式警示色(filePathCell, EnumWarningColor.蓝绿);
            }

            docPrev?.Close(false);
            visioApp.Quit();
            textBoxProgress.WriteLog("执行完成");
        }
        #endregion 读取图衔

        #region 转换为PDF
        public static void VisioToPdfPerRowsThread(
            string targetDirectory,
            string sourceRootDirectory,
            Range filePathRange,
            TextBox textBoxProgress,
            TextBox textBoxError)
        {
            new System.Threading.Thread(
                () => VisioToPDFPerRows(
                    targetDirectory,
                    sourceRootDirectory,
                    filePathRange,
                    textBoxProgress,
                    textBoxError)).Start();
        }

        public static string VisioToPDFPerRows(
            string targetDirectory,
            string sourceRootDirectory,
            Range filePathRange,
            TextBox textBoxProgress,
            TextBox textBoxError)
        {
            //if (string.IsNullOrEmpty(targetDirectory) || !Directory.Exists(targetDirectory))
            //{
            //    if (textBoxError != null)
            //        textBoxError.WriteLog("请选择要输出的新Visio文件目录");
            //    return "请选择要输出的新Visio文件目录";
            //}

            //Microsoft.Office.Interop.Visio.Application visioApp = ETVisio.CreateVisioApplication2013();
            //if (!ETVisio.IsVisio2013OrLater(visioApp))
            //{
            //    visioApp.Quit();
            //    if (textBoxError != null)
            //        textBoxError.WriteLog("本功功能只支持Visio2013及以上版本");
            //    return "本功功能只支持Visio2013及以上版本";
            //}

            //filePathRange = filePathRange.HyOptimizeRangeSize().HyGetVisibleRange();

            //foreach (Range filePathCell in filePathRange.Cells)
            //{
            //    if (filePathCell.IsCellEmpty())
            //        continue;
            //    string visioPath = filePathCell.Value.ToString();

            //    if (!visioPath.EndsWith(".vsd"))
            //        continue;

            //    string resolvedSourceRootDirectory = string.IsNullOrEmpty(sourceRootDirectory)
            //        ? System.IO.Path.GetDirectoryName(visioPath)
            //        : sourceRootDirectory;

            //    bool success = ETVisio.VisioToPdfToAnotherDirectory(
            //        visioApp,
            //        visioPath,
            //        resolvedSourceRootDirectory,
            //        targetDirectory);
            //    if (!success)
            //    {
            //        filePathCell.Format条件格式警示色(EnumWarningColor.出错);
            //    }
            //    if (textBoxProgress != null)
            //        textBoxProgress.WriteLog(success ? $"转换成功:{visioPath}" : $"转换失败:{visioPath}");
            //    if (textBoxError != null && !success)
            //        textBoxError.WriteLog(success ? string.Empty : $"转换失败:{visioPath}");
            //    Debug.WriteLine(success ? $"转换成功:{visioPath}" : $"转换失败:{visioPath}");
            //}

            //visioApp.Quit();
            //if (textBoxProgress != null)
            //    textBoxProgress.WriteLog("转换完成");

            return string.Empty;
        }
        #endregion 转换为PDF

        #region 通用函数
        public static void Set文本格式(Range range, int rowIndex, int colIndex, string value)
        {
            if (string.IsNullOrEmpty(value) || range == null)
                return;
            if (range.Columns.Count < colIndex || range.Rows.Count < rowIndex)
                return;
            range.Cells[rowIndex, colIndex].NumberFormatLocal = "@";
        }


        /// <summary>
        /// 根据输入的文本行数返回最后几行。
        /// </summary>
        /// <param name="inputText">输入的多行文本字符串。</param>
        /// <returns>返回指定数量的文本行，依据输入文本行数而定。</returns>
        public static string GetLastLines(string inputText)
        {
            string[] lines = inputText.Split(new[] { '\n' }, StringSplitOptions.None);

            int lineCount = lines.Length;

            int numberOfLinesToReturn = lineCount > 8 ? 5 : 3;

            string[] resultLines = lines.Skip(Math.Max(0, lineCount - numberOfLinesToReturn)).ToArray();

            return string.Join(Environment.NewLine, resultLines);
        }
        #endregion 通用函数
    }
}