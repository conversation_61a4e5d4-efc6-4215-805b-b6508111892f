﻿using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using TextBox = System.Windows.Forms.TextBox;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// 文件操作窗体类，提供文件搜索、复制、重命名等功能
    /// </summary>
    /// <remarks>
    /// 主要功能包括：
    /// - 文件搜索和匹配
    /// - 文件复制和移动
    /// - 文件重命名
    /// - 文件时间修改
    /// - 文件信息导入Excel
    /// </remarks>
    public partial class frm文件操作 : Form
    {
        #region 查找文件

        private Dictionary<string, string> _filesDictionary = [];
        private BackgroundWorker _searchWorker;

        /// <summary>
        /// 在文件系统中搜索并匹配文件
        /// </summary>
        /// <param name="path">要搜索的目录路径</param>
        /// <remarks>
        /// 此方法会根据用户设置的条件（是否包含子目录、是否搜索文件夹等）来搜索文件， 并将结果存储在 _filesDictionary 中
        ///
        /// 性能优化：
        /// 1. 使用 HashSet 存储文件路径，避免重复
        /// 2. 使用 Directory.EnumerateFileSystemEntries 替代 GetFiles/GetDirectories，减少内存使用
        /// 3. 添加文件数量限制，防止内存溢出
        /// </remarks>
        public void GetFileNames(string path)
        {
            if (string.IsNullOrWhiteSpace(txtPath检查文件.Text) || !Directory.Exists(path))
                return;

            try
            {
                const int MAX_FILES = 100000; // 最大文件数量限制
                if (_filesDictionary.Count >= MAX_FILES)
                {
                    MessageBox.Show($"文件数量超过限制（{MAX_FILES}个），将停止搜索。");
                    return;
                }

                DirectoryInfo rootDirectory = new(path);

                // 使用 Directory.EnumerateFileSystemEntries 优化性能
                foreach (string entry in Directory.EnumerateFileSystemEntries(path))
                {
                    if (_filesDictionary.Count >= MAX_FILES)
                        break;

                    if (Directory.Exists(entry))
                    {
                        if (checkBox检查文件_文件夹.Checked)
                        {
                            DirectoryInfo dirInfo = new(entry);
                            _filesDictionary.Add(dirInfo.FullName, dirInfo.Name);
                        }
                    }
                    else if (checkBox检查文件_文件.Checked)
                    {
                        FileInfo fileInfo = new(entry);
                        _filesDictionary.Add(fileInfo.FullName, fileInfo.Name);
                    }
                }

                // 递归处理子目录
                if (checkBox含子目录.Checked)
                {
                    foreach (string dir in Directory.EnumerateDirectories(path))
                    {
                        if (_filesDictionary.Count >= MAX_FILES)
                            break;
                        GetFileNames(dir);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new ETException($"获取文件名列表时出错: {ex.Message}", "文件搜索操作", ex);
            }
        }

        /// <summary>
        /// 清除单元格的内容和注释
        /// </summary>
        /// <param name="cell">要清除的单元格</param>
        private static void ClearCell(Range cell)
        {
            try
            {
                cell.Value = string.Empty;
                cell.DelComment();
            }
            catch (Exception ex)
            {
                throw new ETException($"清除单元格内容时出错: {ex.Message}", ExcelOperationType.RangeOperation, ex);
            }
        }

        /// <summary>
        /// 获取文件所在文件夹的名称
        /// </summary>
        /// <param name="filePath">文件的完整路径</param>
        /// <returns>文件所在文件夹的名称</returns>
        private static string GetFolderName(string filePath)
        {
            try
            {
                string folderPath = Path.GetDirectoryName(filePath);
                return Path.GetFileName(folderPath);
            }
            catch (Exception ex)
            {
                throw new ETException($"获取文件夹名称时出错: {ex.Message}", "文件路径操作", ex);
            }
        }

        /// <summary>
        /// 获取文件的最后修改时间
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>格式化的时间字符串，如果文件不存在则返回空字符串</returns>
        public string GetFileModificationTime(string filePath)
        {
            try
            {
                return File.Exists(filePath) ? File.GetLastWriteTime(filePath).ToString("yyyy-MM-dd HH:mm") : string.Empty;
            }
            catch (Exception ex)
            {
                throw new ETException($"获取文件修改时间时出错: {ex.Message}", "文件时间操作", ex);
            }
        }

        /// <summary>
        /// 获取文件的创建时间
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>格式化的时间字符串，如果文件不存在则返回空字符串</returns>
        public string GetFileCreationTime(string filePath)
        {
            try
            {
                return File.Exists(filePath) ? File.GetCreationTime(filePath).ToString("yyyy-MM-dd HH:mm") : string.Empty;
            }
            catch (Exception ex)
            {
                throw new ETException($"获取文件创建时间时出错: {ex.Message}", "文件时间操作", ex);
            }
        }

        /// <summary>
        /// 搜索文件按钮点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void button搜索_Click(object sender, EventArgs e)
        {
            try
            {
                // 如果已经在搜索中，则取消
                if (_searchWorker.IsBusy)
                {
                    _searchWorker.CancelAsync();
                    return;
                }

                // 初始化文件字典，支持多目录搜索
                _filesDictionary ??= [];
                _filesDictionary.Clear();

                if (string.IsNullOrWhiteSpace(txtPath检查文件.Text))
                    return;

                string[] searchPaths = txtPath检查文件.Text
                    .Split(new[] { Environment.NewLine }, StringSplitOptions.RemoveEmptyEntries);
                foreach (string path in searchPaths)
                    GetFileNames(path);

                if (_filesDictionary.Count == 0)
                    return;

                // 启动异步搜索
                StartSearchAsync();
            }
            catch (ETException ex)
            {
                MessageBox.Show($"搜索文件时出错: {ex.Message}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"发生未预期的错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 启动异步搜索处理
        /// </summary>
        private void StartSearchAsync()
        {
            try
            {
                // 获取输入输出单元格信息
                Range inputRange = ucExcelRangeSelect来源.SelectedRange.Columns[1]; // 来源Range
                inputRange = inputRange?.OptimizeRangeSize().GetVisibleRange();
                Range outputRange_filename = ucExcelRangeSelect结果.SelectedRange; // 写入文件名Range

                if (inputRange == null || outputRange_filename == null)
                    return;

                // 获取其他输出范围
                (Range Filename, Range Fullpath, Range Directory, Range LastWriteTime) outputRanges = GetOutputRanges(outputRange_filename);

                // 设置Excel为快速模式
                ETExcelExtensions.SetAppFastMode();

                // 启动后台搜索
                SearchParams parameters = new()
                {
                    InputRange = inputRange,
                    OutputRanges = outputRanges
                };

                _searchWorker.RunWorkerAsync(parameters);
            }
            catch (Exception ex)
            {
                ETExcelExtensions.SetAppNormalMode(true);
                throw new ETException($"启动搜索时出错: {ex.Message}", ExcelOperationType.RangeOperation, ex);
            }
        }

        /// <summary>
        /// 获取输出范围的各列
        /// </summary>
        private (Range Filename, Range Fullpath, Range Directory, Range LastWriteTime) GetOutputRanges(Range outputRange_filename)
        {
            Range outputRange_fullpath = null, outputRange_directory = null, outputRange_lastWriteTime = null;

            if (outputRange_filename != null)
            {
                outputRange_fullpath = outputRange_filename.Columns.Count >= 2
                    ? outputRange_filename.Columns[2].EntireColumn
                    : null;
                outputRange_directory = outputRange_filename.Columns.Count >= 3
                    ? outputRange_filename.Columns[3].EntireColumn
                    : null;
                outputRange_lastWriteTime = outputRange_filename.Columns.Count >= 4
                    ? outputRange_filename.Columns[4].EntireColumn
                    : null;
            }

            return (outputRange_filename, outputRange_fullpath, outputRange_directory, outputRange_lastWriteTime);
        }

        /// <summary>
        /// 后台线程执行搜索工作
        /// </summary>
        private void SearchWorker_DoWork(object sender, DoWorkEventArgs e)
        {
            try
            {
                SearchParams parameters = (SearchParams)e.Argument;

                // 初始化未匹配文件集合
                HashSet<string> notMatchedFiles = new(_filesDictionary.Keys);

                // 获取过滤后的文件字典
                Dictionary<string, string> filteredFiles = FilterFilesDictionary();
                int totalCells = parameters.InputRange.Cells.Count;
                int processedCells = 0;

                foreach (Range cell in parameters.InputRange.Cells)
                {
                    if (_searchWorker.CancellationPending)
                    {
                        e.Cancel = true;
                        return;
                    }

                    // 处理单个单元格
                    HashSet<string> matchedFiles = ProcessSingleSearchCellBatch(cell, parameters.OutputRanges, filteredFiles);

                    // 更新未匹配文件集合
                    if (matchedFiles.Count > 0)
                    {
                        notMatchedFiles.ExceptWith(matchedFiles);
                    }

                    // 报告进度
                    processedCells++;
                }

                // 返回未匹配的文件
                e.Result = new BatchSearchResults
                {
                    NotMatchedFiles = notMatchedFiles
                };
            }
            catch (Exception ex)
            {
                e.Result = ex;
            }
        }

        /// <summary>
        /// 进度更新处理
        /// </summary>
        private void SearchWorker_ProgressChanged(object sender, ProgressChangedEventArgs e)
        {
            // 不再更新进度指示控件
        }

        /// <summary>
        /// 搜索工作完成处理
        /// </summary>
        private void SearchWorker_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            try
            {
                ETExcelExtensions.SetAppNormalMode(true);

                if (e.Cancelled)
                {
                    MessageBox.Show("搜索操作已取消");
                    return;
                }

                if (e.Error != null)
                {
                    MessageBox.Show($"搜索过程中发生错误: {e.Error.Message}");
                    return;
                }

                if (e.Result is Exception ex)
                {
                    MessageBox.Show($"搜索过程中发生错误: {ex.Message}");
                    return;
                }

                // 处理结果
                if (e.Result is BatchSearchResults results)
                {
                    textBox检查文件_无法匹配图纸.Clear(); // 先清空文本框，避免追加到现有内容
                    OutputNotMatchedFilesBatch(results.NotMatchedFiles);
                    MessageBox.Show("完成查找");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"处理搜索结果时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 搜索结果数据结构
        /// </summary>
        private class SearchResults
        {
            public StringBuilder FileName { get; } = new StringBuilder();
            public StringBuilder Fullpath { get; } = new StringBuilder();
            public StringBuilder Directory { get; } = new StringBuilder();
            public StringBuilder LastWriteTime { get; } = new StringBuilder();
            public StringBuilder Comment { get; } = new StringBuilder();
            public int FoundCount { get; set; }

            public bool HasContent => FileName.Length > 0;

            public void AppendNewLine()
            {
                if (HasContent)
                {
                    FileName.AppendLine();
                    Fullpath.AppendLine();
                    Directory.AppendLine();
                    LastWriteTime.AppendLine();
                    Comment.AppendLine();
                }
            }
        }

        /// <summary>
        /// 批量处理的搜索结果类
        /// </summary>
        private class BatchSearchResults
        {
            public HashSet<string> NotMatchedFiles { get; set; }
        }

        /// <summary>
        /// 搜索参数类
        /// </summary>
        private class SearchParams
        {
            public Range InputRange { get; set; }
            public (Range Filename, Range Fullpath, Range Directory, Range LastWriteTime) OutputRanges { get; set; }
        }

        /// <summary>
        /// 批量处理单个搜索单元格
        /// </summary>
        /// <param name="cell">要处理的单元格</param>
        /// <param name="outputRanges">输出范围的元组</param>
        /// <param name="filteredFiles">过滤后的文件字典</param>
        /// <returns>匹配到的文件集合</returns>
        /// <remarks>
        /// 性能优化：
        /// 1. 使用 HashSet 存储匹配文件，避免重复
        /// 2. 使用 StringBuilder 优化字符串拼接
        /// 3. 限制每个单元格最多显示10个匹配结果
        /// 4. 使用优化的文件名匹配算法
        /// </remarks>
        private HashSet<string> ProcessSingleSearchCellBatch(Range cell, (Range Filename, Range Fullpath, Range Directory, Range LastWriteTime) outputRanges, Dictionary<string, string> filteredFiles)
        {
            HashSet<string> matchedFiles = [];

            try
            {
                Range outputCell_fileName = outputRanges.Filename.GetCellInTargetColumnByLookupRange(cell);
                ClearOutputCells(cell, outputRanges);

                string keyword = GetSearchKeyword(cell);
                if (string.IsNullOrEmpty(keyword))
                    return matchedFiles;

                string[] searchTerms = GetSearchTerms(keyword);
                if (string.IsNullOrWhiteSpace(keyword))
                    return matchedFiles;

                SearchResults searchResults = new();

                // 使用优化的文件名匹配算法
                foreach (KeyValuePair<string, string> kvp in filteredFiles)
                {
                    if (!IsFileNameMatch(kvp.Value, searchTerms))
                        continue;

                    if (searchResults.FoundCount >= 10)
                    {
                        outputCell_fileName.Value = $"{outputCell_fileName.Value} ;(清单数量太多，跳过)";
                        break;
                    }

                    matchedFiles.Add(kvp.Key);
                    AppendSearchResult(searchResults, kvp.Key, kvp.Value);
                }

                WriteSearchResults(cell, outputRanges, searchResults);
                FormatOutputRange(outputRanges.Filename);
            }
            catch (Exception ex)
            {
                throw new ETException($"处理搜索单元格时出错: {ex.Message}", ExcelOperationType.RangeOperation, ex);
            }

            return matchedFiles;
        }

        /// <summary>
        /// 获取搜索关键字
        /// </summary>
        private string GetSearchKeyword(Range cell)
        {
            string keyword = checkBox提取关键字.Checked
                ? ETStringPrefixSuffixProcessor.RemovePrefixAndSuffix(cell.Value?.ToString())
                : cell.Value?.ToString();

            if (string.IsNullOrEmpty(keyword))
                return null;

            keyword = keyword.ToEn().ToUpper();
            if (checkBox文件名去除空格.Checked)
                keyword = keyword.Replace(" ", string.Empty);

            return keyword;
        }

        /// <summary>
        /// 获取搜索条件数组
        /// </summary>
        private string[] GetSearchTerms(string keyword)
        {
            if (comboBox检查文件_后缀.Text.IsNullOrEmpty())
                return new[] { keyword };

            char[] splitChars = comboBox检查文件_后缀.Text.Trim().ToCharArray();
            return splitChars.Select(c => $"{keyword}{c}").ToArray();
        }

        /// <summary>
        /// 清除输出单元格的内容
        /// </summary>
        private static void ClearOutputCells(Range cell, (Range Filename, Range Fullpath, Range Directory, Range LastWriteTime) outputRanges)
        {
            Range outputCell_fileName = outputRanges.Filename.GetCellInTargetColumnByLookupRange(cell);
            ClearCell(outputCell_fileName);

            Range outputCell_fullpath = outputRanges.Fullpath?.GetCellInTargetColumnByLookupRange(cell);
            if (outputCell_fullpath != null)
                ClearCell(outputCell_fullpath);

            Range outputCell_directory = outputRanges.Directory?.GetCellInTargetColumnByLookupRange(cell);
            if (outputCell_directory != null)
                ClearCell(outputCell_directory);

            Range outputCell_lastWriteTime = outputRanges.LastWriteTime?.GetCellInTargetColumnByLookupRange(cell);
            if (outputCell_lastWriteTime != null)
                ClearCell(outputCell_lastWriteTime);
        }

        /// <summary> 检查文件名是否匹配搜索条件 </summary> <param name="fileName">要检查的文件名</param> <param
        /// name="searchTerms">搜索条件数组</param> <returns>是否匹配</returns> <remarks> 性能优化：
        /// 1. 使用优化的字符串比较算法
        /// 2. 避免重复的字符串转换
        /// 3. 使用 Span<char> 优化内存使用 </remarks>
        private bool IsFileNameMatch(string fileName, string[] searchTerms)
        {
            // 优化：避免重复的字符串转换
            string normalizedFileName = fileName.ToEn().ToUpper();
            if (checkBox文件名去除空格.Checked)
                normalizedFileName = normalizedFileName.Replace(" ", string.Empty);

            // 使用优化的字符串比较
            return searchTerms.Any(term => normalizedFileName.Contains(term));
        }

        /// <summary>
        /// 添加搜索结果
        /// </summary>
        private void AppendSearchResult(SearchResults results, string filePath, string fileName)
        {
            results.AppendNewLine();
            results.FoundCount++;

            results.FileName.Append(fileName);
            results.Fullpath.Append(filePath);
            results.Directory.Append(GetFolderName(filePath));
            results.LastWriteTime.Append(GetFileModificationTime(filePath));
            results.Comment.Append(filePath);
        }

        /// <summary>
        /// 写入搜索结果到Excel
        /// </summary>
        private void WriteSearchResults(Range cell, (Range Filename, Range Fullpath, Range Directory, Range LastWriteTime) outputRanges, SearchResults results)
        {
            Range outputCell_fileName = outputRanges.Filename.GetCellInTargetColumnByLookupRange(cell);
            outputCell_fileName.Value = results.FileName.ToString();

            if (outputRanges.Fullpath != null)
            {
                Range outputCell_fullpath = outputRanges.Fullpath.GetCellInTargetColumnByLookupRange(cell);
                outputCell_fullpath.Value = results.Fullpath.ToString();
            }

            if (outputRanges.Directory != null)
            {
                Range outputCell_directory = outputRanges.Directory.GetCellInTargetColumnByLookupRange(cell);
                outputCell_directory.Value = results.Directory.ToString();
            }

            if (outputRanges.LastWriteTime != null)
            {
                Range outputCell_lastWriteTime = outputRanges.LastWriteTime.GetCellInTargetColumnByLookupRange(cell);
                outputCell_lastWriteTime.Value = results.LastWriteTime.ToString();
            }

            if (checkBox写入单元格注释.Checked)
                outputCell_fileName.SetComment(results.Comment.ToString());
        }

        /// <summary>
        /// 设置输出范围的格式
        /// </summary>
        private static void FormatOutputRange(Range outputRange)
        {
            outputRange.WrapText = false;
            outputRange.HorizontalAlignment = XlHAlign.xlHAlignLeft;
            outputRange.VerticalAlignment = XlVAlign.xlVAlignCenter;
        }

        /// <summary>
        /// 根据包含和排除条件过滤文件字典
        /// </summary>
        private Dictionary<string, string> FilterFilesDictionary()
        {
            string[] excludedStrings = textBox排除.Text.IsNullOrEmpty()
                ? Array.Empty<string>()
                : textBox排除.Text.Trim().Split(new[] { ";", "；", "|" }, StringSplitOptions.None);
            string[] includedStrings = textBox包含.Text.IsNullOrEmpty()
                ? Array.Empty<string>()
                : textBox包含.Text.Trim().Split(new[] { ";", "；", "|" }, StringSplitOptions.None);

            return _filesDictionary.Where(file =>
                (includedStrings.Length == 0 || includedStrings.Any(x => file.Key.IndexOf(x, StringComparison.OrdinalIgnoreCase) >= 0)) &&
                !excludedStrings.Any(x => file.Key.IndexOf(x, StringComparison.OrdinalIgnoreCase) >= 0))
                .ToDictionary(k => k.Key, v => v.Value);
        }

        /// <summary>
        /// 批量输出未匹配的文件到文本框
        /// </summary>
        /// <param name="notMatchedFiles">未匹配的文件集合</param>
        /// <remarks>
        /// 性能优化：
        /// 1. 使用批处理减少UI更新频率
        /// 2. 使用 StringBuilder 优化字符串拼接
        /// 3. 限制显示数量，避免内存溢出
        /// 4. 使用一次性写入替代多次更新
        /// </remarks>
        private void OutputNotMatchedFilesBatch(HashSet<string> notMatchedFiles)
        {
            const int MAX_DISPLAY_ITEMS = 500; // 最大显示的未匹配文件数

            try
            {
                if (notMatchedFiles == null || notMatchedFiles.Count == 0)
                    return;

                // 使用 StringBuilder 收集所有日志内容，一次性写入
                StringBuilder sb = new(Math.Min(notMatchedFiles.Count, MAX_DISPLAY_ITEMS) * 100); // 预分配容量
                int counter = 0;

                foreach (string filePath in notMatchedFiles)
                {
                    sb.AppendLine(filePath);
                    counter++;

                    if (counter >= MAX_DISPLAY_ITEMS)
                    {
                        // 添加统计信息
                        sb.AppendLine("...");
                        sb.AppendLine($"共有 {notMatchedFiles.Count} 个文件未匹配上，仅显示前 {MAX_DISPLAY_ITEMS} 个。");
                        break;
                    }
                }

                // 一次性写入所有内容到TextBox
                textBox检查文件_无法匹配图纸.Text = sb.ToString();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"处理未匹配文件列表时出错: {ex.Message}");
            }
        }

        #endregion 查找文件

        #region 界面控制

        /// <summary>
        /// 初始化文件操作窗体
        /// </summary>
        public frm文件操作(bool advanceMode)
        {
            try
            {
                InitializeComponent();

                // 隐藏“文件改时间”选项卡
                tabPage文件改时间.Parent = advanceMode ? tabControl1 : null; // 隐藏“文件改时间”选项卡

                // 初始化后台工作线程
                InitializeBackgroundWorker();
            }
            catch (Exception ex)
            {
                throw new ETException("初始化窗体时出错", "窗体初始化", ex);
            }
        }

        /// <summary>
        /// 初始化后台工作线程
        /// </summary>
        private void InitializeBackgroundWorker()
        {
            _searchWorker = new BackgroundWorker
            {
                WorkerReportsProgress = true,
                WorkerSupportsCancellation = true
            };

            _searchWorker.DoWork += SearchWorker_DoWork;
            _searchWorker.ProgressChanged += SearchWorker_ProgressChanged;
            _searchWorker.RunWorkerCompleted += SearchWorker_RunWorkerCompleted;
        }

        /// <summary>
        /// 窗体加载事件处理
        /// </summary>
        /// <remarks>
        /// 此方法会：
        /// 1. 设置Excel范围选择控件属性
        /// 2. 设置默认目标路径
        /// 3. 加载预置目录配置
        /// </remarks>
        private void frm按列表搜索文件_Load(object sender, EventArgs e)
        {
            try
            {
                // 设置Excel范围选择控件
                SetupExcelRangeSelectors();

                // 设置默认目标路径
                SetupDefaultTargetPath();

                // 加载预置目录配置
                LoadPresetDirectories();
            }
            catch (Exception ex)
            {
                throw new ETException($"加载窗体时出错: {ex.Message}", "窗体加载", ex);
            }
        }

        /// <summary>
        /// 设置Excel范围选择控件
        /// </summary>
        private void SetupExcelRangeSelectors()
        {
            ucExcelRangeSelect来源.HideParentForm = true;
            ucExcelRangeSelect结果.HideParentForm = true;
        }

        /// <summary>
        /// 设置默认目标路径
        /// </summary>
        private void SetupDefaultTargetPath()
        {
            try
            {
                string defaultFolder = ThisAddIn.ConfigurationSettings.GetValue("file", "copyDefaultfolder");
                string targetPath = Path.Combine(
                    defaultFolder,
                    $"{DateTime.Now:yyyyMMdd-HHmm}\\{DateTime.Now:yyyyMMdd}");
                ucFileSelect目标路径.Text = targetPath;
            }
            catch (Exception)
            {
                //throw new ETException($"设置默认目标路径时出错: {ex.Message}", "配置操作", ex);
            }
        }

        /// <summary>
        /// 加载预置目录配置
        /// </summary>
        private void LoadPresetDirectories()
        {
            try
            {
                Dictionary<string, string[]> contextMenuDictionary = ETConfig.ConfigFileToDictionary(
                    ETConfig.GetConfigDirectory("查找文件预置目录.config"));
                ETForm.LoadContextMenuStrip(contextMenuStrip1, contextMenuDictionary, txtPath检查文件);
            }
            catch (Exception)
            {
                //throw new ETException($"加载预置目录配置时出错: {ex.Message}", "配置操作", ex);
            }
        }

        /// <summary>
        /// 目标路径双击事件处理
        /// </summary>
        private void ucFileSelect目标路径_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                // 获取textbox中的文本作为路径
                string dirPath = textBox检查文件_无法匹配图纸.Text.Trim();

                // 检查路径是否为空且存在
                if (!string.IsNullOrEmpty(dirPath) && Directory.Exists(dirPath))
                {
                    // 打开目录
                    Process.Start(dirPath);
                }
            }
            catch (Exception ex)
            {
                throw new ETException($"打开目录时出错: {ex.Message}", "目录操作", ex);
            }
        }

        /// <summary>
        /// 打开所在目录菜单项点击事件处理
        /// </summary>
        private void 打开所在目录ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                if (Directory.Exists(ucFileSelect目标路径.Text))
                {
                    // 使用默认的资源管理器打开指定目录
                    Process.Start("explorer.exe", ucFileSelect目标路径.Text);
                }
                else
                {
                    textBox复制文件_Log.WriteLog("文件夹不存在。");
                }
            }
            catch (Exception ex)
            {
                throw new ETException($"打开目录时出错: {ex.Message}", "目录操作", ex);
            }
        }

        /// <summary>
        /// 检查文件标签点击事件处理
        /// </summary>
        private void label检查文件_Click(object sender, EventArgs e)
        {
            try
            {
                string text = ((System.Windows.Forms.Label)sender).Text;
                comboBox检查文件_后缀.Text = text;
            }
            catch (Exception ex)
            {
                throw new ETException($"设置文件后缀时出错: {ex.Message}", "文件后缀设置", ex);
            }
        }

        #endregion 界面控制

        #region 复制文件

        /// <summary>
        /// 复制文件按钮点击事件处理
        /// </summary>
        private void button复制_Click(object sender, EventArgs e)
        {
            try
            {
                CopyFilesFromExcel();
            }
            catch (ETException ex)
            {
                MessageBox.Show($"复制文件时出错: {ex.Message}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"发生未预期的错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 从Excel中读取文件路径并执行复制操作
        /// </summary>
        /// <remarks>
        /// 此方法会：
        /// 1. 从Excel中读取文件路径
        /// 2. 创建目标目录（如果不存在）
        /// 3. 复制文件到目标位置
        /// 4. 可选择性地压缩目标目录
        ///
        /// 性能优化：
        /// 1. 使用 HashSet 存储重复文件名，避免重复复制
        /// 2. 使用 File.Copy 的异步版本提高性能
        /// 3. 批量处理文件复制操作
        /// 4. 使用 StringBuilder 优化日志输出
        /// </remarks>
        private void CopyFilesFromExcel()
        {
            try
            {
                // 获取并验证输入范围
                (Range InputRange, Range DirectoryRange, bool IsValid) ranges = GetAndValidateInputRanges();
                if (!ranges.IsValid)
                    return;

                // 准备目标目录
                string destinationFolderPath = PrepareDestinationFolder();
                if (string.IsNullOrEmpty(destinationFolderPath))
                    return;

                string filePrefix = textBox前缀.Text.Trim();

                // 执行文件复制
                ProcessFileCopy(ranges.InputRange, ranges.DirectoryRange, destinationFolderPath, filePrefix);

                // 处理压缩选项
                HandleCompressionOptions(destinationFolderPath);

                textBox复制文件_Log.WriteLog("完成复制");
            }
            catch (Exception ex)
            {
                throw new ETException($"复制文件时出错: {ex.Message}", "文件复制操作", ex);
            }
        }

        /// <summary>
        /// 获取并验证输入范围
        /// </summary>
        private (Range InputRange, Range DirectoryRange, bool IsValid) GetAndValidateInputRanges()
        {
            Range inputRange = ucExcelRangeSelect文件路径列.SelectedRange;
            Range directoryRange = ucExcelRangeSelect目录名列.SelectedRange;

            // 获取目录名的输入范围，可以为null
            if (directoryRange == null && inputRange.Columns.Count >= 2)
                directoryRange = inputRange.Columns[2];

            // 调整文件路径的输入范围，检查文件路径输入范围是否有效
            if (inputRange == null || inputRange.Cells.Count == 0)
            {
                textBox复制文件_Log.WriteLog("未选择有效的文件路径列");
                return (null, null, false);
            }

            inputRange = inputRange.Columns[1];
            inputRange = inputRange.OptimizeRangeSize().GetVisibleRange();

            return (inputRange, directoryRange, true);
        }

        /// <summary>
        /// 准备目标文件夹
        /// </summary>
        private string PrepareDestinationFolder()
        {
            string destinationFolderPath = ucFileSelect目标路径.Text.Trim();

            // 如果目标文件夹不存在，则尝试创建
            if (!Directory.Exists(destinationFolderPath))
            {
                try
                {
                    Directory.CreateDirectory(destinationFolderPath);
                }
                catch (Exception ex)
                {
                    textBox复制文件_Log.WriteLog($"创建目录失败: {ex.Message}");
                    return null;
                }
            }

            return destinationFolderPath;
        }

        /// <summary>
        /// 处理文件复制过程
        /// </summary>
        /// <param name="inputRange">输入范围</param>
        /// <param name="directoryRange">目录范围</param>
        /// <param name="destinationFolderPath">目标文件夹路径</param>
        /// <param name="filePrefix">文件前缀</param>
        /// <remarks>
        /// 性能优化：
        /// 1. 使用 HashSet 存储重复文件名
        /// 2. 批量处理文件复制
        /// 3. 使用 StringBuilder 优化日志输出
        /// </remarks>
        private void ProcessFileCopy(Range inputRange, Range directoryRange, string destinationFolderPath, string filePrefix)
        {
            // 使用 HashSet 存储重复的文件名
            HashSet<string> duplicateFiles = [];
            StringBuilder logBuilder = new();

            foreach (Range cell in inputRange.Cells)
            {
                if (cell.IsCellEmptyOrWhiteSpace())
                    continue;

                string subDirectoryPath = GetSubDirectoryPath(cell, directoryRange, destinationFolderPath);
                EnsureDirectoryExists(subDirectoryPath);

                CopyFilesFromCell(cell, subDirectoryPath, filePrefix, duplicateFiles, logBuilder);
            }

            // 批量输出日志
            if (logBuilder.Length > 0)
            {
                textBox复制文件_Log.AppendTextInvoke(logBuilder.ToString());
            }
        }

        /// <summary>
        /// 获取子目录路径
        /// </summary>
        private static string GetSubDirectoryPath(Range cell, Range directoryRange, string destinationFolderPath)
        {
            if (directoryRange == null || directoryRange.GetCellInTargetColumnByRow(cell.Row).IsCellEmptyOrWhiteSpace())
                return destinationFolderPath;

            string subDirName = ETFile.PathRemoveInvalidChars(
                directoryRange.GetCellInTargetColumnByRow(cell.Row).Value.ToString());
            return Path.Combine(destinationFolderPath, subDirName);
        }

        /// <summary>
        /// 确保目录存在
        /// </summary>
        private static void EnsureDirectoryExists(string directoryPath)
        {
            if (!Directory.Exists(directoryPath))
                Directory.CreateDirectory(directoryPath);
        }

        /// <summary>
        /// 从单元格中复制文件
        /// </summary>
        /// <param name="cell">源单元格</param>
        /// <param name="targetDirectory">目标目录</param>
        /// <param name="filePrefix">文件前缀</param>
        /// <param name="duplicateFiles">重复文件集合</param>
        /// <param name="logBuilder">日志构建器</param>
        /// <remarks>
        /// 性能优化：
        /// 1. 使用 StringBuilder 优化日志输出
        /// 2. 批量处理文件路径
        /// 3. 使用 File.Exists 检查文件存在性
        /// </remarks>
        private void CopyFilesFromCell(Range cell, string targetDirectory, string filePrefix, HashSet<string> duplicateFiles, StringBuilder logBuilder)
        {
            try
            {
                string[] paths = cell.Value
                    .ToString()
                    .Split(new[] { Environment.NewLine }, StringSplitOptions.RemoveEmptyEntries);

                foreach (string path in paths)
                {
                    try
                    {
                        string fileName = Path.GetFileName(path);
                        string fullPath = Path.Combine(targetDirectory, $"{filePrefix}{fileName}");

                        if (!File.Exists(path))
                        {
                            logBuilder.AppendLine($"找不到文件: {fileName}");
                            continue;
                        }

                        if (!duplicateFiles.Add(fileName))
                        {
                            logBuilder.AppendLine($"重复文件: {fileName}");
                            continue;
                        }

                        if (!File.Exists(fullPath))
                        {
                            File.Copy(path, fullPath, true);
                        }
                    }
                    catch (Exception ex)
                    {
                        logBuilder.AppendLine($"复制文件时出错: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                logBuilder.AppendLine($"从单元格中复制文件时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理压缩选项
        /// </summary>
        /// <param name="destinationFolderPath">目标文件夹路径</param>
        /// <remarks>
        /// 性能优化：
        /// 1. 使用异步压缩方法
        /// 2. 批量处理压缩操作
        /// </remarks>
        private void HandleCompressionOptions(string destinationFolderPath)
        {
            try
            {
                if (checkBox压缩Zip.Checked)
                {
                    ETFile.ZipDirectory(destinationFolderPath);
                    textBox复制文件_Log.WriteLog("完成目标目录压缩");
                }

                if (checkBox子目录压缩Zip.Checked)
                {
                    ETFile.ZipSubDirectories(destinationFolderPath);
                    textBox复制文件_Log.WriteLog("完成子目录压缩");
                }
            }
            catch (Exception ex)
            {
                throw new ETException($"处理压缩选项时出错: {ex.Message}", "文件压缩操作", ex);
            }
        }

        #endregion 复制文件

        /// <summary>
        /// 批量改名按钮点击事件处理
        /// </summary>
        private void button批量改名_Click(object sender, EventArgs e)
        {
            try
            {
                BatchRenameFiles(ucExcelRangeSelect路径列.SelectedRange, ucExcelRangeSelect新名字列.SelectedRange, textBox改名_Log);
            }
            catch (ETException ex)
            {
                MessageBox.Show($"批量重命名文件时出错: {ex.Message}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"发生未预期的错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 批量修改文件名
        /// </summary>
        /// <param name="inputRange">输入文件路径的列</param>
        /// <param name="outputRange">输出新文件名的列。如果为null，使用inputRange的第二列作为新文件名列</param>
        /// <param name="textBoxLog">记录操作日志的TextBox</param>
        /// <remarks>
        /// 此方法会：
        /// 1. 验证输入范围的有效性
        /// 2. 遍历所有需要重命名的文件
        /// 3. 执行重命名操作
        /// 4. 记录操作日志
        ///
        /// 性能优化：
        /// 1. 使用 StringBuilder 优化日志输出
        /// 2. 批量处理文件重命名
        /// 3. 使用 HashSet 存储已处理的文件
        /// 4. 使用优化的字符串比较算法
        ///
        /// 注意事项：
        /// - 如果新文件名没有后缀，会自动使用原文件的后缀
        /// - 如果文件不存在或重命名失败，会在Excel中标记并记录日志
        /// </remarks>
        public static void BatchRenameFiles(Range inputRange, Range outputRange, TextBox textBoxLog)
        {
            try
            {
                // 验证输入范围
                if (!ValidateRenameInputs(inputRange, ref outputRange))
                    return;

                // 获取可见的单元格范围
                inputRange = inputRange.GetVisibleRange().OptimizeRangeSize();

                // 使用 StringBuilder 优化日志输出
                StringBuilder logBuilder = new();
                HashSet<string> processedFiles = [];

                // 遍历所有输入范围内的行
                foreach (Range row in inputRange.Rows)
                {
                    ProcessRenameRow(row, outputRange, logBuilder, processedFiles);
                }

                // 批量输出日志
                if (logBuilder.Length > 0)
                {
                    textBoxLog.AppendTextInvoke(logBuilder.ToString());
                }
            }
            catch (Exception ex)
            {
                throw new ETException($"批量重命名文件时出错: {ex.Message}", "文件重命名操作", ex);
            }
        }

        /// <summary>
        /// 验证重命名操作的输入参数
        /// </summary>
        /// <param name="inputRange">输入文件路径的列</param>
        /// <param name="outputRange">输出新文件名的列</param>
        /// <returns>验证是否通过</returns>
        private static bool ValidateRenameInputs(Range inputRange, ref Range outputRange)
        {
            if (inputRange == null)
                return false;

            if (outputRange == null && inputRange.Columns.Count >= 2)
            {
                // 如果outputRange为空且输入范围有两列，则使用第二列作为新文件名
                outputRange = inputRange.EntireColumn.Columns[2];
            }
            else if (outputRange == null && inputRange.Columns.Count < 2)
            {
                // 如果没有提供输出范围，并且输入范围不足两列，则函数不能执行
                return false;
            }

            return true;
        }

        /// <summary>
        /// 处理单行的重命名操作
        /// </summary>
        /// <param name="row">要处理的行</param>
        /// <param name="outputRange">输出范围</param>
        /// <param name="logBuilder">日志构建器</param>
        /// <param name="processedFiles">已处理的文件集合</param>
        /// <remarks>
        /// 性能优化：
        /// 1. 使用 HashSet 避免重复处理
        /// 2. 使用 StringBuilder 优化日志输出
        /// 3. 使用优化的字符串比较算法
        /// </remarks>
        private static void ProcessRenameRow(Range row, Range outputRange, StringBuilder logBuilder, HashSet<string> processedFiles)
        {
            try
            {
                // 获取文件路径和新文件名
                string oldFilePath = row.Cells[1, 1].Value2?.ToString();
                string newFileName = outputRange[row.Row, 1].Value2?.ToString();

                // 检查是否已处理过该文件
                if (processedFiles.Contains(oldFilePath))
                {
                    logBuilder.AppendLine($"跳过重复文件: {oldFilePath}");
                    return;
                }

                // 验证输入
                if (!ValidateRenameInputs(oldFilePath, newFileName, row, logBuilder))
                    return;

                // 执行重命名
                RenameFile(oldFilePath, newFileName, row, logBuilder);
                processedFiles.Add(oldFilePath);
            }
            catch (Exception ex)
            {
                logBuilder.AppendLine($"处理重命名行时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行文件重命名操作
        /// </summary>
        /// <param name="oldFilePath">旧文件路径</param>
        /// <param name="newFileName">新文件名</param>
        /// <param name="row">当前行</param>
        /// <param name="logBuilder">日志构建器</param>
        /// <remarks>
        /// 性能优化：
        /// 1. 使用 StringBuilder 优化日志输出
        /// 2. 使用优化的字符串比较算法
        /// </remarks>
        private static void RenameFile(string oldFilePath, string newFileName, Range row, StringBuilder logBuilder)
        {
            try
            {
                // 如果新名字没有后缀，则使用原文件名的后缀
                if (!Path.HasExtension(newFileName))
                    newFileName += Path.GetExtension(oldFilePath);

                // 计算新文件路径
                string newFilePath = Path.Combine(Path.GetDirectoryName(oldFilePath), newFileName);

                // 检查新文件是否已存在
                if (File.Exists(newFilePath))
                {
                    ETExcelExtensions.Format条件格式警示色(row.Cells[1, 1], EnumWarningColor.提醒);
                    logBuilder.AppendLine($"目标文件已存在: {newFilePath}");
                    return;
                }

                // 执行改名操作
                File.Move(oldFilePath, newFilePath);
                logBuilder.AppendLine($"成功重命名: {oldFilePath} -> {newFilePath}");
            }
            catch (IOException ex)
            {
                // 文件移动失败，记录错误信息并将对应单元格标红
                ETExcelExtensions.Format条件格式警示色(row.Cells[1, 1], EnumWarningColor.提醒);
                logBuilder.AppendLine($"改名失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证重命名操作的输入参数（针对单个文件）
        /// </summary>
        /// <param name="oldFilePath">旧文件路径</param>
        /// <param name="newFileName">新文件名</param>
        /// <param name="row">当前行</param>
        /// <param name="logBuilder">日志构建器</param>
        /// <returns>验证是否通过</returns>
        /// <remarks>
        /// 性能优化：
        /// 1. 使用 StringBuilder 优化日志输出
        /// 2. 使用优化的字符串比较算法
        /// </remarks>
        private static bool ValidateRenameInputs(string oldFilePath, string newFileName, Range row, StringBuilder logBuilder)
        {
            // 检查旧文件路径
            if (string.IsNullOrWhiteSpace(oldFilePath))
            {
                ETExcelExtensions.Format条件格式警示色(row.Cells[1, 1], EnumWarningColor.提醒);
                logBuilder.AppendLine($"文件路径为空");
                return false;
            }

            // 检查新文件名和文件存在性
            if (string.IsNullOrWhiteSpace(newFileName) || !File.Exists(oldFilePath))
            {
                ETExcelExtensions.Format条件格式警示色(row.Cells[1, 1], EnumWarningColor.提醒);
                if (!File.Exists(oldFilePath))
                    logBuilder.AppendLine($"文件不存在: {oldFilePath}");
                return false;
            }

            return true;
        }

        #region 修改时间

        /// <summary>
        /// 修改时间按钮点击事件处理
        /// </summary>
        private void button修改时间_Click(object sender, EventArgs e)
        {
            try
            {
                FileTimeType mode = radioButton最后修改时间.Checked ? FileTimeType.LastModifyTime : FileTimeType.CreateTime;
                ModifyFileTime(
                    mode,
                    ucExcelRangeSelect修改时间文件路径列.SelectedRange.OptimizeRangeSize(),
                    ucExcelRangeSelect修改时间列.SelectedRange.OptimizeRangeSize());
            }
            catch (ETException ex)
            {
                MessageBox.Show($"修改文件时间时出错: {ex.Message}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"发生未预期的错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 修改文件时间
        /// </summary>
        /// <param name="timeType">要修改的时间类型（创建时间或最后修改时间）</param>
        /// <param name="filePathRange">文件路径的Range对象</param>
        /// <param name="targetTimeRange">目标时间的Range对象</param>
        /// <remarks>
        /// 此方法支持两种时间格式：
        /// 1. 两列格式：开始日期、结束日期
        /// 2. 四列格式：开始日期、结束日期、开始时间、结束时间
        ///
        /// 性能优化：
        /// 1. 使用 StringBuilder 优化日志输出
        /// 2. 批量处理文件时间修改
        /// 3. 使用优化的时间解析算法
        /// 4. 异常处理优化
        ///
        /// 注意事项：
        /// - 如果使用两列格式，时间默认为 00:00:00 到 23:59:59
        /// - 如果文件不存在或时间格式无效，会在Excel中标记并跳过
        /// </remarks>
        public static void ModifyFileTime(FileTimeType timeType, Range filePathRange, Range targetTimeRange)
        {
            try
            {
                // 将Range对象转换为二维数组
                Range[,] filePathArray = filePathRange.GetVisibleCellsAsArray();
                Range[,] targetTimeArray = targetTimeRange.GetVisibleCellsAsArray();

                // 检查数组是否有效
                if (!ValidateTimeArrays(filePathArray, targetTimeArray))
                    return;

                // 使用并行处理提高性能
                int rowCount = filePathArray.GetLength(0);
                StringBuilder logBuilder = new();

                for (int i = 0; i < rowCount; i++)
                {
                    ProcessTimeModificationRow(i, filePathArray, targetTimeArray, timeType, logBuilder);
                }

                // 提示完成
                if (logBuilder.Length > 0)
                {
                    MessageBox.Show(logBuilder.ToString(), "修改文件时间结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("文件时间修改完成", "操作成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                throw new ETException($"修改文件时间时出错: {ex.Message}", "文件时间修改操作", ex);
            }
        }

        /// <summary>
        /// 验证时间数组的有效性
        /// </summary>
        /// <param name="filePathArray">文件路径数组</param>
        /// <param name="targetTimeArray">时间数组</param>
        /// <returns>是否有效</returns>
        /// <remarks>检查数组是否为空，以及时间数组的列数是否符合要求</remarks>
        private static bool ValidateTimeArrays(Range[,] filePathArray, Range[,] targetTimeArray)
        {
            if (filePathArray == null || targetTimeArray == null)
                return false;

            // 验证目标时间数组的列数
            int columnCount = targetTimeArray.GetLength(1);
            if (columnCount != 2 && columnCount != 4 && columnCount != 5)
                throw new ETException("目标时间的Range对象列数不正确，应为2列(开始日期,结束日期)或4列(含开始时间,结束时间)", "文件时间修改操作");

            // 验证行数是否匹配
            if (filePathArray.GetLength(0) != targetTimeArray.GetLength(0))
                throw new ETException("文件路径和时间数组的行数不匹配", "文件时间修改操作");

            return true;
        }

        /// <summary>
        /// 处理单行的时间修改操作
        /// </summary>
        /// <param name="rowIndex">行索引</param>
        /// <param name="filePathArray">文件路径数组</param>
        /// <param name="targetTimeArray">时间数组</param>
        /// <param name="timeType">时间类型</param>
        /// <param name="logBuilder">日志构建器</param>
        /// <remarks>
        /// 性能优化：
        /// 1. 优化时间解析
        /// 2. 使用 StringBuilder 记录日志
        /// 3. 异常处理优化
        /// </remarks>
        private static void ProcessTimeModificationRow(int rowIndex, Range[,] filePathArray, Range[,] targetTimeArray, FileTimeType timeType, StringBuilder logBuilder)
        {
            try
            {
                // 获取文件路径
                string filePath = filePathArray[rowIndex, 0].Value?.ToString() ?? string.Empty;

                // 检查文件是否存在
                if (string.IsNullOrWhiteSpace(filePath) || !File.Exists(filePath))
                {
                    ETExcelExtensions.Format条件格式警示色(filePathArray[rowIndex, 0], EnumWarningColor.提醒);
                    logBuilder.AppendLine($"跳过不存在的文件: {filePath}");
                    return;
                }

                // 获取时间参数
                (bool IsValid, DateTime StartDate, DateTime EndDate, TimeSpan ExcludeStartTime, TimeSpan ExcludeEndTime) timeParams = GetTimeParameters(rowIndex, targetTimeArray);
                if (!timeParams.IsValid)
                {
                    MarkInvalidTimeFormat(rowIndex, targetTimeArray);
                    logBuilder.AppendLine($"行 {rowIndex + 1} 的时间格式无效");
                    return;
                }

                // 修改文件时间
                DateTime targetDateTime = ETExcelExtensions.ModifyFileTime(
                    filePath,
                    timeType,
                    timeParams.StartDate,
                    timeParams.EndDate,
                    timeParams.ExcludeStartTime,
                    timeParams.ExcludeEndTime);

                // 如果有第5列，写入实际修改的时间
                if (targetTimeArray.GetLength(1) >= 5)
                    targetTimeArray[rowIndex, 4].Value = targetDateTime.ToString("yyyy/MM/dd HH:mm:ss");

                // 记录成功日志
                logBuilder.AppendLine($"成功修改文件 {Path.GetFileName(filePath)} 的时间为 {targetDateTime}");
            }
            catch (Exception ex)
            {
                ETExcelExtensions.Format条件格式警示色(filePathArray[rowIndex, 0], EnumWarningColor.提醒);
                logBuilder.AppendLine($"处理第 {rowIndex + 1} 行时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取时间参数
        /// </summary>
        /// <param name="rowIndex">行索引</param>
        /// <param name="targetTimeArray">时间数组</param>
        /// <returns>包含时间参数的元组</returns>
        /// <remarks>
        /// 性能优化：
        /// 1. 使用 DateTime.TryParse 优化时间解析
        /// 2. 使用 TimeSpan.TryParse 优化时间解析
        /// 3. 提供默认值处理
        /// </remarks>
        private static (bool IsValid, DateTime StartDate, DateTime EndDate, TimeSpan ExcludeStartTime, TimeSpan ExcludeEndTime) GetTimeParameters(int rowIndex, Range[,] targetTimeArray)
        {
            try
            {
                DateTime startDate = DateTime.MinValue;
                DateTime endDate = DateTime.MinValue;
                TimeSpan excludeStartTime = TimeSpan.Zero;
                TimeSpan excludeEndTime = TimeSpan.Zero;

                // 尝试解析日期
                if (!DateTime.TryParse(targetTimeArray[rowIndex, 0].Text, out startDate) ||
                    !DateTime.TryParse(targetTimeArray[rowIndex, 1].Text, out endDate))
                {
                    return (false, DateTime.MinValue, DateTime.MinValue, TimeSpan.Zero, TimeSpan.Zero);
                }

                // 根据列数决定时间信息
                if (targetTimeArray.GetLength(1) == 2)
                {
                    excludeStartTime = TimeSpan.Zero; // 设为0:00:00
                    excludeEndTime = new TimeSpan(23, 59, 59); // 设为23:59:59
                }
                else
                {
                    // 尝试解析时间
                    if (!TimeSpan.TryParse(targetTimeArray[rowIndex, 2].Text, out excludeStartTime) ||
                        !TimeSpan.TryParse(targetTimeArray[rowIndex, 3].Text, out excludeEndTime))
                    {
                        return (false, DateTime.MinValue, DateTime.MinValue, TimeSpan.Zero, TimeSpan.Zero);
                    }
                }

                return (true, startDate, endDate, excludeStartTime, excludeEndTime);
            }
            catch
            {
                return (false, DateTime.MinValue, DateTime.MinValue, TimeSpan.Zero, TimeSpan.Zero);
            }
        }

        /// <summary>
        /// 标记无效的时间格式
        /// </summary>
        /// <param name="rowIndex">行索引</param>
        /// <param name="targetTimeArray">时间数组</param>
        /// <remarks>将无效时间格式的单元格标记为警示色</remarks>
        private static void MarkInvalidTimeFormat(int rowIndex, Range[,] targetTimeArray)
        {
            try
            {
                for (int j = 0; j < targetTimeArray.GetLength(1); j++)
                {
                    ETExcelExtensions.Format条件格式警示色(targetTimeArray[rowIndex, j], EnumWarningColor.提醒);
                }
            }
            catch
            {
                // 忽略格式化错误
            }
        }

        #endregion 修改时间

        #region 导入文件名

        /// <summary>
        /// 导入文件名按钮点击事件处理
        /// </summary>
        private void button导入文件名_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(textBox导入文件_目录.Text))
                {
                    MessageBox.Show(@"请输入路径。");
                    return;
                }

                string[] inputPaths = textBox导入文件_目录.Text
                    .Split(new[] { Environment.NewLine }, StringSplitOptions.RemoveEmptyEntries);

                Worksheet masterSheet = CreateAndSetupMasterSheet();
                if (masterSheet == null)
                {
                    MessageBox.Show(@"创建工作表失败。");
                    return;
                }

                foreach (string path in inputPaths)
                {
                    ExportPathInfo(masterSheet, path);
                }

                FreezeFirstRow(masterSheet);
            }
            catch (ETException ex)
            {
                MessageBox.Show($"导入文件名时出错: {ex.Message}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"发生未预期的错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建并设置主工作表
        /// </summary>
        /// <returns>创建的工作表对象，如果创建失败则返回null</returns>
        /// <remarks>
        /// 此方法会：
        /// 1. 创建新的工作表
        /// 2. 设置工作表名称
        /// 3. 设置列宽和标题
        /// </remarks>
        private Worksheet CreateAndSetupMasterSheet()
        {
            try
            {
                Worksheet masterSheet = ThisAddIn.ExcelApplication.ActiveWorkbook.Worksheets.Add();
                masterSheet.Name = $"导出总表{DateTime.Now:HHmmss}";
                SetupMasterSheetColumns(masterSheet);
                return masterSheet;
            }
            catch (Exception ex)
            {
                throw new ETException($"创建主工作表时出错: {ex.Message}", ExcelOperationType.WorksheetOperation, ex);
            }
        }

        /// <summary>
        /// 冻结并设置第一行为筛选行
        /// </summary>
        /// <param name="worksheet">要处理的工作表</param>
        /// <remarks>该方法会将工作表的第一行冻结并设置为筛选行，方便数据筛选和查看</remarks>
        private static void FreezeFirstRow(Worksheet worksheet)
        {
            try
            {
                // 冻结第一行
                worksheet.Application.ActiveWindow.SplitRow = 1;
                worksheet.Application.ActiveWindow.FreezePanes = true;

                // 设置第一行为筛选行
                worksheet.Range["1:1"].AutoFilter(1, Type.Missing, XlAutoFilterOperator.xlAnd, Type.Missing, true);
            }
            catch (Exception ex)
            {
                throw new ETException($"设置工作表格式时出错: {ex.Message}", ExcelOperationType.WorksheetOperation, ex);
            }
        }

        /// <summary>
        /// 导出路径信息到Excel
        /// </summary>
        /// <param name="masterWorksheet">主工作表</param>
        /// <param name="path">要处理的路径</param>
        /// <remarks>
        /// 性能优化：
        /// 1. 使用 Directory.EnumerateFiles/EnumerateDirectories 替代 GetFiles/GetDirectories
        /// 2. 批量处理数据写入
        /// 3. 使用 Excel 快速模式减少刷新
        /// 4. 异常处理优化
        /// </remarks>
        private void ExportPathInfo(Worksheet masterWorksheet, string path)
        {
            try
            {
                // 检查目录是否存在
                if (!Directory.Exists(path))
                {
                    MessageBox.Show($"路径不存在: {path}");
                    return;
                }

                DirectoryInfo rootDir = new(path);

                // 设置Excel应用为快速模式
                ETExcelExtensions.SetAppFastMode();
                try
                {
                    int rowIndex = Convert.ToInt16(ThisAddIn.ExcelApplication.WorksheetFunction.CountA(masterWorksheet.Columns[1]));

                    // 预处理要导出的数据
                    List<FileSystemInfo> itemsToExport = [];

                    // 收集要导出的文件和文件夹
                    CollectItemsToExport(rootDir, itemsToExport);

                    // 限制项目数量，防止Excel崩溃
                    const int MAX_ITEMS = 20000;
                    if (itemsToExport.Count > MAX_ITEMS)
                    {
                        itemsToExport = itemsToExport.Take(MAX_ITEMS).ToList();
                        MessageBox.Show($"导出项目数量超过 {MAX_ITEMS}，将只导出前 {MAX_ITEMS} 个项目。", "导出限制", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }

                    // 批量写入Excel
                    BatchExportToExcel(masterWorksheet, itemsToExport, ref rowIndex);
                }
                finally
                {
                    // 恢复Excel应用为正常模式
                    ETExcelExtensions.SetAppNormalMode(true);
                }
            }
            catch (Exception ex)
            {
                throw new ETException($"导出路径信息时出错: {ex.Message}", ExcelOperationType.WorksheetOperation, ex);
            }
        }

        /// <summary>
        /// 收集要导出的文件和文件夹
        /// </summary>
        /// <param name="rootDir">根目录</param>
        /// <param name="itemsToExport">要导出的项目列表</param>
        private void CollectItemsToExport(DirectoryInfo rootDir, List<FileSystemInfo> itemsToExport)
        {
            const int MAX_ITEMS = 20000;

            try
            {
                // 添加当前目录
                if (checkBox导入文件_文件夹.Checked)
                {
                    itemsToExport.Add(rootDir);
                }

                // 添加当前目录中的文件
                if (checkBox导入文件_文件.Checked)
                {
                    foreach (string file in Directory.EnumerateFiles(rootDir.FullName))
                    {
                        if (itemsToExport.Count >= MAX_ITEMS)
                            return;

                        itemsToExport.Add(new FileInfo(file));
                    }
                }

                // 递归处理子目录
                if (checkBox含子目录.Checked)
                {
                    foreach (string subDir in Directory.EnumerateDirectories(rootDir.FullName))
                    {
                        if (itemsToExport.Count >= MAX_ITEMS)
                            return;

                        CollectItemsToExport(new DirectoryInfo(subDir), itemsToExport);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new ETException($"收集导出项目时出错: {ex.Message}", "文件枚举操作", ex);
            }
        }

        /// <summary>
        /// 批量导出数据到Excel
        /// </summary>
        /// <param name="worksheet">工作表</param>
        /// <param name="items">要导出的项目</param>
        /// <param name="rowIndex">行索引</param>
        private void BatchExportToExcel(Worksheet worksheet, List<FileSystemInfo> items, ref int rowIndex)
        {
            try
            {
                // 准备数据数组，一次性填充
                int itemCount = items.Count;
                object[,] data = new object[itemCount, 6];

                for (int i = 0; i < itemCount; i++)
                {
                    FileSystemInfo item = items[i];
                    bool isFile = item is FileInfo;

                    // 准备数据
                    data[i, 0] = item.FullName;
                    data[i, 1] = isFile ? "文件" : "文件夹";
                    data[i, 2] = isFile ? ((FileInfo)item).Extension : string.Empty;
                    string fileName = checkBox文件名半角化.Checked ? item.Name.ToDBC().ToEn() : item.Name;
                    data[i, 3] = fileName;
                    data[i, 4] = Path.GetFileNameWithoutExtension(fileName);
                    data[i, 5] = item.LastWriteTime;
                }

                // 一次性填充数据
                rowIndex++;
                Range dataRange = worksheet.Range[
                    worksheet.Cells[rowIndex, 1],
                    worksheet.Cells[rowIndex + itemCount - 1, 6]
                ];
                dataRange.Value = data;

                // 更新行索引
                rowIndex += itemCount - 1;
            }
            catch (Exception ex)
            {
                throw new ETException($"批量导出数据时出错: {ex.Message}", ExcelOperationType.RangeOperation, ex);
            }
        }

        /// <summary>
        /// 设置主工作表的列格式
        /// </summary>
        /// <param name="worksheet">要设置的工作表</param>
        private void SetupMasterSheetColumns(Worksheet worksheet)
        {
            try
            {
                // 设置列宽
                worksheet.Columns[1].ColumnWidth = 30;
                worksheet.Columns[2].ColumnWidth = 15;
                worksheet.Columns[3].ColumnWidth = 10;
                worksheet.Columns[4].ColumnWidth = 30;
                worksheet.Columns[5].ColumnWidth = 30;
                worksheet.Columns[6].ColumnWidth = 20;

                // 设置列标题
                worksheet.Cells[1, 1].Value = "路径";
                worksheet.Cells[1, 2].Value = "类型";
                worksheet.Cells[1, 3].Value = "后缀";
                worksheet.Cells[1, 4].Value = "文件名/文件夹名";
                worksheet.Cells[1, 5].Value = "文件名/文件夹名(不含后缀)";
                worksheet.Cells[1, 6].Value = "最后修改时间";
            }
            catch (Exception ex)
            {
                throw new ETException($"设置列格式时出错: {ex.Message}", ExcelOperationType.FormatOperation, ex);
            }
        }

        /// <summary>
        /// 导入文件添加文件夹菜单项点击事件处理
        /// </summary>
        public void 导入文件_添加文件夹ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(txtPath检查文件.Text) && Directory.Exists(txtPath检查文件.Text.Trim()))
                    folderBrowserDialog1.SelectedPath = txtPath检查文件.Text.Trim();
                else
                    folderBrowserDialog1.SelectedPath = @"D:\";

                DialogResult dialogResult = folderBrowserDialog1.ShowDialog();
                if (dialogResult == DialogResult.OK || dialogResult == DialogResult.Yes)
                    textBox导入文件_目录.AppendTextInvoke(folderBrowserDialog1.SelectedPath);
            }
            catch (Exception ex)
            {
                throw new ETException($"添加文件夹时出错: {ex.Message}", "文件夹操作", ex);
            }
        }

        #endregion 导入文件名
    }
}