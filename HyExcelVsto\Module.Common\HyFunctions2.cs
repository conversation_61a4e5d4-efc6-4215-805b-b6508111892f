﻿/*
 * ============================================================================
 * 功能模块：Excel通用功能函数库 - 扩展功能
 * ============================================================================
 * 
 * 模块作用：提供Excel操作的扩展功能函数
 * 
 * 主要功能：
 * - 公式转换：将Excel公式转换为数值
 * - 数据处理：提供数据格式转换功能
 * 
 * 注意事项：
 * - 此文件是HyFunctions类的部分类扩展
 * - 提供特定的Excel操作功能
 * ============================================================================
 */

using ET;
using Microsoft.Office.Core;
using Microsoft.Office.Interop.Excel;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// Excel通用功能函数库 - 扩展功能部分
    /// </summary>
    /// <remarks>
    /// HyFunctions类的部分类扩展，提供公式转换等功能
    /// </remarks>
    partial class HyFunctions
    {
        /// <summary>
        /// 公式转数值按钮点击事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="cancelDefault">是否取消默认操作</param>
        /// <remarks>
        /// 将选中区域的公式转换为数值，并恢复Excel正常模式
        /// </remarks>
        public static void Button公式转数值_Click(CommandBarButton sender, ref bool cancelDefault)
        {
            // 获取当前选中的范围
            Range selectionRange = ETExcelExtensions.GetSelectionRange();
            
            // 将公式转换为数值
            selectionRange.ConvertToNumeric(true);
            
            // 恢复Excel正常模式
            ETExcelExtensions.SetAppNormalMode(true);
        }
    }
}