using System;
using System.IO;

namespace HyAssistant.ChinaTowerDownload.Models
{
    /// <summary>
    /// 表示照片的精简信息，用于在下载过程中跟踪照片状态和基本属性。
    /// </summary>
    public class PhotoInfoExcerpt
    {
        /// <summary>
        /// 获取或设置照片所属站点的唯一标识符，关联 <c>StationInfo.STATION_ID</c>。
        /// </summary>
        public string station_id { get; set; }

        /// <summary>
        /// 获取或设置照片所属站点的名称。
        /// </summary>
        public string station_name { get; set; }

        /// <summary>
        /// 获取或设置照片的完整URL地址。
        /// </summary>
        public string url { get; set; }

        /// <summary>
        /// 获取或设置照片的唯一标识符。
        /// </summary>
        public string photoId { get; set; }

        /// <summary>
        /// 获取或设置照片在服务器上的文件名。
        /// </summary>
        public string serverfileName { get; set; }

        /// <summary>
        /// 获取或设置照片的创建时间，以Unix时间戳格式表示（自1970年1月1日以来的秒数）。
        /// </summary>
        public long createTime { get; set; }

        /// <summary>
        /// 获取或设置照片的下载时间，以Unix时间戳格式表示。
        /// <para>特殊值说明：</para>
        /// <para>• 0：表示照片尚未下载（默认状态）</para>
        /// <para>• -1：表示照片下载失败</para>
        /// <para>• 正整数：表示成功下载的Unix时间戳</para>
        /// </summary>
        public long downloadTime { get; set; }

        /// <summary>
        /// 获取或设置照片在数据库中的唯一标识符，作为自增主键使用。
        /// </summary>
        public long ID { get; set; }

        /// <summary>
        /// 初始化 <see cref="PhotoInfoExcerpt"/> 类的新实例，并将 <see cref="downloadTime"/> 设置为0，表示照片处于未下载状态。
        /// </summary>
        public PhotoInfoExcerpt()
        {
            downloadTime = 0; // 默认未下载
        }

        /// <summary>
        /// 使用指定的照片信息初始化 <see cref="PhotoInfoExcerpt"/> 类的新实例，并将下载时间初始化为0。
        /// </summary>
        /// <param name="stationId">照片所属站点的唯一标识符</param>
        /// <param name="stationName">照片所属站点的显示名称</param>
        /// <param name="photoUrl">照片的完整网络地址</param>
        /// <param name="photoId">照片在系统中的唯一标识符</param>
        /// <param name="serverFileName">照片在服务器上的原始文件名</param>
        /// <param name="createTime">照片创建的Unix时间戳（自1970年1月1日以来的秒数）</param>
        public PhotoInfoExcerpt(string stationId, string stationName, string photoUrl, 
            string photoId, string serverFileName, long createTime)
        {
            station_id = stationId;
            station_name = stationName;
            url = photoUrl;
            this.photoId = photoId;
            serverfileName = serverFileName;
            this.createTime = createTime;
            downloadTime = 0;
        }

        /// <summary>
        /// 获取一个值，指示照片是否已成功下载。
        /// 当 <see cref="downloadTime"/> 大于0时返回 <c>true</c>。
        /// </summary>
        public bool IsDownloaded => downloadTime > 0;

        /// <summary>
        /// 获取一个值，指示照片下载是否失败。
        /// 当 <see cref="downloadTime"/> 等于-1时返回 <c>true</c>。
        /// </summary>
        public bool IsDownloadFailed => downloadTime == -1;

        /// <summary>
        /// 获取一个值，指示照片是否处于待下载状态。
        /// 当 <see cref="downloadTime"/> 等于0时返回 <c>true</c>。
        /// </summary>
        public bool IsPendingDownload => downloadTime == 0;

        /// <summary>
        /// 获取照片的文件扩展名（包含点号，如".jpg"）。
        /// <para>该属性通过解析 <see cref="url"/> 获取文件扩展名，如果解析失败则返回".jpg"作为默认值。</para>
        /// </summary>
        public string FileExtension
        {
            get
            {
                try
                {
                    return Path.GetExtension(new Uri(url).LocalPath);
                }
                catch
                {
                    return ".jpg"; // 默认扩展名
                }
            }
        }

        /// <summary>
        /// 根据照片的创建时间和唯一标识符生成本地文件名。
        /// <para>文件名格式：<c>yyyyMMdd_photoId.扩展名</c></para>
        /// <para>如果创建时间解析失败，则仅使用 <see cref="photoId"/> 和 <see cref="FileExtension"/> 生成文件名。</para>
        /// </summary>
        /// <returns>格式化的本地文件名</returns>
        public string GenerateLocalFileName()
        {
            try
            {
                var timeFormat = DateTimeOffset.FromUnixTimeSeconds(createTime).DateTime;
                var fileName = timeFormat.ToString("yyyyMMdd") + "_" + photoId;
                return fileName + FileExtension;
            }
            catch
            {
                return $"{photoId}{FileExtension}";
            }
        }

        /// <summary>
        /// 返回照片信息的字符串表示，包含照片ID、站点名称和当前下载状态。
        /// <para>格式：<c>照片[photoId] 站点名称 - 状态</c></para>
        /// <para>状态可能为：已下载、下载失败、待下载</para>
        /// </summary>
        /// <returns>包含照片关键信息的格式化字符串</returns>
        public override string ToString()
        {
            var status = IsDownloaded ? "已下载" : (IsDownloadFailed ? "下载失败" : "待下载");
            return $"照片[{photoId}] {station_name} - {status}";
        }

        /// <summary>
        /// 确定当前实例是否与另一个对象相等，基于 <see cref="station_id"/> 和 <see cref="serverfileName"/> 的值进行比较。
        /// </summary>
        /// <param name="obj">要与当前实例进行比较的对象</param>
        /// <returns>如果 <paramref name="obj"/> 是 <see cref="PhotoInfoExcerpt"/> 且其 <see cref="station_id"/> 和 <see cref="serverfileName"/> 与当前实例相同（不区分大小写），则为 <c>true</c>；否则为 <c>false</c>。</returns>
        public override bool Equals(object obj)
        {
            if (obj is PhotoInfoExcerpt other)
            {
                return string.Equals(station_id, other.station_id, StringComparison.OrdinalIgnoreCase) &&
                       string.Equals(serverfileName, other.serverfileName, StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }

        /// <summary>
        /// 返回当前实例的哈希代码，基于 <see cref="station_id"/> 和 <see cref="serverfileName"/> 的值计算。
        /// </summary>
        /// <returns>当前实例的哈希代码</returns>
        public override int GetHashCode()
        {
            unchecked
            {
                int hash = 17;
                hash = hash * 23 + (station_id?.GetHashCode() ?? 0);
                hash = hash * 23 + (serverfileName?.GetHashCode() ?? 0);
                return hash;
            }
        }
    }
}
