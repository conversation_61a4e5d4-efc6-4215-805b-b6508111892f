/*
 * ============================================================================
 * 功能模块：ChinaTower配置管理
 * ============================================================================
 * 
 * 模块作用：定义和管理ChinaTower下载功能的相关配置参数
 * 
 * 主要功能：
 * - 存储下载配置参数
 * - 提供配置参数的默认值
 * - 支持配置参数的序列化和反序列化
 * 
 * 执行逻辑：
 * 1. 定义配置项属性
 * 2. 设置默认配置值
 * 3. 提供配置验证方法
 * 
 * 注意事项：
 * - 配置项应根据实际业务需求进行调整
 * - 敏感信息如密码应加密存储
 * ============================================================================
 */

using System;

namespace HyAssistant.ChinaTowerDownload.Configuration
{
    /// <summary>
    /// ChinaTower下载配置类，用于存储和管理下载相关配置参数
    /// </summary>
    public class ChinaTowerConfig
    {
        /// <summary>
        /// API基础地址
        /// </summary>
        public string BaseUrl { get; set; }

        /// <summary>
        /// 认证Token
        /// </summary>
        public string AuthToken { get; set; }

        /// <summary>
        /// 下载超时时间（毫秒）
        /// </summary>
        public int Timeout { get; set; } = 30000;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetries { get; set; } = 3;

        /// <summary>
        /// 下载目录路径
        /// </summary>
        public string DownloadPath { get; set; }

        /// <summary>
        /// 是否启用并发下载
        /// </summary>
        public bool EnableParallelDownload { get; set; } = true;

        /// <summary>
        /// 并发下载线程数
        /// </summary>
        public int ParallelThreadCount { get; set; } = 5;

        /// <summary>
        /// 验证当前配置实例的所有必要参数是否都已正确设置且有效。
        /// </summary>
        /// <returns>如果所有必要的配置项（如BaseUrl、AuthToken、DownloadPath、Timeout、MaxRetries、ParallelThreadCount）均有效，则返回 <c>true</c>；否则返回 <c>false</c>。</returns>
        public bool IsValid()
        {
            // 验证必要配置项是否已设置
            return !string.IsNullOrEmpty(BaseUrl) && 
                   !string.IsNullOrEmpty(AuthToken) && 
                   !string.IsNullOrEmpty(DownloadPath) &&
                   Timeout > 0 && 
                   MaxRetries >= 0 &&
                   ParallelThreadCount > 0;
        }
    }
}