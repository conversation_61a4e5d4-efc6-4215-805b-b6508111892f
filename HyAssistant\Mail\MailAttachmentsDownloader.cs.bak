﻿using ET;
using LiteDB;
using MailKit;
using MailKit.Net.Imap;
using MailKit.Search;
using MimeKit;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HyAssistant
{
    /// <summary>
    /// 邮件附件下载器窗体类，用于管理邮件附件的自动下载和分类存储
    /// </summary>
    public partial class MailAttachmentsDownloader : Form
    {
        /// <summary>
        /// 配置文件路径
        /// </summary>
        string IniFilePath = ETConfig.GetConfigDirectory("mail.ini");

        /// <summary>
        /// INI配置文件操作实例
        /// </summary>
        ETIniFile iniFile;

        /// <summary>
        /// 当前下载任务
        /// </summary>
        Task _downloadTask;

        /// <summary>
        /// 取消令牌源，用于控制任务的取消
        /// </summary>
        CancellationTokenSource cancellationTokenSource;

        /// <summary>
        /// 标记当前是否正在执行任务
        /// </summary>
        bool isBusy;

        /// <summary>
        /// 操作超时时间（毫秒）
        /// </summary>
        int timeoutMilliseconds = 60 * 1000;

        /// <summary>
        /// 日志缓冲区，用于批量更新UI
        /// </summary>
        readonly StringBuilder _logBuffer = new StringBuilder(8192);

        /// <summary>
        /// 日志缓冲区锁对象
        /// </summary>
        readonly object _logBufferLock = new object();

        /// <summary>
        /// 日志更新计时器
        /// </summary>
        System.Windows.Forms.Timer _logUpdateTimer;

        /// <summary>
        /// 日志缓冲区最大条目数，超过此数量将触发UI更新
        /// </summary>
        const int LogBufferThreshold = 20;

        /// <summary>
        /// 当前缓冲区中的日志条目数
        /// </summary>
        int _logEntryCount = 0;

        /// <summary>
        /// 添加邮件分类规则类
        /// </summary>
        public class MailCategoryRule
        {
            public string Name { get; set; }
            public List<string> FromEmails { get; set; } = new List<string>();
            public List<string> SubjectKeywords { get; set; } = new List<string>();
            public string TargetFolder { get; set; }
            public int Priority { get; set; }

            public bool MatchesEmail(string from, string subject)
            {
                bool fromMatch = FromEmails.Count == 0 ||
                                 FromEmails.Any(email => from.IndexOf(email, StringComparison.OrdinalIgnoreCase) >= 0);

                bool subjectMatch = SubjectKeywords.Count == 0 ||
                                    SubjectKeywords.Any(keyword => subject.IndexOf(keyword, StringComparison.OrdinalIgnoreCase) >= 0);

                // 如果FromEmails为空，则只检查subject
                // 如果SubjectKeywords为空，则只检查from
                // 如果两者都不为空，则两者都要匹配
                if (FromEmails.Count == 0)
                    return subjectMatch;

                if (SubjectKeywords.Count == 0)
                    return fromMatch;

                return fromMatch && subjectMatch;
            }
        }

        /// <summary>
        /// 添加邮件分类配置管理类
        /// </summary>
        public class MailCategoryConfig
        {
            readonly string _configPath;
            readonly List<MailCategoryRule> _rules = new List<MailCategoryRule>();
            string _defaultCategory = "邮件";
            bool _enableCategorization = true;

            public MailCategoryConfig(string configPath)
            {
                _configPath = configPath;
                LoadConfig();
            }

            public void LoadConfig()
            {
                _rules.Clear();

                if (!File.Exists(_configPath))
                {
                    // 如果配置文件不存在，使用默认值
                    _defaultCategory = "邮件";
                    _enableCategorization = true;
                    return;
                }

                ETIniFile iniFile = new ETIniFile(_configPath);

                // 读取通用配置
                _defaultCategory = iniFile.IniReadValue("General", "DefaultCategory") ?? "邮件";
                _enableCategorization = bool.Parse(iniFile.IniReadValue("General", "EnableCategorization") ?? "true");

                // 读取所有分组
                string[] sections = GetIniSections(_configPath);
                foreach (string section in sections)
                {
                    if (section.Equals("General", StringComparison.OrdinalIgnoreCase))
                        continue;

                    MailCategoryRule rule = new MailCategoryRule
                    {
                        Name = iniFile.IniReadValue(section, "Name") ?? section,
                        TargetFolder = iniFile.IniReadValue(section, "TargetFolder") ?? _defaultCategory,
                        Priority = int.Parse(iniFile.IniReadValue(section, "Priority") ?? "0")
                    };

                    // 解析FromEmails
                    string fromEmails = iniFile.IniReadValue(section, "FromEmails") ?? string.Empty;
                    if (!string.IsNullOrWhiteSpace(fromEmails))
                    {
                        rule.FromEmails.AddRange(fromEmails.Split(',').Select(e => e.Trim()));
                    }

                    // 解析SubjectKeywords
                    string subjectKeywords = iniFile.IniReadValue(section, "SubjectKeywords") ?? string.Empty;
                    if (!string.IsNullOrWhiteSpace(subjectKeywords))
                    {
                        rule.SubjectKeywords.AddRange(subjectKeywords.Split(',').Select(k => k.Trim()));
                    }

                    _rules.Add(rule);
                }

                // 按优先级排序
                _rules.Sort((a, b) => b.Priority.CompareTo(a.Priority));
            }

            // 获取INI文件中的所有节名称
            string[] GetIniSections(string iniFilePath)
            {
                // 由于IniFile类没有提供获取所有节的方法，我们自己实现一个简单的解析
                if (!File.Exists(iniFilePath))
                    return new string[0];

                List<string> sections = new List<string>();
                try
                {
                    string[] lines = File.ReadAllLines(iniFilePath);
                    foreach (string line in lines)
                    {
                        string trimmedLine = line.Trim();
                        if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]") && trimmedLine.Length > 2)
                        {
                            string section = trimmedLine.Substring(1, trimmedLine.Length - 2);
                            sections.Add(section);
                        }
                    }
                }
                catch
                {
                    // 忽略读取错误
                }
                return sections.ToArray();
            }

            public string GetCategory(MimeMessage message, string yearPrefix)
            {
                if (message == null || !_enableCategorization)
                    return Path.Combine(yearPrefix, _defaultCategory);

                string from = message.From?.Mailboxes?.FirstOrDefault()?.Address ?? string.Empty;
                string subject = message.Subject ?? string.Empty;

                if (string.IsNullOrWhiteSpace(from))
                    return Path.Combine(yearPrefix, _defaultCategory);

                // 按优先级依次检查规则
                foreach (MailCategoryRule rule in _rules)
                {
                    if (rule.MatchesEmail(from, subject))
                    {
                        return Path.Combine(yearPrefix, rule.TargetFolder);
                    }
                }

                return Path.Combine(yearPrefix, _defaultCategory);
            }
        }

        /// <summary>
        /// 添加类级别的字段
        /// </summary>
        MailCategoryConfig _mailCategoryConfig;
        string _mailCategoryConfigPath;

        /// <summary>
        /// 启动邮件下载任务
        /// </summary>
        /// <returns>表示异步操作的任务</returns>
        async Task StartDownloadMail()
        {
            if (isBusy)
            {
                WriteLog("已有任务正在进行中...");
                return;
            }

            try
            {
                // 使用Task.Run将整个下载过程放在后台线程执行
                _downloadTask = Task.Run(async () =>
                {
                    try
                    {
                        await DownloadMailsAsync().ConfigureAwait(false);
                    }
                    catch (Exception ex)
                    {
                        await Task.Run(() => WriteLog($"下载过程发生错误: {ex.Message}")).ConfigureAwait(false);
                    }
                    finally
                    {
                        await Task.Run(() => SetBusyState(false)).ConfigureAwait(false);
                    }
                });

                // 等待任务启动，但不等待完成，使用ConfigureAwait(false)避免死锁
                await Task.Delay(100).ConfigureAwait(false); // 给任务一点启动时间
            }
            catch (Exception ex)
            {
                WriteLog($"启动下载任务时发生错误: {ex.Message}");
                SetBusyState(false);
            }
        }

        /// <summary>
        /// 尝试加载配置信息
        /// </summary>
        /// <param name="config">输出参数，成功时包含加载的配置信息</param>
        /// <returns>如果成功加载配置返回true，否则返回false</returns>
        bool TryLoadConfiguration(out Configuration config)
        {
            config = null; // 初始化为null，只有在成功读取所有配置时才会创建实例

            try
            {
                string imapHost = iniFile.GetValue("ReceiveMail", "ImapHost");
                int imapPort = Convert.ToInt32(iniFile.GetValue("ReceiveMail", "ImapPort"));
                string username = iniFile.GetValue("ReceiveMail", "Username");
                string password = iniFile.GetValue("ReceiveMail", "Password");
                string mailDirectory = iniFile.GetValue("ReceiveMail", "Directory");
                string dbPath = iniFile.GetValue("ReceiveMail", "UidLiteDB");
                string deliveredAfterRaw = iniFile.GetValue("ReceiveMail", "DeliveredAfter");
                string skipFoldersRaw = iniFile.GetValue("Folder", "SkipFolders");
                string destFolderName = iniFile.GetValue("MoveMail", "DestinationFolder");
                string sourceFolderName = iniFile.GetValue("MoveMail", "SourceFolder");

                // 验证读取的配置参数
                if (string.IsNullOrWhiteSpace(imapHost) ||
                    imapPort <= 0 ||
                    string.IsNullOrWhiteSpace(username) ||
                    string.IsNullOrWhiteSpace(password) ||
                    string.IsNullOrWhiteSpace(mailDirectory) ||
                    string.IsNullOrWhiteSpace(dbPath))
                {
                    WriteLog("配置项有缺失，请检查配置文件。");
                    return false;
                }

                DateTime.TryParse(deliveredAfterRaw, out DateTime deliveredAfter);
                string[] skipFolders = skipFoldersRaw.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);

                // 创建Configuration实例并赋值
                config = new Configuration
                {
                    ImapHost = imapHost,
                    ImapPort = imapPort,
                    Username = username,
                    Password = password,
                    MailDirectory = mailDirectory,
                    DbPath = dbPath,
                    DeliveredAfter = deliveredAfter,
                    SkipFolders = skipFolders,
                    DestinationFolderName = destFolderName,
                    SourceFolderName = sourceFolderName
                };

                return true;
            }
            catch (Exception ex)
            {
                WriteLog($"读取配置文件时出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 执行邮件下载的主要异步任务
        /// </summary>
        /// <returns>表示异步操作的任务</returns>
        public async Task DownloadMailsAsync()
        {
            if (isBusy)
            {
                await Task.Run(() => WriteLog("已有任务正在进行中...")).ConfigureAwait(true);
                return;
            }

            await Task.Run(() => SetBusyState(true)).ConfigureAwait(true);

            if (!TryLoadConfiguration(out Configuration configuration))
            {
                await Task.Run(() => SetBusyState(false)).ConfigureAwait(true);
                return;
            }

            cancellationTokenSource = new CancellationTokenSource(timeoutMilliseconds);

            // 使用匿名方法来处理取消事件，在UI线程上更新状态
            cancellationTokenSource.Token.Register(() =>
            {
                // 保存Task引用但不等待，让它自己完成
                Task _ = Task.Run(() =>
                {
                    try
                    {
                        SetBusyState(false);
                    }
                    catch (Exception ex)
                    {
                        // 记录异常但不抛出
                        System.Diagnostics.Debug.WriteLine($"取消操作时出错: {ex.Message}");
                    }
                });
            });

            using (ImapClient client = new ImapClient())
            {
                try
                {
                    await ConnectToImapServerAsync(client, configuration, cancellationTokenSource.Token).ConfigureAwait(true);
                    if (checkBoxAutoMoveMail.Checked)
                    {
                        await MoveEmailsFromFolderAsync(client, configuration, cancellationTokenSource.Token).ConfigureAwait(true);
                    }

                    await DownloadEmailsFromFoldersAsync(client, configuration, cancellationTokenSource.Token).ConfigureAwait(true);
                    await Task.Run(() => Program.mainForm.textBoxLog.WriteLog($"{DateTime.Now:yyyy-MM-dd HH:mm:ss} 邮件助手：所有邮件处理完成")).ConfigureAwait(true);
                }
                catch (OperationCanceledException)
                {
                    if (cancellationTokenSource.IsCancellationRequested)
                    {
                        await Task.Run(() => WriteLog("操作被用户取消。")).ConfigureAwait(true);
                    }
                }
                catch (Exception ex)
                {
                    await Task.Run(() => WriteLog($"发生错误: {ex.Message}")).ConfigureAwait(true);
                }
                finally
                {
                    cancellationTokenSource.Dispose();
                    await Task.Run(() => SetBusyState(false)).ConfigureAwait(true);
                }
            }
        }

        /// <summary>
        /// 设置窗体的忙碌状态
        /// </summary>
        /// <param name="isBusy">是否处于忙碌状态</param>
        void SetBusyState(bool isBusy)
        {
            if (buttonStart.InvokeRequired)
            {
                buttonStart.BeginInvoke(new Action(() => SetBusyState(isBusy)));
                return;
            }

            this.isBusy = isBusy;
            buttonStart.Text = isBusy ? "停止接收" : "接收邮件";
        }

        /// <summary>
        /// 连接到IMAP服务器
        /// </summary>
        /// <param name="client">IMAP客户端实例</param>
        /// <param name="config">配置信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>表示异步操作的任务</returns>
        async Task ConnectToImapServerAsync(
            ImapClient client,
            Configuration config,
            CancellationToken cancellationToken)
        {
            try
            {
                // 连接到IMAP服务器
                WriteLog($"正在连接到 IMAP 服务器 {config.ImapHost}:{config.ImapPort}...");
                await client.ConnectAsync(config.ImapHost, config.ImapPort, true, cancellationToken).ConfigureAwait(true);
                WriteLog("连接成功。");

                // 使用配置中提供的用户名和密码进行登录
                WriteLog($"正在登录至服务器，用户名: {config.Username}...");
                await client.AuthenticateAsync(config.Username, config.Password, cancellationToken).ConfigureAwait(true);
                WriteLog("登录成功。");
            }
            catch (Exception ex)
            {
                // 将异常信息记录到日志中，并重新抛出异常以便上层处理
                WriteLog($"无法连接到 IMAP 服务器或认证失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 在邮件文件夹之间移动邮件
        /// </summary>
        /// <param name="client">IMAP客户端实例</param>
        /// <param name="config">配置信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>表示异步操作的任务</returns>
        async Task MoveEmailsFromFolderAsync(
            ImapClient client,
            Configuration config,
            CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(config.SourceFolderName) ||
                string.IsNullOrWhiteSpace(config.DestinationFolderName))
            {
                WriteLog("源文件夹或目标文件夹名称未配置，跳过移动邮件步骤。");
                return;
            }

            IMailFolder sourceFolder = null;
            IMailFolder destinationFolder = null;

            try
            {
                sourceFolder = await client.GetFolderAsync(config.SourceFolderName, cancellationToken).ConfigureAwait(true);
                destinationFolder = await client.GetFolderAsync(config.DestinationFolderName, cancellationToken).ConfigureAwait(true);

                // 打开源文件夹以读写模式，准备邮件搜索和移动
                if (!sourceFolder.IsOpen)
                    await sourceFolder.OpenAsync(FolderAccess.ReadWrite, cancellationToken).ConfigureAwait(true);

                // 根据配置来搜索邮件
                DateSearchQuery searchQuery = SearchQuery.DeliveredAfter(config.DeliveredAfter);
                if (sourceFolder.IsOpen) // 再次确认文件夹是否打开
                {
                    IList<UniqueId> uidsToMove = await sourceFolder.SearchAsync(searchQuery, cancellationToken).ConfigureAwait(true);
                    WriteLog($"准备移动 {uidsToMove.Count} 条邮件从 '{sourceFolder.Name}' 到 '{destinationFolder.Name}'");

                    // 如果源文件夹中有邮件可供移动，则执行移动操作
                    if (uidsToMove.Count > 0)
                    {
                        await sourceFolder.MoveToAsync(uidsToMove, destinationFolder, cancellationToken).ConfigureAwait(true);
                        WriteLog("邮件移动完成。");
                    }
                }
                else
                {
                    WriteLog("源文件夹在搜索操作前并没有正确打开。");
                }
            }
            catch (Exception ex)
            {
                WriteLog($"邮件操作过程中发生错误: {ex.Message}");
            }
            finally
            {
                // 无论操作是否成功，都关闭已打开的文件夹
                try
                {
                    if (sourceFolder?.IsOpen ?? false)
                        await sourceFolder.CloseAsync().ConfigureAwait(true);
                }
                catch (Exception ex)
                {
                    WriteLog($"尝试关闭源文件夹时发生错误: {ex.Message}");
                }

                try
                {
                    if (destinationFolder?.IsOpen ?? false)
                        await destinationFolder.CloseAsync().ConfigureAwait(true);
                }
                catch (Exception ex)
                {
                    WriteLog($"尝试关闭目标文件夹时发生错误: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 从邮件文件夹下载邮件
        /// </summary>
        /// <param name="client">IMAP客户端实例</param>
        /// <param name="config">配置信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>表示异步操作的任务</returns>
        async Task DownloadEmailsFromFoldersAsync(
            ImapClient client,
            Configuration config,
            CancellationToken cancellationToken)
        {
            // 连接数据库
            string connectionString = $"Filename={config.DbPath};Mode=Share";
            using (LiteDatabase db = new LiteDatabase(connectionString))
            {
                // 一次性获取所有已下载的UID并缓存到内存中
                ILiteCollection<BsonDocument> mailDownloadedUidCollection = db.GetCollection<BsonDocument>("MailDownloadedUid");
                HashSet<uint> downloadedUids = mailDownloadedUidCollection.FindAll()
                    .Select(x => (uint)x["uid"].AsInt32)
                    .ToHashSet();

                // 缓存发件箱文件夹名称集合
                HashSet<string> sendFolder = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
                {
                    "Sent Messages",
                    "※公司-Mail已发送"
                };

                // 缓存需要跳过的文件夹
                HashSet<string> skipFolders = new HashSet<string>(config.SkipFolders, StringComparer.OrdinalIgnoreCase);

                // 批量保存新下载的UID
                List<BsonDocument> newUidDocuments = new List<BsonDocument>();

                // 设置一次全局超时，而不是每封邮件都重置
                cancellationTokenSource.CancelAfter(timeoutMilliseconds * 10);

                // 统计处理结果
                int totalFolders = 0;
                int totalEmails = 0;
                int processedEmails = 0;

                foreach (IMailFolder folder in client.GetFolders(client.PersonalNamespaces[0]))
                {
                    // 跳过不需要下载的文件夹
                    if (skipFolders.Contains(folder.Name))
                        continue;

                    totalFolders++;

                    // 用于确定主目录名称
                    bool isSendMail = sendFolder.Contains(folder.Name);

                    await folder.OpenAsync(FolderAccess.ReadOnly, cancellationToken).ConfigureAwait(true);
                    WriteLog($"打开目录 {folder.Name}");

                    // 使用缓存的日期查询
                    DateSearchQuery query = SearchQuery.DeliveredAfter(config.DeliveredAfter);
                    IList<UniqueId> uids = await folder.SearchAsync(query, cancellationToken).ConfigureAwait(true);

                    int folderEmailCount = uids.Count;
                    totalEmails += folderEmailCount;

                    // 修改日志格式，添加空格使其更易读
                    WriteLog($"： {folderEmailCount} 封邮件", false);
                    if (!uids.Any())
                        continue;

                    // 每个文件夹只记录一次开始处理的日志
                    WriteLog($">> 开始处理 {folder.Name} 文件夹中的 {folderEmailCount} 封邮件");

                    // 批量处理计数器
                    int folderProcessed = 0;

                    foreach (UniqueId uid in uids)
                    {
                        cancellationTokenSource.Token.ThrowIfCancellationRequested();

                        // 使用内存中的HashSet检查，避免频繁数据库查询
                        if (downloadedUids.Contains(uid.Id))
                        {
                            folderProcessed++;
                            continue;
                        }

                        MimeMessage message = await folder.GetMessageAsync(uid, cancellationToken).ConfigureAwait(true);

                        if (message.Date.Year < 2000)
                        {
                            folderProcessed++;
                            continue;
                        }

                        // 减少日志记录频率，只记录每10封邮件的进度或最后一封
                        bool isLastEmail = folderProcessed == folderEmailCount - 1;
                        bool shouldLogProgress = folderProcessed % 10 == 0 || isLastEmail;

                        if (shouldLogProgress)
                        {
                            WriteLog($"  处理进度：{folderProcessed + 1}/{folderEmailCount} - {message.Subject}");
                        }

                        // 创建邮件分类目录的路径
                        string mailCategory = GetMailCategory(message);
                        string subject = GetMailSubject(message);

                        string mailPath = Path.Combine(
                            config.MailDirectory,
                            isSendMail ? "发件箱" : "收件箱",
                            mailCategory ?? "邮件附件",
                            $"{uid}-{message.Date:yyyyMMdd-HHmmss}--{subject}");

                        if (!Directory.Exists(mailPath))
                            Directory.CreateDirectory(mailPath); // 如果邮件分类目录不存在，则创建它

                        // 下载邮件附件
                        await DownloadAttachmentsAsync(message, mailPath, cancellationToken).ConfigureAwait(true);

                        // 将新下载的UID添加到内存集合和待保存列表中
                        downloadedUids.Add(uid.Id);
                        newUidDocuments.Add(new BsonDocument { { "uid", new BsonValue((int)uid.Id) } });

                        // 增加批量保存的阈值，从100增加到200，减少数据库操作
                        if (newUidDocuments.Count >= 200)
                        {
                            mailDownloadedUidCollection.InsertBulk(newUidDocuments);
                            newUidDocuments.Clear();
                        }

                        folderProcessed++;
                        processedEmails++;
                    }

                    // 每个文件夹处理完成后记录一次日志
                    WriteLog($"<< 完成处理 {folder.Name} 文件夹中的 {folderProcessed} 封邮件");
                }

                // 保存剩余的UID记录
                if (newUidDocuments.Count > 0)
                {
                    mailDownloadedUidCollection.InsertBulk(newUidDocuments);
                }

                // 记录总体处理结果
                WriteLog($"---------------------------------------------");
                WriteLog($"邮件下载完成：共处理 {totalFolders} 个文件夹，{totalEmails} 封邮件，新下载 {processedEmails} 封");
                WriteLog($"---------------------------------------------");
            }
        }

        /// <summary>
        /// 获取邮件主题，并处理文件名中的非法字符
        /// </summary>
        /// <param name="message">邮件消息</param>
        /// <returns>处理后的邮件主题</returns>
        string GetMailSubject(MimeMessage message)
        {
            if (string.IsNullOrEmpty(message.Subject))
                return "无主题";

            // 使用StringBuilder避免多次字符串拼接
            StringBuilder sb = new StringBuilder(message.Subject.Length);
            foreach (char c in message.Subject)
            {
                if (Array.IndexOf(_invalidFileNameChars, c) >= 0)
                    sb.Append('_');
                else
                    sb.Append(c);
            }
            return sb.ToString();
        }

        // 类级别的私有字段，缓存无效字符集合
        char[] _invalidFileNameChars = Path.GetInvalidFileNameChars();

        /// <summary>
        /// 下载邮件的所有附件
        /// </summary>
        /// <param name="message">邮件消息</param>
        /// <param name="directory">保存目录</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>表示异步操作的任务</returns>
        async Task DownloadAttachmentsAsync(MimeMessage message, string directory, CancellationToken cancellationToken)
        {
            // 先检查目录是否存在，避免重复检查
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // 统计附件处理结果，减少日志记录次数
            int totalAttachments = message.Attachments.Count();
            int existingCount = 0;
            int downloadedCount = 0;
            int errorCount = 0;
            List<string> errorFiles = new List<string>();

            foreach (MimeEntity attachment in message.Attachments)
            {
                string fileName = attachment.ContentDisposition?.FileName ?? attachment.ContentType.Name;

                try
                {
                    // 使用更高效的字符串处理方式
                    if (fileName != null)
                    {
                        StringBuilder sb = new StringBuilder(fileName.Length);
                        foreach (char c in fileName)
                        {
                            if (Array.IndexOf(_invalidFileNameChars, c) >= 0)
                                sb.Append('_');
                            else
                                sb.Append(c);
                        }
                        fileName = sb.ToString();
                    }
                    else
                    {
                        fileName = "未命名附件";
                    }

                    string filePath = Path.Combine(directory, fileName);

                    if (File.Exists(filePath))
                    {
                        existingCount++;
                        continue;
                    }

                    // 使用较大的缓冲区来减少IO操作次数
                    using (FileStream fileStream = new FileStream(
                        filePath,
                        FileMode.Create,
                        FileAccess.Write,
                        FileShare.None,
                        bufferSize: 81920,
                        useAsync: true))
                    {
                        if (attachment is MessagePart messagePart)
                            await messagePart.Message.WriteToAsync(fileStream, cancellationToken).ConfigureAwait(true);
                        else if (attachment is MimePart mimePart)
                            await mimePart.Content.DecodeToAsync(fileStream, cancellationToken).ConfigureAwait(true);
                    }

                    File.SetAttributes(filePath, FileAttributes.ReadOnly);
                    downloadedCount++;
                }
                catch (Exception)
                {
                    errorCount++;
                    errorFiles.Add(fileName);
                }
            }

            // 批量记录附件处理结果
            if (totalAttachments > 0)
            {
                StringBuilder logMsg = new StringBuilder();
                logMsg.AppendLine($"      附件处理完成: 共{totalAttachments}个");

                if (existingCount > 0)
                    logMsg.AppendLine($"      - 已存在: {existingCount}个");

                if (downloadedCount > 0)
                    logMsg.AppendLine($"      - 已下载: {downloadedCount}个");

                if (errorCount > 0)
                {
                    logMsg.AppendLine($"      - 下载失败: {errorCount}个");
                    foreach (string errorFile in errorFiles.Take(5)) // 最多显示5个错误文件
                    {
                        logMsg.AppendLine($"        * {errorFile}");
                    }
                    if (errorFiles.Count > 5)
                        logMsg.AppendLine($"        * 等{errorFiles.Count - 5}个文件");
                }

                WriteLog(logMsg.ToString(), false);
            }
        }

        /// <summary>
        /// 写入日志信息到日志文本框
        /// </summary>
        /// <param name="msg">日志消息</param>
        /// <param name="newLine">是否换行</param>
        void WriteLog(string msg, bool newLine = true)
        {
            // 将日志添加到缓冲区
            lock (_logBufferLock)
            {
                _logBuffer.Append(msg);
                if (newLine)
                {
                    _logBuffer.AppendLine();
                }
                _logEntryCount++;

                // 如果达到阈值或者是重要消息（包含特定关键词），则立即更新UI
                bool isImportantMessage = msg.Contains("错误") ||
                                         msg.Contains("失败") ||
                                         msg.Contains("完成") ||
                                         msg.StartsWith("正在连接") ||
                                         msg.StartsWith("准备移动");

                if (_logEntryCount >= LogBufferThreshold || isImportantMessage)
                {
                    FlushLogBuffer();
                }
            }
        }

        /// <summary>
        /// 将日志缓冲区内容刷新到UI
        /// </summary>
        void FlushLogBuffer()
        {
            if (_logEntryCount == 0)
                return;

            string logContent;
            lock (_logBufferLock)
            {
                logContent = _logBuffer.ToString();
                _logBuffer.Clear();
                _logEntryCount = 0;
            }

            if (string.IsNullOrEmpty(logContent))
                return;

            if (textBoxLog.InvokeRequired)
            {
                textBoxLog.BeginInvoke(new Action(() => UpdateLogTextBox(logContent)));
            }
            else
            {
                UpdateLogTextBox(logContent);
            }
        }

        /// <summary>
        /// 更新日志文本框内容
        /// </summary>
        /// <param name="logContent">日志内容</param>
        void UpdateLogTextBox(string logContent)
        {
            // 限制日志框内容长度，避免内存占用过大
            if (textBoxLog.TextLength > 50000)
            {
                textBoxLog.Clear();
                textBoxLog.AppendText("日志已清空以节省内存...\r\n");
            }
            textBoxLog.AppendText(logContent);
            // 滚动到最后一行
            textBoxLog.SelectionStart = textBoxLog.Text.Length;
            textBoxLog.ScrollToCaret();
        }

        /// <summary>
        /// 获取邮件的分类信息
        /// </summary>
        /// <param name="message">邮件消息</param>
        /// <returns>邮件分类</returns>
        string GetMailCategory(MimeMessage message)
        {
            if (message == null)
                return string.Empty;

            string mailYear = $"{message.Date.Year}年";
            return _mailCategoryConfig.GetCategory(message, mailYear);
        }

        /// <summary>
        /// 添加重新加载分类配置的方法
        /// </summary>
        void ReloadMailCategoryConfig()
        {
            _mailCategoryConfig.LoadConfig();
            WriteLog("已重新加载邮件分类配置");
        }

        /// <summary>
        /// 处理开始按钮点击事件
        /// </summary>
        async void ButtonStartClick(object sender, EventArgs e)
        {
            try
            {
                if (isBusy)
                {
                    if (cancellationTokenSource != null)
                    {
                        await Task.Run(() => cancellationTokenSource.Cancel()).ConfigureAwait(true);
                    }
                    return;
                }

                // 调用过程函数收取附件
                if (!string.IsNullOrWhiteSpace(textBoxLog.Text))
                {
                    await Task.Run(() => WriteLog(Environment.NewLine)).ConfigureAwait(true);
                }
                await StartDownloadMail().ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                WriteLog($"启动下载邮件时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 邮件配置类，用于存储邮件下载相关的配置信息
        /// </summary>
        public class Configuration
        {
            /// <summary>
            /// IMAP服务器地址
            /// </summary>
            public string ImapHost { get; set; }

            /// <summary>
            /// IMAP服务器端口
            /// </summary>
            public int ImapPort { get; set; }

            /// <summary>
            /// 用户名
            /// </summary>
            public string Username { get; set; }

            /// <summary>
            /// 密码
            /// </summary>
            public string Password { get; set; }

            /// <summary>
            /// 邮件保存目录
            /// </summary>
            public string MailDirectory { get; set; }

            /// <summary>
            /// 数据库文件路径
            /// </summary>
            public string DbPath { get; set; }

            /// <summary>
            /// 邮件投递时间过滤
            /// </summary>
            public DateTime DeliveredAfter { get; set; }

            /// <summary>
            /// 要跳过的文件夹列表
            /// </summary>
            public string[] SkipFolders { get; set; }

            /// <summary>
            /// 目标文件夹名称
            /// </summary>
            public string DestinationFolderName { get; set; }

            /// <summary>
            /// 源文件夹名称
            /// </summary>
            public string SourceFolderName { get; set; }
            // 可以根据需要添加更多配置属性
        }

        /// <summary>
        /// 处理自动下载定时器事件
        /// </summary>
        async void timerAutoDownload_Tick(object sender, EventArgs e)
        {
            try
            {
                if (!checkBoxAutoDownload.Checked || isBusy)
                    return;

                await Task.Run(() =>
                {
                    WriteLog($"{Environment.NewLine}=================================================");
                    WriteLog($"【定时自动下载  {DateTime.Now:yyyy/MM/dd HH:mm:ss}】");
                    WriteLog($"=================================================");
                    Program.mainForm.WriteLog($"{DateTime.Now:yyyy-MM-dd HH:mm:ss} 邮件助手：定时自动下载");
                }).ConfigureAwait(true);

                await StartDownloadMail().ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                WriteLog($"定时自动下载时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化邮件附件下载器窗体
        /// </summary>
        public MailAttachmentsDownloader()
        {
            InitializeComponent();
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

            // 在构造函数中初始化iniFile
            iniFile = new ETIniFile(IniFilePath);

            // 初始化邮件分类配置
            string configDir = Path.Combine(Path.GetDirectoryName(Application.ExecutablePath), "HyAssistant", "config");
            if (!Directory.Exists(configDir))
            {
                Directory.CreateDirectory(configDir);
            }
            _mailCategoryConfigPath = Path.Combine(configDir, "mail_category.ini");

            // 如果配置文件不存在，创建默认配置文件
            if (!File.Exists(_mailCategoryConfigPath))
            {
                CreateDefaultMailCategoryConfig(_mailCategoryConfigPath);
                WriteLog("已创建默认邮件分类配置文件");
            }

            _mailCategoryConfig = new MailCategoryConfig(_mailCategoryConfigPath);

            // 初始化日志更新计时器
            _logUpdateTimer = new System.Windows.Forms.Timer
            {
                Interval = 500 // 500毫秒更新一次日志
            };
            _logUpdateTimer.Tick += (s, e) => FlushLogBuffer();
            _logUpdateTimer.Start();
        }

        /// <summary>
        /// 处理窗体加载事件
        /// </summary>
        void frmMail_Load(object sender, EventArgs e)
        {
            //notifyIcon1.Visible = true;

            timerAutoDownload.Interval = 2 * 60 * 1000;
            timerAutoDownload.Enabled = true;
            timerAutoDownload_Tick(sender, e);
        }

        /// <summary>
        /// 处理自动下载复选框状态改变事件
        /// </summary>
        void checkBoxAutoDownload_CheckedChanged(object sender, EventArgs e)
        { timerAutoDownload.Enabled = checkBoxAutoDownload.Checked; }

        /// <summary>
        /// 处理停止按钮点击事件
        /// </summary>
        void buttonStop_Click(object sender, EventArgs e)
        {
            cancellationTokenSource.Cancel();
            checkBoxAutoDownload.Checked = false;
        }

        /// <summary>
        /// 处理窗体关闭事件
        /// </summary>
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // 在窗体关闭前刷新日志缓冲区
            FlushLogBuffer();

            // 停止日志更新计时器
            if (_logUpdateTimer != null)
            {
                _logUpdateTimer.Stop();
                _logUpdateTimer.Dispose();
            }

            // 取消关闭事件，最小化窗口并隐藏到系统托盘
            this.HideToTray(e);
        }

        /// <summary>
        /// 处理设置按钮点击事件，打开配置文件
        /// </summary>
        void buttonSetup_Click(object sender, EventArgs e)
        {
            // 创建上下文菜单
            ContextMenuStrip menu = new ContextMenuStrip();

            // 添加编辑主配置选项
            ToolStripMenuItem editMainConfig = new ToolStripMenuItem("编辑主配置");
            editMainConfig.Click += (s, args) => Process.Start(IniFilePath);
            menu.Items.Add(editMainConfig);

            // 添加编辑分类配置选项
            ToolStripMenuItem editCategoryConfig = new ToolStripMenuItem("编辑分类配置");
            editCategoryConfig.Click += (s, args) => Process.Start(_mailCategoryConfigPath);
            menu.Items.Add(editCategoryConfig);

            // 添加分隔符
            menu.Items.Add(new ToolStripSeparator());

            // 添加重新加载配置选项
            ToolStripMenuItem reloadConfig = new ToolStripMenuItem("重新加载配置");
            reloadConfig.Click += (s, args) => ReloadMailCategoryConfig();
            menu.Items.Add(reloadConfig);

            // 添加初始化邮件配置选项
            ToolStripMenuItem resetMailConfig = new ToolStripMenuItem("初始化邮件配置");
            resetMailConfig.Click += (s, args) =>
            {
                if (MessageBox.Show(
                    "确定要初始化邮件配置吗？这将覆盖当前的邮件配置文件。",
                    "确认初始化",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning) == DialogResult.Yes)
                {
                    ResetMailConfig();
                }
            };
            menu.Items.Add(resetMailConfig);

            // 添加重置分类配置选项
            ToolStripMenuItem resetCategoryConfig = new ToolStripMenuItem("初始化分类配置");
            resetCategoryConfig.Click += (s, args) =>
            {
                if (MessageBox.Show(
                    "确定要初始化/重置邮件分类配置吗？这将覆盖当前的配置文件。",
                    "确认重置",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning) == DialogResult.Yes)
                {
                    ResetMailCategoryConfig();
                }
            };
            menu.Items.Add(resetCategoryConfig);

            // 显示菜单
            menu.Show(button设置, new Point(0, button设置.Height));
        }

        /// <summary>
        /// 重置邮件主配置文件
        /// </summary>
        void ResetMailConfig()
        {
            try
            {
                // 备份原配置文件（如果存在）
                if (File.Exists(IniFilePath))
                {
                    string backupPath = $"{IniFilePath}.bak";
                    if (File.Exists(backupPath))
                        File.Delete(backupPath);
                    File.Move(IniFilePath, backupPath);
                    WriteLog($"原邮件配置文件已备份为: {backupPath}");
                }

                // 创建新的配置文件
                CreateDefaultMailConfig(IniFilePath);
                WriteLog("邮件配置已初始化");


                // 重新创建iniFile实例
                iniFile = new ETIniFile(IniFilePath);


                WriteLog("已重新加载邮件配置");

                // 打开配置文件进行编辑
                Process.Start("notepad.exe", IniFilePath);
            }
            catch (Exception ex)
            {
                WriteLog($"初始化邮件配置文件时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建默认的邮件主配置文件
        /// </summary>
        /// <param name="configPath">配置文件路径</param>
        void CreateDefaultMailConfig(string configPath)
        {
            using (StreamWriter writer = new StreamWriter(configPath))
            {
                writer.WriteLine(";==========================================================");
                writer.WriteLine("; 邮件配置文件");
                writer.WriteLine("; 此文件用于配置邮件自动下载功能的基本设置");
                writer.WriteLine(";==========================================================");
                writer.WriteLine();

                writer.WriteLine("[ReceiveMail]");
                writer.WriteLine("DeliveredAfter=2023/01/01");
                writer.WriteLine("Directory=E:\\邮件自动存放");
                writer.WriteLine("UidLiteDB=E:\\邮件自动存放\\ReceiveMailAttachmentsUid.db");
                writer.WriteLine("ImapHost=imap.example.com");
                writer.WriteLine("ImapPort=993");
                writer.WriteLine("Username=<EMAIL>");
                writer.WriteLine("Password=your_password_or_app_password");
                writer.WriteLine();

                writer.WriteLine("[Folder]");
                writer.WriteLine("ShowFolderList=false");
                writer.WriteLine("SkipFolders=Drafts;Deleted Messages;Junk;Spam");
                writer.WriteLine();

                writer.WriteLine("[MoveMail]");
                writer.WriteLine("DestinationFolder=INBOX");
                writer.WriteLine("SourceFolder=");
            }
        }

        /// <summary>
        /// 重置邮件分类配置文件
        /// </summary>
        void ResetMailCategoryConfig()
        {
            try
            {
                // 备份原配置文件（如果存在）
                if (File.Exists(_mailCategoryConfigPath))
                {
                    string backupPath = $"{_mailCategoryConfigPath}.bak";
                    if (File.Exists(backupPath))
                        File.Delete(backupPath);
                    File.Move(_mailCategoryConfigPath, backupPath);
                    WriteLog($"原配置文件已备份为: {backupPath}");
                }

                // 创建新的配置文件
                CreateDefaultMailCategoryConfig(_mailCategoryConfigPath);
                WriteLog("邮件分类配置已重置");

                // 重新加载配置
                _mailCategoryConfig.LoadConfig();
                WriteLog("已重新加载邮件分类配置");

                // 打开配置文件进行编辑
                Process.Start("notepad.exe", _mailCategoryConfigPath);
            }
            catch (Exception ex)
            {
                WriteLog($"重置配置文件时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建默认的邮件分类配置文件
        /// </summary>
        /// <param name="configPath">配置文件路径</param>
        void CreateDefaultMailCategoryConfig(string configPath)
        {
            using (StreamWriter writer = new StreamWriter(configPath))
            {
                writer.WriteLine(";==========================================================");
                writer.WriteLine("; 邮件分类规则配置文件");
                writer.WriteLine("; 此文件用于配置邮件的自动分类规则");
                writer.WriteLine("; 每个规则由一个节（如[Rule1]）定义，包含以下属性：");
                writer.WriteLine(";   Name: 规则名称");
                writer.WriteLine(";   FromEmails: 发件人邮箱列表，用逗号分隔，留空表示不限制");
                writer.WriteLine(";   SubjectKeywords: 主题关键词列表，用逗号分隔，留空表示不限制");
                writer.WriteLine(";   TargetFolder: 目标文件夹名称（会自动加上年份前缀）");
                writer.WriteLine(";   Priority: 规则优先级，数字越大优先级越高");
                writer.WriteLine(";==========================================================");
                writer.WriteLine();

                writer.WriteLine("[General]");
                writer.WriteLine("; 通用配置");
                writer.WriteLine("DefaultCategory=邮件");
                writer.WriteLine("EnableCategorization=true");
                writer.WriteLine();

                writer.WriteLine("[Rule1]");
                writer.WriteLine("Name=电子发票");
                writer.WriteLine("FromEmails=");
                writer.WriteLine("SubjectKeywords=电子发票");
                writer.WriteLine("TargetFolder=电子发票");
                writer.WriteLine("Priority=50");
                writer.WriteLine();


                writer.WriteLine("[Rule2]");
                writer.WriteLine("Name=个人邮件");
                writer.WriteLine("FromEmails=@360buy.com,<EMAIL>");
                writer.WriteLine("SubjectKeywords=");
                writer.WriteLine("TargetFolder=个人");
                writer.WriteLine("Priority=30");
            }
        }

        /// <summary>
        /// 处理邮件附件下载菜单项点击事件
        /// </summary>
        void 邮件附件下载ToolStripMenuItem_Click(object sender, EventArgs e) { this.NormalWindowState(); }

        /// <summary>
        /// 处理退出菜单项点击事件
        /// </summary>
        void toolStripMenuItem退出_Click(object sender, EventArgs e) { Environment.Exit(0); }

        /// <summary>
        /// 处理打开文件夹按钮点击事件
        /// </summary>
        void buttonOpenFolder_Click(object sender, EventArgs e)
        {
            string mailDirectory = iniFile.GetValue("ReceiveMail", "Directory");
            Process.Start("explorer.exe", mailDirectory);
        }
    }
}