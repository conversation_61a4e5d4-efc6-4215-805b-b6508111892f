using System;
using System.Drawing;
using System.Windows.Forms;

namespace ET.Controls
{
    /// <summary>
    /// ET通用输入对话框
    /// </summary>
    /// <remarks>
    /// 功能特性：
    /// 1. 支持单行/多行输入模式
    /// 2. 支持滚动条配置（垂直/水平）
    /// 3. 自动调整提示文本宽度和折行显示
    /// 4. 可设置对话框标题和默认值
    /// 5. 返回用户输入的文本内容
    ///
    /// 使用示例： string result = ETInputDialog.Show("请输入路径：", "输入路径", "C:\\默认路径");
    /// </remarks>
    public partial class ETInputDialog : Form
    {
        #region 私有字段

        private int _maxPromptWidth = 400;
        private bool _isMultiline = false;
        private ScrollBars _scrollBars = ScrollBars.None;

        #endregion 私有字段

        #region 构造函数

        /// <summary>
        /// 初始化输入对话框
        /// </summary>
        public ETInputDialog()
        {
            InitializeComponent();
            InitializeDialog();
        }

        #endregion 构造函数

        #region 公共属性

        /// <summary>
        /// 获取或设置提示文本的最大宽度（像素）
        /// </summary>
        public int MaxPromptWidth
        {
            get => _maxPromptWidth;
            set
            {
                _maxPromptWidth = value;
                labelPrompt.MaximumSize = new System.Drawing.Size(value, 0);
                AdjustDialogSize();
            }
        }

        /// <summary>
        /// 获取或设置输入框是否为多行模式
        /// </summary>
        public bool IsMultiline
        {
            get => _isMultiline;
            set
            {
                _isMultiline = value;
                textBoxInput.Multiline = value;
                if (value)
                {
                    textBoxInput.Height = 80; // 多行时增加高度
                    AdjustDialogSize();
                }
            }
        }

        /// <summary>
        /// 获取或设置输入框的滚动条类型
        /// </summary>
        public ScrollBars InputScrollBars
        {
            get => _scrollBars;
            set
            {
                _scrollBars = value;
                textBoxInput.ScrollBars = value;
            }
        }

        /// <summary>
        /// 获取或设置对话框标题
        /// </summary>
        public string DialogTitle
        {
            get => this.Text;
            set => this.Text = value;
        }

        /// <summary>
        /// 获取或设置提示文本
        /// </summary>
        public string PromptText
        {
            get => labelPrompt.Text;
            set
            {
                labelPrompt.Text = value;
                AdjustDialogSize();
            }
        }

        /// <summary>
        /// 获取或设置输入框的默认值
        /// </summary>
        public string DefaultValue
        {
            get => textBoxInput.Text;
            set => textBoxInput.Text = value;
        }

        /// <summary>
        /// 获取用户输入的值
        /// </summary>
        public string InputValue => textBoxInput.Text;

        #endregion 公共属性

        #region 私有方法

        /// <summary>
        /// 初始化对话框
        /// </summary>
        private void InitializeDialog()
        {
            // 设置默认属性
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.StartPosition = FormStartPosition.CenterParent;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowIcon = false;
            this.ShowInTaskbar = false;

            // 设置Tab顺序
            labelPrompt.TabIndex = 0;
            textBoxInput.TabIndex = 1;
            buttonOK.TabIndex = 2;
            buttonCancel.TabIndex = 3;

            // 设置输入框焦点
            textBoxInput.Select();
        }

        /// <summary>
        /// 根据内容调整对话框大小
        /// </summary>
        private void AdjustDialogSize()
        {
            // 计算提示文本所需的高度
            using (Graphics g = labelPrompt.CreateGraphics())
            {
                SizeF textSize = g.MeasureString(labelPrompt.Text, labelPrompt.Font, _maxPromptWidth);
                int promptHeight = (int)Math.Ceiling(textSize.Height) + 10; // 添加一些边距

                // 设置Label高度
                labelPrompt.Height = promptHeight;

                // 调整输入框位置
                textBoxInput.Top = labelPrompt.Bottom + 8;

                // 计算对话框总高度
                int contentHeight = textBoxInput.Bottom + 12; // 内容区域高度
                int totalHeight = contentHeight + panelButtons.Height + 20; // 总高度

                // 设置对话框大小
                this.Height = totalHeight;

                // 确保最小宽度
                int minWidth = Math.Max(_maxPromptWidth + 24, 300);
                if (this.Width < minWidth)
                {
                    this.Width = minWidth;
                }
            }
        }

        #endregion 私有方法

        #region 事件处理

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void buttonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);

            // 调整对话框大小
            AdjustDialogSize();

            // 选中输入框中的所有文本
            textBoxInput.SelectAll();
            textBoxInput.Focus();
        }

        #endregion 事件处理

        #region 静态方法

        /// <summary>
        /// 显示输入对话框（简化版本）
        /// </summary>
        /// <param name="promptText">提示文本</param>
        /// <param name="title">对话框标题</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>用户输入的文本，如果取消则返回null</returns>
        public static string Show(string promptText, string title = "输入对话框", string defaultValue = "")
        {
            using (var dialog = new ETInputDialog())
            {
                dialog.PromptText = promptText;
                dialog.DialogTitle = title;
                dialog.DefaultValue = defaultValue;

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    return dialog.InputValue;
                }
                return null;
            }
        }

        /// <summary>
        /// 显示输入对话框（完整版本）
        /// </summary>
        /// <param name="promptText">提示文本</param>
        /// <param name="title">对话框标题</param>
        /// <param name="defaultValue">默认值</param>
        /// <param name="isMultiline">是否多行输入</param>
        /// <param name="scrollBars">滚动条类型</param>
        /// <param name="maxPromptWidth">提示文本最大宽度</param>
        /// <returns>用户输入的文本，如果取消则返回null</returns>
        public static string Show(string promptText, string title, string defaultValue,
            bool isMultiline, ScrollBars scrollBars = ScrollBars.None, int maxPromptWidth = 400)
        {
            using (var dialog = new ETInputDialog())
            {
                dialog.PromptText = promptText;
                dialog.DialogTitle = title;
                dialog.DefaultValue = defaultValue;
                dialog.IsMultiline = isMultiline;
                dialog.InputScrollBars = scrollBars;
                dialog.MaxPromptWidth = maxPromptWidth;

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    return dialog.InputValue;
                }
                return null;
            }
        }

        #endregion 静态方法
    }
}