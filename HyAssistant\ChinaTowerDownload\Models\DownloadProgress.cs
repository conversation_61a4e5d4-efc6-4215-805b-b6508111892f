using System;

namespace HyAssistant.ChinaTowerDownload.Models
{
    /// <summary>
    /// 表示照片下载过程中的实时进度信息。
    /// </summary>
    public class DownloadProgress
    {
        /// <summary>
        /// 获取或设置本次下载任务中照片的总数量。
        /// </summary>
        public int TotalPhotos { get; set; }

        /// <summary>
        /// 获取或设置已处理（已尝试下载，无论成功、失败或跳过）的照片数量。
        /// </summary>
        public int CompletedPhotos { get; set; }

        /// <summary>
        /// 获取或设置成功下载的照片数量。
        /// </summary>
        public int SuccessfulPhotos { get; set; }

        /// <summary>
        /// 获取或设置下载失败的照片数量。
        /// </summary>
        public int FailedPhotos { get; set; }

        /// <summary>
        /// 获取或设置因文件已存在而被跳过的照片数量。
        /// </summary>
        public int SkippedPhotos { get; set; }

        /// <summary>
        /// 获取或设置当前正在处理（下载）的照片信息。
        /// </summary>
        public PhotoInfoExcerpt CurrentPhoto { get; set; }

        /// <summary>
        /// 获取当前下载任务的进度百分比。
        /// 计算方式为：<c>(CompletedPhotos / TotalPhotos) * 100</c>。如果 <c>TotalPhotos</c> 为0，则返回0。
        /// </summary>
        public double ProgressPercentage
        {
            get
            {
                if (TotalPhotos == 0) return 0;
                return (double)CompletedPhotos / TotalPhotos * 100;
            }
        }

        /// <summary>
        /// 获取当前下载任务的成功率。
        /// 计算方式为：<c>(SuccessfulPhotos / CompletedPhotos) * 100</c>。如果 <c>CompletedPhotos</c> 为0，则返回0。
        /// </summary>
        public double SuccessRate
        {
            get
            {
                if (CompletedPhotos == 0) return 0;
                return (double)SuccessfulPhotos / CompletedPhotos * 100;
            }
        }
    }
}
