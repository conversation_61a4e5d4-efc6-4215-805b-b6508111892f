using ET;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HyAssistant
{
    /// <summary>
    /// 文件分析器主窗体类，提供实时文件监控、变更通知和配置管理功能。
    /// <para>支持INI配置文件持久化、系统托盘最小化、文件系统监视器集成等特性。</para>
    /// <para>实现了文件修改事件的批量处理和通知频率控制，避免频繁弹窗干扰用户。</para>
    /// </summary>
    public partial class FileAnalyzer : Form
    {
        #region 常量和字段

        // 配置文件路径
        private string _iniFilePath;

        // 初始化配置文件对象
        private ETIniFile _iniFile;

        // 文件系统监视器，用于实时监控文件变动
        private FileSystemWatcher fileSystemWatcher;

        // 上次通知时间，用于控制通知频率
        private DateTime lastNotificationTime = DateTime.MinValue;

        // 通知间隔（秒）
        private const int NOTIFICATION_INTERVAL_SECONDS = 10;

        // 存储监控目录下发生修改的文件信息（文件路径 -> 最后修改时间）
        private Dictionary<string, DateTime> modifiedFilesInfo = new Dictionary<string, DateTime>();

        // 用于线程安全访问modifiedFilesInfo的锁对象
        private readonly object modifiedFilesLock = new object();

        #endregion 常量和字段

        #region 初始化,界面

        /// <summary>
        /// 初始化 <see cref="FileAnalyzer"/> 类的新实例，完成窗体组件、配置文件和文件监视器的初始化。
        /// <para>自动加载INI配置文件，创建默认配置（如不存在），并启动文件系统监控。</para>
        /// </summary>
        public FileAnalyzer()
        {
            // 初始化窗体组件
            InitializeComponent();

            // 初始化配置文件
            InitializeConfigFile();

            // 初始化文件系统监视器
            InitializeFileSystemWatcher();
        }

        /// <summary>
        /// 窗体加载事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void Main_Load(object sender, EventArgs e)
        {
            // 绑定控件到INI设置
            BindControlsToIniSettings();
        }

        /// <summary>
        /// 窗体关闭事件处理
        /// </summary>
        private void Main_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                e.Cancel = true;
                this.HideToTray();
            }
            else
            {
                // 如果是应用程序退出，释放资源
                ReleaseResources();
            }
        }

        /// <summary>
        /// 初始化应用程序配置文件，创建INI配置文件对象并确保配置文件存在。
        /// <para>自动创建默认配置文件（如不存在），设置配置文件路径为应用程序配置目录。</para>
        /// <para>处理配置文件创建过程中的异常，确保应用程序正常启动。</para>
        /// </summary>
        private void InitializeConfigFile()
        {
            try
            {
                // 定义配置文件路径
                _iniFilePath = ETConfig.GetConfigDirectory("fileanalyzer.ini");

                // 创建配置文件对象
                _iniFile = new ETIniFile(_iniFilePath);

                // 如果配置文件不存在，创建默认配置
                if (!File.Exists(_iniFilePath))
                {
                    CreateDefaultConfig();
                }
            }
            catch (Exception ex)
            {
                LogMessage($"初始化配置文件时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建默认INI配置文件，包含文件分析器的基本设置和控件默认值。
        /// <para>创建配置文件目录（如不存在），设置监控开关、目录路径和时间范围等默认配置项。</para>
        /// <para>默认配置确保应用程序首次运行时具有合理的初始设置。</para>
        /// </summary>
        private void CreateDefaultConfig()
        {
            try
            {
                // 确保配置文件目录存在
                string configDir = Path.GetDirectoryName(_iniFilePath);
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }

                // 创建默认配置节和值
                string section = "FileAnalyzer";
                _iniFile.SetValue(section, "checkBoxAnalyzer", "1");
                _iniFile.SetValue(section, "uc监控上报修改文件_目录", "");
                _iniFile.SetValue(section, "uc监控上报修改文件_StartTime", DateTime.Now.ToString("HH:mm:ss"));
                _iniFile.SetValue(section, "uc监控上报修改文件_EndTime", DateTime.Now.ToString("HH:mm:ss"));

                // 保存配置文件
                _iniFile.IniWriteFile();

                LogMessage("已创建默认配置文件");
            }
            catch (Exception ex)
            {
                LogMessage($"创建默认配置时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 绑定控件到INI设置
        /// </summary>
        private void BindControlsToIniSettings()
        {
            try
            {
                string section = "FileAnalyzer";

                // 绑定控件到配置文件
                ETForm.BindWindowsFormControl(checkBoxAnalyzer, _iniFile, section, "checkBoxAnalyzer");
                ETForm.BindWindowsFormControl(uc监控上报修改文件_目录, _iniFile, section, "uc监控上报修改文件_目录");
                ETForm.BindWindowsFormControl(uc监控上报修改文件_StartTime, _iniFile, section, "uc监控上报修改文件_StartTime");
                ETForm.BindWindowsFormControl(uc监控上报修改文件_EndTime, _iniFile, section, "uc监控上报修改文件_EndTime");

                // 监听控件变化事件
                uc监控上报修改文件_目录.TextChanged += Uc监控上报修改文件_目录_TextChanged;
                checkBoxAnalyzer.CheckedChanged += CheckBoxAnalyzer_CheckedChanged;

                // 如果checkBoxAnalyzer为True，自动开始监控
                if (checkBoxAnalyzer.Checked)
                {
                    StartMonitoring();
                }
            }
            catch (Exception ex)
            {
                LogMessage($"绑定控件到配置文件时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 托盘图标双击事件处理
        /// </summary>
        private void notifyIcon1_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            this.NormalWindowState();
        }

        #endregion 初始化,界面

        #region 实时文件监控

        /// <summary>
        /// 初始化文件系统监视器，配置实时监控文件变动的参数和事件处理。
        /// <para>设置监视过滤器为所有文件类型，监听文件修改、创建、重命名等事件。</para>
        /// <para>包含子目录监控，确保整个目录树的变化都能被捕获。</para>
        /// </summary>
        private void InitializeFileSystemWatcher()
        {
            fileSystemWatcher = new FileSystemWatcher
            {
                // 设置监视器属性
                NotifyFilter = NotifyFilters.LastWrite
                                           | NotifyFilters.FileName
                                           | NotifyFilters.DirectoryName,

                // 监听所有文件
                Filter = "*.*",

                // 包含子目录
                IncludeSubdirectories = true
            };

            // 注册事件处理程序
            fileSystemWatcher.Changed += FileSystemWatcher_Changed;
            fileSystemWatcher.Created += FileSystemWatcher_Changed;
            fileSystemWatcher.Renamed += FileSystemWatcher_Changed;

            // 根据实时提示选项设置是否启用
            UpdateFileSystemWatcher();
        }

        /// <summary>
        /// 根据用户界面设置更新文件系统监视器的监控路径和启用状态。
        /// <para>验证监控目录的有效性，仅在目录存在且监控开关开启时启用监视器。</para>
        /// <para>处理UI线程切换，确保线程安全的控件操作。</para>
        /// </summary>
        private void UpdateFileSystemWatcher()
        {
            // 确保在UI线程上执行
            if (InvokeRequired)
            {
                BeginInvoke(new Action(UpdateFileSystemWatcher));
                return;
            }

            try
            {
                string directory = uc监控上报修改文件_目录.Text;

                // 停止监视
                fileSystemWatcher.EnableRaisingEvents = false;

                // 只有在checkBoxAnalyzer选中且目录存在时才启用监控
                if (checkBoxAnalyzer.Checked && Directory.Exists(directory))
                {
                    fileSystemWatcher.Path = directory;
                    fileSystemWatcher.EnableRaisingEvents = true;
                    LogMessage($"已启用实时文件监控: {directory}");
                }
                else if (checkBoxAnalyzer.Checked && !string.IsNullOrEmpty(directory))
                {
                    LogMessage($"无法启用实时文件监控: 目录不存在 {directory}");
                }
                else
                {
                    LogMessage("已禁用实时文件监控");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"更新文件系统监视器时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 监控目录文本框变化事件处理
        /// </summary>
        private void Uc监控上报修改文件_目录_TextChanged(object sender, EventArgs e)
        {
            // 清空之前的监控结果
            ClearMonitorResult();

            UpdateFileSystemWatcher();
        }

        /// <summary>
        /// checkBoxAnalyzer选中状态变化事件处理
        /// </summary>
        private void CheckBoxAnalyzer_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                if (checkBoxAnalyzer.Checked)
                {
                    StartMonitoring();
                }
                else
                {
                    StopMonitoring();
                }
            }
            catch (Exception ex)
            {
                LogMessage($"切换监控状态时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 开始监控
        /// </summary>
        private void StartMonitoring()
        {
            try
            {
                UpdateFileSystemWatcher();
                LogMessage("已开始文件监控");
            }
            catch (Exception ex)
            {
                LogMessage($"开始监控时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 停止监控
        /// </summary>
        private void StopMonitoring()
        {
            try
            {
                if (fileSystemWatcher != null)
                {
                    fileSystemWatcher.EnableRaisingEvents = false;
                }
                LogMessage("已停止文件监控");
            }
            catch (Exception ex)
            {
                LogMessage($"停止监控时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 文件系统变化事件处理
        /// </summary>
        private void FileSystemWatcher_Changed(object sender, FileSystemEventArgs e)
        {
            try
            {
                // 控制通知频率，避免短时间内大量通知
                DateTime now = DateTime.Now;
                if ((now - lastNotificationTime).TotalSeconds < NOTIFICATION_INTERVAL_SECONDS)
                {
                    return;
                }

                lastNotificationTime = now;

                // 获取相对路径（在工作线程上执行）
                string directory = string.Empty;
                string relativePath = e.FullPath;

                // 安全地获取目录路径
                BeginInvoke(new Action(() =>
                {
                    try
                    {
                        // 在UI线程上获取目录路径
                        directory = uc监控上报修改文件_目录.Text;

                        // 计算相对路径
                        if (relativePath.StartsWith(directory, StringComparison.OrdinalIgnoreCase))
                        {
                            relativePath = relativePath.Substring(directory.Length).TrimStart('\\');
                        }

                        // 添加文件信息到字典中（线程安全）
                        lock (modifiedFilesLock)
                        {
                            modifiedFilesInfo[relativePath] = now;
                        }

                        // 更新监控结果显示
                        UpdateMonitorResultDisplay();

                        // 构建通知消息
                        string changeType = GetChangeTypeDescription(e.ChangeType);
                        string message = $"检测到文件变动: {changeType}\n{relativePath}";

                        // 记录日志
                        LogMessage($"文件变动: {changeType} - {relativePath}");

                        // 显示通知
                        ETNotificationHelper.ShowNotification(message, false, true);
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"处理文件变动通知时发生错误: {ex.Message}");
                    }
                }));
            }
            catch (Exception ex)
            {
                // 捕获任何可能的异常，确保不会导致应用程序崩溃
                BeginInvoke(new Action(() => LogMessage($"处理文件变动通知时发生错误: {ex.Message}")));
            }
        }

        /// <summary>
        /// 获取变动类型的描述
        /// </summary>
        private string GetChangeTypeDescription(WatcherChangeTypes changeType)
        {
            switch (changeType)
            {
                case WatcherChangeTypes.Created:
                    return "新建";

                case WatcherChangeTypes.Deleted:
                    return "删除";

                case WatcherChangeTypes.Changed:
                    return "修改";

                case WatcherChangeTypes.Renamed:
                    return "重命名";

                default:
                    return changeType.ToString();
            }
        }

        /// <summary>
        /// 更新监控结果显示
        /// </summary>
        private void UpdateMonitorResultDisplay()
        {
            try
            {
                // 确保在UI线程上执行
                if (InvokeRequired)
                {
                    BeginInvoke(new Action(UpdateMonitorResultDisplay));
                    return;
                }

                // 检查控件是否已初始化
                if (textBox监控结果 == null || textBox监控结果.IsDisposed)
                {
                    return;
                }

                lock (modifiedFilesLock)
                {
                    // 按时间排序文件信息
                    var sortedFiles = modifiedFilesInfo
                        .OrderBy(kvp => kvp.Value)
                        .ToList();

                    // 构建显示文本
                    var displayText = new System.Text.StringBuilder();
                    foreach (var fileInfo in sortedFiles)
                    {
                        string timeString = fileInfo.Value.ToString("HH:mm:ss");
                        displayText.AppendLine($"[{timeString}] {fileInfo.Key}");
                    }

                    // 更新textBox内容
                    textBox监控结果.Text = displayText.ToString();

                    // 滚动到最新内容
                    textBox监控结果.SelectionStart = textBox监控结果.TextLength;
                    textBox监控结果.ScrollToCaret();
                }
            }
            catch (Exception ex)
            {
                LogMessage($"更新监控结果显示时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 清空监控结果
        /// </summary>
        private void ClearMonitorResult()
        {
            try
            {
                // 确保在UI线程上执行
                if (InvokeRequired)
                {
                    BeginInvoke(new Action(ClearMonitorResult));
                    return;
                }

                // 清空文件信息字典
                lock (modifiedFilesLock)
                {
                    modifiedFilesInfo.Clear();
                }

                // 清空textBox内容
                if (textBox监控结果 != null && !textBox监控结果.IsDisposed)
                {
                    textBox监控结果.Clear();
                }
            }
            catch (Exception ex)
            {
                LogMessage($"清空监控结果时发生错误: {ex.Message}");
            }
        }

        #endregion 实时文件监控

        #region 工具方法

        /// <summary>
        /// 记录日志消息到日志控件
        /// </summary>
        /// <param name="message">日志消息</param>
        private void LogMessage(string message)
        {
            // 检查是否需要在 UI 线程上执行
            if (InvokeRequired)
            {
                try
                {
                    // 使用BeginInvoke而不是Invoke，避免死锁
                    BeginInvoke(new Action(() => LogMessage(message)));
                }
                catch (ObjectDisposedException)
                {
                    // 窗体已关闭，忽略异常
                    System.Diagnostics.Debug.WriteLine($"[日志] {message}");
                }
                catch (InvalidOperationException)
                {
                    // 窗体正在关闭，忽略异常
                    System.Diagnostics.Debug.WriteLine($"[日志] {message}");
                }
                return;
            }

            try
            {
                // 检查控件是否已初始化
                if (uc监控上报修改文件_Log == null || uc监控上报修改文件_Log.IsDisposed)
                {
                    System.Diagnostics.Debug.WriteLine($"[日志] {message}");
                    return;
                }

                string timestampedMessage = $"[{DateTime.Now}] {message}\r\n";
                uc监控上报修改文件_Log.AppendText(timestampedMessage);

                // 限制日志长度，防止内存占用过大
                if (uc监控上报修改文件_Log.TextLength > 50000)
                {
                    uc监控上报修改文件_Log.Text = uc监控上报修改文件_Log.Text.Substring(uc监控上报修改文件_Log.TextLength - 25000);
                }

                // 滚动到最新日志
                uc监控上报修改文件_Log.SelectionStart = uc监控上报修改文件_Log.TextLength;
                uc监控上报修改文件_Log.ScrollToCaret();
            }
            catch (Exception ex)
            {
                // 记录到调试输出
                System.Diagnostics.Debug.WriteLine($"[日志错误] 无法写入日志: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[日志内容] {message}");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        internal void ReleaseResources()
        {
            try
            {
                // 释放文件系统监视器资源
                if (fileSystemWatcher != null)
                {
                    fileSystemWatcher.EnableRaisingEvents = false;
                    fileSystemWatcher.Changed -= FileSystemWatcher_Changed;
                    fileSystemWatcher.Created -= FileSystemWatcher_Changed;
                    fileSystemWatcher.Renamed -= FileSystemWatcher_Changed;
                    fileSystemWatcher.Dispose();
                    fileSystemWatcher = null;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"释放资源时发生错误: {ex.Message}");
            }
        }

        #endregion 工具方法
    }
}