using HyAssistant.ChinaTowerDownload.Models;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Data.SQLite;
using System.Threading.Tasks;

namespace HyAssistant.ChinaTowerDownload.Data
{
    /// <summary>
    /// <see cref="T:HyAssistant.ChinaTowerDownload.Data.StationRepository"/> 类实现了 <see cref="T:HyAssistant.ChinaTowerDownload.Data.IStationRepository"/> 接口，
    /// 提供了对站点信息进行SQLite数据库操作的具体实现，包括站点的增、删、改、查以及数据库的初始化和索引创建。
    /// </summary>
    public class StationRepository : IStationRepository
    {
        private readonly string _connectionString;

        /// <summary>
        /// 使用指定的数据库连接字符串初始化 <see cref="T:HyAssistant.ChinaTowerDownload.Data.StationRepository"/> 类的新实例。
        /// </summary>
        /// <param name="connectionString">SQLite数据库的连接字符串。此参数不能为空。</param>
        public StationRepository(string connectionString)
        {
            _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
        }

        /// <summary>
        /// 异步初始化数据库表结构，如果表不存在则创建 `stationinfo` 表，并创建必要的索引。
        /// </summary>
        /// <returns>一个表示异步操作的任务，如果数据库初始化成功则结果为 <c>true</c>，否则为 <c>false</c>。</returns>
        public async Task<bool> InitializeDatabaseAsync()
        {
            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    // 创建站点信息表
                    var createTableSql = @"
                        CREATE TABLE IF NOT EXISTS stationinfo (
                            ID INTEGER PRIMARY KEY AUTOINCREMENT,
                            STATION_ID TEXT(200) NOT NULL,
                            STATION_NAME TEXT(200) NOT NULL,
                            STATION_CODE TEXT(200) NOT NULL,
                            LastCrawlTime INTEGER DEFAULT 0
                        );";

                    using (var command = new SQLiteCommand(createTableSql, connection))
                    {
                        await command.ExecuteNonQueryAsync();
                    }

                    // 创建索引
                    await CreateIndexesAsync(connection);
                }

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 异步创建数据库索引，以优化查询性能。此方法会创建针对 `stationinfo` 表中 `STATION_CODE`, `STATION_NAME`, `STATION_ID` 和 `LastCrawlTime` 列的索引。
        /// </summary>
        /// <param name="connection">已打开的SQLite数据库连接。</param>
        private async Task CreateIndexesAsync(SQLiteConnection connection)
        {
            var indexes = new[]
            {
                "CREATE INDEX IF NOT EXISTS idx_stationinfo_code ON stationinfo(STATION_CODE);",
                "CREATE INDEX IF NOT EXISTS idx_stationinfo_name ON stationinfo(STATION_NAME);",
                "CREATE INDEX IF NOT EXISTS idx_stationinfo_station_id ON stationinfo(STATION_ID);",
                "CREATE INDEX IF NOT EXISTS idx_stationinfo_crawl_time ON stationinfo(LastCrawlTime);"
            };

            foreach (var indexSql in indexes)
            {
                try
                {
                    using (var command = new SQLiteCommand(indexSql, connection))
                    {
                        await command.ExecuteNonQueryAsync();
                    }
                }
                catch
                {
                    // 索引创建失败不影响主要功能
                }
            }
        }

        /// <summary>
        /// 异步获取数据库中所有站点的基本信息。
        /// </summary>
        /// <returns>一个表示异步操作的任务，其结果是包含所有站点信息摘要的 <see cref="T:System.Collections.Generic.List`1"/>，如果发生错误则返回空列表。</returns>
        public async Task<List<StationInfoExcerpt>> GetAllStationsAsync()
        {
            var stations = new List<StationInfoExcerpt>();

            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SQLiteCommand("SELECT * FROM stationinfo ORDER BY STATION_CODE", connection))
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            stations.Add(MapToStationInfo(reader));
                        }
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误但不抛出异常
            }

            return stations;
        }

        /// <summary>
        /// 异步根据关键词搜索站点信息。搜索将匹配站点的编码（STATION_CODE）或名称（STATION_NAME）。
        /// </summary>
        /// <param name="keyword">用于搜索的关键词。如果为空或空白，则返回空列表。</param>
        /// <returns>一个表示异步操作的任务，其结果是包含匹配站点信息摘要的 <see cref="T:System.Collections.Generic.List`1"/>，如果发生错误或无匹配项则返回空列表。</returns>
        public async Task<List<StationInfoExcerpt>> SearchStationsAsync(string keyword)
        {
            var stations = new List<StationInfoExcerpt>();

            if (string.IsNullOrWhiteSpace(keyword))
                return stations;

            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var sql = @"
                        SELECT * FROM stationinfo
                        WHERE STATION_CODE LIKE @keyword OR STATION_NAME LIKE @keyword
                        ORDER BY STATION_CODE";

                    using (var command = new SQLiteCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@keyword", $"%{keyword}%");

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                stations.Add(MapToStationInfo(reader));
                            }
                        }
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误但不抛出异常
            }

            return stations;
        }

        /// <summary>
        /// 异步根据站点编码获取单个站点的信息。
        /// </summary>
        /// <param name="stationCode">要查询的站点编码。如果为空或空白，则返回 <c>null</c>。</param>
        /// <returns>一个表示异步操作的任务，其结果是匹配的 <see cref="T:HyAssistant.ChinaTowerDownload.Models.StationInfoExcerpt"/> 对象；如果没有找到匹配项或发生错误，则返回 <c>null</c>。</returns>
        public async Task<StationInfoExcerpt> GetStationByCodeAsync(string stationCode)
        {
            if (string.IsNullOrWhiteSpace(stationCode))
                return null;

            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SQLiteCommand("SELECT * FROM stationinfo WHERE STATION_CODE = @code", connection))
                    {
                        command.Parameters.AddWithValue("@code", stationCode);

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                return MapToStationInfo(reader);
                            }
                        }
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误但不抛出异常
            }

            return null;
        }

        /// <summary>
        /// 异步根据站点ID获取单个站点的信息。
        /// </summary>
        /// <param name="stationId">要查询的站点ID。如果为空或空白，则返回 <c>null</c>。</param>
        /// <returns>一个表示异步操作的任务，其结果是匹配的 <see cref="T:HyAssistant.ChinaTowerDownload.Models.StationInfoExcerpt"/> 对象；如果没有找到匹配项或发生错误，则返回 <c>null</c>。</returns>
        public async Task<StationInfoExcerpt> GetStationByIdAsync(string stationId)
        {
            if (string.IsNullOrWhiteSpace(stationId))
                return null;

            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SQLiteCommand("SELECT * FROM stationinfo WHERE STATION_ID = @id", connection))
                    {
                        command.Parameters.AddWithValue("@id", stationId);

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                return MapToStationInfo(reader);
                            }
                        }
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误但不抛出异常
            }

            return null;
        }

        /// <summary>
        /// 异步批量插入站点信息到数据库。在插入前会检查站点是否存在，如果已存在则跳过。
        /// </summary>
        /// <param name="stations">要插入的站点信息列表。如果列表为空或为 <c>null</c>，则不执行任何操作并返回0。</param>
        /// <returns>一个表示异步操作的任务，其结果是成功插入的站点数量。</returns>
        public async Task<int> InsertStationsAsync(List<StationInfoExcerpt> stations)
        {
            if (stations == null || stations.Count == 0)
                return 0;

            int insertedCount = 0;

            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var transaction = connection.BeginTransaction())
                    {
                        foreach (var station in stations)
                        {
                            // 检查是否已存在
                            if (await StationExistsInternalAsync(connection, station.STATION_ID))
                                continue;

                            // 插入新记录
                            var sql = @"
                                INSERT INTO stationinfo (STATION_ID, STATION_NAME, STATION_CODE, LastCrawlTime)
                                VALUES (@stationId, @stationName, @stationCode, @lastCrawlTime)";

                            using (var command = new SQLiteCommand(sql, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@stationId", station.STATION_ID);
                                command.Parameters.AddWithValue("@stationName", station.STATION_NAME);
                                command.Parameters.AddWithValue("@stationCode", station.STATION_CODE);
                                command.Parameters.AddWithValue("@lastCrawlTime", station.LastCrawlTime);

                                if (await command.ExecuteNonQueryAsync() > 0)
                                    insertedCount++;
                            }
                        }

                        transaction.Commit();
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误但不抛出异常
            }

            return insertedCount;
        }

        /// <summary>
        /// 异步更新指定站点的最后抓取时间。
        /// </summary>
        /// <param name="stationId">要更新的站点的唯一标识符。</param>
        /// <param name="lastCrawlTime">新的最后抓取时间戳。</param>
        /// <returns>一个表示异步操作的任务，如果更新成功则结果为 <c>true</c>，否则为 <c>false</c>。</returns>
        public async Task<bool> UpdateLastCrawlTimeAsync(string stationId, long lastCrawlTime)
        {
            if (string.IsNullOrWhiteSpace(stationId))
                return false;

            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var sql = "UPDATE stationinfo SET LastCrawlTime = @time WHERE STATION_ID = @id";

                    using (var command = new SQLiteCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@time", lastCrawlTime);
                        command.Parameters.AddWithValue("@id", stationId);

                        return await command.ExecuteNonQueryAsync() > 0;
                    }
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 异步获取数据库中站点的总数量。
        /// </summary>
        /// <returns>一个表示异步操作的任务，其结果是数据库中站点的总数量。如果发生错误，则返回0。</returns>
        public async Task<int> GetStationCountAsync()
        {
            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SQLiteCommand("SELECT COUNT(*) FROM stationinfo", connection))
                    {
                        var result = await command.ExecuteScalarAsync();
                        return Convert.ToInt32(result);
                    }
                }
            }
            catch (Exception)
            {
                return 0;
            }
        }

        /// <summary>
        /// 异步检查指定站点ID的站点是否存在于数据库中。
        /// </summary>
        /// <param name="stationId">要检查的站点的唯一标识符。如果为空或空白，则返回 <c>false</c>。</param>
        /// <returns>一个表示异步操作的任务，如果站点存在则结果为 <c>true</c>，否则为 <c>false</c>。</returns>
        public async Task<bool> StationExistsAsync(string stationId)
        {
            if (string.IsNullOrWhiteSpace(stationId))
                return false;

            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    return await StationExistsInternalAsync(connection, stationId);
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 内部异步方法：检查指定站点ID的站点是否存在于数据库中。
        /// </summary>
        /// <param name="connection">已打开的SQLite数据库连接。</param>
        /// <param name="stationId">要检查的站点的唯一标识符。</param>
        /// <returns>一个表示异步操作的任务，如果站点存在则结果为 <c>true</c>，否则为 <c>false</c>。</returns>
        private async Task<bool> StationExistsInternalAsync(SQLiteConnection connection, string stationId)
        {
            using (var command = new SQLiteCommand("SELECT COUNT(*) FROM stationinfo WHERE STATION_ID = @id", connection))
            {
                command.Parameters.AddWithValue("@id", stationId);

                var result = await command.ExecuteScalarAsync();
                return Convert.ToInt32(result) > 0;
            }
        }

        /// <summary>
        /// 异步删除指定站点ID的站点信息。
        /// </summary>
        /// <param name="stationId">要删除的站点的唯一标识符。如果为空或空白，则不执行任何操作并返回 <c>false</c>。</param>
        /// <returns>一个表示异步操作的任务，如果删除成功则结果为 <c>true</c>，否则为 <c>false</c>。</returns>
        public async Task<bool> DeleteStationAsync(string stationId)
        {
            if (string.IsNullOrWhiteSpace(stationId))
                return false;

            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SQLiteCommand("DELETE FROM stationinfo WHERE STATION_ID = @id", connection))
                    {
                        command.Parameters.AddWithValue("@id", stationId);

                        return await command.ExecuteNonQueryAsync() > 0;
                    }
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 将 <see cref="T:System.Data.Common.DbDataReader"/> 中的当前记录映射为 <see cref="T:HyAssistant.ChinaTowerDownload.Models.StationInfoExcerpt"/> 对象。
        /// </summary>
        /// <param name="reader">包含站点信息数据的 <see cref="T:System.Data.Common.DbDataReader"/> 实例。</param>
        /// <returns>一个包含映射后站点信息的 <see cref="T:HyAssistant.ChinaTowerDownload.Models.StationInfoExcerpt"/> 对象。</returns>
        private StationInfoExcerpt MapToStationInfo(DbDataReader reader)
        {
            return new StationInfoExcerpt
            {
                STATION_ID = reader["STATION_ID"].ToString(),
                STATION_NAME = reader["STATION_NAME"].ToString(),
                STATION_CODE = reader["STATION_CODE"].ToString(),
                LastCrawlTime = Convert.ToInt64(reader["LastCrawlTime"])
            };
        }
    }
}
