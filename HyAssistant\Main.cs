using ET;
using ET.ETLicense;
using HyAssistant.NewFolder;
using HyAssistant.WebBrowserV2.Core;
using System;
using System.Drawing;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HyAssistant
{
    /// <summary>
    /// 主窗体类，负责管理所有助手功能的入口和状态
    /// </summary>
    public partial class MainForm : Form
    {
        #region 字段和属性

        /// <summary>
        /// 通信服务实例，用于处理进程间通信
        /// </summary>
        private ETCommunicationService _communicationService;

        /// <summary>
        /// 文件复制助手窗体实例
        /// </summary>
        public FileCopier frmFileCopier;

        /// <summary>
        /// 文件解压助手窗体实例
        /// </summary>
        public FileExtract frmFileExtractor;

        /// <summary>
        /// 邮箱附件接收助手窗体实例
        /// </summary>
        public MailAttachmentsDownloader frmMailAttachmentsDownloader;

        /// <summary>
        /// 网页浏览器窗体实例
        /// </summary>
        public WebBrowser webBrowser;

        /// <summary>
        /// 网页浏览器V2窗体实例
        /// </summary>
        public HyAssistant.WebBrowserV2.Core.WebBrowserV2 webBrowserV2;

        /// <summary>
        /// Visio文件转PDF监控窗体实例
        /// </summary>
        private VisioPDF visioPdfForm;

        /// <summary>
        /// 文件分析监控窗体实例
        /// </summary>
        private FileAnalyzer fileAnalyzerForm;

        /// <summary>
        /// 中国铁塔照片下载助手窗体实例
        /// </summary>
        private ChinaTowerDownload.ChinaTowerDownload chinaTowerDownloadForm;

        /// <summary>
        /// 文本存储目录
        /// </summary>
        private readonly string _noteDirectory;

        /// <summary>
        /// 日志清理定时器
        /// </summary>
        private Timer _logCleanupTimer;

        /// <summary>
        /// 授权信息检查定时器
        /// </summary>
        private Timer _licenseCheckTimer;

        /// <summary>
        /// 新建目录功能管理器
        /// </summary>
        private NewFolderManager _newFolderManager;

        #endregion 字段和属性

        #region 构造函数和初始化

        /// <summary>
        /// 初始化主窗体
        /// </summary>
        public MainForm()
        {
            InitializeComponent();

            // 初始化文本存储目录
            _noteDirectory = ETConfig.GetConfigDirectory("note");
            if (!Directory.Exists(_noteDirectory))
            {
                Directory.CreateDirectory(_noteDirectory);
            }

            // 初始化授权控制器
            HyAssistantLicenseManager.InitializeLicenseController();

            _ = InitializeCommunicationService();
            InitializeTextBoxEvents();
            InitializeLogCleanupTimer();
            InitializeLicenseCheckTimer();

            // 订阅WebBrowser状态变更事件
            if (webBrowser == null)

                // 先不订阅，待WebBrowser创建后再订阅
                WriteLog("WebBrowser尚未初始化，将在创建后订阅状态事件");
            else
            {
                // 立即订阅
                WebBrowserStatusBridge.StatusChanged += WebBrowserStatus_Changed;
            }
        }

        /// <summary>
        /// 处理主窗体加载事件，初始化应用程序
        /// </summary>
        private async void Main_Load(object sender, EventArgs e)
        {
            try
            {
                this.HideToTray();

                // 初始化权限管理器
                HyAssistantLicenseManager.InitializePermissionManagers(this);
                HyAssistantLicenseManager.InitializeAuthorization();

                await InitializeFeaturesAsync().ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                WriteLog($"应用程序初始化出错: {ex.Message}");
                MessageBox.Show($"应用程序初始化出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion 构造函数和初始化

        #region 授权相关

        /// <summary>
        /// 初始化所有功能模块和对应的菜单项
        /// </summary>
        private async Task InitializeFeaturesAsync()
        {
            //根据license权限决定是否显示授权管理菜单
            bool hasLicensePermission = await HyAssistantLicenseManager.HasPermissionAsync(HaPermissionKeys.License).ConfigureAwait(true);

            // 检查后门文件是否存在
            bool backdoorFileExists = File.Exists(@"C:\System\ETLicenseBaseInfo.dat");

            // 如果有授权权限或后门文件存在，则显示授权管理菜单
            授权管理ToolStripMenuItem.Visible = hasLicensePermission || backdoorFileExists;

            // 定义功能模块配置
            (string featureName, Func<Form> formFactory, ToolStripMenuItem menuItem, Action<Form> customInit)[] featureConfigs = new (string featureName, Func<Form> formFactory, ToolStripMenuItem menuItem, Action<Form> customInit)[]
            {
                (HaPermissionKeys.FileCopier, () => frmFileCopier = new FileCopier(), 文件复制助手ToolStripMenuItem,null),
                (HaPermissionKeys.Mail, () => frmMailAttachmentsDownloader = new MailAttachmentsDownloader(), 邮箱附件接收助手ToolStripMenuItem, null),
                (HaPermissionKeys.FileExtract, () => frmFileExtractor = new FileExtract(), 文件解压助手ToolStripMenuItem, null),
                (HaPermissionKeys.FileAnalyzer, () => fileAnalyzerForm = new FileAnalyzer(), 监控文件ToolStripMenuItem, null),
                (HaPermissionKeys.VisioPdf, () => visioPdfForm = new VisioPDF(), 监控Visio文件自动转PDFToolStripMenuItem, null),
                (HaPermissionKeys.WebBrowser, () => {
                    webBrowser = new WebBrowser();
                    return webBrowser;
                }, 网页常挂助手ToolStripMenuItem,
                    form => {
                        // WebBrowser特殊处理：订阅状态事件
                        if (form is WebBrowser browser)

                            WebBrowserStatusBridge.StatusChanged += WebBrowserStatus_Changed;
                            // 移除WebBrowser状态事件订阅的日志输出 WriteLog("已订阅WebBrowser状态事件");
                    })
            };

            // 初始化每个功能模块
            foreach ((string featureName, Func<Form> formFactory, ToolStripMenuItem menuItem, Action<Form> customInit) config in featureConfigs)

                await InitializeFeature(config.featureName, () =>
                {
                    Form form = config.formFactory();
                    InitializeFormWithoutFlicker(form, config.customInit);
                }, config.menuItem).ConfigureAwait(true);

            // 初始化新建目录功能
            InitializeNewFolderFeature();
        }

        /// <summary>
        /// 初始化窗体，避免闪烁
        /// </summary>
        /// <param name="form">需要初始化的窗体</param>
        /// <param name="customInit">自定义初始化操作</param>
        private void InitializeFormWithoutFlicker(Form form, Action<Form> customInit = null)
        {
            // 默认设置：居中显示
            form.StartPosition = FormStartPosition.CenterScreen;

            // 应用自定义设置（如果有）
            customInit?.Invoke(form);

            // 避免窗体闪现
            form.Opacity = 0;
            form.Show();
            form.Hide();
            form.Opacity = 1; // 恢复透明度以便后续显示
        }

        /// <summary>
        /// 初始化单个功能模块
        /// </summary>
        /// <param name="featureName">功能名称</param>
        /// <param name="initAction">初始化动作</param>
        /// <param name="menuItem">对应的菜单项</param>
        private async Task InitializeFeature(string featureName, Action initAction, ToolStripMenuItem menuItem)
        {
            bool hasPermission = await HyAssistantLicenseManager.HasPermissionAsync(featureName).ConfigureAwait(true);

            if (hasPermission)
            {
                initAction?.Invoke();
                menuItem.Visible = true;

                // 如果初始化的是WebBrowser功能，订阅状态变更事件
                if (featureName == HaPermissionKeys.WebBrowser && webBrowser != null)
                {
                    WebBrowserStatusBridge.StatusChanged += WebBrowserStatus_Changed;
                    // 移除WebBrowser状态事件订阅的日志输出 WriteLog("已订阅WebBrowser状态事件");
                }

                // 获取许可过期时间
                var (_, expireTime) = await HyAssistantLicenseManager.CheckPermissionWithExpireTimeAsync(featureName).ConfigureAwait(true);
                if (expireTime.HasValue)
                {
                    // 计算剩余天数
                    int daysLeft = (int)(expireTime.Value - DateTime.Now).TotalDays;

                    // 如果剩余天数少于等于0，将菜单隐藏
                    if (daysLeft <= 0)
                    {
                        menuItem.Visible = false;
                        // 移除授权过期相关日志输出到textBoxLog WriteLog($"功能 {featureName} 授权已过期，菜单已隐藏");
                        return;
                    }

                    // 如果剩余天数少于30天，添加到菜单项文本中提示用户
                    if (daysLeft <= 30)
                    {
                        menuItem.Text = $"{menuItem.Text} [还剩{daysLeft}天]";

                        // 如果少于7天，更改菜单项颜色为红色
                        if (daysLeft <= 7)
                        {
                            menuItem.ForeColor = Color.Red;
                        }
                        else if (daysLeft <= 15)
                        {
                            menuItem.ForeColor = Color.Orange;
                        }
                    }
                }
            }
            else
            {
                // 无权限时隐藏菜单项
                menuItem.Visible = false;
                // 移除授权相关日志输出到textBoxLog WriteLog($"功能 {featureName} 无授权，菜单已隐藏");
            }
        }

        /// <summary>
        /// 显示功能窗体的通用方法
        /// </summary>
        /// <param name="featureName">功能名称，用于检查是否启用</param>
        /// <param name="createForm">创建或获取窗体的委托</param>
        private async Task ShowFeatureForm(string featureName, Func<Form> createForm)
        {
            bool hasPermission = await HyAssistantLicenseManager.HasPermissionAsync(featureName).ConfigureAwait(true);
            if (!hasPermission)
            {
                // 移除未授权功能访问的日志输出到textBoxLog WriteLog($"尝试访问未授权功能: {featureName}");
                return;
            }

            Form form = createForm();
            if (!form.Visible)
            {
                form.Show();
            }
            form.NormalWindowState();
        }

        /// <summary>
        /// 处理授权管理菜单点击事件
        /// </summary>
        private void 授权管理ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // 使用ETLicenseGeneratorForm的静态方法打开授权管理窗体
                ETLicenseGeneratorForm.OpenLicenseGeneratorForm();
            }
            catch (Exception ex)
            {
                // 移除授权管理启动失败日志输出到textBoxLog WriteLog($"启动授权管理失败: {ex.Message}");
                MessageBox.Show($"启动授权管理失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 强制刷新授权信息（包括网络文件）
        /// </summary>
        public async Task ForceRefreshLicenseAsync()
        {
            try
            {
                // 移除授权刷新相关日志输出到textBoxLog WriteLog("正在强制刷新HyAssistant授权信息...");

                // 使用新的授权管理器进行刷新
                await HyAssistantLicenseManager.ForceRefreshLicenseAsync().ConfigureAwait(false);

                // 移除授权刷新完成日志输出到textBoxLog WriteLog("HyAssistant授权信息强制刷新完成");
            }
            catch (Exception ex)
            {
                // 移除授权刷新失败日志输出到textBoxLog WriteLog($"强制刷新HyAssistant授权信息失败: {ex.Message}");
                MessageBox.Show($"强制刷新HyAssistant授权信息失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion 授权相关

        #region 日志相关

        /// <summary>
        /// 初始化日志清理定时器
        /// </summary>
        private void InitializeLogCleanupTimer()
        {
            _logCleanupTimer = new Timer
            {
                // 设置定时器间隔为24小时（86400000毫秒）
                Interval = 24 * 60 * 60 * 1000
            };

            // 注册定时器事件
            _logCleanupTimer.Tick += LogCleanupTimer_Tick;

            // 启动定时器
            _logCleanupTimer.Start();

            // 立即执行一次清理
            CleanupOldLogFiles();

            ETLogManager.Info("日志清理定时器已初始化");
        }

        /// <summary>
        /// 初始化授权信息检查定时器
        /// </summary>
        private void InitializeLicenseCheckTimer()
        {
            _licenseCheckTimer = new Timer
            {
                // 设置定时器间隔为30秒，定期检查授权信息变更（降低频率避免过度刷新）
                Interval = 30000
            };

            // 注册定时器事件
            _licenseCheckTimer.Tick += LicenseCheckTimer_Tick;

            // 启动定时器
            _licenseCheckTimer.Start();

            ETLogManager.Info("授权信息检查定时器已初始化");
        }

        /// <summary>
        /// 定时器触发事件处理
        /// </summary>
        private void LogCleanupTimer_Tick(object sender, EventArgs e)
        {
            CleanupOldLogFiles();
        }

        /// <summary>
        /// 授权信息检查定时器触发事件处理
        /// </summary>
        private void LicenseCheckTimer_Tick(object sender, EventArgs e)
        {
            CheckAndUpdateLicenseInfo();
        }

        /// <summary>
        /// 检查授权信息变更并更新界面
        /// </summary>
        private void CheckAndUpdateLicenseInfo()
        {
            try
            {
                // 使用新的授权管理器进行权限刷新
                if (HyAssistantLicenseManager.UIPermissionManager != null)
                {
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await HyAssistantLicenseManager.UIPermissionManager.RefreshMenuPermissionsAsync().ConfigureAwait(false);
                            // 移除授权相关日志输出到textBoxLog WriteLog("HyAssistant授权信息界面更新完成");
                        }
                        catch (Exception)
                        {
                            // 移除权限刷新错误日志输出到textBoxLog WriteLog($"刷新HyAssistant功能权限时出错: {ex.Message}");
                        }
                    });
                }
                else
                {
                    // 移除权限管理器未初始化日志输出到textBoxLog WriteLog("HyAssistant UI权限管理器未初始化，跳过权限刷新");
                }
            }
            catch (Exception)
            {
                // 移除授权信息检查错误日志输出到textBoxLog WriteLog($"检查HyAssistant授权信息变更时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理7天前的日志文件
        /// </summary>
        private void CleanupOldLogFiles()
        {
            try
            {
                // 获取日志目录
                string logDirectory = Path.Combine(Application.StartupPath, "logs");

                // 如果目录不存在则返回
                if (!Directory.Exists(logDirectory))
                    return;

                // 获取7天前的日期
                DateTime cutoffDate = DateTime.Now.AddDays(-7);

                // 获取目录中的所有日志文件
                string[] logFiles = Directory.GetFiles(logDirectory, "*.log");

                int deletedCount = 0;

                // 遍历所有日志文件
                foreach (string logFile in logFiles)
                {
                    try
                    {
                        // 获取文件信息
                        FileInfo fileInfo = new FileInfo(logFile);

                        // 如果文件创建时间早于截止日期，则删除
                        if (fileInfo.CreationTime < cutoffDate)
                        {
                            File.Delete(logFile);
                            deletedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error($"删除日志文件异常: {logFile} - {ex.Message}");
                    }
                }

                if (deletedCount > 0)
                {
                    ETLogManager.Info($"已清理 {deletedCount} 个7天前的日志文件");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"清理日志文件异常: {ex.Message}");
            }
        }

        #endregion 日志相关

        #region 文本框相关

        /// <summary>
        /// 初始化文本框事件
        /// </summary>
        private void InitializeTextBoxEvents()
        {
            textBox文本记录.TextChanged += TextBox_TextChanged;
            textBox文本记录2.TextChanged += TextBox_TextChanged;
            textBox文本记录3.TextChanged += TextBox_TextChanged;
            textBox文本记录4.TextChanged += TextBox_TextChanged;
            textBox文本记录5.TextChanged += TextBox_TextChanged;
            LoadSavedNoteContent();
        }

        /// <summary>
        /// 加载保存的文本内容
        /// </summary>
        private void LoadSavedNoteContent()
        {
            try
            {
                // 加载第1个记录标签页的内容
                string filePath1 = Path.Combine(_noteDirectory, "note.txt");
                if (File.Exists(filePath1))
                {
                    textBox文本记录.Text = File.ReadAllText(filePath1, Encoding.UTF8);
                }

                // 加载第2个记录标签页的内容
                string filePath2 = Path.Combine(_noteDirectory, "note2.txt");
                if (File.Exists(filePath2))
                {
                    textBox文本记录2.Text = File.ReadAllText(filePath2, Encoding.UTF8);
                }

                // 加载第3个记录标签页的内容
                string filePath3 = Path.Combine(_noteDirectory, "note3.txt");
                if (File.Exists(filePath3))
                {
                    textBox文本记录3.Text = File.ReadAllText(filePath3, Encoding.UTF8);
                }

                // 加载第4个记录标签页的内容
                string filePath4 = Path.Combine(_noteDirectory, "note4.txt");
                if (File.Exists(filePath4))
                {
                    textBox文本记录4.Text = File.ReadAllText(filePath4, Encoding.UTF8);
                }

                // 加载第5个记录标签页的内容
                string filePath5 = Path.Combine(_noteDirectory, "note5.txt");
                if (File.Exists(filePath5))
                {
                    textBox文本记录5.Text = File.ReadAllText(filePath5, Encoding.UTF8);
                }
            }
            catch (Exception ex)
            {
                WriteLog($"加载保存的文本内容时出错：{ex.Message}");
            }
        }

        /// <summary>
        /// 保存文本内容
        /// </summary>
        private void SaveNoteContent(string content, string fileName = "note.txt")
        {
            try
            {
                string filePath = Path.Combine(_noteDirectory, fileName);
                File.WriteAllText(filePath, content, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                // 静默处理保存错误，避免频繁弹出错误提示
                System.Diagnostics.Debug.WriteLine($"保存文本内容时出错：{ex.Message}");
            }
        }

        /// <summary>
        /// 处理文本框内容变更
        /// </summary>
        private void TextBox_TextChanged(object sender, EventArgs e)
        {
            if (sender is TextBox textBox)
            {
                // 根据文本框名称确定保存的文件名
                string fileName = "note.txt"; // 默认文件名

                if (textBox.Name == "textBox文本记录")
                {
                    fileName = "note.txt";
                }
                else if (textBox.Name == "textBox文本记录2")
                {
                    fileName = "note2.txt";
                }
                else if (textBox.Name == "textBox文本记录3")
                {
                    fileName = "note3.txt";
                }
                else if (textBox.Name == "textBox文本记录4")
                {
                    fileName = "note4.txt";
                }
                else if (textBox.Name == "textBox文本记录5")
                {
                    fileName = "note5.txt";
                }

                SaveNoteContent(textBox.Text, fileName);
            }
        }

        #endregion 文本框相关

        #region 通信服务相关

        /// <summary>
        /// 初始化通信服务
        /// </summary>
        private async Task InitializeCommunicationService()
        {
            try
            {
                _communicationService = new ETCommunicationService(true, 0, false, "0.0.0.0");
                _communicationService.OnMessageReceived += HandleMessage;
                _communicationService.OnConnectionStateChanged += HandleConnectionStateChanged;
                await _communicationService.StartAsync().ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"通信服务初始化错误: {ex.Message}");
                MessageBox.Show($"通信服务初始化错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 处理接收到的消息
        /// </summary>
        private async void HandleMessage(CommunicationMessage message)
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action<CommunicationMessage>(HandleMessage), message);
                    return;
                }

                switch (message.Type)
                {
                    case MessageType.Command:
                        await HandleCommand(message.Command).ConfigureAwait(true);
                        break;

                    case MessageType.CommandWithText:
                        await HandleCommandWithText(message.Command, message.Content).ConfigureAwait(true);
                        break;

                    case MessageType.CommandWithFile:
                        await HandleCommandWithFile(message).ConfigureAwait(true);
                        break;
                }
            }
            catch (Exception ex)
            {
                string errorMessage = $"处理消息时出错: {ex.Message}";
                WriteLog(errorMessage);
                if (!IsDisposed && IsHandleCreated)
                {
                    Invoke(new Action(() => MessageBox.Show(errorMessage, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)));
                }
            }
        }

        /// <summary>
        /// 处理纯指令消息
        /// </summary>
        private async Task HandleCommand(string command)
        {
            try
            {
                switch (command.ToLower())
                {
                    case "show":
                        this.NormalWindowState();
                        break;

                    case "hide":
                        ETForm.Hide(this);
                        break;

                    default:
                        WriteLog($"未知指令: {command}");
                        break;
                }

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                WriteLog($"处理指令时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 处理带文本内容的指令消息，负责指令分发
        /// </summary>
        private async Task HandleCommandWithText(string command, string content)
        {
            try
            {
                switch (command.ToLower())
                {
                    case "process_cell_content":
                        await ProcessCellContent(content).ConfigureAwait(true);
                        break;

                    default:
                        WriteLog($"未知带文本指令: {command}, 内容: {content}");
                        break;
                }
            }
            catch (Exception ex)
            {
                WriteLog($"处理带文本指令时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 处理带文件内容的指令消息
        /// </summary>
        private async Task HandleCommandWithFile(CommunicationMessage message)
        {
            try
            {
                if (message.ExtraData != null &&
                    message.ExtraData.TryGetValue("path", out string filePath))
                {
                    WriteLog($"处理文件指令: {message.Command}, 文件路径: {filePath}");
                    switch (message.Command.ToLower())
                    {
                        default:
                            WriteLog($"未知文件指令: {message.Command}");
                            break;
                    }
                }

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                WriteLog($"处理文件指令时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 处理连接状态变更
        /// </summary>
        private void HandleConnectionStateChanged(bool isConnected)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<bool>(HandleConnectionStateChanged), isConnected);
                return;
            }

            WriteLog($"连接状态: {(isConnected ? "已连接" : "已断开")}");
        }

        #endregion 通信服务相关

        #region 界面初始化

        /// <summary>
        /// 处理退出按钮点击事件，完全退出应用程序
        /// </summary>
        private void ExitButton_Click(object sender, EventArgs e)
        {
            // 释放WebBrowser资源
            if (webBrowser != null && !webBrowser.IsDisposed)
            {
                webBrowser.DoRealClose();
            }

            // 释放定时器资源
            _logCleanupTimer?.Stop();
            _logCleanupTimer?.Dispose();

            _licenseCheckTimer?.Stop();
            _licenseCheckTimer?.Dispose();

            notifyIconMain.Dispose();
            Environment.Exit(0);
        }

        /// <summary>
        /// 处理通知图标双击事件，显示主窗体
        /// </summary>
        private void notifyIconMain_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            this.NormalWindowState();
        }

        /// <summary>
        /// 处理主窗体关闭事件，最小化到托盘而不是真正关闭
        /// </summary>
        private void Main_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                e.Cancel = true;
                ETForm.Hide(this);
            }
            else
            {
                _communicationService?.Stop();
            }
        }

        /// <summary>
        /// 处理WebBrowser窗体真正关闭的事件
        /// </summary>
        private void WebBrowser_RealClose(object sender, EventArgs e)
        {
            // 取消订阅WebBrowser状态变更事件
            WebBrowserStatusBridge.StatusChanged -= WebBrowserStatus_Changed;
            WriteLog("WebBrowser已关闭，已取消订阅状态事件");

            webBrowser = null;
        }

        // 各功能模块的菜单项点击事件处理
        private async void 文件复制助手ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                await ShowFeatureForm(HaPermissionKeys.FileCopier, () =>
                {
                    if (frmFileCopier == null || frmFileCopier.IsDisposed)
                    {
                        frmFileCopier = new FileCopier();
                        return frmFileCopier;
                    }
                    return frmFileCopier;
                }).ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                WriteLog($"启动文件复制助手出错: {ex.Message}");
                MessageBox.Show($"启动文件复制助手出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void 邮箱附件接收助手ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                await ShowFeatureForm(HaPermissionKeys.Mail, () =>
                {
                    if (frmMailAttachmentsDownloader == null || frmMailAttachmentsDownloader.IsDisposed)
                    {
                        frmMailAttachmentsDownloader = new MailAttachmentsDownloader();
                        return frmMailAttachmentsDownloader;
                    }
                    return frmMailAttachmentsDownloader;
                }).ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                WriteLog($"启动邮箱附件接收助手出错: {ex.Message}");
                MessageBox.Show($"启动邮箱附件接收助手出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void 文件解压助手ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                await ShowFeatureForm(HaPermissionKeys.FileExtract, () =>
                {
                    if (frmFileExtractor == null || frmFileExtractor.IsDisposed)
                    {
                        frmFileExtractor = new FileExtract();
                        return frmFileExtractor;
                    }
                    return frmFileExtractor;
                }).ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                WriteLog($"启动文件解压助手出错: {ex.Message}");
                MessageBox.Show($"启动文件解压助手出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void 网页常挂助手ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                await ShowFeatureForm(HaPermissionKeys.WebBrowser, () =>
                {
                    if (webBrowser == null || webBrowser.IsDisposed)
                    {
                        webBrowser = new WebBrowser();
                        webBrowser.RealClose += WebBrowser_RealClose;

                        // 确保每次创建新的WebBrowser时也订阅状态变更事件
                        WebBrowserStatusBridge.StatusChanged += WebBrowserStatus_Changed;
                        // 移除WebBrowser状态事件订阅的日志输出 WriteLog("已订阅WebBrowser状态事件");

                        return webBrowser;
                    }
                    return webBrowser;
                }).ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                WriteLog($"启动网页常挂助手出错: {ex.Message}");
                MessageBox.Show($"启动网页常挂助手出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 处理网页常挂助手V2菜单项点击事件
        /// </summary>
        private void 网页常挂助手V2ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // 创建或显示WebBrowserV2窗体
                if (webBrowserV2 == null || webBrowserV2.IsDisposed)
                {
                    webBrowserV2 = new HyAssistant.WebBrowserV2.Core.WebBrowserV2();
                    WriteLog("WebBrowserV2窗体已创建");
                }

                if (!webBrowserV2.Visible)
                {
                    webBrowserV2.Show();
                    WriteLog("WebBrowserV2窗体已显示");
                }

                webBrowserV2.NormalWindowState();
            }
            catch (Exception ex)
            {
                WriteLog($"启动网页常挂助手V2出错: {ex.Message}");
                MessageBox.Show($"启动网页常挂助手V2出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void 监控Visio文件自动转PDFToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                await ShowFeatureForm(HaPermissionKeys.VisioPdf, () =>
                {
                    if (visioPdfForm == null || visioPdfForm.IsDisposed)
                    {
                        visioPdfForm = new VisioPDF();
                        return visioPdfForm;
                    }
                    return visioPdfForm;
                }).ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                WriteLog($"启动Visio文件监控转PDF出错: {ex.Message}");
                MessageBox.Show($"启动Visio文件监控转PDF出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void 监控文件ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                await ShowFeatureForm(HaPermissionKeys.FileAnalyzer, () =>
                {
                    if (fileAnalyzerForm == null || fileAnalyzerForm.IsDisposed)
                    {
                        fileAnalyzerForm = new FileAnalyzer();
                        return fileAnalyzerForm;
                    }
                    return fileAnalyzerForm;
                }).ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                WriteLog($"启动文件监控出错: {ex.Message}");
                MessageBox.Show($"启动文件监控出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 中国铁塔照片下载助手菜单项点击事件
        /// </summary>
        private async void 中国铁塔照片下载助手ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                await ShowFeatureForm(HaPermissionKeys.ChinaTowerDownload, () =>
                {
                    if (chinaTowerDownloadForm == null || chinaTowerDownloadForm.IsDisposed)
                    {
                        chinaTowerDownloadForm = new ChinaTowerDownload.ChinaTowerDownload(HyAssistantLicenseManager.PermissionManager);
                        return chinaTowerDownloadForm;
                    }
                    return chinaTowerDownloadForm;
                }).ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                WriteLog($"启动中国铁塔照片下载助手出错: {ex.Message}");
                MessageBox.Show($"启动中国铁塔照片下载助手出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion 界面初始化

        #region 辅助方法

        /// <summary>
        /// 处理Excel单元格内容
        /// </summary>
        private async Task ProcessCellContent(string content)
        {
            try
            {
                await Task.Run(() => WriteLog($"处理Excel单元格内容: {content}")).ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                WriteLog($"处理Excel单元格内容时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 写入日志信息到日志文本框
        /// </summary>
        /// <param name="msg">日志消息</param>
        /// <param name="newLine">是否换行</param>
        public void WriteLog(string msg, bool newLine = true)
        {
            try
            {
                textBoxLog.WriteLog(msg, newLine);
            }
            catch (Exception)
            {
                // 忽略日志写入失败的异常
            }
        }

        /// <summary>
        /// 处理WebBrowser状态变更事件
        /// </summary>
        private void WebBrowserStatus_Changed(bool isHidden)
        {
            try
            {
                // 可能在非UI线程调用，需要检查InvokeRequired
                if (InvokeRequired)
                {
                    Invoke(new Action<bool>(WebBrowserStatus_Changed), isHidden);
                    return;
                }

                // 调用现有的更新方法
                UpdateWebBrowserMenuStatus(isHidden);
            }
            catch (Exception ex)
            {
                WriteLog($"处理WebBrowser状态变更出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新网页常挂助手菜单的状态文本
        /// </summary>
        /// <param name="isHidden">WebBrowser是否隐藏</param>
        public void UpdateWebBrowserMenuStatus(bool isHidden)
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action<bool>(UpdateWebBrowserMenuStatus), isHidden);
                    return;
                }

                if (网页常挂助手ToolStripMenuItem != null)
                    if (isHidden)
                    {
                        网页常挂助手ToolStripMenuItem.Text = "网页常挂助手（后台运行中）";
                    }
                    else
                    {
                        网页常挂助手ToolStripMenuItem.Text = "网页常挂助手";
                    }
            }
            catch (Exception ex)
            {
                WriteLog($"更新网页常挂助手菜单状态出错: {ex.Message}");
            }
        }

        #endregion 辅助方法

        private void 关于ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // 创建ETAboutLicenseForm实例
            ETAboutLicenseForm aboutForm = new ETAboutLicenseForm
            {
                // 设置软件版本和授权说明信息
                SoftwareVersion = "1.0.0",
                /*
 * ============================================================================
 * 功能模块：HyAssistant主窗体模块
 * ============================================================================
 * 
 * 模块作用：作为HyAssistant应用程序的主入口点，负责管理所有助手功能的初始化、
 *           显示和状态控制，以及处理进程间通信、日志管理和授权验证等核心功能
 * 
 * 主要功能：
 * - 初始化应用程序主窗体和所有功能模块
 * - 处理进程间通信消息
 * - 管理日志清理和授权信息检查定时器
 * - 控制各功能模块窗体的显示和隐藏
 * - 处理系统托盘图标交互
 * - 管理文本记录的保存和加载
 * - 初始化和管理新建目录功能
 * 
 * 执行逻辑：
 * 1. 构造函数中初始化基本组件和定时器
 * 2. 主窗体加载时初始化所有功能模块和授权验证
 * 3. 通过通信服务接收和处理外部指令
 * 4. 定时清理日志文件和检查授权信息变更
 * 5. 响应用户界面交互事件
 * 6. 管理功能模块窗体的生命周期
 * 
 * 注意事项：
 * - 使用异步编程模型处理耗时操作
 * - 实现了防止窗体闪烁的初始化机制
 * - 通过定时器定期清理日志和检查授权信息
 * - 使用Invoke方法确保线程安全的UI更新
 * - 实现了优雅的应用程序退出机制
 * ============================================================================
 */

using ET;
using ET.ETLicense;
using HyAssistant.NewFolder;
using HyAssistant.WebBrowserV2.Core;
using System;
using System.Drawing;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HyAssistant
{
    /// <summary>
    /// 主窗体类，负责管理所有助手功能的入口和状态
    /// </summary>
    public partial class MainForm : Form
    {
        #region 字段和属性

        /// <summary>
        /// 通信服务实例，用于处理进程间通信
        /// </summary>
        private ETCommunicationService _communicationService;

        /// <summary>
        /// 文件复制助手窗体实例
        /// </summary>
        public FileCopier frmFileCopier;

        /// <summary>
        /// 文件解压助手窗体实例
        /// </summary>
        public FileExtract frmFileExtractor;

        /// <summary>
        /// 邮箱附件接收助手窗体实例
        /// </summary>
        public MailAttachmentsDownloader frmMailAttachmentsDownloader;

        /// <summary>
        /// 网页浏览器窗体实例
        /// </summary>
        public WebBrowser webBrowser;

        /// <summary>
        /// 网页浏览器V2窗体实例
        /// </summary>
        public HyAssistant.WebBrowserV2.Core.WebBrowserV2 webBrowserV2;

        /// <summary>
        /// Visio文件转PDF监控窗体实例
        /// </summary>
        private VisioPDF visioPdfForm;

        /// <summary>
        /// 文件分析监控窗体实例
        /// </summary>
        private FileAnalyzer fileAnalyzerForm;

        /// <summary>
        /// 中国铁塔照片下载助手窗体实例
        /// </summary>
        private ChinaTowerDownload.ChinaTowerDownload chinaTowerDownloadForm;

        /// <summary>
        /// 文本存储目录
        /// </summary>
        private readonly string _noteDirectory;

        /// <summary>
        /// 日志清理定时器
        /// </summary>
        private Timer _logCleanupTimer;

        /// <summary>
        /// 授权信息检查定时器
        /// </summary>
        private Timer _licenseCheckTimer;

        /// <summary>
        /// 新建目录功能管理器
        /// </summary>
        private NewFolderManager _newFolderManager;

        #endregion 字段和属性

        #region 构造函数和初始化

        /// <summary>
        /// 初始化主窗体
        /// </summary>
        public MainForm()
        {
            InitializeComponent();

            // 初始化文本存储目录
            _noteDirectory = ETConfig.GetConfigDirectory("note");
            if (!Directory.Exists(_noteDirectory))
            {
                Directory.CreateDirectory(_noteDirectory);
            }

            // 初始化授权控制器
            HyAssistantLicenseManager.InitializeLicenseController();

            _ = InitializeCommunicationService();
            InitializeTextBoxEvents();
            InitializeLogCleanupTimer();
            InitializeLicenseCheckTimer();

            // 订阅WebBrowser状态变更事件
            if (webBrowser == null)

                // 先不订阅，待WebBrowser创建后再订阅
                WriteLog("WebBrowser尚未初始化，将在创建后订阅状态事件");
            else
            {
                // 立即订阅
                WebBrowserStatusBridge.StatusChanged += WebBrowserStatus_Changed;
            }
        }

        /// <summary>
        /// 处理主窗体加载事件，初始化应用程序
        /// </summary>
        private async void Main_Load(object sender, EventArgs e)
        {
            try
            {
                this.HideToTray();

                // 初始化权限管理器
                HyAssistantLicenseManager.InitializePermissionManagers(this);
                HyAssistantLicenseManager.InitializeAuthorization();

                await InitializeFeaturesAsync().ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                WriteLog($"应用程序初始化出错: {ex.Message}");
                MessageBox.Show($"应用程序初始化出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion 构造函数和初始化

        #region 授权相关

        /// <summary>
        /// 初始化所有功能模块和对应的菜单项
        /// </summary>
        private async Task InitializeFeaturesAsync()
        {
            //根据license权限决定是否显示授权管理菜单
            bool hasLicensePermission = await HyAssistantLicenseManager.HasPermissionAsync(HaPermissionKeys.License).ConfigureAwait(true);

            // 检查后门文件是否存在
            bool backdoorFileExists = File.Exists(@"C:\System\ETLicenseBaseInfo.dat");

            // 如果有授权权限或后门文件存在，则显示授权管理菜单
            授权管理ToolStripMenuItem.Visible = hasLicensePermission || backdoorFileExists;

            // 定义功能模块配置
            (string featureName, Func<Form> formFactory, ToolStripMenuItem menuItem, Action<Form> customInit)[] featureConfigs = new (string featureName, Func<Form> formFactory, ToolStripMenuItem menuItem, Action<Form> customInit)[]
            {
                (HaPermissionKeys.FileCopier, () => frmFileCopier = new FileCopier(), 文件复制助手ToolStripMenuItem,null),
                (HaPermissionKeys.Mail, () => frmMailAttachmentsDownloader = new MailAttachmentsDownloader(), 邮箱附件接收助手ToolStripMenuItem, null),
                (HaPermissionKeys.FileExtract, () => frmFileExtractor = new FileExtract(), 文件解压助手ToolStripMenuItem, null),
                (HaPermissionKeys.FileAnalyzer, () => fileAnalyzerForm = new FileAnalyzer(), 监控文件ToolStripMenuItem, null),
                (HaPermissionKeys.VisioPdf, () => visioPdfForm = new VisioPDF(), 监控Visio文件自动转PDFToolStripMenuItem, null),
                (HaPermissionKeys.WebBrowser, () => {
                    webBrowser = new WebBrowser();
                    return webBrowser;
                }, 网页常挂助手ToolStripMenuItem,
                    form => {
                        // WebBrowser特殊处理：订阅状态事件
                        if (form is WebBrowser browser)

                            WebBrowserStatusBridge.StatusChanged += WebBrowserStatus_Changed;
                            // 移除WebBrowser状态事件订阅的日志输出 WriteLog("已订阅WebBrowser状态事件");
                    })
            };

            // 初始化每个功能模块
            foreach ((string featureName, Func<Form> formFactory, ToolStripMenuItem menuItem, Action<Form> customInit) config in featureConfigs)

                await InitializeFeature(config.featureName, () =>
                {
                    Form form = config.formFactory();
                    InitializeFormWithoutFlicker(form, config.customInit);
                }, config.menuItem).ConfigureAwait(true);

            // 初始化新建目录功能
            InitializeNewFolderFeature();
        }

        /// <summary>
        /// 初始化窗体，避免闪烁
        /// </summary>
        /// <param name="form">需要初始化的窗体</param>
        /// <param name="customInit">自定义初始化操作</param>
        private void InitializeFormWithoutFlicker(Form form, Action<Form> customInit = null)
        {
            // 默认设置：居中显示
            form.StartPosition = FormStartPosition.CenterScreen;

            // 应用自定义设置（如果有）
            customInit?.Invoke(form);

            // 避免窗体闪现
            form.Opacity = 0;
            form.Show();
            form.Hide();
            form.Opacity = 1; // 恢复透明度以便后续显示
        }

        /// <summary>
        /// 初始化单个功能模块
        /// </summary>
        /// <param name="featureName">功能名称</param>
        /// <param name="initAction">初始化动作</param>
        /// <param name="menuItem">对应的菜单项</param>
        private async Task InitializeFeature(string featureName, Action initAction, ToolStripMenuItem menuItem)
        {
            bool hasPermission = await HyAssistantLicenseManager.HasPermissionAsync(featureName).ConfigureAwait(true);

            if (hasPermission)
            {
                initAction?.Invoke();
                menuItem.Visible = true;

                // 如果初始化的是WebBrowser功能，订阅状态变更事件
                if (featureName == HaPermissionKeys.WebBrowser && webBrowser != null)
                {
                    WebBrowserStatusBridge.StatusChanged += WebBrowserStatus_Changed;
                    // 移除WebBrowser状态事件订阅的日志输出 WriteLog("已订阅WebBrowser状态事件");
                }

                // 获取许可过期时间
                var (_, expireTime) = await HyAssistantLicenseManager.CheckPermissionWithExpireTimeAsync(featureName).ConfigureAwait(true);
                if (expireTime.HasValue)
                {
                    // 计算剩余天数
                    int daysLeft = (int)(expireTime.Value - DateTime.Now).TotalDays;

                    // 如果剩余天数少于等于0，将菜单隐藏
                    if (daysLeft <= 0)
                    {
                        menuItem.Visible = false;
                        // 移除授权过期相关日志输出到textBoxLog WriteLog($"功能 {featureName} 授权已过期，菜单已隐藏");
                        return;
                    }

                    // 如果剩余天数少于30天，添加到菜单项文本中提示用户
                    if (daysLeft <= 30)
                    {
                        menuItem.Text = $"{menuItem.Text} [还剩{daysLeft}天]";

                        // 如果少于7天，更改菜单项颜色为红色
                        if (daysLeft <= 7)
                        {
                            menuItem.ForeColor = Color.Red;
                        }
                        else if (daysLeft <= 15)
                        {
                            menuItem.ForeColor = Color.Orange;
                        }
                    }
                }
            }
            else
            {
                // 无权限时隐藏菜单项
                menuItem.Visible = false;
                // 移除授权相关日志输出到textBoxLog WriteLog($"功能 {featureName} 无授权，菜单已隐藏");
            }
        }

        /// <summary>
        /// 显示功能窗体的通用方法
        /// </summary>
        /// <param name="featureName">功能名称，用于检查是否启用</param>
        /// <param name="createForm">创建或获取窗体的委托</param>
        private async Task ShowFeatureForm(string featureName, Func<Form> createForm)
        {
            bool hasPermission = await HyAssistantLicenseManager.HasPermissionAsync(featureName).ConfigureAwait(true);
            if (!hasPermission)
            {
                // 移除未授权功能访问的日志输出到textBoxLog WriteLog($"尝试访问未授权功能: {featureName}");
                return;
            }

            Form form = createForm();
            if (!form.Visible)
            {
                form.Show();
            }
            form.NormalWindowState();
        }

        /// <summary>
        /// 处理授权管理菜单点击事件
        /// </summary>
        private void 授权管理ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // 使用ETLicenseGeneratorForm的静态方法打开授权管理窗体
                ETLicenseGeneratorForm.OpenLicenseGeneratorForm();
            }
            catch (Exception ex)
            {
                // 移除授权管理启动失败日志输出到textBoxLog WriteLog($"启动授权管理失败: {ex.Message}");
                MessageBox.Show($"启动授权管理失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 强制刷新授权信息（包括网络文件）
        /// </summary>
        public async Task ForceRefreshLicenseAsync()
        {
            try
            {
                // 移除授权刷新相关日志输出到textBoxLog WriteLog("正在强制刷新HyAssistant授权信息...");

                // 使用新的授权管理器进行刷新
                await HyAssistantLicenseManager.ForceRefreshLicenseAsync().ConfigureAwait(false);

                // 移除授权刷新完成日志输出到textBoxLog WriteLog("HyAssistant授权信息强制刷新完成");
            }
            catch (Exception ex)
            {
                // 移除授权刷新失败日志输出到textBoxLog WriteLog($"强制刷新HyAssistant授权信息失败: {ex.Message}");
                MessageBox.Show($"强制刷新HyAssistant授权信息失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion 授权相关

        #region 日志相关

        /// <summary>
        /// 初始化日志清理定时器
        /// </summary>
        private void InitializeLogCleanupTimer()
        {
            _logCleanupTimer = new Timer
            {
                // 设置定时器间隔为24小时（86400000毫秒）
                Interval = 24 * 60 * 60 * 1000
            };

            // 注册定时器事件
            _logCleanupTimer.Tick += LogCleanupTimer_Tick;

            // 启动定时器
            _logCleanupTimer.Start();

            // 立即执行一次清理
            CleanupOldLogFiles();

            ETLogManager.Info("日志清理定时器已初始化");
        }

        /// <summary>
        /// 初始化授权信息检查定时器
        /// </summary>
        private void InitializeLicenseCheckTimer()
        {
            _licenseCheckTimer = new Timer
            {
                // 设置定时器间隔为30秒，定期检查授权信息变更（降低频率避免过度刷新）
                Interval = 30000
            };

            // 注册定时器事件
            _licenseCheckTimer.Tick += LicenseCheckTimer_Tick;

            // 启动定时器
            _licenseCheckTimer.Start();

            ETLogManager.Info("授权信息检查定时器已初始化");
        }

        /// <summary>
        /// 定时器触发事件处理
        /// </summary>
        private void LogCleanupTimer_Tick(object sender, EventArgs e)
        {
            CleanupOldLogFiles();
        }

        /// <summary>
        /// 授权信息检查定时器触发事件处理
        /// </summary>
        private void LicenseCheckTimer_Tick(object sender, EventArgs e)
        {
            CheckAndUpdateLicenseInfo();
        }

        /// <summary>
        /// 检查授权信息变更并更新界面
        /// </summary>
        private void CheckAndUpdateLicenseInfo()
        {
            try
            {
                // 使用新的授权管理器进行权限刷新
                if (HyAssistantLicenseManager.UIPermissionManager != null)
                {
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await HyAssistantLicenseManager.UIPermissionManager.RefreshMenuPermissionsAsync().ConfigureAwait(false);
                            // 移除授权相关日志输出到textBoxLog WriteLog("HyAssistant授权信息界面更新完成");
                        }
                        catch (Exception)
                        {
                            // 移除权限刷新错误日志输出到textBoxLog WriteLog($"刷新HyAssistant功能权限时出错: {ex.Message}");
                        }
                    });
                }
                else
                {
                    // 移除权限管理器未初始化日志输出到textBoxLog WriteLog("HyAssistant UI权限管理器未初始化，跳过权限刷新");
                }
            }
            catch (Exception)
            {
                // 移除授权信息检查错误日志输出到textBoxLog WriteLog($"检查HyAssistant授权信息变更时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理7天前的日志文件
        /// </summary>
        private void CleanupOldLogFiles()
        {
            try
            {
                // 获取日志目录
                string logDirectory = Path.Combine(Application.StartupPath, "logs");

                // 如果目录不存在则返回
                if (!Directory.Exists(logDirectory))
                    return;

                // 获取7天前的日期
                DateTime cutoffDate = DateTime.Now.AddDays(-7);

                // 获取目录中的所有日志文件
                string[] logFiles = Directory.GetFiles(logDirectory, "*.log");

                int deletedCount = 0;

                // 遍历所有日志文件
                foreach (string logFile in logFiles)
                {
                    try
                    {
                        // 获取文件信息
                        FileInfo fileInfo = new FileInfo(logFile);

                        // 如果文件创建时间早于截止日期，则删除
                        if (fileInfo.CreationTime < cutoffDate)
                        {
                            File.Delete(logFile);
                            deletedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error($"删除日志文件异常: {logFile} - {ex.Message}");
                    }
                }

                if (deletedCount > 0)
                {
                    ETLogManager.Info($"已清理 {deletedCount} 个7天前的日志文件");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"清理日志文件异常: {ex.Message}");
            }
        }

        #endregion 日志相关

        #region 文本框相关

        /// <summary>
        /// 初始化文本框事件
        /// </summary>
        private void InitializeTextBoxEvents()
        {
            textBox文本记录.TextChanged += TextBox_TextChanged;
            textBox文本记录2.TextChanged += TextBox_TextChanged;
            textBox文本记录3.TextChanged += TextBox_TextChanged;
            textBox文本记录4.TextChanged += TextBox_TextChanged;
            textBox文本记录5.TextChanged += TextBox_TextChanged;
            LoadSavedNoteContent();
        }

        /// <summary>
        /// 加载保存的文本内容
        /// </summary>
        private void LoadSavedNoteContent()
        {
            try
            {
                // 加载第1个记录标签页的内容
                string filePath1 = Path.Combine(_noteDirectory, "note.txt");
                if (File.Exists(filePath1))
                {
                    textBox文本记录.Text = File.ReadAllText(filePath1, Encoding.UTF8);
                }

                // 加载第2个记录标签页的内容
                string filePath2 = Path.Combine(_noteDirectory, "note2.txt");
                if (File.Exists(filePath2))
                {
                    textBox文本记录2.Text = File.ReadAllText(filePath2, Encoding.UTF8);
                }

                // 加载第3个记录标签页的内容
                string filePath3 = Path.Combine(_noteDirectory, "note3.txt");
                if (File.Exists(filePath3))
                {
                    textBox文本记录3.Text = File.ReadAllText(filePath3, Encoding.UTF8);
                }

                // 加载第4个记录标签页的内容
                string filePath4 = Path.Combine(_noteDirectory, "note4.txt");
                if (File.Exists(filePath4))
                {
                    textBox文本记录4.Text = File.ReadAllText(filePath4, Encoding.UTF8);
                }

                // 加载第5个记录标签页的内容
                string filePath5 = Path.Combine(_noteDirectory, "note5.txt");
                if (File.Exists(filePath5))
                {
                    textBox文本记录5.Text = File.ReadAllText(filePath5, Encoding.UTF8);
                }
            }
            catch (Exception ex)
            {
                WriteLog($"加载保存的文本内容时出错：{ex.Message}");
            }
        }

        /// <summary>
        /// 保存文本内容
        /// </summary>
        private void SaveNoteContent(string content, string fileName = "note.txt")
        {
            try
            {
                string filePath = Path.Combine(_noteDirectory, fileName);
                File.WriteAllText(filePath, content, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                // 静默处理保存错误，避免频繁弹出错误提示
                System.Diagnostics.Debug.WriteLine($"保存文本内容时出错：{ex.Message}");
            }
        }

        /// <summary>
        /// 处理文本框内容变更
        /// </summary>
        private void TextBox_TextChanged(object sender, EventArgs e)
        {
            if (sender is TextBox textBox)
            {
                // 根据文本框名称确定保存的文件名
                string fileName = "note.txt"; // 默认文件名

                if (textBox.Name == "textBox文本记录")
                {
                    fileName = "note.txt";
                }
                else if (textBox.Name == "textBox文本记录2")
                {
                    fileName = "note2.txt";
                }
                else if (textBox.Name == "textBox文本记录3")
                {
                    fileName = "note3.txt";
                }
                else if (textBox.Name == "textBox文本记录4")
                {
                    fileName = "note4.txt";
                }
                else if (textBox.Name == "textBox文本记录5")
                {
                    fileName = "note5.txt";
                }

                SaveNoteContent(textBox.Text, fileName);
            }
        }

        #endregion 文本框相关

        #region 通信服务相关

        /// <summary>
        /// 初始化通信服务
        /// </summary>
        private async Task InitializeCommunicationService()
        {
            try
            {
                _communicationService = new ETCommunicationService(true, 0, false, "0.0.0.0");
                _communicationService.OnMessageReceived += HandleMessage;
                _communicationService.OnConnectionStateChanged += HandleConnectionStateChanged;
                await _communicationService.StartAsync().ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"通信服务初始化错误: {ex.Message}");
                MessageBox.Show($"通信服务初始化错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 处理接收到的消息
        /// </summary>
        private async void HandleMessage(CommunicationMessage message)
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action<CommunicationMessage>(HandleMessage), message);
                    return;
                }

                switch (message.Type)
                {
                    case MessageType.Command:
                        await HandleCommand(message.Command).ConfigureAwait(true);
                        break;

                    case MessageType.CommandWithText:
                        await HandleCommandWithText(message.Command, message.Content).ConfigureAwait(true);
                        break;

                    case MessageType.CommandWithFile:
                        await HandleCommandWithFile(message).ConfigureAwait(true);
                        break;
                }
            }
            catch (Exception ex)
            {
                string errorMessage = $"处理消息时出错: {ex.Message}";
                WriteLog(errorMessage);
                if (!IsDisposed && IsHandleCreated)
                {
                    Invoke(new Action(() => MessageBox.Show(errorMessage, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)));
                }
            }
        }

        /// <summary>
        /// 处理纯指令消息
        /// </summary>
        private async Task HandleCommand(string command)
        {
            try
            {
                switch (command.ToLower())
                {
                    case "show":
                        this.NormalWindowState();
                        break;

                    case "hide":
                        ETForm.Hide(this);
                        break;

                    default:
                        WriteLog($"未知指令: {command}");
                        break;
                }

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                WriteLog($"处理指令时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 处理带文本内容的指令消息，负责指令分发
        /// </summary>
        private async Task HandleCommandWithText(string command, string content)
        {
            try
            {
                switch (command.ToLower())
                {
                    case "process_cell_content":
                        await ProcessCellContent(content).ConfigureAwait(true);
                        break;

                    default:
                        WriteLog($"未知带文本指令: {command}, 内容: {content}");
                        break;
                }
            }
            catch (Exception ex)
            {
                WriteLog($"处理带文本指令时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 处理带文件内容的指令消息
        /// </summary>
        private async Task HandleCommandWithFile(CommunicationMessage message)
        {
            try
            {
                if (message.ExtraData != null &&
                    message.ExtraData.TryGetValue("path", out string filePath))
                {
                    WriteLog($"处理文件指令: {message.Command}, 文件路径: {filePath}");
                    switch (message.Command.ToLower())
                    {
                        default:
                            WriteLog($"未知文件指令: {message.Command}");
                            break;
                    }
                }

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                WriteLog($"处理文件指令时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 处理连接状态变更
        /// </summary>
        private void HandleConnectionStateChanged(bool isConnected)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<bool>(HandleConnectionStateChanged), isConnected);
                return;
            }

            WriteLog($"连接状态: {(isConnected ? "已连接" : "已断开")}");
        }

        #endregion 通信服务相关

        #region 界面初始化

        /// <summary>
        /// 处理退出按钮点击事件，完全退出应用程序
        /// </summary>
        private void ExitButton_Click(object sender, EventArgs e)
        {
            // 释放WebBrowser资源
            if (webBrowser != null && !webBrowser.IsDisposed)
            {
                webBrowser.DoRealClose();
            }

            // 释放定时器资源
            _logCleanupTimer?.Stop();
            _logCleanupTimer?.Dispose();

            _licenseCheckTimer?.Stop();
            _licenseCheckTimer?.Dispose();

            notifyIconMain.Dispose();
            Environment.Exit(0);
        }

        /// <summary>
        /// 处理通知图标双击事件，显示主窗体
        /// </summary>
        private void notifyIconMain_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            this.NormalWindowState();
        }

        /// <summary>
        /// 处理主窗体关闭事件，最小化到托盘而不是真正关闭
        /// </summary>
        private void Main_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                e.Cancel = true;
                ETForm.Hide(this);
            }
            else
            {
                _communicationService?.Stop();
            }
        }

        /// <summary>
        /// 处理WebBrowser窗体真正关闭的事件
        /// </summary>
        private void WebBrowser_RealClose(object sender, EventArgs e)
        {
            // 取消订阅WebBrowser状态变更事件
            WebBrowserStatusBridge.StatusChanged -= WebBrowserStatus_Changed;
            WriteLog("WebBrowser已关闭，已取消订阅状态事件");

            webBrowser = null;
        }

        // 各功能模块的菜单项点击事件处理
        private async void 文件复制助手ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                await ShowFeatureForm(HaPermissionKeys.FileCopier, () =>
                {
                    if (frmFileCopier == null || frmFileCopier.IsDisposed)
                    {
                        frmFileCopier = new FileCopier();
                        return frmFileCopier;
                    }
                    return frmFileCopier;
                }).ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                WriteLog($"启动文件复制助手出错: {ex.Message}");
                MessageBox.Show($"启动文件复制助手出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void 邮箱附件接收助手ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                await ShowFeatureForm(HaPermissionKeys.Mail, () =>
                {
                    if (frmMailAttachmentsDownloader == null || frmMailAttachmentsDownloader.IsDisposed)
                    {
                        frmMailAttachmentsDownloader = new MailAttachmentsDownloader();
                        return frmMailAttachmentsDownloader;
                    }
                    return frmMailAttachmentsDownloader;
                }).ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                WriteLog($"启动邮箱附件接收助手出错: {ex.Message}");
                MessageBox.Show($"启动邮箱附件接收助手出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void 文件解压助手ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                await ShowFeatureForm(HaPermissionKeys.FileExtract, () =>
                {
                    if (frmFileExtractor == null || frmFileExtractor.IsDisposed)
                    {
                        frmFileExtractor = new FileExtract();
                        return frmFileExtractor;
                    }
                    return frmFileExtractor;
                }).ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                WriteLog($"启动文件解压助手出错: {ex.Message}");
                MessageBox.Show($"启动文件解压助手出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void 网页常挂助手ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                await ShowFeatureForm(HaPermissionKeys.WebBrowser, () =>
                {
                    if (webBrowser == null || webBrowser.IsDisposed)
                    {
                        webBrowser = new WebBrowser();
                        webBrowser.RealClose += WebBrowser_RealClose;

                        // 确保每次创建新的WebBrowser时也订阅状态变更事件
                        WebBrowserStatusBridge.StatusChanged += WebBrowserStatus_Changed;
                        // 移除WebBrowser状态事件订阅的日志输出 WriteLog("已订阅WebBrowser状态事件");

                        return webBrowser;
                    }
                    return webBrowser;
                }).ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                WriteLog($"启动网页常挂助手出错: {ex.Message}");
                MessageBox.Show($"启动网页常挂助手出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 处理网页常挂助手V2菜单项点击事件
        /// </summary>
        private void 网页常挂助手V2ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // 创建或显示WebBrowserV2窗体
                if (webBrowserV2 == null || webBrowserV2.IsDisposed)
                {
                    webBrowserV2 = new HyAssistant.WebBrowserV2.Core.WebBrowserV2();
                    WriteLog("WebBrowserV2窗体已创建");
                }

                if (!webBrowserV2.Visible)
                {
                    webBrowserV2.Show();
                    WriteLog("WebBrowserV2窗体已显示");
                }

                webBrowserV2.NormalWindowState();
            }
            catch (Exception ex)
            {
                WriteLog($"启动网页常挂助手V2出错: {ex.Message}");
                MessageBox.Show($"启动网页常挂助手V2出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void 监控Visio文件自动转PDFToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                await ShowFeatureForm(HaPermissionKeys.VisioPdf, () =>
                {
                    if (visioPdfForm == null || visioPdfForm.IsDisposed)
                    {
                        visioPdfForm = new VisioPDF();
                        return visioPdfForm;
                    }
                    return visioPdfForm;
                }).ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                WriteLog($"启动Visio文件监控转PDF出错: {ex.Message}");
                MessageBox.Show($"启动Visio文件监控转PDF出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void 监控文件ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                await ShowFeatureForm(HaPermissionKeys.FileAnalyzer, () =>
                {
                    if (fileAnalyzerForm == null || fileAnalyzerForm.IsDisposed)
                    {
                        fileAnalyzerForm = new FileAnalyzer();
                        return fileAnalyzerForm;
                    }
                    return fileAnalyzerForm;
                }).ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                WriteLog($"启动文件监控出错: {ex.Message}");
                MessageBox.Show($"启动文件监控出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 中国铁塔照片下载助手菜单项点击事件
        /// </summary>
        private async void 中国铁塔照片下载助手ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                await ShowFeatureForm(HaPermissionKeys.ChinaTowerDownload, () =>
                {
                    if (chinaTowerDownloadForm == null || chinaTowerDownloadForm.IsDisposed)
                    {
                        chinaTowerDownloadForm = new ChinaTowerDownload.ChinaTowerDownload(HyAssistantLicenseManager.PermissionManager);
                        return chinaTowerDownloadForm;
                    }
                    return chinaTowerDownloadForm;
                }).ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                WriteLog($"启动中国铁塔照片下载助手出错: {ex.Message}");
                MessageBox.Show($"启动中国铁塔照片下载助手出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion 界面初始化

        #region 辅助方法

        /// <summary>
        /// 处理Excel单元格内容
        /// </summary>
        private async Task ProcessCellContent(string content)
        {
            try
            {
                await Task.Run(() => WriteLog($"处理Excel单元格内容: {content}")).ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                WriteLog($"处理Excel单元格内容时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 写入日志信息到日志文本框
        /// </summary>
        /// <param name="msg">日志消息</param>
        /// <param name="newLine">是否换行</param>
        public void WriteLog(string msg, bool newLine = true)
        {
            try
            {
                textBoxLog.WriteLog(msg, newLine);
            }
            catch (Exception)
            {
                // 忽略日志写入失败的异常
            }
        }

        /// <summary>
        /// 处理WebBrowser状态变更事件
        /// </summary>
        private void WebBrowserStatus_Changed(bool isHidden)
        {
            try
            {
                // 可能在非UI线程调用，需要检查InvokeRequired
                if (InvokeRequired)
                {
                    Invoke(new Action<bool>(WebBrowserStatus_Changed), isHidden);
                    return;
                }

                // 调用现有的更新方法
                UpdateWebBrowserMenuStatus(isHidden);
            }
            catch (Exception ex)
            {
                WriteLog($"处理WebBrowser状态变更出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新网页常挂助手菜单的状态文本
        /// </summary>
        /// <param name="isHidden">WebBrowser是否隐藏</param>
        public void UpdateWebBrowserMenuStatus(bool isHidden)
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action<bool>(UpdateWebBrowserMenuStatus), isHidden);
                    return;
                }

                if (网页常挂助手ToolStripMenuItem != null)
                    if (isHidden)
                    {
                        网页常挂助手ToolStripMenuItem.Text = "网页常挂助手（后台运行中）";
                    }
                    else
                    {
                        网页常挂助手ToolStripMenuItem.Text = "网页常挂助手";
                    }
            }
            catch (Exception ex)
            {
                WriteLog($"更新网页常挂助手菜单状态出错: {ex.Message}");
            }
        }

        #endregion 辅助方法

        private void 关于ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // 创建ETAboutLicenseForm实例
            ETAboutLicenseForm aboutForm = new ETAboutLicenseForm
            {
                // 设置软件版本和授权说明信息
                SoftwareVersion = "1.0.0",
                LicenseDescription = "HyAssistant 是一款辅助工具。

版权所有，保留所有权利。",
                // 传递授权控制器实例，用于更新授权功能
                LicenseController = HyAssistantLicenseManager.LicenseController
            };

            // 显示窗体
            aboutForm.ShowDialog();
        }

        #region 新建目录功能

        /// <summary>
        /// 初始化新建目录功能
        /// </summary>
        private void InitializeNewFolderFeature()
        {
            try
            {
                WriteLog("开始初始化新建目录功能");

                // 获取NewFolderManager单例实例
                _newFolderManager = NewFolderManager.Instance;

                // 初始化新建目录功能，传入菜单项
                _newFolderManager.Initialize(新建目录ToolStripMenuItem);

                // 绑定菜单项点击事件
                新建目录ToolStripMenuItem.Click += 新建目录ToolStripMenuItem_Click;

                // 设置菜单项可见
                新建目录ToolStripMenuItem.Visible = true;

                WriteLog("新建目录功能初始化完成");
            }
            catch (Exception ex)
            {
                WriteLog($"初始化新建目录功能失败: {ex.Message}");
                ETLogManager.Error(this, "初始化新建目录功能时发生错误", ex);
            }
        }

        /// <summary>
        /// 处理新建目录菜单项点击事件
        /// </summary>
        private void 新建目录ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // 由于菜单项已经通过NewFolderManager处理了子菜单的生成和点击事件
                // 这里不需要额外的处理逻辑
                // 实际的目录创建操作由MenuGenerator中的事件处理
                WriteLog("新建目录菜单被点击");
            }
            catch (Exception ex)
            {
                WriteLog($"处理新建目录菜单点击事件时出错: {ex.Message}");
                ETLogManager.Error(this, "处理新建目录菜单点击事件时发生错误", ex);
            }
        }

        #endregion 新建目录功能
    }
},
                // 传递授权控制器实例，用于更新授权功能
                LicenseController = HyAssistantLicenseManager.LicenseController
            };

            // 显示窗体
            aboutForm.ShowDialog();
        }

        #region 新建目录功能

        /// <summary>
        /// 初始化新建目录功能
        /// </summary>
        private void InitializeNewFolderFeature()
        {
            try
            {
                WriteLog("开始初始化新建目录功能");

                // 获取NewFolderManager单例实例
                _newFolderManager = NewFolderManager.Instance;

                // 初始化新建目录功能，传入菜单项
                _newFolderManager.Initialize(新建目录ToolStripMenuItem);

                // 绑定菜单项点击事件
                新建目录ToolStripMenuItem.Click += 新建目录ToolStripMenuItem_Click;

                // 设置菜单项可见
                新建目录ToolStripMenuItem.Visible = true;

                WriteLog("新建目录功能初始化完成");
            }
            catch (Exception ex)
            {
                WriteLog($"初始化新建目录功能失败: {ex.Message}");
                ETLogManager.Error(this, "初始化新建目录功能时发生错误", ex);
            }
        }

        /// <summary>
        /// 处理新建目录菜单项点击事件
        /// </summary>
        private void 新建目录ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // 由于菜单项已经通过NewFolderManager处理了子菜单的生成和点击事件
                // 这里不需要额外的处理逻辑
                // 实际的目录创建操作由MenuGenerator中的事件处理
                WriteLog("新建目录菜单被点击");
            }
            catch (Exception ex)
            {
                WriteLog($"处理新建目录菜单点击事件时出错: {ex.Message}");
                ETLogManager.Error(this, "处理新建目录菜单点击事件时发生错误", ex);
            }
        }

        #endregion 新建目录功能
    }
}