using ET;
using ET.ETLicense;
using ET.ETLoginWebBrowser;
using HyAssistant.ChinaTowerDownload.Configuration;
using HyAssistant.ChinaTowerDownload.Data;
using HyAssistant.ChinaTowerDownload.Models;
using HyAssistant.ChinaTowerDownload.Services;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HyAssistant.ChinaTowerDownload
{
    /// <summary>
    /// 中国铁塔照片下载主窗体
    /// </summary>
    public partial class ChinaTowerDownload : Form
    {
        #region 私有字段

        private readonly ChinaTowerConfig _config;
        private readonly ChinaTowerHttpService _httpService;
        private readonly IStationRepository _stationRepository;
        private readonly IPhotoRepository _photoRepository;
        private readonly ETPermissionManager _permissionManager;

        private bool _isLoggedIn = false;
        private bool _isDownloading = false;

        #endregion 私有字段

        #region 构造函数

        /// <summary>
        /// 初始化中国铁塔下载窗体
        /// </summary>
        /// <param name="permissionManager">权限管理器</param>
        public ChinaTowerDownload(ETPermissionManager permissionManager)
        {
            InitializeComponent();

            _permissionManager = permissionManager ?? throw new ArgumentNullException(nameof(permissionManager));

            // 初始化配置和服务
            _config = new ChinaTowerConfig();
            _httpService = new ChinaTowerHttpService(_config);

            // 初始化数据访问层
            var connectionString = _config.GetDatabaseConnectionString();
            _stationRepository = new StationRepository(connectionString);
            _photoRepository = new PhotoRepository(connectionString);

            // 初始化UI
            InitializeUI();

            // 检查权限
            CheckPermissions();
        }

        #endregion 构造函数

        #region 初始化方法

        /// <summary>
        /// 初始化UI界面
        /// </summary>
        private void InitializeUI()
        {
            // 设置窗体属性
            this.Text = "中国铁塔照片下载助手";
            this.WindowState = FormWindowState.Normal;
            this.StartPosition = FormStartPosition.CenterScreen;

            // 初始化控件状态
            UpdateUIState();

            // 加载配置信息
            LoadConfiguration();
        }

        /// <summary>
        /// 检查权限
        /// </summary>
        private void CheckPermissions()
        {
            if (!_permissionManager.HasPermission(HaPermissionKeys.ChinaTowerDownload))
            {
                MessageBox.Show("您没有使用中国铁塔照片下载助手的权限，请联系管理员。",
                    "权限不足", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                this.Close();
                return;
            }
        }

        /// <summary>
        /// 加载配置信息
        /// </summary>
        private void LoadConfiguration()
        {
            try
            {
                // 加载保存路径
                if (!string.IsNullOrEmpty(_config.SavePath))
                {
                    textBoxSavePath.Text = _config.SavePath;
                }

                //// 检查认证信息
                //if (_config.IsAuthenticationValid())
                //{
                //    _isLoggedIn = true;
                //    WriteLog("检测到有效的认证信息");

                //    // 验证Token有效性
                //    _ = ValidateTokenAsync();
                //}

                UpdateUIState();
            }
            catch (Exception ex)
            {
                WriteLog($"加载配置失败: {ex.Message}");
            }
        }

        #endregion 初始化方法

        #region UI状态管理

        /// <summary>
        /// 更新UI状态
        /// </summary>
        private void UpdateUIState()
        {
            // 登录相关按钮
            buttonLogin.Enabled = !_isLoggedIn && !_isDownloading;
            buttonInputAuth.Enabled = !_isLoggedIn && !_isDownloading;
            buttonLogout.Enabled = _isLoggedIn && !_isDownloading;

            // 功能按钮（需要登录）
            buttonUpdateStations.Enabled = _isLoggedIn && !_isDownloading;
            buttonSearchStation.Enabled = _isLoggedIn && !_isDownloading;
            buttonCrawlPhotos.Enabled = _isLoggedIn && !_isDownloading;
            buttonCrawlAllStations.Enabled = _isLoggedIn && !_isDownloading;

            // 批量下载照片按钮（不需要登录）
            buttonDownloadPhotos.Enabled = !_isDownloading;

            // 路径设置
            buttonSelectPath.Enabled = !_isDownloading;
            textBoxSavePath.Enabled = !_isDownloading;

            // 搜索相关
            textBoxSearchKeyword.Enabled = !_isDownloading;
            textBoxStationList.Enabled = !_isDownloading;

            // 状态标签
            labelLoginStatus.Text = _isLoggedIn ? "已登录" : "未登录";
            labelLoginStatus.ForeColor = _isLoggedIn ? System.Drawing.Color.Green : System.Drawing.Color.Red;

            if (_isDownloading)
            {
                labelStatus.Text = "正在执行操作...";
                labelStatus.ForeColor = System.Drawing.Color.Blue;
            }
            else
            {
                labelStatus.Text = "就绪";
                labelStatus.ForeColor = System.Drawing.Color.Black;
            }
        }

        /// <summary>
        /// 写入日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="newLine">是否换行</param>
        private void WriteLog(string message, bool newLine = true)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string, bool>(WriteLog), message, newLine);
                return;
            }

            if (newLine)
            {
                textBoxLog.AppendText($"[{DateTime.Now:HH:mm:ss}] {message}\r\n");
            }
            else
            {
                textBoxLog.AppendText(message);
            }

            textBoxLog.ScrollToCaret();
        }

        #endregion UI状态管理

        #region 事件处理

        /// <summary>
        /// 登录按钮点击事件
        /// </summary>
        private async void ButtonLogin_Click(object sender, EventArgs e)
        {
            try
            {
                _isDownloading = true;
                UpdateUIState();

                WriteLog("开始登录...");

                // 检查配置
                WriteLog("正在检查配置信息...");
                WriteLog($"配置诊断信息:\n{_config.GetDiagnosticInfo()}");

                if (!_config.IsConfigurationValid())
                {
                    WriteLog("配置信息不完整，请检查配置文件");
                    WriteLog("请确保以下配置项都已正确设置：Host, Origin, HomePage, PhotoUrl, ListUrl, SavePath");
                    return;
                }

                // 打开登录窗口
                WriteLog($"正在打开登录页面: {_config.HomePage}");
                using (var loginForm = new ETLoginWebBrowser(_config.HomePage))
                {
                    var dialogResult = loginForm.ShowDialog();
                    WriteLog($"登录窗口关闭，返回结果: {dialogResult}");

                    // 获取HTTP标头JSON数据
                    var headersJson = loginForm.HeadersJson;
                    WriteLog($"HTTP标头 JSON 原始数据: {(string.IsNullOrEmpty(headersJson) ? "空" : $"{headersJson.Length} 字符")}");

                    if (string.IsNullOrEmpty(headersJson))
                    {
                        WriteLog("获取登录信息失败：HTTP标头 JSON 为空");
                        WriteLog("可能原因：1) 页面未完全加载 2) 未进行登录操作 3) 网络请求失败");
                        return;
                    }

                    WriteLog($"获取到HTTP标头数据: {headersJson.Length} 字符");
                    WriteLog($"HTTP标头 JSON 内容预览: {(headersJson.Length > 500 ? headersJson.Substring(0, 500) + "..." : headersJson)}");

                    // 解析新格式的登录信息JSON
                    Dictionary<string, string> headers;
                    try
                    {
                        // 使用ETWebBrowserJsonFormatter解析新格式JSON
                        if (!ET.ETLoginWebBrowser.ETWebBrowserJsonFormatter.IsStandardFormat(headersJson))
                        {
                            WriteLog("HTTP标头JSON格式不正确，请确保使用最新版本的ETLoginWebBrowser");
                            return;
                        }

                        var loginInfo = ET.ETLoginWebBrowser.ETWebBrowserJsonFormatter.ParseLoginInfoJson(headersJson);

                        // 将headers部分转换为Dictionary<string, string>
                        headers = new Dictionary<string, string>();
                        if (loginInfo.Headers != null)
                        {
                            foreach (var kvp in loginInfo.Headers)
                            {
                                headers[kvp.Key] = kvp.Value?.ToString() ?? string.Empty;
                            }
                        }

                        WriteLog($"成功解析登录信息，URL: {loginInfo.Url}");
                        WriteLog($"解析出 {headers.Count} 个HTTP标头，{loginInfo.Cookies?.Count ?? 0} 个Cookie");

                        // 输出关键标头信息用于调试
                        var keyHeaders = new[] { "Authorization", "authorization", "Cookie", "cookie" };
                        foreach (var key in keyHeaders)
                        {
                            if (headers.ContainsKey(key))
                            {
                                var value = headers[key];
                                var preview = value.Length > 50 ? value.Substring(0, 50) + "..." : value;
                                WriteLog($"  找到关键标头 {key}: {preview}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        WriteLog($"解析HTTP标头JSON失败: {ex.Message}");
                        WriteLog("请确保使用最新版本的ETLoginWebBrowser模块");
                        return;
                    }

                    if (headers == null || headers.Count == 0)
                    {
                        WriteLog("未找到有效的HTTP标头数据");
                        return;
                    }

                    // 提取认证信息
                    var authInfo = ExtractAuthenticationInfo(headers);

                    if (!authInfo.IsValid)
                    {
                        WriteLog("未找到有效的认证信息，请确认已正确登录");
                        WriteLog($"可用标头: {string.Join(", ", headers.Keys)}");
                        WriteLog("请确保在浏览器中完成登录操作后再关闭窗口");
                        return;
                    }

                    WriteLog($"成功提取认证信息:");
                    WriteLog($"  Authorization: {(string.IsNullOrEmpty(authInfo.Authorization) ? "无" : "已获取")}");
                    WriteLog($"  Cookie: {(string.IsNullOrEmpty(authInfo.Cookie) ? "无" : $"{authInfo.Cookie.Length} 字符")}");
                    WriteLog($"  Token: {(string.IsNullOrEmpty(authInfo.Token) ? "无" : "已获取")}");

                    // 保存认证信息到配置
                    SaveAuthenticationInfo(authInfo);

                    // 设置HTTP服务认证信息
                    _httpService.SetAuthentication(authInfo.Authorization, authInfo.Cookie);

                    // 验证认证信息有效性
                    WriteLog("正在验证认证信息有效性...");
                    var isValid = await ValidateAuthenticationAsync();

                    if (isValid)
                    {
                        _isLoggedIn = true;
                        WriteLog("✓ 登录成功！认证信息有效");
                        WriteLog("现在可以开始下载操作");
                    }
                    else
                    {
                        WriteLog("✗ 认证信息验证失败，请重新登录");
                        WriteLog("可能原因：1) Token已过期 2) 认证信息格式错误 3) 服务器拒绝访问");
                        _config.ClearAuthentication();
                        _httpService.ClearAuthentication();
                    }
                } // 结束using语句
            }
            catch (Exception ex)
            {
                WriteLog($"登录失败: {ex.Message}");
            }
            finally
            {
                _isDownloading = false;
                UpdateUIState();
            }
        }

        /// <summary>
        /// 登出按钮点击事件
        /// </summary>
        private void ButtonLogout_Click(object sender, EventArgs e)
        {
            try
            {
                _config.ClearAuthentication();
                _httpService.ClearAuthentication();
                _isLoggedIn = false;

                WriteLog("已登出");
                UpdateUIState();
            }
            catch (Exception ex)
            {
                WriteLog($"登出失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 输入Authorization按钮点击事件
        /// </summary>
        private void ButtonInputAuth_Click(object sender, EventArgs e)
        {
            try
            {
                //// 按住Shift键点击进行服务器连接测试
                //if (Control.ModifierKeys == Keys.Shift)
                //{
                //    TestServerConnectionAsync();
                //    return;
                //}

                // 使用输入对话框让用户输入Authorization值
                using (var inputDialog = new HyAssistant.InputDialog("输入Authorization", "请输入Authorization值:", _config.Authorization))
                {
                    if (inputDialog.ShowDialog() == DialogResult.OK)
                    {
                        var authValue = inputDialog.InputText?.Trim();

                        if (string.IsNullOrEmpty(authValue))
                        {
                            WriteLog("Authorization值不能为空");
                            return;
                        }

                        // 保存Authorization值到配置
                        _config.Authorization = authValue;
                        _config.AuthorizationLastUpdateTime = DateTime.Now;

                        // 设置HTTP服务认证信息
                        _httpService.SetAuthentication(authValue, "");

                        WriteLog($"已设置Authorization: {authValue.Substring(0, Math.Min(20, authValue.Length))}...");

                        // 验证Authorization有效性
                        WriteLog("正在验证Authorization有效性...");
                        ValidateAuthorizationAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                WriteLog($"设置Authorization失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 异步验证Authorization有效性
        /// </summary>
        private async void ValidateAuthorizationAsync()
        {
            try
            {
                _isDownloading = true;
                UpdateUIState();

                var isValid = await _httpService.ValidateTokenAsync();
                if (isValid)
                {
                    _isLoggedIn = true;
                    WriteLog("✓ Authorization验证成功，已登录");
                }
                else
                {
                    WriteLog("✗ Authorization验证失败，请检查值是否正确");
                    _config.ClearAuthentication();
                    _httpService.ClearAuthentication();
                }
            }
            catch (Exception ex)
            {
                WriteLog($"验证Authorization时发生错误: {ex.Message}");
                _config.ClearAuthentication();
                _httpService.ClearAuthentication();
            }
            finally
            {
                _isDownloading = false;
                UpdateUIState();
            }
        }

        /// <summary>
        /// 选择保存路径按钮点击事件
        /// </summary>
        private void ButtonSelectPath_Click(object sender, EventArgs e)
        {
            using (var dialog = new FolderBrowserDialog())
            {
                dialog.Description = "选择照片保存路径";
                dialog.SelectedPath = textBoxSavePath.Text;

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    textBoxSavePath.Text = dialog.SelectedPath;
                    _config.SavePath = dialog.SelectedPath;
                    WriteLog($"保存路径已设置为: {dialog.SelectedPath}");
                }
            }
        }

        /// <summary>
        /// 更新站址库按钮点击事件
        /// </summary>
        private async void ButtonUpdateStations_Click(object sender, EventArgs e)
        {
            try
            {
                _isDownloading = true;
                UpdateUIState();

                WriteLog("开始更新站址库...");

                // 初始化数据库
                await _stationRepository.InitializeDatabaseAsync();

                // 获取站点信息
                var stations = await _httpService.GetAllStationsAsync();

                if (stations.Count == 0)
                {
                    WriteLog("未获取到站点信息");
                    return;
                }

                WriteLog($"获取到 {stations.Count} 个站点信息");

                // 插入数据库
                var insertedCount = await _stationRepository.InsertStationsAsync(stations);

                WriteLog($"成功插入 {insertedCount} 个新站点");
                WriteLog("站址库更新完成");

                // 更新统计信息
                await ShowStatisticsAsync();
            }
            catch (Exception ex)
            {
                WriteLog($"更新站址库失败: {ex.Message}");
            }
            finally
            {
                _isDownloading = false;
                UpdateUIState();
            }
        }

        /// <summary>
        /// 搜索站点按钮点击事件
        /// </summary>
        private async void ButtonSearchStation_Click(object sender, EventArgs e)
        {
            try
            {
                var keyword = textBoxSearchKeyword.Text.Trim();
                if (string.IsNullOrEmpty(keyword))
                {
                    WriteLog("请输入搜索关键词");
                    return;
                }

                _isDownloading = true;
                UpdateUIState();

                WriteLog($"搜索站点: {keyword}");

                var stations = await _stationRepository.SearchStationsAsync(keyword);

                if (stations.Count == 0)
                {
                    WriteLog("未找到匹配的站点");
                    return;
                }

                WriteLog($"找到 {stations.Count} 个匹配的站点:");

                var stationCodes = new List<string>();
                foreach (var station in stations)
                {
                    WriteLog($"  {station.STATION_CODE} - {station.STATION_NAME}");
                    stationCodes.Add(station.STATION_CODE);
                }

                // 将站点编码填入站点列表文本框
                textBoxStationList.Text = string.Join("\r\n", stationCodes);
            }
            catch (Exception ex)
            {
                WriteLog($"搜索站点失败: {ex.Message}");
            }
            finally
            {
                _isDownloading = false;
                UpdateUIState();
            }
        }

        /// <summary>
        /// 下载站点清单内站点照片按钮点击事件
        /// </summary>
        private async void ButtonCrawlPhotos_Click(object sender, EventArgs e)
        {
            try
            {
                var stationCodes = ExtractStationCodes(textBoxStationList.Text);
                if (stationCodes.Count == 0)
                {
                    WriteLog("请输入站点编码列表");
                    return;
                }

                if (string.IsNullOrEmpty(_config.SavePath))
                {
                    WriteLog("请先设置保存路径");
                    return;
                }

                _isDownloading = true;
                UpdateUIState();

                WriteLog($"开始下载 {stationCodes.Count} 个站点的照片...");

                // 存储所有照片信息的变量
                var allPhotos = new List<PhotoInfoExcerpt>();
                int processedStations = 0;

                // 第一步：抓取所有站点的照片信息到变量
                foreach (var stationCode in stationCodes)
                {
                    try
                    {
                        WriteLog($"正在获取站点照片信息: {stationCode}");

                        // 根据站点编码获取站点信息
                        var station = await _stationRepository.GetStationByCodeAsync(stationCode);
                        if (station == null)
                        {
                            WriteLog($"  站点 {stationCode} 不存在，请先更新站址库");
                            continue;
                        }

                        // 获取照片信息
                        var photos = await _httpService.GetStationPhotosAsync(station);

                        if (photos.Count == 0)
                        {
                            WriteLog($"  站点 {stationCode} 没有照片");
                            continue;
                        }

                        allPhotos.AddRange(photos);
                        WriteLog($"  获取到 {photos.Count} 张照片信息");
                        processedStations++;

                        // 延时避免请求过快
                        await Task.Delay(_config.Sleep);
                    }
                    catch (Exception ex)
                    {
                        WriteLog($"  获取站点 {stationCode} 照片信息失败: {ex.Message}");
                    }
                }

                if (allPhotos.Count == 0)
                {
                    WriteLog("没有找到任何照片");
                    return;
                }

                WriteLog($"照片信息获取完成，共 {processedStations} 个站点，{allPhotos.Count} 张照片");
                WriteLog("开始下载照片...");

                // 第二步：直接下载照片，跳过已存在的文件
                var progress = new Progress<DownloadProgress>(p =>
                {
                    WriteLog($"下载进度: {p.CompletedPhotos}/{p.TotalPhotos} ({p.ProgressPercentage:F1}%)");
                    WriteLog($"  成功: {p.SuccessfulPhotos}, 失败: {p.FailedPhotos}, 跳过: {p.SkippedPhotos}");
                    WriteLog($"  当前站点: {p.CurrentPhoto?.station_name}");
                    WriteLog(""); // 添加空行分隔
                });

                var result = await _httpService.BatchDownloadPhotosAsync(
                    allPhotos, _config.SavePath, _config.MaxConcurrentDownloads, progress);

                WriteLog($"\n下载完成统计:");
                WriteLog($"  总照片数: {result.TotalPhotos}");
                WriteLog($"  成功下载: {result.SuccessfulPhotos}");
                WriteLog($"  下载失败: {result.FailedPhotos}");
                WriteLog($"  跳过照片: {result.SkippedPhotos}");
                WriteLog($"  成功率: {result.SuccessRate:F1}%");
                WriteLog($"  耗时: {result.ElapsedMilliseconds / 1000.0:F1} 秒");
            }
            catch (Exception ex)
            {
                WriteLog($"下载站点清单内照片失败: {ex.Message}");
            }
            finally
            {
                _isDownloading = false;
                UpdateUIState();
            }
        }

        /// <summary>
        /// 抓取所有站点照片信息按钮点击事件
        /// </summary>
        private async void ButtonCrawlAllStations_Click(object sender, EventArgs e)
        {
            // 如果正在下载，则停止
            if (_isDownloading)
            {
                WriteLog("正在停止抓取操作...");
                _isDownloading = false;
                buttonCrawlAllStations.Text = "抓取所有站点照片";
                UpdateUIState();
                return;
            }

            try
            {
                _isDownloading = true;
                buttonCrawlAllStations.Text = "停止抓取";
                UpdateUIState();

                WriteLog("开始抓取所有站点的照片信息...");

                // 初始化数据库
                await _photoRepository.InitializeDatabaseAsync();

                // 获取所有站点信息
                var allStations = await _stationRepository.GetAllStationsAsync();

                if (allStations.Count == 0)
                {
                    WriteLog("数据库中没有站点信息，请先更新站址库");
                    return;
                }

                WriteLog($"从数据库中获取到 {allStations.Count} 个站点，开始抓取照片信息...");

                int totalPhotos = 0;
                int processedStations = 0;
                int skippedStations = 0;

                foreach (var station in allStations)
                {
                    // 检查是否需要停止
                    if (!_isDownloading)
                    {
                        WriteLog("操作已取消");
                        break;
                    }

                    try
                    {
                        WriteLog($"正在处理站点 ({processedStations + 1}/{allStations.Count}): {station.STATION_CODE} - {station.STATION_NAME}");

                        // 获取照片信息
                        var photos = await _httpService.GetStationPhotosAsync(station);

                        if (photos.Count == 0)
                        {
                            WriteLog($"  站点 {station.STATION_CODE} 没有照片");
                            skippedStations++;
                        }
                        else
                        {
                            // 插入数据库
                            var insertedCount = await _photoRepository.InsertPhotosAsync(photos);

                            WriteLog($"  获取到 {photos.Count} 张照片，新增 {insertedCount} 张");
                            totalPhotos += insertedCount;

                            // 更新站点抓取时间
                            await _stationRepository.UpdateLastCrawlTimeAsync(station.STATION_ID,
                                DateTimeOffset.Now.ToUnixTimeSeconds());
                        }

                        processedStations++;

                        // 延时避免请求过快
                        await Task.Delay(_config.Sleep);
                    }
                    catch (Exception ex)
                    {
                        WriteLog($"  处理站点 {station.STATION_CODE} 失败: {ex.Message}");
                        skippedStations++;
                    }
                }

                WriteLog($"所有站点照片信息抓取完成！");
                WriteLog($"  总站点数: {allStations.Count}");
                WriteLog($"  成功处理: {processedStations}");
                WriteLog($"  跳过站点: {skippedStations}");
                WriteLog($"  新增照片: {totalPhotos} 张");

                // 更新统计信息
                await ShowStatisticsAsync();
            }
            catch (Exception ex)
            {
                WriteLog($"抓取所有站点照片信息失败: {ex.Message}");
            }
            finally
            {
                _isDownloading = false;
                buttonCrawlAllStations.Text = "抓取所有站点照片";
                UpdateUIState();
            }
        }

        /// <summary>
        /// 批量下载照片按钮点击事件
        /// </summary>
        private async void ButtonDownloadPhotos_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(_config.SavePath))
                {
                    WriteLog("请先设置保存路径");
                    return;
                }

                _isDownloading = true;
                UpdateUIState();

                WriteLog("开始批量下载照片...");
                WriteLog("注意：批量下载功能不需要登录，将直接下载数据库中的照片");

                // 获取未下载的照片
                var photos = await _photoRepository.GetUndownloadedPhotosAsync();

                if (photos.Count == 0)
                {
                    WriteLog("没有待下载的照片");
                    return;
                }

                WriteLog($"找到 {photos.Count} 张待下载的照片");

                // 创建进度报告
                var progress = new Progress<DownloadProgress>(p =>
                {
                    WriteLog($"下载进度: {p.CompletedPhotos}/{p.TotalPhotos} ({p.ProgressPercentage:F1}%)");
                    WriteLog($"  成功: {p.SuccessfulPhotos}, 失败: {p.FailedPhotos}, 成功率: {p.SuccessRate:F1}%");
                    WriteLog($"  当前站点: {p.CurrentPhoto?.station_name}");
                    WriteLog(""); // 添加空行分隔
                });

                // 开始下载
                var result = await _httpService.BatchDownloadPhotosAsync(
                    photos, _config.SavePath, _config.MaxConcurrentDownloads, progress);

                WriteLog($"\n下载完成统计:");
                WriteLog($"  总照片数: {result.TotalPhotos}");
                WriteLog($"  成功下载: {result.SuccessfulPhotos}");
                WriteLog($"  下载失败: {result.FailedPhotos}");
                WriteLog($"  跳过照片: {result.SkippedPhotos}");
                WriteLog($"  成功率: {result.SuccessRate:F1}%");
                WriteLog($"  耗时: {result.ElapsedMilliseconds / 1000.0:F1} 秒");

                // 更新数据库下载状态
                await UpdateDownloadStatusAsync(photos, result);

                // 更新统计信息
                await ShowStatisticsAsync();
            }
            catch (Exception ex)
            {
                WriteLog($"批量下载失败: {ex.Message}");
            }
            finally
            {
                _isDownloading = false;
                UpdateUIState();
            }
        }

        #endregion 事件处理

        #region 辅助方法

        /// <summary>
        /// 认证信息结构
        /// </summary>
        private class AuthenticationInfo
        {
            public string Authorization { get; set; } = "";
            public string Cookie { get; set; } = "";
            public string Token { get; set; } = "";
            public bool IsValid => !string.IsNullOrEmpty(Authorization) || !string.IsNullOrEmpty(Token);
        }

        /// <summary>
        /// 从HTTP标头中提取认证信息
        /// </summary>
        /// <param name="headers">HTTP标头字典</param>
        /// <returns>认证信息</returns>
        private AuthenticationInfo ExtractAuthenticationInfo(Dictionary<string, string> headers)
        {
            var authInfo = new AuthenticationInfo();

            try
            {
                // 1. 查找Authorization标头
                var authHeaderNames = new[] {
                    "Authorization", "authorization", "AUTHORIZATION",
                    "X-Authorization", "X-Auth-Token", "Token", "ACCESS_TOKEN",
                    "Bearer", "Auth-Token"
                };

                foreach (var headerName in authHeaderNames)
                {
                    if (headers.TryGetValue(headerName, out string authValue) && !string.IsNullOrEmpty(authValue))
                    {
                        authInfo.Authorization = authValue;
                        WriteLog($"找到认证标头: {headerName}={authValue}");
                        break;
                    }
                }

                // 2. 查找Cookie标头
                var cookieHeaderNames = new[] { "Cookie", "cookie", "Set-Cookie", "set-cookie" };
                foreach (var headerName in cookieHeaderNames)
                {
                    if (headers.TryGetValue(headerName, out string cookieValue) && !string.IsNullOrEmpty(cookieValue))
                    {
                        authInfo.Cookie = cookieValue;
                        WriteLog($"找到Cookie标头: {cookieValue.Substring(0, Math.Min(100, cookieValue.Length))}...");
                        break;
                    }
                }

                // 3. 如果没有找到Authorization，尝试从Cookie中提取token
                if (string.IsNullOrEmpty(authInfo.Authorization) && !string.IsNullOrEmpty(authInfo.Cookie))
                {
                    // 尝试多种token模式
                    var tokenPatterns = new[]
                    {
                        @"china-tower-token=([^;]+)",
                        @"token=([^;]+)",
                        @"auth-token=([^;]+)",
                        @"access_token=([^;]+)",
                        @"authorization=([^;]+)"
                    };

                    foreach (var pattern in tokenPatterns)
                    {
                        var tokenMatch = System.Text.RegularExpressions.Regex.Match(authInfo.Cookie, pattern, System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                        if (tokenMatch.Success)
                        {
                            authInfo.Authorization = tokenMatch.Groups[1].Value;
                            WriteLog($"从Cookie中提取到Token (模式: {pattern}): {authInfo.Authorization}");
                            break;
                        }
                    }
                }

                // 4. 设置Token字段
                authInfo.Token = authInfo.Authorization;

                // 5. 如果仍然没有找到认证信息，尝试查找其他可能的认证标头
                if (!authInfo.IsValid)
                {
                    WriteLog("未找到标准认证标头，尝试查找其他可能的认证信息...");
                    foreach (var header in headers)
                    {
                        var key = header.Key.ToLower();
                        if (key.Contains("auth") || key.Contains("token") || key.Contains("bearer"))
                        {
                            WriteLog($"发现可能的认证标头: {header.Key}={header.Value}");
                            if (string.IsNullOrEmpty(authInfo.Authorization))
                            {
                                authInfo.Authorization = header.Value;
                                authInfo.Token = header.Value;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                WriteLog($"提取认证信息时发生错误: {ex.Message}");
            }

            return authInfo;
        }

        /// <summary>
        /// 保存认证信息到配置
        /// </summary>
        /// <param name="authInfo">认证信息</param>
        private void SaveAuthenticationInfo(AuthenticationInfo authInfo)
        {
            try
            {
                _config.Authorization = authInfo.Authorization;
                _config.Cookie = authInfo.Cookie;
                _config.Token = authInfo.Token;
                _config.AuthorizationLastUpdateTime = DateTime.Now;

                WriteLog("认证信息已保存到配置文件");
                WriteLog($"  Authorization: {(!string.IsNullOrEmpty(authInfo.Authorization) ? "已设置" : "未设置")}");
                WriteLog($"  Cookie: {(!string.IsNullOrEmpty(authInfo.Cookie) ? $"{authInfo.Cookie.Length} 字符" : "未设置")}");
                WriteLog($"  Token: {(!string.IsNullOrEmpty(authInfo.Token) ? "已设置" : "未设置")}");
            }
            catch (Exception ex)
            {
                WriteLog($"保存认证信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证认证信息有效性
        /// </summary>
        /// <returns>认证是否有效</returns>
        private async Task<bool> ValidateAuthenticationAsync()
        {
            try
            {
                // 使用HTTP服务的验证方法
                var isValid = await _httpService.ValidateTokenAsync();

                if (isValid)
                {
                    WriteLog("认证信息验证成功");
                }
                else
                {
                    WriteLog("认证信息验证失败");
                }

                return isValid;
            }
            catch (Exception ex)
            {
                WriteLog($"验证认证信息时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 从文本中提取站点编码列表
        /// </summary>
        /// <param name="text">包含站点编码的文本</param>
        /// <returns>站点编码列表</returns>
        private List<string> ExtractStationCodes(string text)
        {
            var codes = new List<string>();

            if (string.IsNullOrWhiteSpace(text))
                return codes;

            var lines = text.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

            foreach (var line in lines)
            {
                var trimmed = line.Trim();
                if (!string.IsNullOrEmpty(trimmed))
                {
                    // 支持多种格式：站点编码、站点编码:站点名称等
                    var parts = trimmed.Split(':');
                    var code = parts[0].Trim();

                    if (!string.IsNullOrEmpty(code) && !codes.Contains(code))
                    {
                        codes.Add(code);
                    }
                }
            }

            return codes;
        }

        /// <summary>
        /// 更新照片下载状态到数据库
        /// </summary>
        /// <param name="photos">照片列表</param>
        /// <param name="result">下载结果</param>
        private async Task UpdateDownloadStatusAsync(List<PhotoInfoExcerpt> photos, DownloadResult result)
        {
            try
            {
                var updates = new Dictionary<long, long>();
                var currentTime = DateTimeOffset.Now.ToUnixTimeSeconds();

                // 标记成功下载的照片
                var successfulPhotos = photos.Where(p => !result.FailedPhotosList.Any(f => f.ID == p.ID)).ToList();
                foreach (var photo in successfulPhotos)
                {
                    updates[photo.ID] = currentTime;
                }

                // 标记失败的照片
                foreach (var failedPhoto in result.FailedPhotosList)
                {
                    updates[failedPhoto.ID] = -1; // -1表示下载失败
                }

                if (updates.Count > 0)
                {
                    var updatedCount = await _photoRepository.BatchUpdateDownloadStatusAsync(updates);
                    WriteLog($"更新了 {updatedCount} 条照片下载状态");
                }
            }
            catch (Exception ex)
            {
                WriteLog($"更新下载状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证Token有效性
        /// </summary>
        private async Task ValidateTokenAsync()
        {
            try
            {
                var isValid = await _httpService.ValidateTokenAsync();
                if (!isValid)
                {
                    WriteLog("Token已失效，请重新登录");
                    _isLoggedIn = false;
                    _config.ClearAuthentication();
                    _httpService.ClearAuthentication();
                    UpdateUIState();
                }
            }
            catch (Exception ex)
            {
                WriteLog($"Token验证失败: {ex.Message}");
            }
        }

        #endregion 辅助方法

        #region 窗体事件

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private async void ChinaTowerDownload_Load(object sender, EventArgs e)
        {
            try
            {
                // 初始化数据库
                await _stationRepository.InitializeDatabaseAsync();
                await _photoRepository.InitializeDatabaseAsync();

                WriteLog("中国铁塔照片下载助手已启动");

                // 显示统计信息
                await ShowStatisticsAsync();

                // 检查是否有有效的认证信息
                //if (_config.IsAuthenticationValid())
                //{
                //    WriteLog("检测到有效的认证信息");

                //    // 运行Authorization测试
                //    WriteLog("开始测试Authorization头的POST请求...");
                //    try
                //    {
                //        var testResult = await _httpService.TestAuthorizationOnlyPostAsync();
                //        WriteLog("=== Authorization测试结果 ===");
                //        WriteLog(testResult);
                //    }
                //    catch (Exception testEx)
                //    {
                //        WriteLog($"Authorization测试失败: {testEx.Message}");
                //    }
                //}
                //else
                //{
                //    WriteLog("未检测到有效的认证信息，请先登录");
                //}
            }
            catch (Exception ex)
            {
                WriteLog($"初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示统计信息
        /// </summary>
        private async Task ShowStatisticsAsync()
        {
            try
            {
                var stationCount = await _stationRepository.GetStationCountAsync();
                var photoStats = await _photoRepository.GetPhotoStatisticsAsync();

                WriteLog($"数据库统计: 站点 {stationCount} 个，照片 {photoStats.TotalPhotos} 张" +
                        $"（已下载 {photoStats.DownloadedPhotos}，待下载 {photoStats.PendingPhotos}，失败 {photoStats.FailedPhotos}）");
            }
            catch (Exception ex)
            {
                WriteLog($"获取统计信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        private void ChinaTowerDownload_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                // 如果正在下载，询问是否确认关闭
                if (_isDownloading)
                {
                    var result = MessageBox.Show("正在执行操作，确定要关闭吗？",
                        "确认关闭", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result != DialogResult.Yes)
                    {
                        e.Cancel = true;
                        return;
                    }
                }

                // 释放资源
                _httpService?.Dispose();
            }
            catch (Exception ex)
            {
                WriteLog($"关闭窗体时发生错误: {ex.Message}");
            }
        }

        #endregion 窗体事件
    }
}