﻿using ET.Controls;

namespace HyExcelVsto.Module.Common
{
    partial class frmWordHelper
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.button替换PPT关键字 = new System.Windows.Forms.Button();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage替换Word = new System.Windows.Forms.TabPage();
            this.textboxSavePath输出路径 = new ET.Controls.HHUcDirectorySelect();
            this.ucExcelRangeSelectData = new ETRangeSelectControl();
            this.label3 = new System.Windows.Forms.Label();
            this.ucFileSelectTemplatePath = new ET.Controls.ETUcFileSelect();
            this.label4 = new System.Windows.Forms.Label();
            this.tabPage转换为PDF = new System.Windows.Forms.TabPage();
            this.ds原根路径_转换为PDF = new ET.Controls.HHUcDirectorySelect();
            this.ds输出路径_转换为PDF = new ET.Controls.HHUcDirectorySelect();
            this.label42 = new System.Windows.Forms.Label();
            this.label29 = new System.Windows.Forms.Label();
            this.button执行_转换为PDF = new System.Windows.Forms.Button();
            this.label30 = new System.Windows.Forms.Label();
            this.ucERS文件路径_转换为PDF = new ETRangeSelectControl();
            this.textBoxError = new System.Windows.Forms.TextBox();
            this.textBoxProgress = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.button1 = new System.Windows.Forms.Button();
            this.tabControl1.SuspendLayout();
            this.tabPage替换Word.SuspendLayout();
            this.tabPage转换为PDF.SuspendLayout();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(17, 44);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(89, 12);
            this.label1.TabIndex = 2;
            this.label1.Text = "Word模板路径：";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(17, 17);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 2;
            this.label2.Text = "数据区域：";
            // 
            // button替换PPT关键字
            // 
            this.button替换PPT关键字.Location = new System.Drawing.Point(509, 12);
            this.button替换PPT关键字.Name = "button替换PPT关键字";
            this.button替换PPT关键字.Size = new System.Drawing.Size(106, 48);
            this.button替换PPT关键字.TabIndex = 3;
            this.button替换PPT关键字.Text = "操作";
            this.button替换PPT关键字.UseVisualStyleBackColor = true;
            this.button替换PPT关键字.Click += new System.EventHandler(this.button替换Word关键字_Click);
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage替换Word);
            this.tabControl1.Controls.Add(this.tabPage转换为PDF);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.tabControl1.Location = new System.Drawing.Point(0, 0);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(629, 157);
            this.tabControl1.TabIndex = 4;
            // 
            // tabPage替换Word
            // 
            this.tabPage替换Word.Controls.Add(this.textboxSavePath输出路径);
            this.tabPage替换Word.Controls.Add(this.button替换PPT关键字);
            this.tabPage替换Word.Controls.Add(this.label2);
            this.tabPage替换Word.Controls.Add(this.ucExcelRangeSelectData);
            this.tabPage替换Word.Controls.Add(this.label3);
            this.tabPage替换Word.Controls.Add(this.ucFileSelectTemplatePath);
            this.tabPage替换Word.Controls.Add(this.label4);
            this.tabPage替换Word.Controls.Add(this.label1);
            this.tabPage替换Word.Location = new System.Drawing.Point(4, 22);
            this.tabPage替换Word.Name = "tabPage替换Word";
            this.tabPage替换Word.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage替换Word.Size = new System.Drawing.Size(621, 131);
            this.tabPage替换Word.TabIndex = 0;
            this.tabPage替换Word.Text = "根据数据表替换Word";
            this.tabPage替换Word.UseVisualStyleBackColor = true;
            // 
            // textboxSavePath输出路径
            // 
            this.textboxSavePath输出路径.Location = new System.Drawing.Point(106, 66);
            this.textboxSavePath输出路径.Name = "textboxSavePath输出路径";
            this.textboxSavePath输出路径.Size = new System.Drawing.Size(397, 21);
            this.textboxSavePath输出路径.TabIndex = 19;
            // 
            // ucExcelRangeSelectData
            // 
            this.ucExcelRangeSelectData.EnableEnterThenSelect = false;
            this.ucExcelRangeSelectData.HideParentForm = true;
            this.ucExcelRangeSelectData.InputPromptText = "请选择：";
            this.ucExcelRangeSelectData.Location = new System.Drawing.Point(106, 12);
            this.ucExcelRangeSelectData.Name = "ucExcelRangeSelectData";
            this.ucExcelRangeSelectData.SelectedRange = null;
            this.ucExcelRangeSelectData.Size = new System.Drawing.Size(397, 21);
            this.ucExcelRangeSelectData.TabIndex = 0;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(17, 71);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 2;
            this.label3.Text = "输出路径：";
            // 
            // ucFileSelectTemplatePath
            // 
            this.ucFileSelectTemplatePath.AutoFillLatestValue = true;
            this.ucFileSelectTemplatePath.DefaultFileExtension = "";
            this.ucFileSelectTemplatePath.FileFilter = "Word (*.docx)|*.docx";
            this.ucFileSelectTemplatePath.Location = new System.Drawing.Point(106, 39);
            this.ucFileSelectTemplatePath.Name = "ucFileSelectTemplatePath";
            this.ucFileSelectTemplatePath.Size = new System.Drawing.Size(397, 21);
            this.ucFileSelectTemplatePath.TabIndex = 1;
            this.ucFileSelectTemplatePath.UseFolderBrowser = false;
            this.ucFileSelectTemplatePath.UseOpenFileDialog = true;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(17, 112);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(497, 12);
            this.label4.TabIndex = 2;
            this.label4.Text = "注意：首列必须是文件全路径或者目标文件名；如果是修改现有Word，可以不用输入模板路径";
            // 
            // tabPage转换为PDF
            // 
            this.tabPage转换为PDF.Controls.Add(this.ds原根路径_转换为PDF);
            this.tabPage转换为PDF.Controls.Add(this.ds输出路径_转换为PDF);
            this.tabPage转换为PDF.Controls.Add(this.label42);
            this.tabPage转换为PDF.Controls.Add(this.label29);
            this.tabPage转换为PDF.Controls.Add(this.button执行_转换为PDF);
            this.tabPage转换为PDF.Controls.Add(this.label30);
            this.tabPage转换为PDF.Controls.Add(this.ucERS文件路径_转换为PDF);
            this.tabPage转换为PDF.Location = new System.Drawing.Point(4, 22);
            this.tabPage转换为PDF.Name = "tabPage转换为PDF";
            this.tabPage转换为PDF.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage转换为PDF.Size = new System.Drawing.Size(621, 131);
            this.tabPage转换为PDF.TabIndex = 1;
            this.tabPage转换为PDF.Text = "转换为PDF";
            this.tabPage转换为PDF.UseVisualStyleBackColor = true;
            // 
            // ds原根路径_转换为PDF
            // 
            this.ds原根路径_转换为PDF.Location = new System.Drawing.Point(172, 68);
            this.ds原根路径_转换为PDF.Name = "ds原根路径_转换为PDF";
            this.ds原根路径_转换为PDF.Size = new System.Drawing.Size(331, 21);
            this.ds原根路径_转换为PDF.TabIndex = 25;
            // 
            // ds输出路径_转换为PDF
            // 
            this.ds输出路径_转换为PDF.Location = new System.Drawing.Point(136, 41);
            this.ds输出路径_转换为PDF.Name = "ds输出路径_转换为PDF";
            this.ds输出路径_转换为PDF.Size = new System.Drawing.Size(367, 21);
            this.ds输出路径_转换为PDF.TabIndex = 26;
            // 
            // label42
            // 
            this.label42.AutoSize = true;
            this.label42.Location = new System.Drawing.Point(17, 72);
            this.label42.Name = "label42";
            this.label42.Size = new System.Drawing.Size(149, 12);
            this.label42.TabIndex = 24;
            this.label42.Text = "保持目录结构，原根路径：";
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Location = new System.Drawing.Point(17, 46);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(65, 12);
            this.label29.TabIndex = 23;
            this.label29.Text = "输出路径：";
            // 
            // button执行_转换为PDF
            // 
            this.button执行_转换为PDF.Location = new System.Drawing.Point(509, 14);
            this.button执行_转换为PDF.Name = "button执行_转换为PDF";
            this.button执行_转换为PDF.Size = new System.Drawing.Size(106, 48);
            this.button执行_转换为PDF.TabIndex = 22;
            this.button执行_转换为PDF.Text = "执行";
            this.button执行_转换为PDF.UseVisualStyleBackColor = true;
            this.button执行_转换为PDF.Click += new System.EventHandler(this.button执行_转换为PDF_Click);
            // 
            // label30
            // 
            this.label30.AutoSize = true;
            this.label30.Location = new System.Drawing.Point(17, 19);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(113, 12);
            this.label30.TabIndex = 21;
            this.label30.Text = "文件路径(单元格)：";
            // 
            // ucERS文件路径_转换为PDF
            // 
            this.ucERS文件路径_转换为PDF.EnableEnterThenSelect = false;
            this.ucERS文件路径_转换为PDF.HideParentForm = true;
            this.ucERS文件路径_转换为PDF.InputPromptText = "请选择：";
            this.ucERS文件路径_转换为PDF.Location = new System.Drawing.Point(136, 14);
            this.ucERS文件路径_转换为PDF.Name = "ucERS文件路径_转换为PDF";
            this.ucERS文件路径_转换为PDF.SelectedRange = null;
            this.ucERS文件路径_转换为PDF.Size = new System.Drawing.Size(367, 21);
            this.ucERS文件路径_转换为PDF.TabIndex = 20;
            // 
            // textBoxError
            // 
            this.textBoxError.Location = new System.Drawing.Point(4, 181);
            this.textBoxError.Multiline = true;
            this.textBoxError.Name = "textBoxError";
            this.textBoxError.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBoxError.Size = new System.Drawing.Size(620, 138);
            this.textBoxError.TabIndex = 4;
            // 
            // textBoxProgress
            // 
            this.textBoxProgress.Location = new System.Drawing.Point(4, 344);
            this.textBoxProgress.Multiline = true;
            this.textBoxProgress.Name = "textBoxProgress";
            this.textBoxProgress.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBoxProgress.Size = new System.Drawing.Size(620, 138);
            this.textBoxProgress.TabIndex = 4;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(2, 166);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(65, 12);
            this.label5.TabIndex = 2;
            this.label5.Text = "错误信息：";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(2, 329);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(65, 12);
            this.label6.TabIndex = 2;
            this.label6.Text = "进度信息：";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(21, 563);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(65, 12);
            this.label7.TabIndex = 2;
            this.label7.Text = "数据区域：";
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(513, 563);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(106, 48);
            this.button1.TabIndex = 3;
            this.button1.Text = "操作";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button替换Word关键字_Click);
            // 
            // frmWordHelper
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(629, 489);
            this.Controls.Add(this.tabControl1);
            this.Controls.Add(this.button1);
            this.Controls.Add(this.textBoxProgress);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.textBoxError);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.label6);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frmWordHelper";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Word工具集";
            this.Load += new System.EventHandler(this.frmHelper_Load);
            this.tabControl1.ResumeLayout(false);
            this.tabPage替换Word.ResumeLayout(false);
            this.tabPage替换Word.PerformLayout();
            this.tabPage转换为PDF.ResumeLayout(false);
            this.tabPage转换为PDF.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private ETRangeSelectControl ucExcelRangeSelectData;
        private ET.Controls.ETUcFileSelect ucFileSelectTemplatePath;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button button替换PPT关键字;
        private System.Windows.Forms.TextBox textBoxError;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        private ET.Controls.HHUcDirectorySelect textboxSavePath输出路径;
        private System.Windows.Forms.TextBox textBoxProgress;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label5;
        private ET.Controls.HHUcDirectorySelect ds原根路径_转换为PDF;
        private ET.Controls.HHUcDirectorySelect ds输出路径_转换为PDF;
        private System.Windows.Forms.Label label42;
        private System.Windows.Forms.Label label29;
        private System.Windows.Forms.Button button执行_转换为PDF;
        private System.Windows.Forms.Label label30;
        private ETRangeSelectControl ucERS文件路径_转换为PDF;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Button button1;
        public System.Windows.Forms.TabPage tabPage替换Word;
        public System.Windows.Forms.TabPage tabPage转换为PDF;
        public System.Windows.Forms.TabControl tabControl1;
    }
}