﻿/*
 * ============================================================================
 * 功能模块：Excel单元格前后字符添加删除工具
 * ============================================================================
 * 
 * 模块作用：为Excel单元格内容批量添加或删除前缀、后缀字符，支持自定义分隔符
 * 
 * 主要功能：
 * - 字符添加：为选中单元格内容添加前缀、后缀和分隔符
 * - 字符删除：从单元格内容中删除指定的前缀、后缀字符
 * - 快速操作：提供常用括号符号的快速添加按钮
 * - 历史记录：保存用户输入的前缀、后缀和分隔符历史
 * - 批量处理：支持对选中的多个单元格进行批量操作
 * 
 * 执行逻辑：
 * 1. 用户选择Excel单元格范围
 * 2. 设置前缀、后缀和分隔符内容
 * 3. 选择添加或删除操作模式
 * 4. 批量处理所有选中的单元格
 * 5. 保存用户输入到历史记录
 * 
 * 注意事项：
 * - 处理过程中会将单元格格式设置为文本格式
 * - 支持空单元格的内容添加操作
 * - 删除操作会循环删除匹配的前缀和后缀
 * - 使用快速模式提高大批量数据的处理性能
 * ============================================================================
 */

using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Windows.Forms;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// Excel单元格前后字符添加删除工具窗体
    /// 提供为单元格内容批量添加或删除前缀、后缀字符的功能
    /// </summary>
    public partial class frm前后添加删除字符 : Form
    {
        #region 前后添加删除字符相关设置
        /// <summary>
        /// 前缀内容历史记录集合
        /// 存储用户曾经输入过的前缀字符，用于下拉列表显示
        /// </summary>
        public static HashSet<string> PrefixContent { get; } = [];

        /// <summary>
        /// 分隔符内容集合
        /// 预定义的常用分隔符选项，包括无分隔符、逗号、顿号、换行符和斜杠
        /// </summary>
        public static HashSet<string> SeparatorContent { get; } = ["无", ",", "、", "换行", "/"];

        /// <summary>
        /// 后缀内容历史记录集合
        /// 存储用户曾经输入过的后缀字符，用于下拉列表显示
        /// </summary>
        public static HashSet<string> SuffixContent { get; } = [];

        /// <summary>
        /// 当前选择的前缀内容
        /// 保存用户最后一次选择的前缀字符
        /// </summary>
        public static string SelectedPrefix { get; set; } = string.Empty;

        /// <summary>
        /// 当前选择的分隔符内容
        /// 保存用户最后一次选择的分隔符，默认为"无"
        /// </summary>
        public static string SelectedSeparator { get; set; } = "无";

        /// <summary>
        /// 当前选择的后缀内容
        /// 保存用户最后一次选择的后缀字符
        /// </summary>
        public static string SelectedSuffix { get; set; } = string.Empty;
        #endregion

        /// <summary>
        /// 初始化前后字符添加删除工具窗体
        /// </summary>
        public frm前后添加删除字符()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 窗体加载事件处理程序，初始化下拉列表控件内容
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 执行逻辑：
        /// 1. 清空所有下拉列表控件
        /// 2. 从历史记录集合中加载前缀和后缀选项
        /// 3. 加载预定义的分隔符选项
        /// 4. 设置控件的默认选中值
        /// </remarks>
        void frm前后添加删除字符_Load(object sender, EventArgs e)
        {
            // 清空所有下拉列表控件
            comboBox添加内容_前.Items.Clear();
            comboBox添加内容_后.Items.Clear();
            comboBoxSplit.Items.Clear();

            // 从历史记录中加载前缀选项
            foreach (string item in PrefixContent)
                comboBox添加内容_前.Items.Add(item);
            // 从历史记录中加载后缀选项
            foreach (string item in SuffixContent)
                comboBox添加内容_后.Items.Add(item);
            // 加载预定义的分隔符选项
            foreach (string item in SeparatorContent)
                comboBoxSplit.Items.Add(item);

            // 设置控件的默认选中值
            comboBox添加内容_前.Text = SelectedPrefix;
            comboBox添加内容_后.Text = SelectedSuffix;
            comboBoxSplit.Text = SelectedSeparator;
        }

        /// <summary>
        /// 确定按钮点击事件处理程序，执行单元格内容修改操作
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 执行逻辑：
        /// 1. 验证用户输入的有效性
        /// 2. 获取用户选择的单元格范围
        /// 3. 处理分隔符转换
        /// 4. 批量处理所有选中的单元格
        /// 5. 保存用户输入到历史记录
        /// 6. 关闭窗体并返回成功结果
        /// </remarks>
        void buttonOK_Click(object sender, EventArgs e)
        {
            try
            {
                // 验证用户输入是否有效
                if (!ValidateInputs()) return;

                // 转换分隔符文本为实际字符
                string separator = comboBoxSplit.Text.ConvertToSplitChar();
                // 获取用户选择的单元格范围
                Range selectionRange = ETExcelExtensions.GetSelectionRange();
                if (selectionRange == null)
                {
                    MessageBox.Show(@"请先选择要处理的单元格范围");
                    return;
                }

                // 获取前缀和后缀内容
                string prefixContent = comboBox添加内容_前.Text.Length > 0 ? comboBox添加内容_前.Text : string.Empty;
                string suffixContent = comboBox添加内容_后.Text.Length > 0 ? comboBox添加内容_后.Text : string.Empty;

                // 批量处理单元格内容
                ProcessCellsContent(selectionRange, prefixContent, suffixContent, separator);
                // 保存用户输入到历史记录
                SaveUserInputs(prefixContent, suffixContent, separator);

                // 设置对话框结果并关闭窗体
                DialogResult = DialogResult.OK;
                Close();
            }
            catch (Exception ex)
            {
                throw new ETException("单元格内容修改失败", "单元格内容修改", ex);
            }
        }

        /// <summary>
        /// 验证用户输入是否有效
        /// </summary>
        /// <returns>如果至少输入了前缀或后缀内容则返回true，否则返回false</returns>
        /// <remarks>
        /// 验证规则：用户必须至少输入前缀或后缀中的一项内容，
        /// 否则操作没有意义
        /// </remarks>
        bool ValidateInputs()
        {
            // 检查是否至少输入了前缀或后缀内容
            if (!string.IsNullOrEmpty(comboBox添加内容_前.Text) || !string.IsNullOrEmpty(comboBox添加内容_后.Text))
            {
                return true;
            }
            // 提示用户输入内容
            MessageBox.Show(@"请输入要添加的内容");
            return false;
        }

        /// <summary>
        /// 批量处理选中单元格的内容
        /// </summary>
        /// <param name="selectionRange">选中的单元格范围</param>
        /// <param name="prefixContent">要添加或删除的前缀内容</param>
        /// <param name="suffixContent">要添加或删除的后缀内容</param>
        /// <param name="separator">分隔符字符</param>
        /// <remarks>
        /// 执行逻辑：
        /// 1. 启用Excel快速模式以提高处理性能
        /// 2. 遍历选中范围内的每个单元格
        /// 3. 对每个单元格执行添加或删除操作
        /// 4. 恢复Excel正常模式并刷新屏幕显示
        /// </remarks>
        void ProcessCellsContent(Range selectionRange, string prefixContent, string suffixContent, string separator)
        {
            try
            {
                // 启用Excel快速模式，提高批量处理性能
                ETExcelExtensions.SetAppFastMode();
                // 遍历选中范围内的每个单元格
                foreach (Range cell in selectionRange.Cells)
                {
                    ProcessSingleCell(cell, prefixContent, suffixContent, separator);
                }
            }
            catch (Exception ex)
            {
                throw new ETException("处理单元格内容失败", "Excel单元格处理", ex);
            }
            finally
            {
                // 恢复Excel正常模式并刷新屏幕显示
                ETExcelExtensions.SetAppNormalMode(true);
            }
        }

        /// <summary>
        /// 处理单个单元格的内容添加或删除操作
        /// </summary>
        /// <param name="cell">要处理的单元格</param>
        /// <param name="prefixContent">前缀内容</param>
        /// <param name="suffixContent">后缀内容</param>
        /// <param name="separator">分隔符</param>
        /// <remarks>
        /// 处理逻辑：
        /// 1. 将单元格格式设置为文本格式
        /// 2. 获取单元格的公式内容
        /// 3. 根据操作模式（添加/删除）和单元格内容状态执行相应操作：
        ///    - 空单元格且为添加模式：直接设置前缀+分隔符+后缀
        ///    - 非空单元格且为添加模式：在现有内容前后添加指定内容
        ///    - 非空单元格且为删除模式：从现有内容中删除指定的前缀和后缀
        /// </remarks>
        void ProcessSingleCell(Range cell, string prefixContent, string suffixContent, string separator)
        {
            // 设置单元格为文本格式
            cell.NumberFormatLocal = "@";
            // 获取单元格的公式内容
            string cellFormula = cell.Formula;

            // 处理空单元格的添加操作
            if (string.IsNullOrEmpty(cellFormula) && radioButton添加.Checked)
            {
                cell.Formula = CombineAdditionStrings(prefixContent, suffixContent, separator);
                return;
            }

            // 处理非空单元格的添加或删除操作
            if (!string.IsNullOrEmpty(cellFormula))
            {
                cell.Formula = radioButton添加.Checked
                    ? AddContentToFormula(cellFormula, prefixContent, suffixContent, separator)
                    : RemovePrefixAndSuffix(cellFormula, prefixContent, suffixContent, separator);
            }
        }

        /// <summary>
        /// 保存用户输入到全局设置和历史记录
        /// </summary>
        /// <param name="prefixContent">前缀内容</param>
        /// <param name="suffixContent">后缀内容</param>
        /// <param name="separator">分隔符</param>
        /// <remarks>
        /// 执行逻辑：
        /// 1. 将非空的前缀、后缀和分隔符添加到对应的历史记录集合
        /// 2. 更新当前选择的值，用于下次打开窗体时的默认显示
        /// 这样用户下次使用时可以看到之前使用过的选项
        /// </remarks>
        void SaveUserInputs(string prefixContent, string suffixContent, string separator)
        {
            // 将非空内容保存到历史记录集合
            if (!string.IsNullOrEmpty(prefixContent))
                PrefixContent.Add(prefixContent);
            if (!string.IsNullOrEmpty(suffixContent))
                SuffixContent.Add(suffixContent);
            if (!string.IsNullOrEmpty(separator))
                SeparatorContent.Add(separator);

            // 保存当前选择的值作为下次的默认值
            SelectedPrefix = prefixContent;
            SelectedSuffix = suffixContent;
            SelectedSeparator = separator;
        }

        /// <summary>
        /// 合并前缀、后缀和分隔符为完整字符串
        /// </summary>
        /// <param name="contentBefore">前缀内容</param>
        /// <param name="contentAfter">后缀内容</param>
        /// <param name="separator">分隔符</param>
        /// <returns>合并后的完整字符串</returns>
        /// <remarks>
        /// 用于处理空单元格的添加操作，将前缀、分隔符和后缀组合成完整内容
        /// 如果分隔符为空则不添加分隔符
        /// </remarks>
        string CombineAdditionStrings(string contentBefore, string contentAfter, string separator)
        {
            return $"{contentBefore}{(separator.Length > 0 ? separator : string.Empty)}{contentAfter}";
        }

        /// <summary>
        /// 在现有公式内容中添加前缀和后缀
        /// </summary>
        /// <param name="formula">原始公式内容</param>
        /// <param name="contentBefore">要添加的前缀内容</param>
        /// <param name="contentAfter">要添加的后缀内容</param>
        /// <param name="separator">分隔符</param>
        /// <returns>添加前缀和后缀后的公式内容</returns>
        /// <remarks>
        /// 处理逻辑：
        /// 1. 如果有前缀内容，在原内容前添加"前缀+分隔符"
        /// 2. 如果有后缀内容，在原内容后添加"分隔符+后缀"
        /// </remarks>
        string AddContentToFormula(string formula, string contentBefore, string contentAfter, string separator)
        {
            // 添加前缀内容
            if (contentBefore.Length > 0) formula = $"{contentBefore}{separator}{formula}";
            // 添加后缀内容
            if (contentAfter.Length > 0) formula = $"{formula}{separator}{contentAfter}";
            return formula;
        }

        /// <summary>
        /// 从公式内容中删除指定的前缀和后缀
        /// </summary>
        /// <param name="formula">原始公式内容</param>
        /// <param name="prefix">要删除的前缀</param>
        /// <param name="suffix">要删除的后缀</param>
        /// <param name="separator">分隔符</param>
        /// <returns>删除前缀和后缀后的公式内容</returns>
        /// <remarks>
        /// 删除逻辑：
        /// 1. 循环删除开头的前缀，直到不再以前缀开头
        /// 2. 删除前缀后，如果有分隔符则同时删除紧跟的分隔符
        /// 3. 循环删除结尾的后缀，直到不再以后缀结尾
        /// 4. 删除后缀后，如果有分隔符则同时删除前面的分隔符
        /// 使用循环确保能删除多层嵌套的前缀和后缀
        /// </remarks>
        string RemovePrefixAndSuffix(string formula, string prefix, string suffix, string separator)
        {
            // 循环删除开头的前缀
            while (formula.StartsWith(prefix))
            {
                formula = formula.Substring(prefix.Length);
                // 删除前缀后紧跟的分隔符
                if (separator.Length > 0)
                    while (formula.StartsWith(separator))
                        formula = formula.Substring(separator.Length);
            }

            // 循环删除结尾的后缀
            while (formula.EndsWith(suffix))
            {
                formula = formula.Substring(0, formula.Length - suffix.Length);
                // 删除后缀前的分隔符
                if (separator.Length > 0)
                    while (formula.EndsWith(separator))
                        formula = formula.Substring(0, formula.Length - separator.Length);
            }

            return formula;
        }

        /// <summary>
        /// 快速添加特定的前后缀字符对
        /// </summary>
        /// <param name="prefix">前缀字符</param>
        /// <param name="suffix">后缀字符</param>
        /// <remarks>
        /// 用于快速按钮操作，自动设置前缀、后缀和分隔符，
        /// 然后立即执行添加操作，无需用户再次点击确定按钮
        /// </remarks>
        void FastAdd(string prefix, string suffix)
        {
            // 设置前缀和后缀
            comboBox添加内容_前.Text = prefix;
            comboBox添加内容_后.Text = suffix;
            // 设置分隔符为无
            comboBoxSplit.Text = @"无";
            // 立即执行添加操作
            buttonOK_Click(null, null);
        }

        /// <summary>
        /// 取消按钮点击事件处理程序
        /// </summary>
        void buttonCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        /// <summary>
        /// 圆括号快速添加按钮点击事件处理程序
        /// </summary>
        void button1_Click(object sender, EventArgs e)
        {
            FastAdd(@"(", @")");
        }

        /// <summary>
        /// 尖括号快速添加按钮点击事件处理程序
        /// </summary>
        void button2_Click(object sender, EventArgs e)
        {
            FastAdd(@"<", @">");
        }

        /// <summary>
        /// 方括号快速添加按钮点击事件处理程序
        /// </summary>
        void button3_Click(object sender, EventArgs e)
        {
            FastAdd(@"[", @"]");
        }

        /// <summary>
        /// 中文方括号快速添加按钮点击事件处理程序
        /// </summary>
        void button4_Click(object sender, EventArgs e)
        {
            FastAdd(@"【", @"】");
        }

        /// <summary>
        /// 花括号快速添加按钮点击事件处理程序
        /// </summary>
        void button5_Click(object sender, EventArgs e)
        {
            FastAdd(@"{", @"}");
        }

        /// <summary>
        /// 前缀下拉框双击事件处理程序，清空输入内容
        /// </summary>
        void comboBox添加内容_前_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            comboBox添加内容_前.Text = string.Empty;
        }

        /// <summary>
        /// 后缀下拉框双击事件处理程序，清空输入内容
        /// </summary>
        void comboBox添加内容_后_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            comboBox添加内容_后.Text = string.Empty;
        }

        /// <summary>
        /// 分隔符下拉框双击事件处理程序，清空输入内容
        /// </summary>
        void comboBoxSplit_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            comboBoxSplit.Text = string.Empty;
        }
    }
}