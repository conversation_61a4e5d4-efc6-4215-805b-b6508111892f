<?xml version="1.0" encoding="utf-8"?>
<ObjMetas>
    <ObjMeta objectType="DEVICE.BIM" label="通用设备" label-en="General Equipment" itemActions="showNeInfo,viewasset,locatebim,modify,remove">
        <form>
        	<row>
        	<field name="CODE" label="编码" label-en="Code" required="true"/><field name="NAME" label="名称" label-en="Name" required="true"/>
        	</row>
        	<row>
        	<field name="room" label="所在机房" label-en="Belong room"/><field name="ROOM_NUM" label="所在房间" label-en="Room"/>
        	</row>
        	<row>
        	<field name="SPEC_CLASS" label="所属类型" label-en="Type"/><field name="SPEC_NAME" label="设备类型" label-en="Equipment type" />
        	</row>
        	<row>
        	<field name="MODEL_TYPE" label="设备型号" label-en="Equipment model"/><field name="CAPACITY" label="标称容量" label-en="Nominal capacity"/>
        	</row>
        	<row>
        	<field name="MANUFACTOR" label="生产厂家" label-en="Manufacturer"/><field name="DEVICE_USER" label="使用厂家" label-en="Manufacturer of User"/>
        	</row>
        	<row>
        	<field name="ASSETS_CODE" label="资产编码" label-en="Asset code"/><field name="OBJECT_CODE" label="实物编码" label-en="Entity code"/>
        	</row>
        	<row>
        	<field name="LIFE_STATE_ID" label="生命周期状态" label-en="Lifecycle State"/><field name="NE_ID" label="网元ID" label-en="NE ID"/>
        	</row>
        </form>
     </ObjMeta>
    
    <ObjMeta objectType="DEVICE.OPERATION.bim">
        <form>
            <row><field name="DEVICE_ID"/><field name="DEVICE_NAME"/></row>
            <row><field name="TML_NAME"/><field name="REQUIREMENTS"/></row>
            <row><field name="EXECUTOR"/><field name="EXECUTION_TIME"/></row>
        </form>
    </ObjMeta>
	<!--
    <ObjMeta objectType="STANDARDRACK" label="机架" label-en="Frame">
        <form>
        	<row>
        	<field name="CODE" label="编码" label-en="Code" required="true"/><field name="NAME" label="名称" label-en="Name" required="true"/>
        	</row>
        	<row>
   						<field name="FACILITY_ID" label="所属机房" label-en="Affiliated room" /><field name="CAPACITY" label="标称容量" label-en="Nominal capacity"/>     
   			  </row>
   			  <row>
   						<field name="MANUFACTOR_ID" label="生产厂家" label-en="Manufacturer"/> <field name="MODEL_ID" label="规格型号" label-en="Model"/>
   				</row>
   				<row>
   				<field name="LENGTH" label="长度" label-en="Length" /><field name="WIDTH" label="宽度" label-en="Width" />
   				</row>
   				<row> 
   						 <field name="HEIGHT" label="高度" label-en="Height" /><field name="LIFE_STATE_ID" label="生命周期"/>
   				</row>
        </form>        
     </ObjMeta>
    
     <ObjMeta objectType="UPS" label="UPS电源" label-en="UPS Power Supply">
        <form>
        	<row>
        	<field name="CODE" label="编码" label-en="Code" required="true"/><field name="NAME" label="名称" label-en="Name" required="true"/>
        	</row>
        	<row>
   						<field name="room" label="所属机房" label-en="Affiliated room" /><field name="CAPACITY" label="标称容量" label-en="Nominal capacity"/>     
   			  </row>
   			  <row>
   						<field name="vendor" label="生产厂家" label-en="Manufacturer"/> <field name="DEVICE_TYPE_ID" label="规格型号" label-en="Model"/>
   				</row>
   				<row>
   					 <field name="LIFE_STATE_ID" label="生命周期状态" label-en="Lifecycle State"/><field name="MNT_DEPT" label="维护部门" label-en="Maintenance department"/>
   				</row>
   						 <field name="NOTES" label="备注" label-en="Comment"/>
        </form>        
     </ObjMeta>
        
   
     <ObjMeta objectType="ACDistrubutionPanel" label="交流配电屏" label-en="AC Distribution Panel">
        <form>
        	<row>
        	<field name="CODE" label="编码" label-en="Code" required="true"/><field name="NAME" label="名称" label-en="Name" required="true"/>
        	</row>
        	<row>
   						<field name="room" label="所属机房" label-en="Affiliated room" /><field name="CAPACITY" label="标称容量" label-en="Nominal capacity"/>     
   			  </row>
   			  <row>
   						<field name="vendor" label="生产厂家" label-en="Manufacturer"/> <field name="DEVICE_TYPE" label="规格型号" label-en="Model"/>
   				</row>
   						 <field name="NOTES" label="备注" label-en="Comment"/>
        </form>        
     </ObjMeta>
    
     <ObjMeta objectType="FAULTSYSTEM" label="动力监控系统" label-en="Power Monitoring System">
        <form>
        	<row>
        	<field name="CODE" label="编码" label-en="Code" required="true"/><field name="NAME" label="名称" label-en="Name" required="true"/>
        	</row>
        	<row>
   						<field name="room" label="所属机房" label-en="Affiliated room" /><field name="CAPACITY" label="标称容量" label-en="Nominal capacity"/>     
   			  </row>
   			  <row>
   						<field name="vendor" label="生产厂家" label-en="Manufacturer"/> <field name="DEVICE_TYPE_ID" label="规格型号" label-en="Model"/>
   				</row>
   						 <field name="NOTES" label="备注" label-en="Comment"/>
        </form>        
     </ObjMeta>
     
      
     
     <ObjMeta objectType="DCSYSTEM" label="直流供电系统" label-en="DC Power Supply System">
        <form>
        	<row>
        	<field name="CODE" label="编码" label-en="Code" required="true"/><field name="NAME" label="名称" label-en="Name" required="true"/>
        	</row>
        	<row>
   						<field name="room" label="所属机房" label-en="Affiliated room" /><field name="CAPACITY" label="标称容量" label-en="Nominal capacity"/>     
   			  </row>
   			  <row>
   						<field name="vendor" label="生产厂家" label-en="Manufacturer"/> <field name="DEVICE_TYPE_ID" label="规格型号" label-en="Model"/>
   				</row>
   						 <field name="NOTES" label="备注" label-en="Comment"/>
        </form>        
     </ObjMeta>
     -->
     <ObjMeta objectType="AIRCONDITION" label="空调" label-en="Air conditioning">
        <form>
        	<row>
        	<field name="CODE" label="编码" label-en="Code" required="true"/><field name="NAME" label="名称" label-en="Name" required="true"/>
        	</row>
        	<row>
   						<field name="room" label="所属机房" label-en="Affiliated room" /><field name="CAPACITY" label="标称容量" label-en="Nominal capacity"/>     
   			  </row>
   			  <row>
   						<field name="vendor" label="生产厂家" label-en="Manufacturer"/> <field name="DEVICE_TYPE_ID" label="规格型号" label-en="Model"/>
   				</row>
   						 <field name="NOTES" label="备注" label-en="Comment"/>
        </form>        
     </ObjMeta>
     
     
     <ObjMeta objectType="OLTDEVICE" needgeom="false" typeActions="add,templates" itemActions="locate,modify,connview,remove,invokePONDiagram">
        <action type="workspace" name="templates" label="模板管理" label-en="Template management" title="OLT模板" title-en="OLT Template" objectType="OLTDEVICE.TEMPLATE" icon="columns"/>
   		<form>
			<field type="divider" label="基本属性" label-en="Basic Attribute"/>
   			<row><field name="site" required="true" onChange="region=site.REGION_ID;CODE,NAME=@genCode(site)"/><field name="room" onChange="${rack}.dropdown.setBaseParam('room',${room}.getValue() || -1)"/></row>
   			<row><field name="rack"/></row>
            <row><field name="CODE" required="true"/><field name="NAME" required="true"/></row>

            <row><field name="LIFE_STATE_ID"/><field name="CAPACITY"/></row>
            <row><field name="CREATOR" readOnly="true"/><field name="CREATE_DATE" readOnly="true"/></row>
            <row><field name="plane" baseParams="PLANE_ID:80206206" label="所属一平面POP点" label-en="Plane 1 POP point"/><field name="plane2" baseParams="PLANE_ID:80206207" label="所属二平面POP点" label-en="Plane 2 POP point"/></row>
            <row><field name="plane3" baseParams="PLANE_ID:80206208" label="所属1.5平面POP点" label-en="Plane 1.5 POP point"/><field name="NOTES"/></row>
            <field type="divider" label="位置信息" label-en="Location information"/>

            <row><field name="LONGITUDE" readOnly="true"/><field name="LATITUDE" readOnly="true"/></row>
            <!--
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row><field name="smallcounty"/><field name="marketingarea"/></row>
            <row><field name="servicearea"/></row>
            -->

            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row><field name="ASSET_CODE"/><field name="CHILD_ASSCARD_CODE"/></row>
            <row><field name="PROPERTY_TYPE_ID"/><field name="PROPERTY_OWNER_ID"/></row>
            <row><field name="MNT_LEVEL_ID"/><field name="MNT_PERSON"/></row>
            <row><field name="MNT_DEPT"/><field name="COLLECTION_DEPT"/></row>
            <row><field name="COLLECTION_PERSON"/><field name="COLLECTION_DATE"/></row>
            <row><field name="AUDIT_PERSON"/><field name="AUDIT_DATE"/></row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row><field name="project"/></row>
   		</form>
        <action name="genCode" type="script">
            <![CDATA[
            var pre = site.CODE + '/OLT';
            _bean.autoCode(this, pre, 3, 'OLTDEVICE', {site: site.id});
        ]]>
        </action>
        <action name="add" label="新增" label-en="new" type="modal"  contentUrl="modules/devicemgt/devicemgt_add_window.html" closable="false">
        </action>
        <action name="modify" label="修改" type="modal"  label-en="modify" contentUrl="modules/devicemgt/devicemgt_edit_window.html" closable="false">
        </action>
    </ObjMeta>

    <ObjMeta objectType="INSPECT.bim" typeActions="" itemActions="viewDevice,view">
        <form name="search">
            <field name="DEVICE_ID"/>
            <field name="BIM_INSPECT_DATE" type="date" daterange="true"/>
        </form>
        <grid chartCategoryField="BIM_INSPECT_DATE">
            <chart title="线图" title-en="Line graph"  type="line" fields="BIM_INFLOW,BIM_OUTFLOW" dataFn="reverse"/>
            <field name="DEVICE_ID" label="设备ID" label-en="Equipment ID"/>
            <field name="CODE" label="设备名称" label-en="Equipment name"/>
           <!--  <field name="BIM_INSPECT_DATE" label="检测时间" label-en="Detection time"/>
           <field name="BIM_TEMPERATURE" label="检测温度" label-en="Detecting temperature"/>
            <field name="BIM_HUMIDITY" label="检测湿度" label-en="Humidity detection"/>
            <field name="BIM_SMOKE" label="烟雾浓度" label-en="Smoke concentration"/>
            <field name="BIM_DEVICERP" label="电量百分比" label-en="Percentage of Electricity"/>-->
            <field name="BIM_NAME" label="名称" label-en="Name" />
            <field name="BIM_INFLOW" label="入流量值" label-en="Inflow value"/>
            <field name="BIM_OUTFLOW" label="出流量值" label-en="Outflow value"/>
            <field name="BIM_CREATETIME" label="时间" label-en="Time"/>
        </grid>
    </ObjMeta>
    <ObjMeta objectType="ALARM.bim" typeActions="" itemActions="viewDevice,view">
        <form>
            <field name="DEVICE_ID" label="设备ID" label-en="Equipment ID"/>
            <field name="CODE" label="设备名称" label-en="Equipment name"/>
            <field name="BIM_ALARM_TYPE_ID" label="告警类型" label-en="Alarm type"/>
            <field name="BIM_ALARM_FROM" label="告警来源" label-en="Alarm source" />
            <field name="BIM_ALARM_LEVEL" label="告警等级" label-en="Alarm level" />
            <!--<field name="BIM_TEMPERATURE" label="检测温度" label-en="Detecting temperature"/>
            <field name="BIM_HUMIDITY" label="检测湿度" label-en="Humidity detection"/>
            <field name="BIM_SMOKE" label="烟雾浓度" label-en="Smoke concentration"/>
            <field name="BIM_DEVICERP" label="电量百分比" label-en="Percentage of Electricity"/> -->
            <field name="BIM_REPORTTIME" label="告警上报时间" label-en="Time when the alarm is reported"/>
            <field name="BIM_SERVICETIME" label="告警时间" label-en="Alarm time"/>
        </form>
        <form name="search">
            <field name="DEVICE_ID"/>
            <field name="BIM_ALARM_TYPE_ID"/>
            <field name="BIM_REPORTTIME" type="date"/>
        </form>
        <grid chartCategoryField="BIM_REPORTTIME">
            <chart title="线图" title-en="Line graph" type="line" fields="BIM_TEMPERATURE,BIM_HUMIDITY,BIM_SMOKE,BIM_DEVICERP" dataFn="reverse"/>
            <chart title="饼图" label-en="Pie chart" type="pie" fields="BIM_ALARM_TYPE_ID" groupData="true">
                <item value="0" color="green"/>
                <item value="1" color="red"/>
                <item value="2" color="#ff6666"/>
                <item value="3" color="#ff3300"/>
                <item value="6" color="#cc3366"/>
                <item value="7" color="#cc3300"/>
            </chart>

            <field name="DEVICE_ID" label="设备ID" label-en="Equipment ID"/>
            <field name="BIM_ALARM_TYPE_ID" label="告警类型" label-en="Alarm type"/>
            <field name="BIM_ALARM_FROM" label="告警来源" label-en="Alarm source" />
            <field name="BIM_ALARM_LEVEL" label="告警等级" label-en="Alarm level" />
            
            <field name="BIM_TEMPERATURE" label="检测温度" label-en="Detecting temperature"/>
            <field name="BIM_HUMIDITY" label="检测湿度" label-en="Humidity detection"/>
            <field name="BIM_SMOKE" label="烟雾浓度" label-en="Smoke concentration"/>
            <field name="BIM_DEVICERP" label="电量百分比" label-en="Percentage of Electricity"/>
            
            <field name="BIM_REPORTTIME" label="告警上报时间" label-en="Time when the alarm is reported"/>
            <field name="BIM_SERVICETIME" label="告警时间" label-en="Alarm time"/>
        </grid>
        <action id="viewDevice" label="设备详情" label-en="Equipment details" type="script">
        <![CDATA[
            _bean.find('SENSOR', {CODE: params.DEVICE_ID}).then(function(s){
                if (s) _bean.showModify(s.objectType, s.id);
                else alert('没有找到对应的烟感设备');
            });
        ]]>
        </action>
    </ObjMeta>

    <ObjMeta objectType="camera.bim" label="摄像头" label-en="Camera" itemActions="opencamera,locatebim,modify,remove">
        <form><field name="CODE" label="设备序列" label-en="Equipment sequence" required="true"/><field name="NAME" label="名称" label-en="Name" required="true"/></form>
        <grid><field name="CODE" label="设备序列" label-en="Equipment sequence"/><field name="NAME" label="名称" label-en="Name"/></grid>
    </ObjMeta>
   

    <ObjMeta objectType="FILE">
        <grid autoReloadInterval="120"><field name="NAME"/><field name="FILE_NAME"/><field name="FILE_TYPE"/><field name="CREATOR"/><field name="CREATE_DATE"/><field name="status" width="300px"/></grid>
        <script>
        <![CDATA[
            om.findGridField('', 'status').getLabel = function(row){
                var el = $('<div>');
                if (row.ifcstatus !== undefined) {
                    var status = row.ifcstatus;
                    var opt = {size: 'small', value: 100};
                    if (status === 0) {
                        opt.state = 'success';
                        opt.label = 'IFC模型就绪';
                    }
                    else if (status === 1) {
                        opt.state = 'active';
                        opt.value = 50;
                        opt.label = '正在转换ifc模型...';
                    }
                    else {
                        opt.state = 'error';
                        opt.label = 'ifc模型转换失败,请删除并重新上传';
                    }
                    _sui.progress(el, opt);
                }
                return el;
            };
            var grid = om.findGrid();
            grid.imageField = function(item){
                var ret = {width: '100px', height: '100px'};
                var imgtypes = ['jpg', 'png', 'gif', 'jpeg'];
                if (imgtypes.indexOf(item.FILE_TYPE.toLowerCase()) !== -1) ret.src = _context.fullDataUrl('bean/getfile.do?_type=FILE&id=' + item.id);
                return ret;
            };
            grid.imageAction = function(item){
                window.open(_context.fullDataUrl('bean/getfile.do?_type=FILE&id=' + item.id, '_blank'));
            };
            grid.itemActions = function(item){
                var ret = ['modify', 'remove', 'download'];
                if (item.FILE_TYPE.toLowerCase() === 'ifc') ret.push('viewbim');
                return ret;
            };
        ]]>
        </script>
    </ObjMeta>

    <ObjMeta objectType="bim.deviceDataPoint">
        <form inline="false">
            <row><field name="type" default="1" required="true"/><field name="slaveIndex" required="true"/></row>
            <row><field name="deviceId" required="true"/><field name="dataId" required="true"/></row>
        </form>
    </ObjMeta>

    <ObjMeta objectType="bim.model.template" label="模板" label-en="Template" labelField="CODE" typeActions="upload" itemActions="download,modify,remove,view3dTemplate">
        <form>
            <row>
                <field name="NAME"/><field name="URL"/>
            </row>
            <row>
                <field name="CREATE_NAME"/><field name="CREATE_TIME"/>
            </row>
        </form>
        <grid>
            <field name="NAME"/><field name="FILE_NAME"/><field name="CREATE_NAME"/><field name="CREATE_TIME"/>
        </grid>
        <action id="upload" label="上传"  label-en="upload" icon="upload" type="script">
        <![CDATA[
            var grid = _actionContext.source;
            _sui.showUploadModal('bean/add.do?_type=bim.model.template', {success: function(data){
                console.log(data);
                grid.reload();
            }});
        ]]>
        </action>
        <action id="download" label="下载" label-en="download" icon="download" url="bean/getfile.do?_type=bim.model.template&amp;id=${id}"/>
        <action id="view3dTemplate" label="3d查看" label-en="3d view" icon="eye" _ui_class="orange" type="script">
        <![CDATA[
          var ps = {deviceId: params.id, deviceInfo: {}};
          _context.doAction({mainConfig: 'apps/bimview/3d/view3d.xml', urlParams: {device: JSON.stringify(ps)}});
        ]]>
        </action>
    </ObjMeta>
</ObjMetas>