using System.Collections.Generic;

namespace HyAssistant.ChinaTowerDownload.Models
{
    /// <summary>
    /// 表示照片下载任务的最终结果统计。
    /// </summary>
    public class DownloadResult
    {
        /// <summary>
        /// 获取或设置本次下载任务中照片的总数量。
        /// </summary>
        public int TotalPhotos { get; set; }

        /// <summary>
        /// 获取或设置成功下载的照片数量。此字段支持 <c>Interlocked</c> 操作，适用于多线程环境下的原子性更新。
        /// </summary>
        public int SuccessfulPhotos;

        /// <summary>
        /// 获取下载失败的照片数量。此属性是一个只读属性，其值取决于 <see cref="FailedPhotosList"/> 中元素的数量。
        /// </summary>
        public int FailedPhotos => FailedPhotosList?.Count ?? 0;

        /// <summary>
        /// 获取或设置在下载过程中因已存在而跳过的照片数量。此字段支持 <c>Interlocked</c> 操作。
        /// </summary>
        public int SkippedPhotos;

        /// <summary>
        /// 获取或设置整个下载过程所消耗的时间，单位为毫秒。
        /// </summary>
        public long ElapsedMilliseconds { get; set; }

        /// <summary>
        /// 获取下载任务的成功率，表示成功下载的照片数占总照片数的百分比。
        /// 计算方式为：<c>(SuccessfulPhotos / TotalPhotos) * 100</c>。如果 <c>TotalPhotos</c> 为0，则返回0。
        /// </summary>
        public double SuccessRate
        {
            get
            {
                if (TotalPhotos == 0) return 0;
                return (double)SuccessfulPhotos / TotalPhotos * 100;
            }
        }

        /// <summary>
        /// 获取下载任务的平均速度，单位为照片每秒。
        /// 计算方式为：<c>SuccessfulPhotos / (ElapsedMilliseconds / 1000.0)</c>。如果 <c>ElapsedMilliseconds</c> 为0，则返回0。
        /// </summary>
        public double AverageSpeed
        {
            get
            {
                if (ElapsedMilliseconds == 0) return 0;
                return (double)SuccessfulPhotos / (ElapsedMilliseconds / 1000.0);
            }
        }

        /// <summary>
        /// 获取或设置一个列表，其中包含所有下载失败的照片的精简信息。
        /// </summary>
        public List<PhotoInfoExcerpt> FailedPhotosList { get; set; } = new List<PhotoInfoExcerpt>();
    }
}
