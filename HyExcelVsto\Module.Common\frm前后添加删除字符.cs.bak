﻿using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Windows.Forms;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// 为Excel单元格内容前后添加或删除字符的窗体
    /// </summary>
    public partial class frm前后添加删除字符 : Form
    {
        #region 前后添加删除字符相关设置
        /// <summary>
        /// 前缀内容集合
        /// </summary>
        public static HashSet<string> PrefixContent { get; } = [];

        /// <summary>
        /// 分隔符内容集合
        /// </summary>
        public static HashSet<string> SeparatorContent { get; } = ["无", ",", "、", "换行", "/"];

        /// <summary>
        /// 后缀内容集合
        /// </summary>
        public static HashSet<string> SuffixContent { get; } = [];

        /// <summary>
        /// 当前选择的前缀内容
        /// </summary>
        public static string SelectedPrefix { get; set; } = string.Empty;

        /// <summary>
        /// 当前选择的分隔符内容
        /// </summary>
        public static string SelectedSeparator { get; set; } = "无";

        /// <summary>
        /// 当前选择的后缀内容
        /// </summary>
        public static string SelectedSuffix { get; set; } = string.Empty;
        #endregion

        public frm前后添加删除字符()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 窗体加载时初始化控件内容
        /// </summary>
        void frm前后添加删除字符_Load(object sender, EventArgs e)
        {
            // 加载设置信息
            comboBox添加内容_前.Items.Clear();
            comboBox添加内容_后.Items.Clear();
            comboBoxSplit.Items.Clear();

            foreach (string item in PrefixContent)
                comboBox添加内容_前.Items.Add(item);
            foreach (string item in SuffixContent)
                comboBox添加内容_后.Items.Add(item);
            foreach (string item in SeparatorContent)
                comboBoxSplit.Items.Add(item);

            comboBox添加内容_前.Text = SelectedPrefix;
            comboBox添加内容_后.Text = SelectedSuffix;
            comboBoxSplit.Text = SelectedSeparator;
        }

        /// <summary>
        /// 执行单元格内容修改操作
        /// </summary>
        void buttonOK_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInputs()) return;

                string separator = comboBoxSplit.Text.ConvertToSplitChar();
                Range selectionRange = ETExcelExtensions.GetSelectionRange();
                if (selectionRange == null)
                {
                    MessageBox.Show(@"请先选择要处理的单元格范围");
                    return;
                }

                string prefixContent = comboBox添加内容_前.Text.Length > 0 ? comboBox添加内容_前.Text : string.Empty;
                string suffixContent = comboBox添加内容_后.Text.Length > 0 ? comboBox添加内容_后.Text : string.Empty;

                ProcessCellsContent(selectionRange, prefixContent, suffixContent, separator);
                SaveUserInputs(prefixContent, suffixContent, separator);

                DialogResult = DialogResult.OK;
                Close();
            }
            catch (Exception ex)
            {
                throw new ETException("单元格内容修改失败", "单元格内容修改", ex);
            }
        }

        /// <summary>
        /// 验证用户输入是否有效
        /// </summary>
        /// <returns>输入是否有效</returns>
        bool ValidateInputs()
        {
            if (!string.IsNullOrEmpty(comboBox添加内容_前.Text) || !string.IsNullOrEmpty(comboBox添加内容_后.Text))
            {
                return true;
            }
            MessageBox.Show(@"请输入要添加的内容");
            return false;
        }

        /// <summary>
        /// 处理选中单元格的内容
        /// </summary>
        /// <param name="selectionRange">选中的单元格范围</param>
        /// <param name="prefixContent">前缀内容</param>
        /// <param name="suffixContent">后缀内容</param>
        /// <param name="separator">分隔符</param>
        void ProcessCellsContent(Range selectionRange, string prefixContent, string suffixContent, string separator)
        {
            try
            {
                ETExcelExtensions.SetAppFastMode();
                foreach (Range cell in selectionRange.Cells)
                {
                    ProcessSingleCell(cell, prefixContent, suffixContent, separator);
                }
            }
            catch (Exception ex)
            {
                throw new ETException("处理单元格内容失败", "Excel单元格处理", ex);
            }
            finally
            {
                ETExcelExtensions.SetAppNormalMode(true);
            }
        }

        /// <summary>
        /// 处理单个单元格的内容
        /// </summary>
        void ProcessSingleCell(Range cell, string prefixContent, string suffixContent, string separator)
        {
            cell.NumberFormatLocal = "@";
            string cellFormula = cell.Formula;

            if (string.IsNullOrEmpty(cellFormula) && radioButton添加.Checked)
            {
                cell.Formula = CombineAdditionStrings(prefixContent, suffixContent, separator);
                return;
            }

            if (!string.IsNullOrEmpty(cellFormula))
            {
                cell.Formula = radioButton添加.Checked
                    ? AddContentToFormula(cellFormula, prefixContent, suffixContent, separator)
                    : RemovePrefixAndSuffix(cellFormula, prefixContent, suffixContent, separator);
            }
        }

        /// <summary>
        /// 保存用户输入到全局设置
        /// </summary>
        void SaveUserInputs(string prefixContent, string suffixContent, string separator)
        {
            // 保存到集合
            if (!string.IsNullOrEmpty(prefixContent))
                PrefixContent.Add(prefixContent);
            if (!string.IsNullOrEmpty(suffixContent))
                SuffixContent.Add(suffixContent);
            if (!string.IsNullOrEmpty(separator))
                SeparatorContent.Add(separator);

            // 保存当前选择的值
            SelectedPrefix = prefixContent;
            SelectedSuffix = suffixContent;
            SelectedSeparator = separator;
        }

        /// <summary>
        /// 合并前缀、后缀和分隔符
        /// </summary>
        string CombineAdditionStrings(string contentBefore, string contentAfter, string separator)
        {
            return $"{contentBefore}{(separator.Length > 0 ? separator : string.Empty)}{contentAfter}";
        }

        /// <summary>
        /// 在公式中添加前缀和后缀内容
        /// </summary>
        string AddContentToFormula(string formula, string contentBefore, string contentAfter, string separator)
        {
            if (contentBefore.Length > 0) formula = $"{contentBefore}{separator}{formula}";
            if (contentAfter.Length > 0) formula = $"{formula}{separator}{contentAfter}";
            return formula;
        }

        /// <summary>
        /// 从公式中删除前缀和后缀
        /// </summary>
        string RemovePrefixAndSuffix(string formula, string prefix, string suffix, string separator)
        {
            while (formula.StartsWith(prefix))
            {
                formula = formula.Substring(prefix.Length);
                if (separator.Length > 0)
                    while (formula.StartsWith(separator))
                        formula = formula.Substring(separator.Length);
            }

            while (formula.EndsWith(suffix))
            {
                formula = formula.Substring(0, formula.Length - suffix.Length);
                if (separator.Length > 0)
                    while (formula.EndsWith(separator))
                        formula = formula.Substring(0, formula.Length - separator.Length);
            }

            return formula;
        }

        /// <summary>
        /// 快速添加特定的前后缀字符
        /// </summary>
        void FastAdd(string prefix, string suffix)
        {
            comboBox添加内容_前.Text = prefix;
            comboBox添加内容_后.Text = suffix;
            comboBoxSplit.Text = @"无";
            buttonOK_Click(null, null);
        }

        void buttonCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        void button1_Click(object sender, EventArgs e)
        {
            FastAdd(@"(", @")");
        }

        void button2_Click(object sender, EventArgs e)
        {
            FastAdd(@"<", @">");
        }

        void button3_Click(object sender, EventArgs e)
        {
            FastAdd(@"[", @"]");
        }

        void button4_Click(object sender, EventArgs e)
        {
            FastAdd(@"【", @"】");
        }

        void button5_Click(object sender, EventArgs e)
        {
            FastAdd(@"{", @"}");
        }

        void comboBox添加内容_前_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            comboBox添加内容_前.Text = string.Empty;
        }

        void comboBox添加内容_后_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            comboBox添加内容_后.Text = string.Empty;
        }

        void comboBoxSplit_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            comboBoxSplit.Text = string.Empty;
        }
    }
}