(function () {
    var currentWindow = window, topWindow = top;
    var urlParams = _util.getUrlParams();
    var debugMode = urlParams.debug === 'true' || window.location.hostname === 'localhost';
    var accessToken = sessionStorage.getItem('access_token');
    if (!currentWindow._context) {
        var win = currentWindow;
        var parentContext, parentWin = win;
        while (!parentContext && parentWin) {
            var parent = win.parent !== win ? win.parent : null;
            parentWin = parent || win.opener;
            win = parentWin;
            try {
                if (parentWin._context)
                    parentContext = parentWin._context;
            }
            catch (err) { }
        }
        var configFile = urlParams.config;
        if (!configFile && parentContext) {
            currentWindow._context = parentContext;
            return;
        }
        if (parentContext && parentContext.debugMode)
            debugMode = true;
        if (!accessToken && parentContext)
            accessToken = parentContext.access_token;
        var eventbus = $({}), eventsAttached_1 = [];
        var context = {
            access_token: accessToken,
            parentContext: null, rootContext: null,
            window: null,
            urlParams: urlParams, debugMode: debugMode,
            dataRootUrl: '',
            version: '', lang: '',
            configPath: null,
            eventbus: eventbus,
            configXml: null, objMetaXmls: [], xmlsByUrl: {}, configLoaded: false,
            useParentMetaConfig: true,
            useParentMeta: true,
            updateParentMeta: false,
            objQueryFixFn: null,
            metas: {}, dicts: null,
            objMetaDefault: null, objMetaDefaultFn: null,
            objMetaDefaultFns: [],
            actionHandler: null,
            lastActionData: null,
            actionDefs: {},
            settings: {},
            clientConf: {},
            shouldEncodeParams: undefined,
            permissionHandler: undefined,
            setup: function () {
                var self = this, clientConfPath = urlParams.clientConf || sessionStorage.getItem('client_conf_path') || 'client_conf.json';
                sessionStorage.setItem('client_conf_path', clientConfPath);
                return new Promise(function (resolve, reject) {
                    if (self.client_conf)
                        resolve(null);
                    else if (self.parentContext && self.parentContext.clientConf) {
                        self.clientConf = self.parentContext.clientConf;
                        resolve(null);
                    }
                    else
                        $.getJSON(clientConfPath, function (conf) {
                            self.clientConf = conf;
                            if (conf._suiDefaults)
                                Object.assign(_sui, conf._suiDefaults);
                            if (conf._beanDefaults)
                                Object.assign(_bean, conf._beanDefaults);
                        }).always(function () {
                            resolve(null);
                        });
                }).then(function () {
                    if (self.clientConf) {
                        var c = self.clientConf, dataUrl = c.dataRootUrl, indexUrl = c.indexUrl, loginUrl = c.loginUrl;
                        if (dataUrl)
                            self.dataRootUrl = dataUrl;
                        if (indexUrl && !indexUrl.startsWith('http'))
                            c.indexUrl = location.protocol + '//' + location.host + '/' + indexUrl;
                        if (loginUrl && !loginUrl.startsWith('http'))
                            c.loginUrl = location.protocol + '//' + location.host + '/' + loginUrl;
                        if (_context.getSetting('debugMode'))
                            _context.debugMode = true;
                        if (c.version)
                            self.version = c.version;
                        if (c.initscripturl)
                            return _util.loadScript(c.initscripturl).then(function () {
                                return self.getUser();
                            });
                    }
                    return self.getUser();
                });
            },
            log: function () {
                if (this.debugMode)
                    console.log.apply(console, arguments);
            },
            getUrlParam: function (k) {
                return this.urlParams[k];
            },
            fixUrl: function (url, appendFlags) {
                if (appendFlags === undefined)
                    appendFlags = true;
                if (url.startsWith('./')) {
                    var _c = _context, path = _c.configPath;
                    if (path === url && _c.parentContext)
                        return _c.parentContext.fixUrl(url, appendFlags);
                    if (path.startsWith('./') && _c.parentContext)
                        path = _c.parentContext.fixUrl(path);
                    var pos = path.lastIndexOf('/');
                    var pre = pos !== -1 ? path.substring(0, pos + 1) : '';
                    url = pre + url.substring(2);
                }
                var configMapping = _context.getSetting('configMapping');
                if (configMapping) {
                    var file = url, search = '', pos = file.indexOf('?');
                    if (pos !== -1) {
                        search = file.substring(pos);
                        file = file.substring(0, pos);
                    }
                    var m = configMapping[file];
                    if (m) {
                        console.log('config mapping: ' + file + ' -> ' + m);
                        file = m;
                    }
                    url = file + search;
                }
                if (appendFlags && _context.version && url.indexOf('_v=') === -1)
                    url = _util.appendUrlParam(url, '_v', _context.version);
                if (appendFlags && this.debugMode && url.indexOf('_r=') === -1)
                    url = _util.appendUrlParam(url, '_r', Math.random());
                if ((url.indexOf('.htm') !== -1) && _context.access_token && url.indexOf('access_token=') === -1 && (url.indexOf('main.html') === -1 || !sessionStorage.getItem('access_token'))) {
                    var c = _context.rootContext || _context;
                    url = _util.appendUrlParam(url, 'access_token', _context.access_token);
                }
                return url;
            },
            loadTheme: function (theme) {
                var defaultTheme = 'lib/semantic/semantic.min.css';
                if (theme === 'default')
                    theme = defaultTheme;
                if (!theme && this.parentContext)
                    theme = this.parentContext.theme;
                if (theme && theme.indexOf('/') === -1) {
                    var ms = theme.split('.'), cat = ms[0], ver = ms[1], name_1 = ms[2];
                    if (ms.length === 1) {
                        cat = 'semantic-ui';
                        ver = 'v2';
                        name_1 = ms[0];
                    }
                    theme = 'lib/semantic/themes/' + cat + '/' + ver + '/semantic.' + name_1 + '.min.css';
                }
                this.theme = theme;
                if (!theme) {
                    theme = _util.getStorage('theme-css') || defaultTheme;
                }
                _util.setStorage('theme-css', theme === defaultTheme ? null : theme);
                var old = $('[id="theme-css"]');
                if (old.attr('href') === theme)
                    return;
                _util.loadCss(theme, { id: 'theme-css' }).then(function (e) { return old.remove(); });
            },
            getXml: function (url, plain) {
                url = this.fixUrl(url);
                return new Promise(function (resolve, reject) {
                    $.get(url, function (content) {
                        if (plain)
                            resolve(content);
                        else {
                            content = _locale.replaceString(content);
                            var xml = $($.parseXML(content));
                            resolve(xml);
                        }
                    }, 'text').fail(function () {
                        console.error('xml file load fail: ' + url);
                        reject();
                    });
                });
            },
            loadConfigXmlPlain: function (url, contents) {
                var _this = this;
                if (contents === void 0) { contents = []; }
                return new Promise(function (resolve, reject) {
                    function success(content) {
                        var star = content.indexOf('<application '), end = content.indexOf('>', star);
                        var parents = [], localeModules = [], version;
                        if (star !== -1) {
                            var appnode_1 = content.substring(star, end);
                            function getattr(name, split) {
                                if (split === void 0) { split = false; }
                                name += '="';
                                var pos = appnode_1.indexOf(name), val;
                                if (pos !== -1)
                                    val = appnode_1.substring(pos + name.length, appnode_1.indexOf('"', pos + name.length));
                                if (val && split)
                                    return val.split(',');
                                return val;
                            }
                            var ps = getattr('extends', true);
                            if (ps)
                                ps.forEach(function (p) {
                                    p = p.trim();
                                    if (p && parents.indexOf(p) === -1)
                                        parents.push(p);
                                });
                            version = getattr('version');
                            ps = getattr('localeModule', true);
                            if (ps)
                                ps.forEach(function (p) {
                                    p = p.trim();
                                    if (p && localeModules.indexOf(p) === -1)
                                        localeModules.push(p);
                                });
                            if (context.shouldEncodeParams === undefined)
                                context.shouldEncodeParams = getattr('shouldEncodeParams');
                        }
                        contents.push({ content: content, localeModules: localeModules, version: version });
                        function fetchParent() {
                            if (!parents.length)
                                return resolve(contents);
                            var p = parents.shift();
                            _context.loadConfigXmlPlain(p, contents).then(fetchParent);
                        }
                        fetchParent();
                    }
                    if (url.startsWith('base64:')) {
                        var text = url.substring(7);
                        text = atob(text);
                        text = decodeURIComponent(text);
                        success(text);
                    }
                    else {
                        url = _this.fixUrl(url);
                        $.get(url, success, 'text').fail(function () { return console.error('load config fail: ' + url); });
                    }
                });
            },
            loadConfigXml: function (url) {
                var _this = this;
                return _context.loadConfigXmlPlain(url).then(function (contents) {
                    var localeModules = [], version;
                    contents.forEach(function (c) {
                        c.localeModules.forEach(function (m) {
                            if (localeModules.indexOf(m) === -1)
                                localeModules.push(m);
                        });
                        version = version || c.version;
                    });
                    if (!_context.version)
                        _context.version = version;
                    return _locale.loadModule(localeModules).then(function () {
                        contents = contents.map(function (c) { return _locale.replaceString(c.content); });
                        var cmain, mainapp;
                        contents.forEach(function (c) {
                            c = $($.parseXML(c));
                            c.find('*[permission]').each(function () {
                                var n = $(this), per = n.attr('permission');
                                if (!_context.hasPermission(per))
                                    n.remove();
                            });
                            if (!cmain) {
                                cmain = c;
                                mainapp = cmain.children('application');
                            }
                            else
                                merge(c);
                        });
                        function merge(xml) {
                            var app = xml.children('application');
                            cpattrto(app[0], mainapp[0], null);
                            cpattrto(app.children('map')[0], mainapp.children('map')[0], mainapp[0]);
                            app.children().each(function () {
                                if (!mainapp.children(this.tagName).length)
                                    mainapp[0].appendChild(this);
                            });
                        }
                        function cpattrto(n1, n2, parentNode) {
                            if (!n1)
                                return;
                            if (!n2)
                                parentNode.appendChild(n1);
                            else
                                $.each(n1.attributes, function (i, a) {
                                    if (!n2.hasAttribute(a.name))
                                        n2.setAttribute(a.name, a.value);
                                });
                        }
                        return cmain;
                    });
                }).then(function (xml) { return _this.setConfigXml(xml); }, function (e) { return window.alert('load config error: ' + e); });
            },
            getText: function (url, plain) {
                url = this.fixUrl(url);
                return new Promise(function (resolve, reject) {
                    $.get(url, function (content) {
                        if (!plain)
                            content = _locale.replaceString(content);
                        resolve(content);
                    }, 'text').fail(function () {
                        console.error('file get fail: ' + url);
                        reject();
                    });
                });
            },
            setConfigXml: function (config) {
                var _this = this;
                this.configXml = config;
                var rootNode = config.children('application');
                var node = this.configRootNode = _util.jQueryToPlain(rootNode, false)[0];
                var metaUrls = rootNode.attr('objMetaUrl'), preloadObjType = rootNode.attr('preloadObjType');
                this.objMetaUrl = metaUrls;
                var actionUrls = rootNode.attr('actionDefUrl');
                var parentMeta = rootNode.attr('useParentMeta');
                if (parentMeta === 'false') {
                    this.useParentMeta = false;
                }
                else {
                    this.useParentMeta = !!this.parentContext;
                }
                parentMeta = rootNode.attr('useParentMetaConfig');
                if (parentMeta === 'false') {
                    this.useParentMetaConfig = false;
                }
                else {
                    this.useParentMetaConfig = !!this.parentContext;
                }
                parentMeta = rootNode.attr('updateParentMeta');
                if (parentMeta !== undefined && parentMeta.length)
                    this.updateParentMeta = parentMeta;
                if (actionUrls) {
                    actionUrls = actionUrls.split(',');
                    actionUrls.forEach(function (url) {
                        _context.getXml(url).then(_this.parseActionFile);
                    });
                }
                config.find('action').each(function (i, action) {
                    _context.parseActionNode(action);
                });
                return new Promise(function (resolve, reject) {
                    var xmls = [];
                    function ok() {
                        _context.objMetaXmls = xmls.concat(_context.objMetaXmls);
                        _context.events.trigger('EVENT_CONTEXT_CONFIG_LOADED');
                        _context.configLoaded = true;
                        if (preloadObjType && preloadObjType.length)
                            _bean.getMetas(preloadObjType.split(',')).then(function () { return resolve(config); });
                        else
                            resolve(config);
                    }
                    if (_context.useParentMetaConfig) {
                        _context.xmlsByUrl = Object.assign({}, _context.parentContext.xmlsByUrl);
                        _context.objMetaXmls = [].concat(_context.parentContext.objMetaXmls);
                    }
                    if (metaUrls) {
                        metaUrls = metaUrls.split(',');
                        load();
                        function load() {
                            if (!metaUrls.length) {
                                ok();
                                return;
                            }
                            var url = metaUrls.shift().trim(), urlPath = url;
                            if (url.startsWith('./')) {
                                var mainPath = _context.configPath;
                                if (mainPath.indexOf('/') !== -1)
                                    mainPath = mainPath.substring(0, mainPath.lastIndexOf('/'));
                                else
                                    mainPath = "";
                                urlPath = mainPath + url.substring(1);
                            }
                            if (!_context.xmlsByUrl[urlPath])
                                _context.getXml(url).then(function (metaFile) {
                                    xmls.push(metaFile);
                                    _context.xmlsByUrl[urlPath] = metaFile;
                                    load();
                                }, load);
                            else
                                load();
                        }
                    }
                    else
                        ok();
                });
            },
            hasMetaDefined: function (objectType) {
                var c = function (metaFile) { return metaFile.find("ObjMeta[objectType='".concat(objectType, "']")).length; };
                return this.objMetaXmls.some(c) || c(this.configXml);
            },
            findConfig: function (selector, parentFirst) {
                if (parentFirst === void 0) { parentFirst = false; }
                var ret;
                if (parentFirst && this.parentContext)
                    ret = this.parentContext.findConfig(selector, true);
                if (!ret || !ret.length)
                    ret = this.configXml.find(selector);
                if (!ret.length && !parentFirst && this.parentContext)
                    ret = this.parentContext.findConfig(selector, false);
                return ret;
            },
            getSetting: function (name) {
                var v = _util.fetchPropertyValue(this.settings, name);
                if (v === undefined && this.configRootNode)
                    v = _util.fetchPropertyValue(this.configRootNode, name);
                if (v === undefined) {
                    if (this.parentContext)
                        v = this.parentContext.getSetting(name);
                    else if (this.clientConf)
                        v = _util.fetchPropertyValue(this.clientConf, name);
                }
                return v;
            },
            setSetting: function (name, value) {
                if (arguments.length === 1 && typeof name === 'object')
                    for (var k in name)
                        this.setSetting(k, name[k]);
                this.settings[name] = value;
            },
            fullDataUrl: function (url, appendToken, tokenKey) {
                if (appendToken === void 0) { appendToken = true; }
                if (url.indexOf('http') !== 0)
                    url = this.dataRootUrl + url;
                if (appendToken && _context.access_token) {
                    tokenKey = tokenKey || 'access_token';
                    if (url.indexOf(tokenKey + '=') === -1) {
                        url += url.indexOf('?') === -1 ? '?' : '&';
                        url += tokenKey + '=' + _context.access_token;
                    }
                }
                return url;
            },
            parseActionNode: function (action) {
                action = _util.jQueryToPlain(action, 'children', false, function (node) {
                    if (_locale)
                        _locale.configNode(node);
                });
                if (action.id || action.name) {
                    var already = _context.actionDefs[action.id || action.name];
                    if (already)
                        action = Object.assign({}, already, action);
                    _context.actionDefs[action.id || action.name] = action;
                }
            },
            parseActionFile: function (actionFile) {
                actionFile.find('action').each(function (i, action) {
                    _context.parseActionNode(action);
                });
            },
            findAction: function (id) {
                var a = this.actionDefs[id];
                if (!a && this.parentContext)
                    a = this.parentContext.findAction(id);
                return a;
            },
            findActions: function (match) {
                var r = [];
                for (var k in this.actionDefs) {
                    var v = this.actionDefs[k];
                    if (match(v))
                        r.push(v);
                }
                return r;
            },
            doAction: function (action, params, source, actionTargetEl, el) {
                if (action instanceof jQuery)
                    action = _util.jQueryToPlain(action[0], true, true);
                if (typeof action === 'string')
                    action = _context.findAction(action);
                if (typeof action === 'function')
                    return action(params, source);
                if (action.context === 'top') {
                    var topContext = void 0;
                    try {
                        topContext = topWindow._context;
                    }
                    catch (e) {
                    }
                    if (!topContext)
                        topContext = _context.rootContext;
                    if (topContext && topContext !== _context)
                        return topContext.doAction(Object.assign({ _orignalContext: _context }, action), params, source, actionTargetEl);
                }
                var parentId = action.extends || action.id || action.name;
                action = Object.assign({}, parentId ? _context.findAction(parentId) : undefined, action);
                if (action.mainConfig && !action.url) {
                    action.url = 'main.html?config=' + action.mainConfig;
                    if (_context.debugMode)
                        action.url += '&debug=true';
                }
                if (action.serverUrl) {
                    action.url = _context.fullDataUrl(action.serverUrl);
                }
                var _actionContext = _context.lastActionData = {
                    def: action, action: action, params: params,
                    source: source, sourceui: source && source.sourceui ? source.sourceui : source, targetEl: actionTargetEl, el: el
                };
                _actionContext.item = params;
                _actionContext.items = params ? (Array.isArray(params) ? params : [params]) : [];
                for (var k in action)
                    if (k !== 'content' && typeof action[k] === 'string') {
                        var v = action[k];
                        if (typeof v === 'string' && v.indexOf('${') !== -1)
                            action[k] = _util.replaceVar(v, params || urlParams);
                    }
                var type = action.type;
                var method = action.method || action.name;
                if (!type && !action.url && !action.contentUrl && action.objectType)
                    type = 'obj';
                if (!method && type === 'obj' && action.formName !== undefined)
                    method = 'modify';
                if (action.objectType && action.type !== 'obj' && !action.url && !action.contentUrl && !action.content && !action.html) {
                    action.url = _bean.getGridUrl(action, action.objectType);
                }
                if (action.url && action.urlParams) {
                    action.url = _util.appendUrlParam(action.url, action.urlParams);
                }
                _context.events.trigger('action_call', action);
                var ret;
                if (_context.actionHandler) {
                    ret = _context.actionHandler(action, params, source, _actionContext);
                }
                if (ret)
                    return ret;
                if (action.fn instanceof Function)
                    return action.fn(params, action, source, _actionContext);
                if (type === 'modal') {
                    var div = $('<div class="ui modal">').appendTo($('body'));
                    _sui.render(div, action, _actionContext);
                    var opt = {
                        onHidden: function () {
                            div.remove();
                        }
                    };
                    opt = Object.assign(opt, action.options);
                    if (action.closable !== undefined)
                        opt.closable = action.closable;
                    var modalEl = _sui.showModal(div, opt);
                }
                else if (type === 'window') {
                    var opt = action;
                    opt.actionContext = _actionContext;
                    var canCache = action._cache;
                    if (params)
                        canCache = false;
                    if (canCache === undefined)
                        canCache = _context.getSetting('defaultActionWindowCache');
                    if (canCache === undefined)
                        canCache = true;
                    return _sui.showWindow(action, canCache);
                }
                else if (type === 'obj') {
                    var m = _context.bean[method];
                    if (!m && action.isRemoteAction)
                        return _bean.action(action.objectType, action.name, params, action.successMessage ? {
                            _showLoading: { success: action.successMessage }
                        } : undefined);
                    return m(params, action, source);
                }
                else if (type === 'map') {
                    return _context.map[method](params, action, source);
                }
                else if (type === 'script') {
                    if (action.url) {
                        _context.getText(action.url).then(function (content) {
                            eval(content);
                        });
                    }
                    else
                        eval(action.script || action.content);
                }
                else if (type === 'exp') {
                    var url = _context.fullDataUrl("bean/exp.do?_expid=".concat(action.name, "&_type=").concat(params.objectType, "&id=").concat(params.id));
                    window.open(url, '_blank');
                }
                else if (action.url) {
                    window.open(action.url, action.method || '_blank');
                }
            },
            openMain: function (configPath, params) {
                this.doAction(Object.assign({ mainConfig: configPath }, params));
            },
            getAddUrl: function (objType) {
                return this.fullDataUrl('bean/add.do?_type=' + objType, false);
            },
            events: {
                on: function () {
                    eventsAttached_1.push([arguments[0], arguments[1]]);
                    _context.eventbus.on.apply(_context.eventbus, arguments);
                    return this;
                },
                one: function () {
                    eventsAttached_1.push([arguments[0], arguments[1]]);
                    _context.eventbus.one.apply(_context.eventbus, arguments);
                    return this;
                },
                off: function () {
                    _context.eventbus.off.apply(_context.eventbus, arguments);
                    return this;
                },
                trigger: function () {
                    _context.eventbus.trigger.apply(_context.eventbus, arguments);
                },
                once: function () {
                    return this.one.apply(this, arguments);
                }
            },
            message: function (content, title, autoHide, hideInfoIcon) {
                if (autoHide === void 0) { autoHide = true; }
                if (hideInfoIcon === void 0) { hideInfoIcon = false; }
                return _sui.message(content, title, autoHide, hideInfoIcon);
            },
            removeMessage: function (messageInstance) {
                return _sui.removeMessage(messageInstance);
            },
            progress: function (percent, label) {
                if (arguments.length === 1 && typeof percent === 'string') {
                    label = percent;
                    percent = undefined;
                }
                if (percent === false || percent === 100) {
                    if (!this.progressInstance)
                        return;
                    this.progressInstance.complete();
                    return this.progressInstance = undefined;
                }
                if (!this.progressInstance) {
                    var el = $('<div id="main_progress_loading">').appendTo($('body'));
                    var opt;
                    if (arguments.length === 1 && typeof percent === 'object') {
                        opt = percent;
                        var background = opt.background, color = opt.color;
                        el.css('background', background);
                    }
                    this.progressInstance = _sui.progress(el, Object.assign({
                        style: 'color: white;', label: 'loading..', completeCallback: function () {
                            el.remove();
                        }
                    }, opt));
                    if (opt && opt.auto) {
                        var per_1 = 0;
                        function animate() {
                            per_1 += typeof opt.auto === 'number' ? opt.auto : .2;
                            if (_context.progressInstance) {
                                if (per_1 < 96)
                                    requestAnimationFrame(animate);
                                _context.progressInstance.setPercent(per_1);
                            }
                        }
                        animate();
                    }
                }
                if (percent)
                    this.progressInstance.setPercent(percent);
                if (label)
                    this.progressInstance.setLabel(label);
                return this.progressInstance;
            },
            configReady: function (callback) {
                if (this.configLoaded)
                    callback();
                else
                    this.events.on('EVENT_CONTEXT_CONFIG_LOADED', callback);
            },
            mapReady: function (callback) {
                var _this = this;
                if (this.map)
                    callback(this.map, this.map.L);
                else
                    this.events.one('EVENT_MAP_CREATED', function () {
                        if (_this.map)
                            callback(_this.map, _this.map.L);
                    });
            },
            closeLastActionModal: function () {
                if (this.lastActionData && this.lastActionData.modalEl) {
                    this.lastActionData.modalEl.modal('hide');
                }
            },
            minimizeAllMdiWindow: function () {
                _sui.minimizeAllMdiWindow();
            },
            _currentUser: null,
            getUser: function (force) {
                if (!this._currentUser && parentContext && currentWindow !== top.window)
                    this._currentUser = parentContext.getUser(true);
                if (force)
                    return this._currentUser;
                if (this._currentUser)
                    return Promise.resolve(this._currentUser);
                var ps = { app: window.location.href };
                var accessTokenInUrl;
                var tokenKeys = _context.getSetting('accessTokenUrlKeys') || ['access_token', 'ticket', 'iamcaspticket', 'sso_sessionid'];
                if (typeof tokenKeys === 'string')
                    tokenKeys = tokenKeys.split(',');
                for (var i = 0; i < tokenKeys.length; i++) {
                    accessTokenInUrl = urlParams[tokenKeys[i].trim()];
                    if (accessTokenInUrl)
                        break;
                }
                if (!accessTokenInUrl)
                    accessTokenInUrl = _context.getSetting('access_token');
                if (accessTokenInUrl && !sessionStorage.getItem('access_token'))
                    ps.access_token = accessTokenInUrl;
                return _bean.post('app/getUser.do', ps).then(function (u) {
                    _context._currentUser = u;
                    if (u) {
                        var accessToken_1 = _context.access_token || sessionStorage.getItem('access_token') || accessTokenInUrl || u.access_token;
                        if (accessToken_1) {
                            if (!_context.access_token)
                                _context.access_token = accessToken_1;
                            var topSelfDomain = false;
                            try {
                                if (topWindow._context && topWindow._bean)
                                    topSelfDomain = true;
                                if (window.location.href.indexOf(accessToken_1) !== -1)
                                    history.replaceState(null, null, window.location.href.replace(accessToken_1, ''));
                            }
                            catch (e) {
                            }
                            if (topSelfDomain && !sessionStorage.getItem('access_token'))
                                sessionStorage.setItem('access_token', accessToken_1);
                        }
                    }
                    if (!_context.access_token) {
                        _context.access_token = sessionStorage.getItem('access_token');
                    }
                    return u;
                });
            },
            hasPermission: function (permission, source) {
                if (!_context._currentUser)
                    return;
                if (!permission)
                    return true;
                if (_util.isjquery(permission)) {
                    var el = permission;
                    permission = el.data('permission') || el.attr('data-permission') || el.attr('permission');
                    if (!permission)
                        return true;
                }
                if (this.permissionHandler) {
                    var chk = this.permissionHandler(permission, source);
                    if (typeof chk === 'boolean')
                        return chk;
                    if (typeof chk === 'string' || Array.isArray(chk))
                        permission = chk;
                }
                if (_context._currentUser.isAdmin)
                    return true;
                var userAuthorities = _context._currentUser.authorities;
                if (!userAuthorities)
                    return true;
                if (typeof permission === 'string' && permission.indexOf(',') !== -1)
                    permission = permission.split(',');
                if (Array.isArray(permission))
                    return permission.every(function (p) { return _context.hasPermission(p); });
                if (typeof permission !== 'string')
                    return _context.hasPermission(permission.permission);
                return userAuthorities.indexOf(permission.trim()) !== -1;
            },
            hasCityMark: function (citymark) {
                var citymarks = this.getSetting('cityMark') || '';
                if (typeof citymarks === 'string')
                    citymarks = citymarks.split(',');
                return citymarks.indexOf(citymark) !== -1;
            },
            findMap: function (active, justSelfWindow) {
                var cc = this.findMapContext(active, justSelfWindow);
                return cc ? cc.map : undefined;
            },
            findMapContext: function (active, justSelfWindow) {
                var cc = _context, map = cc.map;
                while (!map && !justSelfWindow && cc.parentContext) {
                    cc = cc.parentContext;
                    map = cc.map;
                }
                if (map && active) {
                    cc.window.activeMapWorkspace();
                }
                return map ? cc : undefined;
            },
            getTopContext: function () {
                return _context.rootContext || _context;
            },
            getTopWindow: function () {
                return this.getTopContext().window;
            }
        };
        context.window = currentWindow;
        if (parentContext) {
            context.parentContext = parentContext;
            context.rootContext = parentContext.rootContext || parentContext;
            context.eventbus = parentContext.eventbus;
            context.dicts = parentContext.dicts;
            if (context.shouldEncodeParams === undefined)
                context.shouldEncodeParams = parentContext.shouldEncodeParams;
        }
        currentWindow._context = context;
        $(currentWindow).on('unload', function () {
            eventsAttached_1.forEach(function (e) { return _context.events.off(e[0], e[1]); });
        });
    }
})();
//# sourceMappingURL=_context.js.map