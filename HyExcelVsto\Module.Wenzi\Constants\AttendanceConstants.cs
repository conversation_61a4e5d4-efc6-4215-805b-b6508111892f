/*
 * ============================================================================
 * 功能模块：考勤系统常量定义
 * ============================================================================
 * 
 * 模块作用：定义考勤系统中使用的所有常量值
 * 
 * 主要功能：
 * - 状态常量：定义考勤状态的标准值（正常、缺卡、迟到等）
 * - 备注常量：定义考勤备注的标准文本
 * - 类型常量：定义考勤类型的分类标识
 * 
 * 注意事项：
 * - 所有常量值应保持一致性，避免硬编码
 * - 修改常量值时需要考虑对整个系统的影响
 * - 常量值应该具有良好的可读性和语义性
 * ============================================================================
 */

namespace HyExcelVsto.Module.Wenzi.Constants
{
    /// <summary>
    /// 考勤系统常量定义类
    /// </summary>
    /// <remarks>
    /// 集中定义考勤系统中使用的所有常量值，确保系统的一致性
    /// </remarks>
    public static class AttendanceConstants
    {
        /// <summary>
        /// 考勤状态常量定义
        /// </summary>
        /// <remarks>定义员工考勤的各种状态标识</remarks>
        public static class Status
        {
            /// <summary>正常考勤状态</summary>
            public const string Normal = "正常";
            /// <summary>缺卡状态</summary>
            public const string Missing = "缺卡";
            /// <summary>迟到状态</summary>
            public const string Late = "迟到";
            /// <summary>早退状态</summary>
            public const string Early = "早退";
            /// <summary>打卡异常状态</summary>
            public const string Exception = "打卡异常";
            /// <summary>休息状态</summary>
            public const string Rest = "休";
        }

        /// <summary>
        /// 考勤备注常量定义
        /// </summary>
        /// <remarks>定义考勤记录中使用的标准备注文本</remarks>
        public static class Notes
        {
            /// <summary>无打卡记录备注</summary>
            public const string NoClockRecord = "无打卡记录";
            /// <summary>提前上班备注</summary>
            public const string EarlyWork = "提前上班";
            /// <summary>延迟下班备注</summary>
            public const string DelayedWork = "延迟下班";
            /// <summary>中午下班早退备注</summary>
            public const string NoonEarlyLeave = "中午下班早退";
            /// <summary>中午下班打卡异常备注</summary>
            public const string NoonOutException = "中午下班打卡异常";
            /// <summary>中午上班打卡异常备注</summary>
            public const string NoonInException = "中午上班打卡异常";
            /// <summary>中午上班迟到备注</summary>
            public const string NoonLate = "中午上班迟到";
        }

        /// <summary>
        /// 考勤类型常量定义
        /// </summary>
        /// <remarks>定义不同的考勤打卡类型</remarks>
        public static class Types
        {
            /// <summary>四次卡类型（上班、中午下班、中午上班、下班）</summary>
            public const string FourTimesCard = "四次卡";
            /// <summary>普通卡类型（上班、下班）</summary>
            public const string NormalCard = "普通卡";
        }
    }
}