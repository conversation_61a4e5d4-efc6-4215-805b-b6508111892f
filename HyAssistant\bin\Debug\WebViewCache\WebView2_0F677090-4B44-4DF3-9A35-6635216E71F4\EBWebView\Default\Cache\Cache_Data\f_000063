﻿<?xml version="1.0" encoding="utf-8"?>
<application extends="config/gdo3/g5.map.xml" title="{{g5.applicationTitle}}"
             version="1.0"
             actionDefUrl="config/gdo3/g5.action.xml,config/gdo3/bimview-action.xml,config/gdo3/address-action.xml"
             objMetaUrl="config/gdo3/g5.meta.xml,config/gdo3/g5.meta.s.xml,config/gdo3/g5.meta.o.xml,config/gdo3/g5.meta.e.xml,config/gdo3/g5.meta.a.xml,config/gdo3/g5.meta.t.xml"
             dictionaryCache="true"
    >
    <script src="lib/common/index.js"></script>
    <script src="apps/pipe/listener.js"></script>
    <script src="apps/pipe/defaultPermissionHandler.js"></script>
    <script>
        <![CDATA[
            //64138 【广东电信】新增OBD复制目标设备，是否快速扩容字段不继承
            _bean.fieldsNoTemplate.push('IS_KSKR');
            
            //按配置替换一些默认action
            //var act = _context.findAction('devicecoveraddress_man');
            //if (act && _context.getSetting('zjAddressCoverMode')) {
              //console.log(act);
              //act.mainConfig = 'modules/address/devicecoveraddress_zjz.xml&deviceId=${id}';
            //}

            //显示公告
            // _context.doAction('show_announce');

            //全局新增前处理
            _bean.autoInputDataFns.push(function(objectType, data){
              var auto = {};
              var user = _context.getUser(true);
              if (!data.CREATE_DATE) auto.CREATE_DATE = new Date();
              if (!data.CREATOR && user) auto.CREATOR = user.USERNAME;
              
              if (data.shape && _context.map) {
                if (data.shape.indexOf('LINESTRING') !== -1 && data.LENGTH === undefined) auto.LENGTH = _context.map.length(data.shape);
                if (data.shape.indexOf('LINESTRING') !== -1 && data.MAP_LENGTH === undefined) auto.MAP_LENGTH = _context.map.length(data.shape);
                if (data.shape.indexOf('POLYGON') !== -1 && data.MEASURE_AREA === undefined) auto.MEASURE_AREA = _context.map.geodesicArea(data.shape, 2);
              }
              return auto;
            });
            //全局指定对象详细展示里的itemMetasField
            _context.objMetaDefault = {
                itemMetasField: function(data){
                    var om = _bean.getMetaForce(data.objectType);
                    if(data.objectType == 'PIPESECTION' || data.objectType == 'HANGLINGSECTION' || data.objectType == 'COMMONLINE' || data.objectType == 'BURIEDSECTION'){
                        return ['<b>' + om.label + '</b>','  {{g5.fields.CODE}}: ' + data.CODE , '  {{g5.fields.LENGTH}}: '  + data.LENGTH + 'm'];
                    }else if(data.objectType == 'OCABLESECTION' || data.objectType == 'ECABLESECTION'){
                         return ['<b>' + om.label + '</b>','  {{g5.fields.CODE}}: ' + data.CODE , '  {{g5.fields.LENGTH}}: '  + data.LENGTH + 'm', '  {{g5.fields.CAPACITY}}: '  + data.CAPACITY + 'D'];
                    }else if(data.objectType == 'POSITION'){
                        return ['<b>' + om.label + '</b>',' 完整名称:(地址ID:'+data.id+')('+data.addressLevel+'级)' + data.FULL_NAME ];
                    }else if(data.objectType == 'SAFETYSIGN'){
                        return ['<b>' + data.CODE  + '</b>','  {{g5.fields.CODE}}: ' + data.CODE ];
                    }else if(data.objectType.indexOf('TEMPLATE') != -1){
                      var fs = ['<b>' + om.label + '</b>'];
                      if (om.findField('NAME')) fs.push('  {{g5.fields.NAME}}: ' + data.NAME);
                      return fs;
                    }else {
                      var fs = ['<b>' + om.label + '</b>'];
                      if (om.findField('NAME')) fs.push('  {{g5.fields.NAME}}: ' + data.NAME);
                      return fs;
                    }
                }
            };
            //全局在表单中增加资产卡片
            function addAssetTab(fm) {
              if (fm.fields[0].type !== 'tab') {
                fm.fields.unshift({type: 'tab', label: '对象属性'});
              }
              fm.fields.push({type: 'tab', label: '资产卡片'});
              fm.fields.push({name: 'asset.category', type: 'obj', label: '资产目录', rtype: 'ASSET_CATEGORY'});
              fm.fields.push({name: 'asset', type: 'obj', label: '资产卡片号', rtype: 'ASSET', readOnly: true, queryAssemble: 'asset[category]'});
              fm.fields.push({name: 'asset.BUKRS', type: 'text', label: '公司代码', readOnly: true});

            }
            _context.objMetaDefaultFns.push(function(om){
              if (!om.findField('asset')) return;
              var addfm = om.findForm('add'), modifyfm = om.findForm('modify');
              if (addfm) addAssetTab(addfm);
              if (modifyfm) addAssetTab(modifyfm);
              if (!addfm || !modifyfm) {
                var fm = om.findForm('');
                if (fm) addAssetTab(fm);
              }
            });
            //地址有管理界面,将ADDRESS_TREE_LOCATE事件指派到该界面
            if (_context.findAction('addressman'))
              _context.events.on('ADDRESS_TREE_LOCATE', function(e, data){
                //activeWorkspace(_context.findAction('addressman').title);
                if (_context.doAddressTreeLocate) _context.doAddressTreeLocate(data);
                _context.doAction({name: 'addressman', urlParams: {addressId: data.id || data}});
              });

            // 添加对动态校验字段处理
            _bean.formValidateFns.push(function (objtype, data, form,mode) {
                function hasFieldWithNoValue() {
                    let has = false, v = false;
                    for (let i = 0; i < arguments.length; i++) {
                        let name = arguments[i];
                        if (form.getField(name)) has = true;
                        if (form.getData()[name] !== undefined && form.getData()[name] !== null) v = true;
                    }
                    return has && !v;
                }
                switch (objtype) {
                    case "WELL" :
                        let facilityTypeId = form.getField('FACILITY_TYPE_ID').dropdown.getSelectedItem();
                        if(!facilityTypeId || !facilityTypeId.id) {
                            throw new Error('请选择井类别');
                        } else if(facilityTypeId.id !== '80203103') {
                            if(hasFieldWithNoValue('MODEL_ID')) {
                                throw new Error('井类型不能为空');
                            }
                        }
                        break;
                    case "DP" :
                        let deviceTypeId = form.getField('DEVICE_TYPE_ID').dropdown.getSelectedItem();
                        if(!deviceTypeId || !deviceTypeId.id) {
                            throw new Error('请选择分线盒类型');
                        } else if(deviceTypeId.id !== '80205077') {
                            if(hasFieldWithNoValue('parentDevice')) {
                                throw new Error('所属设备不能为空');
                            }
                        }
                        break;
                    case "GJ" :
                        if (mode=='add') {//新增
                            //校验标准地址必须为有效且审核通过的9、10级地址
                            let address = form.getField('addressobj')
                            if ( address.getSelectedItem().addressLevel<9 || address.getSelectedItem().IS_VALID != 100383 || address.getSelectedItem().IS_AUDIT != 80203302 ) {
                                throw new Error('请选择一个有效且审核通过的9、10级地址','提示');
                            }

                            //校验所选工程
                            let project = form.getField('project')
                            if (project) {
                                let project_status_id = project.getSelectedItem().PROJECT_STATUS_ID
                                if (project_status_id && project_status_id == 80204668) {
                                    throw new Error('工程不能为已验收工程!');
                                }
                            }
                        }
                        break;
                    case "ODF" :
                        if (mode=='add') {//新增
                            //校验标准地址必须为有效且审核通过的9、10级地址
                            let address = form.getField('addressobj')
                            if ( address.getSelectedItem().addressLevel<9 || address.getSelectedItem().IS_VALID != 100383 || address.getSelectedItem().IS_AUDIT != 80203302 ) {
                                throw new Error('请选择一个有效且审核通过的9、10级地址','提示');
                            }

                            //校验所选工程
                            let project = form.getField('project')
                            if (project) {
                                let project_status_id = project.getSelectedItem().PROJECT_STATUS_ID
                                if (project_status_id && project_status_id == 80204668) {
                                    throw new Error('工程不能为已验收工程!');
                                }
                            }
                        }
                        break;
                    case "GB" :
                        //校验标准地址必须为有效且审核通过的9、10级地址
                        let address = form.getField('addressobj')
                        if ( address.getSelectedItem().addressLevel<9 || address.getSelectedItem().IS_VALID != 100383 || address.getSelectedItem().IS_AUDIT != 80203302 ) {
                            throw new Error('请选择一个有效且审核通过的9、10级地址','提示');
                        }
                        if (mode=='add') {//新增
                            //校验所选工程
                            let project = form.getField('project')
                            if (project) {
                                let project_status_id = project.getSelectedItem().PROJECT_STATUS_ID
                                if (project_status_id && project_status_id == 80204668) {
                                    throw new Error('工程不能为已验收工程!');
                                }
                            }
                        }
                        break;
                    case "GF" :
                        //校验标准地址必须为有效且审核通过的9、10级地址
                        let address1 = form.getField('addressobj')
                        if ( address1.getSelectedItem().addressLevel<9 || address1.getSelectedItem().IS_VALID != 100383 || address1.getSelectedItem().IS_AUDIT != 80203302 ) {
                            throw new Error('请选择一个有效且审核通过的9、10级地址','提示');
                        }
                        if (mode=='add') {//新增
                            //校验所选工程
                            let project = form.getField('project')
                            if (project) {
                                let project_status_id = project.getSelectedItem().PROJECT_STATUS_ID
                                if (project_status_id && project_status_id == 80204668) {
                                    throw new Error('工程不能为已验收工程!');
                                }
                            }
                        }
                        break;
                    case "OBD" :
                        if (mode=='add') {//新增
                            //校验标准地址必须为有效且审核通过的9、10级地址
                            let address = form.getField('addressobj')
                            if ( address.getSelectedItem().addressLevel<9 || address.getSelectedItem().IS_VALID != 100383 || address.getSelectedItem().IS_AUDIT != 80203302 ) {
                                throw new Error('请选择一个有效且审核通过的9、10级地址','提示');
                            }
                        }
                        break;
                }
                if(objtype == "OBD") {
                    return new Promise(function (resolve, reject) {
                        if (form.getField('MATERIAL_OBJECT_ID').value != form.getField('MATERIAL_OBJECT_ID').original) {//修改了旧实物ID才触发
                            _bean.callService("deviceRelateServiceImpl", "checkMaterialObjectId", [form.getField('MATERIAL_OBJECT_ID').value]).then(function (res) {
                                if (res != null&&res != "") {
                                    _sui.alert(res);
                                    reject()
                                }
                                resolve()
                            })
                        } else {
                            resolve()
                        }
                    })
                }
                if(objtype == 'DDF'){
                    debugger
                    if (mode=='modify') {//修改
                        return new Promise(function (resolve, reject) {
                            let lifeStateField = form.getField('LIFE_STATE_ID')
                            if (lifeStateField && lifeStateField.dropdown) {
                                let lifeState = lifeStateField.dropdown.getSelectedItem()
                                // 退网
                                if (lifeState.id === 100375 || lifeState.id === '100375') {
                                    // 当前DDF是否有被业务占用
                                    let param = {deviceId: data.id}
                                    _bean.action('DDF', 'queryDdfHasBusiness', param).then(function (isHasBusiness) {
                                        if(isHasBusiness) {
                                            // 有被占用，不允许修改生命周起状态为退网
                                            _sui.alert('该设备存在业务引用，不允许设备实体修改生命周期状态为退网', '提示')
                                            reject()
                                        } else {
                                            // 没有被占用，弹窗询问是否清理DDF关联的CM_LINK和CR_LINK_LINK数据
                                            _sui.confirm('该DDF无业务占用，但是存在工程关联或者CC跳接，是否一并清理冗余工程关联及CC跳接？', '提示', '是', '否').then(function () {
                                                debugger
                                                _bean.action('DDF', 'cleanDdfLink', param).then(function (res) {
                                                    if(res) {
                                                        resolve()
                                                    } else {
                                                        reject()
                                                    }
                                                }).catch(function(err) {
                                                    reject()
                                                })
                                            }, function () {
                                                // nothing to do
                                                debugger
                                                resolve()
                                            })
                                        }
                                    })
                                }
                            }
                        })
                    }
                }
            });
        ]]>
    </script>>
		
    <sidenav style="width:18%;background:#edf2f9;border:1px solid #f3f3f3;padding-right:3px;">
        <accordionItem permission="SPACE_FIND" title="{{g5.menu.spaceTree}}" icon="codepen" contentUrl="modules/space/navtree.html"/>
<!--        <accordionItem title="{{g5.menu.layerControl}}" icon="map outline" contentUrl="modules/map/layercontrol.html"/>-->
<!--         <accordionItem title="{{g5.menu.lifeControl}}" icon="circle notch" contentUrl="modules/map/lifecontrol.html"/>-->
<!--        <accordionItem title="{{g5.menu.standardAddress}}" icon="compass outline" contentUrl="apps/pipe/address/standardAddressTree.html"/>-->
<!--        <accordionItem title="{{g5.menu.buildingAddress}}" icon="compass outline" contentUrl="modules/building/buildingAddressTree.html"/>-->
<!--        <accordionItem title="{{g5.menu.gridAreaTree}}" icon="star outline" contentUrl="apps/pipe/gridarea/GridAreaTree.html"/>-->
<!--        <accordionItem title="{{g5.menu.accessarea}}" icon="star outline" contentUrl="apps/pipe/accessarea/AccessAreaTreeZJZ.html"/>-->
<!--        <accordionItem title="{{g5.airport.ductSystem}}" icon="list" contentUrl="modules/pipeSystemMgt/ductSystem/ductsystem.html"/>-->
<!--        <accordionItem title="杆路系统" icon="list" contentUrl="modules/pipeSystemMgt/hanglingSystem/hanglingsystem.html"/>-->
    </sidenav>

    <!--本地网切换-->
    <control style="top:10px;left:19.5em;" contentUrl="modules/common/user-route-switch.html"/>

    <sidenav class="collapsed" float="true" position="right" style="width:12%;background:#edf2f9;border:1px solid #f3f3f3;padding-right:3px;">
        <accordionItem title="{{g5.menu.layerControl}}" icon="map outline" contentUrl="modules/map/layercontrol.html"/>
    </sidenav>

    <control style="top:2px;right:28.5em;" contentUrl="apps/pipe/project/resourceinputproject.html"/>

    <!--导航栏快速搜索配置-->
    <search permission="BASIC_SERVICERES">
      <!--光缆网-->
      <category title="{{g5.menu.ocables}}" permission="OPTICAL_FIND">
          <item objectType="OBD" selected="true" title="分光器" />
          <item objectType="ODF" selected="true" title="光配线架" />
          <item objectType="GJ" selected="true" title="光交接箱" />
          <item objectType="GF" selected="true" title="光分纤箱" />
          <item objectType="GB" selected="true" title="光终端盒" />
          <item objectType="ZHX" selected="true" title="综合配线箱" />
          <item objectType="IDF" selected="true" title="综合配线架" />
          <item objectType="GT" selected="true" title="光缆接头" />
          <item objectType="ONU" selected="true" title="ONU" />
          <item objectType="OLTDEVICE" selected="true" title="OLT" />
          <item objectType="GRESERVE" selected="true" title="光缆预留" />
          <item objectType="OCABLE" selected="true" title="光缆" />
          <item objectType="OCABLESECTION" title="光缆段" selected="true" />
          <item objectType="XBOX" title="XBOX光交箱" selected="true" />
          <item objectType="HUBBOX" title="HUBBOX分光箱" selected="true" />
          <item objectType="ODEVICE" title="光连接设备" selected="false" />
      </category>
      <!--电缆网-->
      <category title="{{g5.menu.ecables}}" permission="ELECTRIC_FIND">
          <item objectType="MDF" selected="true" title="配线架" />
          <item objectType="DDF" selected="true" title="数字配线架" />
          <item objectType="CCP" selected="true" title="交接箱" />
          <item objectType="DP" selected="true" title="分线盒" />
          <item objectType="ZHX" selected="true" title="综合配线箱" />
          <item objectType="IDF" selected="true" title="综合配线架" />
          <item objectType="DT" selected="true" title="电缆接头" />
          <item objectType="DPGROUP" selected="true" title="设备群" />
          <item objectType="ECABLE" selected="true" title="电缆" />
          <item objectType="ECABLESECTION" selected="true" title="电缆段" />
          <item objectType="DRESERVE" selected="true" title="电缆预留" />
      </category>
      <!--支撑网-->
      <category title="{{g5.menu.supports}}" permission="SUPPORTING_FIND">
          <item objectType="PHONEBOOTH" title="电话亭" />
          <item objectType="WELL" title="人手井" />
          <item objectType="DRAWINGPOINT" title="引上点" />
          <item objectType="POLE" title="电杆" />
          <item objectType="SUPPORTPOINT" title="撑点" />
          <item objectType="UNDERWELL" title="地下进线室" />
          <item objectType="PIPESECTION" title="管道段" />
          <item objectType="UPPERSECTION" title="引上段" />
          <item objectType="SHAFT" title="竖井" />
          <item objectType="HANGLINGSECTION" title="吊线段" />
          <item objectType="COMMONLINE" title="关联线" />
          <item objectType="MARKSTONE" title="标石" />
          <item objectType="BURIEDSECTION" title="直埋段" />
      </category>
      <!--空间资源-->
      <category title="{{g5.menu.spaceResource}}" permission="SPACE_FIND">
        <item objectType="SITE" title="局站" />
        <item objectType="ROOM" title="机房" />
        <item objectType="OUTDOORADDRESS" title="安装点" />
      </category>
      <!--辅助设施-->
      <category title="{{g5.menu.ASSISTANCE}}" >
          <item objectType="CABLECOIL" title="盘留" />
          <item objectType="CQJ" title="充气机" />
          <item objectType="WARNINGEQUIPMENT" title="告警器" />
          <item objectType="SENSOR" title="传感器" />
          <item objectType="QMQS" title="气门气塞" />
      </category>
      <!--设备管理-->
      <category title="{{g5.menu.deviceManagement}}">
          <item objectType="DSLAMDEVICE" title="DSLAM设备"/>
          <item objectType="PSTNDEVICE" title="PSTN设备"/>
          <item objectType="AG" title="AG设备"/>
          <item objectType="ROUTER" title="路由器"/>
          <item objectType="eNodeB" title="eNodeB设备"/>
          <item objectType="CDMABTS" title="CDMABTS"/>
          <item objectType="BAS" title="宽带接入设备"/>
      </category>
    </search>
    
  <header id="menubar" contentUrl="modules/common/navheader.html" style="background:#0071bc;color:white;">
  <![CDATA[ 
    <img src="logo.png" width="120px;">
  ]]>
  </header>
  <style>
    <![CDATA[ 
    #menubar .ui.menu .ui.dropdown .menu>.item {
      font-size: 1rem !important;
    }
    ]]>
  </style>
  <menu menuClass="left" itemStyle="color:white;" menuStyle="color:white;font-size:10px;"/>

  <!-- 用户管理参数设置中的左侧列表 -->
  <PrivilegeRootType>
    <item objectType="AREA" />
    <item objectType="DEVICE" />
    <item objectType="FACILITY" />
    <item objectType="ADDRESS" />
  </PrivilegeRootType>
</application>