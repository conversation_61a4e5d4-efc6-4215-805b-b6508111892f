/*
 * ============================================================================
 * 功能模块：邮件附件下载管理模块
 * ============================================================================
 * 
 * 模块作用：提供邮件附件的自动下载、分类存储和管理功能
 * 
 * 主要功能：
 * - 连接IMAP服务器并验证用户凭据
 * - 在邮件文件夹之间移动邮件
 * - 下载邮件附件并按规则分类存储
 * - 根据配置规则对邮件进行分类
 * - 记录已下载邮件UID避免重复下载
 * - 提供邮件下载进度和状态的实时反馈
 * 
 * 执行逻辑：
 * 1. 加载配置文件获取IMAP服务器连接信息和下载规则
 * 2. 连接到IMAP服务器并验证用户凭据
 * 3. 根据配置在邮件文件夹之间移动邮件（可选）
 * 4. 遍历邮件文件夹，获取符合条件的邮件
 * 5. 根据规则对邮件进行分类并创建对应目录
 * 6. 下载邮件附件到指定目录
 * 7. 记录已下载邮件UID到数据库
 * 8. 提供操作日志和进度反馈
 * 
 * 注意事项：
 * - 使用异步操作避免UI阻塞
 * - 实现超时和取消机制防止操作卡死
 * - 使用批量数据库操作提高性能
 * - 处理文件名中的非法字符
 * - 使用日志缓冲区减少UI更新频率
 * ============================================================================
 */

using ET;
using LiteDB;
using MailKit;
using MailKit.Net.Imap;
using MailKit.Search;
using MimeKit;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HyAssistant
{
    /// <summary>
    /// 邮件附件下载器窗体类，用于管理邮件附件的自动下载和分类存储
    /// </summary>
    public partial class MailAttachmentsDownloader : Form
    {
        /// <summary>
        /// 配置文件路径
        /// </summary>
        string IniFilePath = ETConfig.GetConfigDirectory("mail.ini");

        /// <summary>
        /// INI配置文件操作实例
        /// </summary>
        ETIniFile iniFile;

        /// <summary>
        /// 当前下载任务
        /// </summary>
        Task _downloadTask;

        /// <summary>
        /// 取消令牌源，用于控制任务的取消
        /// </summary>
        CancellationTokenSource cancellationTokenSource;

        /// <summary>
        /// 标记当前是否正在执行任务
        /// </summary>
        bool isBusy;

        /// <summary>
        /// 操作超时时间（毫秒）
        /// </summary>
        int timeoutMilliseconds = 60 * 1000;

        /// <summary>
        /// 日志缓冲区，用于批量更新UI
        /// </summary>
        readonly StringBuilder _logBuffer = new StringBuilder(8192);

        /// <summary>
        /// 日志缓冲区锁对象
        /// </summary>
        readonly object _logBufferLock = new object();

        /// <summary>
        /// 日志更新计时器
        /// </summary>
        System.Windows.Forms.Timer _logUpdateTimer;

        /// <summary>
        /// 日志缓冲区最大条目数，超过此数量将触发UI更新
        /// </summary>
        const int LogBufferThreshold = 20;

        /// <summary>
        /// 当前缓冲区中的日志条目数
        /// </summary>
        int _logEntryCount = 0;

        /// <summary>
        /// 邮件分类规则类，定义邮件分类的匹配条件和目标文件夹
        /// </summary>
        public class MailCategoryRule
        {
            /// <summary>
            /// 规则名称
            /// </summary>
            public string Name { get; set; }
            
            /// <summary>
            /// 发件人邮箱匹配列表
            /// </summary>
            public List<string> FromEmails { get; set; } = new List<string>();
            
            /// <summary>
            /// 主题关键词匹配列表
            /// </summary>
            public List<string> SubjectKeywords { get; set; } = new List<string>();
            
            /// <summary>
            /// 目标文件夹路径
            /// </summary>
            public string TargetFolder { get; set; }
            
            /// <summary>
            /// 规则优先级，数值越大优先级越高
            /// </summary>
            public int Priority { get; set; }

            /// <summary>
            /// 检查邮件是否匹配当前规则
            /// </summary>
            /// <param name="from">发件人邮箱地址</param>
            /// <param name="subject">邮件主题</param>
            /// <returns>如果邮件匹配规则返回true，否则返回false</returns>
            public bool MatchesEmail(string from, string subject)
            {
                bool fromMatch = FromEmails.Count == 0 ||
                                 FromEmails.Any(email => from.IndexOf(email, StringComparison.OrdinalIgnoreCase) >= 0);

                bool subjectMatch = SubjectKeywords.Count == 0 ||
                                    SubjectKeywords.Any(keyword => subject.IndexOf(keyword, StringComparison.OrdinalIgnoreCase) >= 0);

                // 如果FromEmails为空，则只检查subject
                // 如果SubjectKeywords为空，则只检查from
                // 如果两者都不为空，则两者都要匹配
                if (FromEmails.Count == 0)
                    return subjectMatch;

                if (SubjectKeywords.Count == 0)
                    return fromMatch;

                return fromMatch && subjectMatch;
            }
        }

        /// <summary>
        /// 邮件分类配置管理类，负责加载和应用邮件分类规则
        /// </summary>
        public class MailCategoryConfig
        {
            /// <summary>
            /// 配置文件路径
            /// </summary>
            readonly string _configPath;
            
            /// <summary>
            /// 分类规则列表
            /// </summary>
            readonly List<MailCategoryRule> _rules = new List<MailCategoryRule>();
            
            /// <summary>
            /// 默认分类名称
            /// </summary>
            string _defaultCategory = "邮件";
            
            /// <summary>
            /// 是否启用分类功能
            /// </summary>
            bool _enableCategorization = true;

            /// <summary>
            /// 初始化邮件分类配置管理器
            /// </summary>
            /// <param name="configPath">配置文件路径</param>
            public MailCategoryConfig(string configPath)
            {
                _configPath = configPath;
                LoadConfig();
            }

            /// <summary>
            /// 从配置文件加载分类规则
            /// </summary>
            public void LoadConfig()
            {
                _rules.Clear();

                if (!File.Exists(_configPath))
                {
                    // 如果配置文件不存在，使用默认值
                    _defaultCategory = "邮件";
                    _enableCategorization = true;
                    return;
                }

                ETIniFile iniFile = new ETIniFile(_configPath);

                // 读取通用配置
                _defaultCategory = iniFile.IniReadValue("General", "DefaultCategory") ?? "邮件";
                _enableCategorization = bool.Parse(iniFile.IniReadValue("General", "EnableCategorization") ?? "true");

                // 读取所有分组
                string[] sections = GetIniSections(_configPath);
                foreach (string section in sections)
                {
                    if (section.Equals("General", StringComparison.OrdinalIgnoreCase))
                        continue;

                    MailCategoryRule rule = new MailCategoryRule
                    {
                        Name = iniFile.IniReadValue(section, "Name") ?? section,
                        TargetFolder = iniFile.IniReadValue(section, "TargetFolder") ?? _defaultCategory,
                        Priority = int.Parse(iniFile.IniReadValue(section, "Priority") ?? "0")
                    };

                    // 解析FromEmails
                    string fromEmails = iniFile.IniReadValue(section, "FromEmails") ?? string.Empty;
                    if (!string.IsNullOrWhiteSpace(fromEmails))
                    {
                        rule.FromEmails.AddRange(fromEmails.Split(',').Select(e => e.Trim()));
                    }

                    // 解析SubjectKeywords
                    string subjectKeywords = iniFile.IniReadValue(section, "SubjectKeywords") ?? string.Empty;
                    if (!string.IsNullOrWhiteSpace(subjectKeywords))
                    {
                        rule.SubjectKeywords.AddRange(subjectKeywords.Split(',').Select(k => k.Trim()));
                    }

                    _rules.Add(rule);
                }

                // 按优先级排序
                _rules.Sort((a, b) => b.Priority.CompareTo(a.Priority));
            }

            /// <summary>
            /// 获取INI文件中的所有节名称
            /// </summary>
            /// <param name="iniFilePath">INI文件路径</param>
            /// <returns>节名称数组</returns>
            string[] GetIniSections(string iniFilePath)
            {
                // 由于IniFile类没有提供获取所有节的方法，我们自己实现一个简单的解析
                if (!File.Exists(iniFilePath))
                    return new string[0];

                List<string> sections = new List<string>();
                try
                {
                    string[] lines = File.ReadAllLines(iniFilePath);
                    foreach (string line in lines)
                    {
                        string trimmedLine = line.Trim();
                        if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]") && trimmedLine.Length > 2)
                        {
                            string section = trimmedLine.Substring(1, trimmedLine.Length - 2);
                            sections.Add(section);
                        }
                    }
                }
                catch
                {
                    // 忽略读取错误
                }
                return sections.ToArray();
            }

            /// <summary>
            /// 根据邮件信息获取分类目录
            /// </summary>
            /// <param name="message">邮件消息</param>
            /// <param name="yearPrefix">年份前缀</param>
            /// <returns>分类目录路径</returns>
            public string GetCategory(MimeMessage message, string yearPrefix)
            {
                if (message == null || !_enableCategorization)
                    return Path.Combine(yearPrefix, _defaultCategory);

                string from = message.From?.Mailboxes?.FirstOrDefault()?.Address ?? string.Empty;
                string subject = message.Subject ?? string.Empty;

                if (string.IsNullOrWhiteSpace(from))
                    return Path.Combine(yearPrefix, _defaultCategory);

                // 按优先级依次检查规则
                foreach (MailCategoryRule rule in _rules)
                {
                    if (rule.MatchesEmail(from, subject))
                    {
                        return Path.Combine(yearPrefix, rule.TargetFolder);
                    }
                }

                return Path.Combine(yearPrefix, _defaultCategory);
            }
        }

        /// <summary>
        /// 邮件分类配置管理器实例
        /// </summary>
        MailCategoryConfig _mailCategoryConfig;
        
        /// <summary>
        /// 邮件分类配置文件路径
        /// </summary>
        string _mailCategoryConfigPath;

        /// <summary>
        /// 启动邮件下载任务
        /// </summary>
        /// <returns>表示异步操作的任务</returns>
        async Task StartDownloadMail()
        {
            if (isBusy)
            {
                WriteLog("已有任务正在进行中...");
                return;
            }

            try
            {
                // 使用Task.Run将整个下载过程放在后台线程执行
                _downloadTask = Task.Run(async () =>
                {
                    try
                    {
                        await DownloadMailsAsync().ConfigureAwait(false);
                    }
                    catch (Exception ex)
                    {
                        await Task.Run(() => WriteLog($"下载过程发生错误: {ex.Message}")).ConfigureAwait(false);
                    }
                    finally
                    {
                        await Task.Run(() => SetBusyState(false)).ConfigureAwait(false);
                    }
                });

                // 等待任务启动，但不等待完成，使用ConfigureAwait(false)避免死锁
                await Task.Delay(100).ConfigureAwait(false); // 给任务一点启动时间
            }
            catch (Exception ex)
            {
                WriteLog($"启动下载任务时发生错误: {ex.Message}");
                SetBusyState(false);
            }
        }

        /// <summary>
        /// 邮件下载配置类，存储IMAP服务器连接和下载设置
        /// </summary>
        class Configuration
        {
            /// <summary>
            /// IMAP服务器主机地址
            /// </summary>
            public string ImapHost { get; set; }
            
            /// <summary>
            /// IMAP服务器端口
            /// </summary>
            public int ImapPort { get; set; }
            
            /// <summary>
            /// 用户名/邮箱地址
            /// </summary>
            public string Username { get; set; }
            
            /// <summary>
            /// 密码/授权码
            /// </summary>
            public string Password { get; set; }
            
            /// <summary>
            /// 邮件保存目录
            /// </summary>
            public string MailDirectory { get; set; }
            
            /// <summary>
            /// 已下载UID数据库路径
            /// </summary>
            public string DbPath { get; set; }
            
            /// <summary>
            /// 只下载此日期之后的邮件
            /// </summary>
            public DateTime DeliveredAfter { get; set; }
            
            /// <summary>
            /// 需要跳过的文件夹列表
            /// </summary>
            public string[] SkipFolders { get; set; }
            
            /// <summary>
            /// 目标文件夹名称（用于邮件移动）
            /// </summary>
            public string DestinationFolderName { get; set; }
            
            /// <summary>
            /// 源文件夹名称（用于邮件移动）
            /// </summary>
            public string SourceFolderName { get; set; }
        }

        /// <summary>
        /// 尝试加载配置信息
        /// </summary>
        /// <param name="config">输出参数，成功时包含加载的配置信息</param>
        /// <returns>如果成功加载配置返回true，否则返回false</returns>
        bool TryLoadConfiguration(out Configuration config)
        {
            config = null; // 初始化为null，只有在成功读取所有配置时才会创建实例

            try
            {
                string imapHost = iniFile.GetValue("ReceiveMail", "ImapHost");
                int imapPort = Convert.ToInt32(iniFile.GetValue("ReceiveMail", "ImapPort"));
                string username = iniFile.GetValue("ReceiveMail", "Username");
                string password = iniFile.GetValue("ReceiveMail", "Password");
                string mailDirectory = iniFile.GetValue("ReceiveMail", "Directory");
                string dbPath = iniFile.GetValue("ReceiveMail", "UidLiteDB");
                string deliveredAfterRaw = iniFile.GetValue("ReceiveMail", "DeliveredAfter");
                string skipFoldersRaw = iniFile.GetValue("Folder", "SkipFolders");
                string destFolderName = iniFile.GetValue("MoveMail", "DestinationFolder");
                string sourceFolderName = iniFile.GetValue("MoveMail", "SourceFolder");

                // 验证读取的配置参数
                if (string.IsNullOrWhiteSpace(imapHost) ||
                    imapPort <= 0 ||
                    string.IsNullOrWhiteSpace(username) ||
                    string.IsNullOrWhiteSpace(password) ||
                    string.IsNullOrWhiteSpace(mailDirectory) ||
                    string.IsNullOrWhiteSpace(dbPath))
                {
                    WriteLog("配置项有缺失，请检查配置文件。");
                    return false;
                }

                DateTime.TryParse(deliveredAfterRaw, out DateTime deliveredAfter);
                string[] skipFolders = skipFoldersRaw.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);

                // 创建Configuration实例并赋值
                config = new Configuration
                {
                    ImapHost = imapHost,
                    ImapPort = imapPort,
                    Username = username,
                    Password = password,
                    MailDirectory = mailDirectory,
                    DbPath = dbPath,
                    DeliveredAfter = deliveredAfter,
                    SkipFolders = skipFolders,
                    DestinationFolderName = destFolderName,
                    SourceFolderName = sourceFolderName
                };

                return true;
            }
            catch (Exception ex)
            {
                WriteLog($"读取配置文件时出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 执行邮件下载的主要异步任务
        /// </summary>
        /// <returns>表示异步操作的任务</returns>
        public async Task DownloadMailsAsync()
        {
            if (isBusy)
            {
                await Task.Run(() => WriteLog("已有任务正在进行中...")).ConfigureAwait(true);
                return;
            }

            await Task.Run(() => SetBusyState(true)).ConfigureAwait(true);

            if (!TryLoadConfiguration(out Configuration configuration))
            {
                await Task.Run(() => SetBusyState(false)).ConfigureAwait(true);
                return;
            }

            cancellationTokenSource = new CancellationTokenSource(timeoutMilliseconds);

            // 使用匿名方法来处理取消事件，在UI线程上更新状态
            cancellationTokenSource.Token.Register(() =>
            {
                // 保存Task引用但不等待，让它自己完成
                Task _ = Task.Run(() =>
                {
                    try
                    {
                        SetBusyState(false);
                    }
                    catch (Exception ex)
                    {
                        // 记录异常但不抛出
                        System.Diagnostics.Debug.WriteLine($"取消操作时出错: {ex.Message}");
                    }
                });
            });

            using (ImapClient client = new ImapClient())
            {
                try
                {
                    await ConnectToImapServerAsync(client, configuration, cancellationTokenSource.Token).ConfigureAwait(true);
                    if (checkBoxAutoMoveMail.Checked)
                    {
                        await MoveEmailsFromFolderAsync(client, configuration, cancellationTokenSource.Token).ConfigureAwait(true);
                    }

                    await DownloadEmailsFromFoldersAsync(client, configuration, cancellationTokenSource.Token).ConfigureAwait(true);
                    await Task.Run(() => Program.mainForm.textBoxLog.WriteLog($"{DateTime.Now:yyyy-MM-dd HH:mm:ss} 邮件助手：所有邮件处理完成")).ConfigureAwait(true);
                }
                catch (OperationCanceledException)
                {
                    if (cancellationTokenSource.IsCancellationRequested)
                    {
                        await Task.Run(() => WriteLog("操作被用户取消。")).ConfigureAwait(true);
                    }
                }
                catch (Exception ex)
                {
                    await Task.Run(() => WriteLog($"发生错误: {ex.Message}")).ConfigureAwait(true);
                }
                finally
                {
                    cancellationTokenSource.Dispose();
                    await Task.Run(() => SetBusyState(false)).ConfigureAwait(true);
                }
            }
        }

        /// <summary>
        /// 设置窗体的忙碌状态
        /// </summary>
        /// <param name="isBusy">是否处于忙碌状态</param>
        void SetBusyState(bool isBusy)
        {
            if (buttonStart.InvokeRequired)
            {
                buttonStart.BeginInvoke(new Action(() => SetBusyState(isBusy)));
                return;
            }

            this.isBusy = isBusy;
            buttonStart.Text = isBusy ? "停止接收" : "接收邮件";
        }

        /// <summary>
        /// 连接到IMAP服务器
        /// </summary>
        /// <param name="client">IMAP客户端实例</param>
        /// <param name="config">配置信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>表示异步操作的任务</returns>
        async Task ConnectToImapServerAsync(
            ImapClient client,
            Configuration config,
            CancellationToken cancellationToken)
        {
            try
            {
                // 连接到IMAP服务器
                WriteLog($"正在连接到 IMAP 服务器 {config.ImapHost}:{config.ImapPort}...");
                await client.ConnectAsync(config.ImapHost, config.ImapPort, true, cancellationToken).ConfigureAwait(true);
                WriteLog("连接成功。");

                // 使用配置中提供的用户名和密码进行登录
                WriteLog($"正在登录至服务器，用户名: {config.Username}...");
                await client.AuthenticateAsync(config.Username, config.Password, cancellationToken).ConfigureAwait(true);
                WriteLog("登录成功。");
            }
            catch (Exception ex)
            {
                // 将异常信息记录到日志中，并重新抛出异常以便上层处理
                WriteLog($"无法连接到 IMAP 服务器或认证失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 在邮件文件夹之间移动邮件
        /// </summary>
        /// <param name="client">IMAP客户端实例</param>
        /// <param name="config">配置信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>表示异步操作的任务</returns>
        async Task MoveEmailsFromFolderAsync(
            ImapClient client,
            Configuration config,
            CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(config.SourceFolderName) ||
                string.IsNullOrWhiteSpace(config.DestinationFolderName))
            {
                WriteLog("源文件夹或目标文件夹名称未配置，跳过移动邮件步骤。");
                return;
            }

            IMailFolder sourceFolder = null;
            IMailFolder destinationFolder = null;

            try
            {
                sourceFolder = await client.GetFolderAsync(config.SourceFolderName, cancellationToken).ConfigureAwait(true);
                destinationFolder = await client.GetFolderAsync(config.DestinationFolderName, cancellationToken).ConfigureAwait(true);

                // 打开源文件夹以读写模式，准备邮件搜索和移动
                if (!sourceFolder.IsOpen)
                    await sourceFolder.OpenAsync(FolderAccess.ReadWrite, cancellationToken).ConfigureAwait(true);

                // 根据配置来搜索邮件
                DateSearchQuery searchQuery = SearchQuery.DeliveredAfter(config.DeliveredAfter);
                if (sourceFolder.IsOpen) // 再次确认文件夹是否打开
                {
                    IList<UniqueId> uidsToMove = await sourceFolder.SearchAsync(searchQuery, cancellationToken).ConfigureAwait(true);
                    WriteLog($"准备移动 {uidsToMove.Count} 条邮件从 '{sourceFolder.Name}' 到 '{destinationFolder.Name}'");

                    // 如果源文件夹中有邮件可供移动，则执行移动操作
                    if (uidsToMove.Count > 0)
                    {
                        await sourceFolder.MoveToAsync(uidsToMove, destinationFolder, cancellationToken).ConfigureAwait(true);
                        WriteLog("邮件移动完成。");
                    }
                }
                else
                {
                    WriteLog("源文件夹在搜索操作前并没有正确打开。");
                }
            }
            catch (Exception ex)
            {
                WriteLog($"邮件操作过程中发生错误: {ex.Message}");
            }
            finally
            {
                // 无论操作是否成功，都关闭已打开的文件夹
                try
                {
                    if (sourceFolder?.IsOpen ?? false)
                        await sourceFolder.CloseAsync().ConfigureAwait(true);
                }
                catch (Exception ex)
                {
                    WriteLog($"尝试关闭源文件夹时发生错误: {ex.Message}");
                }

                try
                {
                    if (destinationFolder?.IsOpen ?? false)
                        await destinationFolder.CloseAsync().ConfigureAwait(true);
                }
                catch (Exception ex)
                {
                    WriteLog($"尝试关闭目标文件夹时发生错误: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 从邮件文件夹下载邮件
        /// </summary>
        /// <param name="client">IMAP客户端实例</param>
        /// <param name="config">配置信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>表示异步操作的任务</returns>
        async Task DownloadEmailsFromFoldersAsync(
            ImapClient client,
            Configuration config,
            CancellationToken cancellationToken)
        {
            // 连接数据库
            string connectionString = $"Filename={config.DbPath};Mode=Share";
            using (LiteDatabase db = new LiteDatabase(connectionString))
            {
                // 一次性获取所有已下载的UID并缓存到内存中
                ILiteCollection<BsonDocument> mailDownloadedUidCollection = db.GetCollection<BsonDocument>("MailDownloadedUid");
                HashSet<uint> downloadedUids = mailDownloadedUidCollection.FindAll()
                    .Select(x => (uint)x["uid"].AsInt32)
                    .ToHashSet();

                // 缓存发件箱文件夹名称集合
                HashSet<string> sendFolder = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
                {
                    "Sent Messages",
                    "※公司-Mail已发送"
                };

                // 缓存需要跳过的文件夹
                HashSet<string> skipFolders = new HashSet<string>(config.SkipFolders, StringComparer.OrdinalIgnoreCase);

                // 批量保存新下载的UID
                List<BsonDocument> newUidDocuments = new List<BsonDocument>();

                // 设置一次全局超时，而不是每封邮件都重置
                cancellationTokenSource.CancelAfter(timeoutMilliseconds * 10);

                // 统计处理结果
                int totalFolders = 0;
                int totalEmails = 0;
                int processedEmails = 0;

                foreach (IMailFolder folder in client.GetFolders(client.PersonalNamespaces[0]))
                {
                    // 跳过不需要下载的文件夹
                    if (skipFolders.Contains(folder.Name))
                        continue;

                    totalFolders++;

                    // 用于确定主目录名称
                    bool isSendMail = sendFolder.Contains(folder.Name);

                    await folder.OpenAsync(FolderAccess.ReadOnly, cancellationToken).ConfigureAwait(true);
                    WriteLog($"打开目录 {folder.Name}");

                    // 使用缓存的日期查询
                    DateSearchQuery query = SearchQuery.DeliveredAfter(config.DeliveredAfter);
                    IList<UniqueId> uids = await folder.SearchAsync(query, cancellationToken).ConfigureAwait(true);

                    int folderEmailCount = uids.Count;
                    totalEmails += folderEmailCount;

                    // 修改日志格式，添加空格使其更易读
                    WriteLog($"： {folderEmailCount} 封邮件", false);
                    if (!uids.Any())
                        continue;

                    // 每个文件夹只记录一次开始处理的日志
                    WriteLog($">> 开始处理 {folder.Name} 文件夹中的 {folderEmailCount} 封邮件");

                    // 批量处理计数器
                    int folderProcessed = 0;

                    foreach (UniqueId uid in uids)
                    {
                        cancellationTokenSource.Token.ThrowIfCancellationRequested();

                        // 使用内存中的HashSet检查，避免频繁数据库查询
                        if (downloadedUids.Contains(uid.Id))
                        {
                            folderProcessed++;
                            continue;
                        }

                        MimeMessage message = await folder.GetMessageAsync(uid, cancellationToken).ConfigureAwait(true);

                        if (message.Date.Year < 2000)
                        {
                            folderProcessed++;
                            continue;
                        }

                        // 减少日志记录频率，只记录每10封邮件的进度或最后一封
                        bool isLastEmail = folderProcessed == folderEmailCount - 1;
                        bool shouldLogProgress = folderProcessed % 10 == 0 || isLastEmail;

                        if (shouldLogProgress)
                        {
                            WriteLog($"  处理进度：{folderProcessed + 1}/{folderEmailCount} - {message.Subject}");
                        }

                        // 创建邮件分类目录的路径
                        string mailCategory = GetMailCategory(message);
                        string subject = GetMailSubject(message);

                        string mailPath = Path.Combine(
                            config.MailDirectory,
                            isSendMail ? "发件箱" : "收件箱",
                            mailCategory ?? "邮件附件",
                            $"{uid}-{message.Date:yyyyMMdd-HHmmss}--{subject}");

                        if (!Directory.Exists(mailPath))
                            Directory.CreateDirectory(mailPath); // 如果邮件分类目录不存在，则创建它

                        // 下载邮件附件
                        await DownloadAttachmentsAsync(message, mailPath, cancellationToken).ConfigureAwait(true);

                        // 将新下载的UID添加到内存集合和待保存列表中
                        downloadedUids.Add(uid.Id);
                        newUidDocuments.Add(new BsonDocument { { "uid", new BsonValue((int)uid.Id) } });

                        // 增加批量保存的阈值，从100增加到200，减少数据库操作
                        if (newUidDocuments.Count >= 200)
                        {
                            mailDownloadedUidCollection.InsertBulk(newUidDocuments);
                            newUidDocuments.Clear();
                        }

                        folderProcessed++;
                        processedEmails++;
                    }

                    // 每个文件夹处理完成后记录一次日志
                    WriteLog($"<< 完成处理 {folder.Name} 文件夹中的 {folderProcessed} 封邮件");
                }

                // 保存剩余的UID记录
                if (newUidDocuments.Count > 0)
                {
                    mailDownloadedUidCollection.InsertBulk(newUidDocuments);
                }

                // 记录总体处理结果
                WriteLog($"---------------------------------------------");
                WriteLog($"邮件下载完成：共处理 {totalFolders} 个文件夹，{totalEmails} 封邮件，新下载 {processedEmails} 封");
                WriteLog($"---------------------------------------------");
            }
        }

        /// <summary>
        /// 获取邮件主题，并处理文件名中的非法字符
        /// </summary>
        /// <param name="message">邮件消息</param>
        /// <returns>处理后的邮件主题</returns>
        string GetMailSubject(MimeMessage message)
        {
            if (string.IsNullOrEmpty(message.Subject))
                return "无主题";

            // 使用StringBuilder避免多次字符串拼接
            StringBuilder sb = new StringBuilder(message.Subject.Length);
            foreach (char c in message.Subject)
            {
                if (Array.IndexOf(_invalidFileNameChars, c) >= 0)
                    sb.Append('_');
                else
                    sb.Append(c);
            }
            return sb.ToString();
        }

        // 类级别的私有字段，缓存无效字符集合
        char[] _invalidFileNameChars = Path.GetInvalidFileNameChars();

        /// <summary>
        /// 下载邮件的所有附件
        /// </summary>
        /// <param name="message">邮件消息</param>
        /// <param name="directory">保存目录</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>表示异步操作的任务</returns>
        async Task DownloadAttachmentsAsync(MimeMessage message, string directory, CancellationToken cancellationToken)
        {
            // 先检查目录是否存在，避免重复检查
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // 统计附件处理结果，减少日志记录次数
            int totalAttachments = message.Attachments.Count();
            int existingCount = 0;
            int downloadedCount = 0;
            int errorCount = 0;
            List<string> errorFiles = new List<string>();

            foreach (MimeEntity attachment in message.Attachments)
            {
                string fileName = attachment.ContentDisposition?.FileName ?? attachment.ContentType.Name;

                try
                {
                    // 使用更高效的字符串处理方式
                    if (fileName != null)
                    {
                        StringBuilder sb = new StringBuilder(fileName.Length);
                        foreach (char c in fileName)
                        {
                            if (Array.IndexOf(_invalidFileNameChars, c) >= 0)
                                sb.Append('_');
                            else
                                sb.Append(c);
                        }
                        fileName = sb.ToString();
                    }
                    else
                    {
                        fileName = "未命名附件";
                    }

                    string filePath = Path.Combine(directory, fileName);

                    if (File.Exists(filePath))
                    {
                        existingCount++;
                        continue;
                    }

                    // 使用较大的缓冲区来减少IO操作次数
                    using (FileStream fileStream = new FileStream(
                        filePath,
                        FileMode.Create,
                        FileAccess.Write,
                        FileShare.None,
                        bufferSize: 81920,
                        useAsync: true))
                    {
                        if (attachment is MessagePart messagePart)
                            await messagePart.Message.WriteToAsync(fileStream, cancellationToken).ConfigureAwait(true);
                        else if (attachment is MimePart mimePart)
                            await mimePart.Content.DecodeToAsync(fileStream, cancellationToken).ConfigureAwait(true);
                    }

                    File.SetAttributes(filePath, FileAttributes.ReadOnly);
                    downloadedCount++;
                }
                catch (Exception)
                {
                    errorCount++;
                    errorFiles.Add(fileName);
                }
            }

            // 批量记录附件处理结果
            if (totalAttachments > 0)
            {
                StringBuilder logMsg = new StringBuilder();
                logMsg.AppendLine($"      附件处理完成: 共{totalAttachments}个");
                if (existingCount > 0)
                    logMsg.AppendLine($"      - 已存在: {existingCount}个");
                if (downloadedCount > 0)
                    logMsg.AppendLine($"      - 新下载: {downloadedCount}个");
                if (errorCount > 0)
                {
                    logMsg.AppendLine($"      - 下载失败: {errorCount}个");
                    foreach (string errorFile in errorFiles)
                        logMsg.AppendLine($"        * {errorFile}");
                }
                WriteLog(logMsg.ToString(), false);
            }
        }

        /// <summary>
        /// 获取邮件分类目录
        /// </summary>
        /// <param name="message">邮件消息</param>
        /// <returns>分类目录名称</returns>
        string GetMailCategory(MimeMessage message)
        {
            if (_mailCategoryConfig == null)
            {
                _mailCategoryConfigPath = Path.Combine(Path.GetDirectoryName(IniFilePath), "mailcategory.ini");
                _mailCategoryConfig = new MailCategoryConfig(_mailCategoryConfigPath);
            }

            string yearPrefix = message.Date.Year.ToString();
            return _mailCategoryConfig.GetCategory(message, yearPrefix);
        }

        /// <summary>
        /// 写入日志到UI和日志缓冲区
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="addNewLine">是否添加换行符</param>
        void WriteLog(string message, bool addNewLine = true)
        {
            if (string.IsNullOrEmpty(message))
                return;

            lock (_logBufferLock)
            {
                if (addNewLine)
                {
                    _logBuffer.AppendLine(message);
                    _logEntryCount++;
                }
                else
                {
                    _logBuffer.Append(message);
                }

                // 当缓冲区中的日志条目数超过阈值时，刷新日志到UI
                if (_logEntryCount >= LogBufferThreshold)
                {
                    FlushLogBuffer();
                }
            }
        }

        /// <summary>
        /// 将日志缓冲区内容刷新到UI
        /// </summary>
        void FlushLogBuffer()
        {
            if (textBoxLog.InvokeRequired)
            {
                textBoxLog.BeginInvoke(new Action(FlushLogBuffer));
                return;
            }

            lock (_logBufferLock)
            {
                if (_logBuffer.Length > 0)
                {
                    textBoxLog.AppendText(_logBuffer.ToString());
                    textBoxLog.SelectionStart = textBoxLog.Text.Length;
                    textBoxLog.ScrollToCaret();

                    // 同时记录到主窗体日志
                    Program.mainForm.textBoxLog.WriteLog(_logBuffer.ToString());

                    // 清空缓冲区
                    _logBuffer.Clear();
                    _logEntryCount = 0;
                }
            }
        }

        /// <summary>
        /// 初始化窗体组件和事件处理程序
        /// </summary>
        public MailAttachmentsDownloader()
        {
            InitializeComponent();

            // 初始化INI文件操作实例
            iniFile = new ETIniFile(IniFilePath);

            // 初始化日志更新计时器
            _logUpdateTimer = new System.Windows.Forms.Timer
            {
                Interval = 500, // 每500毫秒更新一次UI
                Enabled = true
            };
            _logUpdateTimer.Tick += (sender, e) => FlushLogBuffer();

            // 设置窗体关闭事件处理程序
            FormClosing += (sender, e) =>
            {
                if (isBusy)
                {
                    e.Cancel = true;
                    MessageBox.Show("任务正在进行中，请先停止任务再关闭窗口。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // 停止计时器并释放资源
                    _logUpdateTimer.Stop();
                    _logUpdateTimer.Dispose();
                }
            };

            // 设置按钮点击事件处理程序
            buttonStart.Click += async (sender, e) =>
            {
                if (isBusy)
                {
                    // 如果当前正在执行任务，则取消任务
                    cancellationTokenSource?.Cancel();
                }
                else
                {
                    // 否则启动新任务
                    await StartDownloadMail();
                }
            };

            // 加载配置到UI
            LoadConfigToUI();
        }

        /// <summary>
        /// 加载配置到UI控件
        /// </summary>
        void LoadConfigToUI()
        {
            try
            {
                // 读取自动移动邮件设置
                string autoMoveMail = iniFile.GetValue("MoveMail", "AutoMove");
                checkBoxAutoMoveMail.Checked = !string.IsNullOrEmpty(autoMoveMail) && autoMoveMail.Equals("true", StringComparison.OrdinalIgnoreCase);

                // 读取其他配置项并显示在UI上
                textBoxImapHost.Text = iniFile.GetValue("ReceiveMail", "ImapHost");
                textBoxImapPort.Text = iniFile.GetValue("ReceiveMail", "ImapPort");
                textBoxUsername.Text = iniFile.GetValue("ReceiveMail", "Username");
                textBoxPassword.Text = iniFile.GetValue("ReceiveMail", "Password");
                textBoxMailDirectory.Text = iniFile.GetValue("ReceiveMail", "Directory");
                textBoxDbPath.Text = iniFile.GetValue("ReceiveMail", "UidLiteDB");
                textBoxDeliveredAfter.Text = iniFile.GetValue("ReceiveMail", "DeliveredAfter");
                textBoxSkipFolders.Text = iniFile.GetValue("Folder", "SkipFolders");
                textBoxDestFolder.Text = iniFile.GetValue("MoveMail", "DestinationFolder");
                textBoxSourceFolder.Text = iniFile.GetValue("MoveMail", "SourceFolder");
            }
            catch (Exception ex)
            {
                WriteLog($"加载配置到UI时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存UI控件值到配置文件
        /// </summary>
        void SaveUIToConfig()
        {
            try
            {
                // 保存自动移动邮件设置
                iniFile.WriteValue("MoveMail", "AutoMove", checkBoxAutoMoveMail.Checked.ToString().ToLower());

                // 保存其他配置项
                iniFile.WriteValue("ReceiveMail", "ImapHost", textBoxImapHost.Text);
                iniFile.WriteValue("ReceiveMail", "ImapPort", textBoxImapPort.Text);
                iniFile.WriteValue("ReceiveMail", "Username", textBoxUsername.Text);
                iniFile.WriteValue("ReceiveMail", "Password", textBoxPassword.Text);
                iniFile.WriteValue("ReceiveMail", "Directory", textBoxMailDirectory.Text);
                iniFile.WriteValue("ReceiveMail", "UidLiteDB", textBoxDbPath.Text);
                iniFile.WriteValue("ReceiveMail", "DeliveredAfter", textBoxDeliveredAfter.Text);
                iniFile.WriteValue("Folder", "SkipFolders", textBoxSkipFolders.Text);
                iniFile.WriteValue("MoveMail", "DestinationFolder", textBoxDestFolder.Text);
                iniFile.WriteValue("MoveMail", "SourceFolder", textBoxSourceFolder.Text);

                WriteLog("配置已保存。");
            }
            catch (Exception ex)
            {
                WriteLog($"保存配置时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存按钮点击事件处理程序
        /// </summary>
        private void buttonSave_Click(object sender, EventArgs e)
        {
            SaveUIToConfig();
        }

        /// <summary>
        /// 浏览邮件目录按钮点击事件处理程序
        /// </summary>
        private void buttonBrowseMailDir_Click(object sender, EventArgs e)
        {
            using (FolderBrowserDialog dialog = new FolderBrowserDialog())
            {
                dialog.Description = "选择邮件保存目录";
                dialog.ShowNewFolderButton = true;

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    textBoxMailDirectory.Text = dialog.SelectedPath;
                }
            }
        }

        /// <summary>
        /// 浏览数据库文件按钮点击事件处理程序
        /// </summary>
        private void buttonBrowseDbPath_Click(object sender, EventArgs e)
        {
            using (SaveFileDialog dialog = new SaveFileDialog())
            {
                dialog.Filter = "LiteDB数据库文件|*.db|所有文件|*.*";
                dialog.Title = "选择或创建数据库文件";
                dialog.OverwritePrompt = false;

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    textBoxDbPath.Text = dialog.FileName;
                }
            }
        }

        /// <summary>
        /// 清空日志按钮点击事件处理程序
        /// </summary>
        private void buttonClearLog_Click(object sender, EventArgs e)
        {
            textBoxLog.Clear();
        }

        /// <summary>
        /// 打开邮件目录按钮点击事件处理程序
        /// </summary>
        private void buttonOpenMailDir_Click(object sender, EventArgs e)
        {
            try
            {
                string mailDir = textBoxMailDirectory.Text;
                if (!string.IsNullOrEmpty(mailDir) && Directory.Exists(mailDir))
                {
                    Process.Start(mailDir);
                }
                else
                {
                    MessageBox.Show("邮件目录不存在，请先设置有效的目录路径。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                WriteLog($"打开邮件目录时出错: {ex.Message}");
            }
        }
    }
}