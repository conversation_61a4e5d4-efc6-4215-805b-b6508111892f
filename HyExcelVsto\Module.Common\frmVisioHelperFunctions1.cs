/*
 * ============================================================================
 * 功能模块：Visio辅助功能模块 - 第一部分
 * ============================================================================
 * 
 * 模块作用：提供Visio文档处理的核心功能，包括图衔读取、PDF转换和通用工具函数
 * 
 * 主要功能：
 * - 图衔读取：从Visio文档中提取图纸标题栏信息并导出到Excel
 * - PDF转换：将Visio文档批量转换为PDF格式
 * - 通用函数：提供文本格式设置和文本处理工具
 * 
 * 执行逻辑：
 * 1. 图衔读取：遍历文件列表 → 打开Visio文档 → 定位图衔区域 → 提取文本信息 → 写入Excel
 * 2. PDF转换：批量处理Visio文件 → 转换为PDF格式 → 保存到指定目录
 * 3. 通用函数：提供格式化和文本处理支持
 * 
 * 注意事项：
 * - 需要安装Visio应用程序才能正常工作
 * - 图衔位置配置需要与实际图纸模板匹配
 * - 大批量处理时注意内存管理和文件句柄释放
 * ============================================================================
 */

using ET;
using Microsoft.Office.Interop.Excel;
using Microsoft.Office.Interop.Visio;
using System;
using System.Collections.Generic;
using System.Linq;
using Page = Microsoft.Office.Interop.Visio.Page;
using TextBox = System.Windows.Forms.TextBox;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// Visio辅助功能静态类 - 第一部分
    /// 提供Visio文档处理的核心功能，包括图衔读取、PDF转换等
    /// </summary>
    public static partial class VisioHelperFunctions
    {
        #region 读取图衔
        /// <summary>
        /// 执行仅读取图衔操作，从Visio文档中提取图纸标题栏信息并导出到Excel
        /// </summary>
        /// <param name="filePathRange">包含Visio文件路径的Excel单元格范围</param>
        /// <param name="selectedTuxianName">选定的图衔模板名称（上海院/南方院）</param>
        /// <param name="textBoxProgress">用于显示处理进度的文本框控件</param>
        /// <remarks>
        /// 执行逻辑：
        /// 1. 验证输入参数和图衔模板配置
        /// 2. 创建新的Excel工作表用于存储结果
        /// 3. 遍历文件路径范围，逐个处理Visio文档
        /// 4. 从每个页面的图衔区域提取文本信息
        /// 5. 将提取的信息写入Excel表格
        /// 
        /// 支持的图衔模板：上海院、南方院
        /// 提取的信息包括：图纸名称、单位比例、出图日期、图纸编号、页面比例尺
        /// </remarks>
        public static void 执行_仅读取图衔(Range filePathRange, string selectedTuxianName, TextBox textBoxProgress)
        {
            // 验证输入参数
            if (filePathRange == null)
            {
                textBoxProgress.WriteLog("请选择页面名称所在的列");
                return;
            }

            // 优化选择范围，去除空行和隐藏行
            filePathRange = filePathRange.OptimizeRangeSize();

            // 创建新的工作表用于存储导出结果
            Worksheet newWorksheet = Globals.ThisAddIn.Application.ActiveWorkbook.Worksheets.Add();
            newWorksheet.Activate();

            // 设置工作表名称，包含时间戳避免重名
            newWorksheet.Name = $"导出页面信息{DateTime.Now:yyyyMMddHHmm}";

            // 设置表头列名
            int newCol = 1;
            newWorksheet.Cells[1, newCol++].Value = "全路径";
            newWorksheet.Cells[1, newCol++].Value = "页面名称";
            newWorksheet.Cells[1, newCol++].Value = "文件名";
            newWorksheet.Cells[1, newCol++].Value = "页面名称";
            newWorksheet.Cells[1, newCol++].Value = "图纸名称";
            newWorksheet.Cells[1, newCol++].Value = "单位比例";
            newWorksheet.Cells[1, newCol++].Value = "出图日期";
            newWorksheet.Cells[1, newCol++].Value = "图纸编号";
            newWorksheet.Cells[1, newCol++].Value = "页面比例尺";

            // 设置各列的宽度以便更好地显示内容
            newCol = 1;
            newWorksheet.Columns[newCol++].ColumnWidth = 20; // 全路径
            newWorksheet.Columns[newCol++].ColumnWidth = 20; // 页面名称
            newWorksheet.Columns[newCol++].ColumnWidth = 40; // 文件名
            newWorksheet.Columns[newCol++].ColumnWidth = 20; // 页面名称
            newWorksheet.Columns[newCol++].ColumnWidth = 40; // 图纸名称
            newWorksheet.Columns[newCol++].ColumnWidth = 15; // 单位比例
            newWorksheet.Columns[newCol++].ColumnWidth = 15; // 出图日期
            newWorksheet.Columns[newCol++].ColumnWidth = 35; // 图纸编号
            newWorksheet.Columns[newCol++].ColumnWidth = 15; // 页面比例尺

            // 更新屏幕显示
            ETExcelExtensions.ScreenUpdate();

            // 数据行从第2行开始
            int newRow = 2;

            // 创建Visio应用程序实例
            Microsoft.Office.Interop.Visio.Application visioApp = ETVisio.CreateApplication(true);
            Document docPrev = null; // 用于跟踪上一个打开的文档，便于关闭

            // 获取处理进度相关变量
            int rowCount = filePathRange.GetVisibleRowCount();
            int currentRow = 1;

            // 根据选定的图衔名称获取对应的位置配置
            Dictionary<string, RectanglPosition> selectedPositions;

            selectedPositions = selectedTuxianName switch
            {
                "上海院" => VisShapePosition.SH相对位置,
                "南方院" => VisShapePosition.NF相对位置,
                _ => null
            };

            // 验证图衔配置是否有效
            if (selectedPositions == null)
            {
                textBoxProgress.WriteLog("请选择正确的图衔名称：上海院或南方院");
                return;
            }

            // 遍历文件路径范围中的每个单元格
            foreach (Range filePathCell in filePathRange.Cells)
            {
                // 跳过隐藏行
                if (filePathCell.EntireRow.Hidden)
                    continue;
                // 跳过空单元格
                if (filePathCell.IsCellEmpty())
                    continue;

                // 获取文件路径和文件名
                string filePath = filePathCell.Value.ToString();
                string fileName = System.IO.Path.GetFileName(filePath);

                Document doc;

                // 检查文件是否已经在Visio中打开
                doc = ETVisio.FindOpenDocumentByPath(visioApp, filePath);
                if (doc == null)
                {
                    // 文件未打开，尝试打开文件
                    doc = ETVisio.Open(visioApp, filePath, textBoxProgress, false);
                    // 关闭上一个文档以节省内存
                    docPrev?.Close(false);
                    docPrev = doc;
                }
                else
                {
                    // 文件已经打开，更新文档引用
                    docPrev = doc;
                }

                // 检查文档是否成功打开
                if (doc == null)
                {
                    textBoxProgress.WriteLog($"文件{System.IO.Path.GetFileName(filePath)}不存在(或者打不开)");
                    // 记录无法读取的文件信息
                    newCol = 1;
                    newWorksheet.Cells[newRow, newCol++].Value = filePath;
                    newWorksheet.Cells[newRow, newCol++].Value = fileName;
                    newWorksheet.Cells[newRow, newCol++].Value = "文件无法读取";
                    newRow++;
                    continue;
                }

                // 显示当前处理进度
                textBoxProgress.WriteLog($"{currentRow++}/{rowCount}.正在处理 {fileName}");

                // 获取文档中的页面数量
                int pageCount = doc.Pages.Count;

                // 遍历文档中的每个页面
                for (int i = 1; i <= pageCount; i++)
                {
                    newCol = 1;
                    Page page = (Page)doc.Pages[i];

                    // 仅处理前景页面，跳过背景页面
                    if (ETVisio.IsBackgroundPage(page))
                        continue;

                    // 填写基本页面信息
                    newWorksheet.Cells[newRow, newCol++].Value = doc.FullName;    // 文档完整路径
                    newWorksheet.Cells[newRow, newCol++].Value = page.Name;       // 页面名称
                    newWorksheet.Cells[newRow, newCol++].Value = doc.Name;        // 文档名称
                    newWorksheet.Cells[newRow, newCol++].Value = page.Name;       // 页面名称（重复）

                    // 根据图衔模板配置获取各个信息区域的位置
                    RectangleRange rect图名位置 = ETVisio.Get图衔子图形在页面中的位置_BL(
                        page,
                        selectedPositions["图名"],
                        0.01,    // X偏移量
                        0.03,    // Y偏移量
                        0.006);  // 容差值
                    RectangleRange rect单位比例位置 = ETVisio.Get图衔子图形在页面中的位置_BL(
                        page,
                        selectedPositions["单位比例"],
                        0.01,    // X偏移量
                        0.03,    // Y偏移量
                        0.004,   // 宽度容差
                        0.006);  // 高度容差
                    RectangleRange rect日期位置 = ETVisio.Get图衔子图形在页面中的位置_BL(page, selectedPositions["日期"]);
                    RectangleRange rect图号位置 = ETVisio.Get图衔子图形在页面中的位置_BL(page, selectedPositions["图号"]);

                    // 创建区域字典，只包含成功定位的区域
                    Dictionary<string, RectangleRange> regions_BL_Dic = [];

                    if (rect图名位置 != null)
                        regions_BL_Dic.Add("图纸名称", rect图名位置);
                    if (rect单位比例位置 != null)
                        regions_BL_Dic.Add("单位比例", rect单位比例位置);
                    if (rect日期位置 != null)
                        regions_BL_Dic.Add("出图日期", rect日期位置);
                    if (rect图号位置 != null)
                        regions_BL_Dic.Add("图纸编号", rect图号位置);

                    // 从指定区域读取图形中的文本信息
                    Dictionary<string, List<TuxianSubShapeInfo>> txShapes_Dic = ETVisio.ReadTextFromShapes(
                        page,
                        regions_BL_Dic);

                    // 将读取到的文本信息写入Excel对应列
                    ETVisio.ShapeTextToExcel(txShapes_Dic, "图纸名称", newWorksheet.Cells[newRow, newCol++]);
                    ETVisio.ShapeTextToExcel(txShapes_Dic, "单位比例", newWorksheet.Cells[newRow, newCol++]);
                    ETVisio.ShapeTextToExcel(txShapes_Dic, "出图日期", newWorksheet.Cells[newRow, newCol++]);
                    ETVisio.ShapeTextToExcel(txShapes_Dic, "图纸编号", newWorksheet.Cells[newRow, newCol++]);
                    // 获取页面比例尺信息
                    newWorksheet.Cells[newRow, newCol++].Value = ETVisio.GetPaperDrawingScale(page);

                    newRow++;
                }

                // 标记当前文件处理完成
                textBoxProgress.WriteLog($"，读取完成", false);
                ETExcelExtensions.Format条件格式警示色(filePathCell, EnumWarningColor.蓝绿);
            }

            // 清理资源：关闭最后一个文档和Visio应用程序
            docPrev?.Close(false);
            visioApp.Quit();
            textBoxProgress.WriteLog("执行完成");
        }
        #endregion 读取图衔

        #region 转换为PDF
        /// <summary>
        /// 在新线程中执行Visio到PDF的批量转换操作
        /// </summary>
        /// <param name="targetDirectory">PDF文件输出目录</param>
        /// <param name="sourceRootDirectory">源文件根目录</param>
        /// <param name="filePathRange">包含Visio文件路径的Excel单元格范围</param>
        /// <param name="textBoxProgress">进度显示文本框</param>
        /// <param name="textBoxError">错误信息显示文本框</param>
        /// <remarks>
        /// 使用独立线程执行转换操作，避免阻塞UI界面
        /// </remarks>
        public static void VisioToPdfPerRowsThread(
            string targetDirectory,
            string sourceRootDirectory,
            Range filePathRange,
            TextBox textBoxProgress,
            TextBox textBoxError)
        {
            // 创建新线程执行PDF转换操作
            new System.Threading.Thread(
                () => VisioToPDFPerRows(
                    targetDirectory,
                    sourceRootDirectory,
                    filePathRange,
                    textBoxProgress,
                    textBoxError)).Start();
        }

        /// <summary>
        /// 批量将Visio文档转换为PDF格式
        /// </summary>
        /// <param name="targetDirectory">PDF文件输出目录</param>
        /// <param name="sourceRootDirectory">源文件根目录</param>
        /// <param name="filePathRange">包含Visio文件路径的Excel单元格范围</param>
        /// <param name="textBoxProgress">进度显示文本框</param>
        /// <param name="textBoxError">错误信息显示文本框</param>
        /// <returns>操作结果消息</returns>
        /// <remarks>
        /// 注意：当前实现已被注释，需要根据实际需求重新实现
        /// 原实现要求Visio2013及以上版本
        /// </remarks>
        public static string VisioToPDFPerRows(
            string targetDirectory,
            string sourceRootDirectory,
            Range filePathRange,
            TextBox textBoxProgress,
            TextBox textBoxError)
        {
            /* 
             * 注释说明：此方法的实现已被注释，原实现逻辑如下：
             * 1. 验证输出目录是否存在
             * 2. 创建Visio2013应用程序实例并验证版本
             * 3. 优化文件路径范围，获取可见单元格
             * 4. 遍历每个Visio文件路径
             * 5. 过滤.vsd格式文件
             * 6. 解析源文件根目录
             * 7. 调用ETVisio.VisioToPdfToAnotherDirectory执行转换
             * 8. 根据转换结果更新单元格格式和日志
             * 9. 清理Visio应用程序资源
             * 
             * 如需启用此功能，请取消注释并根据实际需求调整实现
             */

            //if (string.IsNullOrEmpty(targetDirectory) || !Directory.Exists(targetDirectory))
            //{
            //    if (textBoxError != null)
            //        textBoxError.WriteLog("请选择要输出的新Visio文件目录");
            //    return "请选择要输出的新Visio文件目录";
            //}

            //Microsoft.Office.Interop.Visio.Application visioApp = ETVisio.CreateVisioApplication2013();
            //if (!ETVisio.IsVisio2013OrLater(visioApp))
            //{
            //    visioApp.Quit();
            //    if (textBoxError != null)
            //        textBoxError.WriteLog("本功功能只支持Visio2013及以上版本");
            //    return "本功功能只支持Visio2013及以上版本";
            //}

            //filePathRange = filePathRange.HyOptimizeRangeSize().HyGetVisibleRange();

            //foreach (Range filePathCell in filePathRange.Cells)
            //{
            //    if (filePathCell.IsCellEmpty())
            //        continue;
            //    string visioPath = filePathCell.Value.ToString();

            //    if (!visioPath.EndsWith(".vsd"))
            //        continue;

            //    string resolvedSourceRootDirectory = string.IsNullOrEmpty(sourceRootDirectory)
            //        ? System.IO.Path.GetDirectoryName(visioPath)
            //        : sourceRootDirectory;

            //    bool success = ETVisio.VisioToPdfToAnotherDirectory(
            //        visioApp,
            //        visioPath,
            //        resolvedSourceRootDirectory,
            //        targetDirectory);
            //    if (!success)
            //    {
            //        filePathCell.Format条件格式警示色(EnumWarningColor.出错);
            //    }
            //    if (textBoxProgress != null)
            //        textBoxProgress.WriteLog(success ? $"转换成功:{visioPath}" : $"转换失败:{visioPath}");
            //    if (textBoxError != null && !success)
            //        textBoxError.WriteLog(success ? string.Empty : $"转换失败:{visioPath}");
            //    Debug.WriteLine(success ? $"转换成功:{visioPath}" : $"转换失败:{visioPath}");
            //}

            //visioApp.Quit();
            //if (textBoxProgress != null)
            //    textBoxProgress.WriteLog("转换完成");

            return string.Empty;
        }
        #endregion 转换为PDF

        #region 通用函数
        /// <summary>
        /// 设置Excel单元格的文本格式
        /// </summary>
        /// <param name="range">Excel单元格范围</param>
        /// <param name="rowIndex">行索引（从1开始）</param>
        /// <param name="colIndex">列索引（从1开始）</param>
        /// <param name="value">要设置的值</param>
        /// <remarks>
        /// 将指定单元格的数字格式设置为文本格式（"@"），
        /// 确保数值以文本形式显示，避免自动格式化
        /// </remarks>
        public static void Set文本格式(Range range, int rowIndex, int colIndex, string value)
        {
            // 验证输入参数
            if (string.IsNullOrEmpty(value) || range == null)
                return;
            // 验证索引范围
            if (range.Columns.Count < colIndex || range.Rows.Count < rowIndex)
                return;
            // 设置单元格为文本格式
            range.Cells[rowIndex, colIndex].NumberFormatLocal = "@";
        }

        /// <summary>
        /// 根据输入文本的行数智能返回最后几行内容
        /// </summary>
        /// <param name="inputText">输入的多行文本字符串</param>
        /// <returns>返回指定数量的文本行，行数根据输入文本总行数动态确定</returns>
        /// <remarks>
        /// 返回规则：
        /// - 总行数 > 8行：返回最后5行
        /// - 总行数 ≤ 8行：返回最后3行
        /// 
        /// 适用场景：日志显示、文本预览等需要显示尾部内容的场合
        /// </remarks>
        public static string GetLastLines(string inputText)
        {
            // 按换行符分割文本
            string[] lines = inputText.Split(new[] { '\n' }, StringSplitOptions.None);

            int lineCount = lines.Length;

            // 根据总行数确定返回行数
            int numberOfLinesToReturn = lineCount > 8 ? 5 : 3;

            // 获取最后指定行数的内容
            string[] resultLines = lines.Skip(Math.Max(0, lineCount - numberOfLinesToReturn)).ToArray();

            // 重新组合为字符串
            return string.Join(Environment.NewLine, resultLines);
        }
        #endregion 通用函数
    }
}