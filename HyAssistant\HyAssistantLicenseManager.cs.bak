using ET;
using ET.ETLicense;
using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HyAssistant
{
    /// <summary>
    /// HyAssistant授权管理器 - 整合了授权控制器初始化、权限检查、用户组管理等功能
    /// </summary>
    internal class HyAssistantLicenseManager
    {
        #region 常量定义

        /// <summary>
        /// 网络超时时间（秒）
        /// </summary>
        private const int NETWORK_TIMEOUT_SECONDS = 30;

        /// <summary>
        /// 授权加密密钥
        /// </summary>
        private const string LICENSE_ENCRYPTION_KEY = "HyAssistant-A3AB7C4B-D32G-4HGA";

        /// <summary>
        /// 授权文件URL
        /// </summary>
        private const string LICENSE_URL = @"https://rgh4r34.oss-cn-guangzhou.aliyuncs.com/HyAssistantLicense.dat";

        #endregion 常量定义

        #region 静态字段

        /// <summary>
        /// 授权控制器，用于验证应用权限
        /// </summary>
        public static ETLicenseController LicenseController;

        /// <summary>
        /// 权限管理器实例
        /// </summary>
        private static ETPermissionManager _permissionManager;

        /// <summary>
        /// UI权限管理器实例
        /// </summary>
        private static HaUIPermissionManager _uiPermissionManager;

        /// <summary>
        /// 防止回调重入的标志
        /// </summary>
        private static volatile bool _isProcessingCallback = false;

        /// <summary>
        /// 上次回调时间
        /// </summary>
        private static DateTime _lastCallbackTime = DateTime.MinValue;

        /// <summary>
        /// 回调计数器
        /// </summary>
        private static int _callbackCount = 0;

        /// <summary>
        /// 主窗体实例引用，用于UI权限管理
        /// </summary>
        private static MainForm _mainFormInstance;

        #endregion 静态字段

        #region 授权控制器初始化

        /// <summary>
        /// 初始化授权控制器
        /// </summary>
        public static void InitializeLicenseController()
        {
            Stopwatch licenseStopwatch = Stopwatch.StartNew();
            try
            {
                ETLogManager.Info("开始初始化HyAssistant授权控制器");

                // 定义授权相关参数
                string licenseFilePath = ETConfig.GetConfigDirectory("license.dat");
                string remoteCachePath = ETConfig.GetConfigDirectory("remote_license_cache.dat");

                // 创建优化后的组合授权控制器，支持网络授权更新回调
                LicenseController = ETLicenseManager.CreateCombinedLicenseController(
                    licenseFilePath,
                    null, // 不使用SMB网络共享
                    LICENSE_URL,
                    LICENSE_ENCRYPTION_KEY,
                    remoteCachePath, // 远程授权缓存路径
                    null, // 用户ID
                    null, // 用户组
                    OnNetworkLicenseUpdated, // 网络授权更新回调
                    NETWORK_TIMEOUT_SECONDS // 网络超时
                );

                licenseStopwatch.Stop();
                ETLogManager.Info($"HyAssistant授权系统初始化完成，耗时: {licenseStopwatch.ElapsedMilliseconds}ms");
            }
            catch (Exception ex)
            {
                licenseStopwatch.Stop();
                ETLogManager.Error($"HyAssistant授权系统初始化失败: {ex.Message}", ex);
                ETLogManager.Warning("HyAssistant授权系统初始化失败，程序将以受限模式运行");
            }
        }

        #endregion 授权控制器初始化

        #region 权限管理器初始化

        /// <summary>
        /// 初始化权限管理器
        /// </summary>
        /// <param name="mainForm">主窗体实例</param>
        public static void InitializePermissionManagers(MainForm mainForm)
        {
            try
            {
                _mainFormInstance = mainForm;

                if (_permissionManager == null && LicenseController != null)
                {
                    _permissionManager = new ETPermissionManager(
                        LicenseController,
                        HaPermissionKeys.RequiredPermissions,
                        6); // 6小时缓存

                    _uiPermissionManager = new HaUIPermissionManager(_permissionManager, mainForm);
                    ETLogManager.Info("HyAssistant权限管理器初始化成功");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"HyAssistant权限管理器初始化失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 初始化并检查所有权限
        /// </summary>
        public static void InitializeAuthorization()
        {
            try
            {
                ETLogManager.Info("开始初始化HyAssistant授权验证");

                // 初始化权限管理器
                if (_mainFormInstance != null)
                {
                    InitializePermissionManagers(_mainFormInstance);
                }

                if (_uiPermissionManager != null)
                {
                    // 使用新的权限管理器
                    ETLogManager.Debug("使用新的权限管理器进行HyAssistant初始化");
                    _uiPermissionManager.Initialize();
                }
                else
                {
                    // 如果权限管理器未初始化，记录错误
                    ETLogManager.Error("HyAssistant权限管理器未初始化，无法进行权限验证");
                    throw new InvalidOperationException("权限管理器未初始化，请确保LicenseController已正确初始化");
                }

                ETLogManager.Info("HyAssistant授权验证初始化完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"初始化HyAssistant授权验证时出错: {ex.Message}", ex);
                throw; // 重新抛出异常
            }
        }

        #endregion 权限管理器初始化

        #region 权限检查方法

        /// <summary>
        /// 获取特定权限的状态
        /// </summary>
        /// <param name="permissionName">权限标识符</param>
        /// <returns>是否有权限</returns>
        public static bool HasPermission(string permissionName)
        {
            try
            {
                // 优先使用新的权限管理器
                if (_uiPermissionManager != null)
                {
                    return _uiPermissionManager.HasPermission(permissionName);
                }

                // 如果权限管理器未初始化，直接使用授权控制器
                if (LicenseController != null)
                {
                    return LicenseController.HasPermissionAsync(permissionName).ConfigureAwait(false).GetAwaiter().GetResult();
                }

                // 如果授权系统未初始化，记录错误并返回false（安全策略：默认拒绝）
                ETLogManager.Error($"授权系统未初始化，权限检查被拒绝: {permissionName}");
                return false;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"检查权限时出错: {permissionName}", ex);
                return false; // 出错时默认拒绝权限（安全策略）
            }
        }

        /// <summary>
        /// 异步检查是否有指定权限
        /// </summary>
        /// <param name="permissionName">权限名称</param>
        /// <returns>如果有权限返回true，否则返回false</returns>
        public static async Task<bool> HasPermissionAsync(string permissionName)
        {
            try
            {
                if (LicenseController == null)
                {
                    // 如果未初始化授权系统，采用安全策略：默认拒绝
                    ETLogManager.Error($"授权系统未初始化，权限检查被拒绝: {permissionName}");
                    return false;
                }

                return await LicenseController.HasPermissionAsync(permissionName).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"异步检查权限失败: {permissionName} - {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 检查权限并获取过期时间
        /// </summary>
        /// <param name="permissionName">权限名称</param>
        /// <returns>权限检查结果(是否有权限, 过期时间)</returns>
        public static async Task<(bool HasPermission, DateTime? ExpireTime)> CheckPermissionWithExpireTimeAsync(string permissionName)
        {
            try
            {
                if (LicenseController == null)
                {
                    // 如果未初始化授权系统，假定有权限且无过期时间
                    return (true, null);
                }

                return await LicenseController.CheckPermissionWithExpireTimeAsync(permissionName).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"检查权限过期时间失败: {permissionName} - {ex.Message}", ex);
                return (false, null);
            }
        }

        #endregion 权限检查方法

        #region 权限刷新方法

        /// <summary>
        /// 强制刷新权限缓存并更新UI界面
        /// </summary>
        public static void ForceRefreshPermissionsAndUI()
        {
            try
            {
                ETLogManager.Info("开始强制刷新HyAssistant权限缓存和UI界面");

                // 优先使用新的权限管理器
                if (_uiPermissionManager != null)
                {
                    ETLogManager.Debug("使用新的权限管理器进行强制刷新");
                    _uiPermissionManager.ForceRefreshPermissionsAndUI();
                    return;
                }

                // 如果权限管理器未初始化，记录错误
                ETLogManager.Error("HyAssistant权限管理器未初始化，无法强制刷新权限");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"强制刷新HyAssistant权限缓存和UI时出错: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 强制刷新权限缓存并更新UI界面（异步版本）
        /// </summary>
        public static async Task ForceRefreshPermissionsAndUIAsync()
        {
            try
            {
                ETLogManager.Info("开始异步强制刷新HyAssistant权限缓存和UI界面");

                // 优先使用新的权限管理器
                if (_permissionManager != null)
                {
                    ETLogManager.Debug("使用新的权限管理器进行异步强制刷新");
                    await _permissionManager.ForceRefreshPermissionsAndUIAsync().ConfigureAwait(false);
                    return;
                }

                // 如果权限管理器未初始化，记录错误
                ETLogManager.Error("HyAssistant权限管理器未初始化，无法异步强制刷新权限");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"异步强制刷新HyAssistant权限缓存和UI时出错: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 强制刷新授权信息（包括网络文件）
        /// </summary>
        public static async Task ForceRefreshLicenseAsync()
        {
            try
            {
                if (LicenseController == null)
                {
                    ETLogManager.Warning("HyAssistant授权控制器未初始化，无法刷新");
                    return;
                }

                ETLogManager.Info("正在强制刷新HyAssistant授权信息...");

                // 使用带回调的强制刷新方法，确保触发 OnNetworkLicenseUpdated 回调
                await LicenseController.ForceRefreshAllWithCallbackAsync().ConfigureAwait(false);

                ETLogManager.Info("HyAssistant授权信息强制刷新完成，回调已触发");

                // 重新设置用户组信息（确保使用最新的配置）
                try
                {
                    RefreshCurrentUserGroups();
                    ETLogManager.Info("HyAssistant用户组信息已在强制刷新后重新设置");
                }
                catch (Exception ex)
                {
                    ETLogManager.Error("强制刷新后重新设置HyAssistant用户组信息时出错", ex);
                }

                // 立即强制刷新权限缓存和UI界面（使用改进的异步方法）
                try
                {
                    await ForceRefreshPermissionsAndUIAsync().ConfigureAwait(false);
                    ETLogManager.Info("HyAssistant权限缓存和UI界面已异步更新完成");
                }
                catch (Exception asyncEx)
                {
                    ETLogManager.Error("异步刷新HyAssistant权限缓存和UI失败", asyncEx);

                    // 如果异步失败，尝试同步方法
                    try
                    {
                        ForceRefreshPermissionsAndUI();
                        ETLogManager.Info("HyAssistant权限缓存和UI界面已同步更新完成");
                    }
                    catch (Exception syncEx)
                    {
                        ETLogManager.Error("同步刷新HyAssistant权限缓存和UI也失败", syncEx);
                    }
                }

                // 异步刷新授权状态
                await RefreshAuthorizationAsync().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                ETLogManager.Error("强制刷新HyAssistant授权信息失败", ex);
            }
        }

        #endregion 权限刷新方法

        #region 用户组管理

        /// <summary>
        /// 刷新当前用户组信息 从配置文件重新读取用户组并设置到 LicenseController
        /// </summary>
        public static void RefreshCurrentUserGroups()
        {
            try
            {
                if (LicenseController == null)
                {
                    ETLogManager.Warning("HyAssistant LicenseController 未初始化，无法刷新用户组");
                    return;
                }

                // 获取机器码和加密密钥
                string machineCode = LicenseController.GetCurrentMachineCode();
                string encryptionKey = ETTextCrypto.GetEncryptionKey(machineCode);

                // 从配置文件读取用户组信息
                string[] userGroups = LoadUserGroupsFromConfig(encryptionKey);

                // 设置到 LicenseController
                LicenseController.SetCurrentUser(null, userGroups);

                ETLogManager.Info($"已重新设置HyAssistant用户组: [{string.Join(", ", userGroups)}]");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"刷新HyAssistant用户组信息失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 从配置文件加载用户组信息
        /// </summary>
        /// <param name="encryptionKey">加密密钥</param>
        /// <returns>用户组数组</returns>
        public static string[] LoadUserGroupsFromConfig(string encryptionKey)
        {
            try
            {
                // 初始化INI文件
                string configPath = ETConfig.GetETConfigIniFilePath();
                ETIniFile iniFile = new ETIniFile(configPath);

                // 读取加密的用户组信息
                string encryptedUserGroups = iniFile.ReadString(ETConfig.COMMON_SECTION, ETConfig.USER_GROUPS_KEY, string.Empty);

                // 如果找到加密的用户组信息，解密并转换为数组
                if (!string.IsNullOrEmpty(encryptedUserGroups))
                {
                    string decryptedUserGroups = ETTextCrypto.DecryptText(encryptedUserGroups, encryptionKey);

                    // 检查是否是空标记
                    const string EMPTY_MARKER = "##EMPTY##";
                    if (decryptedUserGroups == EMPTY_MARKER)
                    {
                        // 用户明确设置为空，返回空数组
                        ETLogManager.Debug("HyAssistant用户组已被明确设置为空");
                        return Array.Empty<string>();
                    }
                    else if (!string.IsNullOrEmpty(decryptedUserGroups))
                    {
                        // 按逗号分隔转换为数组
                        return decryptedUserGroups.Split(new[] { ',', ';' }, StringSplitOptions.RemoveEmptyEntries)
                            .Select(g => g.Trim())
                            .Where(g => !string.IsNullOrEmpty(g))
                            .ToArray();
                    }
                }

                ETLogManager.Warning("HyAssistant配置文件中未找到用户组信息");
                return Array.Empty<string>();
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"从配置文件加载HyAssistant用户组信息失败: {ex.Message}", ex);
                return Array.Empty<string>();
            }
        }

        #endregion 用户组管理

        #region 网络授权更新回调

        /// <summary>
        /// 网络授权信息更新回调处理
        /// </summary>
        /// <param name="updatedLicenseInfo">更新后的授权信息</param>
        /// <param name="source">更新来源</param>
        public static void OnNetworkLicenseUpdated(ETLicenseInfo updatedLicenseInfo, string source)
        {
            // 简单的死循环检测
            DateTime now = DateTime.Now;
            if ((now - _lastCallbackTime).TotalSeconds < 1) // 1秒内
            {
                _callbackCount++;
                if (_callbackCount > 3) // 超过3次
                {
                    ETLogManager.Warning($"检测到HyAssistant网络授权回调可能死循环，跳过本次调用 (来源: {source}, 计数: {_callbackCount})");
                    return;
                }
            }
            else
            {
                _callbackCount = 1; // 重置计数
            }
            _lastCallbackTime = now;

            // 防止重入
            if (_isProcessingCallback)
            {
                ETLogManager.Warning($"HyAssistant网络授权回调正在处理中，跳过重复调用 (来源: {source})");
                return;
            }

            _isProcessingCallback = true;
            try
            {
                ETLogManager.Info($"HyAssistant网络授权信息已从 {source} 更新");

                if (updatedLicenseInfo != null)
                {
                    ETLogManager.Info($"授权版本: {updatedLicenseInfo.Version}");
                    ETLogManager.Info($"颁发者: {updatedLicenseInfo.Issuer}");
                    ETLogManager.Info($"用户数量: {updatedLicenseInfo.Users?.Count ?? 0}");
                    ETLogManager.Info($"分组权限数量: {updatedLicenseInfo.GroupPermissions?.Count ?? 0}");

                    // 关键修复：重新设置用户组信息
                    try
                    {
                        RefreshCurrentUserGroups();
                        ETLogManager.Info("HyAssistant用户组信息已重新设置");
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error("重新设置HyAssistant用户组信息时出错", ex);
                    }

                    // 立即强制刷新权限缓存和UI界面（简化版本，避免死循环）
                    try
                    {
                        // 只执行同步版本，避免重复调用和死循环
                        ForceRefreshPermissionsAndUI();
                        ETLogManager.Info("HyAssistant权限缓存和UI界面已立即更新");
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error("立即刷新HyAssistant权限缓存和UI时出错", ex);
                    }

                    // 异步执行完整的授权刷新
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await RefreshAuthorizationAsync().ConfigureAwait(false);
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Error("刷新HyAssistant授权状态时出错", ex);
                        }
                    });

                    // 通知主窗体更新最新授权信息（如果主窗体实例存在）
                    if (_mainFormInstance != null)
                    {
                        try
                        {
                            // 使用线程安全的方式更新主窗体的授权信息
                            if (_mainFormInstance.InvokeRequired)
                            {
                                _mainFormInstance.Invoke(new Action(() =>
                                {
                                    // 这里可以添加主窗体特定的授权更新逻辑
                                    ETLogManager.Debug("已通知主窗体授权信息更新");
                                }));
                            }
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Error("通知主窗体授权信息更新时出错", ex);
                        }
                    }
                }
                else
                {
                    ETLogManager.Warning("HyAssistant网络授权信息更新失败");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("处理HyAssistant网络授权更新回调时出错", ex);
            }
            finally
            {
                _isProcessingCallback = false;
            }
        }

        #endregion 网络授权更新回调

        #region 授权状态刷新

        /// <summary>
        /// 异步刷新授权状态
        /// </summary>
        public static async Task RefreshAuthorizationAsync()
        {
            try
            {
                ETLogManager.Info("开始刷新HyAssistant授权状态");

                // 重新初始化授权验证
                await Task.Run(() =>
                {
                    try
                    {
                        InitializeAuthorization();
                        ETLogManager.Info("HyAssistant授权状态刷新完成");
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error("重新初始化HyAssistant授权验证时出错", ex);
                    }
                });

                // 如果UI权限管理器已初始化，则刷新UI权限
                if (_uiPermissionManager != null && _mainFormInstance != null)
                {
                    try
                    {
                        // 在UI线程上执行权限刷新
                        if (_mainFormInstance.InvokeRequired)
                        {
                            _mainFormInstance.Invoke(new Action(async () =>
                            {
                                try
                                {
                                    await _uiPermissionManager.RefreshMenuPermissionsAsync().ConfigureAwait(true);
                                    ETLogManager.Info("HyAssistant菜单权限已根据新的授权信息重新刷新");
                                }
                                catch (Exception ex)
                                {
                                    ETLogManager.Error("刷新HyAssistant菜单权限时出错", ex);
                                }
                            }));
                        }
                        else
                        {
                            await _uiPermissionManager.RefreshMenuPermissionsAsync().ConfigureAwait(false);
                            ETLogManager.Info("HyAssistant菜单权限已根据新的授权信息重新刷新");
                        }
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error("刷新HyAssistant菜单权限时出错", ex);
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("刷新HyAssistant授权状态时出错", ex);
            }
        }

        #endregion 授权状态刷新

        #region 公共属性

        /// <summary>
        /// 获取权限管理器实例
        /// </summary>
        public static ETPermissionManager PermissionManager => _permissionManager;

        /// <summary>
        /// 获取UI权限管理器实例
        /// </summary>
        public static HaUIPermissionManager UIPermissionManager => _uiPermissionManager;

        #endregion 公共属性
    }
}