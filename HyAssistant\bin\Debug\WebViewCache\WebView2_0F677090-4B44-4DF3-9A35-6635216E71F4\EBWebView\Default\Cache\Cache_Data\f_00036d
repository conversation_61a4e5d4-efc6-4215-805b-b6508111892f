<?xml version="1.0" encoding="utf-8"?>
<!--支撑网-->
<metas>
    <!-- PHONEBOOTH 电话亭 -->
    <ObjMeta objectType="PHONEBOOTH" autoReload="true" needgeom="true"
             typeActions="add,reset"
             itemActions="locate,modify,remove"
             moreItemActions="assetAttribute,filesman">
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_SHARE_ID" label="共享信息"/>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="CODE" label="编码" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
                <field name="OWNER_NET_ID" label="所属网络"/>
            </row>
            <row>
                <field name="position" label="标准地址" required="true" baseParams="{&quot;_solr&quot;:true}"
                       dropdownOptions="titleField:FULL_NAME"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" readOnly="true" default="80204666"/>
                <field name="TELSTR" label="电话号码串"/>
            </row>
            <row>
                <field name="PHYSICAL_STATE_ID" label="设施状态"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_SHARE_ID" label="共享信息"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:3000;" autoLoad="false">
            <field name="CODE" label="电话亭编码"/>
            <field name="NAME" label="电话亭名称" width="250px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
        </grid>
    </ObjMeta>

    <!-- WELL 人手井 -->
    <ObjMeta objectType="WELL" autoReload="true" icon="assets/icon/map/WELL_1.png" needgeom="true"
      GEN_CODE_TEMPLATE="${site.CODE}/#${sn3}" GEN_NAME_TEMPLATE="${site.CODE}/#${sn3}"
             typeActions="add,reset,templates,remove"
             itemActions="wellview,locate,relocate,modify,remove"
             moreItemActions="assetAttribute,filesman">
        <action type="workspace" name="templates" label="{{g5.template.manage}}" title="人手井模板"
                objectType="WELL.TEMPLATE" icon="columns"/>
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="FACILITY_TYPE_ID" label="设施类型"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MATERIAL_ID" label="材质"/>
            </row>
            <row>
                <field name="OLT_CODE" label="所属OLT"/>
                <field name="DEVICE_BIND_PORT" label="所属PON口"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="地理位置"/>
                <field name="CREATOR" label="录入人"/>
            </row>
            <row>
                <field name="CREATE_DATE" label="入库时间"/>
            </row>
            <row>
                <field name="project" label="工程名称"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="IS_RECORD_IN_UNDER_ID" label="是否录入地下管线系统"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增面板-->
        <form>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="CODE" label="编码" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="ROAD_NAME" label="所在路名"/>
                <field name="NETHEIGHT" label="净空高"/>
            </row>
            <row>
                <field name="DISTANCE_TO_ROAD" label="路边距离" required="true"/>
                <field name="PROJECT_STATUS_ID" label="工程状态" default="80204666" required="true" readOnly="true"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
                <field name="WELLDEGREE" label="方位角"/>
            </row>
            <row>
                <field name="FIXING_FLAG_ID" label="固定装置标志"/>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="所属网络" default="80204105"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="SHAREBUILD_ID" label="共建共享"/>
                <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            </row>
            <row>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="IS_HEAVY_ID" label="是否强电入侵"/>
                <field name="IS_GREEN_POSITION" label="位置准确绿标"/>
            </row>
            <row>
                <field name="IS_RECORD_IN_UNDER_ID" label="是否录入地下管线系统"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="CHECK_DATE" label="清查时间"/>
            </row>
            <row>
                <field name="FACILITY_TYPE_ID" label="井类别" default="80203103" required="true"/>
                <field name="MODEL_ID" label="井类型" required="true"/>
            </row>
            <row>
                <field name="COVER_HEIGHT" label="井盖凸起高度(m)"/>
                <field name="COVER_SHAPE_ID" label="井盖形状"/>
            </row>
            <row>
                <field name="WELLHOLE_STRUCTURE" label="井孔结构"/>
                <field name="COVER_MATERIAL_ID" label="井盖材质" required="true"/>
            </row>
            <row>
                <field name="HOLE_SHAPE_ID" label="井形状" required="true"/>
                <field name="COVER_SIZE" label="井盖尺寸"/>
            </row>
            <row>
                <field name="LENGTH" label="井底长度(CM)"/>
                <field name="WIDTH" label="井底宽度(CM)"/>
            </row>
            <row>
                <field name="HEIGHT" label="井底深度(CM)"/>
                <field name="UP_COVER_HEIGHT" label="上覆盖厚度"/>
            </row>
            <row>
                <field name="COVER_NUM" label="井盖数"/>
                <field name="COVER_MODEL" label="井盖规格"/>
            </row>
            <row>
                <field name="COVER_PASSWORD" label="井盖密码"/>
                <field name="IS_LOCK_ID" label="是否密码锁"/>
            </row>
            <row>
                <field name="MATERIAL_ID" label="井材质"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO;PROJECT_STATUS_ID=!project.PROJECT_STATUS_ID?80204666:project.PROJECT_STATUS_ID"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="asset" label="资产目录" dropdownOptions="titleField:ASSET_CATALOGUE" />
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                  <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!-- 批量修改表格 -->
        <form name="batchmodify">
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME" readOnly="true"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" default="80204848" readOnly="true"/>
            </row>
            <row>
                <field name="CODE" label="编码" readOnly="true"/>
                <field name="NAME" label="名称" readOnly="true"/>
            </row>
            <row>
                <field name="ROAD_NAME" label="所在路名"/>
                <field name="NETHEIGHT" label="净空高"/>
            </row>
            <row>
                <field name="DISTANCE_TO_ROAD" label="路边距离" />
                <field name="PROJECT_STATUS_ID" label="工程状态" default="80204666" readOnly="true"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
                <field name="WELLDEGREE" label="方位角"/>
            </row>
            <row>
                <field name="FIXING_FLAG_ID" label="固定装置标志"/>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="所属网络" default="80204105"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="SHAREBUILD_ID" label="共建共享"/>
                <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            </row>
            <row>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="IS_HEAVY_ID" label="是否强电入侵"/>
                <field name="IS_GREEN_POSITION" label="位置准确绿标"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="CHECK_DATE" label="清查时间"/>
            </row>
            <row>
                <field name="FACILITY_TYPE_ID" label="井类别" default="80203103" />
                <field name="MODEL_ID" label="井类型"/>
            </row>
            <row>
                <field name="COVER_HEIGHT" label="井盖凸起高度(m)"/>
                <field name="COVER_SHAPE_ID" label="井盖形状"/>
            </row>
            <row>
                <field name="WELLHOLE_STRUCTURE" label="井孔结构"/>
                <field name="COVER_MATERIAL_ID" label="井盖材质" />
            </row>
            <row>
                <field name="HOLE_SHAPE_ID" label="井形状" />
                <field name="COVER_SIZE" label="井盖尺寸"/>
            </row>
            <row>
                <field name="LENGTH" label="井底长度(CM)"/>
                <field name="WIDTH" label="井底宽度(CM)"/>
            </row>
            <row>
                <field name="HEIGHT" label="井底深度(CM)"/>
                <field name="UP_COVER_HEIGHT" label="上覆盖厚度"/>
            </row>
            <row>
                <field name="COVER_NUM" label="井盖数"/>
                <field name="COVER_MODEL" label="井盖规格"/>
            </row>
            <row>
                <field name="COVER_PASSWORD" label="井盖密码"/>
                <field name="IS_LOCK_ID" label="是否密码锁"/>
            </row>
            <row>
                <field name="MATERIAL_ID" label="井材质"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO;PROJECT_STATUS_ID=project.PROJECT_STATUS_ID"/>
                <field name="project_name" label="工程名称" readOnly="true"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号" readOnly="true"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" />
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" />
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="asset" label="资产目录" dropdownOptions="titleField:ASSET_CATALOGUE" />
                <field name="PROPERTY_OWNER_ID" label="产权归属" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" default="80209209" multiple="true"/>
            </row>
            <row>
                <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编号"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="MODEL_ID" label="人手井类型"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:3000;" autoLoad="false">
            <field name="CODE" label="人井编码"/>
            <field name="NAME" label="人井名称" width="250px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="FACILITY_TYPE_ID" label="设施类型"/>
            <field name="model" label="型号"/>
            <field name="MATERIAL_ID" label="材质"/>
            <field name="COVER_MATERIAL_ID" label="井盖材质" />
            <field name="HOLE_SHAPE_ID" label="井形状" />
            <field name="COVER_SIZE" label="井盖尺寸"/>
            <field name="LENGTH" label="井底长度(CM)"/>
            <field name="WIDTH" label="井底宽度(CM)"/>
            <field name="HEIGHT" label="井底深度(CM)"/>
            <field name="COVER_NUM" label="井盖数"/>
            <field name="UP_COVER_HEIGHT" label="上覆盖厚度"/>
            <field name="COVER_MODEL" label="井盖规格"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="vendor" label="生产厂家"/>
            <field name="IS_RECORD_IN_UNDER_ID" label="是否录入地下管线系统"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
        </grid>
        <grid name="pipeSystemMgt" typeActions="add,templates,moveInWell,moveOutWell">
            <field name="NAME"/>
            <field name="CODE"/>
        </grid>
    </ObjMeta>
    <!-- 人手井模板 -->
    <ObjMeta objectType="WELL.TEMPLATE" autoReload="true"
             typeActions="add,remove" titleField="NAME" labelField="NAME"
             itemActions="modify,remove,filesman">
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="模板名称" required="true"/>
            </row>
        </form>
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="SHAREBUILD_ID" label="共建共享"/>
                <field name="FIXING_FLAG_ID" label="固定装置标志"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" default="80204848"/>
            </row>
            <row>
                <field name="ROAD_NAME" label="所在路名"/>
                <field name="OWNER_NET_ID" label="所属网络" required="true" default="80204105"/>
            </row>
            <row>
                <field name="NETHEIGHT" label="净空高"/>
                <field name="DISTANCE_TO_ROAD" label="路边距离" required="true"/>
            </row>
            <row>
                <field name="WELLDEGREE" label="方位角"/>
                <field name="NOTES" label="备注"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="FACILITY_TYPE_ID" label="井类别" required="true"/>
                <field name="MODEL_ID" label="井类型" required="true"/>
            </row>
            <row>
                <field name="WELLHOLE_STRUCTURE" label="井孔结构"/>
                <field name="COVER_MATERIAL_ID" label="井盖材质" required="true"/>
            </row>
            <row>
                <field name="HOLE_SHAPE_ID" label="井形状" required="true"/>
                <field name="COVER_SIZE" label="井盖尺寸"/>
            </row>
            <row>
                <field name="LENGTH" label="井底长度(CM)"/>
                <field name="WIDTH" label="井底宽度(CM)"/>
            </row>
            <row>
                <field name="HEIGHT" label="井底深度(CM)"/>
                <field name="UP_COVER_HEIGHT" label="上覆盖厚度"/>
            </row>
            <row>
                <field name="COVER_NUM" label="井盖数"/>
                <field name="COVER_MODEL" label="井盖规格" required="true"/>
            </row>
            <row>
                <field name="COVER_PASSWORD" label="井盖密码"/>
                <field name="COVER_HEIGHT" label="井盖凸起高度(m)"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护部门"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:1390;" autoLoad="false">
            <field name="NAME" label="模板名称" width="250px"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
    </ObjMeta>

    <!-- DRAWINGPOINT 引上点 -->
    <ObjMeta objectType="DRAWINGPOINT" autoReload="true" needgeom="true" 
      GEN_CODE_TEMPLATE="${site.CODE}/YS${sn4}" GEN_NAME_TEMPLATE="${site.CODE}/YS${sn4}"
             typeActions="add,reset,templates,remove"
             itemActions="wellview,locate,relocate,modify,remove"
             moreItemActions="assetAttribute,filesman">
        <action type="workspace" name="templates" label="{{g5.template.manage}}" title="引上点模板"
                objectType="DRAWINGPOINT.TEMPLATE" icon="columns"/>
        <action name="genCode" type="script">
            <![CDATA[
            var pre = site.CODE + '/YS';
            _bean.autoCode(this, pre, 4, 'DRAWINGPOINT', {site: site.id});
        ]]>
        </action>
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="FACILITY_TYPE_ID" label="设施类型"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="地理位置"/>
                <field name="MATERIAL_ID" label="材质"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
          <field name="PROJECT_STATUS_ID" label="工程状态" />
          <row>
                <field name="project" label="工程名称"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增面板-->
        <form name="add">
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="PROJECT_STATUS_ID" label="工程状态" default="80204666" readOnly="true"/>
            </row>
            <row>
                <field name="CODE" label="编码" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="所属网络ID" default="80204105"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
                <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            </row>
            <row>
                <field name="DRAWINGPOINT_LENGTH" label="引上点长度"/>
                <field name="HOLE_NUM" label="管数目"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO;PROJECT_STATUS_ID=project.PROJECT_STATUS_ID"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                <field name="ASSET_CODE" label="资产卡片编号" />
                <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!--修改面板-->
        <form name="modify">
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="PROJECT_STATUS_ID" label="工程状态" default="80204666" readOnly="true"/>
            </row>
            <row>
                <field name="CODE" label="编码" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="所属网络ID" default="80204105"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
                <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            </row>
            <row>
                <field name="DRAWINGPOINT_LENGTH" label="引上点长度"/>
                <field name="HOLE_NUM" label="管数目"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO;PROJECT_STATUS_ID=project.PROJECT_STATUS_ID"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                <field name="ASSET_CODE" label="资产卡片编号" />
                <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!-- 批量修改表格 -->
        <form name="batchmodify">
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME" readOnly="true"/>
                <field name="PROJECT_STATUS_ID" label="工程状态" readOnly="true"/>
            </row>
            <row>
                <field name="CODE" label="编码" readOnly="true"/>
                <field name="NAME" label="名称" readOnly="true"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" readOnly="true"/>
                <field name="LONG_LOCAL_ID" label="长本属性" />
            </row>
            <row>
                <field name="OWNER_NET_ID" label="所属网络ID" />
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
                <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            </row>
            <row>
                <field name="DRAWINGPOINT_LENGTH" label="引上点长度"/>
                <field name="HOLE_NUM" label="管数目"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO;PROJECT_STATUS_ID=project.PROJECT_STATUS_ID"/>
                <field name="project_name" label="工程名称" readOnly="true"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号" readOnly="true"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" multiple="true"/>
            </row>
            <row>
                <field name="ASSET_CODE" label="资产卡片编号" />
                <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:3000;" autoLoad="false">
            <field name="CODE" label="引上点编码"/>
            <field name="NAME" label="引上点名称" width="250px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="FACILITY_TYPE_ID" label="设施类型"/>
            <field name="MATERIAL_ID" label="材质"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
        </grid>
    </ObjMeta>
    <!-- DRAWINGPOINT模板 -->
    <ObjMeta objectType="DRAWINGPOINT.TEMPLATE" autoReload="true"
             typeActions="add,remove" titleField="NAME" labelField="NAME"
             itemActions="modify,remove,filesman">
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="模板名称" required="true"/>
            </row>
        </form>
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
                <field name="OWNER_NET_ID" label="所属网络"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="NOTES" label="备注"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护部门"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:1390;" autoLoad="false">
            <field name="NAME" label="模板名称" width="250px"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
    </ObjMeta>

    <!-- POLE 电杆 -->
    <ObjMeta objectType="POLE" autoReload="true" needgeom="true"
      GEN_CODE_TEMPLATE="${site.CODE}/P${sn4}" GEN_NAME_TEMPLATE="${site.CODE}/P${sn4}"
             typeActions="add,reset,templates,remove"
             itemActions="locate,relocate,modify,remove"
             moreItemActions="poleview,assetAttribute,filesman">
        <action type="workspace" name="templates" label="{{g5.template.manage}}" title="{{g5.template.manage}}"
                objectType="POLE.TEMPLATE" icon="columns"/>
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="FACILITY_TYPE_ID" label="设施类型"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="地理位置"/>
                <field name="MATERIAL_ID" label="材质"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
          <field name="PROJECT_STATUS_ID" label="工程状态" />
          <row>
                <field name="project" label="工程名称"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增面板-->
        <form name="add">
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="CODE" label="编码" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" default="80204666" readOnly="true"/>
                <field name="ROAD_NAME" label="所在路名"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="OWNER_NET_ID" label="所属网络" default="80204105"/>
            </row>
            <row>
                <field name="model" label="电杆规格"/>
                <field name="HEIGHT" label="高度"/>
            </row>
            <row>
                <field name="SUPPORTING_WIRE_FIX_STYLE_ID" label="安装方式"/>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
            </row>
            <row>
                <field name="MATERIAL_ID" label="电杆材料"/>
                <field name="IS_ARRESTER" label="是否有避雷装置"/>
            </row>
            <row>
                <field name="polenet" label="所属杆路"/>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
                <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            </row>
            <row>
                <field name="BUILDING" label="建筑物名称"/>
                <field name="PROUTENAME" label="杆路描述"/>
            </row>
            <row>
                <field name="IS_GREEN_POSITION" label="位置准确绿标"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="STAY_WIRE_NUM" label="拉线条数" required="true"/>
                <field name="POLE_USE_ID" label="电杆类型" required="true"/>
            </row>
            <row>
                <field name="SLINGSEG_FIXTYPE_ID" label="吊线固定装置程式"/>
                <field name="SUPPORTING_WIRE_HEIGHT" label="吊线架空高度"/>
            </row>
            <row>
                <field name="HOOPING_NUM" label="抱箍数量"/>
                <field name="JACKSTAY_NUM" label="撑杆数量"/>
            </row>
            <row>
                <field name="POLE_SHAPE_ID" label="电杆形状"/>
                <field name="SUPPORTING_WIRE_COUNT" label="吊线条数"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO;PROJECT_STATUS_ID=project.PROJECT_STATUS_ID"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="asset" label="资产目录" dropdownOptions="titleField:ASSET_CATALOGUE" />
               
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                 <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!--修改面板-->
        <form name="modify">
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="CODE" label="编码" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" default="80204666" readOnly="true"/>
                <field name="ROAD_NAME" label="所在路名"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="OWNER_NET_ID" label="所属网络" default="80204105"/>
            </row>
            <row>
                <field name="model" label="电杆规格"/>
                <field name="HEIGHT" label="高度"/>
            </row>
            <row>
                <field name="SUPPORTING_WIRE_FIX_STYLE_ID" label="安装方式"/>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
            </row>
            <row>
                <field name="MATERIAL_ID" label="电杆材料"/>
                <field name="IS_ARRESTER" label="是否有避雷装置"/>
            </row>
            <row>
                <field name="polenet" label="所属杆路"/>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
                <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            </row>
            <row>
                <field name="BUILDING" label="建筑物名称"/>
                <field name="PROUTENAME" label="杆路描述"/>
            </row>
            <row>
                <field name="IS_GREEN_POSITION" label="位置准确绿标"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="STAY_WIRE_NUM" label="拉线条数" required="true"/>
                <field name="POLE_USE_ID" label="电杆类型" required="true"/>
            </row>
            <row>
                <field name="SLINGSEG_FIXTYPE_ID" label="吊线固定装置程式"/>
                <field name="SUPPORTING_WIRE_HEIGHT" label="吊线架空高度"/>
            </row>
            <row>
                <field name="HOOPING_NUM" label="抱箍数量"/>
                <field name="JACKSTAY_NUM" label="撑杆数量"/>
            </row>
            <row>
                <field name="POLE_SHAPE_ID" label="电杆形状"/>
                <field name="SUPPORTING_WIRE_COUNT" label="吊线条数"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO;PROJECT_STATUS_ID=project.PROJECT_STATUS_ID"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="asset" label="资产目录" dropdownOptions="titleField:ASSET_CATALOGUE" />

                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!-- 批量修改表格 -->
        <form name="batchmodify">
            <row>
                <field name="site" label="所属局站"  dropdownOptions="titleField:NAME" readOnly="true"/>
                <field name="LIFE_STATE_ID" label="生命周期状态"  default="80204848" readOnly="true"/>
            </row>
            <row>
                <field name="CODE" label="编码"  readOnly="true"/>
                <field name="NAME" label="名称"  readOnly="true"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" default="80204666" readOnly="true"/>
                <field name="ROAD_NAME" label="所在路名"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="OWNER_NET_ID" label="所属网络" default="80204105"/>
            </row>
            <row>
                <field name="model" label="电杆规格"/>
                <field name="HEIGHT" label="高度"/>
            </row>
            <row>
                <field name="SUPPORTING_WIRE_FIX_STYLE_ID" label="安装方式"/>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
            </row>
            <row>
                <field name="MATERIAL_ID" label="电杆材料"/>
                <field name="IS_ARRESTER" label="是否有避雷装置"/>
            </row>
            <row>
                <field name="polenet" label="所属杆路"/>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
                <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            </row>
            <row>
                <field name="BUILDING" label="建筑物名称"/>
                <field name="PROUTENAME" label="杆路描述"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="STAY_WIRE_NUM" label="拉线条数" />
                <field name="POLE_USE_ID" label="电杆类型" />
            </row>
            <row>
                <field name="SLINGSEG_FIXTYPE_ID" label="吊线固定装置程式"/>
                <field name="SUPPORTING_WIRE_HEIGHT" label="吊线架空高度"/>
            </row>
            <row>
                <field name="HOOPING_NUM" label="抱箍数量"/>
                <field name="JACKSTAY_NUM" label="撑杆数量"/>
            </row>
            <row>
                <field name="POLE_SHAPE_ID" label="电杆形状"/>
                <field name="SUPPORTING_WIRE_COUNT" label="吊线条数"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称" readOnly="true"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号" readOnly="true"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" />
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" />
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="asset" label="资产目录" dropdownOptions="titleField:ASSET_CATALOGUE" />

                <field name="PROPERTY_OWNER_ID" label="产权归属"  default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质"  default="80209209" multiple="true"/>
            </row>
            <row>
                <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="MATERIAL_ID" label="电杆材质"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:3000;" autoLoad="false">
            <field name="CODE" label="电杆编码"/>
            <field name="NAME" label="电杆名称" width="250px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="FACILITY_TYPE_ID" label="设施类型"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="id" label="实物ID"/>
        </grid>
    </ObjMeta>
    <!-- POLE模板 -->
    <ObjMeta objectType="POLE.TEMPLATE" autoReload="true"
             typeActions="add,remove" titleField="NAME" labelField="NAME"
             itemActions="modify,remove,filesman">
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="模板名称" required="true"/>
            </row>
        </form>
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="MATERIAL_ID" label="电杆材料"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="OWNER_NET_ID" label="所属网络"/>
            </row>
            <row>
                <field name="model" label="电杆规格"/>
                <field name="HEIGHT" label="高度"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="POLE_SHAPE_ID" label="电杆形状"/>
                <field name="POLE_USE_ID" label="电杆类型" required="true"/>
            </row>
            <row>
                <field name="SLINGSEG_FIXTYPE_ID" label="吊线固定装置程式"/>
                <field name="SUPPORTING_WIRE_HEIGHT" label="吊线架空高度"/>
            </row>
            <row>
                <field name="HOOPING_NUM" label="抱箍数量"/>
                <field name="JACKSTAY_NUM" label="撑杆数量"/>
            </row>
            <row>
                <field name="STAY_WIRE_NUM" label="拉线条数" required="true"/>
                <field name="SUPPORTING_WIRE_COUNT" label="吊线条数"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护部门"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:1390;" autoLoad="false">
            <field name="NAME" label="模板名称" width="250px"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
    </ObjMeta>

    <!-- SUPPORTPOINT 撑点 -->
    <ObjMeta objectType="SUPPORTPOINT" autoReload="true" needgeom="true"
      GEN_CODE_TEMPLATE="${site.CODE}/CD${sn4}" GEN_NAME_TEMPLATE="${site.CODE}/CD${sn4}"
             typeActions="add,reset,templates,remove"
             itemActions="locate,relocate,modify,remove"
             moreItemActions="poleview,assetAttribute,filesman">
        <action type="workspace" name="templates" label="{{g5.template.manage}}" title="{{g5.template.manage}}"
                objectType="SUPPORTPOINT.TEMPLATE" icon="columns"/>
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="FACILITY_TYPE_ID" label="设施类型"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="地理位置"/>
                <field name="MATERIAL_ID" label="材质"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
          <field name="PROJECT_STATUS_ID" label="工程状态" />
          <row>
                <field name="project" label="工程名称"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增面板-->
        <form name="add">
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="CODE" label="编码" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
                <field name="PROJECT_STATUS_ID" label="工程状态" default="80204666" readOnly="true"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="FACILITY_TYPE_ID" label="撑点类型"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="所属网络" default="80204105"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
                <field name="FLAG_TYPE_ID" label="撑点标识类别"/>
            </row>
            <row>
                <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            </row>
            <row>
                <field name="LONGITUDE" readOnly="true"/>
                <field name="LATITUDE" readOnly="true"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO;PROJECT_STATUS_ID=project.PROJECT_STATUS_ID"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>             
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                <field name="ASSET_CODE" label="资产卡片编号" />
                <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!--修改面板-->
        <form name="modify">
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="CODE" label="编码" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
                <field name="PROJECT_STATUS_ID" label="工程状态" default="80204666" readOnly="true"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="FACILITY_TYPE_ID" label="撑点类型"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="所属网络" default="80204105"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
                <field name="FLAG_TYPE_ID" label="撑点标识类别"/>
            </row>
            <row>
                <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            </row>
            <row>
                <field name="LONGITUDE" readOnly="true"/>
                <field name="LATITUDE" readOnly="true"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO;PROJECT_STATUS_ID=project.PROJECT_STATUS_ID"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                <field name="ASSET_CODE" label="资产卡片编号" />
                <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!-- 批量修改表格 -->
        <form name="batchmodify">
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME" readOnly="true"/>
            </row>
            <row>
                <field name="CODE" label="编码" readOnly="true"/>
                <field name="NAME" label="名称" readOnly="true"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" readOnly="true"/>
                <field name="PROJECT_STATUS_ID" label="工程状态" readOnly="true"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" />
                <field name="FACILITY_TYPE_ID" label="撑点类型"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="所属网络" />
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
                <field name="FLAG_TYPE_ID" label="撑点标识类别"/>
            </row>
            <row>
                <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            </row>
            <row>
                <field name="LONGITUDE" readOnly="true"/>
                <field name="LATITUDE" readOnly="true"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称" readOnly="true"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号" readOnly="true"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" />
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" />
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" multiple="true"/>
            </row>
            <row>
                <field name="ASSET_CODE" label="资产卡片编号" />
                <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:3000;" autoLoad="false">
            <field name="CODE" label="撑点编码"/>
            <field name="NAME" label="撑点名称" width="250px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="FACILITY_TYPE_ID" label="设施类型"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
        </grid>
    </ObjMeta>
    <!-- SUPPORTPOINT模板 -->
    <ObjMeta objectType="SUPPORTPOINT.TEMPLATE" autoReload="true"
             typeActions="add,remove" titleField="NAME" labelField="NAME"
             itemActions="modify,remove,filesman">
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="模板名称" required="true"/>
            </row>
        </form>
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
                <field name="OWNER_NET_ID" label="所属网络"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="FACILITY_TYPE_ID" label="撑点类型"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护部门"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:1390;" autoLoad="false">
            <field name="NAME" label="模板名称" width="250px"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
    </ObjMeta>

    <!-- UNDERWELL 地下进线室 -->
    <ObjMeta objectType="UNDERWELL" autoReload="true"
             typeActions="add,reset,remove"
             itemActions="locate,relocate,modify,remove"
             moreItemActions="wellview,assetAttribute,filesman">
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="FACILITY_TYPE_ID" label="设施类型"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="地理位置"/>
                <field name="MATERIAL_ID" label="材质"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
          <field name="PROJECT_STATUS_ID" label="工程状态" />
          <row>
                <field name="project" label="工程名称"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="CODE" label="编码" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="OWNER_NET_ID" label="所属网络" default="80204105"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
                <field name="UTILIZATION_AREA" label="实用面积"/>
            </row>
            <row>
                <field name="LENGTH" label="室内长度"/>
                <field name="WIDTH" label="室内宽度"/>
            </row>
            <row>
                <field name="HEIGHT" label="室内高度"/>
                <field name="DRAINAGE_MODEL" label="排水控制装置型号"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" default="80204666" readOnly="true"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
                <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO;PROJECT_STATUS_ID=project.PROJECT_STATUS_ID"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                <field name="ASSET_CODE" label="资产卡片编号" />
                <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:3000;" autoLoad="false">
            <field name="CODE" label="进线室编码"/>
            <field name="NAME" label="进线室名称" width="250px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="FACILITY_TYPE_ID" label="设施类型"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
        </grid>
    </ObjMeta>

    <!-- PIPESECTION 管道段 -->
    <ObjMeta objectType="PIPESECTION" autoReload="true" needgeom="false"
             typeActions="add,reset,templates,remove"
             itemActions="locate,pipesectionGraph,showCable,modify,remove"
             moreItemActions="expSection,assetAttribute,filesman">
        <action type="workspace" name="templates" label="模板管理" label-en="Template management" title="管道段模板"
                title-en="Pipeline Segment Template" objectType="PIPESECTION.TEMPLATE" icon="columns"/>
        <action name="expSection" type="script" label="导出截面图" label-en="Export CAD" icon="sign-out">
            <![CDATA[
                var url = 'bean/exp.do?_type=PIPESECTION&_scriptmethod=expSection&entityId=' + _actionContext.params.id+'&_expfilename=p_section.dxf';
                var ratio=_context.getSetting('ductSizeRadio');
                if(ratio!=null)
                    url=url+"&ratio="+encodeURIComponent(ratio);
                url=_context.fullDataUrl(url);
                window.open(url, '_blank');
            ]]>
        </action>
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="PIPELINE_TYPE_ID" label="设施类型"/>
            </row>
            <row>
                <field name="LENGTH" label="长度"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="afac" label="A端设施"
                       baseParams='{"SPEC_ID":"ROOM,UNDERWELL,WELL,POLE,SUPPORTPOINT","excludeTemplate":"true"}'/>
                <field name="zfac" label="Z端设施"
                       baseParams='{"SPEC_ID":"ROOM,UNDERWELL,WELL,POLE,SUPPORTPOINT","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
          <row>
                <field name="project" label="工程名称"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
              <field name="PROJECT_STATUS_ID" label="工程状态" />
              <field name="PROPERTY_TYPE_SHARE_ID" label="共享信息" />
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="IS_RECORD_IN_UNDER_ID" label="是否录入地下管线系统"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME" required="true"/>
                <field name="net" label="所属管道" dropdownOptions="titleField:NAME" />
            </row>
            <row>
                <field name="CODE" label="编码" required="true"/>
            </row>
            <row>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="afac" label="A端设施" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"ROOM,UNDERWELL,WELL,POLE,SUPPORTPOINT","excludeTemplate":"true"}'/>
                <field name="zfac" label="Z端设施" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"ROOM,UNDERWELL,WELL,POLE,SUPPORTPOINT","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="adevice" label="A端设备" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"GJ,GF,GB,ZHX,CCP,DP,ZHX,INFORMATIONPOINT","excludeTemplate":"true"}'/>
                <field name="zdevice" label="Z端设备" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"GJ,GF,GB,ZHX,CCP,DP,ZHX,INFORMATIONPOINT","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="PIPELINE_TYPE_ID" label="管道类型"/>
                <field name="PIPE_LEVEL_ID" label="管道级别"/>
            </row>
            <row>
                <field name="PHYSICAL_STATE_ID" label="物理状态"/>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204105"/>
                <field name="BADHOLE_NUM" label="坏孔数"/>
            </row>
            <row>
                <field name="USEDHOLE_NUM" label="在用孔数"/>
                <field name="FREEHOLE_NUM" label="空闲孔数"/>
            </row>
            <row>
                <field name="ALLHOLE_NUM" label="总孔数"/>
                <field name="SONHOLE_NUM" label="子孔数"/>
            </row>
            <row>
                <field name="MAP_LENGTH" label="地图长度(m)"/>
                <field name="LENGTH" label="实际长度(m)" required="true"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" readOnly="true" default="80204666"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_SHARE_ID" label="共享信息"/>
                <field name="IS_RECORD_IN_UNDER_ID" label="是否录入地下管线系统"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="A_FACE_LENGTH" label="起始端截面宽度"/>
                <field name="Z_FACE_LENGTH" label="终止端截面宽度"/>
            </row>
            <row>
                <field name="A_FACE_HEIGHT" label="起始端截面高度"/>
                <field name="Z_FACE_HEIGHT" label="终止端截面高度"/>
            </row>
            <row>
                <field name="A_FACE_NO" label="起始井面号"/>
                <field name="Z_FACE_NO" label="终止井面号"/>
            </row>
            <row>
                <field name="A_BURY_DEPTH" label="起始管道埋深"/>
                <field name="Z_BURY_DEPTH" label="终止管道埋深"/>
            </row>
            <row>
                <field name="HOLECOLNUM" label="起始横向最大管孔数量"/>
                <field name="HOLEROWNUM" label="起始纵向最大管孔数量"/>
            </row>
            <row>
                <field name="ROAD_MATERIAL" label="路面材料"/>
                <field name="ROAD_NAME" label="路段名称"/>
            </row>
            <row>
                <field name="IS_CONSTRUCT" label="是否报建"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="COVERMAT" label="包封材料"/>
                <field name="CONSTRUCT_NO" label="报建批文号"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO;PROJECT_STATUS_ID=project.PROJECT_STATUS_ID"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>      
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
               <field name="ASSET_CODE" label="资产卡片编号" />
               <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="USE_DEPT" label="使用部门"/>
                <field name="CONSTRUCTION" label="施工单位"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!-- 批量修改表格 -->
        <form name="batchmodify">
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME" readOnly="true"/>
                <field name="net" label="所属管道" dropdownOptions="titleField:NAME" />
            </row>
            <row>
                <field name="CODE" label="编码" readOnly="true"/>
            </row>
            <row>
                <field name="NAME" label="名称" readOnly="true"/>
            </row>
            <row>
                <field name="afac" label="A端设施" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"ROOM,UNDERWELL,WELL,POLE,SUPPORTPOINT","excludeTemplate":"true"}'/>
                <field name="zfac" label="Z端设施" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"ROOM,UNDERWELL,WELL,POLE,SUPPORTPOINT","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="adevice" label="A端设备" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"GJ,GF,GB,ZHX,CCP,DP,ZHX,INFORMATIONPOINT","excludeTemplate":"true"}'/>
                <field name="zdevice" label="Z端设备" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"GJ,GF,GB,ZHX,CCP,DP,ZHX,INFORMATIONPOINT","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="PIPELINE_TYPE_ID" label="管道类型"/>
                <field name="PIPE_LEVEL_ID" label="管道级别"/>
            </row>
            <row>
                <field name="PHYSICAL_STATE_ID" label="物理状态"/>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型"/>
                <field name="BADHOLE_NUM" label="坏孔数"/>
            </row>
            <row>
                <field name="USEDHOLE_NUM" label="在用孔数"/>
                <field name="FREEHOLE_NUM" label="空闲孔数"/>
            </row>
            <row>
                <field name="ALLHOLE_NUM" label="总孔数"/>
                <field name="SONHOLE_NUM" label="子孔数"/>
            </row>
            <row>
                <field name="MAP_LENGTH" label="地图长度(m)" readOnly="true"/>
                <field name="LENGTH" label="实际长度(m)" />
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" readOnly="true"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_SHARE_ID" label="共享信息"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="A_FACE_LENGTH" label="起始端截面宽度"/>
                <field name="Z_FACE_LENGTH" label="终止端截面宽度"/>
            </row>
            <row>
                <field name="A_FACE_HEIGHT" label="起始端截面高度"/>
                <field name="Z_FACE_HEIGHT" label="终止端截面高度"/>
            </row>
            <row>
                <field name="A_FACE_NO" label="起始井面号"/>
                <field name="Z_FACE_NO" label="终止井面号"/>
            </row>
            <row>
                <field name="A_BURY_DEPTH" label="起始管道埋深"/>
                <field name="Z_BURY_DEPTH" label="终止管道埋深"/>
            </row>
            <row>
                <field name="HOLECOLNUM" label="起始横向最大管孔数量"/>
                <field name="HOLEROWNUM" label="起始纵向最大管孔数量"/>
            </row>
            <row>
                <field name="ROAD_MATERIAL" label="路面材料"/>
                <field name="ROAD_NAME" label="路段名称"/>
            </row>
            <row>
                <field name="IS_CONSTRUCT" label="是否报建"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" readOnly="true"/>
            </row>
            <row>
                <field name="COVERMAT" label="包封材料"/>
                <field name="CONSTRUCT_NO" label="报建批文号"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称" readOnly="true"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号" readOnly="true"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" />
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" />
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" multiple="true"/>
            </row>
            <row>
                <field name="ASSET_CODE" label="资产卡片编号" />
                <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="USE_DEPT" label="使用部门"/>
                <field name="CONSTRUCTION" label="施工单位"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="LENGTH" label="实际长度"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:3000;" autoLoad="false">
            <field name="CODE" label="管道段编码"/>
            <field name="NAME" label="管道段名称" width="250px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="PIPELINE_TYPE_ID" label="设施类型"/>
            <field name="LENGTH" label="长度"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="afac" label="A端设施"/>
            <field name="zfac" label="Z端设施"/>
            <field name="adevice" label="A端设备"/>
            <field name="zdevice" label="Z端设备"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="IS_RECORD_IN_UNDER_ID" label="是否录入地下管线系统"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
        </grid>
        <grid name="pipeSystemMgt" typeActions="add,templates,laying,moveInPipesection,moveOutPipesection">
            <field name="NAME"/>
            <field name="CODE"/>
        </grid>
        <!--缆线测量 grid-->
        <grid name="measureLine" toolbar="false" rowDetailField="false">
            <field name="METACATEGORYCN" label="类别"/>
            <field name="CODE" label="编码"/>
            <field name="MAP_LENGTH" label="地图长度"/>
            <field name="LENGTH" label="实际长度"/>
        </grid>
    </ObjMeta>
    <!-- PIPESECTION模板 -->
    <ObjMeta objectType="PIPESECTION.TEMPLATE" autoReload="true"
             typeActions="add,remove" titleField="NAME" labelField="NAME"
             itemActions="modify,remove,filesman">
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="模板名称" required="true"/>
            </row>
        </form>
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="PIPELINE_TYPE_ID" label="管道类型"/>
                <field name="PIPE_LEVEL_ID" label="管道级别"/>
            </row>
            <row>
                <field name="PHYSICAL_STATE_ID" label="物理状态"/>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
            </row>
            <row>
                <field name="USEDHOLE_NUM" label="在用孔数"/>
                <field name="FREEHOLE_NUM" label="空闲孔数"/>
            </row>
            <row>
                <field name="BADHOLE_NUM" label="坏孔数"/>
                <field name="OWNER_NET_ID" label="网络类型"/>
            </row>
            <row>
                <field name="ALLHOLE_NUM" label="总孔数"/>
                <field name="SONHOLE_NUM" label="子孔数"/>
            </row>
            <row>
                <field name="MAP_LENGTH" label="地图长度(m)"/>
                <field name="LENGTH" label="实际长度(m)" required="true"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="A_FACE_LENGTH" label="起始端截面宽度"/>
                <field name="Z_FACE_LENGTH" label="终止端截面宽度"/>
            </row>
            <row>
                <field name="A_FACE_HEIGHT" label="起始端截面高度"/>
                <field name="Z_FACE_HEIGHT" label="终止端截面高度"/>
            </row>
            <row>
                <field name="A_FACE_NO" label="起始井面号"/>
                <field name="Z_FACE_NO" label="终止井面号"/>
            </row>
            <row>
                <field name="A_BURY_DEPTH" label="起始管道埋深"/>
                <field name="Z_BURY_DEPTH" label="终止管道埋深"/>
            </row>
            <row>
                <field name="ROAD_NAME" label="所在路名"/>
                <field name="ROAD_MATERIAL" label="路面材料"/>
            </row>
            <row>
                <field name="IS_CONSTRUCT" label="是否报建"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="COVERMAT" label="包封材料"/>
                <field name="CONSTRUCT_NO" label="报建批文号"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护部门"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:1390;" autoLoad="false">
            <field name="NAME" label="模板名称" width="250px"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
    </ObjMeta>

    <!-- UPPERSECTION 引上段 -->
    <ObjMeta objectType="UPPERSECTION" autoReload="true" needgeom="false"
             typeActions="add,reset,remove"
             itemActions="locate,modify,remove"
             moreItemActions="pipesectionGraph,assetAttribute,filesman">
        <action name="genCode" type="script">
            <![CDATA[
	            var a = '', z = '';
	            if (afac && afac.CODE) a = afac.CODE;
	            if (zfac && zfac.CODE) z = zfac.CODE;
	            a + '-' + z;
	        ]]>
        </action>
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="PIPELINE_TYPE_ID" label="设施段类型"/>
            </row>
            <row>
                <field name="LENGTH" label="长度"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="afac" label="A端设施"
                       baseParams='{"SPEC_ID":"ROOM_OUTDOORADDRESS,UNDERWELL,DRAWINGPOINT,WELL,MARKSTONE","excludeTemplate":"true"}'/>
                <field name="zfac" label="Z端设施"
                       baseParams='{"SPEC_ID":"ROOM_OUTDOORADDRESS,UNDERWELL,DRAWINGPOINT,WELL,MARKSTONE","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="project" label="工程名称"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
              <field name="PROJECT_STATUS_ID" label="工程状态" />
              <field name="PROPERTY_TYPE_SHARE_ID" label="共享信息" />
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME" required="true"/>
                <field name="net" label="所属管道" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" label="编码" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204105"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="PIPELINE_TYPE_ID" label="管道类型"/>
                <field name="PIPE_LEVEL_ID" label="管道级别"/>
            </row>
            <row>
                <field name="PHYSICAL_STATE_ID" label="物理状态"/>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
            </row>
            <row>
                <field name="afac" label="A端设施" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"ROOM_OUTDOORADDRESS,UNDERWELL,DRAWINGPOINT,WELL,MARKSTONE","excludeTemplate":"true"}'/>
                <field name="zfac" label="Z端设施" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"ROOM_OUTDOORADDRESS,UNDERWELL,DRAWINGPOINT,WELL,MARKSTONE","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="ALLHOLE_NUM" label="总孔数"/>
                <field name="SONHOLE_NUM" label="子孔数"/>
            </row>
            <row>
                <field name="BADHOLE_NUM" label="坏孔数"/>
                <field name="ROAD_NAME" label="所在路名"/>
            </row>
            <row>
                <field name="LENGTH" label="实际长度(m)" required="true"/>
                <field name="MAP_LENGTH" label="地图长度(m)"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" readOnly="true" default="80204666"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_SHARE_ID" label="共享信息"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="A_FACE_LENGTH" label="起始端截面宽度"/>
                <field name="Z_FACE_LENGTH" label="终止端截面宽度"/>
            </row>
            <row>
                <field name="A_FACE_HEIGHT" label="起始端截面高度"/>
                <field name="Z_FACE_HEIGHT" label="终止端截面高度"/>
            </row>
            <row>
                <field name="A_FACE_NO" label="起始井面号"/>
                <field name="Z_FACE_NO" label="终止井面号"/>
            </row>
            <row>
                <field name="A_BURY_DEPTH" label="起始管道埋深"/>
                <field name="Z_BURY_DEPTH" label="终止管道埋深"/>
            </row>
            <row>
                <field name="USEDHOLE_NUM" label="在用孔数"/>
                <field name="FREEHOLE_NUM" label="空闲孔数"/>
            </row>
            <row>
                <field name="ROAD_MATERIAL" label="路面材料"/>
                <field name="COVERMAT" label="包封材料"/>
            </row>
            <row>
                <field name="CONSTRUCT_NO" label="报建批文号"/>
                <field name="IS_CONSTRUCT" label="是否报建"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO;PROJECT_STATUS_ID=project.PROJECT_STATUS_ID"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>        
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
               <field name="ASSET_CODE" label="资产卡片编号" />
               <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!-- 批量修改表格 -->
        <form name="batchmodify">
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME" readOnly="true"/>
                <field name="net" label="所属管道" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" label="编码" readOnly="true"/>
                <field name="NAME" label="名称" readOnly="true"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" />
                <field name="LIFE_STATE_ID" label="生命周期状态" readOnly="true"/>
            </row>
            <row>
                <field name="PIPELINE_TYPE_ID" label="管道类型"/>
                <field name="PIPE_LEVEL_ID" label="管道级别"/>
            </row>
            <row>
                <field name="PHYSICAL_STATE_ID" label="物理状态"/>
                <field name="LONG_LOCAL_ID" label="长本属性" />
            </row>
            <row>
                <field name="afac" label="A端设施" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"ROOM_OUTDOORADDRESS,UNDERWELL,DRAWINGPOINT,WELL,MARKSTONE","excludeTemplate":"true"}'/>
                <field name="zfac" label="Z端设施" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"ROOM_OUTDOORADDRESS,UNDERWELL,DRAWINGPOINT,WELL,MARKSTONE","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="ALLHOLE_NUM" label="总孔数"/>
                <field name="SONHOLE_NUM" label="子孔数"/>
            </row>
            <row>
                <field name="BADHOLE_NUM" label="坏孔数"/>
                <field name="ROAD_NAME" label="所在路名"/>
            </row>
            <row>
                <field name="LENGTH" label="实际长度(m)" />
                <field name="MAP_LENGTH" label="地图长度(m)" readOnly="true"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" readOnly="true" />
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_SHARE_ID" label="共享信息"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="A_FACE_LENGTH" label="起始端截面宽度"/>
                <field name="Z_FACE_LENGTH" label="终止端截面宽度"/>
            </row>
            <row>
                <field name="A_FACE_HEIGHT" label="起始端截面高度"/>
                <field name="Z_FACE_HEIGHT" label="终止端截面高度"/>
            </row>
            <row>
                <field name="A_FACE_NO" label="起始井面号"/>
                <field name="Z_FACE_NO" label="终止井面号"/>
            </row>
            <row>
                <field name="A_BURY_DEPTH" label="起始管道埋深"/>
                <field name="Z_BURY_DEPTH" label="终止管道埋深"/>
            </row>
            <row>
                <field name="USEDHOLE_NUM" label="在用孔数"/>
                <field name="FREEHOLE_NUM" label="空闲孔数"/>
            </row>
            <row>
                <field name="ROAD_MATERIAL" label="路面材料"/>
                <field name="COVERMAT" label="包封材料"/>
            </row>
            <row>
                <field name="CONSTRUCT_NO" label="报建批文号"/>
                <field name="IS_CONSTRUCT" label="是否报建"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称" readOnly="true"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号" readOnly="true"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" multiple="true"/>
            </row>
            <row>
                <field name="ASSET_CODE" label="资产卡片编号" />
                <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="LENGTH" label="实际长度"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:3000;" autoLoad="false">
            <field name="CODE" label="引上段编码"/>
            <field name="NAME" label="引上段名称" width="250px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="PIPELINE_TYPE_ID" label="支撑段类型"/>
            <field name="LENGTH" label="长度"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="afac" label="A端设施"/>
            <field name="zfac" label="Z端设施"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
        </grid>
    </ObjMeta>

    <!-- SHAFT 竖井 -->
    <ObjMeta objectType="SHAFT" autoReload="true" needgeom="false"
             typeActions="add,reset,remove"
             itemActions="locate,modify,remove"
             moreItemActions="pipesectionGraph,assetAttribute,filesman">
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="PIPELINE_TYPE_ID" label="设施段类型"/>
            </row>
            <row>
                <field name="LENGTH" label="长度"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="afac" label="A端设施"
                       baseParams='{"SPEC_ID":"ROOM_OUTDOORADDRESS,UNDERWELL","excludeTemplate":"true"}'/>
                <field name="zfac" label="Z端设施"
                       baseParams='{"SPEC_ID":"ROOM_OUTDOORADDRESS,UNDERWELL","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="project" label="工程名称"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
              <field name="PROJECT_STATUS_ID" label="工程状态" />
              <field name="PROPERTY_TYPE_SHARE_ID" label="共享信息" />
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="net" label="所属管道" required="true" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" label="编码" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="PIPELINE_TYPE_ID" label="管道类型"/>
                <field name="PIPE_LEVEL_ID" label="管道级别"/>
            </row>
            <row>
                <field name="PHYSICAL_STATE_ID" label="物理状态"/>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
            </row>
            <row>
                <field name="afac" label="A端设施" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"ROOM_OUTDOORADDRESS,UNDERWELL","excludeTemplate":"true"}'/>
                <field name="zfac" label="Z端设施" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"ROOM_OUTDOORADDRESS,UNDERWELL","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="USEDHOLE_NUM" label="在用孔数"/>
                <field name="FREEHOLE_NUM" label="空闲孔数"/>
            </row>
            <row>
                <field name="ALLHOLE_NUM" label="总孔数"/>
                <field name="SONHOLE_NUM" label="子孔数"/>
            </row>
            <row>
                <field name="BADHOLE_NUM" label="坏孔数"/>
                <field name="ROAD_NAME" label="所在路名"/>
            </row>
            <row>
                <field name="LENGTH" label="实际长度(m)" required="true"/>
                <field name="MAP_LENGTH" label="地图长度(m)"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" readOnly="true" default="80204666"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_SHARE_ID" label="共享信息"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="A_FACE_LENGTH" label="起始端截面宽度"/>
                <field name="Z_FACE_LENGTH" label="终止端截面宽度"/>
            </row>
            <row>
                <field name="A_FACE_HEIGHT" label="起始端截面高度"/>
                <field name="Z_FACE_HEIGHT" label="终止端截面高度"/>
            </row>
            <row>
                <field name="A_FACE_NO" label="起始井面号"/>
                <field name="Z_FACE_NO" label="终止井面号"/>
            </row>
            <row>
                <field name="A_BURY_DEPTH" label="起始管道埋深"/>
                <field name="Z_BURY_DEPTH" label="终止管道埋深"/>
            </row>
            <row>
                <field name="ROAD_MATERIAL" label="路面材料"/>
                <field name="COVERMAT" label="包封材料"/>
            </row>
            <row>
                <field name="CONSTRUCT_NO" label="报建批文号"/>
                <field name="IS_CONSTRUCT" label="是否报建"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO;PROJECT_STATUS_ID=project.PROJECT_STATUS_ID"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>            
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row> 
            <row>
                <field name="ASSET_CODE" label="资产卡片编号" />
                <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
             <field name="LENGTH" label="长度"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:3000;" autoLoad="false">
            <field name="CODE" label="竖井编码"/>
            <field name="NAME" label="竖井名称" width="250px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="PIPELINE_TYPE_ID" label="支撑段类型"/>
            <field name="LENGTH" label="长度"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="afac" label="A端设施"/>
            <field name="zfac" label="Z端设施"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
        </grid>
    </ObjMeta>

    <!-- HANGLINGSECTION 吊线段 -->
    <ObjMeta objectType="HANGLINGSECTION" autoReload="true" needgeom="false"
             typeActions="add,reset,template,remove"
             itemActions="locate,modify,remove"
             moreItemActions="showCable,assetAttribute,filesman">
        <action type="workspace" name="templates" label="模板管理" label-en="Template management" title="吊线段模板"
                title-en="Pipeline Segment Template" objectType="HANGLINGSECTION.TEMPLATE" icon="columns"/>
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="PIPELINE_TYPE_ID" label="设施段类型"/>
            </row>
            <row>
                <field name="LENGTH" label="长度"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="afac" label="A端设施"
                       baseParams='{"SPEC_ID":"ROOM_OUTDOORADDRESS,POLE,DRAWINGPOINT,SUPPORTPOINT,MARKSTONE","excludeTemplate":"true"}'/>
                <field name="zfac" label="Z端设施"
                       baseParams='{"SPEC_ID":"ROOM_OUTDOORADDRESS,POLE,DRAWINGPOINT,SUPPORTPOINT,MARKSTONE","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="project" label="工程名称"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
              <field name="PROJECT_STATUS_ID" label="工程状态" />
              <field name="PROPERTY_TYPE_SHARE_ID" label="共享信息" />
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="net" label="所属吊线" required="false" rtype="HANGLING" dropdownOptions="titleField:NAME" />
            </row>
            <row>
                <field name="CODE" label="编码" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="afac" label="A端设施" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"ROOM_OUTDOORADDRESS,POLE,DRAWINGPOINT,SUPPORTPOINT,MARKSTONE","excludeTemplate":"true"}'/>
                <field name="zfac" label="Z端设施" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"ROOM_OUTDOORADDRESS,POLE,DRAWINGPOINT,SUPPORTPOINT,MARKSTONE","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204105"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="PIPELINE_TYPE_ID" label="管道类型"/>
                <field name="MATERIAL_ID" label="吊线材料"/>
            </row>
            <row>
                <field name="PHYSICAL_STATE_ID" label="物理状态"/>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
            </row>
            <row>
                <field name="IS_VIRTUAL" label="是否虚拟"/>
                <field name="HANDCABLE_NUM" label="挂缆条数"/>
            </row>
            <row>
                <field name="LENGTH" label="实际长度(m)" required="true"/>
                <field name="MAP_LENGTH" label="地图长度(m)"/>
            </row>
            <row>
                <field name="SLINGSEG_MODEL" label="吊线程式"/>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" readOnly="true" default="80204666"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_SHARE_ID" label="共享信息"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO;PROJECT_STATUS_ID=project.PROJECT_STATUS_ID"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="asset" label="资产目录" dropdownOptions="titleField:ASSET_CATALOGUE" />             
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                  <field name="ASSET_CODE" label="资产卡片编号" />
                  <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!--批量修改表格-->
        <form name="batchmodify">
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME" readOnly="true"/>
                <field name="net" label="所属吊线" rtype="HANGLING" dropdownOptions="titleField:NAME" />
            </row>
            <row>
                <field name="CODE" label="编码" readOnly="true"/>
                <field name="NAME" label="名称" readOnly="true"/>
            </row>
            <row>
                <field name="afac" label="A端设施" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"ROOM_OUTDOORADDRESS,POLE,DRAWINGPOINT,SUPPORTPOINT,MARKSTONE","excludeTemplate":"true"}'/>
                <field name="zfac" label="Z端设施" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"ROOM_OUTDOORADDRESS,POLE,DRAWINGPOINT,SUPPORTPOINT,MARKSTONE","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" readOnly="true"/>
            </row>
            <row>
                <field name="PIPELINE_TYPE_ID" label="管道类型"/>
                <field name="MATERIAL_ID" label="吊线材料"/>
            </row>
            <row>
                <field name="PHYSICAL_STATE_ID" label="物理状态"/>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
            </row>
            <row>
                <field name="IS_VIRTUAL" label="是否虚拟"/>
                <field name="HANDCABLE_NUM" label="挂缆条数"/>
            </row>
            <row>
                <field name="LENGTH" label="实际长度(m)" />
                <field name="MAP_LENGTH" label="地图长度(m)" readOnly="true"/>
            </row>
            <row>
                <field name="SLINGSEG_MODEL" label="吊线程式"/>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" readOnly="true"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_SHARE_ID" label="共享信息"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称" readOnly="true"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号" readOnly="true"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" />
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" />
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="asset" label="资产目录" dropdownOptions="titleField:ASSET_CATALOGUE" />
                <field name="PROPERTY_OWNER_ID" label="产权归属" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" multiple="true"/>
            </row>
            <row>
                <field name="ASSET_CODE" label="资产卡片编号" />
                <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="LENGTH" label="实际长度"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:3000;" autoLoad="false">
            <field name="CODE" label="吊线段编码"/>
            <field name="NAME" label="吊线段名称" width="250px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="PIPELINE_TYPE_ID" label="支撑段类型"/>
            <field name="LENGTH" label="长度"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="afac" label="A端设施"/>
            <field name="zfac" label="Z端设施"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
        </grid>
        <grid name="hanglingSystemMgt" typeActions="add,templates,laying,moveInHanglingSection,moveOutHanglingSection">
            <field name="NAME"/>
            <field name="CODE"/>
        </grid>
    </ObjMeta>
    <!-- HANGLINGSECTION模板 -->
    <ObjMeta objectType="HANGLINGSECTION.TEMPLATE" autoReload="true"
             typeActions="add,remove" titleField="NAME" labelField="NAME"
             itemActions="modify,remove,filesman">
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="模板名称" required="true"/>
            </row>
        </form>
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="HANDCABLE_NUM" label="挂缆条数"/>
                <field name="OWNER_NET_ID" label="网络类型"/>
            </row>
            <row>
                <field name="PIPELINE_TYPE_ID" label="吊线段类型"/>
                <field name="MATERIAL_ID" label="吊线材料"/>
            </row>
            <row>
                <field name="PHYSICAL_STATE_ID" label="物理状态"/>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
            </row>
            <row>
                <field name="IS_VIRTUAL" label="是否虚拟"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="LENGTH" label="实际长度(m)" required="true"/>
                <field name="MAP_LENGTH" label="地图长度(m)"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护部门"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:1390;" autoLoad="false">
            <field name="NAME" label="模板名称" width="250px"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
    </ObjMeta>

    <!-- COMMONLINE 关联线 -->
    <ObjMeta objectType="COMMONLINE" autoReload="true" needgeom="false"
             typeActions="add,reset,template,remove"
             itemActions="locate,modify,remove"
             moreItemActions="showCable,filesman">
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="PIPELINE_TYPE_ID" label="设施段类型"/>
            </row>
            <row>
                <field name="LENGTH" label="长度"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="afac" label="A端设施"
                       baseParams='{"SPEC_ID":"DEVICE,FACILITY","excludeTemplate":"true"}'/>
                <field name="zfac" label="Z端设施"
                       baseParams='{"SPEC_ID":"DEVICE,FACILITY","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="project" label="工程名称"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
              <field name="PROJECT_STATUS_ID" label="工程状态" />
              <field name="PROPERTY_TYPE_SHARE_ID" label="共享信息" />
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" label="编码" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="afac" label="A端设施" dropdownOptions="titleField:NAME"
                       baseParams='{"excludeTemplate":"true"}'/>
                <field name="zfac" label="Z端设施" dropdownOptions="titleField:NAME"
                       baseParams='{"excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="adevice" label="A端设备" dropdownOptions="titleField:NAME"
                       baseParams='{"excludeTemplate":"true"}'/>
                <field name="zdevice" label="Z端设备" dropdownOptions="titleField:NAME"
                       baseParams='{"excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="LENGTH" label="实际长度(m)" required="true"/>
                <field name="MAP_LENGTH" label="地图长度(m)"/>
            </row>
            <row>
                <field name="PHYSICAL_STATE_ID" label="物理状态"/>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" readOnly="true" default="80204666"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_SHARE_ID" label="共享信息"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO;PROJECT_STATUS_ID=project.PROJECT_STATUS_ID"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                <field name="ASSET_CODE" label="资产卡片编号" />
                <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!-- 批量修改表格 -->
        <form name="batchmodify">
            <row>
                <field name="site" label="所属局站" readOnly="true" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" label="编码" readOnly="true"/>
                <field name="NAME" label="名称" readOnly="true"/>
            </row>
            <row>
                <field name="afac" label="A端设施" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"FACILITY","excludeTemplate":"true"}'/>
                <field name="zfac" label="Z端设施" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"FACILITY","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="adevice" label="A端设备" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"DEVICE","excludeTemplate":"true"}'/>
                <field name="zdevice" label="Z端设备" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"DEVICE","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" readOnly="true"/>
            </row>
            <row>
                <field name="LENGTH" label="实际长度(m)"/>
                <field name="MAP_LENGTH" label="地图长度(m)" readOnly="true"/>
            </row>
            <row>
                <field name="PHYSICAL_STATE_ID" label="物理状态"/>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" readOnly="true"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_SHARE_ID" label="共享信息"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称" readOnly="true"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号" readOnly="true"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" multiple="true"/>
            </row>
            <row>
                <field name="ASSET_CODE" label="资产卡片编号" />
                <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="LENGTH" label="实际长度"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:3000;" autoLoad="false">
            <field name="CODE" label="关联线编码"/>
            <field name="NAME" label="关联线名称" width="250px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="PIPELINE_TYPE_ID" label="支撑段类型"/>
            <field name="LENGTH" label="长度"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="afac" label="A端设施"/>
            <field name="zfac" label="Z端设施"/>
            <field name="adevice" label="A端设备"/>
            <field name="zdevice" label="Z端设备"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
        </grid>
    </ObjMeta>
    <!-- COMMONLINE模板 -->
    <ObjMeta objectType="COMMONLINE.TEMPLATE" autoReload="true"
             typeActions="add,remove" titleField="NAME" labelField="NAME"
             itemActions="modify,remove,filesman">
        <!--高级查询条件-->
        <form name="search">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="模板名称" required="true"/>
            </row>
        </form>
        <form>
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="名称"/>
                <field name="OWNER_NET_ID" label="网络类型"/>
            </row>
            <row>
                <field name="PHYSICAL_STATE_ID" label="物理状态"/>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
            </row>
            <row>
                <field name="IS_VIRTUAL" label="是否虚拟"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="LENGTH" label="实际长度(m)" required="true"/>
                <field name="MAP_LENGTH" label="地图长度(m)"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护部门"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:1390;" autoLoad="false">
            <field name="NAME" label="模板名称" width="250px"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
    </ObjMeta>

    <!-- MARKSTONE 标石 -->
    <ObjMeta objectType="MARKSTONE" autoReload="true" needgeom="true"
             typeActions="add,reset,remove"
             itemActions="locate,modify,remove"
             moreItemActions="filesman,poleview">
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="PIPELINE_TYPE_ID" label="设施段类型"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="地理位置"/>
                <field name="MATERIAL_ID" label="材质"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
          <field name="PROJECT_STATUS_ID" label="工程状态" />
          <row>
                <field name="project" label="工程名称"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="OWNER_NET_ID" label="所属网络" default="80204105"/>
            </row>
            <row>
                <field name="CODE" label="编码" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
            </row>
            <row>
                <field name="BUILDING" label="建筑物名称"/>
                <field name="ROAD_NAME" label="所在路名"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" readOnly="true" default="80204666"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO;PROJECT_STATUS_ID=project.PROJECT_STATUS_ID"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>            
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                <field name="ASSET_CODE" label="资产卡片编号" />
                <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!-- 批量修改表格 -->
        <form name="batchmodify">
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME" readOnly="true"/>
                <field name="OWNER_NET_ID" label="所属网络" />
            </row>
            <row>
                <field name="CODE" label="编码" readOnly="true"/>
                <field name="NAME" label="名称" readOnly="true"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" />
                <field name="LIFE_STATE_ID" label="生命周期状态" readOnly="true"/>
            </row>
            <row>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
            </row>
            <row>
                <field name="BUILDING" label="建筑物名称"/>
                <field name="ROAD_NAME" label="所在路名"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" readOnly="true" />
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称" readOnly="true"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号" readOnly="true"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" multiple="true"/>
            </row>
            <row>
                <field name="ASSET_CODE" label="资产卡片编号" />
                <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:3000;" autoLoad="false">
            <field name="CODE" label="标石编码"/>
            <field name="NAME" label="标石名称" width="250px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
        </grid>
    </ObjMeta>

    <!-- BURIEDSECTION 直埋段 -->
    <ObjMeta objectType="BURIEDSECTION" autoReload="true" needgeom="false"
             typeActions="add,reset,remove"
             itemActions="locate,modify,remove"
             moreItemActions="filesman">
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="PIPELINE_TYPE_ID" label="设施段类型"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="地理位置"/>
                <field name="LENGTH" label="长度"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="afac" label="A端设施"
                       baseParams='{"SPEC_ID":"WELL,MARKSTONE","excludeTemplate":"true"}'/>
                <field name="zfac" label="Z端设施"
                       baseParams='{"SPEC_ID":"WELL,MARKSTONE","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="project" label="工程名称"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
              <field name="PROJECT_STATUS_ID" label="工程状态" />
              <field name="PROPERTY_TYPE_SHARE_ID" label="共享信息" />
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="net" label="所属吊线" required="true" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" label="编码" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="afac" label="A端设施" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"WELL,MARKSTONE","excludeTemplate":"true"}'/>
                <field name="zfac" label="Z端设施" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"WELL,MARKSTONE","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="OWNER_NET_ID" label="网络类型" default="80204105"/>
            </row>
            <row>
                <field name="LENGTH" label="实际长度(m)" required="true"/>
                <field name="MAP_LENGTH" label="地图长度(m)"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
                <field name="PHYSICAL_STATE_ID" label="物理状态"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" default="80204666"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_SHARE_ID" label="共享信息"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="A_FACE_LENGTH" label="起始端截面宽度"/>
                <field name="Z_FACE_LENGTH" label="终止端截面宽度"/>
            </row>
            <row>
                <field name="A_FACE_HEIGHT" label="起始端截面高度"/>
                <field name="Z_FACE_HEIGHT" label="终止端截面高度"/>
            </row>
            <row>
                <field name="A_FACE_NO" label="起始井面号"/>
                <field name="Z_FACE_NO" label="终止井面号"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO;PROJECT_STATUS_ID=project.PROJECT_STATUS_ID"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!-- 批量修改表格 -->
        <form name="batchmodify">
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME" readOnly="true"/>
                <field name="net" label="所属吊线" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" label="编码" readOnly="true"/>
                <field name="NAME" label="名称" readOnly="true"/>
            </row>
            <row>
                <field name="afac" label="A端设施" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"WELL,MARKSTONE","excludeTemplate":"true"}'/>
                <field name="zfac" label="Z端设施" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID":"WELL,MARKSTONE","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" />
                <field name="OWNER_NET_ID" label="网络类型" />
            </row>
            <row>
                <field name="LENGTH" label="实际长度(m)" />
                <field name="MAP_LENGTH" label="地图长度(m)" readOnly="true"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" readOnly="true"/>
                <field name="PHYSICAL_STATE_ID" label="物理状态"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" readOnly="true"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_SHARE_ID" label="共享信息"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="A_FACE_LENGTH" label="起始端截面宽度"/>
                <field name="Z_FACE_LENGTH" label="终止端截面宽度"/>
            </row>
            <row>
                <field name="A_FACE_HEIGHT" label="起始端截面高度"/>
                <field name="Z_FACE_HEIGHT" label="终止端截面高度"/>
            </row>
            <row>
                <field name="A_FACE_NO" label="起始井面号"/>
                <field name="Z_FACE_NO" label="终止井面号"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称" readOnly="true"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号" readOnly="true"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_OWNER_ID" label="产权归属" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="LENGTH" label="实际长度"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:3000;" autoLoad="false">
            <field name="CODE" label="直埋段编码"/>
            <field name="NAME" label="直埋段名称" width="250px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="PIPELINE_TYPE_ID" label="支撑段类型"/>
            <field name="LENGTH" label="长度"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="afac" label="A端设施"/>
            <field name="zfac" label="Z端设施"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
        </grid>
    </ObjMeta>

    <!-- 放到菜单的支撑网下，不放快速搜索里-->
    <!-- PIPE 管道网 -->
    <ObjMeta objectType="PIPE" autoReload="true" itemActions="modify,remove">
        <form>
            <field type="divider" label="基础属性" label-en="Professional attribute"/>
            <row>
                <field name="NAME"/>
            </row>
            <row>
                <field name="CODE"/>
            </row>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="PORE_KM" label="管孔公里"/>
                <field name="PORE_NUMBER" label="管孔数"/>
            </row>
            <row>
                <field name="PIPE_LEVEL_ID" label="管道级别"/>
                <field name="SONHOLE_KM" label="子孔公里"/>
            </row>
            <row>
                <field name="TUBEPASS_KM" label="管程公里"/>
                <field name="PIPESECTION_NUM" label="管道段数"/>
            </row>
            <row>
                <field name="SONHOLE_NUM" label="子孔数"/>
                <field name="MANWELL_NUM" label="人井数量"/>
            </row>
            <row>
                <field name="HANDWELL_NUM" label="手井数量"/>
                <field name="OWNER_NET_ID" label="所属网络ID"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号" />
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="入库时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级" default="102602"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="MNT_DEPT" label="维护机构"/>
                <field name="MNT_PERSON" label="维护人"/>
            </row>
        </form>
        <grid>
            <field name="NAME"/>
            <field name="CODE"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="CREATE_DATE" label="入库时间"/>
        </grid>
    </ObjMeta>

    <!-- 放到菜单的支撑网下，不放快速搜索里-->
    <!-- HANGLING 杆路网 -->
    <ObjMeta objectType="HANGLING" autoReload="true" itemActions="modify,filesman,remove">
        <form>
            <field type="divider" label="基础属性" label-en="Professional attribute"/>
            <row>
                <field name="NAME"/>
                <field name="CODE"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="LENGTH" label="吊线长度"/>
                <field name="NET_TYPE_ID" label="吊线类型"/>
            </row>
            <row>
                <field name="SLING_MODEL_ID" label="吊线程式"/>
                <field name="MATERIAL_ID" label="吊线材料"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="asset" label="资产编码" readOnly="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="入库时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_ID"/>
            </row>
            <row>
                <field name="CHECKING_PERSON" label="检查责任人"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <!-- <field type="divider" label="坐标" label-en="Location information"/>
            <field type="divider" label="工程属性" label-en="Engineering property"/> -->
        </form>
        <grid>
            <field name="NAME"/>
            <field name="CODE"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="CREATE_DATE" label="入库时间"/>
        </grid>
    </ObjMeta>













    <!-- 走线槽 -->
    <ObjMeta objectType="LINESLOT" autoReload="true" needgeom="false" itemActions="locate,modify,filesman,pipelineImpactAnalysis,remove"
             moreItemActions="">
        <form>
            <row>
                <field name="site" required="true" onChange="region=site.REGION_ID"/>
            </row>
            <row>
                <field name="CODE"/>
                <field name="NAME"/>
            </row>
            <row>
                <field name="afac"/>
                <field name="zfac"/>
            </row>
            <row>
                <field name="OWNER_NET_ID"/>
                <field name="LIFE_STATE_ID"/>
            </row>
            <row>
                <field name="PHYSICAL_STATE_ID"/>
                <field name="LONG_LOCAL_ID"/>
            </row>
            <row>
                <field name="LENGTH"/>
                <field name="MAP_LENGTH"/>
            </row>
            <row>
                <field name="NOTES"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <!-- 
           <row><field name="ASSET_CODE"/><field name="CHILD_ASSCARD_CODE"/></row>
            -->
            <row>
                <field name="CREATOR"/>
                <field name="CREATE_DATE"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_ID"/>
                <field name="PROPERTY_OWNER_ID"/>
            </row>
            <row>
                <field name="MNT_WAY_ID"/>
                <field name="MNT_LEVEL_ID" label="维护状态"/>
            </row>
            <row>
                <field name="MNT_PERSON"/>
            </row>
            <row>
                <field name="AUDIT_PERSON"/>
                <field name="AUDIT_DATE"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project"/>
            </row>
        </form>
    </ObjMeta>





    <ObjMeta objectType="PIPELINE" autoReload="true" labelField="NAME">

        <form name="query">
            <row>
                <field name="NAME" required="false"/>
                <field name="CODE" required="false"/>
            </row>
        </form>
        <grid name="laying"><!-- 敷设显示表格 -->
            <!-- holes字段是代码控制显示穿孔信息的 -->
            <field name="CODE"/>
            <field name="NAME"/>
            <field name="selectedHole"/>
        </grid>
        <grid name="gridview" itemActions="locate,modify,pipesectionGraph,layingGridByPipeline">
            <field name="CODE"/>
            <field name="NAME"/>
            <action id="layingGridByPipeline" label="查看缆段" label-en="View cable segment" type="modal">
                <![CDATA[
                <script>
                    var pipeline = _actionContext.params, el = _actionContext.el;
                    _bean.find(pipeline.objectType, {id: pipeline.id, _assemble: 'cables'}).then(function(p){
                        var cs = p.cables.filter(function(c){return c.objectType === 'OCABLESECTION' || c.objectType === 'ECABLESECTION';});
                        _bean.grid('CABLE', null, el, {data: cs});
                    });
                </script>
            ]]>
            </action>
        </grid>
        <grid name="batchModifyLength">
            <field name="METACATEGORYCN" label="规格" getLabel="row.METACATEGORYCN"/>
            <field name="CODE"/>
            <field name="NAME"/>
            <field name="LENGTH" allowedEdit="true"/>
            <field name="isModifySuccess" label="是否修改成功" getLabel="row.isModifySuccess"/>
        </grid>
        <grid toolbar="false" rowDetailField="false" name="opticGroupLocate">
            <field name="GROUP" label="组别"/>
            <field name="OPTIC_CODES" label="同支撑段的光路编码"/>
            <field name="LENGTH" label="同支撑段长度总计"/>
        </grid>
    </ObjMeta>
    <!--管群模版管理-->
    <ObjMeta objectType="TUBEGROUP.TEMPLATE" autoReload="true" itemActions="groupInput,modify,remove" typeActions="add">
        <form>
            <field name="CODE" required="true"/>
            <row>
                <field name="HEIGHT" default="300" required="true"/>
                <field name="WIDTH" default="400" required="true"/>
            </row>
        </form>
        <action name="groupInput" type="window" class="large" style="height:600px;" width="1000px" label="管群录入"
                label-en="Group Input" title="管群录入 ${NAME}" title-en="Group input ${NAME}" icon="keyboard"
                permission="SUPPORTING_FIND"
                mainConfig="apps/pipe/pipeline/tubegroup/TubeGroupSecView.xml&amp;tubegroupId=${id}"/>
    </ObjMeta>

    <ObjMeta objectType="HOLE.TEMPLATE" autoReload="true" typeActions="add" itemActions="modify,remove">
        <form>
            <row>
                <field name="NAME"/>
                <field name="CODE"/>
            </row>
            <row>
                <field name="DIAMETER" label-en="Diameter (mm)" default="110"/>
            </row>
            <row>
                <field name="MATERIAL_ID"/>
                <field name="VIEW_SHAPE_ID" default="80402240"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_ID"/>
                <field name="PROPERTY_OWNER_ID"/>
            </row>
        </form>

    </ObjMeta>

    <ObjMeta objectType="FAULTPOINT" autoReload="true" refreshMapWhenUpdate="true" itemActions="filesman,locate,modify,remove">
        <form>
            <field name="NAME"/>
            <row>
                <field name="device"/>
                <field name="port"/>
            </row>
            <row>
                <field name="SEARCHDIRECTION"/>
                <field name="DISTANCE"/>
            </row>
            <row>
                <field name="cable"/>
                <field name="optic"/>
            </row>
        </form>
        <grid>
            <field name="NAME"/>
            <field name="optic"/>
            <field name="cable"/>
            <field name="device"/>
            <field name="port"/>
            <field name="DISTANCE"/>
        </grid>
    </ObjMeta>
    <!-- 安全标识 -->
    <ObjMeta objectType="SAFETYSIGN" autoReload="true" needgeom="true" refreshMapWhenUpdate="true"
             itemActions="filesman,locate,modify,remove">
        <form>
            <row>
                <field name="CODE" label="编码"/>
            </row>
            <row>
                <field name="NOTES" inputType="textarea" label="备注" inputTagName="textarea"/>
            </row>
        </form>
        <grid>
            <field name="CODE"/>
            <field name="NOTES"/>
        </grid>
    </ObjMeta>
</metas>