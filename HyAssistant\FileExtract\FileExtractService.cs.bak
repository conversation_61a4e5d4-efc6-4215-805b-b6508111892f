using ET;
using SharpCompress.Archives;
using SharpCompress.Common;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace HyAssistant
{
    /// <summary>
    /// 文件提取服务类，负责文件解压缩处理逻辑
    /// </summary>
    public class FileExtractService : IDisposable
    {
        // 文件夹大小缓存，用于避免重复计算
        readonly ConcurrentDictionary<string, Tuple<long, int, DateTime>> _folderInfoCache =
            new ConcurrentDictionary<string, Tuple<long, int, DateTime>>(StringComparer.OrdinalIgnoreCase);

        // 缓存过期时间（分钟）
        const int CACHE_EXPIRY_MINUTES = 30;

        // 标识是否正在处理文件
        bool _isProcessing = false;

        // 取消令牌源
        CancellationTokenSource _cancellationTokenSource;

        // 处理任务集合
        readonly Dictionary<string, Task> _processingTasks = new Dictionary<string, Task>();

        /// <summary>
        /// 初始化文件提取服务
        /// </summary>
        public FileExtractService()
        {
            _cancellationTokenSource = new CancellationTokenSource();
        }

        /// <summary>
        /// 验证路径的有效性和权限
        /// </summary>
        public bool ValidatePaths(string[] sourcePaths, string targetPath)
        {
            try
            {
                // 验证目标路径
                if (!Directory.Exists(targetPath))
                {
                    try
                    {
                        Directory.CreateDirectory(targetPath);
                    }
                    catch
                    {
                        return false;
                    }
                }

                // 测试目标路径写入权限
                string testFile = Path.Combine(targetPath, $"permission_test_{Guid.NewGuid()}.tmp");
                try
                {
                    using (FileStream fs = File.Create(testFile, 1, FileOptions.DeleteOnClose)) { }
                }
                catch
                {
                    return false;
                }

                // 验证源路径
                foreach (string sourcePath in sourcePaths)
                {
                    if (Directory.Exists(sourcePath))
                    {
                        try
                        {
                            // 测试读取权限
                            Directory.GetFiles(sourcePath, "*", SearchOption.TopDirectoryOnly);
                        }
                        catch
                        {
                            return false;
                        }
                    }
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 启动文件处理任务
        /// </summary>
        public async Task StartProcessing(string configFilePath, Action<string, string, bool> logCallback)
        {
            if (_isProcessing)
            {
                logCallback("警告", "已有处理任务正在运行，请先停止当前任务", true);
                return;
            }

            if (!File.Exists(configFilePath))
            {
                logCallback("错误", $"配置文件不存在: {configFilePath}", true);
                return;
            }

            _isProcessing = true;

            try
            {
                await Task.Run(() =>
                {
                    ETIniFile configFile = new ETIniFile(configFilePath);

                    // 获取所有节
                    List<string> sections = configFile.GetSectionNames();

                    foreach (string section in sections)
                    {
                        // 检查是否是有效的配置节，只处理带有指定前缀的节
                        if (section.StartsWith(FileExtract.SECTION_PREFIX, StringComparison.OrdinalIgnoreCase) &&
                            configFile.GetValue(section, "enable") == "1")
                        {
                            string taskName = configFile.GetValue(section, "name", string.Empty);
                            int intervalInMinutes = int.Parse(configFile.GetValue(section, "IntervalInMinutes", "0"));
                            DateTime creationTimeLimit = DateTime.Parse(
                                configFile.GetValue(section, "CreationTimeLimit", DateTime.MinValue.ToString()));
                            DateTime modificationTimeLimit = DateTime.Parse(
                                configFile.GetValue(section, "ModificationTimeLimit", DateTime.MinValue.ToString()));
                            string[] sourcePaths = configFile.GetValue(section, "SourcePaths").Split('|');
                            string[] filePatterns = configFile.GetValue(section, "FilePattern").Split('|');
                            string targetPath = configFile.GetValue(section, "TargetPath");

                            // 安全检查
                            if (!ValidatePaths(sourcePaths, targetPath))
                            {
                                logCallback("错误", $"任务 {taskName} 的路径无效或无足够权限", true);
                                continue;
                            }

                            // 创建并启动任务
                            Task task = CreateProcessingTask(
                                taskName,
                                sourcePaths,
                                filePatterns,
                                targetPath,
                                creationTimeLimit,
                                modificationTimeLimit,
                                intervalInMinutes,
                                logCallback);

                            _processingTasks[section] = task;
                        }
                        // 处理旧式配置节
                        else if (!section.StartsWith(FileExtract.SECTION_PREFIX, StringComparison.OrdinalIgnoreCase) &&
                                 configFile.GetValue(section, "enable") == "1")
                        {
                            logCallback("警告", $"检测到旧式配置节: {section}，请使用配置工具更新为新格式", true);

                            string taskName = configFile.GetValue(section, "name", string.Empty);
                            int intervalInMinutes = int.Parse(configFile.GetValue(section, "IntervalInMinutes", "0"));
                            DateTime creationTimeLimit = DateTime.Parse(
                                configFile.GetValue(section, "CreationTimeLimit", DateTime.MinValue.ToString()));
                            DateTime modificationTimeLimit = DateTime.Parse(
                                configFile.GetValue(section, "ModificationTimeLimit", DateTime.MinValue.ToString()));
                            string[] sourcePaths = configFile.GetValue(section, "SourcePaths").Split('|');
                            string[] filePatterns = configFile.GetValue(section, "FilePattern").Split('|');
                            string targetPath = configFile.GetValue(section, "TargetPath");

                            // 安全检查
                            if (!ValidatePaths(sourcePaths, targetPath))
                            {
                                logCallback("错误", $"任务 {taskName} 的路径无效或无足够权限", true);
                                continue;
                            }

                            // 创建并启动任务
                            Task task = CreateProcessingTask(
                                taskName,
                                sourcePaths,
                                filePatterns,
                                targetPath,
                                creationTimeLimit,
                                modificationTimeLimit,
                                intervalInMinutes,
                                logCallback);

                            _processingTasks[section] = task;
                        }
                    }
                }).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                logCallback("错误", $"启动处理任务时出错: {ex.Message}", true);
                _isProcessing = false;
            }
        }

        /// <summary>
        /// 取消所有正在进行的处理任务
        /// </summary>
        public async Task CancelAllProcessing()
        {
            if (_cancellationTokenSource != null && !_cancellationTokenSource.IsCancellationRequested)
            {
                _cancellationTokenSource.Cancel();

                // 等待所有任务完成或被取消
                foreach (Task task in _processingTasks.Values)
                {
                    try
                    {
                        await task;
                    }
                    catch (OperationCanceledException)
                    {
                        // 正常取消，不需要处理
                    }
                    catch (Exception)
                    {
                        // 其他错误，忽略
                    }
                }

                _processingTasks.Clear();
                _cancellationTokenSource = new CancellationTokenSource();
                _isProcessing = false;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                // 取消处理任务
                CancelAllProcessing().Wait();

                // 释放取消令牌
                if (_cancellationTokenSource != null)
                {
                    _cancellationTokenSource.Dispose();
                    _cancellationTokenSource = null;
                }
            }
            catch
            {
                // 忽略释放资源时的错误
            }
        }

        /// <summary>
        /// 创建处理任务
        /// </summary>
        Task CreateProcessingTask(
            string taskName,
            string[] sourcePaths,
            string[] filePatterns,
            string targetPath,
            DateTime creationTimeLimit,
            DateTime modificationTimeLimit,
            int intervalInMinutes,
            Action<string, string, bool> logCallback)
        {
            return Task.Run(
                async () =>
                {
                    while (!_cancellationTokenSource.Token.IsCancellationRequested)
                    {
                        await ProcessFiles(
                            taskName,
                            sourcePaths,
                            filePatterns,
                            targetPath,
                            creationTimeLimit,
                            modificationTimeLimit,
                            logCallback).ConfigureAwait(false);

                        await Task.Delay(TimeSpan.FromMinutes(intervalInMinutes), _cancellationTokenSource.Token).ConfigureAwait(false);
                    }
                },
                _cancellationTokenSource.Token);
        }

        /// <summary>
        /// 处理文件
        /// </summary>
        async Task ProcessFiles(
            string taskName,
            string[] sourcePaths,
            string[] filePatterns,
            string targetPath,
            DateTime creationTimeLimit,
            DateTime modificationTimeLimit,
            Action<string, string, bool> logCallback)
        {
            try
            {
                logCallback("运行", $"开始执行：{taskName}", true);

                // 处理每个源路径下的文件
                foreach (string sourcePath in sourcePaths)
                {
                    if (!Directory.Exists(sourcePath))
                    {
                        continue;
                    }

                    await ProcessFilesInPath(
                        sourcePath,
                        filePatterns,
                        targetPath,
                        creationTimeLimit,
                        modificationTimeLimit,
                        logCallback).ConfigureAwait(false);
                }

                logCallback("完成", $"{taskName}", true);
            }
            catch (OperationCanceledException)
            {
                logCallback("信息", $"{taskName} 任务已取消", true);
            }
            catch (Exception ex)
            {
                logCallback("错误", $"{taskName} 任务执行出错：{ex.Message}", true);
            }
        }

        /// <summary>
        /// 处理指定路径下的文件
        /// </summary>
        async Task ProcessFilesInPath(
            string sourcePath,
            string[] filePatterns,
            string targetPath,
            DateTime creationTimeLimit,
            DateTime modificationTimeLimit,
            Action<string, string, bool> logCallback)
        {
            foreach (string filePattern in filePatterns)
            {
                try
                {
                    string[] files = Directory.GetFiles(sourcePath, filePattern, SearchOption.AllDirectories);

                    foreach (string file in files)
                    {
                        if (_cancellationTokenSource.Token.IsCancellationRequested)
                        {
                            return;
                        }

                        try
                        {
                            await ProcessSingleFile(
                                file,
                                targetPath,
                                creationTimeLimit,
                                modificationTimeLimit,
                                logCallback).ConfigureAwait(false);
                        }
                        catch (Exception ex)
                        {
                            logCallback("错误", $"处理文件 {file} 时出错：{ex.Message}", false);
                        }
                    }
                }
                catch (Exception ex)
                {
                    logCallback("错误", $"搜索文件模式 {filePattern} 在 {sourcePath} 时出错: {ex.Message}", false);
                }
            }
        }

        /// <summary>
        /// 处理单个文件
        /// </summary>
        async Task ProcessSingleFile(
            string file,
            string targetPath,
            DateTime creationTimeLimit,
            DateTime modificationTimeLimit,
            Action<string, string, bool> logCallback)
        {
            FileInfo fileInfo = new FileInfo(file);
            if (fileInfo.CreationTime >= creationTimeLimit &&
                fileInfo.LastWriteTime >= modificationTimeLimit)
            {
                string dateFolder = fileInfo.LastWriteTime.ToString("yyyy-MM-dd");
                string extractFolder = Path.Combine(
                    targetPath,
                    dateFolder,
                    Path.GetFileNameWithoutExtension(file));

                // 防止路径穿越攻击
                if (!IsValidExtractPath(targetPath, extractFolder))
                {
                    logCallback("警告", $"检测到不安全的路径: {extractFolder}", true);
                    return;
                }

                if (!Directory.Exists(extractFolder) ||
                    Directory.GetFileSystemEntries(extractFolder).Length == 0)
                {
                    try
                    {
                        Directory.CreateDirectory(extractFolder);
                        await Task.Run(() => ExtractArchive(file, extractFolder, _cancellationTokenSource.Token)).ConfigureAwait(false);
                        logCallback("信息", $"已解压文件：{file} 到 {extractFolder}", true);
                    }
                    catch (Exception ex) when (!(ex is OperationCanceledException))
                    {
                        logCallback("错误", $"创建或解压到目录 {extractFolder} 时出错: {ex.Message}", false);
                        return;
                    }
                }

                Tuple<long, int> folderInfo = await GetFolderInfoAsync(extractFolder, _cancellationTokenSource.Token).ConfigureAwait(false);
                long folderSize = folderInfo.Item1;
                int fileCount = folderInfo.Item2;

                await DeleteDuplicateFolders(
                    targetPath,
                    Path.GetFileNameWithoutExtension(file),
                    extractFolder,
                    folderSize,
                    fileCount,
                    logCallback).ConfigureAwait(false);
            }
        }

        /// <summary>
        /// 验证解压路径是否合法（防止路径穿越攻击）
        /// </summary>
        bool IsValidExtractPath(string basePath, string fullPath)
        {
            // 规范化路径
            string normalizedBasePath = Path.GetFullPath(basePath);
            string normalizedFullPath = Path.GetFullPath(fullPath);

            // 确保解压路径是基础路径的子目录
            return normalizedFullPath.StartsWith(normalizedBasePath, StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 异步获取文件夹信息（大小和文件数量）
        /// </summary>
        async Task<Tuple<long, int>> GetFolderInfoAsync(string folderPath, CancellationToken cancellationToken)
        {
            // 先检查缓存
            if (_folderInfoCache.TryGetValue(folderPath, out Tuple<long, int, DateTime> cachedInfo))
            {
                // 检查缓存是否过期
                if ((DateTime.Now - cachedInfo.Item3).TotalMinutes < CACHE_EXPIRY_MINUTES)
                {
                    return new Tuple<long, int>(cachedInfo.Item1, cachedInfo.Item2);
                }
            }

            // 并行计算文件夹大小和文件数量
            Task<long> folderSizeTask = Task.Run(() => GetDirectorySize(folderPath, cancellationToken), cancellationToken);
            Task<int> fileCountTask = Task.Run(() => GetDirectoryFileCount(folderPath, cancellationToken), cancellationToken);

            await Task.WhenAll(folderSizeTask, fileCountTask).ConfigureAwait(false);

            long folderSize = folderSizeTask.Result;
            int fileCount = fileCountTask.Result;

            // 更新缓存
            _folderInfoCache[folderPath] = new Tuple<long, int, DateTime>(folderSize, fileCount, DateTime.Now);

            return new Tuple<long, int>(folderSize, fileCount);
        }

        /// <summary>
        /// 获取文件夹中的文件数量
        /// </summary>
        int GetDirectoryFileCount(string folderPath, CancellationToken cancellationToken)
        {
            try
            {
                return Directory.GetFiles(folderPath, "*", SearchOption.AllDirectories).Length;
            }
            catch (Exception ex) when (!(ex is OperationCanceledException))
            {
                ETLogManager.Error($"计算文件夹 {folderPath} 的文件数量时出错", ex);
                return -1;
            }
        }

        /// <summary>
        /// 使用 SharpCompress 解压文件
        /// </summary>
        void ExtractArchive(string sourceFile, string destinationDirectory, CancellationToken cancellationToken)
        {
            try
            {
                using (IArchive archive = ArchiveFactory.Open(sourceFile))
                {
                    foreach (IArchiveEntry entry in archive.Entries)
                    {
                        // 检查取消标记
                        cancellationToken.ThrowIfCancellationRequested();

                        if (!entry.IsDirectory)
                        {
                            // 验证条目路径是否安全
                            string fullPath = Path.Combine(destinationDirectory, entry.Key);
                            if (!IsValidExtractPath(destinationDirectory, fullPath))
                            {
                                ETLogManager.Warning($"检测到不安全的压缩文件条目: {entry.Key}");
                                continue;
                            }

                            entry.WriteToDirectory(
                                destinationDirectory,
                                new ExtractionOptions { ExtractFullPath = true, Overwrite = true });
                        }
                    }
                }
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                // 保留内部异常的堆栈跟踪
                throw new Exception($"解压文件 {sourceFile} 时出错：{ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取文件夹大小
        /// </summary>
        long GetDirectorySize(string folderPath, CancellationToken cancellationToken)
        {
            try
            {
                // 分批处理文件以减少内存使用
                long totalSize = 0;
                foreach (string file in Directory.GetFiles(folderPath, "*", SearchOption.AllDirectories))
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    totalSize += new FileInfo(file).Length;
                }
                return totalSize;
            }
            catch (Exception ex) when (!(ex is OperationCanceledException))
            {
                ETLogManager.Error($"计算文件夹 {folderPath} 大小时出错", ex);
                return -1;
            }
        }

        /// <summary>
        /// 查找并删除重复文件夹
        /// </summary>
        async Task DeleteDuplicateFolders(
            string targetPath,
            string folderName,
            string originalFolder,
            long originalSize,
            int originalFileCount,
            Action<string, string, bool> logCallback)
        {
            try
            {
                // 查找所有同名但不同路径的文件夹
                IEnumerable<string> potentialDuplicateFolders = Directory.GetDirectories(
                    targetPath,
                    "*",
                    SearchOption.AllDirectories)
                    .Where(f => Path.GetFileName(f).Equals(folderName, StringComparison.OrdinalIgnoreCase)
                             && !f.Equals(originalFolder, StringComparison.OrdinalIgnoreCase));

                foreach (string folder in potentialDuplicateFolders)
                {
                    _cancellationTokenSource.Token.ThrowIfCancellationRequested();

                    // 改进文件夹路径比较，确保不是子文件夹或父文件夹
                    if (!IsSubdirectoryOf(folder, originalFolder) && !IsSubdirectoryOf(originalFolder, folder))
                    {
                        Tuple<long, int> folderInfo = await GetFolderInfoAsync(folder, _cancellationTokenSource.Token).ConfigureAwait(false);
                        long folderSize = folderInfo.Item1;
                        int fileCount = folderInfo.Item2;

                        // 使用更高级的比较算法
                        if (IsFolderContentSimilar(originalSize, originalFileCount, folderSize, fileCount))
                        {
                            try
                            {
                                Directory.Delete(folder, true);
                                logCallback("信息", $"删除重复文件夹：{folder}", false);
                            }
                            catch (Exception ex)
                            {
                                logCallback("错误", $"删除重复文件夹 {folder} 时出错：{ex.Message}", false);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) when (!(ex is OperationCanceledException))
            {
                logCallback("错误", $"检查重复文件夹时出错: {ex.Message}", false);
            }
        }

        /// <summary>
        /// 判断一个路径是否是另一个路径的子目录
        /// </summary>
        bool IsSubdirectoryOf(string potentialChild, string potentialParent)
        {
            DirectoryInfo di1 = new DirectoryInfo(potentialChild);
            DirectoryInfo di2 = new DirectoryInfo(potentialParent);

            while (di1 != null)
            {
                if (di1.FullName.Equals(di2.FullName, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
                di1 = di1.Parent;
            }
            return false;
        }

        /// <summary>
        /// 更高级的文件夹内容相似度比较
        /// </summary>
        bool IsFolderContentSimilar(long size1, int count1, long size2, int count2)
        {
            // 文件数量必须完全相同
            if (count1 != count2) return false;

            // 如果文件大小完全相同，很可能是相同内容
            if (size1 == size2) return true;

            // 如果文件大小接近（允许1%的误差），考虑为相似
            double sizeRatio = (double)size1 / size2;
            return sizeRatio >= 0.99 && sizeRatio <= 1.01;
        }
    }
}