﻿/*
 * ============================================================================
 * 功能模块：Excel向下填充工具
 * ============================================================================
 * 
 * 模块作用：提供Excel单元格向下填充功能，支持多种填充模式和智能填充策略
 * 
 * 主要功能：
 * - 智能填充：按上一行的值进行向下填充，支持空单元格自动填充
 * - 多列支持：支持多列同时进行向下填充操作
 * - 填充模式：提供三种填充模式（当前单元格下方、筛选行下方、选定单元格）
 * - 视觉标注：对填充的单元格进行颜色标注，便于识别
 * - 标色管理：支持清除填充单元格的颜色标注
 * - 筛选支持：智能识别Excel筛选状态，跳过筛选行
 * 
 * 执行逻辑：
 * 1. 用户选择Excel单元格范围
 * 2. 系统检测筛选状态并更新填充选项
 * 3. 用户选择填充模式（当前下方/筛选行下方/选定范围）
 * 4. 系统按列遍历，识别非空值作为填充源
 * 5. 对空单元格进行智能填充
 * 6. 对填充的单元格进行颜色标注
 * 
 * 注意事项：
 * - 填充过程中会跳过隐藏行
 * - 支持合并单元格的智能处理
 * - 使用快速模式提高大批量数据的处理性能
 * - 提供完整的日志记录和异常处理
 * ============================================================================
 */

using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Windows.Forms;


namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// Excel向下填充工具窗体
    /// 提供智能向下填充功能，支持多种填充模式和视觉标注
    /// </summary>
    /// <remarks>
    /// 此窗体提供以下功能：
    /// 1. 按上一行的值进行向下填充
    /// 2. 支持多列同时填充
    /// 3. 默认对填充的单元格进行标色
    /// 4. 直接在窗体中选择不同的填充模式
    /// </remarks>
    public partial class frm向下填充 : Form
    {
        /// <summary>
        /// 当前选定的单元格范围
        /// 存储用户在Excel中选择的目标填充范围
        /// </summary>
        private Range _selectedRange;

        /// <summary>
        /// Excel工作表的筛选行号
        /// 用于识别筛选状态，确定筛选行下方填充的起始位置
        /// </summary>
        private int _filterRowNumber;

        /// <summary>
        /// 已标记颜色的单元格范围
        /// 用于记录已标色的单元格，便于后续清除颜色标注
        /// </summary>
        private Range _markedRange;

        /// <summary>
        /// 初始化向下填充窗体
        /// </summary>
        public frm向下填充()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 按上一行的值填充按钮点击事件处理程序
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 执行逻辑：
        /// 1. 根据用户选择的填充模式获取目标范围
        /// 2. 启用Excel快速模式提高处理性能
        /// 3. 按列遍历，识别非空值作为填充源
        /// 4. 对空单元格进行智能填充
        /// 5. 对填充的单元格进行颜色标注
        /// 6. 记录操作日志和异常处理
        /// </remarks>
        void button按上一行的值填充_Click(object sender, EventArgs e)
        {
            List<Range> filledCells = []; // 存放已填充单元格的Range列表

            try
            {
                // 根据用户选择的模式获取目标填充范围
                Range targetRange = GetTargetRange();
                if (targetRange == null)
                {
                    return;
                }

                // 获取填充模式描述并记录日志
                string fillMode = GetSelectedFillMode();
                ETLogManager.Info($"开始向下填充操作，填充模式: {fillMode}，目标区域: {targetRange.Address}");
                // 启用Excel快速模式，提高批量处理性能
                ETExcelExtensions.SetAppFastMode();

                // 按列遍历目标范围进行智能填充
                for (int columnIndex = 1; columnIndex <= targetRange.Columns.Count; columnIndex++)
                {
                    dynamic currentColumn = targetRange.Columns[columnIndex];
                    object previousValue = null; // 存储上一个非空值，作为填充源
                    bool isMergedCell = false; // 标记是否在处理连续的合并单元格区域
                    int lastFilledRow = 1; // 最后一个有值单元格的行索引

                    // 遍历当前列的每个单元格
                    foreach (Range cell in currentColumn.Cells)
                    {
                        // 跳过隐藏行，避免填充到不可见的单元格
                        if (cell.EntireRow.Hidden) continue;

                        if (!cell.IsCellEmpty())
                        {
                            // 发现非空单元格，更新填充源值
                            previousValue = cell.Value;
                            isMergedCell = true;
                            lastFilledRow = cell.Row;
                        }
                        else if (cell.IsCellEmpty() && isMergedCell)
                        {
                            // 空单元格且在合并区域内，执行填充
                            cell.Value = previousValue ?? string.Empty;
                            filledCells.Add(cell);
                            ETLogManager.Debug($"填充单元格 {cell.Address} 的值为: {previousValue}");
                        }
                        else if (!cell.IsCellEmpty() && cell.Row != lastFilledRow + 1)
                        {
                            // 非连续的非空单元格，重置合并状态
                            isMergedCell = false;
                            previousValue = string.Empty;
                        }
                    }
                }

                // 对填充的单元格进行颜色标注，便于用户识别
                if (filledCells.Count > 0)
                {
                    // 将所有填充的单元格合并为一个范围
                    Range filledRange = ETExcelExtensions.UnionRanges(filledCells);
                    // 应用紫罗兰色标注
                    filledRange.Format条件格式警示色(EnumWarningColor.紫罗兰);

                    // 记录已标色的范围，用于后续清除标色操作
                    _markedRange = filledRange;

                    ETLogManager.Info($"已为填充的单元格添加标色，范围: {filledRange.Address}");
                }
            }
            catch (Exception ex)
            {
                throw new ETException("向下填充操作失败", "单元格填充操作", ex);
            }
            finally
            {
                // 恢复Excel正常模式并刷新屏幕显示
                ETExcelExtensions.SetAppNormalMode(true);
            }

            // 操作完成后保持窗体打开，便于用户进行其他操作
        }

        /// <summary>
        /// 清除标色按钮点击事件处理程序
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 执行逻辑：
        /// 1. 检查是否存在已标色的单元格范围
        /// 2. 清除已标色单元格的条件格式
        /// 3. 重置标记范围记录
        /// 4. 记录操作日志
        /// </remarks>
        void button清除标色_Click(object sender, EventArgs e)
        {
            try
            {
                if (_markedRange != null)
                {
                    // 清除条件格式
                    _markedRange.FormatConditions.Delete();
                    ETLogManager.Info($"已清除标色范围: {_markedRange.Address}");

                    // 重置标记范围
                    _markedRange = null;
                }
                else
                {
                    ETLogManager.Info("没有找到需要清除的标色范围");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("清除标色失败", ex);
                throw new ETException("清除标色失败", "清除单元格标色", ex);
            }
        }

        /// <summary>
        /// 窗体加载事件处理程序
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        void frm向下填充_Load(object sender, EventArgs e)
        {
            InitializeDialog();
        }

        /// <summary>
        /// 初始化对话框内容和选项设置
        /// </summary>
        /// <remarks>
        /// 执行逻辑：
        /// 1. 获取用户当前选择的Excel单元格范围
        /// 2. 检测工作表的筛选状态
        /// 3. 更新填充选项的描述文本
        /// 4. 根据筛选状态启用或禁用相应选项
        /// </remarks>
        private void InitializeDialog()
        {
            try
            {
                // 获取当前选定范围
                _selectedRange = ETExcelExtensions.GetSelectionRange();
                if (_selectedRange == null)
                {
                    return;
                }

                // 获取筛选行号
                _filterRowNumber = _selectedRange.Worksheet.GetWorksheetFilterRowNumber();

                // 更新选项描述
                UpdateOptionDescriptions();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("初始化向下填充窗体失败", ex);
            }
        }

        /// <summary>
        /// 更新填充选项的描述文本，显示具体的单元格地址和筛选信息
        /// </summary>
        /// <remarks>
        /// 更新内容：
        /// 1. 选项1：显示当前选择的起始单元格地址
        /// 2. 选项2：显示筛选行信息，如果无筛选则禁用该选项
        /// 3. 选项3：显示完整的选定单元格范围地址
        /// 根据筛选状态自动选择默认选项
        /// </remarks>
        private void UpdateOptionDescriptions()
        {
            if (_selectedRange == null) return;

            // 获取选定范围的第一个单元格
            Range firstCell = _selectedRange.Cells[1, 1];
            string cellAddress = firstCell.Address[false, false];

            // 更新选项1描述 - 显示起始单元格
            radioButton填充当前单元格下方.Text = $"填充当前下方 ({cellAddress} 开始)";

            // 更新选项2描述 - 显示筛选行信息
            if (_filterRowNumber > 0)
            {
                radioButton填充筛选行下方.Text = $"填充筛选行下方 (跳过1-{_filterRowNumber}行)";
                radioButton填充筛选行下方.Enabled = true;
                radioButton填充筛选行下方.Checked = true;
            }
            else
            {
                radioButton填充筛选行下方.Text = "填充筛选行下方 (当前无筛选)";
                radioButton填充筛选行下方.Enabled = false;
                radioButton填充当前单元格下方.Checked = true;
            }

            // 更新选项3描述 - 显示选定范围
            radioButton填充选定单元格.Text = $"填充选定单元格 ({_selectedRange.Address[false, false]})";
        }

        /// <summary>
        /// 获取用户选择的填充模式描述文本
        /// </summary>
        /// <returns>填充模式的描述字符串</returns>
        /// <remarks>
        /// 返回值对应：
        /// - "填充当前单元格下方"：从选定单元格开始向下填充
        /// - "填充筛选行下方"：从筛选行下方开始填充
        /// - "填充选定单元格"：仅在选定范围内填充
        /// - "未知模式"：未选择任何选项时的默认值
        /// </remarks>
        private string GetSelectedFillMode()
        {
            if (radioButton填充当前单元格下方.Checked)
                return "填充当前单元格下方";
            else if (radioButton填充筛选行下方.Checked)
                return "填充筛选行下方";
            else if (radioButton填充选定单元格.Checked)
                return "填充选定单元格";
            else
                return "未知模式";
        }

        /// <summary>
        /// 根据用户选择的填充模式计算实际的目标填充范围
        /// </summary>
        /// <returns>计算得出的目标填充范围，如果计算失败则返回null</returns>
        /// <remarks>
        /// 计算逻辑：
        /// 1. 填充当前单元格下方：从选定单元格开始到列底部的范围
        /// 2. 填充筛选行下方：从筛选行下一行开始到工作表使用范围底部
        /// 3. 填充选定单元格：使用用户原始选择的范围
        /// 所有范围都会经过OptimizeRangeSize()优化处理
        /// </remarks>
        private Range GetTargetRange()
        {
            if (_selectedRange == null) return null;

            try
            {
                if (radioButton填充当前单元格下方.Checked)
                {
                    // 填充当前单元格下方单元格：从选定单元格开始到列底部
                    Range firstCell = _selectedRange.Cells[1, 1];
                    Range columnRange = firstCell.EntireColumn;
                    Range currentCellTargetRange = _selectedRange.Worksheet.Range[
                        _selectedRange.Worksheet.Cells[firstCell.Row, firstCell.Column],
                        _selectedRange.Worksheet.Cells[columnRange.Rows.Count, firstCell.Column + _selectedRange.Columns.Count - 1]];
                    return currentCellTargetRange.OptimizeRangeSize();
                }
                else if (radioButton填充筛选行下方.Checked)
                {
                    // 填充筛选行下方单元格：从筛选行下方开始
                    if (_filterRowNumber > 0)
                    {
                        Range filterRowTargetRange = _selectedRange.Worksheet.Range[
                            _selectedRange.Worksheet.Cells[_filterRowNumber + 1, _selectedRange.Column],
                            _selectedRange.Worksheet.Cells[_selectedRange.Worksheet.UsedRange.Rows.Count, _selectedRange.Column + _selectedRange.Columns.Count - 1]];
                        return filterRowTargetRange.OptimizeRangeSize();
                    }
                    return _selectedRange.OptimizeRangeSize();
                }
                else if (radioButton填充选定单元格.Checked)
                {
                    // 填充选定单元格：仅填充用户选定的范围
                    return _selectedRange.OptimizeRangeSize();
                }

                return null;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("计算目标填充范围失败", ex);
                return null;
            }
        }
    }
}