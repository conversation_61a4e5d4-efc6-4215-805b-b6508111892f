﻿using System;
using System.Threading;
using System.Windows.Forms;

namespace HyAssistant
{
    static class Program
    {
        // Mutex唯一标识，建议保持不变
        static Mutex mutex = new Mutex(true, "{E5B60C88-59E7-4F6F-AB7F-2A6AEDD9BECF}");
        public static MainForm mainForm;

        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            bool isNewInstance = false;
            try
            {
                // 尝试获取Mutex
                isNewInstance = mutex.WaitOne(TimeSpan.Zero, true);

                if (!isNewInstance)
                {
                    // 已有实例，弹窗让用户选择
                    DialogResult result = MessageBox.Show("程序已经在运行中！\n是否继续启动另一个实例？", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning);
                    if (result == DialogResult.Cancel)
                    {
                        // 用户选择取消，优雅退出
                        return;
                    }
                    // 用户选择继续，不持有Mutex，允许多开
                }

                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // 设置日志路径
                //string logsPath = Path.Combine(Application.StartupPath, "logs");
                //ETLogManager.LogPath = logsPath;

                mainForm = new MainForm();
                Application.Run(mainForm);
            }
            finally
            {
                // 仅新实例需要释放Mutex
                if (isNewInstance)
                {
                    mutex.ReleaseMutex();
                }
            }
        }
    }
}
