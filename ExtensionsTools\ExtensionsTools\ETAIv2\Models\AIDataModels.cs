/*
 * ========================================
 * ETAIv2 - Excel AI辅助工具库 v2.0
 * ========================================
 *
 * 文件名: AIDataModels.cs
 * 描述: AI数据模型定义，包含数据源配置、数据组、单元格数据等核心数据结构
 * 作者: ETAIv2开发团队
 * 创建时间: 2024
 * 版本: 2.0.0
 *
 * 主要功能:
 * - 定义AI处理的数据源配置结构
 * - 定义数据组和单元格数据模型
 * - 定义文件数据处理模型
 * - 定义处理进度信息模型
 *
 * 依赖项:
 * - Microsoft.Office.Interop.Excel (Excel COM互操作)
 * - System.Collections.Generic (集合类型)
 *
 * ========================================
 */

using System;
using System.Collections.Generic;
using Microsoft.Office.Interop.Excel;

namespace ET.ETAIv2.Models
{
    /// <summary>
    /// AI数据源配置类
    /// 定义AI处理所需的所有配置信息，包括Excel区域、处理模式、模型配置等
    /// </summary>
    /// <remarks>
    /// 这个类是ETAIv2处理流程的核心配置载体，包含了从Excel数据提取到AI处理再到结果回填的所有必要信息
    /// </remarks>
    public class AIDataSourceConfig
    {
        /// <summary>
        /// 数据源区域 - 包含原始数据的Excel区域
        /// </summary>
        /// <value>Excel Range对象，指向包含待分析数据的单元格区域</value>
        public Range SourceRange { get; set; }

        /// <summary>
        /// 回填区域 - AI分析结果将写入的Excel区域
        /// </summary>
        /// <value>Excel Range对象，指向AI处理结果的目标单元格区域</value>
        public Range TargetRange { get; set; }

        /// <summary>
        /// 提示词区域 - 包含列级提示词的Excel区域
        /// </summary>
        /// <value>Excel Range对象，包含针对每列的特定AI指令</value>
        public Range PromptRange { get; set; }

        /// <summary>
        /// 文件来源区域 - 包含文件路径的Excel区域（可选）
        /// </summary>
        /// <value>Excel Range对象，包含需要AI分析的文件路径</value>
        public Range FileRange { get; set; }

        /// <summary>
        /// 数据处理模式 - 指定按行还是按列进行数据分组
        /// </summary>
        /// <value>DataSourceMode枚举值，决定数据的分组策略</value>
        public DataSourceMode Mode { get; set; }

        /// <summary>
        /// 全局提示词 - 应用于所有数据处理的通用AI指令
        /// </summary>
        /// <value>字符串形式的全局AI指令，从规则文件中加载</value>
        public string GlobalPrompt { get; set; }

        /// <summary>
        /// AI模型配置 - 包含API密钥、模型参数等配置信息
        /// </summary>
        /// <value>AIModelConfig对象，包含完整的AI模型配置</value>
        public AIModelConfig ModelConfig { get; set; }

        /// <summary>
        /// 文件处理模式 - 指定文件是上传到OpenAI还是本地读取
        /// </summary>
        /// <value>FileProcessingMode枚举值，决定文件处理策略</value>
        public FileProcessingMode FileProcessingMode { get; set; }

        /// <summary>
        /// 是否回填null值 - 控制是否将null结果写入Excel
        /// </summary>
        /// <value>布尔值，true表示回填null值，false表示跳过null值</value>
        public bool FillNullValues { get; set; }
    }

    /// <summary>
    /// 数据源处理模式枚举
    /// 定义AI处理数据时的分组策略
    /// </summary>
    /// <remarks>
    /// 不同的模式会影响数据的分组方式和AI处理的逻辑：
    /// - 行模式适合处理记录型数据，每行代表一个完整的数据实体
    /// - 列模式适合处理属性型数据，每列代表一个数据维度
    /// </remarks>
    public enum DataSourceMode
    {
        /// <summary>
        /// 按行处理模式
        /// 将Excel数据按行分组，每行作为一个独立的数据组进行AI分析
        /// 适用场景：每行代表一条记录，如客户信息、订单数据等
        /// </summary>
        ByRow,

        /// <summary>
        /// 按列处理模式
        /// 将Excel数据按列分组，每列作为一个独立的数据组进行AI分析
        /// 适用场景：每列代表一个属性维度，如时间序列数据、统计分析等
        /// </summary>
        ByColumn
    }

    /// <summary>
    /// 文件处理模式枚举
    /// 定义如何处理Excel中引用的外部文件
    /// </summary>
    /// <remarks>
    /// 不同的处理模式有不同的优缺点：
    /// - 本地读取：读取文件内容并嵌入到AI消息中，与对应行/列数据整合
    /// - 忽略文件：不处理文件内容，仅保留文件路径信息
    /// </remarks>
    public enum FileProcessingMode
    {
        /// <summary>
        /// 本地读取文件内容
        /// 在本地提取文件文本内容，然后嵌入到对应的数据组消息中
        /// 优点：文件内容与数据直接关联，处理速度快，数据隐私性好
        /// 缺点：文本提取质量依赖本地实现，复杂文档支持有限
        /// </summary>
        ReadLocally,

        /// <summary>
        /// 忽略文件内容
        /// 不读取和处理文件内容，仅保留文件路径信息
        /// 优点：处理速度最快，不受文件大小限制
        /// 缺点：无法利用文件中的信息进行AI分析
        /// </summary>
        IgnoreFiles
    }

    /// <summary>
    /// AI数据组类
    /// 表示一组相关的Excel数据，是AI处理的基本单位
    /// </summary>
    /// <remarks>
    /// 数据组是ETAIv2处理流程中的核心概念，每个数据组包含：
    /// - 源数据：需要AI分析的原始Excel数据
    /// - 目标单元格：AI分析结果的写入位置
    /// - 文件数据：相关的外部文件（如PDF、Word文档等）
    /// - 列级提示词：针对特定列的AI指令
    ///
    /// 根据DataSourceMode的不同，数据组可能代表一行数据或一列数据
    /// </remarks>
    public class AIDataGroup
    {
        /// <summary>
        /// 数据组唯一标识符
        /// </summary>
        /// <value>字符串形式的组ID，用于跟踪和调试，格式如"row_1"或"col_A"</value>
        public string GroupId { get; set; }

        /// <summary>
        /// 源数据单元格列表
        /// 包含需要AI分析的原始Excel单元格数据
        /// </summary>
        /// <value>CellData对象列表，每个对象包含单元格的地址、值、类型等信息</value>
        public List<CellData> SourceCells { get; set; }

        /// <summary>
        /// 目标单元格列表
        /// AI分析结果将写入这些单元格
        /// </summary>
        /// <value>CellData对象列表，定义了结果回填的目标位置</value>
        public List<CellData> TargetCells { get; set; }

        /// <summary>
        /// 关联的文件数据列表
        /// 包含与此数据组相关的外部文件信息
        /// </summary>
        /// <value>FileData对象列表，可能包含PDF、Word、文本文件等</value>
        public List<FileData> Files { get; set; }

        /// <summary>
        /// 列级提示词字典
        /// 为特定列定义的AI处理指令
        /// </summary>
        /// <value>字典，键为列标识（如"A"、"B"），值为对应的提示词内容</value>
        public Dictionary<string, string> ColumnPrompts { get; set; }

        /// <summary>
        /// 构造函数
        /// 初始化所有集合属性为空集合
        /// </summary>
        public AIDataGroup()
        {
            SourceCells = new List<CellData>();
            TargetCells = new List<CellData>();
            Files = new List<FileData>();
            ColumnPrompts = new Dictionary<string, string>();
        }
    }

    /// <summary>
    /// Excel单元格数据类
    /// 封装单个Excel单元格的完整信息
    /// </summary>
    /// <remarks>
    /// 这个类提供了Excel单元格的完整描述，包括位置、内容、类型等信息。
    /// 支持多种数据类型的识别和处理，特别是文件路径的自动识别。
    /// </remarks>
    public class CellData
    {
        /// <summary>
        /// Excel单元格地址
        /// </summary>
        /// <value>标准Excel地址格式，如"A1"、"B5"等</value>
        /// <example>"A1", "B5", "AA100"</example>
        public string Address { get; set; }

        /// <summary>
        /// 单元格的原始值
        /// </summary>
        /// <value>可以是字符串、数字、日期、布尔值等任意类型</value>
        /// <remarks>保持原始类型，便于后续的类型转换和处理</remarks>
        public object Value { get; set; }

        /// <summary>
        /// 数据类型标识
        /// </summary>
        /// <value>数据类型字符串，如"text"、"number"、"date"、"file"等</value>
        /// <seealso cref="ET.ETAIv2.Constants.ExcelConstants.DataTypes"/>
        public string DataType { get; set; }

        /// <summary>
        /// 是否为文件路径标识
        /// </summary>
        /// <value>true表示该单元格包含文件路径，false表示普通数据</value>
        /// <remarks>用于快速识别需要进行文件处理的单元格</remarks>
        public bool IsFile { get; set; }

        /// <summary>
        /// Excel行号（从1开始）
        /// </summary>
        /// <value>Excel中的行号，与Excel界面显示一致</value>
        public int Row { get; set; }

        /// <summary>
        /// Excel列号（从1开始）
        /// </summary>
        /// <value>Excel中的列号，A列=1，B列=2，以此类推</value>
        public int Column { get; set; }
    }

    /// <summary>
    /// 文件数据类 - 简化版本，专注于本地文件处理
    /// 封装文件的完整信息和处理状态
    /// </summary>
    /// <remarks>
    /// 这个类支持本地文件处理模式：
    /// 1. 本地读取：在本地提取文件内容，嵌入到AI消息中
    /// 2. 忽略文件：仅保留文件路径信息，不处理内容
    ///
    /// 支持的文件格式包括：PDF、Word、文本文件、HTML等
    /// </remarks>
    public class FileData
    {
        /// <summary>
        /// 文件的完整路径
        /// </summary>
        /// <value>文件在本地文件系统中的绝对或相对路径</value>
        /// <example>@"C:\Documents\report.pdf"</example>
        public string FilePath { get; set; }

        /// <summary>
        /// 文件名（不包含路径）
        /// </summary>
        /// <value>纯文件名，包含扩展名</value>
        /// <example>"report.pdf", "data.xlsx"</example>
        public string FileName { get; set; }

        /// <summary>
        /// 文件类型（扩展名）
        /// </summary>
        /// <value>小写的文件扩展名，包含点号</value>
        /// <example>".pdf", ".docx", ".txt"</example>
        public string FileType { get; set; }

        /// <summary>
        /// 文件内容（本地读取模式时使用）
        /// </summary>
        /// <value>提取的文本内容，在ReadLocally模式下有值，IgnoreFiles模式下为null</value>
        /// <remarks>文件内容将嵌入到对应数据组的AI消息中</remarks>
        public string Content { get; set; }

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        /// <value>文件的字节大小，用于验证是否超过处理限制</value>
        public long FileSize { get; set; }
    }

    /// <summary>
    /// 处理进度信息类
    /// 用于向用户界面报告AI处理的实时进度
    /// </summary>
    /// <remarks>
    /// 这个类提供了丰富的进度信息，包括：
    /// - 百分比进度：便于显示进度条
    /// - 文字描述：便于用户理解当前操作
    /// - 分组信息：便于跟踪批处理进度
    /// - 时间戳：便于性能分析和调试
    ///
    /// 支持两种构造方式：简单进度和详细分组进度
    /// </remarks>
    public class ProcessingProgress
    {
        /// <summary>
        /// 进度描述消息
        /// </summary>
        /// <value>描述当前处理状态的文字信息</value>
        /// <example>"正在提取数据...", "正在调用AI服务...", "正在回填结果..."</example>
        public string Message { get; set; }

        /// <summary>
        /// 完成百分比（0-100）
        /// </summary>
        /// <value>整数形式的完成百分比，用于进度条显示</value>
        /// <remarks>取值范围：0-100，其中100表示完全完成</remarks>
        public int Percentage { get; set; }

        /// <summary>
        /// 进度更新时间戳
        /// </summary>
        /// <value>进度信息创建的本地时间</value>
        /// <remarks>用于性能分析和调试，自动设置为当前时间</remarks>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 当前处理的数据组标识
        /// </summary>
        /// <value>正在处理的数据组ID，如"row_5"或"batch_2"</value>
        /// <remarks>在批处理模式下特别有用，可以为空</remarks>
        public string CurrentGroup { get; set; }

        /// <summary>
        /// 总数据组数量
        /// </summary>
        /// <value>需要处理的数据组总数</value>
        /// <remarks>用于计算整体进度，在批处理模式下有效</remarks>
        public int TotalGroups { get; set; }

        /// <summary>
        /// 已完成的数据组数量
        /// </summary>
        /// <value>已经处理完成的数据组数量</value>
        /// <remarks>用于计算剩余工作量，在批处理模式下有效</remarks>
        public int CompletedGroups { get; set; }

        /// <summary>
        /// 基础构造函数
        /// 创建简单的进度信息
        /// </summary>
        /// <param name="message">进度描述消息</param>
        /// <param name="percentage">完成百分比（0-100）</param>
        public ProcessingProgress(string message, int percentage)
        {
            Message = message;
            Percentage = percentage;
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// 详细构造函数
        /// 创建包含分组信息的详细进度信息
        /// </summary>
        /// <param name="message">进度描述消息</param>
        /// <param name="percentage">完成百分比（0-100）</param>
        /// <param name="currentGroup">当前处理的组标识</param>
        /// <param name="completedGroups">已完成的组数量</param>
        /// <param name="totalGroups">总组数量</param>
        public ProcessingProgress(string message, int percentage, string currentGroup, int completedGroups, int totalGroups)
            : this(message, percentage)
        {
            CurrentGroup = currentGroup;
            CompletedGroups = completedGroups;
            TotalGroups = totalGroups;
        }
    }
}
