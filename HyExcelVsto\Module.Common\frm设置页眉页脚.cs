﻿/*
 * ============================================================================
 * 功能模块：Excel页眉页脚设置工具
 * ============================================================================
 * 
 * 模块作用：为Excel工作表提供页眉页脚的快速设置功能
 * 
 * 主要功能：
 * - 页眉设置：设置工作表的左、中、右页眉内容
 * - 页脚设置：设置工作表的左、中、右页脚内容
 * - 批量应用：支持对多个选中工作表同时设置
 * - 历史记录：保存常用的页眉页脚内容供快速选择
 * - 默认模板：提供常用的页眉页脚模板
 * - 日期自动化：自动生成当前日期格式
 * 
 * 执行逻辑：
 * 1. 加载窗体并初始化默认内容
 * 2. 绑定ComboBox历史记录功能
 * 3. 用户输入或选择页眉页脚内容
 * 4. 确认后批量应用到选中的工作表
 * 5. 保存用户输入到历史记录
 * 
 * 注意事项：
 * - 支持Excel页眉页脚的特殊格式代码
 * - 自动保存用户的输入历史
 * - 提供常用的页眉页脚模板
 * ============================================================================
 */

using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Windows.Forms;

using Excel = Microsoft.Office.Interop.Excel;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// Excel工作表页眉页脚设置工具窗体
    /// </summary>
    /// <remarks>
    /// 提供页眉页脚的快速设置功能，支持批量应用和历史记录
    /// </remarks>
    public partial class frm设置页眉页脚 : Form
    {
        /// <summary>
        /// Excel应用程序实例
        /// </summary>
        /// <remarks>用于访问Excel的页眉页脚设置功能</remarks>
        public Excel.Application XlApp;

        /// <summary>
        /// 初始化页眉页脚设置窗体
        /// </summary>
        /// <remarks>初始化组件并获取Excel应用程序实例</remarks>
        public frm设置页眉页脚()
        {
            InitializeComponent();
            XlApp = Globals.ThisAddIn.Application;
        }

        /// <summary>
        /// 窗体加载事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <exception cref="ETException">初始化失败时抛出</exception>
        /// <remarks>
        /// 执行逻辑：绑定历史记录 → 绑定常用值 → 设置默认模板
        /// </remarks>
        private void frm设置页眉页脚_Load(object sender, EventArgs e)
        {
            try
            {
                // 为所有页眉页脚ComboBox绑定历史记录功能
                ETForm.BindComboBox(comboBox页眉左);
                ETForm.BindComboBox(comboBox页眉中);
                ETForm.BindComboBox(comboBox页眉右);
                ETForm.BindComboBox(comboBox页脚左);
                ETForm.BindComboBox(comboBox页脚中);
                ETForm.BindComboBox(comboBox页脚右);

                // 绑定常用值文本框
                ETForm.BindTextBox(textBox常用值);

                // 设置默认页脚模板（如果ComboBox为空）
                if (string.IsNullOrEmpty(comboBox页脚左.Text))
                    comboBox页脚左.Text = @"设计负责人:xxx";
                if (string.IsNullOrEmpty(comboBox页脚中.Text))
                    comboBox页脚中.Text = @"审核:xxx                    编制:xxx";
                if (string.IsNullOrEmpty(comboBox页脚右.Text))
                    comboBox页脚右.Text = $@"编制日期:{DateTime.Now.ToString("yyyy年M月")}";
            }
            catch (Exception ex)
            {
                throw new ETException("初始化页眉页脚窗体失败", "窗体初始化", ex);
            }
        }

        /// <summary>
        /// 设置页眉页脚按钮点击事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <exception cref="ETException">设置页眉页脚失败时抛出</exception>
        /// <remarks>
        /// 执行逻辑：确认操作 → 遍历选中工作表 → 应用设置 → 关闭窗体
        /// </remarks>
        private void button设置页眉页脚_Click(object sender, EventArgs e)
        {
            try
            {
                // 确认用户操作
                DialogResult userConfirmation = MessageBox.Show(@"确定要设置页眉页码?", @"提示", MessageBoxButtons.YesNo);
                if (userConfirmation != DialogResult.Yes) 
                    return;

                // 遍历所有选中的工作表并应用设置
                foreach (Worksheet worksheet in XlApp.ActiveWindow.SelectedSheets)
                {
                    try
                    {
                        ApplyHeaderFooterToWorksheet(worksheet);
                    }
                    catch (Exception sheetEx)
                    {
                        throw new ETException($"设置工作表 {worksheet.Name} 的页眉页脚失败", "页眉页脚设置", sheetEx);
                    }
                }

                // 关闭窗体并释放资源
                Close();
                Dispose();
            }
            catch (Exception ex)
            {
                throw new ETException("设置页眉页脚失败", "页眉页脚设置", ex);
            }
        }

        /// <summary>
        /// 为指定工作表应用页眉页脚设置
        /// </summary>
        /// <param name="worksheet">目标工作表</param>
        /// <exception cref="ETException">设置页眉页脚属性失败时抛出</exception>
        /// <remarks>
        /// 将用户输入的页眉页脚内容应用到工作表的PageSetup属性
        /// </remarks>
        private void ApplyHeaderFooterToWorksheet(Worksheet worksheet)
        {
            try
            {
                // 设置页脚内容
                worksheet.PageSetup.LeftFooter = comboBox页脚左.Text;
                worksheet.PageSetup.CenterFooter = comboBox页脚中.Text;
                worksheet.PageSetup.RightFooter = comboBox页脚右.Text;

                // 设置页眉内容
                worksheet.PageSetup.LeftHeader = comboBox页眉左.Text;
                worksheet.PageSetup.CenterHeader = comboBox页眉中.Text;
                worksheet.PageSetup.RightHeader = comboBox页眉右.Text;
            }
            catch (Exception ex)
            {
                throw new ETException("设置页眉页脚属性失败", "页眉页脚设置", ex);
            }
        }
    }
}