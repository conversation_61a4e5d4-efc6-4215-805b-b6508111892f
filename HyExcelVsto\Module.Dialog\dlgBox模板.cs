﻿/*
 * ============================================================================
 * 功能模块：对话框模板
 * ============================================================================
 * 
 * 模块作用：提供标准对话框的基础模板
 * 
 * 主要功能：
 * - 对话框基础框架：提供标准的对话框窗体模板
 * - 组件初始化：初始化对话框的基本组件
 * 
 * 注意事项：
 * - 这是一个基础模板类，可用于创建其他对话框
 * - 继承自Windows Forms的Form类
 * ============================================================================
 */

using System.Windows.Forms;

namespace HyExcelVsto.Module.Dialog
{
    /// <summary>
    /// 对话框模板类
    /// </summary>
    /// <remarks>
    /// 提供标准对话框的基础模板，可用于创建其他对话框
    /// </remarks>
    public partial class dlgBox模板 : Form
    {
        /// <summary>
        /// 初始化对话框模板
        /// </summary>
        /// <remarks>初始化对话框的基本组件</remarks>
        public dlgBox模板()
        {
            InitializeComponent();
        }
    }
}