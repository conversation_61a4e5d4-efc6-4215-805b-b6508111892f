﻿/*
 * ============================================================================
 * 功能模块：Excel单元格复制及合并操作
 * ============================================================================
 * 
 * 模块作用：提供Excel单元格内容的复制、合并及特殊格式化功能，支持多种分隔符和GPS坐标处理
 * 
 * 主要功能：
 * - 单元格内容复制：支持单个或多个单元格内容的复制操作
 * - 内容合并：使用指定分隔符将多个单元格内容合并为一个字符串
 * - 唯一值提取：可选择是否提取重复值，确保结果的唯一性
 * - GPS坐标处理：支持GPS坐标的提取和格式化，包括经纬度顺序调整
 * - 公式生成：支持生成Excel公式格式的字符串（带括号）
 * - 分隔符管理：支持多种分隔符选择和自定义分隔符保存
 * 
 * 执行逻辑：
 * 1. 获取用户选中的Excel单元格范围
 * 2. 根据按钮类型执行不同的处理逻辑（普通合并、GPS处理、公式生成等）
 * 3. 提取单元格内容并根据设置去重或保留重复值
 * 4. 使用指定分隔符连接内容并添加前后缀
 * 5. 将处理结果复制到系统剪贴板
 * 
 * 注意事项：
 * - GPS处理限制选中单元格数量不超过100个
 * - 支持两种GPS坐标格式：经度在前和纬度在前
 * - 窗体关闭时自动保存用户自定义的分隔符
 * - 支持"不关闭窗体"选项便于连续操作
 * ============================================================================
 */

using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Windows.Forms;
using Button = System.Windows.Forms.Button;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// 复制及合并操作窗体
    /// 提供Excel单元格内容的复制、合并及特殊格式化功能
    /// </summary>
    public partial class frm复制及合并 : Form
    {
        /// <summary>
        /// 处理添加字符按钮点击事件
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        void button添加字符_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取当前选择的Excel范围
                Range selectionRange = ETExcelExtensions.GetSelectionRange();
                if (selectionRange == null)
                {
                    MessageBox.Show("请先选择Excel单元格范围");
                    return;
                }

                string currentButtonText = ((Button)sender).Text;
                ProcessButtonClick(currentButtonText, selectionRange);

                if (!checkBoxNoClose.Checked)
                    Close();
            }
            catch (Exception ex)
            {
                throw new ETException("处理添加字符时发生错误", "单元格内容操作", ex);
            }
        }

        /// <summary>
        /// 处理不同按钮的点击逻辑
        /// </summary>
        /// <param name="buttonText">按钮文本</param>
        /// <param name="selectionRange">选中的Excel范围</param>
        void ProcessButtonClick(string buttonText, Range selectionRange)
        {
            switch (buttonText)
            {
                case @"确定":
                    WriteClipboard(string.Empty, string.Empty);
                    break;

                case "+":
                    SetupPlusOperation();
                    break;

                case "= +":
                    SetupFormulaOperation();
                    break;

                case "gps 116,23":
                case "gps 23,116":
                    ProcessGpsOperation(selectionRange, buttonText);
                    break;

                default:
                    comboBoxSplit.Text = buttonText;
                    WriteClipboard(string.Empty, string.Empty);
                    break;
            }
        }

        /// <summary>
        /// 设置加号操作的相关配置
        /// </summary>
        void SetupPlusOperation()
        {
            comboBoxSplit.Text = "+";
            checkBox提取唯一值.Checked = false;
            WriteClipboard(string.Empty, string.Empty);
        }

        /// <summary>
        /// 设置公式操作的相关配置
        /// </summary>
        void SetupFormulaOperation()
        {
            comboBoxSplit.Text = "+";
            checkBox提取唯一值.Checked = false;
            WriteClipboard("=(", ")");
        }

        /// <summary>
        /// 处理GPS坐标相关操作
        /// </summary>
        /// <param name="selectionRange">选中的Excel范围</param>
        /// <param name="buttonText">按钮文本</param>
        void ProcessGpsOperation(Range selectionRange, string buttonText)
        {
            try
            {
                if (selectionRange.Cells.Count > 100)
                {
                    MessageBox.Show("选中单元格数量过多，请少于100个单元格");
                    return;
                }

                XlGpsPoint gpsInfo = ETGPS.ExtractGps(selectionRange);
                if (gpsInfo == null)
                {
                    MessageBox.Show("无法提取GPS信息");
                    return;
                }

                string siteName = ExtractSiteName(selectionRange);
                string formattedGps = FormatGpsCoordinates(gpsInfo, buttonText, siteName);
                Clipboard.SetText(formattedGps);
            }
            catch (Exception ex)
            {
                throw new ETException("处理GPS坐标时发生错误", "GPS数据处理", ex);
            }
        }

        /// <summary>
        /// 从选中范围中提取站点名称
        /// </summary>
        /// <param name="selectionRange">选中的Excel范围</param>
        /// <returns>站点名称，如果没有则返回空字符串</returns>
        string ExtractSiteName(Range selectionRange)
        {
            foreach (Range cell in selectionRange.Cells)
            {
                if (!cell.IsCellEmpty() && !cell.IsCellNumber())
                {
                    return cell.Value.ToString() + ":";
                }
            }
            return string.Empty;
        }

        /// <summary>
        /// 格式化GPS坐标
        /// </summary>
        /// <param name="gpsInfo">GPS坐标信息</param>
        /// <param name="format">格式类型</param>
        /// <param name="siteName">站点名称</param>
        /// <returns>格式化后的GPS坐标字符串</returns>
        string FormatGpsCoordinates(XlGpsPoint gpsInfo, string format, string siteName)
        {
            return format == "gps 116,23"
                ? $"{siteName}{gpsInfo.Longitude},{gpsInfo.Latitude}"
                : $"{siteName}{gpsInfo.Latitude},{gpsInfo.Longitude}";
        }

        /// <summary>
        /// 将选中内容写入剪贴板
        /// </summary>
        /// <param name="prefix">前缀</param>
        /// <param name="suffix">后缀</param>
        void WriteClipboard(string prefix, string suffix)
        {
            try
            {
                // 获取选定的单元格范围
                Range selectedRange = ETExcelExtensions.GetSelectionRange();
                if (selectedRange == null)
                {
                    MessageBox.Show("请先选择Excel单元格范围");
                    return;
                }

                // 处理单个单元格的情况
                if (selectedRange.Cells.Count == 1)
                {
                    if (!selectedRange.IsCellEmpty())
                        Clipboard.SetText(selectedRange.Value);
                    return;
                }

                // 提取并处理单元格内容
                ProcessMultipleCells(selectedRange, prefix, suffix);
            }
            catch (Exception ex)
            {
                throw new ETException("写入剪贴板时发生错误", "剪贴板数据处理", ex);
            }
        }

        /// <summary>
        /// 处理多个单元格的内容并写入剪贴板
        /// </summary>
        /// <param name="selectedRange">选中的Excel范围</param>
        /// <param name="prefix">前缀</param>
        /// <param name="suffix">后缀</param>
        void ProcessMultipleCells(Range selectedRange, string prefix, string suffix)
        {
            // 提取不重复的单元格文本列表
            List<string> uniqueValues = selectedRange.ConvertRangeToStringList(checkBox提取唯一值.Checked);
            if (uniqueValues == null || uniqueValues.Count == 0)
            {
                MessageBox.Show(@"无法提取到内容");
                return;
            }

            // 连接单元格内容，添加前缀和后缀
            string separatorChar = comboBoxSplit.Text.ConvertToSplitChar();
            string joinedText = string.Join(separatorChar, uniqueValues);
            string finalOutput = $"{prefix}{joinedText}{suffix}".Trim();

            // 将结果复制到剪贴板
            Clipboard.SetText(finalOutput);
        }

        #region 窗体事件处理

        /// <summary>
        /// 窗体构造函数
        /// </summary>
        public frm复制及合并()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 窗体关闭时保存设置
        /// </summary>
        void dlgBox复制_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(comboBoxSplit.Text))
                {
                    GlobalSettings.CopyDialogSeparators.Add(comboBoxSplit.Text);
                }
            }
            catch (Exception ex)
            {
                throw new ETException("保存窗体设置时发生错误", "配置保存操作", ex);
            }
        }

        /// <summary>
        /// 窗体加载时初始化设置
        /// </summary>
        void dlgBox复制_Load(object sender, EventArgs e)
        {
            try
            {
                // 加载分隔符选项
                comboBoxSplit.Items.Clear();
                foreach (string separator in GlobalSettings.CopyDialogSeparators)
                {
                    comboBoxSplit.Items.Add(separator);
                }

                ETForm.BindWindowsFormControl(comboBoxSplit, ThisAddIn.ConfigurationSettings, "设置", "str复制字符串分隔符");
                ETForm.BindWindowsFormControl(checkBox提取唯一值, ThisAddIn.ConfigurationSettings, "设置", "bool复制字符串提取唯一值");
            }
            catch (Exception ex)
            {
                throw new ETException("加载窗体设置时发生错误", "配置加载操作", ex);
            }
        }

        /// <summary>
        /// 显示更多功能按钮点击事件
        /// </summary>
        void button弹出更多功能_Click(object sender, EventArgs e)
        {
            Height = 108;
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        void buttonCancel_Click(object sender, EventArgs e)
        {
            Close();
        }

        #endregion
    }
}