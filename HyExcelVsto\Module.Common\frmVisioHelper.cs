﻿/*
 * ============================================================================
 * 功能模块：Visio辅助工具窗体
 * ============================================================================
 * 
 * 模块作用：提供Visio图纸处理的综合辅助功能，包括图衔读写、PDF转换、文本说明更新等
 * 
 * 主要功能：
 * - 图衔信息读取：从Visio图纸中提取图衔信息
 * - 图衔信息写入：批量更新Visio图纸的图衔内容
 * - PDF转换：将Visio文件批量转换为PDF格式
 * - 文本说明更新：批量更新图纸中的说明文本内容
 * - 图纸检查：验证图纸内容的完整性和规范性
 * 
 * 执行逻辑：
 * 1. 界面初始化：加载配置设置，绑定控件状态
 * 2. 功能选择：通过Tab页切换不同的处理功能
 * 3. 参数配置：设置输入输出路径、处理选项等
 * 4. 批量执行：调用VisioHelperFunctions进行实际处理
 * 5. 进度监控：实时显示处理进度和错误信息
 * 
 * 注意事项：
 * - 处理前会检查Visio进程状态，避免冲突
 * - 支持2003版本Visio的兼容性保存
 * - 所有批量操作都支持多线程处理
 * ============================================================================
 */

using ET;
using System;
using System.Windows.Forms;
using Range = Microsoft.Office.Interop.Excel.Range;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// Visio辅助工具主窗体
    /// </summary>
    /// <remarks>
    /// 提供Visio图纸的批量处理功能，包括图衔管理、PDF转换、文本更新等
    /// 支持多种Visio操作的图形化界面和批量处理能力
    /// </remarks>
    public partial class frmVisioHelper : Form
    {
        /// <summary>
        /// 是否允许保存为2003版本的Visio格式
        /// </summary>
        /// <remarks>用于兼容旧版本Visio文件的保存需求</remarks>
        public bool 允许保存为2003版本visio;

        #region 执行

        /// <summary>
        /// 执行仅读取图衔信息的操作
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 从指定的Visio文件中读取图衔信息，不进行修改操作
        /// 读取结果会显示在进度文本框中
        /// </remarks>
        private void button执行_读取图衔_Click(object sender, EventArgs e)
        { VisioHelperFunctions.执行_仅读取图衔(ucERS文件路径_仅读取图衔.SelectedRange, comboBox图衔名称.Text, textBoxProgress); }

        /// <summary>
        /// 执行批量写入图衔信息的操作
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 根据Excel数据批量更新Visio图纸的图衔信息
        /// 支持多种图衔处理选项：比例修改、内容删除、图号处理等
        /// 使用多线程处理提高批量操作效率
        /// </remarks>
        private void button执行_写入图衔_Click(object sender, EventArgs e)
        {
            VisioHelperFunctions.写入图衔PerRowsThread(
                ucERS文件路径_图衔信息.SelectedRange,
                ucERS页面名称_图衔信息.SelectedRange,
                ds输出路径_图衔信息.Text,
                checkBox直接保存原文件_图衔信息.Checked,
                ucERS图纸名称_写图衔.SelectedRange,
                ucERS单位比例_写图衔.SelectedRange,
                ucERS出图日期_写图衔.SelectedRange,
                ucERS图纸编号_写图衔.SelectedRange,
                fs图衔文件_写图衔.Text,
                comboBox图衔名称.Text,
                textBox修改比例值_写图衔.Text,
                checkBox根据关键字改比例_写图衔.Checked,
                checkBox删除图衔左侧内容_写图衔.Checked,
                checkBox删除图衔图号_写图衔.Checked,
                textBoxProgress,
                textBoxError);
        }

        /// <summary>
        /// 执行Visio文件批量转换为PDF的操作
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 将指定的Visio文件批量转换为PDF格式
        /// 支持路径优化和多线程处理，提高转换效率
        /// </remarks>
        private void button执行_转换为PDF_Click(object sender, EventArgs e)
        {
            // 优化文件路径范围，提高处理效率
            Range filePathRange = ucERS文件路径_转换为PDF.SelectedRange?.OptimizeRangeSize();

            VisioHelperFunctions.VisioToPdfPerRowsThread(
                ds输出路径_转换为PDF.Text,
                ds原根路径_转换为PDF.Text,
                filePathRange,
                textBoxProgress,
                textBoxError);
        }

        /// <summary>
        /// 执行批量更新图纸说明文本的操作
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 根据Excel数据批量更新Visio图纸中的说明文本
        /// 支持铁塔站名、经纬度地址、安全风险点、说明等多种文本内容的更新
        /// 使用多线程处理提高批量操作效率
        /// </remarks>
        private void button执行_更新说明_Click(object sender, EventArgs e)
        {
            VisioHelperFunctions.操作说明文本PerRowsThread(
                ucERS文件路径_文本说明.SelectedRange,
                ucERS页面名称_文本说明.SelectedRange,
                ds输出路径_文本说明.Text,
                checkBox直接保存原文件_文本说明.Checked,
                ucERS铁塔站名编码_读_文本说明.SelectedRange,
                ucERS经纬度及地址_读_文本说明.SelectedRange,
                ucERS安全风险点_读_文本说明.SelectedRange,
                ucERS说明_读_文本说明.SelectedRange,
                ucERS铁塔站名编码_写_文本说明.SelectedRange,
                ucERS经纬度及地址_写_文本说明.SelectedRange,
                ucERS安全风险点_写_文本说明.SelectedRange,
                ucERS说明_写_文本说明.SelectedRange,
                textBoxProgress,
                textBoxError);
        }

        /// <summary>
        /// 执行图纸检查操作
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>预留功能，用于检查图纸内容的完整性和规范性</remarks>
        private void button执行_检查图纸_Click(object sender, EventArgs e)
        {
        }

        /// <summary>
        /// 执行读取信息操作
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>预留功能，用于从图纸中读取特定信息</remarks>
        private void button执行_读取信息_Click(object sender, EventArgs e)
        {
        }

        /// <summary>
        /// 执行其它操作
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>预留功能，用于扩展其它Visio处理功能</remarks>
        private void button执行_其它_Click(object sender, EventArgs e)
        {
        }

        #endregion 执行

        #region 界面控制

        /// <summary>
        /// 初始化frmVisioHelper窗体
        /// </summary>
        /// <remarks>调用InitializeComponent初始化界面组件</remarks>
        public frmVisioHelper()
        { InitializeComponent(); }

        /// <summary>
        /// 窗体加载事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 执行逻辑：检查Visio进程 → 加载配置设置 → 绑定控件状态 → 初始化界面
        /// 确保Visio环境正常，避免多进程冲突
        /// </remarks>
        private void frmHelper_Load(object sender, EventArgs e)
        {
            // 检查Visio进程状态，避免多进程冲突
            if (ETVisio.IsVisioThreadRunning())
            {
                bool result = ETExcelExtensions.VerifyCode("Visio进程正在运行，多个Visio运行将影响代码正确运行，是否强制结束");
                if (result)
                    ETVisio.KillVisioApplication();
            }

            // 加载Visio版本兼容性配置
            允许保存为2003版本visio = ThisAddIn.ConfigurationSettings.GetValue("Visio辅助", "允许保存为2003版本visio", "false") == "true";
            //ETVisio.允许保存为2003版本visio = 允许保存为2003版本visio;

            // 绑定各种路径和选项控件到配置系统
            ETForm.BindWindowsFormControl(fs图衔文件_写图衔, ThisAddIn.ConfigurationSettings, "Visio辅助", "fs图衔文件_替换图衔");
            ETForm.BindWindowsFormControl(ds输出路径_图衔信息, ThisAddIn.ConfigurationSettings, "Visio辅助", "fs输出路径_图衔信息");
            ETForm.BindWindowsFormControl(ds输出路径_文本说明, ThisAddIn.ConfigurationSettings, "Visio辅助", "ds输出路径_文本说明");
            ETForm.BindWindowsFormControl(ds输出路径_转换为PDF, ThisAddIn.ConfigurationSettings, "Visio辅助", "ds输出路径_转换为PDF");
            ETForm.BindWindowsFormControl(ds原根路径_转换为PDF, ThisAddIn.ConfigurationSettings, "Visio辅助", "ds原根路径_转换为PDF");
            ETForm.BindWindowsFormControl(checkBox删除图衔左侧内容_写图衔, ThisAddIn.ConfigurationSettings, "Visio辅助", "checkBox删除图衔左侧内容");
            ETForm.BindWindowsFormControl(checkBox删除图衔图号_写图衔, ThisAddIn.ConfigurationSettings, "Visio辅助", "checkBox删除图衔图号");

            // 绑定比例修改选项
            ETForm.BindWindowsFormControl(
                checkBox根据关键字改比例_写图衔,
                ThisAddIn.ConfigurationSettings,
                "Visio辅助",
                "checkBox根据关键字修改比例_写_图衔信息");

            // 绑定图衔名称下拉框
            ETForm.BindComboBox(comboBox图衔名称, ThisAddIn.ConfigurationSettings, "Visio辅助", "comboBox图衔名称");

            // 初始化Tab页布局
            tabControl1_SelectedIndexChanged(null, null);

            // 隐藏未完成的功能页面
            tabControl1.HideTabPage(tabPage其它);
            tabControl1.HideTabPage(tabPage读取数据表);
        }

        /// <summary>
        /// 导入文件清单按钮点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 打开文件操作窗体，自动配置为导入文件模式
        /// 默认启用文件导入和子目录扫描功能
        /// </remarks>
        private void button导入文件清单_Click(object sender, EventArgs e)
        {
            // 创建文件操作窗体实例
            frm文件操作 frm = new(false);
            ThisAddIn.OpenForm(frm);
            
            // 切换到导入文件名页面
            frm.tabControl1.SelectedTab = frm.tabPage导入文件名;
            
            // 配置导入选项：启用文件导入，包含子目录，禁用文件夹导入
            frm.checkBox导入文件_文件.Checked = true;
            frm.checkBox含子目录.Checked = true;
            frm.checkBox导入文件_文件夹.Checked = false;
            
            // 触发添加文件夹操作
            frm.导入文件_添加文件夹ToolStripMenuItem_Click(null, null);
        }

        /// <summary>
        /// Tab页切换事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>根据不同的Tab页设置splitContainer1的SplitterDistance</remarks>
        private void tabControl1_SelectedIndexChanged(object sender, EventArgs e)
        {
            int addDistance = 46;

            if (tabControl1.SelectedTab == tabPage仅读取图衔)
            {
                splitContainer1.SplitterDistance = button执行_仅读取图衔.Top + button执行_仅读取图衔.Height + addDistance;
            }
            else if (tabControl1.SelectedTab == tabPage读写取图衔)
            {
                splitContainer1.SplitterDistance = textBox修改比例值_写图衔.Top + textBox修改比例值_写图衔.Height + addDistance;
            }
            else if (tabControl1.SelectedTab == tabPage读取数据表)
            {
                splitContainer1.SplitterDistance = ucERS新增天馈表_读取信息.Top + ucERS新增天馈表_读取信息.Height + addDistance;
            }
            else if (tabControl1.SelectedTab == tabPage文本说明)
            {
                splitContainer1.SplitterDistance = ucERS说明_写_文本说明.Top + ucERS说明_写_文本说明.Height + addDistance;
            }
            else if (tabControl1.SelectedTab == tabPage转换为PDF)
            {
                splitContainer1.SplitterDistance = ds原根路径_转换为PDF.Top + ds原根路径_转换为PDF.Height + addDistance;
            }
            else if (tabControl1.SelectedTab == tabPage其它)
            {
                splitContainer1.SplitterDistance = groupBox1.Top + groupBox1.Height + addDistance;
            }
            else if (tabControl1.SelectedTab == tabPage检查图纸)
            {
                splitContainer1.SplitterDistance = ucERS铁塔站名_检查图纸.Top + ucERS铁塔站名_检查图纸.Height + addDistance;
            }
        }

        /// <summary>
        /// 强制结束所有Visio应用程序进程
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 用于解决Visio进程冲突问题
        /// 强制终止所有Visio相关进程，确保后续操作正常进行
        /// </remarks>
        private void KillAllVisioApp_Click(object sender, EventArgs e)
        {
            ETVisio.KillVisioApplication();
        }

        #endregion 界面控制
    }
}