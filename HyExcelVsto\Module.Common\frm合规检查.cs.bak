﻿using ET;
using HyExcelVsto.Interfaces;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Windows.Forms;
using Application = Microsoft.Office.Interop.Excel.Application;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// Excel工作表内容合规性检查窗体
    /// </summary>
    public partial class frm合规检查 : Form, IExcelMessageReceiver
    {
        /// <summary>
        /// Excel应用程序实例
        /// </summary>
        public Application XlApp;

        /// <summary>
        /// 初始化合规检查窗体
        /// </summary>
        public frm合规检查()
        {
            InitializeComponent();
            XlApp = Globals.ThisAddIn.Application;
        }

        /// <summary>
        /// 获取窗体标识符
        /// </summary>
        public string FormIdentifier => "合规检查";

        /// <summary>
        /// 处理来自Excel的选区消息
        /// </summary>
        /// <param name="target">目标范围</param>
        /// <param name="message">消息内容</param>
        public void OnExcelSelectionMessage(Range target, string message = null)
        {
            if (message == "刷新")
            {
                // 处理刷新消息
            }
        }

        /// <summary>
        /// 窗体加载事件处理
        /// </summary>
        void frm合规检查_Load(object sender, EventArgs e)
        {
            // 窗体加载时设置位置（注释掉的代码根据需求决定是否启用）
            // Left = Screen.PrimaryScreen.Bounds.Width - Width - 50;
            // Top = (Screen.PrimaryScreen.Bounds.Height - Height) / 2 + 200;
        }

        /// <summary>
        /// 标注按钮点击事件处理
        /// </summary>
        void button标注_Click(object sender, EventArgs e)
        {
            try
            {
                Range selectedRange = ETExcelExtensions.GetSelectionRange();
                string candidateText = textBox候选项.Text;

                if (string.IsNullOrWhiteSpace(candidateText))
                {
                    MessageBox.Show("请输入候选项内容", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                HashSet<string> validValues = new(candidateText.Split());

                foreach (Range cell in selectedRange.Cells)
                {
                    try
                    {
                        if (cell.IsCellEmpty())
                            continue;

                        string cellValue = cell.Value?.ToString();
                        if (cellValue != null && !validValues.Contains(cellValue))
                        {
                            cell.Format条件格式警示色(EnumWarningColor.提醒);
                        }
                    }
                    catch (Exception cellEx)
                    {
                        throw new ETException("单元格合规检查失败", "单元格格式设置", cellEx);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new ETException("合规检查失败", "工作表内容检查", ex);
            }
        }

        /// <summary>
        /// 加载数据验证列表到文本框
        /// </summary>
        /// <param name="targetRange">目标范围</param>
        void LoadValidationListToTextBox(Range targetRange)
        {
            try
            {
                List<string> validationList = targetRange.GetValidationOptions();
                if (validationList != null)
                {
                    textBox候选项.Text = string.Join(Environment.NewLine, validationList);
                }
            }
            catch (Exception ex)
            {
                throw new ETException("加载数据验证列表失败", "数据验证读取", ex);
            }
        }

        /// <summary>
        /// 加载指定范围的值到文本框
        /// </summary>
        /// <param name="targetRange">目标范围</param>
        void LoadRangeValuesToTextBox(Range targetRange)
        {
            try
            {
                List<string> valueList = targetRange.ConvertRangeToStringList(true);
                if (valueList != null)
                {
                    textBox候选项.Text = string.Join(Environment.NewLine, valueList);
                }
            }
            catch (Exception ex)
            {
                throw new ETException("加载值列表失败", "单元格值读取", ex);
            }
        }

        /// <summary>
        /// 单选按钮状态改变事件处理
        /// </summary>
        void radioButton_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                RadioButton selectedRadioButton = (RadioButton)sender;
                switch (selectedRadioButton.Text)
                {
                    case "下拉可选项":
                        LoadValidationListToTextBox(ETExcelExtensions.GetSelectionRange(true, false));
                        break;

                    case "可选项列表":
                        LoadRangeValuesToTextBox(ucExcelRangeSelect候选项.SelectedRange);
                        break;
                }
            }
            catch (Exception ex)
            {
                throw new ETException("切换选项失败", "选项切换操作", ex);
            }
        }

        /// <summary>
        /// 用户选择范围后的事件处理
        /// </summary>
        void ucExcelRangeSelect候选项_SelectionMadeEvent(object sender, EventArgs er)
        {
            try
            {
                LoadRangeValuesToTextBox(ucExcelRangeSelect候选项.SelectedRange);
            }
            catch (Exception ex)
            {
                throw new ETException("加载选定范围值失败", "范围选择操作", ex);
            }
        }
    }
}