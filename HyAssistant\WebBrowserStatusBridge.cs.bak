namespace HyAssistant
{
    /// <summary>
    /// WebBrowser状态桥接类，用于在不同窗体间传递WebBrowser状态
    /// </summary>
    public static class WebBrowserStatusBridge
    {
        /// <summary>
        /// 定义状态变更委托
        /// </summary>
        /// <param name="isHidden">WebBrowser是否处于隐藏状态</param>
        public delegate void StatusChangedHandler(bool isHidden);

        /// <summary>
        /// 创建状态变更事件
        /// </summary>
        public static event StatusChangedHandler StatusChanged;

        /// <summary>
        /// 触发状态变更事件的方法
        /// </summary>
        /// <param name="isHidden">WebBrowser是否处于隐藏状态</param>
        public static void OnStatusChanged(bool isHidden)
        {
            StatusChanged?.Invoke(isHidden);
        }
    }
}