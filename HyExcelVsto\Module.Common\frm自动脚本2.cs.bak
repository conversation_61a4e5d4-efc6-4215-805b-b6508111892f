using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;
using Range = Microsoft.Office.Interop.Excel.Range;

namespace HyExcelVsto.Module.Common
{
    public partial class frm自动脚本 : Form
    {
        readonly string[] 可用命令名Array =
        {
            //【操作前台工作簿】
            //格式类
            "设定字体大小",
            "设定字体字型",
            "执行数字格式刷",
            "执行字符格式刷",
            "执行背景格式刷",
            "执行下拉可选项格式刷",
            "设置下拉可选项",
            "去条件格式",
            "整列去条件格式",
            "可见单元格去条件格式",
            "清除格式",
            "字符设置显示格式",
            "字符设置通用格式",
            "设置表格外圈双线",
            "设置表格边框线",
            //数据输入设置类
            "获取输入内容",
            "设置值",
            "设置值(RC坐标)",
            "空单元格补充字符",
            "填充空白",
            "转数字",
            "ReplacementCharacter",
            "设置为数据输入模式",
            "去干扰字符并半角化",
            "去前后空格",
            //排序筛选类
            "设置排序",
            "筛选显示所有值",
            "取消指定列筛选",
            "取消所有列筛选",
            "设置筛选",
            "去除筛选模式",
            "设置模糊筛选",
            "设置多条件筛选",
            "设置多条件排除筛选",
            "横向查找",
            "横向查找-相等",
            "横向查找-包含",
            "横向查找-起始于",
            "横向查找-结束于",
            //格式类2
            "设置行高",
            "设置自动行高",
            "设置倍数行高",
            "设置列宽",
            "设置页眉",
            "设置页脚",
            "删除隐藏行",
            "显示隐藏行列",
            "显示隐藏表",
            //工作簿操作
            "当前工作簿另存副本",
            "当前工作簿另存PDF",
            "保存备份",
            "同目录保存备份",
            "保存当前工作簿",
            //工作表类
            "按引用复制工作表到新工作簿",
            "按表名复制工作表到新工作簿",
            "删除工作表",
            "按引用复制工作表到目标工作簿",
            "按表名复制工作表到目标工作簿",
            "锁定工作表",
            "解锁工作表",
            "锁定单元格",
            "解锁单元格",
            //APP类
            "设置屏幕更新",
            "停止屏幕更新",
            "强制重新计算一次",
            "设置自动运算",
            "停止自动运算",
            "显示层级",
            "显示隐藏分级标记",
            //APP类
            "只保留第一单元格公式",
            "复制值到指定单元格",

            //【循环输出】
            "执行动作集合",
            "循环运行脚本", //"执行动作跳转(谨慎设置)",
            //跳转类
            "跳转工作表(前台工作簿)",
            "跳转单元格(前台工作簿)",
            "跳转顶部(前台工作簿)",

            //其它
            "下拉可选项引用转固定字符串",
            "删除指定工作簿(名)所有外部连接",
            "输出警告对话框",
            "设置单元格输入模式",
            "复制值到剪贴板",
            "复制文件到剪贴板",

            //【跳转工作簿】
            "跳转回脚本所在工作簿",
            "跳转到指定工作簿",
            "打开工作簿",

            //数据表同步功能
            "按策略表同步数据表"

            //定位类
        //定位到含特定字符单元格

            ////以下待开发
        //复制工作表到新工作簿
        //跳转工作簿
        //显示层级
        //复制显示内容到新工作簿
        //新建工作表
        //新建工作簿
    };

        readonly string[] 可用候选项Array =
        {
            "宋体",
            "黑体",
            "等线",
            "微软雅黑",
            "Show",
            "Hide",
            "VeryHide",
            "显示行",
            "隐藏行",
            "显示列",
            "隐藏列",
            "跳过标题",
            "升序",
            "降序",
            "值",
            "公式",
            "白色",
            "黑色",
            "浅灰色",
            "灰色",
            "红色",
            "粉红色",
            "绿色",
            "土绿色",
            "蓝色",
            "浅蓝色",
            "浅黄色",
            "紫色",
            "求和模式",
            "保存",
            "跳过"
        };

        readonly string[] 命令行状态Array = { "停用", "隐藏", "启用" };

        public void Run执行脚本(string groupName = null)
        {
            if (!初始化(false))
                return;

            Range listTabelRange = ETExcelExtensions.OptimizeRangeSize(autoExecuteControlWorksheet.Range["A2:M2000"]);
            Workbook thisWorkbook = ThisAddIn.ExcelApplication.ActiveWorkbook; //定义当前工作簿，用于跳转回当前工作簿，即脚本所在工作簿

            textboxLog.WriteLog("----------------------------------");
            textboxLog.WriteLog($"{DateTime.Now:mm:ss}  :开始执行");

            ETExcelExtensions.SetAppFastMode();

            //先把分组名存储在 methodHs中,用于循环执行脚本
            HashSet<string> methodHs = [];
            for (int listIndex = 1; listIndex <= listTabelRange.Rows.Count; listIndex++)
                if (!ETExcelExtensions.IsCellEmptyOrWhiteSpace(listTabelRange.Cells[listIndex, 动作集合名_列号]))
                    methodHs.Add(listTabelRange.Cells[listIndex, 动作集合名_列号].Value.ToString().Trim());

            //开始执行
            for (int listIndex = 1; listIndex <= listTabelRange.Rows.Count; listIndex++)
            {
                #region 基础变量
                if (string.IsNullOrEmpty(groupName)) //如果不传入 分组名， 表示由对话框执行
                {
                    int rowNum = listTabelRange.Rows[listIndex].Row;
                    if (!_checkedListBox脚本RowIndex.ContainsKey(rowNum))
                        continue;
                    if (!checkedListBox脚本.GetItemChecked(_checkedListBox脚本RowIndex[rowNum]))
                        continue;
                }
                else //传入 分组名， 表示菜单调用执行
                {
                    string rowGroupName = listTabelRange.Cells[listIndex, 动作集合名_列号].Value == null
                        ? null
                        : listTabelRange.Cells[listIndex, 动作集合名_列号].Value.ToString().Trim();
                    if (string.IsNullOrWhiteSpace(rowGroupName))
                        continue;
                    if (rowGroupName.Trim() != groupName)
                        continue;

                    if (groupName.StartsWith("=="))
                        continue;
                    if (groupName.StartsWith("--"))
                        continue;
                }

                //如果禁用则跳过
                Range commandStatusCell = listTabelRange.Cells[listIndex, 命令行状态_列号];
                if (!ETExcelExtensions.IsCellEmpty(commandStatusCell) &&
                    (new List<string> { "禁用", "停用" }).Contains(commandStatusCell.Value.ToString().Trim()))
                    continue;
                #region 单元格变量赋值
                string method = ETExcelExtensions.GetRangeValueAsString(listTabelRange.Cells[listIndex, 命令行_列号]);
                Range rangeTo = ETExcelExtensions.GetRangeByFormulaThenByValue(
                    listTabelRange.Cells[listIndex, 作用对象_列号]);
                string rangeToValue = ETExcelExtensions.GetRangeValueAsString(listTabelRange.Cells[listIndex, 作用对象_列号]);
                Range rangeToCell = listTabelRange.Cells[listIndex, 作用对象_列号]; //指向目标对象单元格，不会进行换算

                if (string.IsNullOrWhiteSpace(method))
                    continue;

                string parameter1Formula = ETExcelExtensions.GetRangeFormula(listTabelRange.Cells[listIndex, 参数1_列号]);
                string parameter2Formula = ETExcelExtensions.GetRangeFormula(listTabelRange.Cells[listIndex, 参数2_列号]);
                string parameter3Formula = ETExcelExtensions.GetRangeFormula(listTabelRange.Cells[listIndex, 参数3_列号]);
                string parameter4Formula = ETExcelExtensions.GetRangeFormula(listTabelRange.Cells[listIndex, 参数4_列号]);

                string parameter1Value = ETExcelExtensions.GetRangeValueAsString(listTabelRange.Cells[listIndex, 参数1_列号]);
                string parameter2Value = ETExcelExtensions.GetRangeValueAsString(listTabelRange.Cells[listIndex, 参数2_列号]);
                string parameter3Value = ETExcelExtensions.GetRangeValueAsString(listTabelRange.Cells[listIndex, 参数3_列号]);
                string parameter4Value = ETExcelExtensions.GetRangeValueAsString(listTabelRange.Cells[listIndex, 参数4_列号]);

                Range parameter1Range =
                    ETExcelExtensions.GetRangeByFormulaThenByValue(listTabelRange.Cells[listIndex, 参数1_列号]);
                Range parameter2Range =
                    ETExcelExtensions.GetRangeByFormulaThenByValue(listTabelRange.Cells[listIndex, 参数2_列号]);
                Range parameter3Range =
                    ETExcelExtensions.GetRangeByFormulaThenByValue(listTabelRange.Cells[listIndex, 参数3_列号]);
                Range parameter4Range =
                    ETExcelExtensions.GetRangeByFormulaThenByValue(listTabelRange.Cells[listIndex, 参数4_列号]);

                Range parameter1Range2 = listTabelRange.Cells[listIndex, 参数1_列号];
                Range parameter2Range2 = listTabelRange.Cells[listIndex, 参数2_列号];
                Range parameter3Range2 = listTabelRange.Cells[listIndex, 参数3_列号];
                Range parameter4Range2 = listTabelRange.Cells[listIndex, 参数4_列号];

                Range outputRange1 = listTabelRange.Cells[listIndex, 输出1_列号];
                Range outputRange2 = listTabelRange.Cells[listIndex, 输出2_列号];
                #endregion 单元格变量赋值
                string note = ETExcelExtensions.GetRangeValueAsString(listTabelRange.Cells[listIndex, 备注_列号]);
                if (string.IsNullOrEmpty(method))
                    continue;
                #endregion 基础变量
                if (!string.IsNullOrWhiteSpace(note))
                    textboxLog.WriteLog($"\r\n【note:{note}】");
                switch (method)
                {
                    case "执行动作集合":
                    case "循环运行脚本":

                        // 获取循环次数
                        double cycleTimes = parameter1Range2.IsCellEmptyOrWhiteSpace() ? 1 : parameter1Range2.CalculateSum();

                        if (cycleTimes == 0)
                            cycleTimes = 1;

                        // 获取循环动作组名称
                        string cycleGroup = string.Empty;

                        if (!string.IsNullOrWhiteSpace(rangeToValue) && methodHs.Contains(rangeToValue))
                            cycleGroup = rangeToValue;

                        // 如果是执行动作集合,只循环一次
                        if (method == "执行动作集合")
                            cycleTimes = 1;

                        // 开始执行循环
                        for (int i = 1; i <= cycleTimes; i++)
                        {
                            // 判断是否退出循环
                            if (cycleTimes > 1)
                            {
                                if (parameter2Range2.IsCellError() ||
                                    (parameter2Range2.IsCellNumber() && parameter2Range2.CalculateSum() == 0) ||
                                    parameter2Range2.IsCellEmptyOrWhiteSpace())
                                {
                                    NotifyMessage("出错或者结束");
                                    // 完全结束循环
                                    break;
                                }

                                if (parameter2Range2.Value.ToString() == "结束" ||
                                    parameter2Range2.Value.ToString().ToUpper() == "FALSE" ||
                                    (parameter2Range2.IsCellLogical() && parameter2Range2.ToString() == "False"))
                                {
                                    NotifyMessage("结束");
                                    // 完全结束循环
                                    break;
                                }
                            }

                            // 输出循环开始日志
                            PrintMessage(method, $"开始循环({i})");

                            // 执行动作
                            Run执行脚本(cycleGroup);

                            // 设置输出单元格为当前循环数
                            if (cycleTimes > 1)
                                outputRange1.Value = i;
                        }

                        // 输出循环执行成功日志
                        PrintMessage(method);

                        break;

                    case "转数字":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        rangeTo.ConvertToNumeric();

                        PrintMessage(method);
                        break;
                    #region 已完成代码脚本
                    case "ReplacementCharacter":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method) || string.IsNullOrEmpty(parameter1Value))
                            continue;
                        rangeTo.Replace替换字符(parameter1Value, parameter2Value, false);

                        PrintMessage(method);
                        break;

                    case "清除格式":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        rangeTo.Del文本格式();

                        PrintMessage(method);
                        break;

                    case "设定字体大小":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method) || string.IsNullOrEmpty(parameter1Value))
                            continue;
                        rangeTo.Set设置字体大小(parameter1Value);

                        PrintMessage(method);
                        break;

                    case "设定字体字型":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method) || string.IsNullOrEmpty(parameter1Value))
                            continue;
                        rangeTo.Set设置字型(parameter1Value);

                        PrintMessage(method);
                        break;

                    case "执行数字格式刷":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        ETExcelExtensions.Set数字格式刷(rangeTo, listTabelRange.Cells[listIndex, 参数1_列号]);

                        PrintMessage(method);
                        break;

                    case "执行字符格式刷":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        ETExcelExtensions.Set字体格式刷(rangeTo, listTabelRange.Cells[listIndex, 参数1_列号]);

                        PrintMessage(method);
                        break;

                    case "执行背景格式刷":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        ETExcelExtensions.Set背景格式刷(rangeTo, listTabelRange.Cells[listIndex, 参数1_列号]);

                        PrintMessage(method);
                        break;

                    case "设置行高":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method) || string.IsNullOrEmpty(parameter1Value))
                            continue;
                        rangeTo.Set设置行高(parameter1Value);

                        PrintMessage(method);
                        break;

                    case "设置倍数行高":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method) || string.IsNullOrEmpty(parameter1Value))
                            continue;
                        rangeTo.Set倍数行高(parameter1Value);

                        PrintMessage(method);
                        break;

                    case "设置自动行高":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        rangeTo.Set自动行高();

                        PrintMessage(method);
                        break;

                    case "设置列宽":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method) || string.IsNullOrEmpty(parameter1Value))
                            continue;
                        rangeTo.Set设置列宽(parameter1Value);

                        PrintMessage(method);
                        break;

                    case "设置页眉":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        ETExcelExtensions.Set设置页眉(rangeTo.Parent, parameter1Value, parameter2Value, parameter3Value);

                        PrintMessage(method);
                        break;

                    case "设置页脚":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        ETExcelExtensions.Set设置页脚(
                            rangeTo.Parent,
                            listTabelRange.Cells[listIndex, 参数1_列号].Value,
                            listTabelRange.Cells[listIndex, 参数2_列号].Value,
                            listTabelRange.Cells[listIndex, 参数3_列号].Value);

                        PrintMessage(method);
                        break;

                    case "取消指定列筛选":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        rangeTo.Filter取消设置指定列筛选();

                        PrintMessage(method);
                        break;

                    case "取消所有列筛选":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        rangeTo.Filter撤销所有筛选条件();

                        PrintMessage(method);
                        break;

                    case "去除筛选模式":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        ETExcelExtensions.Filter取消筛选模式(rangeTo.Parent);

                        PrintMessage(method);
                        break;

                    case "筛选显示所有值":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        ETExcelExtensions.Filter撤销所有筛选条件(rangeTo.Parent);

                        PrintMessage(method);
                        break;

                    case "设置筛选":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method) || string.IsNullOrEmpty(parameter1Value))
                            continue;

                        if (string.IsNullOrEmpty(parameter2Value))
                            rangeTo.Filter设置条件筛选(parameter1Value, EnumFindMode.Contains);
                        else
                            rangeTo.Filter设置条件筛选(
                                new List<string> { parameter1Value, parameter2Value },
                                EnumFindMode.Contains);

                        PrintMessage(method);
                        break;

                    case "设置模糊筛选":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method) || string.IsNullOrEmpty(parameter1Value))
                            continue;

                        if (string.IsNullOrEmpty(parameter2Value))
                            rangeTo.Filter设置条件筛选(parameter1Value, EnumFindMode.Contains);
                        else
                            rangeTo.Filter设置条件筛选(
                                new List<string> { parameter1Value, parameter2Value },
                                EnumFindMode.Contains);

                        PrintMessage(method);
                        break;

                    case "设置多条件筛选":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method) || string.IsNullOrEmpty(parameter1Value))
                            continue;
                        if (string.IsNullOrEmpty(parameter2Value))
                            parameter2Value = "\t|";
                        rangeTo.Filter设置条件筛选(parameter1Value, parameter2Value, EnumFindMode.Contains);

                        PrintMessage(method);
                        break;

                    case "设置多条件排除筛选":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method) || string.IsNullOrEmpty(parameter1Value))
                            continue;

                        if (string.IsNullOrEmpty(parameter2Value))
                            parameter2Value = "\t|";
                        rangeTo.Filter设置排除条件筛选(parameter1Value, parameter2Value, false);

                        PrintMessage(method);
                        break;

                    case "设置排序":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method) || string.IsNullOrEmpty(parameter1Value))
                            continue;
                        rangeTo.Set设置排序(parameter1Value);

                        PrintMessage(method);
                        break;

                    case "执行下拉可选项格式刷":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        ETExcelExtensions.SetOptional设置下拉可选项(rangeTo, listTabelRange.Cells[listIndex, 参数1_列号]);

                        PrintMessage(method);
                        break;

                    case "设置下拉可选项":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method) || string.IsNullOrEmpty(parameter1Value))
                            continue;
                        rangeTo.SetOptional设置下拉可选项(parameter1Formula);

                        PrintMessage(method);
                        break;

                    case "删除隐藏行":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        rangeTo.Del删除隐藏行(false);

                        PrintMessage(method);
                        break;

                    case "显示隐藏表":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        string showHide = parameter1Value;
                        ETExcelExtensions.Set显示隐藏表(rangeTo.Parent, parameter1Value);

                        PrintMessage(method);
                        break;

                    case "删除工作表":
                        //if (CheckRangeIsNullAndOutputMsg(rangeTo, method)) continue;
                        if (rangeTo != null)
                        {
                            ETExcelExtensions.Delete删除工作表(rangeTo.Parent);
                        }
                        else
                        {
                            if (!rangeToCell.IsCellEmpty())
                            {
                                Worksheet shDel =
                                    ETExcelExtensions.GetWorksheetByName(
                                    XlApp.ActiveWorkbook,
                                    rangeToCell.Value.ToString());
                                if (shDel != null)
                                    shDel.Delete删除工作表();
                            }
                        }

                        PrintMessage(method);
                        break;

                    case "显示层级":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method) || string.IsNullOrEmpty(parameter1Value))
                            continue;
                        ETExcelExtensions.Set控制列层级(rangeTo.Parent, parameter1Value);

                        PrintMessage(method);
                        break;

                    case "显示隐藏层级标记":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method) || string.IsNullOrEmpty(parameter1Value))
                            continue;
                        ETExcelExtensions.Set显示层级按键(rangeTo.Parent, parameter1Value);

                        PrintMessage(method);
                        break;

                    case "保存备份":
                        if (string.IsNullOrEmpty(parameter1Value) && string.IsNullOrEmpty(parameter2Value))
                            continue;
                        //parameter1:路径，可以留空
                        //parameter2:文件名，可以留空
                        FileInfo fileInfo1 = ThisAddIn.ExcelApplication.ActiveWorkbook
                            .SaveCopyAs(
                                parameter1Value,
                                null,
                                parameter2Value,
                                DateTime.Now.ToString("yyyyMMdd-HHmmss"),
                                null,
                                true);
                        outputRange1.Value = fileInfo1 == null ? "null" : fileInfo1.FullName;

                        PrintMessage(method);
                        break;

                    case "同目录保存备份":
                        outputRange1.Value = ThisAddIn.ExcelApplication.ActiveWorkbook
                            .SaveCopyAs(
                                null,
                                Path.Combine(ThisAddIn.ExcelApplication.ActiveWorkbook.Path, "[bak]"),
                                null,
                                DateTime.Now.ToString("yyyyMMdd-HHmmss"));

                        PrintMessage(method);
                        break;

                    case "保存当前工作簿":
                        ETExcelExtensions.Save(ThisAddIn.ExcelApplication.ActiveWorkbook);

                        PrintMessage(method);
                        break;

                    case "当前工作簿另存": //另存为后是操作新的文件名的工作表
                        //parameter1:路径，可以留空
                        //parameter2:文件名，可以留空
                        if (string.IsNullOrEmpty(parameter1Value) && string.IsNullOrEmpty(parameter2Value))
                            continue;
                        //如果新路径和新名字都为空无法操作保存同名文件

                        FileInfo fileInfo2 = ThisAddIn.ExcelApplication.ActiveWorkbook
                            .SaveCopyAs(ThisAddIn.ExcelApplication.ActiveWorkbook.Path, parameter1Value, parameter2Value);
                        outputRange1.Value = fileInfo2 == null ? "null" : fileInfo2.FullName;

                        PrintMessage(method);
                        break;

                    case "当前工作簿另存副本":
                        //parameter1:路径，可以留空
                        //parameter2:文件名，可以留空
                        if (string.IsNullOrEmpty(parameter1Value) && string.IsNullOrEmpty(parameter2Value))
                            continue;
                        //如果新路径和新名字都为空无法操作保存同名文件

                        //如果第3个参数标注跳过，这跳过这次保存
                        if (!string.IsNullOrEmpty(parameter3Value) && parameter3Value == "跳过")
                            continue;

                        outputRange1.Value = ThisAddIn.ExcelApplication.ActiveWorkbook
                            .SaveCopyAs(ThisAddIn.ExcelApplication.ActiveWorkbook.Path, parameter1Value, parameter2Value)
                            .FullName;

                        PrintMessage(method);
                        break;

                    case "当前工作簿另存PDF":
                        //parameter1:路径，可以留空
                        //parameter2:文件名，可以留空
                        if (string.IsNullOrEmpty(parameter1Value) && string.IsNullOrEmpty(parameter2Value))
                            continue;
                        //如果新路径和新名字都为空无法操作保存同名文件

                        //如果第3个参数标注跳过，这跳过这次保存
                        if (!string.IsNullOrEmpty(parameter3Value) && parameter3Value == "跳过")
                            continue;

                        outputRange1.Value = ThisAddIn.ExcelApplication.ActiveWorkbook
                            .ExportToPdf(
                                ThisAddIn.ExcelApplication.ActiveWorkbook.Path,
                                parameter1Value,
                                parameter2Value)
                            .FullName;

                        PrintMessage(method);
                        break;

                    case "跳转回脚本所在工作簿":
                        thisWorkbook.ActivateWorkbook();

                        PrintMessage(method);
                        break;

                    case "跳转工作簿":
                    case "跳转到指定工作簿":

                        if (string.IsNullOrEmpty(parameter1Value))
                        {
                            textboxLog.WriteLog($"{DateTime.Now:mm:ss}    未指定打开 工作簿名称");
                            continue;
                        }

                        try
                        {
                            Workbook jumpWorkbook = parameter1Formula.GetWorkbookNameByFormulaThenByName();
                            if (jumpWorkbook == null)
                            {
                                outputRange1.Value = "无法跳转";
                                outputRange2.Value = "无法跳转";
                            }
                            else
                            {
                                outputRange1.Value = jumpWorkbook.Name;
                                outputRange2.Value = jumpWorkbook.FullName;
                            }

                            jumpWorkbook.ActivateWorkbook();
                        }
                        catch (Exception)
                        {
                            outputRange1.Value = "无法跳转";
                            outputRange2.Value = "无法跳转";
                        }

                        PrintMessage(method);
                        break;

                    case "跳转工作表(前台工作簿)":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        ETExcelExtensions.ActivateWorksheet(rangeTo.Parent, false);
                        outputRange1.Value = rangeTo.Parent.Name;

                        PrintMessage(method);
                        break;

                    case "打开工作簿":
                        //CheckRangeIsNullAndOutputMsg(rangeTo, method);
                        //parameter1:工作簿路径
                        Workbook openWorkbook = ETExcelExtensions.Open工作簿(parameter1Value, true);
                        if (openWorkbook == null)
                        {
                            outputRange1.Value = "无法打开";
                            outputRange2.Value = "无法打开";
                        }
                        else
                        {
                            outputRange1.Value = openWorkbook.Name;
                            outputRange2.Value = openWorkbook.FullName;
                        }

                        PrintMessage(method);
                        break;

                    case "按引用复制工作表到新工作簿":
                    case "按表名复制工作表到新工作簿":
                    case "按引用复制工作表到目标工作簿":
                    case "按表名复制工作表到目标工作簿":

                        try
                        {
                            Worksheet fromSh;
                            if (method.StartsWith("按引用"))
                            {
                                if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                                    continue;
                                fromSh = rangeTo.GetParent();
                            }
                            else
                            {
                                if (string.IsNullOrEmpty(rangeToValue))
                                    continue;
                                fromSh = rangeToValue.GetWorksheetByFormulaThenByName();
                            }

                            if (fromSh == null)
                            {
                                outputRange1.Value = "无法复制";
                                outputRange2.Value = "无法复制";
                                return;
                            }

                            if (method.EndsWith("新工作簿"))
                            {
                                Worksheet newworksheet = fromSh.CopyWorksheetToNewWorkbook();
                                outputRange1.Value = newworksheet.Parent.Name;
                            }
                            else
                            {
                                Workbook toWb = parameter1Value.GetWorkbookNameByFormulaThenByName();
                                fromSh.CopyWorksheetTo(toWb);
                                outputRange1.Value = toWb.Name;
                                outputRange2.Value = toWb.FullName;
                            }
                        }
                        catch (Exception)
                        {
                            outputRange1.Value = "无法复制";
                            outputRange2.Value = "无法复制";
                        }

                        ETExcelExtensions.Calculation();

                        PrintMessage(method, method);
                        break;

                    case "显示隐藏行列":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method) || string.IsNullOrEmpty(parameter1Value))
                            continue;
                        rangeTo.Show显示隐藏行列(parameter1Value);

                        PrintMessage(method);
                        break;

                    case "显示隐藏分级标记":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method) || string.IsNullOrEmpty(parameter1Value))
                            continue;
                        ETExcelExtensions.DisplayOutline(parameter1Value);

                        PrintMessage(method);
                        break;

                    case "跳转单元格(前台工作簿)":
                    case "跳转单元格":

                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        rangeTo.JumpToRange();
                        outputRange1.Value = rangeTo.Parent.Name;

                        PrintMessage(method);
                        break;

                    case "跳转顶部(前台工作簿)":
                    case "跳转顶部":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        ETExcelExtensions.ActivateWorksheet(rangeTo.Parent, true);

                        PrintMessage(method);
                        break;

                    case "设置值":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        rangeTo.Fast快速赋值(parameter1Value, false);

                        PrintMessage(method);
                        break;

                    case "设置值(RC坐标)":
                        Range setValueRange = rangeToValue.GetRangeByRowColumnCoordinate();
                        if (setValueRange == null)
                            continue;
                        setValueRange.Fast快速赋值(parameter1Value, false);

                        PrintMessage(method);

                        break;

                    case "空单元格补充字符":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method) || string.IsNullOrEmpty(parameter1Value))
                            continue;
                        rangeTo.Fill填充空白单元格(parameter1Value, false);

                        PrintMessage(method);
                        break;

                    case "去干扰字符并半角化":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        rangeTo.ConvertRangeToHalfWidth(false);

                        PrintMessage(method);
                        break;

                    case "去前后空格":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        rangeTo.Trim去前后空格(false);

                        PrintMessage(method);
                        break;

                    case "设置屏幕更新":
                        ETExcelExtensions.Update();

                        PrintMessage(method);
                        break;

                    case "停止屏幕更新":
                        ETExcelExtensions.Update(false);

                        PrintMessage(method);
                        break;

                    case "强制重新计算一次":
                    case "强制重新计算":
                        ETExcelExtensions.ForceRecalculateAndRestoreState();

                        PrintMessage(method);
                        break;

                    case "设置自动运算":
                        ETExcelExtensions.AutoCalculation();

                        PrintMessage(method);
                        break;

                    case "停止自动运算":
                        ETExcelExtensions.AutoCalculation(false);

                        PrintMessage(method);
                        break;

                    case "去条件格式":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        rangeTo.Del删除条件格式();

                        PrintMessage(method);
                        break;

                    case "整列去条件格式":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        rangeTo.EntireColumn.Del删除条件格式();

                        PrintMessage(method);
                        break;

                    case "可见单元格去条件格式":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        rangeTo.GetVisibleRange().Del删除条件格式();

                        PrintMessage(method);
                        break;

                    case "填充空白":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;

                        if (string.IsNullOrEmpty(parameter1Value))
                            rangeTo.FillEmptyCells(" ", false, false);
                        if (!string.IsNullOrEmpty(parameter1Value) &&
                            parameter1Value.IndexOf("跳过标题", StringComparison.Ordinal) >= 0)
                            rangeTo.FillEmptyCells(" ", true, false);

                        PrintMessage(method);
                        break;

                    case "设置为数据输入模式":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        rangeTo.SetCellToCandidate1();

                        PrintMessage(method);
                        break;

                    case "获取输入内容":
                        if (string.IsNullOrEmpty(parameter1Value))
                            continue;
                        ETExcelExtensions.GetInputAndSetCell(parameter1Value, outputRange1);

                        PrintMessage(method);
                        break;

                    case "只保留第一单元格公式":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;

                        if (string.IsNullOrEmpty(parameter1Value))
                            rangeTo.Del保留首单元格式其他删除(false, false);
                        if (!string.IsNullOrEmpty(parameter1Value) &&
                            parameter1Value.IndexOf("跳过标题", StringComparison.Ordinal) >= 0)
                            rangeTo.Del保留首单元格式其他删除(true, false);

                        PrintMessage(method);
                        break;

                    case "复制值到指定单元格":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method) || parameter1Range == null)
                            continue;

                        string pasteType = string.IsNullOrWhiteSpace(parameter2Value)
                            ? "值"
                            : parameter2Value.Replace(" ", string.Empty).ToUpper();
                        pasteType = pasteType.Substring(0, 1);
                        if (pasteType == "F" || pasteType == "公")
                            rangeTo.CopyRangeFormula(parameter1Range);
                        else
                            rangeTo.CopyRangeValues(parameter1Range);

                        PrintMessage(method);
                        break;

                    case "横向查找":
                    case "横向查找-相等":
                    case "横向查找-包含":
                    case "横向查找-起始于":
                    case "横向查找-结束于":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method) || string.IsNullOrEmpty(parameter1Value))
                            continue;

                        frm批量查找 frm = new();
                        if (method == "横向查找" || method == "横向查找-相等")
                            frm.radioButton相同.Checked = true;
                        if (method == "横向查找-包含")
                            frm.radioButton包含.Checked = true;
                        if (method == "横向查找-起始于")
                            frm.radioButton起始于.Checked = true;
                        if (method == "横向查找-结束于")
                            frm.radioButton相同.Checked = true;

                        frm.Run横向查找(rangeTo, parameter1Value);

                        PrintMessage(method);
                        break;

                    case "下拉可选项引用转固定字符串":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        rangeTo.TurnDropDownToFixedString();
                        PrintMessage(method);
                        break;

                    case "字符设置显示格式":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method) || string.IsNullOrEmpty(parameter1Value))
                            continue;
                        rangeTo.Set设置格式(parameter1Value);
                        PrintMessage(method);
                        break;

                    case "字符设置通用格式":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        rangeTo.Set通用格式();
                        PrintMessage(method);
                        break;

                    case "根据参数判定是否结束执行":
                        if (string.IsNullOrEmpty(parameter1Value))
                            continue;
                        parameter1Value = parameter1Value.Trim().ToUpper();
                        if (parameter1Value == "END" ||
                            parameter1Value == "结束" ||
                            parameter1Value == "1" ||
                            parameter1Value == "TRUE")
                        {
                            PrintMessage(method);
                            return;
                        }

                        break;

                    //case "执行动作跳转(谨慎设置)":
                    //    if (CheckRangeIsNullAndOutputMsg(rangeTo, method)) continue;
                    //    if (rangeTo.Parent != rangeToFirstCell.Parent) continue;
                    //    listIndex = rangeTo.Row - 1;

                    // break;

                    case "删除指定工作簿所有外部连接":
                    case "删除指定工作簿(名)所有外部连接":

                        if (string.IsNullOrEmpty(rangeToValue))
                            continue;

                        Workbook beforeRunWorkbook = XlApp.ActiveWorkbook;
                        Workbook delExcelLinksWb = rangeToValue.GetWorkbookNameByFormulaThenByName();
                        if (delExcelLinksWb == null)
                        {
                            outputRange1.Value = "找不到工作簿";
                            continue;
                        }

                        delExcelLinksWb.Delete外部连接(); //删除链接需跳转为当前工作表才有效果
                        beforeRunWorkbook.Activate(); //执行完成后跳回
                        break;

                    case "输出警告对话框":
                        if (string.IsNullOrEmpty(parameter1Value))
                            continue;
                        MessageBox.Show(parameter1Value, @"脚本提示");

                        break;

                    case "锁定单元格":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        rangeTo.Lock();

                        break;

                    case "解锁单元格":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        rangeTo.UnLockRange();

                        break;

                    case "锁定工作表":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        ((Worksheet)rangeTo.Parent).Lock();

                        break;

                    case "解锁工作表":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        ((Worksheet)rangeTo.Parent).UnLockWorksheet();

                        break;

                    case "设置表格外圈双线":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        rangeTo.Format设置表格外圈双线();

                        break;

                    case "设置表格边框线":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        rangeTo.Format设置边框();

                        break;

                    case "设置单元格输入模式":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method) || string.IsNullOrEmpty(parameter1Value))
                            continue;
                        rangeTo.SetCellInputMode(parameter1Value.Trim());

                        break;
                    #endregion 已完成代码脚本
                    case "按策略表同步数据表":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;

                        textboxLog.WriteLog($"执行: {rangeToValue}");
                        excelUpdater.logTextBox = textboxLog;
                        excelUpdater.ExecuteUpdate(autoUpdateControlSheet, rangeToValue);

                        rangeTo.Format设置边框();

                        break;

                    case "复制值到剪贴板":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        Clipboard.SetText(rangeToValue);

                        break;

                    case "复制文件到剪贴板":
                        if (CheckRangeIsNullAndOutputMsg(rangeTo, method))
                            continue;
                        if (File.Exists(rangeToValue))
                            ETFile.FileCopyToClipboard(rangeToValue);

                        break;

                    default:
                        textboxLog.WriteLog($"不可用命令:\"{method}\"");
                        break;
                }
            }

            ETExcelExtensions.SetAppNormalMode(true);

            textboxLog.WriteLog($"{DateTime.Now:mm:ss}  :完成执行");
            textboxLog.WriteLog("----------------------------------");
            textboxLog.WriteLog(string.Empty);

            if (!string.IsNullOrEmpty(groupName))
                timerCloseWindow.Enabled = true; //如果是外部传入打开执行脚本的，关闭对话框
        }

        public void Run执行脚本(string groupName, string talbeName)
        {
            try
            {
                switch (talbeName)
                {
                    case "autoExecute":
                        Run执行脚本(groupName);
                        break;

                    case "autoUpdate":
                        textboxLog.WriteLog($"执行: {groupName}");
                        excelUpdater.logTextBox = textboxLog;
                        excelUpdater.ExecuteUpdate(autoUpdateControlSheet, groupName);
                        break;
                }
            }
            catch (Exception ex)
            {
                textboxLog.WriteLog($"错误: {ex.Message}");
            }
        }
    }
}