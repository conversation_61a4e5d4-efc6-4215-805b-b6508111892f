﻿/*
 * ============================================================================
 * 功能模块：Excel批量查找和筛选工具
 * ============================================================================
 * 
 * 模块作用：为Excel提供强大的批量查找、筛选和数据处理功能
 * 
 * 主要功能：
 * - 横向查找：在行方向上查找匹配的文本内容
 * - 纵向查找：在列方向上查找匹配的文本内容
 * - 多模式搜索：支持完全匹配、包含、起始于、结束于等搜索模式
 * - 智能筛选：基于搜索结果自动筛选和隐藏不匹配的行或列
 * - 非空筛选：快速筛选出非空值和非零值的数据
 * - 关键字处理：支持提取文本中的关键字进行搜索
 * - 后缀处理：支持根据配置的后缀规则处理搜索词
 * - 结果高亮：对匹配的单元格进行高亮标记
 * 
 * 执行逻辑：
 * 1. 用户输入搜索条件和选择搜索模式
 * 2. 系统解析搜索词并根据配置进行预处理
 * 3. 在指定范围内执行搜索操作
 * 4. 收集匹配和未匹配的结果
 * 5. 应用筛选规则隐藏不匹配的行或列
 * 6. 高亮显示匹配的单元格
 * 7. 显示搜索统计结果
 * 
 * 注意事项：
 * - 搜索操作会影响工作表的显示状态
 * - 支持撤销筛选操作恢复原始状态
 * - 大数据量搜索时可能影响性能
 * ============================================================================
 */

using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// Excel批量查找和筛选窗体类
    /// </summary>
    /// <remarks>
    /// 提供Excel中的批量查找、筛选和数据处理功能，支持多种搜索模式和智能筛选
    /// </remarks>
    public partial class frm批量查找 : Form
    {
        #region 私有字段

        /// <summary>
        /// 窗体自动缩放行为控制（已注释）
        /// </summary>
        //readonly AutoCollapseWindowBehavior collapseBehavior;

        /// <summary>
        /// 标记的单元格范围
        /// </summary>
        /// <remarks>用于高亮显示匹配结果的单元格范围</remarks>
        Range _markedRange;

        /// <summary>
        /// 搜索方向枚举
        /// </summary>
        /// <remarks>决定是在行方向还是列方向进行搜索</remarks>
        EnumRowColumn _searchDirection;

        /// <summary>
        /// 搜索关键词集合
        /// </summary>
        /// <remarks>存储所有需要查找的关键词</remarks>
        HashSet<string> _searchTerms = [];

        /// <summary>
        /// 匹配项目集合
        /// </summary>
        /// <remarks>存储所有成功匹配的文本</remarks>
        HashSet<string> _matchedItems = [];

        /// <summary>
        /// 匹配单元格范围集合
        /// </summary>
        /// <remarks>存储所有包含匹配文本的单元格</remarks>
        HashSet<Range> _matchedRanges = [];

        /// <summary>
        /// 未匹配项目集合
        /// </summary>
        /// <remarks>存储所有未能找到匹配的搜索词</remarks>
        HashSet<string> _unmatchedItems = [];

        /// <summary>
        /// 列可见性状态字典
        /// </summary>
        /// <remarks>
        /// 用于记录筛选前的列显示状态，以便恢复
        /// Key: 列索引, Value: 是否可见
        /// </remarks>
        Dictionary<int, bool> _columnVisibility = [];

        /// <summary>
        /// 当前选中的单元格范围
        /// </summary>
        /// <remarks>用于确定搜索和筛选的范围</remarks>
        Range _selectedRange;

        /// <summary>
        /// 当前工作表引用
        /// </summary>
        /// <remarks>用于执行工作表级别的操作</remarks>
        Worksheet _currentWorksheet;

        /// <summary>
        /// 后缀配置字典
        /// </summary>
        /// <remarks>
        /// 存储不同类型的后缀配置
        /// Key: 后缀类型名称, Value: 该类型包含的后缀数组
        /// </remarks>
        Dictionary<string, string[]> _suffixConfig = [];

        #endregion 私有字段

        #region 初始化方法

        /// <summary>
        /// 初始化搜索相关变量
        /// </summary>
        /// <remarks>
        /// 执行逻辑：清空集合 → 重置标记 → 获取工作表
        /// </remarks>
        /// <exception cref="ETException">初始化失败时抛出</exception>
        void InitializeSearchVariables()
        {
            try
            {
                // 清空所有搜索相关的集合
                _searchTerms.Clear();
                _matchedItems.Clear();
                _matchedRanges.Clear();
                _unmatchedItems.Clear();
                _columnVisibility.Clear();
                
                // 重置标记范围
                _markedRange = null;
                
                // 获取当前活动工作表
                _currentWorksheet = Globals.ThisAddIn.Application.ActiveSheet;
            }
            catch (Exception ex)
            {
                throw new ETException("初始化搜索变量失败", "初始化Excel搜索", ex);
            }
        }

        #endregion 初始化方法

        #region 搜索和筛选方法

        /// <summary>
        /// 执行搜索和筛选操作的主方法
        /// </summary>
        /// <param name="startRange">起始单元格范围</param>
        /// <param name="direction">搜索方向（行/列）</param>
        /// <exception cref="ETException">搜索操作失败时抛出</exception>
        /// <remarks>
        /// 执行逻辑：初始化 → 验证参数 → 获取搜索条件 → 执行搜索 → 显示结果
        /// </remarks>
        void PerformSearchAndFilter(Range startRange, EnumRowColumn direction)
        {
            try
            {
                // 初始化搜索变量和状态
                InitializeSearchVariables();

                // 验证起始范围
                if (startRange == null)
                    return;
                    
                _searchDirection = direction;

                // 获取搜索模式和搜索词
                EnumFindMode searchMode = GetSearchMode();
                string[] searchTerms = GetSearchTerms();
                if (searchTerms.Length == 0)
                    return;

                // 执行模糊搜索并收集结果
                _matchedRanges = new HashSet<Range>(
                    ETExcelExtensions
                        .FuzzySearch(
                            startRange,
                            _searchTerms,
                            _searchDirection,
                            searchMode,
                            out _matchedItems,
                            out _unmatchedItems,
                            out _selectedRange,
                            out _columnVisibility
                        )
                        .Cast<Range>()
                );

                // 显示搜索结果并应用筛选
                DisplaySearchResults(startRange);
            }
            catch (Exception ex)
            {
                throw new ETException("执行搜索和筛选操作失败", "Excel搜索筛选", ex);
            }
        }

        /// <summary>
        /// 获取当前选择的搜索模式
        /// </summary>
        /// <returns>搜索模式枚举值</returns>
        /// <remarks>根据用户界面单选按钮状态返回相应的搜索模式</remarks>
        EnumFindMode GetSearchMode()
        {
            // 检查各种搜索模式的单选按钮状态
            if (radioButton相同.Checked)
                return EnumFindMode.Equal;
            if (radioButton包含.Checked)
                return EnumFindMode.Contains;
            if (radioButton起始于.Checked)
                return EnumFindMode.StartsWith;
            if (radioButton结束于.Checked)
                return EnumFindMode.EndsWith;
                
            // 默认返回完全匹配模式
            return EnumFindMode.Equal;
        }

        /// <summary>
        /// 获取并处理搜索关键词数组
        /// </summary>
        /// <returns>处理后的搜索关键词数组</returns>
        /// <exception cref="ETException">处理搜索关键词时发生错误</exception>
        /// <remarks>
        /// 执行逻辑：解析输入 → 分割文本 → 处理关键字 → 处理后缀 → 过滤保存
        /// 支持分隔符：换行符、制表符、分号、逗号、竖线、顿号
        /// </remarks>
        string[] GetSearchTerms()
        {
            try
            {
                // 获取并规范化输入文本
                string inputText = textBox查找.Text.RegularEnter(true);
                string[] searchTerms;

                // 根据输入格式确定分隔方式
                bool hasNewLine = inputText.IndexOf("\n", StringComparison.Ordinal) >= 0;
                searchTerms = hasNewLine
                    ? inputText.Split('\n')  // 按换行符分割
                    : textBox查找.Text.Split("\t;,|、，；");  // 按多种分隔符分割

                // 处理关键字（如果启用）
                if (ShouldProcessKeywords())
                {
                    searchTerms = ProcessKeywords(searchTerms);
                }

                // 处理后缀（如果配置）
                if (ShouldProcessSuffix())
                {
                    searchTerms = ProcessSuffix(searchTerms);
                }

                // 过滤空值并保存到搜索词集合
                searchTerms = FilterAndSaveSearchTerms(searchTerms);

                return searchTerms;
            }
            catch (Exception ex)
            {
                throw new ETException("处理搜索关键词失败", "Excel关键词处理", ex);
            }
        }

        /// <summary>
        /// 判断是否需要处理关键字
        /// </summary>
        /// <returns>需要处理关键字时返回true</returns>
        bool ShouldProcessKeywords()
        {
            return checkBox只取关键字.Enabled && checkBox只取关键字.Checked;
        }

        /// <summary>
        /// 处理搜索词中的关键字
        /// </summary>
        /// <param name="terms">原始搜索词数组</param>
        /// <returns>提取关键字后的搜索词数组</returns>
        /// <remarks>使用ETStringPrefixSuffixProcessor移除前缀和后缀</remarks>
        string[] ProcessKeywords(string[] terms)
        {
            return terms.Select(term => ETStringPrefixSuffixProcessor.RemovePrefixAndSuffix(term)).ToArray();
        }

        /// <summary>
        /// 判断是否需要处理后缀
        /// </summary>
        /// <returns>需要处理后缀时返回true</returns>
        bool ShouldProcessSuffix()
        {
            return comboBoxSuffix.Text != "无"
                && comboBoxSuffix.Text.Length > 0
                && _suffixConfig.ContainsKey(comboBoxSuffix.Text);
        }

        /// <summary>
        /// 处理搜索词中的后缀
        /// </summary>
        /// <param name="terms">原始搜索词数组</param>
        /// <returns>处理后缀后的搜索词数组</returns>
        /// <remarks>根据后缀配置移除相应的前缀和后缀</remarks>
        string[] ProcessSuffix(string[] terms)
        {
            return terms.Select(term => ETStringPrefixSuffixProcessor.RemovePrefixAndSuffix(term)).ToArray();
        }

        /// <summary>
        /// 过滤空值并保存搜索词到集合
        /// </summary>
        /// <param name="terms">原始搜索词数组</param>
        /// <returns>过滤后的搜索词数组</returns>
        /// <remarks>移除空白项并转换为HashSet以提高查找性能</remarks>
        string[] FilterAndSaveSearchTerms(string[] terms)
        {
            // 过滤空白项
            string[] filteredTerms = terms.Where(term => !string.IsNullOrWhiteSpace(term)).ToArray();
            
            // 保存到搜索词集合
            _searchTerms = filteredTerms.ToHashset();
            
            return filteredTerms;
        }

        /// <summary>
        /// 显示搜索结果并应用格式化
        /// </summary>
        /// <param name="startRange">起始单元格范围</param>
        /// <exception cref="ETException">显示结果失败时抛出</exception>
        /// <remarks>
        /// 执行逻辑：标记范围 → 应用筛选 → 更新界面 → 恢复模式
        /// </remarks>
        void DisplaySearchResults(Range startRange)
        {
            try
            {
                // 如果有匹配结果，进行标记和筛选
                if (_matchedRanges != null && _matchedRanges.Any())
                {
                    // 合并匹配的范围并应用高亮格式
                    _markedRange = ETExcelExtensions.UnionRanges(_matchedRanges);
                    _markedRange?.Format条件格式警示色(EnumWarningColor.筛选色);

                    // 应用筛选规则
                    ApplyFilter(startRange);
                }

                // 更新用户界面显示
                UpdateUI(startRange);
                
                // 恢复Excel应用程序的正常模式
                ETExcelExtensions.SetAppNormalMode(true);
            }
            catch (Exception ex)
            {
                throw new ETException("显示搜索结果失败", "Excel结果显示", ex);
            }
        }

        /// <summary>
        /// 根据搜索方向应用相应的筛选规则
        /// </summary>
        /// <param name="startRange">起始单元格范围</param>
        /// <exception cref="ETException">应用筛选失败时抛出</exception>
        /// <remarks>行搜索应用行筛选，列搜索应用列筛选</remarks>
        void ApplyFilter(Range startRange)
        {
            try
            {
                // 根据搜索方向选择筛选方式
                switch (_searchDirection)
                {
                    case EnumRowColumn.Row:
                        ApplyRowFilter();
                        break;

                    case EnumRowColumn.Column:
                        ApplyColumnFilter(startRange);
                        break;
                }
            }
            catch (Exception ex)
            {
                throw new ETException("应用筛选失败", "Excel筛选", ex);
            }
        }

        /// <summary>
        /// 应用行筛选（隐藏不匹配的列）
        /// </summary>
        /// <exception cref="ETException">应用行筛选失败时抛出</exception>
        /// <remarks>隐藏选定范围的整列，显示匹配范围的整列</remarks>
        void ApplyRowFilter()
        {
            try
            {
                // 隐藏选定范围的整列
                if (_selectedRange != null)
                    _selectedRange.EntireColumn.Hidden = true;
                    
                // 显示匹配范围的整列
                if (_markedRange != null)
                    _markedRange.EntireColumn.Hidden = false;
            }
            catch (Exception ex)
            {
                throw new ETException("应用行筛选失败", "Excel行筛选", ex);
            }
        }

        /// <summary>
        /// 应用列筛选（基于颜色的自动筛选）
        /// </summary>
        /// <param name="startRange">起始单元格范围</param>
        /// <exception cref="ETException">应用列筛选失败时抛出</exception>
        /// <remarks>在筛选行上应用基于单元格颜色的自动筛选</remarks>
        void ApplyColumnFilter(Range startRange)
        {
            try
            {
                // 获取工作表的筛选行号
                int filterRow = startRange.Worksheet.GetWorksheetFilterRowNumber();
                if (filterRow == 0)
                {
                    MessageBox.Show("未设置筛选");
                    return;
                }
                
                // 获取筛选列号并应用颜色筛选
                int filterColumn = startRange.Cells[1, 1].Column;
                startRange
                    .Worksheet.Rows[filterRow]
                    .AutoFilter(
                        filterColumn,
                        EnumWarningColor.筛选色,
                        XlAutoFilterOperator.xlFilterCellColor
                    );
            }
            catch (Exception ex)
            {
                throw new ETException("应用列筛选失败", "Excel列筛选", ex);
            }
        }

        /// <summary>
        /// 更新用户界面显示信息
        /// </summary>
        /// <param name="startRange">起始单元格范围</param>
        /// <exception cref="ETException">更新界面失败时抛出</exception>
        /// <remarks>更新搜索位置、结果统计和匹配项目列表</remarks>
        void UpdateUI(Range startRange)
        {
            try
            {
                // 更新搜索位置信息显示
                label列.Text =
                    _searchDirection == EnumRowColumn.Row
                        ? $"行:{ETExcelExtensions.ConvertNumberToColumnName(startRange.Cells[1, 1].Row).ToUpper()}"
                        : $"列:{ETExcelExtensions.ConvertNumberToColumnName(startRange.Cells[1, 1].Column).ToUpper()}";

                // 更新结果统计标签
                tabPage存在.Text = $"存在: {_matchedRanges?.Count ?? 0}";
                tabPage不存在.Text = $"不存在: {_unmatchedItems?.Count ?? 0}";
                
                // 更新匹配和未匹配项目的文本显示
                textBox存在.Text = string.Join(Environment.NewLine, _matchedItems ?? Enumerable.Empty<string>());
                textBox不存在.Text = string.Join(Environment.NewLine, _unmatchedItems ?? Enumerable.Empty<string>());
            }
            catch (Exception ex)
            {
                throw new ETException("更新界面失败", "Excel界面更新", ex);
            }
        }

        /// <summary>
        /// 执行横向查找操作（公共接口）
        /// </summary>
        /// <param name="inputRange">输入的单元格范围</param>
        /// <param name="findText">要查找的文本</param>
        /// <exception cref="ETException">横向查找失败时抛出</exception>
        /// <remarks>
        /// 执行逻辑：验证参数 → 设置查找文本 → 激活工作表 → 执行查找 → 清理资源
        /// </remarks>
        public void Run横向查找(Range inputRange, string findText)
        {
            try
            {
                // 验证输入参数
                if (inputRange == null || string.IsNullOrEmpty(findText))
                    return;
                    
                // 设置查找文本并激活目标工作表
                textBox查找.Text = findText;
                inputRange.GetParent().Activate();

                // 执行横向查找（从右侧相邻单元格开始）
                PerformSearchAndFilter(inputRange.Cells[1, 1].Offset[0, 1], EnumRowColumn.Row);

                // 清理资源并关闭窗体
                _matchedRanges.Clear(); // 避免关闭时的提醒
                Close();
                Dispose();
            }
            catch (Exception ex)
            {
                throw new ETException("横向查找失败", "Excel横向查找", ex);
            }
        }

        /// <summary>
        /// 执行竖向查找操作（公共接口）
        /// </summary>
        /// <param name="inputRange">输入的单元格范围</param>
        /// <param name="findText">要查找的文本</param>
        /// <exception cref="ETException">竖向查找失败时抛出</exception>
        /// <remarks>
        /// 执行逻辑：验证参数 → 设置查找文本 → 激活工作表 → 执行查找 → 清理资源
        /// </remarks>
        public void Run竖向查找(Range inputRange, string findText)
        {
            try
            {
                // 验证输入参数
                if (inputRange == null || string.IsNullOrEmpty(findText))
                    return;
                    
                // 设置查找文本并激活目标工作表
                textBox查找.Text = findText;
                inputRange.GetParent().Activate();

                // 执行竖向查找（从下方相邻单元格开始）
                PerformSearchAndFilter(inputRange.Cells[1, 1].Offset[1, 0], EnumRowColumn.Column);

                // 清理资源并关闭窗体
                _matchedRanges.Clear(); // 避免关闭时的提醒
                Close();
                Dispose();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception($"执行竖向查找失败: {ex.Message}"));
                throw new ETException("执行竖向查找失败", ex);
            }
        }

        /// <summary>
        /// 筛选非空白单元格
        /// </summary>
        /// <param name="findDirection">查找方向（行/列）</param>
        /// <param name="outZero">是否排除零值</param>
        /// <exception cref="ETException">筛选操作失败时抛出</exception>
        /// <remarks>
        /// 执行逻辑：初始化 → 获取范围 → 查找非空单元格 → 显示结果
        /// </remarks>
        void 筛选非空白单元格(EnumRowColumn findDirection, bool outZero)
        {
            try
            {
                // 初始化搜索变量
                InitializeSearchVariables();
                Range startRange = ThisAddIn.ExcelApplication.Selection.Cells[1, 1];
                _searchDirection = findDirection;

                // 根据搜索方向获取目标范围
                Range targetRange =
                    _searchDirection == EnumRowColumn.Row
                        ? ETExcelExtensions.GetSelectionRow()
                        : ETExcelExtensions.GetSelectionColumn();
                        
                // 优化范围大小并设置选中范围
                targetRange = targetRange
                    .GetRangeAfterCell(startRange, _searchDirection)
                    .OptimizeRangeSize();
                _selectedRange = targetRange;

                // 查找非空白单元格
                _matchedRanges = new HashSet<Range>(
                    targetRange
                        .FindNonEmptyCells(out _matchedItems, out _columnVisibility, outZero)
                        .Cast<Range>()
                );

                // 显示查找结果
                DisplaySearchResults(startRange);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception($"筛选非空白单元格失败: {ex.Message}"));
                throw new ETException("筛选非空白单元格失败", ex);
            }
        }

        #endregion 搜索和筛选方法

        #region 事件处理方法

        /// <summary>
        /// 窗体加载事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <exception cref="ETException">窗体加载失败时抛出</exception>
        /// <remarks>执行逻辑：加载后缀配置 → 初始化下拉列表</remarks>
        void frm批量查找_Load(object sender, EventArgs e)
        {
            try
            {
                // 加载后缀配置文件
                InitializeSuffixConfig();
                
                // 初始化后缀下拉列表
                InitializeSuffixComboBox();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception($"窗体加载失败: {ex.Message}"));
                throw new ETException("窗体加载失败", ex);
            }
        }

        /// <summary>
        /// 初始化后缀配置字典
        /// </summary>
        /// <remarks>从配置文件加载后缀处理规则</remarks>
        void InitializeSuffixConfig()
        {
            _suffixConfig = ETConfig.ConfigFileToDictionary(
                ETConfig.GetConfigDirectory("批量查找后缀.config")
            );
        }

        /// <summary>
        /// 初始化后缀下拉列表控件
        /// </summary>
        /// <remarks>设置下拉列表样式并添加配置项</remarks>
        void InitializeSuffixComboBox()
        {
            // 清空并设置下拉列表样式
            comboBoxSuffix.Items.Clear();
            comboBoxSuffix.DropDownStyle = ComboBoxStyle.DropDownList;
            
            // 添加默认选项
            comboBoxSuffix.Items.Add("无");
            
            // 添加配置文件中的后缀类型
            foreach (string key in _suffixConfig.Keys)
            {
                comboBoxSuffix.Items.Add(key);
            }
        }

        /// <summary>
        /// 窗体关闭事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">窗体关闭事件参数</param>
        /// <remarks>检查筛选状态并确认是否关闭窗体</remarks>
        void frm批量查找_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                // 如果没有匹配结果，直接关闭
                if (_matchedRanges.Count <= 0)
                    return;

                // 提示用户确认关闭
                DialogResult result = MessageBox.Show(
                    @"处于筛选状态，是否关闭窗口？",
                    @"是否关闭",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question
                );
                
                // 用户选择不关闭时取消关闭操作
                if (result != DialogResult.Yes)
                    e.Cancel = true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception($"窗体关闭失败: {ex.Message}"));
                // 不抛出异常，避免影响窗体关闭
            }
        }

        /// <summary>
        /// "相同"单选按钮状态变更事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <exception cref="ETException">处理状态变更失败时抛出</exception>
        /// <remarks>根据选中状态控制关键字选项和后缀设置</remarks>
        void radioButton相同_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                // 相同模式下禁用关键字提取功能
                checkBox只取关键字.Enabled = !radioButton相同.Checked;
                
                // 重置后缀选择
                comboBoxSuffix.Text = "无";
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception($"处理单选按钮状态变更失败: {ex.Message}"));
                throw new ETException("处理单选按钮状态变更失败", ex);
            }
        }

        /// <summary>
        /// 清空菜单项点击事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <exception cref="ETException">清空操作失败时抛出</exception>
        /// <remarks>清空查找文本并重置界面状态</remarks>
        void 清空ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // 清空查找文本框
                textBox查找.Text = string.Empty;
                
                // 选择第一个标签页并重置标签文本
                tabControl设置.SelectTab(0);
                tabPage存在.Text = @"存在";
                tabPage不存在.Text = @"不存在";
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception($"清空操作失败: {ex.Message}"));
                throw new ETException("清空操作失败", ex);
            }
        }

        /// <summary>
        /// 取消标色后关闭按钮点击事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <exception cref="ETException">取消标色失败时抛出</exception>
        /// <remarks>清除高亮格式并清空匹配结果</remarks>
        void button取消标色后关闭_Click(object sender, EventArgs e)
        {
            try
            {
                // 激活当前工作表
                if (_currentWorksheet != null)
                    _currentWorksheet.Activate();
                    
                // 清除标记范围的条件格式
                if (_markedRange != null)
                    _markedRange.FormatConditions.Delete();
                    
                // 清空匹配范围集合
                _matchedRanges.Clear();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception($"取消标色失败: {ex.Message}"));
                throw new ETException("取消标色失败", ex);
            }
        }

        /// <summary>
        /// 提取选定单元格唯一值菜单项点击事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <exception cref="ETException">提取唯一值失败时抛出</exception>
        /// <remarks>从选定范围提取非空唯一值并显示在查找文本框中</remarks>
        void 提取选定单元格唯一值ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取选定范围并优化大小
                Range selectedRange = ETExcelExtensions.GetSelectionRange().OptimizeRangeSize();
                
                // 提取非空单元格的唯一值
                List<string> uniqueValues = selectedRange.ConvertRangeToStringList(true, true);
                if (uniqueValues == null || uniqueValues.Count == 0)
                {
                    MessageBox.Show(@"全部空白单元格");
                    return;
                }

                // 将唯一值显示在查找文本框中
                textBox查找.Text = string.Join(Environment.NewLine, uniqueValues);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception($"提取唯一值失败: {ex.Message}"));
                throw new ETException("提取唯一值失败", ex);
            }
        }

        /// <summary>
        /// 横向筛选非0值单元格菜单项点击事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <exception cref="ETException">横向筛选失败时抛出</exception>
        void 横向筛选非0值单元格ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                筛选非空白单元格(EnumRowColumn.Row, false);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception("横向筛选非0值失败"));
                throw new ETException("横向筛选非0值失败", ex);
            }
        }

        /// <summary>
        /// 横向筛选非空白单元格菜单项点击事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <exception cref="ETException">横向筛选失败时抛出</exception>
        void 横向筛选非空白单元格ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                筛选非空白单元格(EnumRowColumn.Row, true);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception("横向筛选非空白单元格失败"));
                throw new ETException("横向筛选非空白单元格失败", ex);
            }
        }

        /// <summary>
        /// 竖向筛选非0值单元格菜单项点击事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <exception cref="ETException">竖向筛选失败时抛出</exception>
        void 竖向筛选非0值单元格ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                筛选非空白单元格(EnumRowColumn.Column, false);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception("竖向筛选非0值失败"));
                throw new ETException("竖向筛选非0值失败", ex);
            }
        }

        /// <summary>
        /// 竖向筛选非空白单元格菜单项点击事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <exception cref="ETException">竖向筛选失败时抛出</exception>
        void 竖向筛选非空白单元格ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                筛选非空白单元格(EnumRowColumn.Column, true);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception("竖向筛选非空白单元格失败"));
                throw new ETException("竖向筛选非空白单元格失败", ex);
            }
        }

        /// <summary>
        /// 竖向筛选按钮点击事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>执行竖向（列方向）的搜索和筛选操作</remarks>
        void button竖向筛选_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取当前选中的起始单元格
                Range startRange = ThisAddIn.ExcelApplication.Selection.Cells[1, 1];
                
                // 执行竖向搜索和筛选
                PerformSearchAndFilter(startRange, EnumRowColumn.Column);

                // 启用自动缩放功能（已注释）
                //collapseBehavior.IsEnabled = true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception("竖向筛选失败"));
                MessageBox.Show($"竖向筛选失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);

                // 禁用自动缩放功能（已注释）
                //collapseBehavior.IsEnabled = false;
            }
        }

        /// <summary>
        /// 横向筛选按钮点击事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>执行横向（行方向）的搜索和筛选操作</remarks>
        void button横向筛选_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取当前选中的起始单元格
                Range startRange = ThisAddIn.ExcelApplication.Selection.Cells[1, 1];

                // 执行横向搜索和筛选
                PerformSearchAndFilter(startRange, EnumRowColumn.Row);

                // 启用自动缩放功能（已注释）
                //collapseBehavior.IsEnabled = true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception("横向筛选失败"));
                MessageBox.Show($"横向筛选失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);

                // 禁用自动缩放功能（已注释）
                //collapseBehavior.IsEnabled = false;
            }
        }

        /// <summary>
        /// 去筛选按钮点击事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>根据筛选方向恢复原始的显示状态并清除格式</remarks>
        void button去筛选_Click(object sender, EventArgs e)
        {
            try
            {
                // 验证工作表是否可用
                if (_currentWorksheet == null)
                    return;

                // 根据搜索方向恢复筛选状态
                if (_searchDirection == EnumRowColumn.Row)
                {
                    // 恢复列的可见性状态
                    foreach (KeyValuePair<int, bool> kvp in _columnVisibility)
                    {
                        _currentWorksheet.Columns[kvp.Key].Hidden = kvp.Value;
                    }
                }
                else
                {
                    // 移除自动筛选
                    int filterRowIndex = _currentWorksheet.GetWorksheetFilterRowNumber();
                    if (filterRowIndex > 0)
                    {
                        _currentWorksheet.Rows[filterRowIndex].AutoFilter(_selectedRange.Cells[1, 1].Column);
                    }
                }

                // 清除标记范围的格式条件
                if (_markedRange != null)
                {
                    _markedRange.FormatConditions.Delete();
                }
                
                // 清空匹配范围集合
                _matchedRanges.Clear();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(new Exception("取消筛选失败"));
                MessageBox.Show($"取消筛选失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion 事件处理方法

        #region 辅助方法

        /// <summary>
        /// 检查筛选数据是否可用
        /// </summary>
        /// <returns>筛选数据可用时返回true</returns>
        /// <remarks>验证所需的数据和范围是否已正确初始化</remarks>
        bool IsFilterDataAvailable()
        {
            return _selectedRange != null
                && _currentWorksheet != null
                && _matchedRanges != null
                && _matchedRanges.Count > 0;
        }

        /// <summary>
        /// 移除横向筛选效果
        /// </summary>
        /// <remarks>根据保存的可见性状态恢复列的显示或隐藏</remarks>
        void RemoveHorizontalFilter()
        {
            foreach (KeyValuePair<int, bool> kvp in _columnVisibility)
                _currentWorksheet.Columns[kvp.Key].Hidden = kvp.Value;
        }

        /// <summary>
        /// 移除纵向筛选效果
        /// </summary>
        /// <remarks>移除应用于筛选行的自动筛选</remarks>
        void RemoveVerticalFilter()
        {
            int filterRowIndex = _currentWorksheet.GetWorksheetFilterRowNumber();
            if (filterRowIndex > 0)
                _currentWorksheet.Rows[filterRowIndex].AutoFilter(_selectedRange.Cells[1, 1].Column);
        }

        /// <summary>
        /// 清除格式并重置匹配范围
        /// </summary>
        /// <remarks>删除标记范围的格式条件并清空匹配范围集合</remarks>
        void ClearFormatAndResetFoundRanges()
        {
            _markedRange?.FormatConditions.Delete();
            _matchedRanges.Clear();
        }

        #endregion 辅助方法

        #region 构造函数

        /// <summary>
        /// 初始化批量查找窗体
        /// </summary>
        /// <remarks>初始化组件并配置自动缩放行为（已注释）</remarks>
        public frm批量查找()
        {
            InitializeComponent();

            // 初始化自动缩放行为（已注释）
            //collapseBehavior = new AutoCollapseWindowBehavior(this);
            //collapseBehavior.IsEnabled = false;  // 默认禁用自动缩放功能
            //collapseBehavior.SetExpandedSize(305, 457);  // 设置展开时的尺寸
            //collapseBehavior.CollapsedWidth = 150;  // 设置收缩时的宽度
            //Height = 427;
        }

        #endregion 构造函数
    }
}
