<?xml version="1.0" encoding="utf-8"?>
<!--设备侧-->
<metas>
    <!-- DSLAMDEVICE 设备 -->
    <ObjMeta objectType="DSLAMDEVICE" label="DSLAM设备"
             typeActions="" extraActions="expgrid,expall"
             itemActions="view">
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="room_outdooraddress" label="所属机房"/>
            </row>
            <row>
                <field name="ADDRESS_ID" label="标准地址"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:2000px;" autoLoad="false">
            <field name="CODE" label="编码" width="250px"/>
            <field name="NAME" label="名称" width="250px"/>
            <field name="site" label="局站" getLabel="row.site_value.NAME"/>
            <field name="room_outdooraddress" label="所属机房"/>
            <field name="BELONG_SPECIALITY_ID" label="所属专业"/>
            <field name="OWNER_NET_ID" label="所属网络ID"/>
            <field name="NETWORK_LAYER_ID" label="网络层次"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="UPCONTACT_WAY_ID" label="上行方式ID"/>
            <field name="BAC_ADDRESS" label="BAC地址"/>
            <field name="IS_TYM_ID" label="是否天翼猫"/>
            <field name="DSLAM_TYPE_ID" label="DSLAM类型" width="100px"/>
            <field name="ACCESS_IP" label="接入服务器IP（163域）" width="150px"/>
            <field name="BIZ_TYPE_ID" label="运行类型"/>
            <field name="region" label="所属区域"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
        </grid>
        <action type="script" name="view" label="查看" label-en="View">
            <![CDATA[
                var telanturl=_context.clientConf.telantUrl;
                var dslamurl = "com.ccssoft.inventory.web.equipment.view.DeviceDetail.d7?openMode=modify&metaClassName=DSLAMDEVICE&filter=pipe&entityId="+_actionContext.params.id+"&gisCallParam=NDQxMDAwMDAwMDAwMDAxMDA0MTU5OTM5";
                window.open(telanturl+dslamurl);
            ]]>
        </action>
    </ObjMeta>

    <!-- PSTNDEVICE 设备 -->
    <ObjMeta objectType="PSTNDEVICE" label="PSTN设备"
             typeActions="" extraActions="expgrid,expall"
             itemActions="view">
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="room_outdooraddress" label="所属机房"/>
            </row>
            <row>
                <field name="ADDRESS_ID" label="标准地址"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:2000px;" autoLoad="false">
            <field name="CODE" label="编码" width="250px"/>
            <field name="NAME" label="名称" width="250px"/>
            <field name="site" label="局站" getLabel="row.site_value.NAME"/>
            <field name="room_outdooraddress" label="所属机房"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="USING_STATE_ID" lable="业务状态"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="IS_HOME_ID" label="母局模块局"/>
            <field name="SWITCH_ATTR_ID" label="交换机特性"/>
            <field name="region" label="所属区域"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
        </grid>
        <action type="script" name="view" label="查看" label-en="View">
            <![CDATA[
                var telanturl=_context.clientConf.telantUrl;
                var pstnurl = "com.ccssoft.inventory.web.equipment.view.DeviceDetail.d7?openMode=modify&metaClassName=PSTNDEVICE&filter=pipe&entityId="+_actionContext.params.id+"&gisCallParam=NDQxMDAwMDAwMDAwMDAxMDA0MTU5OTM5";
                window.open(telanturl+pstnurl);
            ]]>
        </action>
    </ObjMeta>

    <!-- AG 设备 -->
    <ObjMeta objectType="AG" label="AG设备"
             typeActions="" extraActions="expgrid,expall"
             itemActions="view">
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="room_outdooraddress" label="所属机房"/>
            </row>
            <row>
                <field name="ADDRESS_ID" label="标准地址"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:2000px;" autoLoad="false">
            <field name="CODE" label="编码" width="250px"/>
            <field name="NAME" label="名称" width="250px"/>
            <field name="site" label="局站" getLabel="row.site_value.NAME"/>
            <field name="room_outdooraddress" label="所属机房"/>
            <field name="BELONG_SPECIALITY_ID" label="所属专业"/>
            <field name="OWNER_NET_ID" label="所属网络ID"/>
            <field name="NETWORK_LAYER_ID" label="网络层次"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="USING_STATE_ID" lable="业务状态"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="IS_IMS_ID" label="是否IMS"/>
            <field name="SWITCH_ATTR_ID" label="交换机特性"/>
            <field name="region" label="所属区域"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
        </grid>
        <action type="script" name="view" label="查看" label-en="View">
            <![CDATA[
                var telanturl=_context.clientConf.telantUrl;
                var agurl = "com.ccssoft.inventory.web.equipment.view.DeviceDetail.d7?openMode=modify&metaClassName=AG&filter=pipe&entityId="+_actionContext.params.id+"&gisCallParam=NDQxMDAwMDAwMDAwMDAxMDA0MTU5OTM5";
                window.open(telanturl+agurl);
            ]]>
        </action>
    </ObjMeta>

    <!-- ROUTER 设备 -->
    <ObjMeta objectType="ROUTER" label="路由器"
             typeActions="" extraActions="expgrid,expall"
             itemActions="view">
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="room_outdooraddress" label="机房/安装点"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>

            <row>
                <field name="ADDRESS_ID" label="标准地址"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="region" label="所属区域"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县"/>
                <field name="marketingarea" label="划小营销区"/>
            </row>
          <field name="PROJECT_STATUS_ID" label="工程状态" />
          <row>
                <field name="project" label="工程名称"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:2000px;" autoLoad="false">
            <field name="CODE" label="编码" width="250px"/>
            <field name="NAME" label="名称" width="250px"/>
            <field name="site" label="局站" getLabel="row.site_value.NAME"/>
            <field name="room_outdooraddress" label="所属机房"/>
            <field name="OWNER_NET_ID" label="所属网络"/>
            <field name="NETWORK_LAYER_ID" label="网络层次"/>
            <field name="SECURITY_LEVEL_ID" label="设备安全等级"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="NM_IP" label="网管IP"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="region" label="所属区域"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
        </grid>
        <action type="script" name="view" label="查看" label-en="View">
            <![CDATA[
                var telanturl=_context.clientConf.telantUrl;
                var routerurl = "com.ccssoft.inventory.web.equipment.view.DeviceDetail.d7?openMode=modify&metaClassName=ROUTER&filter=pipe&entityId="+_actionContext.params.id+"&gisCallParam=NDQxMDAwMDAwMDAwMDAxMDA0MTU5OTM5";
                window.open(telanturl+routerurl);
            ]]>
        </action>
    </ObjMeta>

    <!-- eNodeB 设备 -->
    <ObjMeta objectType="eNodeB" label="eNodeB"
             typeActions="" extraActions="expgrid,expall"
             itemActions="view">
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="room_outdooraddress" label="机房/安装点"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="ADDRESS_ID" label="标准地址"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="region" label="所属区域"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县"/>
                <field name="marketingarea" label="划小营销区"/>
            </row>
          <field name="PROJECT_STATUS_ID" label="工程状态" />
          <row>
                <field name="project" label="工程名称"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:2000px;" autoLoad="false">
            <field name="CODE" label="编码" width="250px"/>
            <field name="NAME" label="名称" width="250px"/>
            <field name="site" label="局站" getLabel="row.site_value.NAME"/>
            <field name="room_outdooraddress" label="所属机房"/>
            <field name="OWNER_NET_ID" label="所属网络"/>
            <field name="NETWORK_LAYER_ID" label="网络层次"/>
            <field name="SECURITY_LEVEL_ID" label="设备安全等级"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="NM_IP" label="网管IP"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="region" label="所属区域"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
        </grid>
        <action type="script" name="view" label="查看" label-en="View">
            <![CDATA[
                var telanturl=_context.clientConf.telantUrl;
                var eNodeBurl = "com.ccssoft.inventory.web.equipment.view.DeviceDetail.d7?openMode=modify&metaClassName=eNodeB&filter=pipe&entityId="+_actionContext.params.id+"&gisCallParam=NDQxMDAwMDAwMDAwMDAxMDA0MTU5OTM5";
                window.open(telanturl+eNodeBurl);
            ]]>
        </action>
    </ObjMeta>

    <!-- CDMABTS 设备 -->
    <ObjMeta objectType="CDMABTS" label="C网基站"
             typeActions="" extraActions="expgrid,expall"
             itemActions="view">
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="room_outdooraddress" label="机房/安装点"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="ADDRESS_ID" label="标准地址"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="region" label="所属区域"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县"/>
                <field name="marketingarea" label="划小营销区"/>
            </row>
          <field name="PROJECT_STATUS_ID" label="工程状态" />
          <row>
                <field name="project" label="工程名称"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:2000px;" autoLoad="false">
            <field name="CODE" label="编码" width="250px"/>
            <field name="NAME" label="名称" width="250px"/>
            <field name="site" label="局站" getLabel="row.site_value.NAME"/>
            <field name="room_outdooraddress" label="所属机房"/>
            <field name="OWNER_NET_ID" label="所属网络"/>
            <field name="NETWORK_LAYER_ID" label="网络层次"/>
            <field name="SECURITY_LEVEL_ID" label="设备安全等级"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="NM_IP" label="网管IP"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="region" label="所属区域"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
        </grid>
        <action type="script" name="view" label="查看" label-en="View">
            <![CDATA[
                var telanturl=_context.clientConf.telantUrl;
                var cdmabtsurl = "com.ccssoft.inventory.web.equipment.view.DeviceDetail.d7?openMode=modify&metaClassName=CDMABTS&filter=pipe&entityId="+_actionContext.params.id+"&gisCallParam=NDQxMDAwMDAwMDAwMDAxMDA0MTU5OTM5";
                window.open(telanturl+cdmabtsurl);
            ]]>
        </action>
    </ObjMeta>

    <!-- BAS 设备 -->
    <ObjMeta objectType="BAS" label="宽带接入设备"
             typeActions="" extraActions="expgrid,expall"
             itemActions="view">
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="room_outdooraddress" label="机房/安装点"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="ADDRESS_ID" label="标准地址"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="region" label="所属区域"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县"/>
                <field name="marketingarea" label="划小营销区"/>
            </row>
          <field name="PROJECT_STATUS_ID" label="工程状态" />
          <row>
                <field name="project" label="工程名称"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:2000px;" autoLoad="false">
            <field name="CODE" label="编码" width="250px"/>
            <field name="NAME" label="名称" width="250px"/>
            <field name="site" label="局站" getLabel="row.site_value.NAME"/>
            <field name="room_outdooraddress" label="所属机房"/>
            <field name="OWNER_NET_ID" label="所属网络"/>
            <field name="NETWORK_LAYER_ID" label="网络层次"/>
            <field name="SECURITY_LEVEL_ID" label="设备安全等级"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="NM_IP" label="网管IP"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="region" label="所属区域"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
        </grid>
        <action type="script" name="view" label="查看" label-en="View">
            <![CDATA[
                var telanturl=_context.clientConf.telantUrl;
                var basurl = "com.ccssoft.inventory.web.equipment.view.DeviceDetail.d7?openMode=modify&metaClassName=BAS&filter=pipe&entityId="+_actionContext.params.id+"&gisCallParam=NDQxMDAwMDAwMDAwMDAxMDA0MTU5OTM5";
                window.open(telanturl+basurl);
            ]]>
        </action>
    </ObjMeta>

</metas>