using HyAssistant.ChinaTowerDownload.Models;
using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.Threading.Tasks;

namespace HyAssistant.ChinaTowerDownload.Data
{
    /// <summary>
    /// <see cref="T:HyAssistant.ChinaTowerDownload.Data.IPhotoRepository"/> 接口的SQLite数据库实现。负责照片信息的持久化操作，包括数据库初始化、照片的增删改查等。
    /// </summary>
    public class PhotoRepository : IPhotoRepository
    {
        private readonly string _connectionString;

        /// <summary>
        /// 使用指定的数据库连接字符串初始化 <see cref="T:HyAssistant.ChinaTowerDownload.Data.PhotoRepository"/> 类的新实例。
        /// </summary>
        /// <param name="connectionString">SQLite数据库的连接字符串。例如: "Data Source=photos.db;Version=3;"</param>
        public PhotoRepository(string connectionString)
        {
            _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
        }

        /// <summary>
        /// 异步初始化数据库表结构，包括创建 `stationphoto` 表和相关索引（如果它们不存在）。
        /// </summary>
        /// <returns>一个表示异步操作的任务，其结果为一个布尔值，指示数据库初始化是否成功。</returns>
        public async Task<bool> InitializeDatabaseAsync()
        {
            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    // 创建照片信息表
                    var createTableSql = @"
                        CREATE TABLE IF NOT EXISTS stationphoto (
                            ID INTEGER PRIMARY KEY AUTOINCREMENT,
                            station_id TEXT(200) NOT NULL,
                            station_name TEXT(200) NOT NULL,
                            url TEXT(200) NOT NULL,
                            photoId TEXT(200) NOT NULL,
                            serverfileName TEXT(200) NOT NULL,
                            createTime INTEGER DEFAULT 0,
                            downloadTime INTEGER DEFAULT 0
                        );";

                    using (var command = new SQLiteCommand(createTableSql, connection))
                    {
                        await command.ExecuteNonQueryAsync();
                    }

                    // 创建索引
                    await CreateIndexesAsync(connection);
                }

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 异步创建 `stationphoto` 表的数据库索引，以优化查询性能。
        /// </summary>
        /// <param name="connection">已打开的SQLite数据库连接。</param>
        /// <returns>表示异步操作的任务。</returns>
        private async Task CreateIndexesAsync(SQLiteConnection connection)
        {
            var indexes = new[]
            {
                "CREATE INDEX IF NOT EXISTS idx_stationphoto_download ON stationphoto(downloadTime);",
                "CREATE INDEX IF NOT EXISTS idx_stationphoto_station ON stationphoto(station_id);",
                "CREATE INDEX IF NOT EXISTS idx_stationphoto_photoid ON stationphoto(photoId);",
                "CREATE INDEX IF NOT EXISTS idx_stationphoto_filename ON stationphoto(serverfileName);",
                "CREATE INDEX IF NOT EXISTS idx_stationphoto_create_time ON stationphoto(createTime);",
                "CREATE INDEX IF NOT EXISTS idx_stationphoto_station_download ON stationphoto(station_id, downloadTime);",
                "CREATE INDEX IF NOT EXISTS idx_stationphoto_station_filename ON stationphoto(station_id, serverfileName);"
            };

            foreach (var indexSql in indexes)
            {
                try
                {
                    using (var command = new SQLiteCommand(indexSql, connection))
                    {
                        await command.ExecuteNonQueryAsync();
                    }
                }
                catch
                {
                    // 索引创建失败不影响主要功能
                }
            }
        }

        /// <summary>
        /// 异步获取所有标记为未下载（`downloadTime` 为 0 或 NULL）的照片信息。
        /// </summary>
        /// <returns>一个表示异步操作的任务，其结果是 <see cref="T:System.Collections.Generic.List`1"/> 集合，包含所有未下载的照片摘要信息。如果没有任何未下载的照片，则返回一个空列表。</returns>
        public async Task<List<PhotoInfoExcerpt>> GetUndownloadedPhotosAsync()
        {
            var photos = new List<PhotoInfoExcerpt>();

            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var sql = "SELECT * FROM stationphoto WHERE downloadTime = 0 OR downloadTime IS NULL ORDER BY station_name, createTime DESC";

                    using (var command = new SQLiteCommand(sql, connection))
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            photos.Add(MapToPhotoInfo(reader));
                        }
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误但不抛出异常
            }

            return photos;
        }

        /// <summary>
        /// 异步获取指定站点ID的所有照片信息。
        /// </summary>
        /// <param name="stationId">要查询的站点的唯一标识符。</param>
        /// <returns>一个表示异步操作的任务，其结果是 <see cref="T:System.Collections.Generic.List`1"/> 集合，包含指定站点的所有照片摘要信息。如果未找到指定站点的照片或 `stationId` 为空，则返回一个空列表。</returns>
        public async Task<List<PhotoInfoExcerpt>> GetPhotosByStationAsync(string stationId)
        {
            var photos = new List<PhotoInfoExcerpt>();

            if (string.IsNullOrWhiteSpace(stationId))
                return photos;

            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var sql = "SELECT * FROM stationphoto WHERE station_id = @stationId ORDER BY createTime DESC";

                    using (var command = new SQLiteCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@stationId", stationId);

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                photos.Add(MapToPhotoInfo(reader));
                            }
                        }
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误但不抛出异常
            }

            return photos;
        }

        /// <summary>
        /// 异步获取所有标记为下载失败（`downloadTime` 为 -1）的照片信息。
        /// </summary>
        /// <returns>一个表示异步操作的任务，其结果是 <see cref="T:System.Collections.Generic.List`1"/> 集合，包含所有下载失败的照片摘要信息。如果没有任何下载失败的照片，则返回一个空列表。</returns>
        public async Task<List<PhotoInfoExcerpt>> GetFailedPhotosAsync()
        {
            var photos = new List<PhotoInfoExcerpt>();

            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var sql = "SELECT * FROM stationphoto WHERE downloadTime = -1 ORDER BY createTime DESC";

                    using (var command = new SQLiteCommand(sql, connection))
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            photos.Add(MapToPhotoInfo(reader));
                        }
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误但不抛出异常
            }

            return photos;
        }

        /// <summary>
        /// 异步批量插入照片信息到数据库。在插入前会检查照片是否已存在，如果存在则跳过。
        /// </summary>
        /// <param name="photos">要插入的 <see cref="T:System.Collections.Generic.List`1"/> 集合，包含照片摘要信息。</param>
        /// <returns>一个表示异步操作的任务，其结果是成功插入的记录数量。</returns>
        public async Task<int> InsertPhotosAsync(List<PhotoInfoExcerpt> photos)
        {
            if (photos == null || photos.Count == 0)
                return 0;

            int insertedCount = 0;

            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var transaction = connection.BeginTransaction())
                    {
                        foreach (var photo in photos)
                        {
                            // 检查是否已存在
                            if (await PhotoExistsInternalAsync(connection, photo.station_id, photo.serverfileName))
                                continue;

                            // 插入新记录
                            var sql = @"
                                INSERT INTO stationphoto (station_id, station_name, url, photoId, serverfileName, createTime, downloadTime) 
                                VALUES (@stationId, @stationName, @url, @photoId, @serverfileName, @createTime, @downloadTime)";

                            using (var command = new SQLiteCommand(sql, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@stationId", photo.station_id);
                                command.Parameters.AddWithValue("@stationName", photo.station_name);
                                command.Parameters.AddWithValue("@url", photo.url);
                                command.Parameters.AddWithValue("@photoId", photo.photoId);
                                command.Parameters.AddWithValue("@serverfileName", photo.serverfileName);
                                command.Parameters.AddWithValue("@createTime", photo.createTime);
                                command.Parameters.AddWithValue("@downloadTime", photo.downloadTime);

                                if (await command.ExecuteNonQueryAsync() > 0)
                                    insertedCount++;
                            }
                        }

                        transaction.Commit();
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误但不抛出异常
            }

            return insertedCount;
        }

        /// <summary>
        /// 异步更新指定照片的下载状态。
        /// </summary>
        /// <param name="photoId">要更新状态的照片的唯一标识符。</param>
        /// <param name="downloadTime">照片的下载时间戳。0表示未下载，-1表示下载失败，大于0表示下载成功的时间戳。</param>
        /// <returns>一个表示异步操作的任务，其结果为一个布尔值，指示下载状态是否成功更新。</returns>
        public async Task<bool> UpdateDownloadStatusAsync(long photoId, long downloadTime)
        {
            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var sql = "UPDATE stationphoto SET downloadTime = @downloadTime WHERE photoId = @photoId";

                    using (var command = new SQLiteCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@photoId", photoId);
                        command.Parameters.AddWithValue("@downloadTime", downloadTime);

                        return await command.ExecuteNonQueryAsync() > 0;
                    }
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 异步批量更新多张照片的下载状态。
        /// </summary>
        /// <param name="updates">一个 <see cref="T:System.Collections.Generic.Dictionary`2"/>，其中键是照片的唯一标识符（photoId），值是对应的下载时间戳（0表示未下载，-1表示下载失败，大于0表示下载成功的时间戳）。</param>
        /// <returns>一个表示异步操作的任务，其结果是成功更新的记录数量。</returns>
        public async Task<int> BatchUpdateDownloadStatusAsync(Dictionary<long, long> updates)
        {
            if (updates == null || updates.Count == 0)
                return 0;

            int updatedCount = 0;

            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var transaction = connection.BeginTransaction())
                    {
                        var sql = "UPDATE stationphoto SET downloadTime = @downloadTime WHERE photoId = @photoId";

                        using (var command = new SQLiteCommand(sql, connection, transaction))
                        {
                            foreach (var update in updates)
                            {
                                command.Parameters.Clear();
                                command.Parameters.AddWithValue("@photoId", update.Key);
                                command.Parameters.AddWithValue("@downloadTime", update.Value);

                                if (await command.ExecuteNonQueryAsync() > 0)
                                    updatedCount++;
                            }
                        }

                        transaction.Commit();
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误但不抛出异常
            }

            return updatedCount;
        }

        /// <summary>
        /// 异步获取所有照片的统计信息，包括总数、已下载、未下载和下载失败的照片数量。
        /// </summary>
        /// <returns>一个表示异步操作的任务，其结果是 <see cref="T:HyAssistant.ChinaTowerDownload.Models.PhotoStatistics"/> 对象，包含照片的统计数据。</returns>
        public async Task<PhotoStatistics> GetPhotoStatisticsAsync()
        {
            var stats = new PhotoStatistics();

            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var sql = @"
                        SELECT 
                            COUNT(*) as total,
                            SUM(CASE WHEN downloadTime > 0 THEN 1 ELSE 0 END) as downloaded,
                            SUM(CASE WHEN downloadTime = 0 OR downloadTime IS NULL THEN 1 ELSE 0 END) as undownloaded,
                            SUM(CASE WHEN downloadTime = -1 THEN 1 ELSE 0 END) as failed
                        FROM stationphoto";

                    using (var command = new SQLiteCommand(sql, connection))
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            stats.TotalPhotos = reader.GetInt32(0);
                            stats.DownloadedPhotos = reader.GetInt32(1);
                            stats.PendingPhotos = reader.GetInt32(2);
                            stats.FailedPhotos = reader.GetInt32(3);
                        }
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误但不抛出异常
            }

            return stats;
        }

        /// <summary>
        /// 异步获取指定站点ID的照片统计信息，包括总数、已下载、未下载和下载失败的照片数量。
        /// </summary>
        /// <param name="stationId">要查询的站点的唯一标识符。</param>
        /// <returns>一个表示异步操作的任务，其结果是 <see cref="T:HyAssistant.ChinaTowerDownload.Models.PhotoStatistics"/> 对象，包含指定站点的照片统计数据。如果 `stationId` 为空，则返回一个空的统计对象。</returns>
        public async Task<PhotoStatistics> GetPhotoStatisticsByStationAsync(string stationId)
        {
            var stats = new PhotoStatistics();

            if (string.IsNullOrWhiteSpace(stationId))
                return stats;

            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var sql = @"
                        SELECT 
                            COUNT(*) as total,
                            SUM(CASE WHEN downloadTime > 0 THEN 1 ELSE 0 END) as downloaded,
                            SUM(CASE WHEN downloadTime = 0 OR downloadTime IS NULL THEN 1 ELSE 0 END) as undownloaded,
                            SUM(CASE WHEN downloadTime = -1 THEN 1 ELSE 0 END) as failed
                        FROM stationphoto 
                        WHERE station_id = @stationId";

                    using (var command = new SQLiteCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@stationId", stationId);

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                stats.TotalPhotos = reader.GetInt32(0);
                                stats.DownloadedPhotos = reader.GetInt32(1);
                                stats.PendingPhotos = reader.GetInt32(2);
                                stats.FailedPhotos = reader.GetInt32(3);
                            }
                        }
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误但不抛出异常
            }

            return stats;
        }

        /// <summary>
        /// 异步检查指定站点和服务器文件名的照片是否存在于数据库中。
        /// </summary>
        /// <param name="stationId">站点的唯一标识符。</param>
        /// <param name="serverFileName">服务器上照片的文件名。</param>
        /// <returns>一个表示异步操作的任务，如果照片存在则结果为 <c>true</c>，否则为 <c>false</c>。</returns>
        public async Task<bool> PhotoExistsAsync(string stationId, string serverFileName)
        {
            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    return await PhotoExistsInternalAsync(connection, stationId, serverFileName);
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 内部异步方法：检查指定站点和服务器文件名的照片是否存在于数据库中。
        /// </summary>
        /// <param name="connection">已打开的SQLite数据库连接。</param>
        /// <param name="stationId">站点的唯一标识符。</param>
        /// <param name="serverFileName">服务器上照片的文件名。</param>
        /// <returns>一个表示异步操作的任务，如果照片存在则结果为 <c>true</c>，否则为 <c>false</c>。</returns>
        private async Task<bool> PhotoExistsInternalAsync(SQLiteConnection connection, string stationId, string serverFileName)
        {
            using (var command = new SQLiteCommand("SELECT COUNT(*) FROM stationphoto WHERE station_id = @stationId AND serverfileName = @serverFileName", connection))
            {
                command.Parameters.AddWithValue("@stationId", stationId);
                command.Parameters.AddWithValue("@serverFileName", serverFileName);
                return Convert.ToInt32(await command.ExecuteScalarAsync()) > 0;
            }
        }

        /// <summary>
        /// 异步删除指定站点的所有照片记录。
        /// </summary>
        /// <param name="stationId">要删除照片记录的站点的唯一标识符。</param>
        /// <returns>一个表示异步操作的任务，其结果是成功删除的记录数量。</returns>
        public async Task<int> DeletePhotosByStationAsync(string stationId)
        {
            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SQLiteCommand("DELETE FROM stationphoto WHERE station_id = @stationId", connection))
                    {
                        command.Parameters.AddWithValue("@stationId", stationId);
                        return await command.ExecuteNonQueryAsync();
                    }
                }
            }
            catch (Exception)
            {
                return 0;
            }
        }

        /// <summary>
        /// 异步重置所有下载失败的照片状态（downloadTime = -1）为未下载状态（downloadTime = 0）。
        /// </summary>
        /// <returns>一个表示异步操作的任务，其结果是成功重置的记录数量。</returns>
        public async Task<int> ResetFailedPhotosAsync()
        {
            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SQLiteCommand("UPDATE stationphoto SET downloadTime = 0 WHERE downloadTime = -1", connection))
                    {
                        return await command.ExecuteNonQueryAsync();
                    }
                }
            }
            catch (Exception)
            {
                return 0;
            }
        }

        /// <summary>
        /// 将数据库读取器中的当前记录映射为 <see cref="T:HyAssistant.ChinaTowerDownload.Models.PhotoInfoExcerpt"/> 对象。
        /// </summary>
        /// <param name="reader">包含照片信息数据的 <see cref="T:System.Data.Common.DbDataReader"/> 实例。</param>
        /// <returns>一个包含映射后照片信息的 <see cref="T:HyAssistant.ChinaTowerDownload.Models.PhotoInfoExcerpt"/> 对象。</returns>
        private PhotoInfoExcerpt MapToPhotoInfo(System.Data.Common.DbDataReader reader)
        {
            return new PhotoInfoExcerpt
            {
                station_id = reader["station_id"].ToString(),
                station_name = reader["station_name"].ToString(),
                url = reader["url"].ToString(),
                photoId = reader["photoId"].ToString(),
                serverfileName = reader["serverfileName"].ToString(),
                createTime = Convert.ToInt64(reader["createTime"]),
                downloadTime = Convert.ToInt64(reader["downloadTime"])
            };
        }
    }
}
