<?xml version="1.0" encoding="utf-8"?>
<!--电缆网-->
<metas>
    <!-- MDF 配线架 -->
    <ObjMeta objectType="MDF" autoReload="true" needgeom="false"
             typeActions="add,reset,templates,remove"
             itemActions="locate,modify,remove,assetAttribute,filesman"
             moreItemActions="connview,echengduan,deviceOccupyRate,invokeLogicDiagram,invokeRouteDiagram">
        <action type="workspace" name="templates" label="模板管理" label-en="Template management" title="配线架模板"
                title-en="MDF Template" objectType="MDF.TEMPLATE" icon="columns"/>
        <action type="workspace" name="connview" label="端子图" label-en="Panel Diagram" icon="delicious"
                extra="_ui_class:gray" title="端子面板图-${NAME}" title-en="Panel Diagram-${NAME}"
                url="econnector.html?objecttype=${objectType}&amp;id=${id}" permission="ELECTRIC_MANAGE"/>
        <action type="workspace" id="connectSection" label="关联电缆" title="${NAME}-关联电缆" objectType="ECABLE"
                options='{"baseParams": {"adevice": "${id}"}}'/>
        <action name="echengduan" label="成端管理" type="script">
            <![CDATA[
             var dev = _actionContext.params;
            _bean.action('DEVICE', 'queryForEconnector', {id: dev.id, objecttype: dev.objectType}).then(function (data) {
                _device = data;

                _context.doAction({
                    type: "workspace",
                    title: "成端管理",
                    url: "modules/econnector/econnectorManager.html"
                }, {device: _device}, "成端管理");
            });
        ]]>
        </action>
        <!--查询条件-->
        <form name="search" labelWidth="6em">
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="room_outdooraddress" label="机房/安装点"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
                <field name="DISMANTLED_STATE_ID" label='退网状态'/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" baseParams="{&quot;_solr&quot;:true}"
                       dropdownOptions="titleField:FULL_NAME"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="region" label="所属区域"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
                <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            </row>
            <row>
                <field name="project" label="工程名称" dropdownOptions="titleField:NAME"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="ROW_NO" label='行号'/>
                <field name="COL_NO" label='列号'/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="site" required="true" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" required="true" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" baseParams="{&quot;_solr&quot;:true}"
                       dropdownOptions="titleField:FULL_NAME"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="ADDRESS_ID" label="地址ID" readOnly="true"/>
                <field name="CAPACITY" label='标称容量' required="true"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" default="80204666"/>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
                <field name="OWNER_NET_ID" label="网络类型" default="80204561"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
                <field name="DISTRICT" label="所属小区" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="KEEP_LINE" label="保留线对"/>
                <field name="ADDRESS_DESC" label="具体位置" orderBy="false"/>
            </row>
            <row>
                <field name="model" label="型号"/>
                <field name="LENGTH" label="长度(米)"/>
            </row>
            <row>
                <field name="WIDTH" label="宽度(米)"/>
                <field name="HEIGHT" label="高度(米)"/>
            </row>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="IS_USERMDF_ID" label="是否用户MDF"/>
            </row>
            <row>
                <field name="DEPRECIATION" label="折旧年限(年)"/>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
            </row>
            <row>
                <field name="DEVICE_TYPE_ID" label="规格型号"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" editable="false"/>
                <field name="LATITUDE" label="Y" editable="false"/>
            </row>
            <row>
                <field name="ROW_NO" label='行号'/>
                <field name="COL_NO" label='列号'/>
            </row>
            <row>
                <field name="V_CONTRACTOR" queryAssemble="V_CONTRACTOR" label="承包人姓名"/>
                <field name="V_CONTRACTOR_ACC" queryAssemble="V_CONTRACTOR_ACC" label="承包人账号"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
                <field name="MATERIAL_ID" label="材质"/>
            </row>
            <row>
                <field name="NET_MODE_ID" label="组网方式"/>
                <field name="IS_JOIN" label="是否交接间"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="IS_DSLAM_DOWN" label="是否DSLAM下移"/>
            </row>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="NETWORK_LAYER_ID" label="网络层次"/>
            </row>
            <row>
                <field name="POS_X" label='机房内X坐标'/>
                <field name="POS_Y" label='机房内Y坐标'/>
            </row>
            <row>
                <field name="WORKINGTYPE" label="测量标示"/>
                <field name="IS_FTTX_ABILITY_ID" label="FTTX能力"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
                <field name="BELONG_SPECIALITY_ID" label="所属专业" default="80204674" readOnly="true"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="asset" label="资产目录" dropdownOptions="titleField:ASSET_CATALOGUE" />
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="入库时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="MNT_DEPT" label="维护部门"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="address" label="标准地址"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:6000px;" autoLoad="false">
            <field name="CODE" label="配线架编码" width="120px"/>
            <field name="NAME" label="配线架名称" width="120px"/>
            <field name="ROW_NO" label='行号' width="100px"/>
            <field name="COL_NO" label='列号' width="100px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="room_outdooraddress" label="所属机房" getLabel="row.room_outdooraddress_value.NAME"/>
            <field name="terminal_value.PORTS_TOTAL" label="端口总数"/>
            <field name="terminal_value.PORTS_USED" label="端子占用数"/>
            <field name="terminal_value.PORTS_FREE" label="端子空闲数"/>
            <field name="terminal_value.PORTS_CONNECTED" label="已成端端子数" width="100px"/>
            <field name="terminal_value.PORTS_BAD" label="坏端子数"/>
            <field name="terminal_value.PORTS_USEDRATE" label="端子占用率(%)" width="100px"/>
            <field name="address" label="标准地址" width="300px"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="CREATE_DATE" label="录入时间"/>
            <field name="OWNER_NET_ID" label="网络类型"/>
            <field name="CAPACITY" label="标称容量"/>
            <field name="KEEP_LINE" label="保留线对"/>
            <field name="MNT_LEVEL_ID" label="维护等级"/>
            <field name="MATERIAL_ID" label="材质"/>
            <field name="NET_MODE_ID" label="组网方式"/>
            <field name="IS_JOIN" label="是否交接间"/>
            <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
            <field name="IS_DSLAM_DOWN" label="是否DSLAM下移"/>
            <field name="INSTALL_WAY_ID" label="安装方式"/>
            <field name="NETWORK_LAYER_ID" label="网络层次"/>
            <field name="POS_X" label='机房内X坐标'/>
            <field name="POS_Y" label='机房内Y坐标'/>
            <field name="WORKINGTYPE" label="测量标示"/>
            <field name="IS_FTTX_ABILITY_ID" label="FTTX能力"/>
            <field name="NOTES" label="备注"/>
            <field name="ASSET_CODE" label="资产编码"/>
            <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号"/>
            <field name="CREATOR" label="录入人"/>
            <field name="MODIFIER" label="修改人"/>
            <field name="AUDIT_PERSON" label="验收人"/>
            <field name="PROPERTY_TYPE_ID" label="产权性质"/>
            <field name="PROPERTY_OWNER_ID" label="产权归属"/>
            <field name="MNT_PERSON" label="维护人"/>
            <field name="MNT_DEPT" label="维护单位"/>
            <field name="LENGTH" label='长度(米)'/>
            <field name="WIDTH" label='宽度(米)'/>
            <field name="HEIGHT" label='高度(米)'/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
            <field name="LONGITUDE" label="经度"/>
            <field name="LATITUDE" label="纬度"/>
        </grid>

        <grid name="alreadyBindPort">
            <field name="fromDeviceName" label="本端设备名称" />
            <field name="fromModule" label="本端模块"/>
            <field name="fromModuleType" label="本端模块类型"/>
            <field name="fromPortCode" label="本端端子编码"/>
            <field name="fromPortSeq" label="本端端子端序"/>
            <field name="oppositeDeviceName" label="对端设备名称"  />
            <field name="oppositeModule"  label="对端模块"/>
            <field name="oppositeModuleType" label="对端模块类型"/>
            <field name="oppositePortCode" label="对端端子编码"/>
            <field name="oppositePortSeq" label="对端端子端序"/>
        </grid>

        <grid name="deviceBindMdfAlreadyBindPort">
            <field name="fromDeviceName" label="本端设备名称" />
            <field name="fromFrameName" label="本端机框名称" />
            <field name="fromCard" label="本端板卡" />
            <field name="fromCardType" label="本端板卡类型"/>
            <field name="fromPortCode" label="本端端口编码"/>
            <field name="fromPortUsingState" label="本端端口业务状态"/>
            <field name="fromPortNetCode" label="本端网管编码"/>
            <field name="oppositeDeviceName" label="对端设备名称"  />
            <field name="oppositeModule"  label="对端模块"/>
            <field name="oppositeModuleType" label="对端模块类型"/>
            <field name="oppositePortSeq" label="对端端子端序"/>
            <field name="oppositePortCode" label="对端端子编码"/>
            <field name="loopBindPstnCode" label="LOOP关联PSTN端子编码"/>
            <field name="pstnPortState" label="PSTN端子业务状态"/>
        </grid>
    </ObjMeta>
    <!-- 配线架模板 -->
    <ObjMeta objectType="MDF.TEMPLATE" labelField="NAME" autoReload="true" needgeom="false" typeActions="add,remove" itemActions="modify,connview,remove">
        <action type="workspace" name="connview" label="端子图" label-en="Panel Diagram" icon="delicious"
                extra="_ui_class:gray" title="端子面板图-${NAME}" title-en="Panel Diagram-${NAME}"
                url="econnector.html?objecttype=${objectType}&amp;id=${id}" permission="ELECTRIC_MANAGE"/>
        <!--查询条件-->
        <form name="search" labelWidth="6em">
            <field name="NAME" label="模板名称"/>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="" autoLoad="false">
            <field name="NAME" label="模板名称"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--新增/修改面板-->
        <form>
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" required="true" label="名称"/>
            </row>
            <row>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
                <field name="KEEP_LINE" label="保留线对"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true"/>
                <field name="OWNER_NET_ID" label="网络类型"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="CAPACITY" label='标称容量' required="true"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="LENGTH" label="长度(米)"/>
                <field name="WIDTH" label="宽度(米)"/>
            </row>
            <row>
                <field name="HEIGHT" label="高度(米)"/>
                <field name="region" label="所属区域"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
                <field name="MATERIAL_ID" label="材质"/>
            </row>
            <row>
                <field name="NET_MODE_ID" label="组网方式"/>
                <field name="IS_JOIN" label="是否交接间"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="IS_DSLAM_DOWN" label="是否DSLAM下移"/>
            </row>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="NETWORK_LAYER_ID" label="网络层次"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="入库时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="MNT_DEPT" label="维护部门"/>
            </row>
        </form>
        <action name="genCode" type="script">
            <![CDATA[
            var pre = site.CODE + '/MDF';
            _bean.autoCode(this, pre, 4, 'MDF', {site: site.id});
        ]]>
        </action>
    </ObjMeta>

    <!-- DDF 数字配线架-->
    <ObjMeta objectType="DDF" autoReload="true" needgeom="false"
             typeActions="add,reset,templates,remove"
             itemActions="locate,modify,remove,assetAttribute,filesman"
             moreItemActions="connview,echengduan,invokeLogicDiagram,invokeRouteDiagram">
        <action type="workspace" name="templates" label="模板管理" label-en="Template management" title="数字配线架模板"
                title-en="MDF Template" objectType="DDF.TEMPLATE" icon="columns"/>
        <action type="workspace" name="connview" label="端子图" label-en="Panel Diagram" icon="delicious"
                extra="_ui_class:gray" title="端子面板图-${NAME}" title-en="Panel Diagram-${NAME}"
                url="econnector.html?objecttype=${objectType}&amp;id=${id}" permission="ELECTRIC_MANAGE"/>
        <action type="workspace" id="connectSection" label="关联电缆" title="${NAME}-关联电缆" objectType="ECABLE"
                options='{"baseParams": {"adevice": "${id}"}}'/>
        <action name="echengduan" label="成端管理" type="script">
            <![CDATA[
             var dev = _actionContext.params;
            _bean.action('DEVICE', 'queryForEconnector', {id: dev.id, objecttype: dev.objectType}).then(function (data) {
                _device = data;

                _context.doAction({
                    type: "workspace",
                    title: "成端管理",
                    url: "modules/econnector/econnectorManager.html"
                }, {device: _device}, "成端管理");
            });
        ]]>
        </action>
        <!--查询条件-->
        <form name="search" labelWidth="6em">
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="STANDARD_CODE" label="集团编码"/>
                <field name="STANDARD_NAME" label="集团名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="room_outdooraddress" label="机房/安装点"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" baseParams="{&quot;_solr&quot;:true}"
                       dropdownOptions="titleField:FULL_NAME"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="region" label="所属区域" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
                <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            </row>
            <row>
                <field name="project" label="工程名称" dropdownOptions="titleField:NAME"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" required="true" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="CODE" required="true"/>
                <field name="NAME" required="true"/>
            </row>
            <row>
                <field name="STANDARD_CODE" label="集团编码"/>
                <field name="STANDARD_NAME" label="集团名称"/>
            </row>
            <row>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" default="100079"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="CAPACITY" label='标称容量' required="true"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" baseParams="{&quot;_solr&quot;:true}"
                       dropdownOptions="titleField:FULL_NAME"/>
                <field name="DISMANTLED_STATE_ID" label='退网状态'/>
            </row>
            <row>
                <field name="ADDRESS_ID" label="地址ID" readOnly="true"/>
                <field name="DISTRICT" label="所属小区" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="ROW_NO" label='行号' required="true"/>
                <field name="COL_NO" label='列号' required="true"/>
            </row>
            <row>
                <field name="LENGTH" label='长度(米)'/>
                <field name="WIDTH" label='宽度(米)'/>
            </row>
            <row>
                <field name="HEIGHT" label='高度(米)'/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="typeddf" label="机架型号"/>
                <field name="PROJECT_STATUS_ID" label='工程状态' required="true" default="80204666"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X"/>
                <field name="LATITUDE" label="Y"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="BELONG_SPECIALITY_ID" label="所属专业" readOnly="true" default="80204674"/>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
            </row>
            <row>
                <field name="POS_X" label='机房内X坐标'/>
                <field name="POS_Y" label='机房内Y坐标'/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="入库时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="asset" label="资产目录" dropdownOptions="titleField:ASSET_CATALOGUE" />
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
                <field name="MNT_DEPT" label="维护部门"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!--批量修改表格-->
        <form name="batchmodify">
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
                <field name="DISMANTLED_STATE_ID" label='退网状态'/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="address" label="标准地址"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:6000px;" autoLoad="false">
            <field name="CODE" label="配线架编码" width="120px"/>
            <field name="NAME" label="配线架名称" width="120px"/>
            <field name="ROW_NO" label='行号' width="100px"/>
            <field name="COL_NO" label='列号' width="100px"/>
            <field name="STANDARD_CODE" label="集团编码" width="150px"/>
            <field name="STANDARD_NAME" label="集团名称" width="150px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="room_outdooraddress" label="所属机房" getLabel="row.room_outdooraddress_value.NAME"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            <field name="vendor" label="生产厂家"/>
            <field name="LONG_LOCAL_ID"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="LENGTH" label='长度(米)'/>
            <field name="WIDTH" label='宽度(米)'/>
            <field name="HEIGHT" label='高度(米)'/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
            <field name="LIFE_STATE_ID" label="生命周期状态"/>
            <field name="DISMANTLED_STATE_ID" label='退网状态'/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="CREATE_DATE" label="录入时间"/>
            <field name="POS_X" label="原X坐标" readOnly="false"/>
            <field name="POS_Y" label="原Y坐标" readOnly="false"/>
            <field name="LONGITUDE" label="经度"/>
            <field name="LATITUDE" label="纬度"/>
        </grid>
    </ObjMeta>
    <!-- 数字配线架模板-->
    <ObjMeta objectType="DDF.TEMPLATE" labelField="NAME" autoReload="true" needgeom="false" typeActions="add,remove" itemActions="modify,remove" reloadByEventObjType="true">
        <action type="workspace" name="connview" label="端子图" label-en="Panel Diagram" icon="delicious"
                extra="_ui_class:gray" title="端子面板图-${NAME}" title-en="Panel Diagram-${NAME}"
                url="econnector.html?objecttype=${objectType}&amp;id=${id}" permission="ELECTRIC_MANAGE"/>
        <!--查询条件-->
        <form name="search" labelWidth="6em">
            <field name="NAME" label="模板名称"/>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="" autoLoad="false">
            <field name="NAME" label="模板名称"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--新增/修改面板-->
        <form>
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" required="true" label="名称"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true"/>
            </row>
            <row>
                <field name="CAPACITY" label='标称容量' required="true"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
                <field name="LENGTH" label='长度(米)'/>
            </row>
            <row>
                <field name="WIDTH" label='宽度(米)'/>
                <field name="HEIGHT" label='高度(米)'/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="typeddf" label="机架类型"/>
                <field name="CONFIG_TYPE_ID" label="配置点类型"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型"/>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="入库时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
                <field name="MNT_DEPT" label="维护部门"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
            </row>
            <row>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
            </row>
        </form>
        <action name="genCode" type="script">
            <![CDATA[
            var pre = site.CODE + '/DDF';
            _bean.autoCode(this, pre, 3, 'DDF', {site: site.id});
        ]]>
        </action>
    </ObjMeta>

    <!-- CCP 电交接箱 -->
    <ObjMeta objectType="CCP" autoReload="true"
             typeActions="add,reset,templates,remove"
             itemActions="locate,modify,remove,assetAttribute,filesman"
             moreItemActions="connview,echengduan,deviceOccupyRate,deviceMapping,connectSection,invokeLogicDiagram,invokeRouteDiagram">
        <action type="workspace" name="templates" label="模板管理" label-en="Template management" title="交接箱模板"
                title-en="MDF Template" objectType="CCP.TEMPLATE" icon="columns"/>
        <action type="workspace" name="connview" label="端子图" label-en="Panel Diagram" icon="delicious"
                extra="_ui_class:gray" title="端子面板图-${NAME}" title-en="Panel Diagram-${NAME}"
                url="econnector.html?objecttype=${objectType}&amp;id=${id}" permission="ELECTRIC_MANAGE"/>
        <action type="window" id="connectSection" label="关联电缆" title="${NAME}-关联电缆" width="800" >
                   <![CDATA[
                    <script>
                        var params = _actionContext.params;                   
                        let table =  _bean.grid('ECABLE','',_actionContext.el,{data:[],toolbar:false});
    	                let param = {_showLoading:true,A_DEVICE_ID:params.id,Z_DEVICE_ID:params.id,"_opgroup": 'or(A_DEVICE_ID,Z_DEVICE_ID)'};
        
    	                _bean.query("ECABLESECTION",param).then(function (data) {
    		            
                          let list = new Array();
                          data.map(item=>{list.push(...[item.id])});
        	            _bean.query('ECABLE', {'ecablesections': list}).then(function(ecable){

                              table.setData(ecable);                        
                         });
    	                });
                    </script>
                ]]>
        </action>
        <action name="echengduan" label="成端管理" type="script">
            <![CDATA[
             var dev = _actionContext.params;
            _bean.action('DEVICE', 'queryForEconnector', {id: dev.id, objecttype: dev.objectType}).then(function (data) {
                _device = data;

                _context.doAction({
                    type: "workspace",
                    title: "成端管理",
                    url: "modules/econnector/econnectorManager.html"
                }, {device: _device}, "成端管理");
            });
        ]]>
        </action>
        <!--高级查询条件-->
        <form name="search" labelWidth="8em">
            <field type="divider" label="基本属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="room_outdooraddress" label="机房/安装点"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
                <field name="DISMANTLED_STATE_ID" label='退网状态'/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="ADDRESS_ID" label="标准地址"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="region" label="所属区域"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
                <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            </row>
            <row>
                <field name="project" label="工程名称" dropdownOptions="titleField:NAME"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <row>
                <field name="CODE" label="编码" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="room_outdooraddress" label="机房/安装点" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
                <field name="ADDRESS_ID" label="地址ID" readOnly="true"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" baseParams="{&quot;_solr&quot;:true}"
                       dropdownOptions="titleField:FULL_NAME" required="true"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" default="80204666"/>
                <field name="CAPACITY" label='标称容量' required="true"/>
            </row>
            <row>
                <field name="model" label="型号"/>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
                <field name="DISMANTLED_STATE_ID" label='退网状态'/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
                <field name="DISTRICT" label="所属小区"/>
            </row>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="MATERIAL_ID" label="材质"/>
            </row>
            <row>
                <field name="DEPRECIATION" label="折旧年限(年)"/>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204561"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="POS_X" label="原X坐标" readOnly="true"/>
                <field name="POS_Y" label="原Y坐标" readOnly="true"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="NET_MODE_ID" label="组网方式"/>
                <field name="IS_DSLAM_DOWN" label="是否DSLAM下移"/>
            </row>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="NETWORK_LAYER_ID" label="网络层次"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MAX_LENGTH" label="最长距离"/>
                <field name="MIN_LENGTH" label="最短距离"/>
            </row>
            <row>
                <field name="BELONG_SPECIALITY_ID" label="所属专业" readOnly="true" default="80204676"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="入库时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="MNT_DEPT" label="维护部门"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="address" label="标准地址"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:6000px;" autoLoad="false">
            <field name="CODE" label="交接箱编码" width="120px"/>
            <field name="NAME" label="交接箱名称" width="120px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="terminal_value.PORTS_TOTAL" label="端口总数"/>
            <field name="terminal_value.PORTS_USED" label="端子占用数"/>
            <field name="terminal_value.PORTS_FREE" label="端子空闲数"/>
            <field name="terminal_value.PORTS_CONNECTED" label="已成端端子数" width="100px"/>
            <field name="terminal_value.PORTS_BAD" label="坏端子数"/>
            <field name="terminal_value.PORTS_USEDRATE" label="端子占用率(%)" width="100px"/>
            <field name="BUSI_NUMBER" label="业务数"/>
            <field name="USER_NUMBER" label="用户数"/>
            <field name="addressobj" label="标准地址" getLabel="row.addressobj_value.FULL_NAME" width="300px"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="CAPACITY" label='标称容量'/>
            <field name="OWNER_NET_ID" label="网络类型"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="CREATE_DATE" label="录入时间"/>
            <field name="NOTES" label="备注"/>
            <field name="NET_MODE_ID" label="组网方式"/>
            <field name="IS_DSLAM_DOWN" label="是否DSLAM下移"/>
            <field name="INSTALL_WAY_ID" label="安装方式"/>
            <field name="NETWORK_LAYER_ID" label="网络层次"/>
            <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
            <field name="MNT_LEVEL_ID" label="维护等级"/>
            <field name="MAX_LENGTH" label="最长距离"/>
            <field name="MIN_LENGTH" label="最短距离"/>
            <field name="ASSET_CODE" label="资产编码"/>
            <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号"/>
            <field name="CREATOR" label="录入人"/>
            <field name="MODIFIER" label="修改人"/>
            <field name="AUDIT_PERSON" label="验收人"/>
            <field name="PROPERTY_TYPE_ID" label="产权性质"/>
            <field name="PROPERTY_OWNER_ID" label="产权归属"/>
            <field name="MNT_PERSON" label="维护人"/>
            <field name="MNT_DEPT" label="维护单位"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
        </grid>
        <grid name="CCPCodeCutOver">
            <field name="TYPE" label="类型" label-en="Type" />
            <field name="COUNT" label="数量" label-en="Count" />
            <field name="OLDSITE" label="旧所属局站" label-en="oldSite" />
            <field name="NEWSITE" label="新所属局站" label-en="newSite" />
            <field name="RESULT" label="处理结果" label-en="Result" />
        </grid>
    </ObjMeta>
    <!-- 电交接箱模板 -->
    <ObjMeta objectType="CCP.TEMPLATE" labelField="NAME" autoReload="true" needgeom="false" typeActions="add,remove" itemActions="modify,connview,remove">
        <action type="workspace" name="connview" label="端子图" label-en="Panel Diagram" icon="delicious"
                extra="_ui_class:gray" title="端子面板图-${NAME}" title-en="Panel Diagram-${NAME}"
                url="econnector.html?objecttype=${objectType}&amp;id=${id}" permission="ELECTRIC_MANAGE"/>
        <!--查询条件-->
        <form name="search" labelWidth="6em">
            <field name="NAME" label="模板名称"/>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="" autoLoad="false">
            <field name="NAME" label="模板名称"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--新增/修改面板-->
        <form>
            <row>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型"/>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="MATERIAL_ID" label="材质"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true"/>
            </row>
            <row>
                <field name="CAPACITY" label='标称容量' required="true"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="region" label="所属区域"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="NET_MODE_ID" label="组网方式"/>
                <field name="IS_DSLAM_DOWN" label="是否DSLAM下移"/>
            </row>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="NETWORK_LAYER_ID" label="网络层次"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MAX_LENGTH" label="最长距离"/>
                <field name="MIN_LENGTH" label="最短距离"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="入库时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="MNT_DEPT" label="维护部门"/>
            </row>
        </form>
        <action name="genCode" type="script">
            <![CDATA[
            var pre = site.CODE + '/J';
            _bean.autoCode(this, pre, 3, 'CCP', {site: site.id});
        ]]>
        </action>
    </ObjMeta>

    <!-- DP 分线盒 -->
    <ObjMeta objectType="DP" autoReload="true"
             typeActions="add,reset,templates,remove"
             itemActions="locate,modify,remove,assetAttribute,filesman"
             moreItemActions="connview,echengduan,deviceOccupyRate,connectSection,devicecoveraddress_man,gridElement,invokeLogicDiagram,invokeRouteDiagram">
        <action type="workspace" name="templates" label="模板管理" label-en="Template management" title="直配分线盒模板"
                title-en="DP Template" objectType="DP.TEMPLATE" icon="columns"/>
        <action type="workspace" name="connview" label="端子图" label-en="Panel Diagram" icon="delicious"
                extra="_ui_class:gray" title="端子面板图-${NAME}" title-en="Panel Diagram-${NAME}"
                url="econnector.html?objecttype=${objectType}&amp;id=${id}" permission="ELECTRIC_MANAGE"/>
        <action type="workspace" id="connectSection" label="关联电缆" title="${NAME}-关联电缆" objectType="ECABLE"
                options='{"baseParams": {"adevice": "${id}"}}'/>
        <action name="echengduan" label="成端管理" type="script">
            <![CDATA[
             var dev = _actionContext.params;
            _bean.action('DEVICE', 'queryForEconnector', {id: dev.id, objecttype: dev.objectType}).then(function (data) {
                _device = data;

                _context.doAction({
                    type: "workspace",
                    title: "成端管理",
                    url: "modules/econnector/econnectorManager.html"
                }, {device: _device}, "成端管理");
            });
        ]]>
        </action>
        <!--查询条件-->
        <form name="search" labelWidth="6em">
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="room_outdooraddress" label="机房/安装点"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
                <field name="DISMANTLED_STATE_ID" label='退网状态'/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" baseParams="{&quot;_solr&quot;:true}"
                       dropdownOptions="titleField:FULL_NAME"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="PHYSICAL_STATE_ID" label="设施状态"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
                <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            </row>
            <row>
                <field name="region" label="所属区域"/>
                <field name="CREATOR" label="录入人"/>
            </row>
            <row>
                <field name="project" label="工程名称" dropdownOptions="titleField:NAME"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="id" label="实物ID"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="DEVICE_TYPE_ID" label="分线盒类型" required="true" default="80205078"/>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" readOnly="true" default="80204666"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="CODE" label="编码" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="dpgroup" label="所属DP群"/>
                <field name="ADDRESS_ID" label="地址ID" readOnly="true"/>
            </row>
            <row>
                <field name="room_outdooraddress" label="所属机房"/>
                <field name="addressobj" label="标准地址" baseParams="{&quot;_solr&quot;:true}"
                       dropdownOptions="titleField:FULL_NAME" required="true"/>
            </row>
            <row>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置" orderBy="false"/>
                <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204561"/>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
                <field name="DISTRICT" label="所属小区"/>
            </row>
            <row>
                <field name="parentDevice" label="所属设备" required="true" baseParams='{"SPEC_ID": "1020100002,1020300002","excludeTemplate": "true"}'
                       dropdownOptions="titleField:NAME"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="INSTALL_SUPPORTER_TYPE_ID" label="安装方式"/>
                <field name="IS_URBANVILLAGE_ID" label="是否城中村"/>
            </row>
            <row>
                <field name="DEPRECIATION" label="折旧年限(年)"/>
                <field name="IS_CHECKPOINT_ID" label="是否检查点"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="POS_X" label="原X坐标"/>
                <field name="POS_Y" label="原Y坐标"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="CAPACITY" label="标称容量" required="true"/>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识" required="true"/>
            </row>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="NETWORK_LAYER_ID" label="网络层次"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="PIPE_FIRST_FLAG" label="管优标志"/>
            </row>
            <row>
                <field name="MATERIAL_ID" label="材质"/>
                <field name="NET_MODE_ID" label="组网方式"/>
            </row>
            <row>
                <field name="IS_TIANYIMAO" label="是否天翼猫"/>
                <field name="SUBSCIRBE_FLAG" label="装机预约标识"/>
            </row>
            <row>
                <field name="WIDE_MEASURE_RATE" label="宽测速率"/>
                <field name="THEORY_RATE" label="理论速率"/>
            </row>
            <row>
                <field name="SITE_RADIO_ABILITY" label="局端语音能力"/>
                <field name="SITE_WIDE_ABILITY" label="局端宽带能力"/>
            </row>
            <row>
                <field name="MAX_LENGTH" label="最长距离"/>
                <field name="MIN_LENGTH" label="最短距离"/>
            </row>
            <row>
                <field name="BELONG_SPECIALITY_ID" label="所属专业"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="入库时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="MNT_DEPT" label="维护部门"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="address" label="标准地址"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:6000px;" autoLoad="false">
            <field name="CODE" label="分线盒编码" width="120px"/>
            <field name="NAME" label="分线盒名称" width="120px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="terminal_value.PORTS_TOTAL" label="端口总数"/>
            <field name="terminal_value.PORTS_USED" label="端子占用数"/>
            <field name="terminal_value.PORTS_FREE" label="端子空闲数"/>
            <field name="terminal_value.PORTS_CONNECTED" label="已成端端子数" width="100px"/>
            <field name="terminal_value.PORTS_BAD" label="坏端子数"/>
            <field name="terminal_value.PORTS_USEDRATE" label="端子占用率(%)" width="100px"/>
            <field name="addressobj" getLabel="row.addressobj_value.FULL_NAME" label="标准地址" width="300px"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="OWNER_NET_ID" label="网络类型"/>
            <field name="LIFE_STATE_ID" label="生命周期状态" width="100px"/>
            <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="CREATE_DATE" label="录入时间"/>
            <field name="MNT_LEVEL_ID" label="维护等级"/>
            <field name="NOTES" label="备注"/>
            <field name="IS_TIANYIMAO" label="是否天翼猫"/>
            <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
            <field name="INSTALL_WAY_ID" label="安装方式"/>
            <field name="NETWORK_LAYER_ID" label="网络层次"/>
            <field name="ACCESS_MODE_ID" label="接入方式"/>
            <field name="PIPE_FIRST_FLAG" label="管优标志"/>
            <field name="CAPACITY" label='标称容量'/>
            <field name="WIDE_MEASURE_RATE" label="宽测速率"/>
            <field name="THEORY_RATE" label="理论速率"/>
            <field name="SITE_RADIO_ABILITY" label="局端语音能力"/>
            <field name="SITE_WIDE_ABILITY" label="局端宽带能力"/>
            <field name="ASSET_CODE" label="资产编码"/>
            <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号"/>
            <field name="CREATOR" label="录入人"/>
            <field name="MODIFIER" label="修改人"/>
            <field name="AUDIT_PERSON" label="验收人"/>
            <field name="PROPERTY_TYPE_ID" label="产权性质"/>
            <field name="PROPERTY_OWNER_ID" label="产权归属"/>
            <field name="MNT_PERSON" label="维护人"/>
            <field name="MNT_DEPT" label="维护单位"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
        </grid>
    </ObjMeta>
    <!-- 分线盒模板 -->
    <ObjMeta objectType="DP.TEMPLATE" labelField="NAME" autoReload="true" needgeom="false" typeActions="add,remove" itemActions="modify,connview,remove">
        <action type="workspace" name="connview" label="端子图" label-en="Panel Diagram" icon="delicious"
                extra="_ui_class:gray" title="端子面板图-${NAME}" title-en="Panel Diagram-${NAME}"
                url="econnector.html?objecttype=${objectType}&amp;id=${id}" permission="ELECTRIC_MANAGE"/>
        <!--查询条件-->
        <form name="search" labelWidth="6em">
            <field name="NAME" label="模板名称"/>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="" autoLoad="false">
            <field name="NAME" label="模板名称"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--新增/修改面板-->
        <form>
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型"/>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true"/>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="CAPACITY" label='标称容量'/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
                <field name="region" label="所属区域"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="IS_TIANYIMAO" label="是否天翼猫"/>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
            </row>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="NETWORK_LAYER_ID" label="网络层次"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
            </row>
            <row>
                <field name="MATERIAL_ID" label="材质"/>
                <field name="NET_MODE_ID" label="组网方式"/>
            </row>
            <row>
                <field name="SUBSCIRBE_FLAG" label="装机预约标识"/>
                <field name="PIPE_FIRST_FLAG" label="管优标志"/>
            </row>
            <row>
                <field name="WIDE_MEASURE_RATE" label="宽测速率"/>
                <field name="THEORY_RATE" label="理论速率"/>
            </row>
            <row>
                <field name="SITE_RADIO_ABILITY" label="局端语音能力"/>
                <field name="SITE_WIDE_ABILITY" label="局端宽带能力"/>
            </row>
            <row>
                <field name="MAX_LENGTH" label="最长距离"/>
                <field name="MIN_LENGTH" label="最短距离"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="入库时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="MNT_DEPT" label="维护部门"/>
            </row>
        </form>
        <action name="genCode" type="script">
            <![CDATA[
            var type = DEVICE_TYPE_ID.id;
            var p = type == '80205077' ? 'ZDP' : 'DP';
            var pre = site.CODE + '/' + p;
            _bean.autoCode(this, pre, 3, 'DP', {site: site.id, DEVICE_TYPE_ID: type});
        ]]>
        </action>
    </ObjMeta>

    <!-- 电缆接头 DT -->
    <ObjMeta objectType="DT" autoReload="true" needgeom="false"
             typeActions="add,reset,templates,remove"
             itemActions="locate,modify,remove,filesman,relateECablesection"
             moreItemActions="invokeLogicDiagram,invokeRouteDiagram">
        <action type="workspace" name="templates" label="模板管理" label-en="Template management" title="电缆接头模板"
                title-en="DT Template" objectType="DT.TEMPLATE" icon="columns"/>
        <action type="workspace" name="connview" label="端子图" label-en="Panel Diagram" icon="delicious"
                extra="_ui_class:gray" title="端子面板图-${NAME}" title-en="Panel Diagram-${NAME}"
                url="connector.html?objecttype=${objectType}&amp;id=${id}" permission="ELECTRIC_MANAGE"/>
        <action type="workspace" id="connectSection" label="关联电缆" title="${NAME}-关联电缆" objectType="ECABLE"
                options='{"baseParams": {"adevice": "${id}"}}'/>
        <!-- action type="workspace" name="cablesections" objectType="ECABLESECTION" label="电缆段管理"
                label-en="Cable Segment Management" title="${NAME}-电缆段" title-en="${NAME} - cable segment"
                options='{"baseParams": {"net": "${id}"}}'/ -->
        <!--查询条件-->
        <form name="search" labelWidth="6em">
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="facility" label="所属支撑设施" baseParams='{"SPEC_ID": "UNDERWELL,WELL,POLE,DRAWINGPOINT,SUPPORTPOINT","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
                <field name="DISMANTLED_STATE_ID" label='退网状态'/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="DEVICE_TYPE_ID" label="接头类型"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="ACCESS_MODE_ID" label="接入方式"/>
                <field name="region" label="所属区域"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
                <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            </row>
            <row>
                <field name="project" label="工程名称" dropdownOptions="titleField:NAME"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME" required="true"/>
                <field name="net" label="所属电缆" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="facility" label="所属支撑设施" dropdownOptions="titleField:NAME"
                       baseParams='{"SPEC_ID": "UNDERWELL,WELL,POLE,DRAWINGPOINT,SUPPORTPOINT","excludeTemplate":"true"}'/>
                <field name="dpgroup" label="所属DP群"/>
            </row>
            <row>
                <field name="model" label="型号"/>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204561"/>
                <field name="DEVICE_TYPE_ID" label="接头类型"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="TGTYPE" label="头套类型"/>
                <field name="DISMANTLED_STATE_ID" label='退网状态'/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="DEPRECIATION" label="折旧年限(年)"/>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" default="80204666"/>
            </row>
            <row>
                <field name="LONGITUDE" label="X" readOnly="true"/>
                <field name="LATITUDE" label="Y" readOnly="true"/>
            </row>
            <row>
                <field name="POS_X" label="原X坐标" readOnly="true"/>
                <field name="POS_Y" label="原Y坐标" readOnly="true"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="NETWORK_LAYER_ID" label="网络层次"/>
            </row>
            <row>
                <field name="MATERIAL_ID" label="材质"/>
                <field name="NET_MODE_ID" label="组网方式"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
                <field name="SUPPORTTYPE" label="支撑设施类型"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="IS_JUMPER" label="是否跳接"/>
                <field name="ATTENUATION" label="接头衰耗"/>
            </row>
            <row>
                <field name="JOINTMODEL" label="接头规格"/>
                <field name="JOINTCOVER" label="套管信息"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置" orderBy="false"/>
                <field name="NOTES" label="备注"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="入库时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="MNT_DEPT" label="维护部门"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="address" label="标准地址"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:6000px;" autoLoad="false">
            <field name="CODE" label="电接头编码" width="120px"/>
            <field name="NAME" label="电接头名称" width="120px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME" width="120px"/>
            <field name="LONG_LOCAL_ID" label="长本属性" width="100px"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="net" label="所属电缆"/>
            <field name="INSTALL_WAY_ID" label="安装方式"/>
            <field name="facility" label="所属支撑设施"/>
            <field name="OWNER_NET_ID" label="网络类型"/>
            <field name="TGTYPE" label="头套类型"/>
            <field name="LIFE_STATE_ID" label="生命周期状态"/>
            <field name="DISMANTLED_STATE_ID" label='退网状态'/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="CREATE_DATE" label="录入时间"/>
            <field name="NOTES" label="备注"/>
            <field name="INSTALL_WAY_ID" label="安装方式"/>
            <field name="NETWORK_LAYER_ID" label="网络层次"/>
            <field name="MATERIAL_ID" label="材质"/>
            <field name="NET_MODE_ID" label="组网方式"/>
            <field name="MNT_LEVEL_ID" label="维护等级"/>
            <field name="SUPPORTTYPE" label="支撑设施类型"/>
            <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
            <field name="ATTENUATION" label="接头衰耗"/>
            <field name="IS_JUMPER" label="是否跳接"/>
            <field name="JOINTMODEL" label="接头规格"/>
            <field name="JOINTCOVER" label="套管信息"/>
            <field name="ASSET_CODE" label="资产编码"/>
            <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号"/>
            <field name="CREATOR" label="录入人"/>
            <field name="MODIFIER" label="修改人"/>
            <field name="AUDIT_PERSON" label="验收人"/>
            <field name="PROPERTY_TYPE_ID" label="产权性质"/>
            <field name="PROPERTY_OWNER_ID" label="产权归属"/>
            <field name="MNT_PERSON" label="维护人"/>
            <field name="MNT_DEPT" label="维护单位"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
        </grid>
        <grid name="lineCutFromCable">
            <field name="METACATEGORYCN" label="实体类型"/>
            <field name="CODE" label="实体编码"/>
            <field name="ecableCode" label="电缆编码"/>
            <field name="outSeq" label="接头处总出线序"/>
            <field name="total" label="接头处线对总数"/>
            <field name="ecableSectionSeq" label="电缆段线序"/>
        </grid>
        <grid name="lineCutToCable">
            <field name="CODE" label="电缆接头编码"/>
            <field name="ecableCode" label="电缆编码"/>
            <field name="inSeq" label="接头处总进线序"/>
            <field name="outSeq" label="接头处总出线序"/>
            <field name="freeLineSeq" label="电缆段线序"/>
        </grid>
    </ObjMeta>
    <!-- 电缆接头模板 -->
    <ObjMeta objectType="DT.TEMPLATE" labelField="NAME" autoReload="true" needgeom="false" typeActions="add,remove" itemActions="modify,connview,remove">
        <action type="workspace" name="connview" label="端子图" label-en="Panel Diagram" icon="delicious"
                extra="_ui_class:gray" title="端子面板图-${NAME}" title-en="Panel Diagram-${NAME}"
                url="econnector.html?objecttype=${objectType}&amp;id=${id}" permission="ELECTRIC_MANAGE"/>
        <!--查询条件-->
        <form name="search" labelWidth="6em">
            <field name="NAME" label="模板名称"/>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="" autoLoad="false">
            <field name="NAME" label="模板名称"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--新增/修改面板-->
        <form>
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="名称" required="true"/>
                <field name="TGTYPE" label="头套类型"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型"/>
                <field name="DEVICE_TYPE_ID" label="接头类型"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
                <field name="region" label="所属区域"/>
            </row>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="NETWORK_LAYER_ID" label="网络层次"/>
            </row>
            <row>
                <field name="MATERIAL_ID" label="材质"/>
                <field name="NET_MODE_ID" label="组网方式"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
                <field name="SUPPORTTYPE" label="支撑设施类型"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true"/>
            </row>
            <row>
                <field name="ATTENUATION" label="接头衰耗"/>
                <field name="IS_JUMPER" label="是否跳接"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="入库时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="MNT_DEPT" label="维护部门"/>
            </row>
        </form>
        <action name="genCode" type="script">
            <![CDATA[
            var pre = site.CODE + '/DT';
            _bean.autoCode(this, pre, 3, 'DT', {site: site.id});
        ]]>
        </action>
    </ObjMeta>

    <!-- 设备群 DPGROUP -->
    <ObjMeta objectType="DPGROUP" autoReload="true" needgeom="true"
             typeActions="add,reset,remove"
             itemActions="locate,modify,remove,dpmanage,filesman"
             moreItemActions="">
        <action type="workspace" name="connview" label="端子图" label-en="Panel Diagram" icon="delicious"
                extra="_ui_class:gray" title="端子面板图-${NAME}" title-en="Panel Diagram-${NAME}"
                url="connector.html?objecttype=${objectType}&amp;id=${id}"/>
        <!--查询条件-->
        <form name="search" labelWidth="6em">
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="room_outdooraddress" label="所属机房"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
                <field name="DISMANTLED_STATE_ID" label='退网状态'/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="MATERIAL_ID" label="材质"/>
                <field name="NET_MODE_ID" label="组网方式"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" baseParams="{&quot;_solr&quot;:true}"
                       dropdownOptions="titleField:FULL_NAME" required="true"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
                <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            </row>
            <row>
                <field name="project" label="工程名称" dropdownOptions="titleField:NAME"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人"/>
                <field name="id" label="实物ID"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME" required="true"/>
                <field name="CAPACITY" label='标称容量' required="true"/>
            </row>
            <row>
                <field name="ADDRESS_ID" label="地址ID" readOnly="true"/>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" default="80204666"/>
            </row>
            <row>
                <field name="addressobj" label="标准地址" baseParams="{&quot;_solr&quot;:true}"
                       dropdownOptions="titleField:FULL_NAME" required="true"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="具体位置"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="OWNER_NET_ID" label="网络类型" default="80204561"/>
                <field name="DEVICE_TYPE_ID" label="设备类型"/>
            </row>
            <row>
                <field name="DISTRICT" label="所属小区"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <field name="NOTES" label="备注"/>
            <field type="divider" label="专业属性" label-en="Professional attribute"/>
            <row>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
                <field name="NETWORK_LAYER_ID" label="网络层次"/>
            </row>
            <row>
                <field name="MATERIAL_ID" label="材质"/>
                <field name="NET_MODE_ID" label="组网方式"/>
            </row>
            <row>
                <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
            </row>
            <field name="BELONG_SPECIALITY_ID" label="所属专业" default="80204676" readOnly="true"/>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="入库时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="MNT_DEPT" label="维护部门"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:6000px;" autoLoad="false">
            <field name="CODE" label="编码"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="所属局站"/>
            <field name="NET_MODE_ID" label="组网方式"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="CAPACITY" label='标称容量'/>
            <field name="OWNER_NET_ID" label="网络类型"/>
            <field name="LIFE_STATE_ID" label="生命周期状态"/>
            <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="CREATE_DATE" label="录入时间"/>
            <field name="NOTES" label="备注"/>
            <field name="INSTALL_WAY_ID" label="安装方式"/>
            <field name="NETWORK_LAYER_ID" label="网络层次"/>
            <field name="MATERIAL_ID" label="材质"/>
            <field name="CUSTOMER_FLAG_ID" label="政企客户/公众客户标识"/>
            <field name="ASSET_CODE" label="资产编码"/>
            <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号"/>
            <field name="CREATOR" label="录入人"/>
            <field name="MODIFIER" label="修改人"/>
            <field name="AUDIT_PERSON" label="验收人"/>
            <field name="PROPERTY_OWNER_ID" label="产权归属"/>
            <field name="MNT_LEVEL_ID" label="维护等级"/>
            <field name="MNT_PERSON" label="维护人"/>
            <field name="MNT_DEPT" label="维护部门"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
        </grid>
        <action type="window" name="dpmanage" label="DP群管理" label-en="DP Manage" icon="delicious" extra="_ui_class:gray"
                title="DP群管理-${NAME}" title-en="DP Manage-${NAME}"
                contentUrl="apps/pipe/dpgroupmanage/dpgroupManage.html?id=${id}"/>
    </ObjMeta>

    <!-- DRESERVE 电缆预留 -->
    <ObjMeta objectType="DRESERVE" autoReload="true" needgeom="false"
             typeActions="add,reset,remove"
             itemActions="locate,modify,connview,filesman,remove">
        <action type="workspace" name="connview" label="端子图" label-en="Panel Diagram" icon="delicious"
                extra="_ui_class:gray" title="端子面板图-${NAME}" title-en="Panel Diagram-${NAME}"
                url="connector.html?objecttype=${objectType}&amp;id=${id}"/>
        <!--查询条件-->
        <form name="search" labelWidth="6em">
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="facility" label="所属支撑设施" required="true"
                       baseParams='{"SPEC_ID": "UNDERWELL,WELL,POLE,DRAWINGPOINT,SUPPORTPOINT","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="ADDRESS_DESC" label="地理位置"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
                <field name="OBLIGATE_NUM" label="预留芯数"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
                <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            </row>
            <row>
                <field name="region" label="所属区域"/>
                <field name="CREATOR" label="录入人"/>
            </row>
            <row>
                <field name="project" label="工程名称" dropdownOptions="titleField:NAME"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="id" label="实物ID"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="预留编码" required="true"/>
                <field name="NAME" label="预留名称" required="true"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME" required="true"/>
                <field name="ecablesection" label="所属缆段" dropdownOptions="titleField:CODE"/>
            </row>
            <row>
                <field name="facility" label="所属支撑设施" required="true"
                       baseParams='{"SPEC_ID": "UNDERWELL,WELL,POLE,DRAWINGPOINT,SUPPORTPOINT","excludeTemplate":"true"}'/>
                <field name="OBLIGATE_NUM" label="预留芯数"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true" readOnly="true" default="80204666"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="INSTALL_WAY_ID" label="安装方式"/>
            </row>
            <row>
                <field name="RES_TYPE_ID" label="预留类型"/>
                <field name="NOTES" label="备注"/>
            </row>
            <row>
                <field name="ASSET_CODE" label="资产编码"/>
                <field name="LENGTH" label="预留长度"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
            </row>
            <row>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
                <field name="CHECKING_PERSON" label="检查责任人"/>
            </row>
            <row>
                <field name="START_USE_DATE" label="启用时间"/>
                <field name="STOP_USE_DATE" label="停用日期"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:2800px;" autoLoad="false">
            <field name="CODE" label="预留编码"/>
            <field name="NAME" label="预留箱名称"/>
            <field name="site" label="所属局站"/>
            <field name="facility" label="所属支撑设施"/>
            <field name="OBLIGATE_NUM" label="预留芯数"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="ADDRESS_DESC" label="地理位置"/>
            <field name="ASSET_CODE" label="资产编码"/>
            <field name="LENGTH" label="预留长度"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            <field name="LIFE_STATE_ID" label="生命周期状态"/>
            <field name="DISMANTLED_STATE_ID" label="退网状态"/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="CREATE_DATE" label="录入时间"/>
        </grid>
    </ObjMeta>

    <!-- ECABLE 电缆 -->
    <ObjMeta objectType="ECABLE" autoReload="true"
             typeActions="add,templates,reset,remove"
             itemActions="locateNetCable,modify,remove,filesman,wirepair,cablesections,jts,censors,assetAttribute"
             moreItemActions="layingByCable,occupancyInfo,backOffNetworkStatusRepair,associatedResource_cmnet,invokeLogicDiagram,invokeRouteDiagram">
        <!--关联资源生命周期状态维护-->
        <action name="associatedResource_cmnet" label="关联资源生命周期状态维护" type="script">
            <![CDATA[
                let data =  _actionContext.params;
                let content = $(`
                    <h4 style="margin-left: 60px;">是否将电缆、以及对应电缆段、电缆线对的生命周期状态翻转为退网</h4>
                `);
                //取消按钮
				let cancelBtn = $('<div class="ui cancel button" id="cancelBtn">'+ "取消" +'</div>');
				//确定按钮
				let sureBtn = $('<div class="ui button" id="sureBtn">'+ "确定" +'</div>');
                //弹窗
                let win = _sui.showCommonModal("",content,[sureBtn,cancelBtn]);
                //确定按钮绑定事件
                win.on("click","#sureBtn",function (e) {
				    e.preventDefault();
				   _bean.callService("electricityCablePointServiceImpl","associatedResourceCmnet",[{"netId":data.id}]).then(function (res) {

                        if(res.length) {
                        alert("关联的电缆线对有业务占用，不能改退网");
                        _context.doAction({ type: 'window', title: '有业务的数据列表', contentUrl: 'modules/cable/lineSeq.html' }, res);
                        } else {
                        alert("修改成功");
                        _actionContext.sourceui.reload();
                        }

                    });
					//关闭窗口
				    _sui.closeModal(win);
				});
            ]]>
        </action>
        <action type="workspace" name="templates" label="模板管理" label-en="Template management" title="电缆模板"
                title-en="ECABLE Template" objectType="ECABLE.TEMPLATE" icon="columns"/>
        <action type="workspace" name="wirepair" objectType="LINESEQ" label="线对管理" label-en="Line Management"
                title="${NAME}-线对管理" title-en="${NAME} - Line Management" url="modules/ecable/ecableline.html?id=${id}&amp;objecttype=ecable"
                extra="_ui_class:gray"/>
        <action type="workspace" name="cablesections" objectType="ECABLESECTION" label="电缆段管理"
                label-en="Cable Segment Management" title="${NAME}-电缆段" title-en="${NAME} - cable segment"
                options='{"baseParams": {"net": "${id}"}, "showSearch": false, "autoLoad":true}'/>
        <action type="workspace" name="occupancyInfo" objectType="" label="占用信息" label-en="Occupancy Information"
                title="${NAME}-占用信息" title-en="${NAME} - cable Occupancy Information"
                url="modules/ecable/ecableOccInfo.html?id=${id}" extra="_ui_class:gray"/>
        <action type="workspace" name="jts" objectType="DT" label="电缆接头" label-en="Cable Segment Management"
                title="${NAME}-电缆接头" title-en="${NAME} - DT" options='{"baseParams": {"net": "${id}"}, "autoLoad": true}'/>
        <action type="workspace" name="censors" objectType="SENSOR" label="传感器" label-en="Cable Segment Management"
                title="${NAME}-传感器" title-en="${NAME} - SENSOR" options='{"baseParams": {"net": "${id}"}}'/>
        <!--查询条件-->
        <form name="search" labelWidth="6em">
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
            </row>
            <row>
                <field name="DISMANTLED_STATE_ID" label='退网状态'/>
                <field name="TOPO_ID" label="拓扑结构"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="CATEGORY_ID" label="电缆类型"/>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="LAY_FASHION_ID" label="敷设方式"/>
                <field name="DIAMETER_ID" label="电缆线径"/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
                <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            </row>
            <row>
                <field name="region" label="所属区域"/>
                <field name="CREATOR" label="录入人"/>
            </row>
            <row>
                <field name="project" label="工程名称" dropdownOptions="titleField:NAME"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="id" label="实物ID"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CATEGORY_ID" label="电缆类型" required="true" default="80203388"/>
                <field name="TOPO_ID" label="拓扑结构" required="true" default="102543"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME" required="true"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" default="80204848" required="true"/>
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="CODE" label="编码" required="true"/>
            </row>
            <row>
                <field name="CAPACITY" label="电缆线对总数" required="true"/>
                <field name="zdevice" label="终止设备" baseParams='{"SPEC_ID": "ZHX,DP,DDF,MDF,IDF,CCP,DRESERVE","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="MODEL_ID" label="型号"/>
            </row>
            <row>
                <field name="CABLESTYLE" label='规格型号'/>
                <field name="DIAMETER_ID" label="电缆线径"/>
            </row>
            <row>
                <field name="LAY_FASHION_ID" label="敷设方式"/>
                <field name="vendor" label="生产厂家" dropdownOptions="titleField:NAME"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID" label="长本属性" default="100079"/>
                <field name="adevice" label="起始设备" baseParams='{"SPEC_ID": "MDF,IDF,CCP,ZHX,DP,DDF,DRESERVE","excludeTemplate":"true"}'/>
            </row>
            <row>
                <field name="LENGTH" label="长度(m)"/>
                <field name="CABLE_FILLING_TYPE_ID" label="填充类型"/>
            </row>
            <row>
                <field name="ADDRESS_DESC" label="地址"/>
                <field name="DISMANTLED_STATE_ID" label='退网状态'/>
            </row>
            <row>
                <field name="DEPRECIATION" label="折旧年限(年)"/>
                <field name="USE_TYPE_ID" label='用途'/>
            </row>
            <row>
                <field name="HIREPIPECOMPACT" label="租用管道合同书"/>
                <field name="HIREPIPEYEAR" label="租用管道年限"/>
            </row>
            <row>
                <field name="USING_DEPARTMENT" label="使用部门"/>
                <field name="BUILDWAY_ID" label="建设模式"/>
            </row>
            <field name="NOTES" label="备注"/>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO;PROJECT_STATUS_ID=project.PROJECT_STATUS_ID"/>
                <field name="project_name" label="工程名称"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"  readOnly="true" equired="true" default="80204666"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="100077"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="100063"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="入库时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="ASSET_CODE" label="资产编码"/>
                <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="INTO_NETWORK_DATE" label="入网日期"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="CHECKING_PERSON" label="检查人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="width:6000px;" autoLoad="false">
            <field name="CODE" label="电缆编码"/>
            <field name="NAME" label="电缆名称"/>
            <field name="site" label="所属局站"/>
            <field name="LIFE_STATE_ID" label="生命周期状态"/>
            <field name="DISMANTLED_STATE_ID" label='退网状态'/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="CREATE_DATE" label="录入时间"/>
            <field name="LENGTH" label="长度"/>
            <field name="TOPO_ID" label="拓扑结构"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            <field name="LONG_LOCAL_ID" label="长本属性"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="LAY_FASHION_ID" label="敷设方式"/>
            <field name="CATEGORY_ID" label="电缆类型"/>
            <field name="MODEL_ID" label="型号"/>
            <field name="DIAMETER_ID" label="电缆线径"/>
            <field name="MNT_LEVEL_ID" label="维护等级"/>
            <field name="CAPACITY" label="电缆线对总数"/>
            <field name="CABLE_FILLING_TYPE_ID" label="填充类型"/>
            <field name="NOTES" label="备注"/>
            <field name="ASSET_CODE" label="资产编码"/>
            <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号"/>
            <field name="CREATOR" label="录入人"/>
            <field name="MODIFIER" label="修改人"/>
            <field name="AUDIT_PERSON" label="验收人"/>
            <field name="PROPERTY_TYPE_ID" label="产权性质"/>
            <field name="PROPERTY_OWNER_ID" label="产权归属"/>
            <field name="project_value.CODE" label="所属工程"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
        </grid>
        <grid name="lineCutOverResult">
            <field name="METACATEGORYCN" label="实体类型"/>
            <field name="AFTER_CODE" label="新编码(可编辑)" allowedEdit="true"/>
            <field name="AFTER_ROUTE" label="新成端线序"/>
            <field name="AFTER_SITE" label="新所属局站"/>
            <field name="BEFORE_CODE" label="旧编码"/>
            <field name="BEFORE_ROUTE" label="旧成端线序"/>
            <field name="BEFORE_SITE" label="旧所属局站"/>
        </grid>
    </ObjMeta>
    <!-- 电缆模板 -->
    <ObjMeta objectType="ECABLE.TEMPLATE" labelField="NAME" autoReload="true" typeActions="add,remove" itemActions="modify,remove">
        <!--查询条件-->
        <form name="search" labelWidth="6em">
            <field name="NAME" label="模板名称"/>
        </form>
        <!--查询结果列表-->
        <grid tableStyle="" autoLoad="false">
            <field name="NAME" label="模板名称"/>
            <field name="CREATOR" label="创建人"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--新增/修改面板-->
        <form>
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="LENGTH" label="长度"/>
                <field name="LONG_LOCAL_ID" label="长本属性"/>
            </row>
            <row>
                <field name="TOPO_ID" label="拓扑结构" required="true"/>
                <field name="CATEGORY_ID" label="电缆类型" required="true"/>
            </row>
            <row>
                <field name="LAY_FASHION_ID" label="敷设方式"/>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true"/>
            </row>
            <row>
                <field name="DIAMETER_ID" label="电缆线径"/>
                <field name="CAPACITY" label="电缆线对总数" required="true"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
                <field name="region" label="所属区域"/>
            </row>
        </form>
        <action name="genCode" type="script">
            <![CDATA[
            var cableType = CATEGORY_ID.id;
            var cts = {'80203388': 'ZG', '80203389': 'PX', '80203390': 'ZJ', '80203391': 'LL',  };
            var ctType = cts[cableType];
            var pre = site.CODE + '/' + ctType;
            _bean.autoCode(this, pre, 3, 'ECABLE', {CATEGORY_ID: cableType, site: site.id});
        ]]>
        </action>
    </ObjMeta>

    <!-- ECABLESECTION 电缆段 -->
    <ObjMeta objectType="ECABLESECTION" autoReload="true" needgeom="false"
             typeActions="add,templates,reset,remove"
             itemActions="locate,modify,remove,assetAttribute,ecablecoils,dreserves,qmqss,wirepair,associatedResource"
             moreItemActions="laying,clearLaying,filesman,backOffNetworkStatusRepair,ecable_relations_window,layingByGrid">
        <action name="ecable_relations_window" label="解除关系" type="window" width="600" contentUrl="apps/pipe/ecable/action/ecable_relations_window.html" />
        <!--关联资源生命周期状态维护-->
        <action name="associatedResource" label="关联资源生命周期状态维护" type="script">
            <![CDATA[
                let data =  _actionContext.params;
                let content = $(`
                    <h4 style="margin-left: 60px;">是否将对应电缆段的生命周期状态翻转为退网</h4>
                `);
                //取消按钮
				let cancelBtn = $('<div class="ui cancel button" id="cancelBtn">'+ "取消" +'</div>');
				//确定按钮
				let sureBtn = $('<div class="ui button" id="sureBtn">'+ "确定" +'</div>');
                //弹窗
                let win = _sui.showCommonModal("",content,[sureBtn,cancelBtn]);
                //确定按钮绑定事件
                win.on("click","#sureBtn",function (e) {
				    e.preventDefault();
				   _bean.callService("electricityCablePointServiceImpl","associatedResource",[{"cableId":data.id}]).then(function (res) {
                        alert("修改成功");
                    });
					//关闭窗口
				    _sui.closeModal(win);
				});
            ]]>
        </action>
        <action type="workspace" name="templates" label="模板管理" label-en="Template management" title="电缆段模板"
                title-en="ECABLESECTION Template" objectType="ECABLESECTION.TEMPLATE" icon="columns"/>
        <action type="workspace" name="wirepair" objectType="LINESEQ" label="线对管理" label-en="Line Management"
                title="${NAME}-线对管理" title-en="${NAME} - Line Management" url="modules/ecable/ecableline.html?id=${id}&amp;objecttype=ecablesection"
                extra="_ui_class:gray"/>
        <action name="laying" label="敷设" label-en="Laying" title="敷设-${NAME}" title-en="Laying -${NAME}"
                url="main.html?config=apps/pipe/pipeline/layingBySec.xml&amp;secId=${id}"/>
        <action type="workspace" name="ecablecoils" objectType="ECABLECOIL" label="电缆盘留"
                label-en="Cable Segment Management" title="${NAME}-电缆盘留" title-en="${NAME} - ECABLECOIL"
                options='{"baseParams": {"cable": "${id}"}}'/>
        <action type="workspace" name="dreserves" objectType="DRESERVE" label="电缆预留" label-en="Cable Segment Management"
                title="${NAME}-电缆预留" title-en="${NAME} - DRESERVE"
                options='{"baseParams": {"ecablesection": "${id}"}}'/>
        <action type="workspace" name="qmqss" objectType="QMQS" label="气门气塞" label-en="Cable Segment Management"
                title="${NAME}-气门气塞" title-en="${NAME} - QMQS" options='{"baseParams": {"cable": "${id}"}}'/>
        <!--查询条件-->
        <form name="search" labelWidth="6em">
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码"/>
                <field name="NAME" label="名称"/>
            </row>
            <row>
                <field name="site" label="所属局站"/>
                <field name="CABLE_TYPE_ID" label="缆线类型"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态"/>
                <field name="DISMANTLED_STATE_ID" label='退网状态'/>
            </row>
            <row>
                <field name="LENGTH" label="长度"/>
                <field name="CAPACITY" label="线对数"/>
            </row>
            <row>
                <field name="LAYING_WAY_ID" label="敷设方式"/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="adevice" required="true"
                       baseParams='{"SPEC_ID":"MDF,CCP,DP,DT,ZHX,INFORMATIONPOINT,DRESERVE","excludeTemplate": "true"}'/>
                <field name="zdevice" required="true"
                       baseParams='{"SPEC_ID":"MDF,CCP,DP,DT,ZHX,INFORMATIONPOINT,DRESERVE","excludeTemplate": "true"}'/>
            </row>
            <row>
                <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
                <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            </row>
            <row>
                <field name="region" label="所属区域"/>
                <field name="CREATOR" label="录入人"/>
            </row>
            <row>
                <field name="project" label="工程名称" dropdownOptions="titleField:NAME"/>
                <field name="PROJECT_STATUS_ID" label="工程状态"/>
            </row>
            <row>
                <field name="id" label="实物ID"/>
            </row>
        </form>
        <!--新增/修改面板-->
        <form>
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="名称" required="true"/>
            </row>
            <row>
                <field name="NAME" label="编码" required="true"/>
            </row>
            <row>
                <field name="site" label="所属局站" dropdownOptions="titleField:NAME" required="true"/>
                <field name="net" label="所属电缆" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="LENGTH" label="实际长度(m)" required="true"/>
                <field name="MAP_LENGTH" label="地图长度(m)"/>
            </row>
            <row>
                <field name="START_LINE_SEQ" label="成端起始线序组" required="true"/>
                <field name="END_LINE_SEQ" label="成端终止线序组" required="true"/>
            </row>
            <row>
                <field name="CABLE_TYPE_ID" label="缆线类型" required="true"/>
                <field name="CAPACITY" label="线对数" required="true"/>
            </row>
            <row>
                <field name="adevice" required="true"
                       baseParams='{"SPEC_ID":"MDF,CCP,DP,DT,ZHX,INFORMATIONPOINT,DRESERVE","excludeTemplate": "true"}'/>
                <field name="zdevice" required="true"
                       baseParams='{"SPEC_ID":"MDF,CCP,DP,DT,ZHX,INFORMATIONPOINT,DRESERVE","excludeTemplate": "true"}'/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true" default="80204848"/>
               
            </row>
            <row>
                <field name="EXIT_NETWORK_DATE" label="退网日期" readOnly="true"/>
            </row>
            <row>
                <field name="DIAMETER" label="直径"/>
                <field name="MNT_LEVEL_ID" label="维护等级"/>
            </row>
            <row>
                <field name="LAYING_WAY_ID" label="敷设方式"/>
                <field name="POWER_CABLE_SPEC" label="规格型号"/>
            </row>
            <row>
                <field name="DEPRECIATION" label="折旧年限(年)"/>
                <field name="USE_AGE_LIMIT" label="使用年限（年）"/>
            </row>
            <row>
                <field name="DISMANTLED_STATE_ID" label='退网状态'/>
                <field name="MNT_WAY_ID" label="维护方式"/>
            </row>
            <row>
                <field name="vendor" label="生产厂商"/>
                <field name="RESERVE_LENGTH" label="预留长度（米）"/>
            </row>
            <row>
                <field name="NOTES" label="备注"/>
                <field name="USING_DEPARTMENT" label="使用部门"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
                <field name="PROJECT_STATUS_ID" label="工程状态" readOnly="true" equired="true" default="80204666"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="asset" label="资产目录" dropdownOptions="titleField:ASSET_CATALOGUE" />
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true" default="80209209" multiple="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true" default="80209223" multiple="true"/>
            </row>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="入库时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="MNT_PERSON" label="维护人"/>
                <field name="INTO_NETWORK_DATE" label="入网日期"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="采集部门名称"/>
                <field name="COLLECTION_PERSON" label="采集人名称"/>
            </row>
            <row>
                <field name="CHECKING_PERSON" label="检查人名称"/>
            </row>
            <row>
                <field name="id" label="实物ID" readOnly="true"/>
                <field name="MATERIAL_OBJECT_ID" label="实物ID_旧" readOnly="true"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>
        <grid name="mini">
            <field name="CODE" label="编号"/>
            <field name="NAME" label="名称"/>
            <field name="site" label="局站"/>
            <field name="net" label="所属缆"/>
            <field name="CABLE_TYPE_ID" label="缆线类型"/>
            <field name="LENGTH" label="长度"/>
            <field name="CAPACITY" label="线对数"/>
            <field name="CREATE_DATE" label="创建时间"/>
        </grid>
        <!--查询结果列表-->
        <grid tableStyle="width:6000px;" autoLoad="false">
            <field name="CODE" label="电缆段编码" width="120px"/>
            <field name="NAME" label="电缆段名称" width="120px"/>
            <field name="site" label="所属局站" getLabel="row.site_value.NAME"/>
            <field name="net" label="所属电缆名称" getLabel="row.ECABLESECTION_value.NAME"/>
            <field name="net" label="所属电缆编码" getLabel="row.ECABLESECTION_value.CODE"/>
            <field name="CABLE_TYPE_ID" label="缆线类型"/>
            <field name="CAPACITY" label="线对数"/>
            <field name="LAYING_WAY_ID" label="敷设方式"/>
            <field name="MNT_WAY_ID" label="维护方式"/>
            <field name="LIFE_STATE_ID" label="生命周期状态"/>
            <field name="DISMANTLED_STATE_ID" label='退网状态'/>
            <field name="EXIT_NETWORK_DATE" label="退网日期"/>
            <field name="PROJECT_STATUS_ID" label="工程状态"/>
            <field name="CREATE_DATE" label="录入时间"/>
            <field name="adevice"/>
            <field name="zdevice"/>
            <field name="region" label="所属区域" getLabel="row.region_value.NAME"/>
            <field name="smallcounty" label="划小区县" getLabel="row.smallcounty_value.NAME"/>
            <field name="marketingarea" label="划小营销区" getLabel="row.marketingarea_value.NAME"/>
            <field name="LENGTH" label='长度'/>
            <field name="NOTES" label="备注"/>
            <field name="MNT_LEVEL_ID" label="维护等级"/>
            <field name="MAP_LENGTH" label="地图长度(m)"/>
            <field name="START_LINE_SEQ" label="成端起始线序组"/>
            <field name="END_LINE_SEQ" label="成端终止线序组"/>
            <field name="ASSET_CODE" label="资产编码"/>
            <field name="CHILD_ASSCARD_CODE" label="子资产卡片编号"/>
            <field name="CREATOR" label="录入人"/>
            <field name="MODIFIER" label="修改人"/>
            <field name="AUDIT_PERSON" label="验收人"/>
            <field name="PROPERTY_TYPE_ID" label="产权性质"/>
            <field name="PROPERTY_OWNER_ID" label="产权归属"/>
            <field name="MATERIAL_OBJECT_ID" label="实物ID_旧"/>
            <field name="id" label="实物ID"/>
        </grid>

        <!-- 资源关联电缆段的列表 -->
        <grid name="resourcesRelationEcablesectionGrid" toolbar="false" footer="false" rowCheckable="false" showRowNum="true" typeActions=""
              itemActions="fibers">
            <field name="CODE"/>
            <field name="NAME"/>
        </grid>

        <grid name="eCableSectionMerge">
            <field name="CODE"/>
            <field name="NAME"/>
            <field name="START_LINE_SEQ"/>
            <field name="END_LINE_SEQ"/>
        </grid>
        <grid name="eCableSectionMerge2">
            <field name="CODE"/>
            <field name="NAME"/>
            <field name="adevice"/>
            <field name="zdevice"/>
            <field name="CAPACITY"/>
        </grid>
        <grid name="lineRoute">
            <field name="net" getLabel="row.net.CODE"/>
            <field name="CODE"/>
            <field name="NAME"/>
            <field name="LENGTH"/>
            <field name="adevice" label="A设备名称" orderBy="false" getLabel="row.adevice.NAME"/>
            <field name="adevice" label="A设备编码" orderBy="false" getLabel="row.adevice.CODE"/>
            <field name="zdevice" label="Z设备名称" orderBy="false" getLabel="row.zdevice.NAME"/>
            <field name="zdevice" label="Z设备编码" orderBy="false" getLabel="row.zdevice.CODE"/>
        </grid>
    </ObjMeta>
    <!-- 电缆段模板 -->
    <ObjMeta objectType="ECABLESECTION.TEMPLATE" labelField="NAME" autoReload="true" typeActions="add,remove" itemActions="modify,remove">
        <form>
            <row>
                <field name="site" onChange="CODE,NAME=@genCode(CABLE_TYPE_ID,site)"/>
            </row>
            <row>
                <field name="adevice"/>
                <field name="zdevice"/>
            </row>
            <row>
                <field name="CODE"/>
                <field name="NAME"/>
            </row>
            <row>
                <field name="DIAMETER"/>
                <field name="CAPACITY"/>
            </row>
            <row>
                <field name="LONG_LOCAL_ID"/>
                <field name="LIFE_STATE_ID"/>
            </row>
            <row>
                <field name="LENGTH"/>
                <field name="NOTES"/>
            </row>
            <row>
                <field name="MNT_LEVEL_ID"/>
                <field name="MNT_WAY_ID"/>
            </row>
            <row>
                <field name="DISMANTLED_STATE_ID"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称" dropdownOptions="titleField:NAME"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="ASSET_CODE"/>
                <field name="CHILD_ASSCARD_CODE"/>
            </row>
            <row>
                <field name="CREATOR"/>
                <field name="CREATE_DATE"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_ID"/>
                <field name="PROPERTY_OWNER_ID"/>
            </row>
            <row>
                <field name="INTO_NETWORK_DATE"/>
                <field name="MNT_PERSON"/>
            </row>
            <row>
                <field name="AUDIT_PERSON"/>
                <field name="AUDIT_DATE"/>
            </row>
            <row>
                <field name="vendor"/>
            </row>
        </form>

        <action name="genCode" type="script">
            <![CDATA[
            var pre = adevice.CODE + '-' + zdevice.CODE + '/';
            _bean.autoCode(this, pre, 3, 'ECABLESECTION', {ecable: ecable.id});
        ]]>
        </action>
    </ObjMeta>

    <!-- LINESEQ 电缆线对 -->
    <ObjMeta objectType="LINESEQ" autoReload="false" needgeom="false"
             typeActions="releaseport,modifyPhysicStatus,modifBussinessState"
             itemActions="locate,modify,remove,occupationInfo,lineroute,modifyPhysicStatus,modifBussinessState">
        <action name="locate" type="script">
          <![CDATA[ 
            _bean.find('LINESEQ', {id: _actionContext.params.id, _assemble: 'ecable'}).then(function(o){
              _context.doAction('locateNetCable', o.ecable);
            });
          ]]>
        </action>
        <action type="workspace" name="occupationInfo" objectType="LINESEQ" label="占用信息"
                label-en="Occupation information" title="${NAME}-占用信息" title-en="${NAME} - Occupation information"
                url="modules/ecable/lineoccupationInfo.html?id=${id}" extra="_ui_class:gray"/>
        <action type="workspace" name="lineroute" objectType="LINESEQ" label="电缆线对路由" label-en="Line Route"
                title="${NAME}-电缆线对路由" title-en="${NAME} - Line Route" url="modules/ecable/lineRoute.html?id=${id}"
                extra="_ui_class:gray"/>
        <action name="modifyPhysicStatus" label="修改物理状态" label-en="Modify PhysicStatus" type="window" itemsable="true"
                contentUrl="modules/ecable/modifyLineseqPhysicalState.html"/>
        <action name="modifBussinessState" label="修改业务状态" label-en="Modify BussinessState" type="window" itemsable="true"
                contentUrl="modules/ecable/modifyLineseqBussinessState.html"/>
        <grid autoLoad="false">
            <field name="SEQ" label="序号"/>
            <field name="USING_STATE_ID" label="业务状态"/>
            <field name="PHYSICAL_STATE_ID" label="物理状态"/>
            <field name="LOCK_STATE_ID" label="封锁状态"/>
            <field name="adevice" label="起始设备名称" getLabel="row.adevice_value.NAME"/>
            <field name="adevice" label="起始设备编码" getLabel="row.adevice_value.CODE"/>
            <field name="aport" label="起始端口编码" getLabel="row.aport_value.CODE"/>
            <field name="aport" label="起始端口序号" getLabel="row.aport_value.SEQ"/>
            <field name="zdevice" label="终止设备名称" getLabel="row.zdevice_value.CODE"/>
            <field name="zdevice" label="终止设备编码" getLabel="row.zdevice_value.NAME"/>
            <field name="zport" label="终止端口编码" getLabel="row.zport_value.CODE"/>
            <field name="zport" label="终止端口序号" getLabel="row.zport_value.SEQ"/>
        </grid>
        <grid name="ecableOccupationInfo">
            <field name="SEQ"/>
            <field name="isConnected" label="是否成端"/>
            <field name="useingState" label="业务状态"/>
            <field name="accessCode" label="业务号码"/>
            <field name="adevice" label="起始设备名称" orderBy="false" getLabel="row.adevice.NAME"/>
            <field name="adevice" label="起始设备编码" orderBy="false" getLabel="row.adevice.CODE"/>
            <field name="aport" label="起始端口编码" orderBy="false" getLabel="row.aport.CODE"/>
            <field name="zdevice" label="终止设备名称" orderBy="false" getLabel="row.zdevice.CODE"/>
            <field name="zdevice" label="终止设备编码" orderBy="false" getLabel="row.zdevice.NAME"/>
            <field name="zport" label="终止端口编码" orderBy="false" getLabel="row.zport.CODE"/>
        </grid>
        <grid name="occupationInfo">
            <field name="CODE"/>
            <field name="ecable" label="电缆编码" getLabel="row.ecable.CODE"/>
            <field name="useingState" label="业务状态"/>
            <field name="accessCode" label="业务号码"/>
            <field name="pruductTypeName" label="产品类型"/>
            <field name="adevice" label="起始设备名称" orderBy="false" getLabel="row.adevice.NAME"/>
            <field name="adevice" label="起始设备编码" orderBy="false" getLabel="row.adevice.CODE"/>
            <field name="aport" label="起始端口编码" orderBy="false" getLabel="row.aport.CODE"/>
            <field name="zdevice" label="终止设备名称" orderBy="false" getLabel="row.zdevice.CODE"/>
            <field name="zdevice" label="终止设备编码" orderBy="false" getLabel="row.zdevice.NAME"/>
            <field name="zport" label="终止端口编码" orderBy="false" getLabel="row.zport.CODE"/>
            <field name="customerName" label="客户名称"/>
            <field name="installAddress" label="装机地址"/>
        </grid>
    </ObjMeta>

    <ObjMeta objectType="MODULE" autoReload="true" needgeom="false" itemActions="modify,remove">
        <form name="addModuleForEconnector" labelWidth="8rem">
            <row>
                <field name="startModuleCode" label="起始模块编号" type="number" required="true"/>
                <field name="ARRANGEMENT_ID" label="模块排列方式" default="100351" type="dict" required="true"
                       dropdownOptions='{"floatingMenu":false}'/>
            </row>
            <row>
                <field name="startModuleRowNo" label="起始模块行号" type="number" required="true"/>
                <field name="startModuleColNo" label="起始模块列号" type="number" required="true"/>
            </row>
            <row>
                <field name="rowNum" default="1" label="每列模块数" type="number" required="true"/>
                <field name="colNum" default="1" readOnly="true" label="每行模块数" type="number" required="true"/>
            </row>
            <row>
                <field name="startPortCode" default="1" label="端子起始编码" type="number" required="true"/>
                <field name="portArrangement" default="100351" type="dict" required="true"
                       dropdownOptions='{"floatingMenu":false}'/>
            </row>
            <row>
                <field name="portRowNum" default="10" label="模块内端子行数" type="number" required="true"/>
                <field name="portColNum" label="模块内端子列数" default="10" type="number" required="true"/>
            </row>
            <row>
                <field name="CONNECTORCODEMODE" default="80402155" required="true"
                       dropdownOptions='{"floatingMenu":false}'/>
                <field name="portStartNo" label="端子起始序号" type="number" required="true"/>
            </row>
        </form>
        <form name="addModuleForEconnectorSXZ" labelWidth="8rem">
            <row>
                <field name="startModuleCode" label="起始模块编号" type="number" required="true"/>
                <field name="ARRANGEMENT_ID" label="模块排列方式" default="100351" type="dict" required="true"
                       dropdownOptions='{"floatingMenu":false}'/>
            </row>
            <row>
                <field name="startModuleRowNo" label="起始模块行号" type="number" required="true"/>
                <field name="startModuleColNo" label="起始模块列号" type="number" required="true"/>
            </row>
            <row>
                <field name="rowNum" default="1" label="每列模块数" type="number" required="true"/>
                <field name="colNum" default="1" readOnly="true" label="每行模块数" type="number" required="true"/>
            </row>
            <row>
                <field name="startPortCode" default="1" label="端子起始编码" type="number" required="true"/>
                <field name="portArrangement" default="100351" type="dict" required="true"
                       dropdownOptions='{"floatingMenu":false}'/>
            </row>
            <row>
                <field name="portRowNum" default="10" label="模块内端子行数" type="number" required="true"/>
                <field name="portColNum" label="模块内端子列数" default="10" type="number" required="true"/>
            </row>
            <row>
                <field name="CONNECTORCODEMODE" default="80402155" required="true"
                       dropdownOptions='{"floatingMenu":false}'/>
                <field name="portStartNo" label="端子起始序号" type="number" required="true"/>
            </row>
            <row><field name="PORT_LAYOUT_ID" label="焊点桩位"/></row>
        </form>
        <grid name="bindProjectGrid">
            <field name="SEQ" label="端子序号"/>
            <field name="CODE" label="端子编码"/>
            <field name="CODE" label="模块编码" getLabel="row.card.CODE"/>
        </grid>

    </ObjMeta>

    <!--电缆盘留 -->
    <ObjMeta objectType="ECABLECOIL" autoReload="true" needgeom="false" itemActions="modify,filesman,remove">
        <form>
            <field type="divider" label="基础属性" label-en="Basic Attribute"/>
            <row>
                <field name="CODE" label="编码" required="true"/>
                <field name="NAME" label="名称" required="true"/>
            </row>
            <row>
                <field name="site" label="所属局站" required="true" dropdownOptions="titleField:NAME"/>
                <field name="cable" label="所属缆段" dropdownOptions="titleField:NAME" baseParams='{"SPEC_ID":"ECABLESECTION"}'/>
            </row>
            <row>
                <field name="facility" label="所属支撑设施"/>
                <field name="LENGTH" label="长度（米）"/>
            </row>
            <row>
                <field name="LIFE_STATE_ID" label="生命周期状态" required="true"/>
                <field name="ASSISTANCE_TYPE_ID" label="辅助设备类型" readOnly="true"/>
            </row>
            <row>
                <field name="MNT_WAY_ID" label="维护方式"/>
                <field name="ASSET_CODE" label="资产编码"/>
            </row>
            <row>
                <field name="PROJECT_STATUS_ID" label="工程状态" required="true"  readOnly="true" default="80204666"/>
                <field name="NOTES" label="备注"/>
            </row>
            <field type="divider" label="划小属性" label-en="Small district attribute"/>
            <row>
                <field name="smallcounty" label="划小区县" dropdownOptions="titleField:NAME" required="true"/>
                <field name="marketingarea" label="划小营销区" dropdownOptions="titleField:NAME" required="true"/>
            </row>
            <row>
                <field name="servicearea" label="营业区名称"/>
            </row>
            <field type="divider" label="资产属性" label-en="Asset attribute"/>
            <row>
                <field name="CREATOR" label="录入人" readOnly="true"/>
                <field name="CREATE_DATE" label="录入时间" readOnly="true"/>
            </row>
            <row>
                <field name="MODIFIER" label="修改人" readOnly="true"/>
                <field name="MODIFY_DATE" label="修改时间" readOnly="true"/>
            </row>
            <row>
                <field name="AUDIT_PERSON" label="验收人"/>
                <field name="AUDIT_DATE" label="验收时间"/>
            </row>
            <row>
                <field name="PROPERTY_TYPE_ID" label="产权性质" required="true"/>
                <field name="PROPERTY_OWNER_ID" label="产权归属" required="true"/>
            </row>
            <row>
                <field name="COLLECTION_DEPT" label="数据采集单位"/>
                <field name="COLLECTION_PERSON" label="数据采集人"/>
            </row>
            <row>
                <field name="COLLECTION_DATE" label="数据采集日期"/>
                <field name="CHECKING_PERSON" label="检查责任人"/>
            </row>
            <row>
                <field name="START_USE_DATE" label="启用时间"/>
                <field name="STOP_USE_DATE" label="停用日期"/>
            </row>
            <field type="divider" label="工程属性" label-en="Engineering property"/>
            <row>
                <field name="project" label="工程编码"
                       onChange="project_name=project.NAME;project_serialno=project.SERIALNO"/>
                <field name="project_name" label="工程名称"/>
            </row>
            <row>
                <field name="project_serialno" label="工程流水号"/>
            </row>
            <script src="apps/gdo3/common/validate_controls.js"/>
        </form>

    </ObjMeta>

    <ObjMeta objectType="PR_PORTECONNECT" autoReload="true" needgeom="false" typeActions="" itemActions="">
        <form name="MDFCableConn">
            <row>
                <field name="deviceCode" readOnly="true" label="设备名称" required="true"/>
                <field name="ecable" required="true"/>
            </row>
            <row>
                <field name="STARTPORTSEQ" onChange="ENDLINESEQ=@getEndlineseq()" required="true"/>
                <field name="ENDPORTSEQ" onChange="ENDLINESEQ=@getEndlineseq()" required="true"/>
            </row>
            <row>
                <field name="STARTLINESEQ" onChange="ENDLINESEQ=@getEndlineseq()" required="true"/>
                <field name="ENDLINESEQ" readOnly="true" required="true"/>
            </row>
            <row>
                <field name="COLNUM" required="true"/>
            </row>
        </form>
        <form name="EdeviceCableConn">
            <row>
                <field name="deviceCode" readOnly="true" required="true" label="设备名称"/>
                <field name="ecable" required="true"/>
            </row>
            <row>
                <field name="STARTPORTSEQ" onChange="ENDLINESEQ=@getEndlineseq()" required="true"/>
                <field name="ENDPORTSEQ" onChange="ENDLINESEQ=@getEndlineseq()" required="true"/>
            </row>
            <row>
                <field name="STARTLINESEQ" onChange="ENDLINESEQ=@getEndlineseq()" required="true"/>
                <field name="ENDLINESEQ" readOnly="true" required="true"/>
            </row>
        </form>
        <action name="getEndlineseq" type="script">
            <![CDATA[
                if(!isNaN(ENDPORTSEQ)&&!isNaN(STARTPORTSEQ)&&!isNaN(STARTLINESEQ)){
                    STARTLINESEQ+(ENDPORTSEQ-STARTPORTSEQ);
                }
            ]]>
        </action>
        <grid itemActions="getConnInfo">
            <field name="device" getLabel="row.device_value.CODE" label="设备编码"/>
            <field name="COLNUM"/>
            <field name="STARTPORTSEQ"/>
            <field name="ENDPORTSEQ"/>
            <field name="ecable" getLabel="row.ecable_value.CODE" label="电缆编码"/>
            <field name="STARTLINESEQ"/>
            <field name="ENDLINESEQ"/>
            <field name="CONNECTDIRECTION"/>
            <action name="getConnInfo" label="电缆成端信息表" type="window" maxable="false" style="height:200px;width:600px"
                    contentUrl="modules/econnector/econnectorManagerInfo.html"/>
        </grid>

        <grid name="connectedInfo">
            <field name="lineSeq" label="成端线序"/>
            <field name="devCode" label="设施编码"/>
            <field name="portSeq" label="端子序列"/>
        </grid>
        <grid name="ePortCutOver">
            <field name="COLNUM"/>
            <field name="STARTPORTSEQ"/>
            <field name="ENDPORTSEQ"/>
            <field name="STARTLINESEQ"/>
            <field name="ENDLINESEQ"/>
            <field name="ecable" getLabel="row.ecable.CODE" label="电缆编码"/>
        </grid>
    </ObjMeta>
    <ObjMeta objectType="COLUMNFRAME" autoReload="true" needgeom="false" typeActions="" itemActions="">
        <form name="addColumnframe">
            <row>
                <field name="COL_TYPE" required="true" dropdownOptions='{"floatingMenu":false}'/>
                <field name="START_COL" label="起始列号" required="true"/>
            </row>
            <row>
                <field name="COL_NUM" default="1" readOnly="true"/>
                <field name="ARRANGEMENT" required="true" dropdownOptions='{"floatingMenu":false}'/>
            </row>
            <row>
                <field name="LENGTH"/>
                <field name="WIDTH"/>
            </row>
            <row>
                <field name="HEIGHT"/>
                <field name="COL_NO" label="起始位置" required="true"/>
            </row>
        </form>
    </ObjMeta>
</metas>