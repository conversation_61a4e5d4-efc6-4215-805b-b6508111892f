﻿/*
 * ============================================================================
 * 功能模块：HyAssistant程序入口模块
 * ============================================================================
 * 
 * 模块作用：作为HyAssistant应用程序的入口点，负责初始化应用程序环境、
 *           处理多实例运行控制和启动主窗体
 * 
 * 主要功能：
 * - 控制应用程序单实例运行或允许多开
 * - 初始化Windows Forms应用程序环境
 * - 创建并运行主窗体
 * 
 * 执行逻辑：
 * 1. 使用Mutex实现单实例控制
 * 2. 检测是否已有程序实例在运行
 * 3. 如果已有实例运行，提示用户选择是否继续启动
 * 4. 初始化应用程序的视觉样式和文本渲染默认值
 * 5. 创建并运行主窗体
 * 6. 在程序退出时正确释放Mutex资源
 * 
 * 注意事项：
 * - Mutex的唯一标识符应保持不变以确保正确的实例控制
 * - 使用try-finally确保Mutex在程序退出时被正确释放
 * - 允许用户选择是否启动多个程序实例
 * ============================================================================
 */

using System;
using System.Threading;
using System.Windows.Forms;

namespace HyAssistant
{
    static class Program
    {
        // Mutex唯一标识，建议保持不变
        static Mutex mutex = new Mutex(true, "{E5B60C88-59E7-4F6F-AB7F-2A6AEDD9BECF}");
        public static MainForm mainForm;

        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            bool isNewInstance = false;
            try
            {
                // 尝试获取Mutex
                isNewInstance = mutex.WaitOne(TimeSpan.Zero, true);

                if (!isNewInstance)
                {
                    // 已有实例，弹窗让用户选择
                    DialogResult result = MessageBox.Show("程序已经在运行中！\n是否继续启动另一个实例？", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning);
                    if (result == DialogResult.Cancel)
                    {
                        // 用户选择取消，优雅退出
                        return;
                    }
                    // 用户选择继续，不持有Mutex，允许多开
                }

                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // 设置日志路径
                //string logsPath = Path.Combine(Application.StartupPath, "logs");
                //ETLogManager.LogPath = logsPath;

                mainForm = new MainForm();
                Application.Run(mainForm);
            }
            finally
            {
                // 仅新实例需要释放Mutex
                if (isNewInstance)
                {
                    mutex.ReleaseMutex();
                }
            }
        }
    }
}
