var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
var _sui;
(function (_sui) {
    _sui.EVENT_COMPONENT_RENDERED = 'EVENT_COMPONENT_RENDERED';
    _sui.EVENT_UPLOAD_SUCCESS = 'EVENT_UPLOAD_SUCCESS';
    _sui.formFieldFactory = {};
    _sui.API_GAP_TIME = 1000;
    _sui.defaultUploadOptions = {};
    if ($.fn.api)
        $.fn.api.settings.silent = true;
    if ($.fn.dimmer)
        $.fn.dimmer.settings.opacity = 0;
    if ($.fn.form)
        $.fn.form.settings.keyboardShortcuts = false;
    function hasPermission(permission) {
        return window['_context'] && window['_context'].hasPermission(permission) !== false;
    }
    function toApiSetting(dataProvider, beforeFn, afterFn, resultFixFn) {
        if (beforeFn === void 0) { beforeFn = null; }
        if (afterFn === void 0) { afterFn = null; }
        if (resultFixFn === void 0) { resultFixFn = null; }
        return {
            responseAsync: function (settings, callback) {
                if (beforeFn)
                    beforeFn();
                _util.dataCallback(function (ret, isError) {
                    if (resultFixFn)
                        ret = resultFixFn(ret, isError);
                    callback(ret, isError);
                    if (afterFn)
                        afterFn(ret, isError);
                }, dataProvider, [settings]);
            }
        };
    }
    _sui.toApiSetting = toApiSetting;
    function permissionCheckEl(el, permissionFn) {
        if (!_util.isjquery(el))
            el = $(el);
        var permission = el.data('permission') || el.attr('data-permission') || el.attr('permission');
        if (typeof permissionFn === 'string')
            permission = permissionFn;
        if (permission) {
            var pass = true;
            if (typeof permissionFn === 'function')
                pass = permissionFn(permission, el);
            else if (window['_context']) {
                pass = _context.hasPermission(permission);
            }
            if (!pass) {
                console.log('permission check fail:' + el);
                el.hide();
                return false;
            }
        }
        return true;
    }
    _sui.permissionCheckEl = permissionCheckEl;
    function permissionHandleEl(selector, mode, permissionFn) {
        if (mode === void 0) { mode = undefined; }
        if (permissionFn === void 0) { permissionFn = undefined; }
        var el = _util.isjquery(selector) ? selector : $(selector);
        mode = mode || _context.getSetting('permissionHandleElMode') || 'hide';
        el.each(function () {
            var e = $(this);
            if (!permissionCheckEl(e, permissionFn)) {
                if (mode === 'hide')
                    e.hide();
                else if (mode === 'disable')
                    e.addClass('disabled');
                else if (mode === 'remove')
                    e.remove();
            }
        });
    }
    _sui.permissionHandleEl = permissionHandleEl;
    function fetchLabel(o) {
        if (!o)
            return '';
        if (typeof o === 'string')
            return o;
        var fs = ['name', 'code', 'title', 'label', 'NAME', 'CODE'];
        return _util.fetchField(o, fs);
    }
    _sui.fetchLabel = fetchLabel;
    function api(el, fn, permissionHandle) {
        if (permissionHandle === void 0) { permissionHandle = true; }
        if (!_util.isjquery(el))
            el = $(el);
        if (permissionHandle) {
            if (!permissionCheckEl(el, permissionHandle))
                return Promise.reject();
        }
        return new Promise(function (resolve, reject) {
            el.api(toApiSetting(fn, function () {
                el.addClass('disabled');
                el.__lastApiTimestamp = performance.now();
            }, function (data, isError) {
                if (el.__lastApiTimestamp && performance.now() - el.__lastApiTimestamp < _sui.API_GAP_TIME) {
                    setTimeout(function () { return el.removeClass('disabled'); }, _sui.API_GAP_TIME);
                }
                else
                    el.removeClass('disabled');
                delete el.__lastApiTimestamp;
                if (!isError)
                    resolve(data);
                else
                    reject(data);
            }));
        });
    }
    _sui.api = api;
    function popup(el, options) {
        $(el).popup(options);
    }
    _sui.popup = popup;
    function htmlNode(node, content, _actionContext, globalEval, nodeDef) {
        if (nodeDef === void 0) { nodeDef = null; }
        if (!content)
            return;
        _actionContext = _actionContext || {};
        _actionContext.el = node;
        if (nodeDef)
            _actionContext.def = nodeDef;
        var scripts, styles;
        if (!globalEval) {
            var scriptRe = /<script(.(?!(src)))*?>[\s\S]*?<\/script>/ig;
            scripts = content.match(scriptRe);
            if (scripts)
                content = content.replace(scriptRe, '');
        }
        var styleRe = /<style(.(?!(src)))*?>[\s\S]*?<\/style>/ig;
        styles = content.match(styleRe);
        if (styles && styles.length) {
            content = content.replace(styleRe, '');
            styles.forEach(function (style) {
                style = style.replace(/<style.*?>/ig, '');
                style = style.replace(/<\/style>/ig, '');
                var styleNode = document.createElement('style');
                styleNode.innerHTML = style;
                document.head.appendChild(styleNode);
            });
        }
        node.html(content);
        if (scripts && scripts.length)
            scripts.forEach(function (script) {
                script = script.replace(/<script.*?>/ig, '');
                script = script.replace(/<\/script>/ig, '');
                if (_actionContext.evalScope) {
                    var init = '';
                    for (var k in _actionContext.evalScope) {
                        init += 'var ' + k + '=_actionContext.evalScope.' + k + ';';
                    }
                    script = init + '\r\n' + script;
                }
                eval(script);
            });
    }
    _sui.htmlNode = htmlNode;
    function setStyle(el, value) {
        if (!value)
            return;
        if (typeof value === 'string') {
            var css_1 = {}, pos_1 = -1;
            value.split(';').forEach(function (kv) {
                pos_1 = kv.indexOf(':');
                if (pos_1 !== -1) {
                    css_1[kv.substring(0, pos_1).trim()] = kv.substring(pos_1 + 1).trim();
                }
            });
            value = css_1;
        }
        el.css(value);
    }
    _sui.setStyle = setStyle;
    function setAttrs(el, value) {
        if (!value)
            return;
        if (typeof value === 'string') {
            var css_2 = {}, pos_2 = -1;
            value.split(';').forEach(function (kv) {
                pos_2 = kv.indexOf(':');
                if (pos_2 !== -1) {
                    css_2[kv.substring(0, pos_2).trim()] = kv.substring(pos_2 + 1).trim();
                }
            });
            value = css_2;
        }
        el.attr(value);
    }
    _sui.setAttrs = setAttrs;
    function render(node, nodeDef, _actionContext, append) {
        if (append === void 0) { append = false; }
        if (nodeDef.style) {
            setStyle(node, nodeDef.style);
        }
        if (nodeDef.class)
            node.addClass(nodeDef.class);
        if (node[0].tagName === 'ASIDE')
            node.css('height', '100%');
        if (append)
            node = $('<div style="width:100%;height:100%;overflow:auto;">').appendTo(node);
        if (nodeDef.id && !node.attr('id'))
            node.attr('id', nodeDef.id);
        if (nodeDef.accordionItem) {
            var items = nodeDef.accordionItem;
            if (!Array.isArray(items))
                items = [items];
            node.css('overflow', 'hidden');
            _sui.accordionMenu(node, {
                data: items, itemRender: function (item, el) {
                    render(el, item, _actionContext);
                }
            });
        }
        else if (nodeDef.contentUrl) {
            var url_1 = nodeDef.contentUrl;
            if (_context && _context.fixUrl)
                url_1 = _context.fixUrl(url_1);
            $.get(url_1, function (data) {
                var isVue = nodeDef.vue || data.indexOf('new Vue(') !== -1;
                data = _locale.replaceString(data, isVue ? /##.*?##/g : undefined);
                if (isVue)
                    _util.loadScripts(['lib/vue/vue.min.js', 'lib/vue/g5components.js'], 'Vue', true).then(function () { return htmlNode(node, data, _actionContext, nodeDef.tagName === 'html', nodeDef); });
                else
                    htmlNode(node, data, _actionContext, nodeDef.tagName === 'html', nodeDef);
            });
        }
        else {
            var url = nodeDef.url;
            if (url) {
                var iframe = $('<iframe allowfullscreen>').attr('src', url).height('100%').width('100%');
                node.html(iframe);
            }
            else
                htmlNode(node, nodeDef.html || nodeDef.content, _actionContext, nodeDef.tagName === 'html', nodeDef);
        }
    }
    _sui.render = render;
    function showLoading(el, message, task) {
        if (task === void 0) { task = undefined; }
        el = el || document.body;
        el = _util.isjquery(el) ? el : $(el);
        var already = el.data('__loadingDimmer');
        if (message === false) {
            if (already) {
                already.remove();
                el.removeData('__loadingDimmer');
            }
            return;
        }
        if (already)
            return;
        var dimmer = $('<div class="ui active dimmer">'), loader;
        if (message && _util.isjquery(message))
            loader = message;
        else {
            loader = $('<div class="ui loader tiny">');
            if (typeof message === 'string') {
                loader.addClass('text');
                loader.html(message);
            }
        }
        loader.appendTo(dimmer);
        dimmer.appendTo(el);
        el.data('__loadingDimmer', dimmer);
        if (task)
            _util.dataCallback(function () {
                showLoading(el, false);
            }, task);
    }
    _sui.showLoading = showLoading;
    function renderActionsTo(actionProvider, el, item, scope, options) {
        if (options === void 0) { options = {}; }
        _util.dataCallback(function (actions) {
            if (!actions)
                return;
            if (typeof actions === 'string')
                actions = actions.split(',');
            if (!Array.isArray(actions))
                actions = [actions];
            actions.forEach(function (a) {
                if (typeof a === 'string')
                    a = _context.findAction(a);
                if (_util.isjquery(a))
                    a.appendTo(el);
                else {
                    var span_1 = $('<span class="bar_action">').attr({ id: a.id || a.name || a.label, title: a.title || a.tooltip || a.tip || a.label || a.text }).appendTo(el);
                    if (a._ui_class)
                        span_1.addClass(a._ui_class);
                    var buttonStyle = options.buttonStyle === true;
                    if (buttonStyle) {
                        span_1.addClass('ui mini compact button');
                    }
                    if (a.icon) {
                        $('<i class="icon">').addClass(a.icon).appendTo(span_1);
                        if (buttonStyle)
                            span_1.addClass('icon');
                    }
                    var text = a.text || a.label;
                    if (a.icon && options.iconOnly === true)
                        text = undefined;
                    if (text) {
                        if (buttonStyle)
                            span_1.append(text);
                        else
                            $('<a>').html(a.label || a.text).appendTo(span_1);
                    }
                    if (buttonStyle) {
                        api(span_1, function () {
                            var it = item;
                            if (typeof it === 'function')
                                it = it();
                            return _context.doAction(a, it, scope, span_1);
                        });
                    }
                    else
                        span_1.click(function (e) {
                            e.stopPropagation();
                            var it = item;
                            if (typeof it === 'function')
                                it = it();
                            el.addClass('active');
                            span_1.find('.icon').addClass('loading');
                            var rml = function () {
                                span_1.find('.icon').removeClass('loading');
                                el.removeClass('active');
                            };
                            var ret = _context.doAction(a, it, scope, span_1);
                            if (_util.isPromise(ret))
                                ret.then(rml, rml);
                            else
                                rml();
                            return false;
                        });
                }
            });
        }, actionProvider, [item], scope);
    }
    _sui.renderActionsTo = renderActionsTo;
    function destroyComponents(el) {
        function destroy(i) {
            try {
                if (i.__componentInstance) {
                    i.__componentInstance.destroy();
                }
                else if (i.children && i.children.length)
                    for (var j = 0; j < i.children.length; j++)
                        destroy(i.children[j]);
            }
            catch (e) { }
        }
        destroy(el);
    }
    var __mouseevtTimer;
    function __checkboxMouseDown(e) {
        clearTimeout(__mouseevtTimer);
        this.__current_mouse_event_ = e;
    }
    function __checkboxMouseUp(e) {
        var self = this;
        __mouseevtTimer = setTimeout(function () {
            delete self.__current_mouse_event_;
        }, 200);
    }
    function bindMouseEvent(jqObj) {
        jqObj.mousedown(__checkboxMouseDown);
        jqObj.mouseup(__checkboxMouseUp);
    }
    function getCurrentMouseEvent(t) {
        if (_util.isjquery(t))
            t = t[0];
        return t.__current_mouse_event_;
    }
    if (window['_context']) {
        var conf = window['_context'].clientConf;
        if (conf && conf._suiDefaults)
            Object.assign(_sui, conf._suiDefaults);
    }
    var Component = (function () {
        function Component(parentEl, options) {
            if (options === void 0) { options = {}; }
            this.options = options;
            this.autoParentEl = true;
            this.isRendered = false;
            this.events = $({});
            if (parentEl && !_util.isjquery(parentEl))
                parentEl = $(parentEl);
            this.parentEl = parentEl;
            if (options && options.defaultOptions) {
                Object.assign(this, options.defaultOptions);
            }
            Object.assign(this, options);
            this.options = this.options || {};
        }
        Component.prototype.getParentEl = function () {
            return this.parentEl;
        };
        Component.prototype.getEl = function () {
            return this.templateEl;
        };
        Component.prototype.prepare = function () {
            return null;
        };
        Component.prototype.render = function () {
            var _this = this;
            if (this.isRendered)
                return this;
            if (this.options.defaultOptions) {
                Object.assign(this, this.options.defaultOptions);
            }
            Object.assign(this, this.options);
            if (!this.parentEl && this.autoParentEl) {
                this.parentEl = $('<div>');
            }
            if (this.parentEl)
                this.parentEl = $(this.parentEl);
            var el = this.parentEl;
            var pre = this.prepare();
            if (pre instanceof Promise || _util.isPromise(pre))
                pre.then(function (pre) { return _this.init(el, pre); });
            else
                this.init(el, pre);
            return this;
        };
        Component.prototype.rendered = function () {
            var _this = this;
            if (this.isRendered)
                return Promise.resolve(this);
            return new Promise(function (resolve) {
                _this.on(_sui.EVENT_COMPONENT_RENDERED, function () { return resolve(_this); });
            });
        };
        Component.prototype.init = function (el, prepared) {
            var _this = this;
            if (this.template) {
                this.templateEl = $(this.template);
                if (el)
                    this.templateEl.appendTo(el);
                if (this.templateCss)
                    this.templateEl.css(this.templateCss);
                if (this.templateClass)
                    this.templateEl.addClass(this.templateClass);
                if (this.options.style) {
                    var style = this.options.style;
                    if (typeof style === 'string')
                        this.templateEl.attr('style', style);
                    else
                        this.templateEl.css(style);
                }
                if (this.options.class)
                    this.templateEl.addClass(this.options.class);
                this.templateEl[0].__componentInstance = this;
            }
            var rd = this.doRender(this.templateEl, prepared);
            if (rd && rd.then)
                rd.then(function () {
                    _this.isRendered = true;
                    _this.trigger(_sui.EVENT_COMPONENT_RENDERED);
                });
            else {
                this.isRendered = true;
                this.trigger(_sui.EVENT_COMPONENT_RENDERED);
            }
            if (this.data)
                this.setData(this.data);
        };
        Component.prototype.doRender = function (el, prepare) {
        };
        Component.prototype.setData = function (data) {
            var _this = this;
            var filter = this.dataFilter;
            _util.dataCallback(function (d) {
                if (d && filter) {
                    if (Array.isArray(d))
                        d = d.filter(filter);
                    else {
                        d = [d].filter(filter)[0];
                    }
                }
                _this.doSetData(d);
            }, data);
        };
        Component.prototype.doSetData = function (data) {
            this.data = data;
            this.trigger('setdata', { data: data });
        };
        Component.prototype.on = function (eventType, fn, once) {
            if (once === void 0) { once = false; }
            once ? this.events.one(eventType, fn) : this.events.on(eventType, fn);
            return this;
        };
        Component.prototype.off = function (eventType, fn) {
            this.events.off(eventType, fn);
            return this;
        };
        Component.prototype.trigger = function (eventType, event) {
            if (event === void 0) { event = null; }
            this.events.trigger(eventType, event);
            return this;
        };
        Component.prototype.redraw = function (newData) {
            if (newData === void 0) { newData = null; }
            newData = newData || this.data;
            this.setData(newData);
        };
        Component.prototype.destroy = function () {
            this.events.off();
        };
        return Component;
    }());
    _sui.Component = Component;
    var SuiComponent = (function (_super) {
        __extends(SuiComponent, _super);
        function SuiComponent(suiMethod, parentEl, options) {
            if (options === void 0) { options = {}; }
            var _this = _super.call(this, parentEl, options) || this;
            _this.suiMethod = suiMethod;
            _this.options = options;
            _this.defaultSuiOptions = {};
            return _this;
        }
        SuiComponent.prototype.doRender = function (el, prepare) {
            if (this.attached)
                this.templateEl.addClass('attached').addClass(this.attached);
            if (this.size)
                this.templateEl.addClass(this.size);
            _super.prototype.doRender.call(this, el, prepare);
            this.getSuiEl()[this.suiMethod](Object.assign(this.defaultSuiOptions, this.suiOptions));
        };
        SuiComponent.prototype.getSuiEl = function () {
            return this.templateEl;
        };
        SuiComponent.prototype.behavior = function (name) {
            var _a;
            var args = [];
            for (var _i = 1; _i < arguments.length; _i++) {
                args[_i - 1] = arguments[_i];
            }
            return (_a = this.getSuiEl())[this.suiMethod].apply(_a, __spreadArray([name], args, false));
        };
        return SuiComponent;
    }(Component));
    _sui.SuiComponent = SuiComponent;
    var Button = (function (_super) {
        __extends(Button, _super);
        function Button() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.template = '<button class="ui button">';
            _this.autoParentEl = false;
            return _this;
        }
        Button.prototype.doRender = function (el) {
            if (this.icon) {
                el.addClass('icon');
                $('<i class="icon">').addClass(this.icon).prependTo(el);
            }
            el.html(this.text);
            el.addClass(this.btnClass);
            if (this.api)
                _sui.api(el, this.api);
        };
        return Button;
    }(Component));
    _sui.Button = Button;
    function button(el, options) {
        return new Button(el, options).render();
    }
    _sui.button = button;
    var Item = (function (_super) {
        __extends(Item, _super);
        function Item() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.template = "\n        <div class=\"item _suiitem\">\n            <div class=\"image\" style=\"display:none;max-height:200px;overflow:hidden;\"><img style=\"max-width:380px;\"></div>\n            <div class=\"content\" style=\"min-height:48px;\">\n                <div id=\"_title\" style=\"display:inline-block;\">\n                  <a class=\"ui header small\" style=\"display:inline-block;\"></a>\n                  <i class=\"icon copy outline\" id=\"_copybtn\"></i>\n                </div>\n                <div class=\"meta\"></div>\n                <div class=\"description\"><p></p></div>\n                <div class=\"extra\" style=\"display:flex;flex-direction:row;\"><span class=\"_labels ui tag labels compact mini\" style=\"flex-grow:1;\"></span><span class=\"_btns\" style=\"position:-webkit-sticky;position:sticky;right: 24px\"></span>\n                <div class=\"ui text menu _moreBtns\" style=\"margin: 0px\"><div class=\"ui dropdown item \"><i class=\"dropdown icon\">\u66F4\u591A</i>\n                  <div class=\"menu _menuItems\">\n                  </div>\n                </div></div></div>\n            </div>\n        </div>\n        ";
            return _this;
        }
        Item.prototype.computeValue = function (field, data, callback) {
            if (typeof field === 'string') {
                var rv = void 0;
                rv = data[field];
                if (rv === undefined && field.indexOf('.') != -1) {
                    try {
                        rv = eval('data.' + field);
                    }
                    catch (e) { }
                }
                field = rv;
            }
            _util.dataCallback(callback, field, [data]);
        };
        Item.prototype.doSetData = function (itemData) {
            var _this = this;
            var el = this.templateEl;
            this.computeValue(this.imageField, itemData, function (value) {
                if (value !== false) {
                    el.find('>.image').show();
                    var imgDiv_1 = el.find('.image'), img = imgDiv_1.find('img'), imgAction_1 = _this.imageAction;
                    imgDiv_1.show();
                    if (typeof value === 'string')
                        img.attr('src', value);
                    else if (value && typeof value === 'object' && !_util.isjquery(value)) {
                        img.attr('src', value.src);
                        imgDiv_1.css(value);
                    }
                    else if (value) {
                        img.remove();
                        imgDiv_1.html(value);
                    }
                    if (imgAction_1)
                        imgDiv_1.css('cursor', 'pointer').click(function (e) {
                            _this.doActionFn(imgAction_1, itemData, _this, imgDiv_1);
                        });
                }
            });
            this.computeValue(this.titleField, itemData, function (value) {
                var headerEl = el.find('#_title');
                if (value === false)
                    headerEl.remove();
                else {
                    var copybtn = headerEl.find('#_copybtn');
                    if (copybtn.length) {
                        copybtn.click(function () {
                            _util.copy(value, '复制成功');
                        });
                    }
                    headerEl = headerEl.find('.header');
                    headerEl.safeHtml(value || _this.emptyHeaderText);
                    if (_this.headerAction)
                        headerEl.click(function (e) { return _this.doActionFn(_this.headerAction, itemData, _this, headerEl); });
                }
            });
            this.computeValue(this.descriptionField, itemData, function (value) {
                el.find('.content .description p').safeHtml(value);
            });
            var metaEl = el.find('.content .meta');
            this.computeValue(this.metasField, itemData, function (ms) {
                if (ms === false) {
                    metaEl.remove();
                    return;
                }
                ms = ms || [];
                if (!Array.isArray(ms))
                    ms = [ms];
                ms.forEach(function (meta) {
                    if (_util.isPromise(meta)) {
                        meta.then(function (meta) {
                            meta = _util.xssUtil.filterXSS(meta);
                            $('<span class="cinema">').html(meta).appendTo(metaEl);
                        });
                    }
                    else {
                        meta = _util.xssUtil.filterXSS(meta);
                        $('<span class="cinema">').html(meta).appendTo(metaEl);
                    }
                });
            });
            var extraEl = el.find('.content .extra');
            this.computeValue(this.labelsField, itemData, function (labels) {
                labels = labels || [];
                if (!Array.isArray(labels))
                    labels = [labels];
                labels.forEach(function (label) { return $('<div class="ui label ">').safeHtml(label).appendTo(extraEl.find('._labels')); });
            });
            var isMapPopup = this['source'] === 'map';
            if (!isMapPopup) {
                if (this.actionsField.length > 0 && this.moreActionsField && this.moreActionsField.length > 0) {
                    var actionsSize_1 = this.actionsField.length;
                    this.moreActionsField.reverse().map(function (item) {
                        _this.actionsField.splice(actionsSize_1 - 1, 0, item);
                    });
                }
            }
            this.computeValue(this.actionsField, itemData, function (actions) {
                actions = actions || [];
                actions = actions.filter(function (a) { return hasPermission(a); });
                var btns = extraEl.find('._btns');
                actions.forEach(function (action) {
                    var btn = $('<div class="ui very mini compact icon button" style="margin-top: 5px">').appendTo(btns).attr('title', action.label).text(action.label);
                    if (action.icon) {
                        var iconEl = $('<i class="icon">').addClass(action.icon).prependTo(btn);
                        if (iconEl.hasClass('fa'))
                            iconEl.removeClass('icon');
                    }
                    if (action._ui_class)
                        btn.addClass(action._ui_class);
                    _sui.api(btn, function () {
                        return _this.doActionFn(action, itemData, _this, btn);
                    });
                });
            });
            if (!isMapPopup) {
                extraEl.find('._moreBtns').remove();
            }
            else {
                this.computeValue(this.moreActionsField, itemData, function (actions) {
                    actions = actions || [];
                    actions = actions.filter(function (a) { return hasPermission(a); });
                    var moreBtns = extraEl.find('._menuItems');
                    if (actions.length != 0) {
                        actions.forEach(function (action) {
                            var moreBtn = $("<div class=\"item\"></div>").text(action.label).appendTo(moreBtns);
                            _sui.api(moreBtn, function () {
                                _this.doActionFn(action, itemData, _this, moreBtn);
                            });
                        });
                        extraEl.find('.ui.dropdown').dropdown();
                    }
                    else {
                        extraEl.find('._moreBtns').remove();
                    }
                });
            }
        };
        return Item;
    }(Component));
    _sui.Item = Item;
    var Menu = (function (_super) {
        __extends(Menu, _super);
        function Menu() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.template = "<div class=\"ui vertical buttons _btns compact mini\"></div>";
            return _this;
        }
        Menu.prototype.computeValue = function (field, data, callback) {
            if (typeof field === 'string')
                field = data[field];
            _util.dataCallback(callback, field, [data]);
        };
        Menu.prototype.doSetData = function (itemData) {
            var _this = this;
            var btns = this.templateEl;
            this.computeValue(this.actionsField, itemData, function (actions) {
                actions = actions || [];
                actions = actions.filter(function (a) { return hasPermission(a); });
                actions.forEach(function (action) {
                    var btn = $("<button class=\"ui very mini compact button\" style=\"background: white;text-align: left;\">").appendTo(btns).attr('title', action.label).text(action.label);
                    btn.hover(function () {
                        $(this).css("background", "#e7e7ea");
                    }, function () {
                        $(this).css("background", "white");
                    });
                    _sui.api(btn, function () {
                        _this.doActionFn(action, itemData, _this, btn);
                    });
                });
            });
        };
        return Menu;
    }(Component));
    _sui.Menu = Menu;
    var Table = (function (_super) {
        __extends(Table, _super);
        function Table() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.template = "\n            <div style=\"display:flex;flex-flow:column;height:100%;\">\n                <div class=\"_gridMenu\"></div>\n                <div style=\"flex:auto;display: flex;flex-direction: column;overflow:auto;position:relative;\">\n                    <div style=\"flex: auto;overflow:auto;padding:0 2px;\">\n                        <table class=\"_maintable ui unstackable selectable striped celled very compact table tbody\" style=\"border-radius: 0px;\">\n                            <thead></thead>\n                            <tbody></tbody>\n                        </table>\n                    </div>\n                </div>\n                <table class=\"_foottable ui unstackable selectable striped celled very compact table tfoot\" style=\"margin-top: 0px;position: -webkit-sticky;position: sticky;bottom: 0;border-top-left-radius: 0px;border-top-right-radius: 0px;border-top: 0px;\">\n                    <tfoot></tfoot>\n                </table>\n            </div>\n        ";
            _this.columns = [];
            _this.showHead = true;
            _this.showRowNum = false;
            _this.showToolbar = true;
            _this.rowCheckWidth = '48px';
            _this.initChecked = false;
            _this.selectCheck = true;
            _this.checkRowClass = 'sui-row-checked';
            _this.cellSelectable = false;
            _this.cellSelectClass = 'sui-cell-selected';
            _this.toolbarAutoHide = false;
            _this.showTreeTable = false;
            _this.rowKey = 'id';
            _this.checkedData = [];
            _this.allowedEdit = false;
            _this.lockEdit = false;
            _this.showSum = false;
            _this.fixNumber = true;
            _this.sortable = true;
            _this.filterable = true;
            _this.userOrderBys = [];
            _this.appendColumnActionsButtonStyle = true;
            _this.appendColumnActionsStyle = { width: '120px' };
            _this.cellEditDirtyClass = 'cell-edit-dirty';
            return _this;
        }
        Table.prototype.doRender = function (el, prepare) {
            var _this = this;
            if (this.columnFilter)
                this.columns = this.columns.filter(this.columnFilter);
            var filterCols = this.columns.filter(function (col) {
                if (col.visible !== undefined) {
                    if (col.visible === false)
                        return false;
                    if (typeof col.visible === 'function')
                        return col.visible(col, _this);
                }
                return true;
            });
            if (filterCols.length != this.columns.length) {
                this.columns = filterCols;
            }
            if (this.defaultColumnProperty) {
                if (typeof this.defaultColumnProperty === 'string')
                    this.defaultColumnProperty = _util.parsekv(this.defaultColumnProperty);
                this.columns.forEach(function (c) {
                    Object.assign(c, _this.defaultColumnProperty);
                });
            }
            var tableFieldShowMaxLength = _context.getSetting('tableFieldShowMaxLength');
            if (tableFieldShowMaxLength) {
                this.columns.forEach(function (c) {
                    if (!c.showMaxLength && c.showMaxLength !== false) {
                        Object.assign(c, { showMaxLength: tableFieldShowMaxLength });
                    }
                });
            }
            if (this.appendColumnActions) {
                var cacts_1 = this.appendColumnActions;
                var appendCol = Object.assign({
                    style: this.appendColumnActionsStyle, exportable: false,
                    getLabel: function (item) {
                        if (_util.isjquery(cacts_1))
                            return cacts_1;
                        var div = $('<div>');
                        renderActionsTo(cacts_1, div, item, _this, {
                            buttonStyle: _this.appendColumnActionsButtonStyle
                        });
                        return div;
                    }
                }, this.appendColumnActionsOption);
                this.columns.push(appendCol);
            }
            if (this.rowCheckable === undefined)
                this.rowCheckable = this.showRowNum ? false : true;
            var tableEl = el.find('table._maintable'), toolbarEl = el.find('> ._gridMenu');
            tableEl.css('table-layout', 'fixed');
            if (this.tableStyle)
                setStyle(tableEl, this.tableStyle);
            if (this.tableClass)
                tableEl.addClass(this.tableClass);
            if (this.toolbar) {
                this.toolbar = $(this.toolbar).prependTo(toolbarEl);
                if (!this.showToolbar)
                    this.toolbar.hide();
                else if (this.toolbarAutoHide) {
                    this.toolbar.hide();
                    el.hover(function () {
                        _this.toolbar.show();
                    }, function () {
                        _this.toolbar.hide();
                    });
                }
            }
            var head = $('<tr>').appendTo(el.find('thead'));
            this.tbody = el.find('.tbody').find('tbody');
            if (this.showHead) {
                if (this.rowHeader || this.showRowNum || this.rowCheckable) {
                    var th_1 = $('<th class="colhead" style="text-align:center;z-index:1">').appendTo(head);
                    if (this.showRowNum && this.rowCheckable)
                        th_1.css('text-align', 'left');
                    if (this.columns.find(function (c) { return c.frozen; }))
                        th_1.addClass('frozen');
                    if (this.rowCheckable || this.showRowNum)
                        th_1.css('width', this.rowCheckWidth);
                    if (this.rowHeader) {
                        _util.dataCallback(function (v) {
                            v = $(v);
                            v.prependTo(th_1);
                        }, this.rowHeader, [null, -1]);
                    }
                    else if (this.rowCheckable && this.rowCheckable !== 'single') {
                        if (!this.checkHeaderEl) {
                            var ca = this.checkHeaderEl = $("<div class=\"ui fitted checkbox\"><input type=\"checkbox\"".concat(this.initChecked ? ' checked ' : '', "><label></label></div>"));
                            ca.checkbox({
                                onChecked: function (e) {
                                    _this.checkAll();
                                },
                                onUnchecked: function (e) {
                                    _this.checkAll(false);
                                }
                            });
                        }
                        th_1.html(this.checkHeaderEl);
                    }
                    if (this.rowHeaderStyle)
                        setStyle(th_1, this.rowHeaderStyle);
                }
                if (this.showTreeTable) {
                    $('<th style="text-align:center;width: 48px;">#</th>').appendTo(head);
                }
                this.columns.forEach(function (col, idx) {
                    _this.getColumnLabel(col, idx).appendTo(head);
                });
                this.setupResize(head);
                this.setupColumnDrag(head);
            }
            if (this.showSum) {
                this.sumTr = $('<tr>').appendTo(el.find('tfoot'));
            }
            el.find('.tbody').find('thead').html(el.find('.thead').find('thead').html());
            if (this.footer) {
                var len = this.columns.length;
                if (this.showRowNum || this.rowCheckable)
                    len++;
                var tr = $('<tr>').appendTo(el.find('tfoot'));
                var th = $('<th>').attr('colspan', len).appendTo(tr).css({ padding: '2px 8px 0 8px', background: 'transparent' });
                this.footer = $(this.footer).appendTo(th);
            }
            if (this.script) {
                var grid = this;
                eval(this.script);
            }
            if (this.columns.find(function (col) { return col.frozen; })) {
                var timer_1;
                tableEl.parent().scroll(function () {
                    if (!tableEl.parent()[0].scrollLeft)
                        return;
                    tableEl.find('tr>th.frozen,tr>td.frozen').addClass('frozen-scrolling').each(function () {
                        if (this.style.left === undefined || this.style.left === '')
                            this.style.left = $(this).position().left + 'px';
                    });
                    clearTimeout(timer_1);
                    timer_1 = setTimeout(function () {
                        if (!tableEl.parent()[0].scrollLeft)
                            tableEl.find('tr>th.frozen,tr>td.frozen').removeClass('frozen-scrolling').each(function () {
                                this.style.left = '';
                            });
                    }, 500);
                });
            }
        };
        Table.prototype.getColumnLabel = function (col, colIndex) {
            var _this = this;
            var th = $('<th>').html(col.title || col.label).data('__column', col);
            if (col.frozen)
                th.addClass('frozen');
            if (col.style)
                setStyle(th, col.style);
            if (col.width)
                th.css('width', col.width);
            setStyle(th, col.style);
            if (col.class)
                th.addClass(col.class);
            if (col.exportable === false)
                th.addClass('_col_no_export');
            if (this.filterable && col.filterable !== false) {
                var self_1 = this;
                var filterIcon_1 = $('<i class="col icon filter disabled">').appendTo(th).click(function () {
                    _sui.prompt({ title: '请输入筛选关键字', allowEmpty: true, value: col.__filterText }, function (filterText) {
                        col.__filterText = filterText;
                        filterIcon_1.toggleClass('disabled', !filterText);
                        self_1.displayFilter = !filterText ? undefined : function (row, idx) {
                            filterText = filterText.toLowerCase();
                            var label = self_1.getLabel(row, col, idx);
                            if (label === null || label === undefined)
                                return false;
                            if (typeof label === 'string')
                                return label.toLowerCase().indexOf(filterText) !== -1;
                            if (_util.isjquery(label))
                                label = label[0];
                            if (label.innerHTML)
                                label = label.innerHTML;
                            label = label + '';
                            return label.toLowerCase().indexOf(filterText) !== -1;
                        };
                        self_1.redraw();
                    });
                });
            }
            if (this.sortable && col.sortable !== false) {
                var sorts_1 = $('<i class="col icon sort disabled">').appendTo(th);
                var sortTimeout_1;
                sorts_1.click(function (e) {
                    sorts_1.removeClass('disabled up down');
                    var sortFieldName = col.sortField || col.name;
                    var idx = _this.userOrderBys.findIndex(function (f) { return f === sortFieldName || f.startsWith(sortFieldName + ' '); });
                    var dir;
                    if (idx === -1) {
                        _this.userOrderBys.push(sortFieldName);
                        idx = _this.userOrderBys.length - 1;
                    }
                    else {
                        var fo = _this.userOrderBys[idx].split(' ');
                        if (fo.length > 1)
                            dir = fo[1].toLowerCase();
                    }
                    dir = _this.computeNextSortDir(dir, col);
                    if (!dir)
                        _this.userOrderBys.splice(idx, 1);
                    else
                        _this.userOrderBys[idx] = sortFieldName + ' ' + dir;
                    if (dir === 'asc')
                        sorts_1.addClass('up');
                    else if (dir === 'desc')
                        sorts_1.addClass('down');
                    else
                        sorts_1.addClass('disabled');
                    clearTimeout(sortTimeout_1);
                    sortTimeout_1 = setTimeout(function () {
                        _this.doSortRows(col, dir, th);
                    }, 500);
                });
            }
            return th;
        };
        Table.prototype.setupResize = function (headTr) {
            var lastPageX = undefined, self = this;
            function mousemove(e) {
                if (lastPageX === undefined)
                    return;
                var th = headTr.find('th.resizing');
                if (th.length) {
                    var lx = lastPageX, nx = lastPageX = e.pageX, r = nx - lx;
                    if (r) {
                        var next = th.next(), tw = th.innerWidth(), nw = next.innerWidth();
                        th.innerWidth(tw + r);
                        next.innerWidth(nw - r);
                    }
                }
                e.stopPropagation();
            }
            $(document).mouseup(function (e) {
                headTr.find("th.resizing").removeClass("resizing");
                lastPageX = undefined;
                e.stopPropagation();
                $(document).off('mousemove', mousemove);
            });
            headTr.find('th').each(function () {
                $(this).is(":not(:last-child)") && $(this).append("<div class='resizer'></div>");
            });
            headTr.find('.resizer').on('mousedown', function (e) {
                headTr.find("th").removeClass("resizing");
                $(this).closest("th").addClass("resizing");
                lastPageX = e.pageX;
                e.stopPropagation();
                e.preventDefault();
                $(document).on('mousemove', mousemove);
                return false;
            });
            headTr.find('.resizer').dblclick(function () {
                var theTh = $(this).closest("th"), idx = theTh.index();
                var maxw = 0;
                self.templateEl.find('.suitablerow').each(function () {
                    var tr = $(this), tds = tr.children('td'), td = tds.eq(idx);
                    var w = _util.computeDisplayWidth(td[0].innerHTML, td);
                    var padding = td.outerWidth() - td.width();
                    if (w) {
                        w += padding;
                        if (w > maxw)
                            maxw = w;
                    }
                });
                if (maxw)
                    theTh.innerWidth(maxw);
            });
        };
        Table.prototype.setupColumnDrag = function (headTr) {
            var self = this, draggingTh;
            headTr.find('th').each(function () {
                var el = $(this);
                if (el.data('__column') === undefined)
                    return;
                el.attr('draggable', true);
                this.ondragstart = function () {
                    draggingTh = this;
                };
                this.ondragover = function (e) {
                    e.preventDefault();
                    if ($(this).data('__column') === undefined)
                        return false;
                };
                this.ondrop = function (e) {
                    var target = $(this), targetCol = target.data('__column');
                    if (targetCol === undefined)
                        return false;
                    if (draggingTh !== this) {
                        var dragging = $(draggingTh);
                        var targetIndex_1 = target.index(), draggingIndex_1 = dragging.index();
                        targetIndex_1 > draggingIndex_1 ? target.after(dragging) : target.before(dragging);
                        self.templateEl.find('.suitablerow').each(function () {
                            var tr = $(this), tds = tr.children('td'), from = tds.eq(draggingIndex_1), to = tds.eq(targetIndex_1);
                            targetIndex_1 > draggingIndex_1 ? to.after(from) : to.before(from);
                        });
                        var draggingCol = dragging.data('__column'), draggingColIndex = self.columns.indexOf(draggingCol), targetColIndex = self.columns.indexOf(targetCol);
                        self.columns.splice(draggingColIndex, 1);
                        self.columns.splice(targetColIndex, 0, draggingCol);
                    }
                    draggingTh = undefined;
                };
            });
        };
        Table.prototype.computeNextSortDir = function (currentDir, col) {
            if (currentDir === 'asc')
                return 'desc';
            return 'asc';
        };
        Table.prototype.doSortRows = function (col, dir, th) {
            var _this = this;
            var i = th.index();
            this.data.sort(function (a, b) {
                var tr = _this.getRowEl(a);
                a = tr.children('td').eq(i).text();
                tr = _this.getRowEl(b);
                b = tr.children('td').eq(i).text();
                if (col.type === 'number') {
                    try {
                        a = parseFloat(a);
                        b = parseFloat(b);
                    }
                    catch (e) { }
                }
                if (a == b)
                    return 0;
                if (dir === 'desc')
                    return a > b ? -1 : 1;
                return a < b ? -1 : 1;
            });
            this.redraw();
        };
        Table.prototype.doSetData = function (rows, append) {
            var _this = this;
            if (append === void 0) { append = false; }
            if (!append) {
                this.tbody.find('tr').remove();
                this.checkedData = [];
            }
            rows = rows || [];
            this.data = this.data || [];
            var start = 0;
            if (append)
                start = this.data.length;
            this.data = append ? this.data.concat(rows) : rows;
            var rowsToAdd = rows;
            if (this.displayFilter) {
                rowsToAdd = rowsToAdd.filter(this.displayFilter);
            }
            rowsToAdd.forEach(function (row, index) {
                _this.addRow(row, start + index, false);
            });
            if (this.checkHeaderEl && this.checkHeaderEl.checkbox)
                this.checkHeaderEl.checkbox(this.initChecked ? 'set checked' : 'set unchecked');
            if (this.initChecked)
                this.checkedData = this.checkedData.concat(rowsToAdd);
            this.updateSumRow();
            this.autoSetThWidth();
            this.trigger('setdata', { data: rows });
        };
        Table.prototype.addRow = function (row, index, newData) {
            var _this = this;
            if (index === void 0) { index = -1; }
            if (newData === void 0) { newData = true; }
            this.data = this.data || [];
            var rowNum = index;
            if (rowNum === -1)
                rowNum = this.data.length;
            var tr = $('<tr class="suitablerow">');
            tr.data('userdata', row);
            if (this.rowEvents) {
                tr.on(this.rowEvents);
            }
            if (newData)
                rowNum === 0 ? tr.prependTo(this.tbody) : this.tbody.find('tr').eq(rowNum - 1).after(tr);
            else
                tr.appendTo(this.tbody);
            tr.click(function (e) { return _this.rowClick(row, tr, rowNum, e); });
            this.redrawRow(row, tr, rowNum);
            if (newData) {
                this.data = this.data || [];
                this.data.splice(rowNum, 0, row);
                this.updateSumRow();
            }
            this.updateRowNum();
        };
        Table.prototype.redraw = function (row) {
            var _this = this;
            if (row === void 0) { row = null; }
            this.tbody.find('tr._itemDetail').remove();
            if (Array.isArray(row))
                row.forEach(function (r) { return _this.redrawRow(r); });
            else if (row)
                this.redrawRow(row);
            else {
                this.doSetData(this.data);
            }
        };
        Table.prototype.addRowCheck = function (row, tr, rowNum) {
            var _this = this;
            if (tr === void 0) { tr = null; }
            if (rowNum === void 0) { rowNum = -1; }
            var h = $("<td class=\"colhead\" style=\"text-align:center;font-size:smaller;width:".concat(this.rowCheckWidth, ";\">")).appendTo(tr);
            if (this.showRowNum && this.rowCheckable)
                h.css('text-align', 'left');
            if (this.columns.find(function (c) { return c.frozen; }))
                h.addClass('frozen');
            if (this.rowHeaderStyle)
                setStyle(h, this.rowHeaderStyle);
            if (this.rowCheckable) {
                var rowNumText = this.showRowNum ? rowNum + 1 : '';
                var checked = this.initChecked;
                if (!checked && this.checkedData.indexOf(row) !== -1)
                    checked = true;
                var b_1 = $("<div class=\"ui checkbox _rowchecker\"><input type=\"checkbox\" name=\"\" ".concat(checked ? ' checked ' : '', "><label>").concat(rowNumText, "</label></div>")).appendTo(h);
                b_1.click(function (e) {
                    e.stopPropagation();
                    var batchAppending = e.shiftKey && _this.lastChecked;
                    if (batchAppending) {
                        e.preventDefault();
                        var start = _this.lastChecked.index, now = rowNum;
                        if (now > start)
                            now -= 1;
                        else if (now < start)
                            now += 1;
                        _this.doBatchCheck(_this.lastChecked.index, now);
                        return false;
                    }
                });
                if (!this.showRowNum)
                    b_1.addClass('fitted');
                b_1.checkbox({
                    onChecked: function (e) {
                        if (_this.rowCheckable === 'single')
                            _this.checkAll(false, b_1);
                        _this.checkedData.push(row);
                        _this.trigger('checkchange', { checked: true, row: row });
                        _this.lastChecked = { data: row, index: rowNum };
                        if (_this.checkRowColor)
                            tr.css('background-color', _this.checkRowColor);
                        if (_this.checkRowClass)
                            tr.addClass(_this.checkRowClass);
                    },
                    onUnchecked: function (e) {
                        _this.checkedData.splice(_this.checkedData.indexOf(row), 1);
                        _this.trigger('checkchange', { checked: false, row: row });
                        if (_this.checkRowColor)
                            tr.css('background-color', '');
                        if (_this.checkRowClass)
                            tr.removeClass(_this.checkRowClass);
                    }
                });
            }
            else {
                h.text(rowNum + 1);
                this.updateRowNum(rowNum, tr);
            }
        };
        Table.prototype.redrawRow = function (row, tr, rowNum) {
            var _this = this;
            if (tr === void 0) { tr = null; }
            if (rowNum === void 0) { rowNum = -1; }
            if (!row)
                return;
            tr = tr || this.getRowEl(row);
            if (!tr || !tr.length) {
                this.addRow(row, -1, false);
                return;
            }
            tr.empty();
            tr.removeAttr('style');
            if (rowNum === -1)
                rowNum = this.tbody.find('tr.suitablerow').index(tr);
            if (row._rowStyle)
                setStyle(tr, row._rowStyle);
            if (this.rowHeader) {
                _util.dataCallback(function (v) {
                    v = $(v);
                    if (!v.is('td'))
                        v = $('<td>').append(v);
                    v.prependTo(tr);
                }, this.rowHeader, [row, rowNum]);
            }
            else if (this.showRowNum || this.rowCheckable) {
                this.addRowCheck(row, tr, rowNum);
            }
            tr.dblclick(function (e) {
                _this.trigger('rowDblclick', { data: row });
            });
            if (this.showTreeTable) {
                var content = $('<i class="caret right icon"></i>');
                $('<td style="text-align:center;">').safeHtml(content).appendTo(tr);
            }
            this.columns.forEach(function (col, index) {
                var label = null;
                var nonText = false;
                try {
                    col.index = rowNum;
                    label = _this.getLabel(row, col, rowNum);
                    if (label != null && typeof label !== 'string') {
                        nonText = true;
                    }
                }
                catch (e) {
                    console.log(e.message);
                }
                var td = $('<td>');
                if (col.showMaxLength) {
                    if (label === undefined || label === null) {
                        label = '';
                    }
                    else if (!nonText) {
                        label = label + '';
                    }
                    var oldLabel_1 = label;
                    if (label.length > col.showMaxLength && !nonText) {
                        td = $('<td data-tooltip="' + oldLabel_1 + '" data-position="bottom center" data-inverted="">');
                        label = label.substring(0, col.showMaxLength) + '...';
                        td.click(function (e) {
                            if (col.maxLengthHandle && col.maxLengthHandle == 'alert') {
                                _sui.alert(oldLabel_1, '<i class="icon info">完整内容</i>');
                            }
                            else {
                                _util.copy(oldLabel_1, '复制"' + (col.label == null ? '' : col.label) + '"成功');
                            }
                        });
                    }
                    var widthLabel = '';
                    if (!nonText) {
                        widthLabel += label;
                    }
                    var maxTextLength = col.maxTextLength;
                    if (!maxTextLength) {
                        maxTextLength = 1;
                    }
                    if (widthLabel.length > maxTextLength) {
                        maxTextLength = widthLabel.length;
                        col.maxText = widthLabel;
                    }
                    col.maxTextLength = maxTextLength;
                    col.nonText = nonText;
                }
                if (col.customRender) {
                    try {
                        label = _this.customRender(col, row, rowNum, td, tr);
                        var maxWidth = col.maxWidth;
                        if (!maxWidth || label.width() > maxWidth) {
                            col.maxWidth = label.width();
                        }
                    }
                    catch (e) {
                    }
                }
                td.safeHtml(label).appendTo(tr);
                if (col.exportable === false)
                    td.addClass('_col_no_export');
                var excelCellType = col.excelCellType;
                if (!excelCellType && (!col.type || col.type === 'text' || col.type === 'id'))
                    excelCellType = 's';
                if (excelCellType == 'text')
                    excelCellType = 's';
                else if (excelCellType == 'bool' || excelCellType == 'boolean')
                    excelCellType = 'b';
                else if (excelCellType == 'number')
                    excelCellType = 'n';
                else if (excelCellType == 'date' || excelCellType == 'datetime')
                    excelCellType = 'd';
                if (excelCellType)
                    td.attr('t', excelCellType);
                if (col.frozen)
                    td.addClass('frozen');
                if (col.style)
                    setStyle(td, col.style);
                if (col.class)
                    td.addClass(col.class);
                if (col.width)
                    td.css('width', col.width);
                var _style = row["_style"];
                if (_style) {
                    var name_1 = col.name;
                    if (_style[name_1]) {
                        td.css(_style[name_1]);
                    }
                }
                var allowEdit = col.allowEdit;
                if (allowEdit === undefined)
                    allowEdit = col.allowedEdit;
                if (allowEdit === undefined)
                    allowEdit = _this.options.allowEdit;
                if (allowEdit === undefined)
                    allowEdit = _this.allowedEdit;
                if (allowEdit) {
                    td._originText = td.text();
                    _this.getComponentForEdit(row, col, td);
                }
                if (_this.cellSelectable && col.cellSelectable !== false) {
                    var sc_1 = _this.cellSelectClass;
                    var celldata = { row: row, col: col, colIndex: index };
                    td.data('cell-data', celldata);
                    td.click(function () {
                        td.toggleClass(sc_1);
                    });
                }
            });
            this.updateSumRow();
        };
        Table.prototype.customRender = function (col, row, rowIndex, tdEl, trEl) {
            if (rowIndex === void 0) { rowIndex = -1; }
            if (tdEl === void 0) { tdEl = null; }
            if (trEl === void 0) { trEl = null; }
            var customRender = col.customRender;
            if (typeof customRender === 'string') {
                customRender = window[customRender];
            }
            if (customRender.length == 0) {
                return customRender();
            }
            else if (customRender.length == 1) {
                return customRender(row);
            }
            else if (customRender.length == 2) {
                return customRender(row, rowIndex);
            }
            else if (customRender.length == 3) {
                return customRender(row, rowIndex, tdEl);
            }
            else if (customRender.length == 4) {
                return customRender(row, rowIndex, tdEl, trEl);
            }
            else {
                return this.getLabel(row, col, rowIndex);
            }
        };
        Table.prototype.autoSetThWidth = function () {
            var _this = this;
            if (!this.columns) {
                return;
            }
            var preEl = $('body').find('#textWidthPre');
            if (!preEl || preEl.length == 0) {
                preEl = $('<pre id="textWidthPre"></pre>').css({ display: 'none' });
                $('body').append(preEl);
            }
            var thList = this.templateEl.find('th');
            this.columns.forEach(function (col, index) {
                if (col.maxTextLength || (col.maxWidth && col.customRender)) {
                    var widthLabel = '';
                    if (col.maxText) {
                        widthLabel = col.maxText;
                    }
                    var colIndex = index;
                    if (_this.showRowNum || _this.rowCheckable) {
                        colIndex++;
                    }
                    if (_this.showTreeTable) {
                        colIndex++;
                    }
                    if (thList && colIndex < thList.length) {
                        var thEl = $(thList[colIndex]);
                        try {
                            thEl.css("zIndex", 1);
                        }
                        catch (err) {
                        }
                        var width = 0;
                        if (col.maxWidth && col.customRender) {
                            width = col.maxWidth;
                        }
                        else {
                            if (col.nonText || (widthLabel != null && thEl.text() && widthLabel.length < thEl.text().length)) {
                                widthLabel = thEl.text();
                            }
                            preEl.html(_util.xssUtil.filterXSS(widthLabel));
                            width = preEl.width() + 60;
                            if (width == 50) {
                                var canvas = document.createElement('canvas');
                                var canvasCtx = canvas.getContext("2d");
                                canvasCtx.font = $('body').css('font-size');
                                width = canvasCtx.measureText(widthLabel).width + 50;
                            }
                        }
                        if (width && width > 60) {
                            if ((!col.maxWidth || col.maxWidth <= width)) {
                                thEl.css("width", width);
                                col.maxWidth = width;
                            }
                            else if (col.maxWidth) {
                                thEl.css("width", col.maxWidth);
                            }
                        }
                    }
                }
            });
        };
        Table.prototype.getSelectCells = function () {
            var tds = [];
            this.templateEl.find('.' + this.cellSelectClass).each(function () {
                var td = $(this), d = td.data('cell-data');
                d.el = td;
                tds.push(d);
            });
            return tds;
        };
        Table.prototype.getComponentForEdit = function (row, col, td) {
            var editTr = "<div class=\"ui input inner-input\">\n                <input type=\"text\" placeholder=\"\">\n            </div>";
            var self = this;
            td.dblclick(function (e) {
                if (td.find(".inner-input").length > 0) {
                    e.preventDefault();
                    return;
                }
                if (_context.lastActiveObjCombo) {
                    _context.lastActiveObjCombo.callback();
                }
                var orginValue = $(e.target).text();
                var replaceEl = $(editTr);
                replaceEl.find("input").val(orginValue).click(function (e) {
                    e.stopPropagation();
                });
                td.html(replaceEl);
                replaceEl.find("input").focus();
                td.toggleClass("editable");
                replaceEl.find("input").blur(endEditTd).keydown(function (e) {
                    if (e.keyCode == "13" || e.keyCode == "27") {
                        endEditTd(e.keyCode == "27");
                    }
                });
                function endEditTd(giveup) {
                    var inputVal = td.find("input").val();
                    if (!giveup) {
                        td.safeHtml(inputVal);
                        var fieldName = col.name;
                        row[fieldName] = inputVal;
                    }
                    else {
                        td.text(orginValue);
                    }
                    td.toggleClass("editable");
                    self.markCellDirty(td, col.cellEditDirtyClass);
                }
            });
        };
        Table.prototype.markCellDirty = function (td, dirtyClass) {
            if (dirtyClass === void 0) { dirtyClass = undefined; }
            var dirty = false;
            if (td._originText && td._originText != td.text())
                dirty = true;
            if (dirtyClass === undefined)
                dirtyClass = this.cellEditDirtyClass;
            td.toggleClass(dirtyClass, dirty);
        };
        Table.prototype.getRowEl = function (row) {
            var ri = this.getRowElAndIndex(row);
            return ri ? ri[0] : null;
        };
        Table.prototype.getRowElAndIndex = function (row) {
            var idx = this.getRowIndex(row);
            if (idx === -1)
                return null;
            var tr = this.tbody.find('tr.suitablerow').eq(idx);
            return [tr, idx];
        };
        Table.prototype.updateRowNum = function (rowNum, tr) {
            if (rowNum === void 0) { rowNum = -1; }
            if (tr === void 0) { tr = null; }
            if (this.showRowNum) {
                if (tr) {
                    if (this.rowCheckable)
                        tr.find('td:eq(0)').find('label').text(rowNum + 1);
                    else
                        tr.find('td:eq(0)').text(rowNum + 1);
                }
                else {
                    this.updateAllRowNums();
                }
            }
        };
        Table.prototype.updateAllRowNums = function () {
            var _this = this;
            clearTimeout(this._updateRowNumTimer);
            this._updateRowNumTimer = setTimeout(function () {
                _this.tbody.find('tr.suitablerow').each(function (i, tr) {
                    if (_this.rowCheckable)
                        $(tr).find('td:eq(0)').find('label').text(i + 1);
                    else
                        $(tr).find('td:eq(0)').text(i + 1);
                });
            }, 200);
        };
        Table.prototype.addRows = function (rows, rowNum) {
            var _this = this;
            if (rowNum === void 0) { rowNum = -1; }
            this.data = this.data || [];
            if (rowNum === -1)
                rowNum = this.data.length;
            rows.forEach(function (r, i) { return _this.addRow(r, rowNum + i); });
        };
        Table.prototype.getLabel = function (row, col, rowNum) {
            var rendered;
            if (typeof col.html === 'string')
                rendered = col.html;
            else if (Array.isArray(col.htmls))
                col.htmls.forEach(function (l) { rendered = l.content || l; });
            else if (typeof col.getLabel === 'function')
                rendered = col.getLabel(row, col, rowNum);
            else if (typeof col.getLabel === 'string')
                rendered = eval(col.getLabel);
            else if (Array.isArray(col.getLabels))
                col.getLabels.forEach(function (l) {
                    rendered = eval(l.content || l);
                });
            if (rendered === undefined && this.renderer)
                rendered = this.renderer(row, col);
            if (rendered === undefined || rendered === null)
                rendered = row[col.name];
            if (rendered === undefined && col.name.indexOf('.') !== -1) {
                try {
                    rendered = eval('row.' + col.name);
                }
                catch (e) { }
            }
            if (rendered && rendered.getParentEl && rendered.getEl) {
                rendered = rendered.getParentEl();
            }
            var fixNumber = col.fixNumber;
            if (fixNumber === undefined)
                fixNumber = this.fixNumber;
            if (typeof rendered === 'number' && fixNumber !== false) {
                if (typeof fixNumber === 'function')
                    rendered = fixNumber(rendered, row, col);
                else {
                    if (parseInt(rendered + '') !== rendered) {
                        if (typeof fixNumber === 'string') {
                            try {
                                fixNumber = parseInt(fixNumber);
                            }
                            catch (e) { }
                        }
                        rendered = rendered.toFixed(typeof fixNumber === 'number' ? this.fixNumber : 2);
                    }
                }
            }
            if (typeof rendered === 'number' && (col.type === 'date' || col.type === 'datetime')) {
                rendered = _util.formatDate(rendered, col.type === 'date' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm');
            }
            return rendered;
        };
        Table.prototype.removeRow = function (row) {
            var index = this.data.indexOf(row);
            if (index != -1)
                this.removeRowAt(index);
            return index;
        };
        Table.prototype.removeRowAt = function (index) {
            this.tbody.find('tr._itemDetail').remove();
            this.tbody.find('tr').eq(index).remove();
            if (this.checkedData.indexOf(this.data[index]) !== -1)
                this.checkedData.splice(this.checkedData.indexOf(this.data[index]), 1);
            this.data.splice(index, 1);
            this.updateRowNum();
        };
        Table.prototype.getRowIndex = function (row) {
            return this.data.indexOf(row);
        };
        Table.prototype.getRowAt = function (idx) {
            return this.data[idx];
        };
        Table.prototype.removeDetailRow = function () {
            this.tbody.find('tr._itemDetail').remove();
            this.rowDetailEl = null;
        };
        Table.prototype.doBatchCheck = function (lastIndex, toIndex) {
            this.tbody.find('tr.suitablerow').filter(function (index) {
                if (toIndex < lastIndex)
                    return index >= toIndex && index < lastIndex;
                return index > lastIndex && index <= toIndex;
            }).find('> td.colhead > .checkbox').checkbox('check');
        };
        Table.prototype.rowClick = function (row, tr, rowNum, event) {
            var _this = this;
            this.trigger('rowclick', { row: row, tr: tr });
            var singleAppending = event.ctrlKey, batchAppending = event.shiftKey && this.lastChecked;
            if (this.selectCheck || singleAppending || batchAppending) {
                if (batchAppending) {
                    var lastIndex = this.lastChecked.index;
                    this.doBatchCheck(lastIndex, rowNum);
                }
                else {
                    if (!singleAppending && this.selectCheck !== 'multiple')
                        this.checkAll(false);
                    tr.find('> td.colhead > .checkbox').checkbox('check');
                }
                if (singleAppending || batchAppending)
                    return;
            }
            if (this.rowDetailField) {
                if (this.rowDetailEl && this.rowDetailEl.closest('tr').prev()[0] === tr[0]) {
                    this.removeDetailRow();
                    return;
                }
                this.tbody.find('tr._itemDetail').remove();
                var ntr = $('<tr>').addClass('_itemDetail');
                tr.after(ntr);
                var len = this.columns.length;
                if (this.showRowNum || this.rowCheckable)
                    len++;
                var td = $('<td>').css({ textAlign: 'left' }).attr('colspan', len).appendTo(ntr);
                var detail = _util.fetchField(row, this.rowDetailField);
                this.rowDetailEl = $(detail).appendTo(td);
                return this.rowDetailEl;
            }
            if (row && row.childrenDatas) {
                var itd = tr.find('td i');
                if (itd && itd.hasClass('right')) {
                    itd.removeClass('right');
                    itd.addClass('down');
                }
                else if (itd && itd.hasClass('down')) {
                    itd.removeClass('down');
                    itd.addClass('right');
                }
                var childrenRows = this.tbody.find('tr.childrenRow-' + row[this.rowKey]);
                if (childrenRows.length > 0) {
                    childrenRows.remove();
                    return;
                }
                childrenRows.remove();
                row.childrenDatas.forEach(function (childrenData, index) {
                    var ntr = $('<tr>').addClass('suitablerow childrenRow-' + row[_this.rowKey]);
                    tr.after(ntr);
                    tr = ntr;
                    if (_this.showRowNum || _this.rowCheckable) {
                        _this.addRowCheck(childrenData, tr, rowNum + index);
                    }
                    if (_this.showTreeTable) {
                        $('<td style="text-align:center;">' + (index + 1) + '</td>').appendTo(tr);
                    }
                    _this.columns.forEach(function (column) {
                        var td = $('<td>').appendTo(tr);
                        var label = '';
                        try {
                            label = _this.getLabel(childrenData, column, rowNum);
                        }
                        catch (e) {
                            console.log(e.message);
                        }
                        td.html(label);
                    });
                });
            }
        };
        Table.prototype.getChecked = function () {
            var s = this.checkedData.slice();
            return this.rowCheckable === 'single' ? s[0] : s;
        };
        Table.prototype.checkAll = function (check, but) {
            if (check === void 0) { check = true; }
            if (but === void 0) { but = null; }
            this.tbody.find('td > ._rowchecker').filter(function () {
                if (but && but[0] === this)
                    return false;
                var v = $(this).checkbox('is checked');
                return v !== check;
            }).checkbox(check ? 'check' : 'uncheck');
            if (this.checkHeaderEl && this.checkHeaderEl.checkbox)
                this.checkHeaderEl.checkbox(check ? 'set checked' : 'set unchecked');
        };
        Table.prototype.checkRow = function (row, check) {
            if (check === void 0) { check = true; }
            var re = this.getRowEl(row);
            if (re) {
                re.find('td > .checkbox').checkbox(check ? 'check' : 'uncheck');
            }
        };
        Table.prototype.checkRows = function (rows, check) {
            var _this = this;
            if (check === void 0) { check = true; }
            rows.forEach(function (r) { return _this.checkRow(r, check); });
        };
        Table.prototype.updateSumRow = function () {
            var _this = this;
            var tr = this.sumTr;
            if (!tr)
                return;
            tr.empty();
            if (this.rowHeader || this.showRowNum || this.rowCheckable) {
                $('<th>').appendTo(tr);
            }
            this.columns.forEach(function (col, i) {
                $('<th>').html(_this.getSumText(col, i)).appendTo(tr);
            });
        };
        Table.prototype.getSumText = function (col, i) {
            var so = col.sumOption || {};
            var sumFn = so.fn || col.sumFn, sumText = so.text || col.sumText;
            if (sumFn)
                return sumFn(col, i);
            if (sumText)
                return sumText;
            if (col.type === 'number') {
                var sumMethod = so.method || col.sumMethod;
                if (!sumMethod && col.label && (col.label.endsWith('率') || col.label.endsWith('%')))
                    sumMethod = 'avg';
                var total_1 = 0, data = this.data;
                if (this.displayFilter)
                    data = data.filter(this.displayFilter);
                data.forEach(function (row) { return total_1 += row[col.name] || 0; });
                if (sumMethod === 'avg')
                    total_1 /= data.length;
                var fd = 2;
                if (so.fixed)
                    fd = so.fixed;
                var text = total_1.toFixed(fd), pos = text.lastIndexOf('.');
                if (pos !== -1) {
                    while (text[text.length - 1] === '0')
                        text = text.substring(0, text.length - 1);
                    if (text.endsWith('.'))
                        text = text.substring(0, text.length - 1);
                }
                return text;
            }
            if (i === 0)
                return _locale.sui.table.summary;
            return '';
        };
        Table.prototype.showLoading = function (v) {
            if (v === void 0) { v = true; }
            showLoading(this.getEl().find('._maintable').parent(), v ? (_locale.common ? _locale.common.loading : 'loading..') : false);
        };
        Table.prototype.moveCheckedRow = function (up, checked) {
            var _this = this;
            if (up === void 0) { up = true; }
            if (checked === void 0) { checked = undefined; }
            if (!checked)
                checked = this.getChecked();
            if (!checked.length)
                return;
            checked.sort(function (a, b) {
                return _this.data.indexOf(a) - _this.data.indexOf(b);
            });
            if (!up)
                checked.reverse();
            var lastRow = undefined;
            checked.forEach(function (o, i) {
                var pos = _this.data.indexOf(o), canMove = true;
                if (up) {
                    var minPos = 0;
                    if (lastRow)
                        minPos = _this.data.indexOf(lastRow) + 1;
                    if (pos <= minPos)
                        canMove = false;
                }
                else {
                    var maxPos = _this.data.length - 1;
                    if (lastRow)
                        maxPos = _this.data.indexOf(lastRow) - 1;
                    if (pos >= maxPos)
                        canMove = false;
                }
                lastRow = o;
                if (canMove) {
                    var toredraw = [o];
                    if (up)
                        pos--;
                    else
                        pos++;
                    toredraw.push(_this.data[pos]);
                    _this.removeRow(o);
                    _this.addRow(o, pos);
                    _this.checkRow(o);
                }
                _this.getRowEl(o).transition('glow');
            });
            return true;
        };
        return Table;
    }(Component));
    _sui.Table = Table;
    function grid(el, options) {
        if (options === void 0) { options = null; }
        return new Table(el, options).render();
    }
    _sui.grid = grid;
    var AbstractFormField = (function (_super) {
        __extends(AbstractFormField, _super);
        function AbstractFormField(field, parentEl, options) {
            if (options === void 0) { options = {}; }
            var _this = _super.call(this, parentEl, options) || this;
            _this.field = field;
            _this.template = '<label></label>';
            _this.inputTagName = 'input';
            _this.inputTagName = field.inputTagName || _this.inputTagName;
            _this.inputAttrs = field.inputAttrs || _this.inputAttrs;
            _this.inputStyle = field.inputStyle || _this.inputStyle;
            return _this;
        }
        AbstractFormField.prototype.doRender = function (el, pre) {
            var _this = this;
            _super.prototype.doRender.call(this, el, pre);
            var labelEl = this.labelEl = this.templateEl.parent().find('label');
            labelEl.html(this.field.label).css({ 'align-items': 'center' });
            labelEl.attr('title', this.field.tooltip || this.field.title || this.field.label);
            if (this.inline)
                labelEl.css('width', this.inline.labelWidth || '6rem');
            if (this.inputTagName) {
                var input = this.inputEl = $('<' + this.inputTagName + '>');
                this.templateEl.after(input);
                input.attr('name', this.field.name);
                if (this.inline) {
                    input.css('flex-grow', 1);
                    input.css('width', '4em');
                }
                setAttrs(input, this.inputAttrs);
                setStyle(input, this.inputStyle);
                input.change(function () {
                    _this.value = _this.getInputElValue();
                    _this.trigger('change', _this.value);
                });
            }
            else {
                this.inputEl = this.templateEl.find('input');
                if (!this.inputEl.length)
                    this.templateEl.parent().find('input');
            }
            if (this.iconActions) {
                var span = $('<span class="field-act-bar">').appendTo(this.getParentEl());
                var itemFn = function () {
                    return _this.formInstance.getData();
                };
                renderActionsTo(this.iconActions, span, itemFn, this);
            }
        };
        AbstractFormField.prototype.doIconAction = function (id) {
            this.getParentEl().find('.field-act-bar .bar_action#' + id).click();
        };
        AbstractFormField.prototype.setValue = function (v, dirty) {
            if (dirty === void 0) { dirty = true; }
            if (!dirty)
                this.original = v;
            this.value = v;
            this.setInputValue(v);
        };
        AbstractFormField.prototype.setInputValue = function (v) {
            if (this.inputTagName && this.inputEl) {
                var old = this.inputEl.val();
                if (old === undefined)
                    old = '';
                if (v === undefined)
                    v = '';
                this.inputEl.val(v);
                if (old != v)
                    this.inputEl.change();
            }
        };
        AbstractFormField.prototype.setInitValue = function (v) {
            this.setValue(v, false);
        };
        AbstractFormField.prototype.getInputElValue = function () {
            return this.inputEl.val();
        };
        AbstractFormField.prototype.getValue = function () {
            return this.value;
        };
        AbstractFormField.prototype.getInitValue = function () {
            return this.original;
        };
        AbstractFormField.prototype.isDirty = function () {
            return this.original != this.value;
        };
        AbstractFormField.prototype.onChange = function (fn) {
            this.on('change', fn);
        };
        AbstractFormField.prototype.setReadOnly = function (v) {
            var _this = this;
            if (v === void 0) { v = true; }
            this.rendered().then(function () {
                if (v) {
                    _this.getParentEl().find('input').attr('readOnly', 'readOnly');
                    _this.getParentEl().find('.field-act-bar').css('pointer-events', 'none');
                }
                else {
                    _this.getParentEl().find('input').removeAttr('readOnly');
                    _this.getParentEl().find('.field-act-bar').css('pointer-events', 'auto');
                }
            });
        };
        AbstractFormField.prototype.setForm = function (f) {
            this.formInstance = f;
        };
        AbstractFormField.prototype.setRequired = function (v) {
            var _this = this;
            if (v === void 0) { v = true; }
            this.rendered().then(function () {
                if (v) {
                    _this.formInstance.getEl().form('add rule', _this.field.name, 'empty');
                }
                else {
                    _this.formInstance.getEl().form('remove rule', _this.field.name, 'empty');
                }
                _this.getParentEl().toggleClass('required', v);
                if (window['_context'] && window['_context'].getSetting('formFieldRequiredWarn')) {
                    _this.getParentEl().addClass('requiredwarn', v);
                }
                _this.formInstance.getEl().form('validate field', _this.field.name);
            });
        };
        return AbstractFormField;
    }(Component));
    _sui.AbstractFormField = AbstractFormField;
    var InputField = (function (_super) {
        __extends(InputField, _super);
        function InputField() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.original = '';
            _this.value = '';
            return _this;
        }
        InputField.prototype.doRender = function (el, pre) {
            _super.prototype.doRender.call(this, el, pre);
            if (this.inputType) {
                this.inputEl.attr('type', this.inputType);
                if (this.inputType === 'password')
                    this.inputEl.attr('autocomplete', 'new-password');
            }
        };
        InputField.prototype.getInputElValue = function () {
            var v = this.inputEl.val();
            if (this.inputType === 'password')
                v = btoa(v) + "\r\n";
            return v;
        };
        InputField.prototype.setValue = function (v, dirty) {
            if (dirty === void 0) { dirty = true; }
            _super.prototype.setValue.call(this, v, dirty);
        };
        return InputField;
    }(AbstractFormField));
    _sui.InputField = InputField;
    var TextAreaField = (function (_super) {
        __extends(TextAreaField, _super);
        function TextAreaField() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.inputTagName = 'textarea';
            _this.original = '';
            _this.value = '';
            return _this;
        }
        return TextAreaField;
    }(AbstractFormField));
    _sui.TextAreaField = TextAreaField;
    var TextField = (function (_super) {
        __extends(TextField, _super);
        function TextField() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.inputType = 'text';
            return _this;
        }
        return TextField;
    }(InputField));
    _sui.TextField = TextField;
    var LabelField = (function (_super) {
        __extends(LabelField, _super);
        function LabelField(field, parentEl, options) {
            if (options === void 0) { options = {}; }
            var _this = _super.call(this, field, parentEl, options) || this;
            _this.field = field;
            _this.inputTagName = 'div';
            _this.getLabel = '';
            _this.getLabel = field.getLabel;
            if (field.value || field.default)
                _this.rendered().then(function () {
                    _this.setValue(field.value || field.default);
                });
            return _this;
        }
        LabelField.prototype.setValue = function (v, dirty) {
            var _this = this;
            if (dirty === void 0) { dirty = true; }
            _util.dataCallback(function (val) {
                if (_util.isjquery(val))
                    val.appendTo(_this.inputEl);
                else
                    _this.inputEl.html(val);
            }, this.field.getLabel, [v], this);
        };
        return LabelField;
    }(AbstractFormField));
    _sui.LabelField = LabelField;
    var ExpressionField = (function (_super) {
        __extends(ExpressionField, _super);
        function ExpressionField(field, parentEl, options) {
            if (options === void 0) { options = {}; }
            var _this = _super.call(this, field, parentEl, options) || this;
            _this.field = field;
            _this.inputType = 'text';
            _this.getLabel = '';
            _this.getLabel = field.getLabel;
            return _this;
        }
        ExpressionField.prototype.getValueByGetLabel = function (v) {
            var value = v;
            return eval(this.getLabel);
        };
        ExpressionField.prototype.setValue = function (v, dirty) {
            if (dirty === void 0) { dirty = true; }
            var value = this.getValueByGetLabel(v);
            _super.prototype.setValue.call(this, value, dirty);
            this.inputEl.val(value);
        };
        return ExpressionField;
    }(InputField));
    _sui.ExpressionField = ExpressionField;
    var NumberField = (function (_super) {
        __extends(NumberField, _super);
        function NumberField() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.inputType = 'number';
            return _this;
        }
        NumberField.prototype.doRender = function (el, pre) {
            _super.prototype.doRender.call(this, el, pre);
            if (this.inputEl)
                this.inputEl.keydown(function (e) {
                    if (e.which == 69)
                        return false;
                });
        };
        NumberField.prototype.toNumber = function (v) {
            if (typeof v === 'string') {
                if (v.indexOf('.') === -1)
                    v = parseInt(v);
                v = parseFloat(v);
            }
            if (typeof v === 'number' && isNaN(v))
                v = 0;
            return v;
        };
        NumberField.prototype.getInputElValue = function () {
            var v = this.inputEl.val();
            return this.toNumber(v);
        };
        NumberField.prototype.setValue = function (v, dirty) {
            if (dirty === void 0) { dirty = true; }
            if (v !== undefined && v !== '')
                v = this.toNumber(v);
            _super.prototype.setValue.call(this, v, dirty);
            this.inputEl.val(v);
        };
        return NumberField;
    }(InputField));
    _sui.NumberField = NumberField;
    var DateField = (function (_super) {
        __extends(DateField, _super);
        function DateField() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.dateFormat = 'YYYY-MM-DD';
            _this.datetimeFormat = 'YYYY-MM-DD HH:mm';
            _this.rangeSeparator = ' ~ ';
            _this.defaultPickerOpts = { timePicker24Hour: true, showDropdowns: true, singleDatePicker: true, autoUpdateInput: false, locale: {}, autoApply: true, alwaysShowCalendars: true, showCustomRangeLabel: false };
            return _this;
        }
        DateField.prototype.prepare = function () {
            _super.prototype.prepare.call(this);
            if (!$.prototype.daterangepicker) {
                _util.loadCss('lib/jquery/daterangepicker/daterangepicker.css');
                return _util.loadScript('lib/moment.min.js', 'moment').then(function (e) {
                    return _util.loadScript('lib/jquery/daterangepicker/daterangepicker.js');
                });
            }
        };
        DateField.prototype.destroy = function () {
            _super.prototype.destroy.call(this);
            this.inputEl.data('daterangepicker').remove();
        };
        DateField.prototype.doRender = function (el, pre) {
            _super.prototype.doRender.call(this, el, pre);
            this.defaultPickerOpts.locale = Object.assign({}, _locale.sui.datepicker);
            var opt = Object.assign({}, this.defaultPickerOpts);
            opt.locale.separator = this.rangeSeparator;
            opt.locale.format = this.dateFormat;
            if (this.field.type === 'datetime') {
                opt.timePicker = true;
                opt.locale.format = this.datetimeFormat;
            }
            opt.singleDatePicker = !this.field.daterange;
            if (!opt.singleDatePicker) {
                var moment = window['moment'];
                var ranges = opt.ranges = {}, lc = _locale.sui.datepicker || {};
                ranges[lc.today || 'Today'] = [moment().startOf('day'), moment().endOf('day')];
                ranges[lc.thisweek || 'This week'] = [moment().startOf('week'), moment().endOf('week')];
                ranges[lc.lastweek || 'Last week'] = [moment().subtract(1, 'weeks').startOf('week'), moment().subtract(1, 'weeks').endOf('week')];
                ranges[lc.thismonth || 'This month'] = [moment().startOf('month'), moment().endOf('month')];
                ranges[lc.lastmonth || 'Last month'] = [moment().subtract(1, 'months').startOf('month'), moment().subtract(1, 'months').endOf('month')];
                ranges[lc.thisyear || 'This year'] = [moment().startOf('year'), moment().endOf('year')];
                ranges[lc.lastyear || 'Last year'] = [moment().subtract(1, 'years').startOf('year'), moment().subtract(1, 'years').endOf('year')];
            }
            if (this.field.datepickerOptions) {
                Object.assign(opt, _util.parsekv(this.field.datepickerOptions));
            }
            var inputEl = this.inputEl;
            inputEl.attr('autocomplete', 'off');
            if (!this.field.disabled && !this.field.readOnly) {
                inputEl.daterangepicker(opt, function (start, end) {
                    var newValue = this.startDate.format(this.locale.format);
                    if (!this.singleDatePicker) {
                        newValue += this.locale.separator + this.endDate.format(this.locale.format);
                    }
                    if (newValue !== inputEl.val())
                        inputEl.val(newValue).change();
                });
                inputEl.data('daterangepicker').container.click(function () { return false; });
            }
        };
        DateField.prototype.date2string = function (v) {
            if (!v)
                return '';
            return this.formatDate(v, this.field.type === 'datetime' ? this.datetimeFormat : this.dateFormat);
        };
        DateField.prototype.formatDate = function (d, f) {
            return _util.formatDate(d, f);
        };
        DateField.prototype.setValue = function (v, dirty) {
            if (dirty === void 0) { dirty = true; }
            if (Array.isArray(v)) {
                var f = this.field.type === 'datetime' ? this.datetimeFormat : this.dateFormat;
                v = this.formatDate(v[0], f) + this.rangeSeparator + this.formatDate(v[1], f);
            }
            if (typeof v !== 'string')
                v = this.date2string(v);
            _super.prototype.setValue.call(this, v, dirty);
        };
        return DateField;
    }(InputField));
    _sui.DateField = DateField;
    var BoolField = (function (_super) {
        __extends(BoolField, _super);
        function BoolField() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.original = undefined;
            _this.value = undefined;
            _this.template = "\n            <div class=\"ui checkbox\">\n            <input type=\"checkbox\">\n            <label></label>\n            </div>\n        ";
            _this.inputTagName = '';
            return _this;
        }
        BoolField.prototype.prepare = function () {
            if (this.field.trueValue !== undefined)
                this.trueValue = this.field.trueValue;
            if (this.field.falseValue !== undefined)
                this.falseValue = this.field.falseValue;
            return _super.prototype.prepare.call(this);
        };
        BoolField.prototype.doRender = function (el, pre) {
            var _this = this;
            _super.prototype.doRender.call(this, el, pre);
            var labelEl = this.templateEl.parent().find('label');
            labelEl.css('width', 'auto');
            this.inputEl = el.find('input');
            this.inputEl.change(function () {
                _this.value = _this.getInputElValue();
                _this.trigger('change', _this.value);
            });
        };
        BoolField.prototype.getInputElValue = function () {
            return this.inputEl.is(':checked');
        };
        BoolField.prototype.setValue = function (v, dirty) {
            if (dirty === void 0) { dirty = true; }
            if (v === this.trueValue)
                v = true;
            else if (v === this.falseValue)
                v = false;
            _super.prototype.setValue.call(this, v, dirty);
            this.templateEl.checkbox(v ? 'check' : 'uncheck');
        };
        BoolField.prototype.getValue = function () {
            if (this.value && this.trueValue !== undefined)
                return this.trueValue;
            if (!this.value && this.falseValue !== undefined)
                return this.falseValue;
            return _super.prototype.getValue.call(this);
        };
        return BoolField;
    }(InputField));
    _sui.BoolField = BoolField;
    var PasswordField = (function (_super) {
        __extends(PasswordField, _super);
        function PasswordField() {
            return _super !== null && _super.apply(this, arguments) || this;
        }
        PasswordField.prototype.doRender = function (el, pre) {
            _super.prototype.doRender.call(this, el, pre);
            this.inputEl.before($('<input type="password" style="display:none;">'));
        };
        return PasswordField;
    }(InputField));
    _sui.PasswordField = PasswordField;
    var CheckField = (function (_super) {
        __extends(CheckField, _super);
        function CheckField() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.inputTagName = '';
            _this.selectedItems = [];
            _this.value = '';
            return _this;
        }
        CheckField.prototype.prepare = function () {
            if (!this.data)
                this.data = this.field.data || this.field.items;
            return _super.prototype.prepare.call(this);
        };
        CheckField.prototype.doSetData = function (items) {
            var _this = this;
            var sels = this.selectedItems, self = this;
            if (typeof items === 'string')
                items = _util.parsekv(items);
            if (items && !Array.isArray(items))
                items = [items];
            if (items)
                items.forEach(function (i) {
                    var value = i.value || i.id;
                    var node = $('<div>').appendTo(_this.getParentEl());
                    var ch = $('<input type="checkbox">').attr('id', value).appendTo(node).change(function () {
                        self.value = self.getChecked().join(',');
                        self.trigger('change', self.value);
                    });
                    $('<label>').css({ padding: '5px' }).html(i.label || i.title || i.name).appendTo(node);
                    if (i.selected || i.checked)
                        ch.prop('checked', true);
                });
            _super.prototype.doSetData.call(this, items);
            this.inputEl = this.getParentEl().find('input');
        };
        CheckField.prototype.getChecked = function () {
            var cs = [];
            this.getParentEl().find('input:checkbox:checked').each(function () {
                cs.push(this.id);
            });
            return cs;
        };
        CheckField.prototype.setValue = function (v, dirty) {
            if (dirty === void 0) { dirty = true; }
            if (typeof v === 'string')
                v = v.split(',');
            if (v !== null && v !== undefined && !Array.isArray(v))
                v = [v];
            this.getParentEl().find('input:checkbox').each(function () {
                var _this = this;
                $(this).prop('checked', v && v.findIndex(function (vo) { return vo == _this.id; }) !== -1);
            });
            if (v)
                v = v.join(',');
            _super.prototype.setValue.call(this, v, dirty);
        };
        CheckField.prototype._falseClickFn = function () {
            return false;
        };
        CheckField.prototype.setReadOnly = function (v) {
            var _this = this;
            if (v === void 0) { v = true; }
            this.rendered().then(function () {
                if (v)
                    _this.getParentEl().find('input').click(_this._falseClickFn);
                else
                    _this.getParentEl().find('input').off('click', _this._falseClickFn);
            });
        };
        return CheckField;
    }(AbstractFormField));
    _sui.CheckField = CheckField;
    var RadioField = (function (_super) {
        __extends(RadioField, _super);
        function RadioField() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.inputTagName = '';
            return _this;
        }
        RadioField.prototype.prepare = function () {
            if (!this.data)
                this.data = this.field.data || this.field.items;
            return _super.prototype.prepare.call(this);
        };
        RadioField.prototype.doSetData = function (items) {
            var _this = this;
            var self = this;
            if (typeof items === 'string')
                items = _util.parsekv(items);
            if (items && !Array.isArray(items))
                items = [items];
            if (items)
                items.forEach(function (i) {
                    var value = i.value || i.id;
                    var node = $('<div>').appendTo(_this.getParentEl());
                    $('<input type="radio">').attr('id', value).attr('name', self.field.name).appendTo(node).change(function () {
                        self.value = $(this).parent().find('input:radio:checked').attr('id');
                        self.trigger('change', self.value);
                    });
                    $('<label>').css({ padding: '5px' }).html(i.label || i.title || i.name).appendTo(node);
                });
            this.inputEl = this.getParentEl().find('input');
            _super.prototype.doSetData.call(this, items);
        };
        RadioField.prototype.setValue = function (v, dirty) {
            if (dirty === void 0) { dirty = true; }
            _super.prototype.setValue.call(this, v, dirty);
            this.getParentEl().find('input:radio').each(function () {
                $(this).prop('checked', v == this.id);
            });
        };
        RadioField.prototype._falseClickFn = function () {
            return false;
        };
        RadioField.prototype.setReadOnly = function (v) {
            var _this = this;
            if (v === void 0) { v = true; }
            this.rendered().then(function () {
                if (v)
                    _this.getParentEl().find('input').click(_this._falseClickFn);
                else
                    _this.getParentEl().find('input').off('click', _this._falseClickFn);
            });
        };
        return RadioField;
    }(AbstractFormField));
    _sui.RadioField = RadioField;
    var SearchField = (function (_super) {
        __extends(SearchField, _super);
        function SearchField() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.inputTagName = '';
            _this.searchOptions = {};
            return _this;
        }
        SearchField.prototype.doRender = function (el, pre) {
            var _this = this;
            _super.prototype.doRender.call(this, el, pre);
            this.search = search(el.parent(), Object.assign(this.field, this.options, this.searchOptions));
            return this.search.rendered().then(function () {
                _this.search.getEl().css('flex-grow', 1);
                _this.inputEl = el.parent().find('input');
                _this.search.on('change', function (e, data) {
                    _this.value = data;
                    _this.trigger('change', data);
                });
            });
        };
        SearchField.prototype.getValue = function () {
            return this.search.getValue();
        };
        SearchField.prototype.setValue = function (v) {
            this.search.setValue(v);
        };
        SearchField.prototype.getValueItem = function () {
            return this.search.getValueItem();
        };
        return SearchField;
    }(AbstractFormField));
    _sui.SearchField = SearchField;
    var GeomField = (function (_super) {
        __extends(GeomField, _super);
        function GeomField() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.iconActions = [{
                    icon: 'map pin', fn: function (data, action, uiField) {
                        _this.iconClick(data, action, uiField);
                    }
                }];
            _this.noEndDraw = false;
            _this.editSaveReturn = true;
            _this.tabActive = 0;
            return _this;
        }
        GeomField.prototype.iconClick = function (data, action, uiField) {
            var _this = this;
            var field = uiField.field;
            if (field.noEndDraw) {
                this.noEndDraw = field.noEndDraw;
            }
            if (field.editSaveReturn) {
                this.editSaveReturn = field.editSaveReturn;
            }
            var mapContext = this.getMapContext();
            if (mapContext.window.parent['activeMapWorkspace']) {
                this.tabActive = mapContext.window['getWorkspace']().getActiveTabIndex();
            }
            if (field && field.beforeDraw) {
                var beforeDraw = field.beforeDraw(data, action, uiField);
                if (_util.isPromise(beforeDraw)) {
                    _util.dataCallback(function () {
                        _this.activeDraw(data, action, uiField);
                    }, beforeDraw);
                }
                else if (beforeDraw) {
                    this.activeDraw(data, action, uiField);
                }
            }
            else {
                this.activeDraw(data, action, uiField);
            }
        };
        GeomField.prototype.getMapContext = function () {
            var mapContext = _context, map = mapContext.map;
            while (!map && mapContext.parentContext) {
                mapContext = mapContext.parentContext;
            }
            return mapContext;
        };
        GeomField.prototype.activeDraw = function (data, action, uiField) {
            if (uiField.field.drawtool == true)
                return this.activeDrawWithDrawTool(data, action, uiField);
            return this.activeDrawDefault(data, action, uiField);
        };
        GeomField.prototype.activeDrawDefault = function (data, action, uiField) {
            var _this = this;
            var field = uiField.field;
            var drawOptions = { draw: { circle: true, rectangle: true, marker: true, polyline: true, polygon: true } };
            var drawType = field.gtype;
            if (drawType === 'point')
                drawType = 'marker';
            if (drawType) {
                drawOptions.draw[drawType] = true;
                for (var k in drawOptions)
                    if (k !== drawType)
                        drawOptions[k] = false;
                drawOptions.initMode = drawType;
            }
            var fo = field.mapDrawOptions;
            if (fo) {
                fo = _util.parsekv(fo);
                Object.assign(drawOptions, fo);
            }
            var mapContext = this.getMapContext();
            var map = mapContext.map;
            mapContext.window['activeMapWorkspace']();
            map.activeDrawSelect(false);
            mapContext.message(field.mapDrawMessage || '请选择绘制方式并在地图上进行绘制');
            maskModalsDimmer(true);
            map.activeDraw(drawOptions, function (type, graphic) {
                var endReturn = !_this.noEndDraw;
                map.enableEditHandler();
                if (field && field.afterDraw) {
                    var afterDraw = field.afterDraw(mapContext, type, graphic, uiField, map, _this.tabActive);
                    if (_util.isPromise(afterDraw)) {
                        _util.dataCallback(function (e, isError) {
                            if (!isError)
                                _this.endDraw(mapContext, type, graphic, uiField, map, endReturn);
                        }, afterDraw);
                    }
                    else if (afterDraw) {
                        _this.endDraw(mapContext, type, graphic, uiField, map, endReturn);
                    }
                }
                else {
                    _this.endDraw(mapContext, type, graphic, uiField, map, endReturn);
                }
            });
            if (map.lmap) {
                map.lmap.off('draw:edited');
                map.isEditing = true;
                map.lmap.on('draw:edited', function (e) {
                    var type = e.layerType;
                    var layers = e.layers;
                    if (!layers) {
                        return;
                    }
                    var isUpdate = false;
                    layers.eachLayer(function (graphic) {
                        isUpdate = true;
                        if (field && field.afterDraw && layers) {
                            var afterDraw = field.afterDraw(mapContext, type, graphic, uiField, map, _this.tabActive);
                            if (_util.isPromise(afterDraw)) {
                                _util.dataCallback(function () {
                                    _this.endDraw(mapContext, type, graphic, uiField, map, _this.editSaveReturn);
                                }, afterDraw);
                            }
                            else if (afterDraw) {
                                _this.endDraw(mapContext, type, graphic, uiField, map, _this.editSaveReturn);
                            }
                        }
                        else {
                            _this.endDraw(mapContext, type, graphic, uiField, map, _this.editSaveReturn);
                        }
                    });
                    if (!isUpdate) {
                        if (_this.editSaveReturn) {
                            map.activeDraw(false);
                        }
                        if (_this.tabActive && _this.editSaveReturn) {
                            mapContext.window['getWorkspace']().activeTab(_this.tabActive);
                        }
                    }
                });
            }
        };
        GeomField.prototype.activeDrawWithDrawTool = function (data, action, uiField) {
            var _this = this;
            var field = uiField.field;
            var drawOptions = { draw: { circle: true, rectangle: true, marker: true, polyline: true, polygon: true } };
            var drawType = field.gtype;
            if (drawType === 'point')
                drawType = 'marker';
            if (drawType) {
                drawOptions.draw[drawType] = true;
                for (var k in drawOptions)
                    if (k !== drawType)
                        drawOptions[k] = false;
                drawOptions.initMode = drawType;
            }
            var fo = field.mapDrawOptions;
            if (fo) {
                fo = _util.parsekv(fo);
                Object.assign(drawOptions, fo);
            }
            var mapContext = this.getMapContext();
            var map = mapContext.map;
            mapContext.window['activeMapWorkspace']();
            map.activeDrawSelect(false);
            mapContext.message(field.mapDrawMessage || '请选择绘制方式并在地图上进行绘制');
            maskModalsDimmer(true);
            _context.doAction({
                context: 'top',
                title: field.mapDrawTitle || field.mapDrawMessage || '请选择绘制方式并在地图上进行绘制',
                type: 'window', width: 400, contentUrl: 'modules/map/drawtool.html',
                drawOptions: drawOptions,
                cancelCallback: function () {
                    if (_this.tabActive) {
                        setTimeout(function () {
                            mapContext.window['getWorkspace']().activeTab(_this.tabActive);
                        }, 300);
                    }
                },
                callback: function (graphic, wkt, type) {
                    return new Promise(function (resolve, reject) {
                        var endReturn = !_this.noEndDraw;
                        map.enableEditHandler();
                        if (field && field.afterDraw) {
                            var afterDraw = field.afterDraw(mapContext, type, graphic, uiField, map, _this.tabActive);
                            if (_util.isPromise(afterDraw)) {
                                _util.dataCallback(function (e, isError) {
                                    if (!isError) {
                                        _this.endDraw(mapContext, type, graphic, uiField, map, endReturn);
                                        resolve(true);
                                    }
                                    else
                                        resolve(false);
                                }, afterDraw);
                            }
                            else if (afterDraw) {
                                _this.endDraw(mapContext, type, graphic, uiField, map, endReturn);
                                resolve(true);
                            }
                        }
                        else {
                            _this.endDraw(mapContext, type, graphic, uiField, map, endReturn);
                            resolve(true);
                        }
                    });
                }
            }, {});
        };
        GeomField.prototype.endDraw = function (mapContext, type, graphic, uiField, map, endReturn) {
            var _this = this;
            var mdis = mapContext.window._sui.minimizeAllMdiWindow();
            var geomVal = mapContext.map.getWkt(graphic);
            uiField.setValue(geomVal);
            if (endReturn) {
                map.enableEditHandler(false);
                map.activeDraw(false);
                map.activeEdit(false);
            }
            setTimeout(function () {
                mdis.each(function () {
                    this.__componentInstance.restore();
                });
                if (_this.tabActive && endReturn) {
                    mapContext.window['getWorkspace']().activeTab(_this.tabActive);
                }
                maskModalsDimmer(false);
            }, 300);
        };
        return GeomField;
    }(AbstractFormField));
    _sui.GeomField = GeomField;
    var DropdownField = (function (_super) {
        __extends(DropdownField, _super);
        function DropdownField() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.inputTagName = '';
            return _this;
        }
        DropdownField.prototype.doRender = function (el, pre) {
            var _this = this;
            _super.prototype.doRender.call(this, el, pre);
            if (!this.dropdownOptions) {
                this.dropdownOptions = {};
            }
            if (this.field.multiple) {
                this.dropdownOptions.multiple = this.field.multiple;
            }
            this.dropdown = this.createDropdown(el.parent(), pre, Object.assign({ allowEmpty: !this.field.required && !this.field.notShowEmptyItem }, this.dropdownOptions)).render();
            this.dropdown['fieldInstance'] = this;
            return this.dropdown.rendered().then(function () {
                if (_this.inline) {
                    _this.dropdown.getEl().css('flex-grow', 1);
                }
                _this.inputEl = el.parent().find('input');
                _this.dropdown.on('change', function (e, data) {
                    _this.value = data;
                    _this.trigger('change', data);
                });
            });
        };
        DropdownField.prototype.setValue = function (v, dirty) {
            if (dirty === void 0) { dirty = true; }
            this.dropdown.setValue(v);
            if (v && typeof v === 'object')
                v = v[this.dropdown.valueField];
            _super.prototype.setValue.call(this, v, dirty);
        };
        DropdownField.prototype.setReadOnly = function (v) {
            var _this = this;
            this.dropdown.rendered().then(function () {
                if (v) {
                    _this.dropdown.getEl().find('input').attr('readOnly', 'readOnly');
                    _this.dropdown.getEl().find('.icon.dropdown').css('pointer-events', 'none');
                    _this.dropdown.getEl().css('pointer-events', 'none');
                }
                else {
                    _this.dropdown.getEl().find('input').removeAttr('readOnly');
                    _this.dropdown.getEl().find('.icon.dropdown').css('pointer-events', 'auto');
                    _this.dropdown.getEl().css('pointer-events', 'auto');
                }
            });
        };
        DropdownField.prototype.getSelectedItem = function () {
            return this.dropdown ? this.dropdown.getSelectedItem() : null;
        };
        return DropdownField;
    }(AbstractFormField));
    _sui.DropdownField = DropdownField;
    var Form = (function (_super) {
        __extends(Form, _super);
        function Form(rows, parentEl, options) {
            if (options === void 0) { options = {}; }
            var _this = _super.call(this, parentEl, options) || this;
            _this.rows = rows;
            _this.template = '<div class="ui equal width form compact" style="padding:10px">';
            _this.uiFields = {};
            _this.fieldElByNames = {};
            _this.fieldDefs = {};
            _this.baseData = {};
            _this.execRuleAfterDataSet = false;
            _this.inlineFields = {};
            _this.disabled = false;
            _this.readOnly = false;
            _this.ignoreRequired = false;
            _this.ignoreScript = false;
            _this.applyDefaultIf = true;
            _this.dataLoaded = false;
            return _this;
        }
        Form.prototype.doRender = function (el, prepare) {
            var _this = this;
            _super.prototype.doRender.call(this, el, prepare);
            this.data = this.data || {};
            if (this.formClass)
                el.addClass(this.formClass);
            if (this.formStyle)
                setStyle(el, this.formStyle);
            var realRows = [];
            this.rows.forEach(function (row) {
                if (row.type === 'group') {
                    realRows.push({ type: 'divider', label: row.label });
                    row.fields.forEach(function (r) { return realRows.push(r); });
                }
                else
                    realRows.push(row);
            });
            var validateFields = {};
            realRows.forEach(function (row) {
                _this.appendRow(el, row, validateFields);
            });
            if (this.applyDefaultIf !== false) {
                var _loop_1 = function (k) {
                    var f = this_1.fieldDefs[k];
                    var v = f.value;
                    if (v === undefined)
                        v = f.defaultValue;
                    if (v === undefined)
                        v = f.default;
                    if (v !== undefined) {
                        var uf_1 = this_1.uiFields[k];
                        if (uf_1.rendered)
                            uf_1.rendered().then(function () {
                                uf_1.setValue(v);
                            });
                        else
                            uf_1.setValue(v);
                    }
                };
                var this_1 = this;
                for (var k in this.fieldDefs) {
                    _loop_1(k);
                }
            }
            if (this.script && !this.ignoreScript)
                this.rendered().then(function () {
                    var ev = function (code) {
                        if (_util.isPromise(code)) {
                            code.then(ev);
                            return;
                        }
                        code = code.trim();
                        if (!code)
                            return;
                        var form = _this;
                        eval(code);
                    };
                    if (Array.isArray(_this.script))
                        _this.script.forEach(ev);
                    else
                        ev(_this.script);
                });
            var vo = this.validateOption || {
                fields: validateFields, inline: true,
                onFailure: function (formErrors, fields) {
                    try {
                        var errEls = $(_this.templateEl).find('.field.error');
                        if (errEls && errEls.length > 0) {
                            var fixedHeight = 125;
                            var errorFieldElTop = $(errEls[0]).offset().top;
                            var scrollTop = $(_this.templateEl).parent().parent().parent().scrollTop();
                            if (errorFieldElTop > fixedHeight) {
                                $(_this.templateEl).parent().parent().parent().scrollTop(scrollTop + errorFieldElTop - fixedHeight);
                            }
                            else if (errorFieldElTop < fixedHeight && scrollTop > 0) {
                                $(_this.templateEl).parent().parent().parent().scrollTop(scrollTop + errorFieldElTop - fixedHeight);
                            }
                        }
                    }
                    catch (e) {
                        console.error(e);
                    }
                }
            };
            setTimeout(function () {
                _this.templateEl.form(vo);
            }, 200);
        };
        Form.prototype.remoteValidateByFields = function (fields) {
            var _this = this;
            if (fields === void 0) { fields = []; }
            if (fields && fields.length > 0) {
                setTimeout(function () {
                    try {
                        _this.templateEl.form('remove field', fields);
                    }
                    catch (e) {
                    }
                }, 300);
            }
        };
        Form.prototype.destroy = function () {
            for (var k in this.uiFields) {
                var v = this.uiFields[k];
                if (typeof v.destroy === 'function')
                    v.destroy();
            }
        };
        Form.prototype.appendRow = function (el, row, validateFields) {
            var _this = this;
            if (row.type === 'divider') {
                var d = $('<div class="ui horizontal divider">').appendTo(el);
                if (row.html)
                    d.html(row.html);
                else if (row.label)
                    d.text(row.label);
                return;
            }
            else if (row.type === 'label' && !row.getLabel) {
                var div = $('<div>').appendTo(el);
                render(div, row, { evalScope: { form: this, field: div } });
                return;
            }
            var fields = row;
            if (row.fields)
                fields = row.fields;
            fields = Array.isArray(fields) ? fields : [fields];
            if (this.fieldFilter)
                fields = fields.filter(this.fieldFilter);
            if (_sui.formFieldFilter)
                fields = fields.filter(_sui.formFieldFilter);
            if (!fields.length)
                return;
            var fieldsEl = $('<div class="fields">').appendTo(el);
            var inline = row.inline;
            if (inline === undefined)
                inline = this.inlineFields;
            if (inline) {
                fieldsEl.addClass('inline');
            }
            fields.forEach(function (f) {
                if (_this.fieldDecorator)
                    f = _this.fieldDecorator(f, _this);
                var fieldEl = $('<div class="field">').appendTo(fieldsEl);
                var rules = [];
                if (f.required && !_this.ignoreRequired) {
                    if (f.required_city && window['_context'] && window['_context']._currentUser) {
                        var shardingId = _context._currentUser.SHARDING_ID;
                        var requiredcitys = f.required_city.split(',');
                        if (requiredcitys.indexOf(shardingId.toString()) !== -1) {
                            fieldEl.addClass('required');
                            rules.push({ type: 'empty' });
                            if (window['_context'] && window['_context'].getSetting('formFieldRequiredWarn')) {
                                fieldEl.addClass('requiredwarn');
                            }
                        }
                    }
                    else {
                        fieldEl.addClass('required');
                        rules.push({ type: 'empty' });
                        if (window['_context'] && window['_context'].getSetting('formFieldRequiredWarn')) {
                            fieldEl.addClass('requiredwarn');
                        }
                    }
                }
                if (f.class)
                    fieldEl.addClass(f.class);
                if (f.style)
                    setStyle(fieldEl, f.style);
                if (inline)
                    fieldEl.css('display', 'flex');
                var uiField = _this.createField(f, fieldEl, inline);
                if (uiField && uiField.setForm)
                    uiField.setForm(_this);
                if (f.rules) {
                    var fr = f.rules;
                    if (typeof fr === 'string')
                        try {
                            fr = JSON.parse(fr);
                        }
                        catch (e) { }
                    if (fr) {
                        if (typeof fr === 'string')
                            fr = { type: fr };
                        if (Array.isArray(fr))
                            rules = rules.concat(fr);
                        else
                            rules.push(fr);
                    }
                }
                if (rules.length)
                    validateFields[f.name] = { rules: rules };
                var self = _this;
                function mf() {
                    if (f.disabled || self.disabled) {
                        fieldEl.addClass('disabled');
                        fieldEl.find('input').attr('readOnly', 'readOnly');
                    }
                    else if (f.readOnly || self.readOnly) {
                        if (uiField['setReadOnly'])
                            uiField['setReadOnly'](true);
                        else
                            fieldEl.find('input').attr('readOnly', 'readOnly');
                    }
                    if (f.inputListener && uiField['inputEl'])
                        for (var k in f.inputListener) {
                            uiField['inputEl'].on(k, undefined, uiField, f.inputListener[k]);
                        }
                    if (typeof f.onRender === 'function')
                        f.onRender(uiField, self, f);
                    else if (typeof f.onRender === 'string')
                        eval(f.onRender);
                }
                if (uiField instanceof Component)
                    uiField.render().rendered().then(function (e) { return mf(); });
                else
                    mf();
                if (f.name) {
                    if (uiField) {
                        _this.uiFields[f.name] = uiField;
                        _this.fieldDefs[f.name] = f;
                    }
                    _this.fieldElByNames[f.name] = fieldEl;
                }
                _this.dealFieldOnChange(f, uiField);
            });
        };
        Form.prototype.dealFieldOnChange = function (field, uiField) {
            var _this = this;
            if (uiField) {
                if (uiField.onChange)
                    uiField.onChange(function (e) {
                        var rule = field.onChange;
                        if (rule) {
                            if (!Array.isArray(rule))
                                rule = [rule];
                            rule.forEach(function (r) {
                                if (typeof r === 'function')
                                    r(uiField, _this, field);
                                else
                                    _this.execRule(r, uiField, field);
                            });
                        }
                        if (_this.dataLoaded)
                            _this.trigger('change', uiField);
                    });
            }
        };
        Form.prototype.execRule = function (rule, changingField, field) {
            var _this = this;
            rule.split(';').forEach(function (r) {
                if (r.indexOf('${') !== -1) {
                    var uifields = _this.uiFields;
                    var code = _util.replaceVar(r, function (key) {
                        return 'uifields["' + key + '"]';
                    });
                    var isDirty = uifields[field.name].isDirty();
                    eval(code);
                    return;
                }
                var nv = r.split('=');
                if (nv.length !== 2) {
                    console.log('invalid rule: ' + field);
                    return;
                }
                var value = _this.computeValueByExp(nv[1].trim());
                var name = nv[0];
                if (value !== undefined)
                    _util.dataCallback(function (v, err) {
                        if (err)
                            return console.log(err);
                        name.split(',').forEach(function (fieldName) {
                            fieldName = fieldName.trim();
                            var must = true;
                            if (fieldName.endsWith('?')) {
                                must = false;
                                fieldName = fieldName.substr(0, fieldName.length - 1);
                            }
                            var nameField = _this.getFormField(fieldName);
                            if (!nameField)
                                return;
                            if (changingField && changingField.getValue() === changingField.getInitValue() && nameField.getInitValue() && !nameField.isDirty()) {
                                must = false;
                            }
                            if (!must && nameField.getValue())
                                return;
                            nameField.setValue(v);
                        });
                    }, value);
            });
        };
        Form.prototype.getFormComputeContext = function () {
            return this.getData();
        };
        Form.prototype.computeValueByExp = function (exp) {
            var code = '', data = this.getFormComputeContext();
            for (var k in data)
                code += 'var ' + k.replace(/\./g, '_') + '=data["' + k + '"] || "";';
            var ret = eval(code + exp);
            return ret;
        };
        Form.prototype.getFormField = function (name) {
            return this.uiFields[name];
        };
        Form.prototype.getField = function (name) {
            return this.uiFields[name];
        };
        Form.prototype.setFieldValue = function (name, v) {
            this.getFormField(name).setValue(v);
        };
        Form.prototype.getFieldEl = function (name) {
            return this.fieldElByNames[name];
        };
        Form.prototype.createField = function (field, fieldEl, inline) {
            if (this.fieldFactory) {
                var f = this.fieldFactory(field, fieldEl, inline);
                if (f)
                    return f;
            }
            if (_sui.formFieldFactory[field.type]) {
                var f = _sui.formFieldFactory[field.type](field, fieldEl, inline);
                if (f)
                    return f;
            }
            if (field.inputType) {
                if (field.inputType === 'password')
                    return new PasswordField(field, fieldEl, { inputType: field.inputType, inline: inline });
                return new InputField(field, fieldEl, { inputType: field.inputType, inline: inline });
            }
            var type = field.formType || field.type;
            if (type === 'bool')
                return new BoolField(field, fieldEl, { inline: inline });
            if (type === 'date' || type === 'datetime')
                return new DateField(field, fieldEl, { inline: inline });
            if (type === 'number') {
                if (window['_context'] && _context.numberFieldAsText === true) {
                    field.rules = field.rules || { type: 'number' };
                    return new TextField(field, fieldEl, { inline: inline });
                }
                return new NumberField(field, fieldEl, { inline: inline });
            }
            if (type === 'html') {
                if (field.fn) {
                    field.fn(fieldEl, this);
                    return fieldEl;
                }
                return $(field.content).appendTo(fieldEl);
            }
            if (type === 'expression' && field.getLabel)
                return new ExpressionField(field, fieldEl, { inline: inline });
            if (type === 'label' && field.getLabel)
                return new LabelField(field, fieldEl, { inline: inline });
            if (type === 'textarea')
                return new TextAreaField(field, fieldEl, {});
            if (type === 'check')
                return new CheckField(field, fieldEl, { inline: inline });
            if (type === 'radio')
                return new RadioField(field, fieldEl, { inline: inline });
            if (type === 'search')
                return new SearchField(field, fieldEl, { inline: inline });
            if (type === 'geometry')
                return new GeomField(field, fieldEl, { inline: inline });
            if (type !== 'empty')
                return new TextField(field, fieldEl, { inline: inline });
        };
        Form.prototype.validate = function () {
            this.templateEl.form('validate form');
            var valid = this.templateEl.form('is valid');
            if (valid && this.validateFn)
                valid = this.validateFn(this.getData());
            return valid;
        };
        Form.prototype.getData = function (dirty) {
            if (dirty === void 0) { dirty = false; }
            var o = {};
            for (var k in this.uiFields) {
                var f = this.uiFields[k];
                if (f.isDirty && !f.isDirty() && dirty)
                    continue;
                if (f.getParentEl && f.getParentEl() && f.getParentEl().hasClass('disabled'))
                    continue;
                if (f.getEl && f.getEl() && f.getEl().hasClass('disabled'))
                    continue;
                if (f.getValue)
                    o[k] = f.getValue();
                else if (f.value && typeof f.value !== 'function')
                    o[k] = f.value;
            }
            Object.assign(o, this.baseData);
            return o;
        };
        Form.prototype.getValue = function (k) {
            var f = this.uiFields[k];
            if (f.getValue)
                return f.getValue();
            else if (f.value && typeof f.value !== 'function')
                return f.value;
        };
        Form.prototype.doSetData = function (data) {
            var _this = this;
            _super.prototype.doSetData.call(this, data);
            if (data) {
                var init_1 = data._init === true;
                if (init_1)
                    delete data._init;
                var _loop_2 = function (k) {
                    var f = this_2.uiFields[k];
                    var v = data[k + '_value'];
                    if (v === undefined)
                        v = data[k];
                    if (v === undefined) {
                        if (k.indexOf('.') !== -1) {
                            try {
                                v = eval('data.' + k);
                            }
                            catch (e) { }
                        }
                    }
                    if (typeof v !== 'undefined')
                        f.rendered().then(function () {
                            init_1 ? f.setInitValue(v) : f.setValue(v);
                        });
                };
                var this_2 = this;
                for (var k in this.uiFields) {
                    _loop_2(k);
                }
                if (!this.dataLoaded)
                    setTimeout(function () {
                        _this.dataLoaded = true;
                        _this.trigger('dataLoaded');
                    }, 1600);
            }
        };
        Form.prototype.setInitData = function (data) {
            if (data) {
                var _loop_3 = function (k) {
                    var f = this_3.uiFields[k];
                    if (data[k] !== undefined)
                        f.rendered().then(function () {
                            f.setInitValue(data[k]);
                        });
                };
                var this_3 = this;
                for (var k in this.uiFields) {
                    _loop_3(k);
                }
            }
        };
        Form.prototype.disable = function () {
            this.templateEl.find('.fields').addClass('disabled');
        };
        Form.prototype.enable = function () {
            this.templateEl.find('.fields').removeClass('disabled');
        };
        Form.prototype.onChange = function (fn) {
            this.on('change', fn);
        };
        Form.prototype.onInputChange = function (fn) {
            if (!this.uiFields) {
                return;
            }
            var _loop_4 = function (key) {
                var field = this_4.uiFields[key];
                if (field && field.inputEl) {
                    field.inputEl.on('input propertychange', function (e) {
                        field.value = field.getInputElValue();
                        fn(e, field);
                    });
                }
            };
            var this_4 = this;
            for (var key in this.uiFields) {
                _loop_4(key);
            }
        };
        Form.prototype.reset = function () {
            for (var k in this.uiFields) {
                var f = this.uiFields[k], initValue = f.getInitValue ? f.getInitValue() : undefined;
                f.setValue(initValue, false);
            }
        };
        Form.prototype.setBaseData = function (d) {
            d = d || {};
            this.baseData = d;
        };
        Form.prototype.setBaseDataValue = function (k, v) {
            if (v === undefined && typeof k === 'object') {
                for (var kk in k)
                    this.setBaseDataValue(kk, k[kk]);
                return;
            }
            this.baseData[k] = v;
        };
        return Form;
    }(Component));
    _sui.Form = Form;
    function form(parentEl, options) {
        if (options === void 0) { options = {}; }
        return new Form(options.rows || options.fields, parentEl, options).render();
    }
    _sui.form = form;
    var Dropdown = (function (_super) {
        __extends(Dropdown, _super);
        function Dropdown(parentEl, options) {
            if (options === void 0) { options = {}; }
            var _this = _super.call(this, parentEl, options) || this;
            _this.titleField = 'name';
            _this.valueField = 'id';
            _this.search = false;
            _this.iconField = false;
            _this.autoSeachDelayWhenShowByKey = 5000;
            _this.dropDownIcon = 'dropdown';
            _this.allowEmpty = true;
            _this.emptyValue = 'null';
            _this.autoLoad = false;
            _this.autoSelect = false;
            _this.clearable = false;
            _this.dataSort = false;
            _this.multiple = false;
            _this.template = "\n        <div class=\"ui selection dropdown _dropdown\">\n            <input type=\"hidden\">\n            <span class=\"_dp_extra_icons\">\n            </span>\n            <i class=\"icon _dp_icon\"></i>\n            <div class=\"_dp_stub\"></div>\n            <div class=\"default text _dp_text\"></div>\n            <div class=\"menu\"></div>\n        </div>\n        ";
            _this.loading = false;
            if (options.emptyText === undefined)
                _this.emptyText = _locale.emptyText;
            if (_this.emptyText === undefined)
                _this.emptyText = '[null]';
            if (_this.floatingMenu === undefined) {
                _this.floatingMenu = _context.getSetting('dropdownFloatingMenu');
            }
            if (_this.floatingMenu === undefined || _this.floatingMenu == null) {
                _this.floatingMenu = true;
            }
            _this.templateCss = options.templateCss;
            return _this;
        }
        Dropdown.prototype.doRender = function (el, pre) {
            var _this = this;
            var placeholder = this.placeholder;
            if (placeholder)
                el.children('.default.text').text(placeholder);
            if (this.multiple) {
                this.templateEl.addClass('multiple');
            }
            var text = el.find('.default.text'), search;
            if (this.search) {
                this.templateEl.addClass('search');
                search = $('<input class="search" autocomplete="off">').insertBefore(text).focus(function () {
                    if (!search.val() && text.text() && text.text() !== placeholder) {
                        search.val(text.text()).select();
                        text.text('');
                    }
                }).blur(function () {
                    if (!search.val())
                        _this.templateEl.dropdown('restore selected');
                });
            }
            var opt = Object.assign({}, this.getDefaultDropdownOptions(), this.dropdownOptions);
            if (this.inputName)
                el.find('input').attr('name', this.inputName);
            if (this.template) {
                this.templateEl.find('i.icon').addClass(this.dropDownIcon);
                this.templateEl.css('minWidth', '10em');
            }
            if (this.templateCss) {
                this.templateEl.css(this.templateCss);
            }
            this.menuEl = this.templateEl.find('.menu');
            this.templateEl.dropdown(opt);
            if (this.dropDownIcon !== 'dropdown') {
            }
            if (this.showByKey) {
                var compositing_1 = false;
                var keyWhich_1 = this.showByKey;
                if (keyWhich_1 === true)
                    keyWhich_1 = 13;
                var lastInput_1, inputTimer_1;
                var inputEl_1 = el.find('input.search');
                if (!inputEl_1.length)
                    inputEl_1 = el.find('input');
                inputEl_1.keypress(function (e) {
                    if (e.which === keyWhich_1) {
                        lastInput_1 = inputEl_1.val();
                        clearTimeout(inputTimer_1);
                        _this.templateEl.dropdown('filter');
                    }
                }).on('input', function () {
                    var v = inputEl_1.val();
                    if (v == lastInput_1)
                        return;
                    lastInput_1 = v;
                    clearTimeout(inputTimer_1);
                    if (v)
                        inputTimer_1 = setTimeout(function () {
                            if (compositing_1)
                                return;
                            _this.templateEl.dropdown('filter');
                        }, _this.autoSeachDelayWhenShowByKey);
                }).on('compositionstart', function () {
                    compositing_1 = true;
                }).on('compositionend', function () {
                    compositing_1 = false;
                    inputEl_1.trigger('input');
                });
            }
            if (!this.data && this.value)
                this.setValue(this.value);
            if (this.extraIconActions)
                this.extraIconActions.forEach(function (a) {
                    $('<i class="icon">').css({ cursor: 'pointer' }).addClass(a.icon).appendTo(el.find('._dp_extra_icons')).click(function () {
                        a.fn(_this);
                    });
                });
            if (this.autoSelect || this.autoLoad) {
                setTimeout(function () {
                    _this.templateEl.dropdown('queryRemote', '', function () {
                        var auto = _this.autoSelect;
                        if (auto) {
                            var data = _this._originRemoteData;
                            var firstRow = data[0], onlyRow = void 0;
                            if (data.length === 1)
                                onlyRow = data[0];
                            var toselect = void 0;
                            if (typeof auto === 'function')
                                toselect = auto(data, _this);
                            else if (auto === 'only')
                                toselect = onlyRow;
                            else if (auto === 'first')
                                toselect = firstRow;
                            else
                                toselect = firstRow;
                            if (toselect)
                                _this.setValue(toselect);
                        }
                    });
                }, 100);
            }
        };
        Dropdown.prototype.getEmptyItem = function () {
            var _a;
            if (!this.emptyItem)
                this.emptyItem = (_a = {}, _a[this.titleField] = this.emptyText, _a[this.valueField] = this.emptyValue, _a);
            return this.emptyItem;
        };
        Dropdown.prototype.doCreateItem = function (i) {
            var item = $('<div>').attr('data-value', i[this.valueField]);
            var label = this.getLabel(i);
            _util.isjquery(label) ? item.append(label) : item.text(label);
            if (this.iconField) {
                var icon = this.getIcon(i);
                if (typeof icon === 'string')
                    $('<i class="icon">').addClass(icon).prependTo(item);
                else if (icon && icon.prependTo)
                    icon.prependTo(item);
            }
            item.addClass(i._item_class || 'item');
            if (i.class)
                item.addClass(i.class);
            var itemTip = this.getItemTooltip(i);
            if (itemTip)
                item.attr('title', itemTip);
            return item;
        };
        Dropdown.prototype.getItemTooltip = function (item) {
            return item._tip;
        };
        Dropdown.prototype.doSetData = function (v) {
            var _this = this;
            v = v || [];
            v = Array.isArray(v) ? v : [v];
            if (this.isRendered) {
                this.menuEl.empty();
                if (this.allowEmpty) {
                    var ei = this.getEmptyItem();
                    this.menuEl.append($('<div class="item">').attr('data-value', ei[this.valueField]).text(this.getLabel(ei)));
                }
                v.forEach(function (i) {
                    var itemEl = _this.doCreateItem(i).appendTo(_this.menuEl);
                    if (_this.options.childrenMenu !== false && i.children && Array.isArray(i.children)) {
                        var im_1 = $('<div class="menu">').appendTo(itemEl);
                        $('<i class="dropdown icon">').appendTo(itemEl);
                        i.children.forEach(function (ci) {
                            _this.doCreateItem(ci).appendTo(im_1);
                        });
                    }
                });
                this.templateEl.dropdown('refresh');
            }
            _super.prototype.doSetData.call(this, v);
            if (this.value || this.value === 0) {
                if (this.templateEl) {
                    this.templateEl.dropdown('clear');
                    this.templateEl.find('input').val('');
                }
                this.setValue(this.value);
            }
        };
        Dropdown.prototype.getIcon = function (obj) {
            return this.iconField ? _util.fetchField(obj, this.iconField) : null;
        };
        Dropdown.prototype.getLabel = function (obj) {
            var label = obj[this.titleField];
            if (label === undefined || label === null)
                label = this.unknownTitle;
            if (label === undefined || label === null)
                label = _locale.unknownText;
            return label;
        };
        Dropdown.prototype.onShow = function () {
            if (!this.floatingMenu)
                return;
            var dp = this.templateEl.data('module-dropdown');
            var me = this.menuEl, mep = me.parent(), pos = mep.offset();
            var tmpd = this._tmpFloatingDropdownEl = $('<div class="compact">').css({ position: 'absolute' }).css({ zIndex: 99999, visibility: 'hidden', top: pos.top, left: pos.left, width: mep.css('width'), height: mep.css('height') });
            tmpd[0].className = mep[0].className;
            tmpd.addClass('compact');
            me.appendTo(tmpd);
            tmpd.appendTo(document.body);
            this.trigger('show', this);
        };
        Dropdown.prototype.onHide = function () {
            var _this = this;
            if (this._tmpFloatingDropdownEl) {
                this.menuEl.hide();
                setTimeout(function () {
                    _this.menuEl.appendTo(_this.templateEl);
                    if (_this._tmpFloatingDropdownEl) {
                        _this._tmpFloatingDropdownEl.remove();
                        _this._tmpFloatingDropdownEl = null;
                    }
                }, 260);
            }
            this.trigger('hide', this);
        };
        Dropdown.prototype.getDefaultDropdownOptions = function () {
            var _this = this;
            var o = {
                forceSelection: false,
                minCharacters: this.showByKey ? 999 : 0, clearable: this.clearable,
                delay: {
                    hide: 200,
                    show: 200,
                    search: 600,
                    touch: 50
                },
                floatingMenu: this.floatingMenu,
                onShow: function () { return _this.onShow(); }, onHide: function () { return _this.onHide(); },
                saveRemoteData: false, showNoResults: true, showOnFocus: false,
                fields: { remoteValues: 'data', name: this.titleField, value: this.valueField },
                onChange: function (v, t, c) {
                    _this.triggerChange(v, t, c);
                    var title = c && c.attr && c.attr('title');
                    _this.templateEl.attr('title', title || '');
                },
                apiSettings: {
                    responseAsync: function (settings, callback) {
                        if (_this.loading)
                            return;
                        _this.loading = true;
                        _util.dataCallback(function (ret, err) {
                            _this.loading = false;
                            if (err === true)
                                ret = [];
                            if (!ret.data)
                                ret = { data: ret };
                            _this._originRemoteData = ret.data;
                            if (!ret.data.length || _this.allowEmpty)
                                ret.data = ret.data.slice();
                            if (ret.data.length == 0) {
                                var ni = {};
                                ni[_this.titleField] = _locale.bean.noMatchItemText;
                                ni[_this.valueField] = 'category';
                                ret.data.push(ni);
                            }
                            var settingFields = _this.templateEl.dropdown('setting', 'fields');
                            if (settingFields)
                                settingFields.name = _this.titleField;
                            ret.data.forEach(function (item) {
                                if (item[_this.titleField] === undefined || item[_this.titleField] === null)
                                    item[_this.titleField] = _this.getLabel(item);
                            });
                            if (_this.dataSort) {
                                if (typeof _this.dataSort === 'function')
                                    ret.data.sort(_this.dataSort);
                                else
                                    ret.data.sort(function (a, b) {
                                        a = a[_this.titleField];
                                        b = b[_this.titleField];
                                        if (a == b)
                                            return 0;
                                        return a < b ? -1 : 1;
                                    });
                            }
                            if (_this.allowEmpty)
                                ret.data.splice(0, 0, _this.getEmptyItem());
                            callback(ret);
                            _this.data = ret.data;
                            setTimeout(function (e) {
                                _this.templateEl.find('.menu .item[data-value="category"]').removeClass('item').addClass('header').prepend($('<i class="tags icon"></i>'));
                                if (_this._tmpFloatingDropdownEl)
                                    _this._tmpFloatingDropdownEl.find('.menu .item[data-value="category"]').removeClass('item').addClass('header').prepend($('<i class="tags icon"></i>'));
                                var itemEl, itemTip, iconEl;
                                ret.data.forEach(function (d) {
                                    iconEl = _this.iconField ? _this.getIcon(d) : undefined;
                                    if (typeof iconEl === 'string')
                                        iconEl = $('<i class="icon">').addClass(iconEl);
                                    itemTip = _this.getItemTooltip(d);
                                    if (iconEl || itemTip) {
                                        itemEl = _this.templateEl.find('.menu .item[data-value="' + d[_this.valueField] + '"]');
                                        if (iconEl)
                                            itemEl.prepend(iconEl);
                                        if (itemTip)
                                            itemEl.attr('title', itemTip);
                                        if (_this._tmpFloatingDropdownEl) {
                                            itemEl = _this._tmpFloatingDropdownEl.find('.menu .item[data-value="' + d[_this.valueField] + '"]');
                                            if (iconEl)
                                                itemEl.prepend(iconEl);
                                            if (itemTip)
                                                itemEl.attr('title', itemTip);
                                        }
                                    }
                                });
                            }, 120);
                        }, _this.dataProvider, [settings]);
                    }
                }
            };
            if (this.dropDownIcon !== 'dropdown') {
                o.selector = { icon: '> .' + this.dropDownIcon + '.icon' };
            }
            return o;
        };
        Dropdown.prototype.triggerChange = function (value, text, c) {
            this.trigger('change', this.getValue());
        };
        Dropdown.prototype.clear = function () {
            this.value = undefined;
            if (this.data && this.dataProvider) {
                this.data = undefined;
            }
            if (!this.templateEl)
                return;
            this.templateEl.dropdown('clear');
            this.templateEl.children('.menu').empty();
            this.templateEl.find('input').val('');
        };
        Dropdown.prototype.fetchValueFromObject = function (v) {
            return v && typeof v === 'object' ? v[this.valueField] : v;
        };
        Dropdown.prototype.setValue = function (v) {
            var _this = this;
            if (Array.isArray(v) && v.length === 1)
                v = v[0];
            if (v && typeof v === 'object' && !Array.isArray(v)) {
                var exists = this.data && this.data.find(function (d) { return d[_this.valueField] == v[_this.valueField]; });
                if (!exists) {
                    this.value = v[this.valueField];
                    this.setData([v]);
                    return;
                }
            }
            if (Array.isArray(v))
                v = v.map(function (v1) {
                    return _this.fetchValueFromObject(v1);
                }).join(',');
            else
                v = this.fetchValueFromObject(v);
            this.value = v;
            if (this.templateEl) {
                var tv = this.value;
                if (tv === 0)
                    tv = '0';
                if (tv === null || tv === undefined)
                    this.clear();
                else {
                    this.templateEl.find('input').val('');
                    if (this.multiple && tv.split) {
                        tv = tv.split(',');
                    }
                    this.templateEl.dropdown('clear');
                    this.templateEl.dropdown('set selected', tv);
                }
            }
        };
        Dropdown.prototype.getValue = function () {
            if (!this.templateEl)
                return null;
            var v = this.templateEl.dropdown('get value');
            if (v === 'null' || v === this.emptyValue || v === this.emptyText)
                v = this.emptyValue;
            return v;
        };
        Dropdown.prototype.getSelectedItem = function (emptyAsNull) {
            var _this = this;
            if (emptyAsNull === void 0) { emptyAsNull = true; }
            var v = this.getValue();
            if (!v)
                return null;
            if (v === this.emptyValue && emptyAsNull)
                return null;
            if (this.data)
                return this.data.find(function (item) { return item[_this.valueField] == v; });
            return null;
        };
        Dropdown.prototype.setText = function (text) {
            if (this.templateEl)
                this.templateEl.dropdown('set text', text);
        };
        Dropdown.prototype.open = function (query) {
            if (query === void 0) { query = undefined; }
            if (this.templateEl) {
                this.templateEl.dropdown('filter', query);
                this.templateEl.dropdown('show');
            }
        };
        return Dropdown;
    }(Component));
    _sui.Dropdown = Dropdown;
    function combo(el, options) {
        return new Dropdown(el, options).render();
    }
    _sui.combo = combo;
    var Tabs = (function (_super) {
        __extends(Tabs, _super);
        function Tabs() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.template = null;
            _this.headerClass = '';
            _this.contentClass = 'bottom attached';
            _this.hideTitle = false;
            _this.defaultTabOptions = { childrenOnly: true };
            _this.tabItemTemplate = "<a class=\"item\">";
            _this.autoActive = true;
            _this.tabhistory = [];
            _this.showHeaderMenu = false;
            _this.dblClkClose = true;
            _this.headerMenuIcon = '<i class="small icon caret square down outline"></i>';
            return _this;
        }
        Tabs.prototype.prepare = function () {
            var thiz = this;
            this.tabOptions = Object.assign({
                onLoad: function () {
                    thiz.trigger('change');
                }
            }, this.defaultTabOptions, this.tabOptions);
        };
        Tabs.prototype.doRender = function (el, pre) {
            var _this = this;
            _super.prototype.doRender.call(this, el, pre);
            if (!this.headerEl) {
                this.headerEl = this.parentEl.children('.menu');
                if (!this.headerEl.length)
                    this.headerEl = $('<div class="ui menu">').prependTo(this.parentEl);
                if (this.headerClass)
                    this.headerEl.addClass(this.headerClass);
                this.headerEl.css({ flexFlow: 'wrap', width: '100%' });
                if (this.headerCss)
                    this.headerEl.css(this.headerCss);
                if (this.showHeaderMenu) {
                    var dropdown = $('<div class="">').css({ position: 'absolute', zIndex: 100 }).appendTo(this.headerEl);
                    var icon_1 = $(this.headerMenuIcon).appendTo(dropdown).hide();
                    this.headerMenu = $('<div class="ui basic popup">').css({ margin: 0, padding: '5px 2px 2px 5px', overflow: 'hidden auto' }).appendTo(this.headerEl);
                    this.headerMenu = $('<div>').css({ overflow: 'hidden', paddingLeft: '5px', fontSize: 'smaller' }).appendTo(this.headerMenu);
                    dropdown.popup({ on: 'click', hoverable: true, position: 'right center', lastResort: true });
                    this.headerEl.hover(function (e) { return icon_1.show(); }, function (e) { return icon_1.hide(); });
                    this.addHeaderItem(null, _locale.sui.tabs.closeAll, false).click(function (e) { return _this.clearTabs(true); });
                }
            }
            if (!this.contentEl)
                this.contentEl = this.parentEl;
            this.tabOptions.context = this.contentEl;
            this.headerEl.find('*[data-tab]').tab(this.tabOptions);
            this.setTitleVisible();
        };
        Tabs.prototype.fixTabName = function (name) {
            return name.replace('/', '_');
        };
        Tabs.prototype.hasTab = function (name) {
            return this.headerEl.children('.item[data-tab="' + this.fixTabName(name) + '"]').length > 0;
        };
        Tabs.prototype.setTitleVisible = function () {
            var hide = this.hideTitle;
            if (!hide)
                return;
            if (hide === 'single')
                hide = this.headerEl.children('.item').length <= 1;
            hide ? this.headerEl.hide() : this.headerEl.show();
            if (_util.isSafari()) {
                var height = this.contentEl.height();
                if (!hide)
                    height -= 40;
                this.contentEl.children('.ui.tab.segment').height(height);
            }
        };
        Tabs.prototype.addTab = function (name, content, options, index) {
            var _this = this;
            if (options === void 0) { options = {}; }
            if (index === void 0) { index = undefined; }
            var closable = options.closable;
            if (closable === undefined)
                closable = true;
            var active = options.active;
            if (active === undefined)
                active = this.autoActive;
            var title = options.title || options.label || name;
            if (this.hasTab(name)) {
                this.activeTab(name);
                return;
            }
            active = active || this.headerEl.children('[data-tab]').length === 0;
            content = $(content);
            content.addClass('ui tab').attr('data-tab', this.fixTabName(name)).appendTo(this.contentEl);
            content.css('flex-grow', '1');
            if (this.contentClass)
                content.addClass(this.contentClass);
            if (this.contentCss)
                content.css(this.contentCss);
            var tabTitle = title || name;
            tabTitle = _util.xssUtil.filterXSS(tabTitle);
            var nameDiv = _util.isjquery(tabTitle) ? tabTitle : $('<label>').html(tabTitle);
            if (typeof tabTitle === 'string')
                nameDiv.attr('title', tabTitle);
            var tab = $(this.tabItemTemplate).attr('data-tab', this.fixTabName(name)).html(nameDiv).tab(Object.assign({}, this.tabOptions, options.tabOptions));
            tab.css({ position: 'relative' });
            var tabs = this.headerEl.children('[data-tab]');
            if (index === undefined)
                index = options.index;
            if (tabs.length && index !== undefined) {
                if (index < 0)
                    index = tabs.length + index;
                this.getTabHead(index).before(tab);
            }
            else
                tab.appendTo(this.headerEl);
            if (closable) {
                var c_1 = $('<i class="x icon">').appendTo(tab).css({ visibility: 'hidden', position: 'absolute', right: '0px' });
                tab.hover(function (e) { return c_1.css({ visibility: 'visible' }); }, function (e) { return c_1.css({ visibility: 'hidden' }); });
                c_1.click(function (e) { return _this.removeTab(name); });
                c_1.hover(function (e) { return c_1.addClass('red'); }, function (e) { return c_1.removeClass('red'); });
                if (this.dblClkClose)
                    tab.dblclick(function (e) { return _this.removeTab(name); });
            }
            if (active)
                this.activeTab(name);
            this.setTitleVisible();
            if (this.showHeaderMenu && this.headerMenu) {
                this.addHeaderItem(name, tabTitle, closable);
            }
        };
        Tabs.prototype.addHeaderItem = function (name, title, closable) {
            var self = this;
            var item = $('<a class="item">').css({ 'min-width': '10em', 'padding-right': '30px', padding: '.7em' }).html(title).appendTo(this.headerMenu).attr('data-item', name).click(function () {
                if (name)
                    self.activeTab(name);
            });
            if (closable) {
                $('<i class="x icon">').css({ position: 'absolute', right: '0' }).appendTo(item).click(function () {
                    self.removeTab(name);
                    return false;
                });
            }
            return item;
        };
        Tabs.prototype.removeTab = function (name, force) {
            if (force === void 0) { force = true; }
            var head = this.getTabHead(name);
            if (!force && head.find('i.icon.remove').length === 0)
                return;
            var active = head.hasClass('active');
            head.remove();
            this.getTabContent(name).remove();
            if (typeof name === 'number')
                name = head.data('tab');
            this.tabhistory = this.tabhistory.filter(function (t) { return t !== name; });
            if (active)
                this.activeLastTab();
            this.setTitleVisible();
            if (this.showHeaderMenu && this.headerMenu) {
                this.headerMenu.children('.item[data-item="' + name + '"]').remove();
            }
        };
        Tabs.prototype.activeLastTab = function () {
            if (this.tabhistory.length) {
                this.activeTab(this.tabhistory.pop());
            }
            else {
                var current = this.getActiveTabName();
                if (!current)
                    this.activeTab(0);
            }
        };
        Tabs.prototype.clearTabs = function (closableOnly) {
            var _this = this;
            if (closableOnly === void 0) { closableOnly = false; }
            var titles = [];
            this.headerEl.children('[data-tab]').each(function (i, item) { return titles.push($(item).attr('data-tab')); });
            titles.forEach(function (t) { return _this.removeTab(t, !closableOnly); });
        };
        Tabs.prototype.activeTab = function (name, active) {
            if (active === void 0) { active = true; }
            var tabs = this.headerEl.children('[data-tab]');
            var head;
            if (active) {
                head = this.getTabHead(name);
            }
            if (!head.length)
                head = this.getTabHead(0);
            if (head.hasClass('active'))
                return;
            head.trigger('click');
            this.tabhistory.push(head.data('tab'));
        };
        Tabs.prototype.getActiveTabName = function () {
            return this.headerEl.children('.active[data-tab]').attr('data-tab');
        };
        Tabs.prototype.getTabLength = function () {
            return this.headerEl.children('[data-tab]').length;
        };
        Tabs.prototype.getActiveTabIndex = function () {
            return this.headerEl.children('[data-tab]').index(this.headerEl.children('.active[data-tab]'));
        };
        Tabs.prototype.getTabHead = function (name) {
            if (typeof name === 'string')
                name = this.fixTabName(name);
            return typeof name === 'string' ? this.headerEl.children('[data-tab="' + name + '"]') : this.headerEl.children('[data-tab]').eq(name);
        };
        Tabs.prototype.getTabContent = function (name) {
            if (typeof name === 'string')
                name = this.fixTabName(name);
            return typeof name === 'string' ? this.contentEl.children('.tab[data-tab="' + name + '"]') : this.contentEl.children('.tab').eq(name);
        };
        return Tabs;
    }(Component));
    _sui.Tabs = Tabs;
    function tabs(el, options) {
        if (options === void 0) { options = null; }
        return new Tabs(el, options).render();
    }
    _sui.tabs = tabs;
    var StepTabs = (function (_super) {
        __extends(StepTabs, _super);
        function StepTabs() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.template = '<div style="display:flex;flex-direction:column;height:100%"></div>';
            _this.stepTemplate = "<i class=\"icon\"></i><div class=\"content\"><div class=\"title\"></div><div class=\"description\"></div></div>";
            _this.stepOptions = {
                tabItemTemplate: '<a class="step">',
                headerClass: 'steps mini',
                headerCcs: { flex: 'none' },
                contentCcs: { flex: 'auto' }
            };
            _this.showActionBar = false;
            return _this;
        }
        StepTabs.prototype.doRender = function (el, pre) {
            var _this = this;
            _super.prototype.doRender.call(this, el, pre);
            this.tabsInstance = tabs(el, this.stepOptions);
            if (this.showActionBar !== false)
                this.tabsInstance.rendered().then(function () {
                    var bar = _this.actionbar = $('<div class="buttons ui mini">').css({ order: 10, textAlign: 'center', background: 'yellow', flex: 'none', width: '100%' }).appendTo(el);
                    $('<div class="ui button">').text('上一步').appendTo(bar).click(function () { return _this.doPreStep(); });
                    $('<div class="ui button">').text('下一步').appendTo(bar).click(function () { return _this.doNextStep(); });
                    $('<div class="ui button">').text('完成').appendTo(bar).click(function () { return _this.doFinish(); });
                });
        };
        StepTabs.prototype.doNextStep = function () {
            var total = this.tabsInstance.getTabLength(), idx = this.tabsInstance.getActiveTabIndex();
            if (idx < total - 1)
                this.tabsInstance.activeTab(idx + 1);
        };
        StepTabs.prototype.doPreStep = function () {
            var total = this.tabsInstance.getTabLength(), idx = this.tabsInstance.getActiveTabIndex();
            if (idx > 0)
                this.tabsInstance.activeTab(idx - 1);
        };
        StepTabs.prototype.doFinish = function () {
            debugger;
        };
        StepTabs.prototype.rendered = function () {
            var _this = this;
            return _super.prototype.rendered.call(this).then(function () { return _this.tabsInstance.rendered(); });
        };
        StepTabs.prototype.addStep = function (iconClass, title, content, description) {
            var _this = this;
            if (description === void 0) { description = null; }
            this.rendered().then(function () {
                var t = $(_this.stepTemplate);
                var iconEl = t.filter('.icon');
                if (!iconEl.length)
                    iconEl = t.find('.icon');
                iconEl.addClass(iconClass);
                t.find('.title').html(title);
                t.find('.description').html(description);
                _this.tabsInstance.addTab(title, content, { closable: false, active: false, title: t });
            });
        };
        return StepTabs;
    }(Component));
    _sui.StepTabs = StepTabs;
    function stepTabs(el, options) {
        if (options === void 0) { options = null; }
        return new StepTabs(el, options).render();
    }
    _sui.stepTabs = stepTabs;
    var List = (function (_super) {
        __extends(List, _super);
        function List() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.titleField = ['title', 'label', 'name', 'NAME'];
            _this.descriptionField = 'description';
            _this.iconField = 'icon';
            _this.nodeTemplate = "\n            <div class=\"item\">\n                <i class=\"icon\"></i>\n                <div class=\"content\">\n                    <div class=\"header\"></div>\n                    <div class=\"description\"></div>\n                </div>\n            </div>\n        ";
            _this.template = "\n            <div class=\"ui list\">\n        ";
            return _this;
        }
        List.prototype.doSetData = function (v) {
            v = v || {};
            v = Array.isArray(v) ? v : [v];
            this.templateEl.empty();
            this.appendNodes(v, this.templateEl);
            _super.prototype.doSetData.call(this, v);
        };
        List.prototype.appendNodes = function (ch, parentNodeEl) {
            var _this = this;
            ch.forEach(function (item) {
                _this.appendNode(item, parentNodeEl);
            });
        };
        List.prototype.appendNode = function (item, parentNodeEl) {
            var _this = this;
            var node = $(this.nodeTemplate);
            _util.appendDataCallback(node.find('.header'), this.getTitle(item));
            _util.appendDataCallback(node.find('.description'), this.getDescription(item));
            var icon = this.getIcon(item);
            if (typeof icon === 'string')
                node.find('.icon').first().addClass(icon);
            else if (icon)
                node.find('.icon').first().append(icon);
            node.appendTo(parentNodeEl);
            if (item.children && item.children.length) {
                var p = $(this.template).appendTo(node.find('.content'));
                this.appendNodes(item.children, p);
            }
            if (this.hoverListener || this.hoverOutListener) {
                node.hover(function (e) {
                    if (_this.hoverListener)
                        _this.hoverListener(item, node);
                }, function (e) {
                    if (_this.hoverOutListener)
                        _this.hoverOutListener(item, node);
                });
            }
        };
        List.prototype.getTitle = function (item) {
            var _this = this;
            var title = _util.fetchField(item, this.titleField);
            var link = item.action || this.itemAction;
            if (link) {
                title = $('<span>').css({ cursor: 'pointer' }).safeHtml(title).click(function (e) {
                    return _context.doAction(link, item, _this, title);
                });
            }
            return title;
        };
        List.prototype.getDescription = function (item) {
            return _util.fetchField(item, this.descriptionField);
        };
        List.prototype.getIcon = function (item) {
            return _util.fetchField(item, this.iconField);
        };
        return List;
    }(Component));
    _sui.List = List;
    function list(parentEl, options) {
        return new List(parentEl, options).render();
    }
    _sui.list = list;
    var Tree = (function (_super) {
        __extends(Tree, _super);
        function Tree() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.nodeTemplate = "\n            <div class=\"title _suitree-title\" style=\"margin:0;\">\n                <i class=\"icon\"></i>\n                <span class=\"_label\"></span>\n                <span class=\"_bar\" style=\"margin-left:10px;font-size:smaller;\"></span>\n            </div>\n            <div class=\"content _node_content\"></div>\n        ";
            _this.template = "\n            <div class=\"ui accordion _suitree\">\n        ";
            _this.iconTrigger = undefined;
            _this.actionHide = true;
            _this.lockActionHide = false;
            _this.loadLevel = 0;
            _this.enableRefreshNodeAction = false;
            _this.autoCheck = true;
            _this.loadMoreText = '......';
            _this.loadMoreCount = 50;
            _this.defaultRefreshNodeParams = { _cache: false };
            _this.draggable = false;
            _this.dragOverClass = 'sui-dragover-item';
            return _this;
        }
        Tree.prototype.doRender = function (el, pre) {
            _super.prototype.doRender.call(this, el, pre);
            if (this.scrollTransition === undefined) {
                this.scrollTransition = { animation: 'glow', completeClass: 'scroll-heilight-item' };
                if (window['_context'] && window['_context'].getSetting('scrollTransition'))
                    this.scrollTransition = window['_context'].getSetting('scrollTransition');
            }
            var self = this;
            var opts = {
                animateChildren: false,
                exclusive: false, onOpening: function () {
                    if (thiz.isLeafNode(this.data('userdata')))
                        return false;
                    if (this.data('childrenLoaded'))
                        return;
                    thiz.loadChildren(this.data('userdata'), this);
                }, onOpen: function () {
                    self.trigger('open', this);
                }
            };
            if (this.iconTrigger)
                opts.selector = { trigger: '.title>.icon:first-child' };
            else
                opts.selector = { trigger: '.title>.icon:first-child,.title>._label' };
            var thiz = this;
            this.templateEl.accordion(opts);
        };
        Tree.prototype.doSetData = function (v) {
            var _this = this;
            _super.prototype.doSetData.call(this, v);
            if (this.loadLevel) {
                if (this.data)
                    this.data.forEach(function (n) { return _this.openInLevel(n, _this.loadLevel); });
            }
        };
        Tree.prototype.openInLevel = function (node, level) {
            var _this = this;
            this.open(node).then(function (ch) {
                if (--level > 0 && ch && ch.length)
                    ch.forEach(function (c) { return _this.openInLevel(c, level); });
            }, function (e) { });
        };
        Tree.prototype.openIf = function (nodeFn, nodes, justLoadChildren) {
            var _this = this;
            if (nodes === void 0) { nodes = null; }
            if (justLoadChildren === void 0) { justLoadChildren = false; }
            nodes = nodes || this.data;
            nodes = nodes.filter(nodeFn);
            var ch = [];
            return Promise.all(nodes.map(function (n) {
                return _this.open(n, false, undefined, undefined, justLoadChildren).then(function (childs) {
                    ch.push.apply(ch, childs);
                });
            })).then(function () {
                if (ch.length)
                    return _this.openIf(nodeFn, ch, justLoadChildren);
                return Promise.resolve();
            });
        };
        Tree.prototype.loadIf = function (nodeFn, nodes) {
            if (nodes === void 0) { nodes = undefined; }
            return this.openIf(nodeFn, nodes, true);
        };
        Tree.prototype.findContentNode = function (node, parentEl, expandLoadMore) {
            var _this = this;
            if (parentEl === void 0) { parentEl = undefined; }
            if (expandLoadMore === void 0) { expandLoadMore = false; }
            var index = -1, contentEl;
            if (expandLoadMore) {
                var havingmore = (parentEl || this.templateEl).find('.node_load_more');
                if (havingmore.length)
                    havingmore.click();
            }
            (parentEl || this.templateEl).find('._node_content.content').each(function (i, item) {
                contentEl = $(item);
                var d = contentEl.data('userdata');
                if (d && _this.equalsNode(d, node)) {
                    index = i;
                    return false;
                }
            });
            if (parentEl && index !== -1) {
                this.templateEl.find('._node_content.content').each(function (i, item) {
                    if (item == contentEl[0]) {
                        index = i;
                        return false;
                    }
                });
            }
            if (index === -1 && !expandLoadMore) {
                return this.findContentNode(node, parentEl, true);
            }
            return [index, contentEl];
        };
        Tree.prototype.refreshNode = function (node, contentEl, justSelfNode) {
            if (contentEl === void 0) { contentEl = null; }
            if (justSelfNode === void 0) { justSelfNode = false; }
            if (typeof contentEl === 'boolean') {
                justSelfNode = contentEl;
                contentEl = null;
            }
            contentEl = contentEl || this.findContentNode(node)[1];
            if (!contentEl || !contentEl.length)
                return;
            var nodeEl = contentEl.prev('.title'), data = contentEl.data('userdata');
            this.redrawNodeTitle(nodeEl, data);
            if (!justSelfNode)
                this.clearNodeChilden(node, contentEl, true, this.defaultRefreshNodeParams);
            return contentEl;
        };
        Tree.prototype.clearNodeChilden = function (node, contentEl, force, reload) {
            if (contentEl === void 0) { contentEl = null; }
            if (force === void 0) { force = true; }
            if (reload === void 0) { reload = false; }
            if (!contentEl)
                contentEl = this.findContentNode(node)[1];
            node = node || contentEl.data('userdata');
            if (node && contentEl) {
                contentEl.children('.accordion').remove();
                var chloaded = contentEl.data('childrenLoaded');
                if (node.forceChildrenLoaded) {
                    chloaded = node.forceChildrenLoaded;
                }
                if (chloaded) {
                    contentEl.data('childrenLoaded', null);
                    if (force)
                        contentEl.data('children', null);
                    if (reload)
                        this.loadChildren(node, contentEl, typeof reload === 'object' ? reload : undefined);
                }
            }
        };
        Tree.prototype.isLeafNode = function (node) {
            return node && node._isLeaf === true;
        };
        Tree.prototype.getLeafIcon = function (node) {
            return '';
        };
        Tree.prototype.closeNode = function (item) {
            var idx = this.findContentNode(item)[0];
            if (idx !== -1)
                this.templateEl.accordion('close', idx);
        };
        Tree.prototype.open = function (node, scroll, children, nodeParentEl, justLoadChildren) {
            var _this = this;
            if (scroll === void 0) { scroll = false; }
            if (children === void 0) { children = undefined; }
            if (nodeParentEl === void 0) { nodeParentEl = undefined; }
            if (justLoadChildren === void 0) { justLoadChildren = false; }
            if (this.isLeafNode(node))
                return Promise.reject(null);
            var _a = this.findContentNode(node, nodeParentEl), index = _a[0], contentEl = _a[1];
            if (index === -1) {
            }
            if (scroll && contentEl)
                _util.scrollToIf(contentEl.prev('.title'), this.parentEl);
            return new Promise(function (resolve, reject) {
                if (index === -1)
                    reject();
                else {
                    if (typeof children === 'function') {
                        var alreadyCh = _this.getChildren(node, contentEl);
                        children = children(alreadyCh);
                    }
                    if (children) {
                        if (Array.isArray(children))
                            contentEl.data('children', children);
                        _this.clearNodeChilden(node, contentEl, !Array.isArray(children), true);
                    }
                    if (justLoadChildren) {
                        _this.loadChildren(node, contentEl);
                    }
                    else
                        _this.templateEl.accordion('open', index);
                    var trig_1 = function () {
                        resolve(_this.getChildren(node));
                    };
                    if (contentEl.data('childrenLoaded'))
                        trig_1();
                    else {
                        contentEl.one('childrenLoaded', function (e) {
                            trig_1();
                        });
                    }
                }
            });
        };
        Tree.prototype.openAll = function (nodes) {
            var _this = this;
            if (nodes === void 0) { nodes = null; }
            nodes = nodes || this.data;
            nodes.forEach(function (node) { return _this.open(node).then(function (children) {
                if (children && children.length)
                    _this.openAll(children);
            }, function () { }); });
        };
        Tree.prototype.scrollTo = function (node) {
            var _a = this.findContentNode(node), index = _a[0], contentEl = _a[1];
            if (this.scrollTransition && this.scrollTransition.completeClass)
                this.getEl().find('.title').removeClass(this.scrollTransition.completeClass);
            _util.scrollToIf(contentEl.prev('.title'), this.parentEl, this.scrollTransition);
        };
        Tree.prototype.locate = function (path, scrollTo, loadChildren, nodeParentEl) {
            var _this = this;
            if (scrollTo === void 0) { scrollTo = true; }
            if (loadChildren === void 0) { loadChildren = true; }
            if (nodeParentEl === void 0) { nodeParentEl = undefined; }
            path = path.slice();
            if (path.length !== 1) {
                var node_1 = path.shift(), nextNode_1 = path[0];
                this._lookingupNode = nextNode_1;
                var needReloadChildren = function (alreadyChildren) {
                    if (alreadyChildren)
                        return !alreadyChildren.find(function (c) { return _this.equalsNode(c, nextNode_1); });
                };
                return this.open(node_1, false, loadChildren ? needReloadChildren : [path[0]], nodeParentEl).then(function (e) {
                    var expandLoadMore = false;
                    var _a = _this.findContentNode(node_1, nodeParentEl, expandLoadMore), index = _a[0], contentEl = _a[1];
                    if (index !== -1)
                        nodeParentEl = contentEl;
                    return _this.locate(path, scrollTo, loadChildren, nodeParentEl);
                });
            }
            else
                return new Promise(function (resolve, reject) {
                    if (scrollTo)
                        setTimeout(function () {
                            _this.scrollTo(path[0]);
                        }, 600);
                    resolve(null);
                    _this._lookingupNode = undefined;
                });
        };
        Tree.prototype.equalsNode = function (o1, o2) {
            if (o1 === o2)
                return true;
            var id1 = o1.id || o1, id2 = o2.id || o2;
            return id1 == id2;
        };
        Tree.prototype.genLoadMoreDataIf = function (data, childrenEl) {
            var _this = this;
            var moreCnt = this.loadMoreCount;
            var allDataLength = data.length;
            if (moreCnt && allDataLength > moreCnt) {
                if (this._lookingupNode) {
                    var pos = data.findIndex(function (d) { return _this.equalsNode(_this._lookingupNode, d); });
                    if (pos >= moreCnt) {
                        moreCnt = Math.ceil((pos + 1) / moreCnt) * this.loadMoreCount;
                    }
                }
                var rest = data.slice(moreCnt);
                data = data.slice(0, moreCnt);
                if (allDataLength > moreCnt) {
                    var loadAll = {
                        __toAppendData: rest, childrenEl: childrenEl
                    };
                    data.push(loadAll);
                }
            }
            return data;
        };
        Tree.prototype.reload = function () {
            this.redraw(true);
        };
        Tree.prototype.redraw = function (newData) {
            if (newData === true)
                newData = this.data;
            if (newData) {
                _super.prototype.redraw.call(this, newData);
                return;
            }
            var self = this;
            this.templateEl.find('._node_content.content').each(function () {
                var contentEl = $(this);
                var titleEl = contentEl.prev('.title'), data = contentEl.data('userdata');
                self.redrawNodeTitle(titleEl, data);
            });
        };
        Tree.prototype.redrawNodeTitle = function (node, item) {
            var _this = this;
            if (item === undefined) {
                var contentEl = node.filter('._node_content.content');
                item = contentEl.data('userdata');
            }
            if (item === undefined || item === null)
                return;
            var titleEl = node.filter('._suitree-title');
            if (item.__toAppendData) {
                titleEl.children('._label').addClass('node_load_more').text(this.loadMoreText).css({ fontWeight: 'bold', fontSize: 'smaller' }).click(function () {
                    var itemContentEl = _this.findContentNode(item, item.childrenEl)[1], itemTitleEl = itemContentEl.prev();
                    itemContentEl.remove();
                    itemTitleEl.remove();
                    var data = _this.genLoadMoreDataIf(item.__toAppendData, item.childrenEl);
                    _this.appendNodes(data, item.childrenEl);
                });
            }
            else
                titleEl.children('._label').safeHtml(this.getTitle(item));
            titleEl.children('._label').attr('title', titleEl.find('._label > b').text());
            if (!item.__toAppendData) {
                var bar_1 = titleEl.find('._bar').empty();
                if (this.actionHide || this.hoverListener) {
                    if (this.actionHide)
                        bar_1.hide();
                    titleEl.hover(function (e) {
                        if (!_this.lockActionHide)
                            bar_1.show();
                        if (_this.hoverListener)
                            _this.hoverListener(item, titleEl, bar_1);
                    }, function (e) {
                        if (_this.actionHide && !_this.lockActionHide)
                            bar_1.hide();
                        if (_this.hoverOutListener)
                            _this.hoverOutListener(item, titleEl, bar_1);
                    });
                }
                var actions = this.getActions(item);
                if (actions && !Array.isArray(actions))
                    actions = [actions];
                if (actions && actions.length) {
                    actions.forEach(function (a) {
                        var self = _this;
                        function da(a) {
                            if (typeof a === 'function')
                                a = a(item);
                            if (a && hasPermission(a)) {
                                var i_1;
                                if (_util.isjquery(a))
                                    i_1 = a;
                                else {
                                    i_1 = a.icon ? $('<i class="icon">') : $('<a>').text(a.label || a.title);
                                    i_1.attr('title', a.tooltip || a.label || a.title).addClass(a.icon).click(function (e) {
                                        self.doAction(a, item, i_1);
                                        return false;
                                    });
                                }
                                i_1.appendTo(bar_1);
                            }
                        }
                        if (_util.isPromise(a))
                            a.then(da);
                        else
                            da(a);
                    });
                }
            }
        };
        Tree.prototype.getNodeClass = function (item) {
            return item._nodeClass;
        };
        Tree.prototype.isDraggableNode = function (item, node) {
            return this.draggable && item._draggable !== false;
        };
        Tree.prototype.isDroppableNode = function (item, node) {
            return true;
        };
        Tree.prototype.appendNode = function (item, el) {
            var node = $(this.nodeTemplate).appendTo(el);
            this.redrawNodeTitle(node, item);
            var contentEl = node.filter('._node_content.content');
            contentEl.data('userdata', item);
            contentEl.css('padding', '0 0 0 1.5em');
            var desc = this.getDescription(item);
            if (desc) {
                _util.appendDataCallback($('<p>').appendTo(contentEl), desc);
            }
            var isLeaf = this.isLeafNode(item);
            var icon, cls;
            if (!item.__toAppendData) {
                icon = this.getIcon(item);
                if (!icon && icon !== false) {
                    if (isLeaf)
                        icon = this.getLeafIcon(item);
                    else
                        icon = 'dropdown';
                }
                cls = this.getNodeClass(item);
            }
            var openIcon;
            if (icon) {
                openIcon = node.find('.icon').first();
                openIcon.addClass(icon);
            }
            if (cls) {
                contentEl.addClass(cls);
                contentEl.prev('.title').addClass(cls);
            }
            this.trigger('nodeAppended', { el: node, data: item });
            if (openIcon && !isLeaf && this.hideOpenIconFn) {
                openIcon.removeClass(icon);
                openIcon.addClass('spinner loading');
                _util.dataCallback(function (hide) {
                    openIcon.removeClass('spinner loading');
                    if (hide === true) {
                        node.children = [];
                    }
                    else {
                        openIcon.addClass(icon);
                    }
                }, this.hideOpenIconFn, [item, node, this]);
            }
            if (this.isDraggableNode(item, node)) {
                if (!this._draggingOverItem) {
                    this._draggingOverItem = $('<div>').addClass(this.dragOverClass);
                }
                node.attr('draggable', true);
                var titleEl_1 = node.filter('._suitree-title');
                var self_2 = this, domNode = titleEl_1[0];
                domNode.ondragstart = function () {
                    self_2._draggingNode = node;
                    self_2._draggingData = item;
                };
                domNode.ondrop = function (e) {
                    e.preventDefault();
                    self_2._draggingOverItem.remove();
                    if (self_2._draggingNode && self_2._draggingNode !== node) {
                        contentEl.after(self_2._draggingNode);
                        var parentNode = self_2._draggingNode.closest('._node_content'), parentData = parentNode.data('userdata');
                        self_2.trigger('nodeDroped', { node: self_2._draggingNode, data: self_2._draggingData, preNode: node, preData: item, parentData: parentData });
                        self_2._draggingNode.filter('._suitree-title').transition({ animation: 'glow' });
                        self_2._draggingNode.filter('._node_content').transition({ animation: 'glow' });
                        self_2._draggingNode = null;
                        self_2._draggingData = null;
                    }
                };
                domNode.ondragover = function (e) {
                    e.preventDefault();
                    if (self_2._draggingNode === node || self_2._draggingNode.find(node).length)
                        return false;
                    if (self_2.isDroppableNode(item, node) === false)
                        return false;
                    var lastOverNode = self_2._draggingOverItem.__overnode;
                    self_2._draggingOverItem.appendTo((contentEl.is(':visible') ? contentEl : titleEl_1));
                    if (lastOverNode !== node) {
                        self_2._draggingOverItem.__overnode = node;
                        self_2._draggingOverItem.data('__dragoverstarttime', performance.now());
                    }
                    else {
                        var starttime = self_2._draggingOverItem.data('__dragoverstarttime');
                        if (starttime && starttime != false) {
                            var cost = performance.now() - starttime;
                            if (cost > 3500) {
                                self_2._draggingOverItem.data('__dragoverstarttime', false);
                                self_2.open(item, false, undefined, el);
                            }
                        }
                    }
                };
            }
            return node;
        };
        Tree.prototype.doAction = function (action, item, targetEl) {
            (_context || _util).doAction(action, item, this, targetEl);
        };
        Tree.prototype.loadChildren = function (item, node, queryParams) {
            var _this = this;
            if (queryParams === void 0) { queryParams = undefined; }
            var childrenEl = $('<div class="accordion">').appendTo(node).css('margin', 0);
            var ch = node.data('children') || item.children;
            if (ch) {
                this.appendNodes(ch, childrenEl);
                node.data('childrenLoaded', true);
                node.trigger('childrenLoaded');
                this.trigger('childrenLoaded', ch);
            }
            else if (this.loader) {
                var icon_2 = node.prev().find('.icon:not(span > .icon)');
                icon_2.addClass('loading');
                _util.dataCallback(function (data, isError) {
                    if (isError && !Array.isArray(data))
                        data = [];
                    node.data('childrendata', data);
                    if (!isError && data) {
                        data = _this.genLoadMoreDataIf(data, childrenEl);
                        _this.appendNodes(data, childrenEl);
                    }
                    icon_2.removeClass('loading');
                    node.data('childrenLoaded', true);
                    node.trigger('childrenLoaded');
                    _this.trigger('childrenLoaded', data);
                }, this.loader, [item, queryParams]);
            }
        };
        Tree.prototype.getChildren = function (item, contentNode) {
            if (contentNode === void 0) { contentNode = undefined; }
            if (item === undefined || item === null)
                return this.data;
            var node = contentNode || this.findContentNode(item)[1];
            if (!node)
                return null;
            if (node.data('children'))
                return node.data('children');
            if (item.children)
                return item.children;
            return node.data('childrendata');
        };
        Tree.prototype.getNodeParent = function (item) {
            var node = this.findContentNode(item)[1];
            if (!node)
                return null;
            node = node.parent().closest('._node_content.content');
            return node.data('userdata');
        };
        Tree.prototype.appendNodes = function (ch, el) {
            _super.prototype.appendNodes.call(this, ch, el);
            this.templateEl.accordion('refresh');
        };
        Tree.prototype.getActions = function (item) {
            var _this = this;
            var acs = _util.fetchField(item, this.actionField);
            if (acs) {
                if (!Array.isArray(acs))
                    acs = [acs];
                acs = acs.map(function (a) {
                    if (typeof a === 'string')
                        a = _this.findActionByName(a, item);
                    if (a && (a.id || a.name)) {
                        var na = _this.findActionByName(a.id || a.name, item);
                        if (na)
                            a = Object.assign({}, na, a);
                    }
                    return a;
                }).filter(function (a) { return a; });
            }
            if (this.enableRefreshNodeAction) {
                acs = acs || [];
                var icon = 'circle notch';
                if (typeof this.enableRefreshNodeAction === 'string')
                    icon = this.enableRefreshNodeAction;
                acs.unshift({
                    label: _locale.common.refresh || '刷新', icon: icon, fn: function () {
                        _this.refreshNode(item);
                    }
                });
            }
            return acs;
        };
        Tree.prototype.findActionByName = function (name, item) {
            return _context.findAction(name);
        };
        Tree.prototype.getExtraActions = function (item) {
            return _util.fetchField(item, this.extraActionField);
        };
        Tree.prototype.findPath = function (node) {
            var path = [node], el = this.findContentNode(node)[1];
            while (el && !el.is(this.templateEl)) {
                el = el.parent();
                if (el.hasClass('content')) {
                    path.unshift(el.data('userdata'));
                }
            }
            return path;
        };
        Tree.prototype.travalNodes = function (fn) {
            this.templateEl.find('._node_content.content').each(function () {
                var contentEl = $(this);
                var titleEl = contentEl.prev('.title'), data = contentEl.data('userdata');
                fn(titleEl, contentEl, data);
            });
        };
        Tree.prototype.removeNode = function (item) {
            var content = this.findContentNode(item)[1];
            if (content) {
                content.prev('.title').remove();
                content.remove();
            }
        };
        return Tree;
    }(List));
    _sui.Tree = Tree;
    function tree(parentEl, options) {
        return new Tree(parentEl, options).render();
    }
    _sui.tree = tree;
    var CheckTree = (function (_super) {
        __extends(CheckTree, _super);
        function CheckTree() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.showCheck = true;
            _this.selectCheck = false;
            _this.checkedNodeClass = 'tree_node_checked';
            _this.checkedData = [];
            _this.autoChangingIgnoreInitCheck = true;
            return _this;
        }
        CheckTree.prototype.prepare = function () {
            if (this.iconTrigger === undefined && this.showCheck && this.selectCheck)
                this.iconTrigger = true;
            return _super.prototype.prepare.call(this);
        };
        CheckTree.prototype.shouldAutoCheck = function (item) {
            return this.autoCheck && item.shouldAutoCheck !== false;
        };
        CheckTree.prototype.appendNodes = function (ch, el) {
            _super.prototype.appendNodes.call(this, ch, el);
            this.updateAllCheckedClass();
        };
        CheckTree.prototype.appendNode = function (item, el) {
            var _this = this;
            var node = _super.prototype.appendNode.call(this, item, el);
            var ch = null;
            try {
                ch = node.children('input._itemcheck[type="checkbox"]').first();
            }
            catch (e) {
                console.error(e);
            }
            var shouldCheck;
            if (this.checkedData.some(function (cd) { return _this.equalsNode(cd, item); }))
                shouldCheck = true;
            else {
                shouldCheck = item._initChecked || item._checked;
                if (shouldCheck !== undefined && ch) {
                    ch.data('_initChecked', !!shouldCheck);
                }
                if (shouldCheck === undefined && this.checkInit) {
                    shouldCheck = this.checkInit(item);
                }
                if (shouldCheck === undefined && this.shouldAutoCheck(item)) {
                    var parentContentEl = el.closest('._node_content.content');
                    if (parentContentEl) {
                        shouldCheck = parentContentEl.prev().find('> input._itemcheck').prop('checked');
                    }
                }
            }
            if (shouldCheck && ch) {
                ch.prop('checked', true).change();
            }
            return node;
        };
        CheckTree.prototype.redrawNodeTitle = function (node, item) {
            var _this = this;
            var contentEl = node.filter('._node_content.content');
            if (item === undefined) {
                item = contentEl.data('userdata');
            }
            if (item === undefined || item === null)
                return;
            var ch = node.children('input._itemcheck[type="checkbox"]').first();
            var labelEl = node.children('._label').first();
            labelEl.off('click.checkselect');
            if (!item.__toAppendData && this.showCheck && this.checkable(item)) {
                if (!ch.length) {
                    ch = $('<input type="checkbox" class="_itemcheck">').insertBefore(labelEl);
                    if (!this.autoCheck)
                        bindMouseEvent(ch);
                    ch.click(function (e) { return e.stopPropagation(); });
                    ch.change(function (e) {
                        var autoChanging = !e.originalEvent;
                        function changeit(c, v) {
                            var old = c.prop('checked');
                            if (old !== v)
                                c.prop('checked', v).change();
                        }
                        var checked = ch.prop('checked');
                        if (!autoChanging && !_this.autoCheck && _this.isAppendingCheck(ch, e)) {
                            var last = _this.lastCheckRecord ? _this.lastCheckRecord.data : undefined;
                            if (last) {
                                var _a = _this.findContentNode(last), lastIndex_1 = _a[0], lastContentEl = _a[1];
                                var _b = _this.findContentNode(item), toIndex_1 = _b[0], currentContentEl = _b[1];
                                if (lastIndex_1 !== toIndex_1) {
                                    var prev_1 = toIndex_1 < lastIndex_1;
                                    var tosel_1 = [];
                                    _this.templateEl.find('._node_content.content').each(function (i, c) {
                                        if (prev_1 && i < lastIndex_1 && i >= toIndex_1) {
                                            tosel_1.push(c);
                                        }
                                        else if (i > lastIndex_1 && i <= toIndex_1) {
                                            tosel_1.push(c);
                                        }
                                    });
                                    if (prev_1)
                                        tosel_1.reverse();
                                    tosel_1.forEach(function (c) {
                                        c = $(c);
                                        var titleEl = c.prev('.title');
                                        if (titleEl.is(':visible'))
                                            _this._checkNodeByContentEl($(c), checked);
                                    });
                                    return;
                                }
                            }
                        }
                        var ret = true;
                        if (_this.checkListener)
                            ret = _this.checkListener(item, checked, autoChanging, ch);
                        var shouldAutoCheck = _this.shouldAutoCheck(item);
                        if (ret !== false) {
                            if (shouldAutoCheck) {
                                if (!autoChanging && shouldAutoCheck !== 'parent') {
                                    var chboxes = contentEl.find('.accordion > .title > input._itemcheck');
                                    var ignoreInitCheck_1 = _this.autoChangingIgnoreInitCheck;
                                    chboxes.each(function () {
                                        var cch = $(this);
                                        if (checked) {
                                            var initChecked = cch.data('_initChecked');
                                            if (initChecked !== undefined && !ignoreInitCheck_1)
                                                checked = initChecked;
                                        }
                                        changeit(cch, checked);
                                    });
                                }
                                if (shouldAutoCheck !== 'children') {
                                    var parentContentEl = contentEl.parent().closest('._node_content.content');
                                    if (parentContentEl) {
                                        var hasChecked = contentEl.parent().find('> .title > input._itemcheck:checked');
                                        var parentChecked = hasChecked.length > 0;
                                        var parentInput = parentContentEl.prev('.title').children('input._itemcheck');
                                        changeit(parentInput, parentChecked);
                                    }
                                }
                            }
                        }
                        if (_this.checkedNodeClass) {
                            node.filter('.title').first().toggleClass(_this.checkedNodeClass, checked);
                        }
                        if (checked && !_this.checkedData.find(function (cd) { return _this.equalsNode(cd, item); }))
                            _this.checkedData.push(item);
                        else if (!checked) {
                            var pos = _this.checkedData.findIndex(function (cd) { return _this.equalsNode(cd, item); });
                            if (pos !== -1)
                                _this.checkedData.splice(pos, 1);
                        }
                        _this.lastCheckRecord = { data: item, checked: checked };
                        _this.updateAllCheckedClass();
                        _this.trigger('checkchange', { checked: true, node: item });
                    });
                }
                if (this.selectCheck)
                    labelEl.on('click.checkselect', function (e) {
                        ch.click();
                    });
            }
            else {
                ch.remove();
            }
            return _super.prototype.redrawNodeTitle.call(this, node, item);
        };
        CheckTree.prototype.updateAllCheckedClass = function () {
            this.templateEl.find('input._itemcheck').each(function () {
                var ch = $(this), title = ch.parent('._suitree-title'), contentEl = title.next('._node_content.content');
                ;
                if (ch.is(':checked')) {
                    var cs = contentEl.find('input._itemcheck');
                    var csChecked = contentEl.find('input._itemcheck:checked');
                    ch.toggleClass('_itemcheck_notall', cs.length !== csChecked.length);
                }
            });
        };
        CheckTree.prototype.isAppendingCheck = function (chEl, e) {
            var me = getCurrentMouseEvent(chEl);
            if (me)
                return me.shiftKey;
            return e.shiftKey || (e.originalEvent && e.originalEvent.shiftKey);
        };
        CheckTree.prototype.checkable = function (item) {
            return item._checkable !== false;
        };
        CheckTree.prototype.checkNode = function (item, value) {
            if (value === void 0) { value = true; }
            var node = this.findContentNode(item)[1];
            if (node && node.length) {
                this._checkNodeByContentEl(node, value);
            }
        };
        CheckTree.prototype._checkNodeByContentEl = function (contentEl, value) {
            var ch = contentEl.prev('.title').find('input._itemcheck').first();
            ch.prop('checked', value).change();
        };
        CheckTree.prototype.checkAll = function (nodes, value) {
            if (value === void 0) { value = true; }
            if (nodes === false)
                value = false;
            var isAll = !Array.isArray(nodes), self = this;
            this.templateEl.find('._node_content').each(function () {
                var contentEl = $(this);
                var d = contentEl.data('userdata');
                if (isAll || nodes.some(function (o) { return self.equalsNode(o, d); })) {
                    var ch = contentEl.prev('.title').find('input._itemcheck').first();
                    ch.prop('checked', value).change();
                }
            });
        };
        CheckTree.prototype.getChecked = function () {
            var _this = this;
            this.checkedData = this.checkedData.filter(function (o) {
                return _this.findContentNode(o)[0] !== -1;
            });
            return this.checkedData.slice();
        };
        CheckTree.prototype.isChecked = function (item) {
            var _this = this;
            return this.checkedData.find(function (o) { return _this.equalsNode(o, item); });
        };
        return CheckTree;
    }(Tree));
    _sui.CheckTree = CheckTree;
    function checktree(parentEl, options) {
        return new CheckTree(parentEl, options).render();
    }
    _sui.checktree = checktree;
    var CategroyTabs = (function (_super) {
        __extends(CategroyTabs, _super);
        function CategroyTabs() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.template = "\n            <div class=\"ui grid\">\n                <div class=\"four wide column\">\n                    <div class=\"ui vertical fluid tabular menu\"></div>\n                </div>\n                <div style=\"overflow:hidden;\" class=\"twelve wide column\">\n                </div>\n            </div>\n        ";
            _this.tabMenus = [];
            return _this;
        }
        CategroyTabs.prototype.doRender = function (el, prepare) {
            var columns = el.children('.column');
            this.headerEl = columns.eq(0).find('.menu');
            this.contentEl = columns.eq(1);
            this.contentClass = '';
            _super.prototype.doRender.call(this, el, prepare);
        };
        CategroyTabs.prototype.doSetData = function (v) {
            var _this = this;
            this.clearTabs();
            var catNameToActive;
            v = Array.isArray(v) ? v : [v];
            if (v.length === 1) {
                this.templateEl.children('.column').first().hide();
                this.templateEl.children('.column').last().removeClass('twelve wide').addClass('sixteen wide');
            }
            v.forEach(function (cat) {
                var tabContent = $('<div class="" style="overflow:auto">');
                var tabMenu = new Tabs(tabContent, { headerClass: 'tiny secondary compact', contentClass: '', tabOptions: _this.tabOptions }).render();
                _this.addTab(cat.name, tabContent, { closable: false, active: false, title: cat.title });
                var tabs = cat.tabs || cat.children;
                var tabNameToActive = null;
                tabs.forEach(function (tab) {
                    tabMenu.addTab(tab.name || tab.label || tab.objectType, tab.content, { closable: false, active: false, title: tab.title });
                    if (tab.tabActive && !tabNameToActive)
                        tabNameToActive = tab.name;
                });
                if (tabNameToActive)
                    tabMenu.activeTab(tabNameToActive);
                if (cat.tabActive && !catNameToActive)
                    catNameToActive = cat.name;
                _this.tabMenus.push(tabMenu);
            });
            if (catNameToActive)
                this.activeTab(catNameToActive);
        };
        CategroyTabs.prototype.getTabMenus = function () {
            return this.tabMenus;
        };
        return CategroyTabs;
    }(Tabs));
    _sui.CategroyTabs = CategroyTabs;
    var UploadBox = (function (_super) {
        __extends(UploadBox, _super);
        function UploadBox(url, parentEl, options) {
            if (options === void 0) { options = null; }
            var _this = _super.call(this, parentEl, options) || this;
            _this.url = url;
            _this.template = "\n            <div style=\"border: 2px dotted gray;border-radius:5px;text-align:center;vertical-align:middle;padding: 10px;\">\n                <div>\n                    <div class=\"description\"></div>\n                    <h1 class=\"title\"></h1>\n                    <h2 class=\"subTitle\"></h2>\n                    <div style=\"color:gray;font-size:small;padding:5px;\"></div>\n                    <div><div class=\"ui blue button\" style=\"position:relative;\">".concat(_locale.sui.upload.chooseFile, "<input style=\"position:absolute;top:0;left:0;width:100%;height:100%;opacity:0;cursor:pointer;\" type=\"file\" name=\"files[]\" multiple=\"multiple\"></div></div>\n                </div>\n                <div id=\"fileList\" style=\"padding:10px;\"></div>\n                <div id=\"debug\" style=\"display:none;padding:10px;text-align:left;\"><ul></ul></div>\n            </div>\n        ");
            _this.title = _locale.sui.upload.title;
            _this.subTitle = _context.getSetting('_sui.defaultUploadSubTitle');
            _this.description = '';
            _this.accept = '';
            return _this;
        }
        UploadBox.prototype.prepare = function () {
            _super.prototype.prepare.call(this);
            if (!$.prototype.dmUploader)
                return _util.loadScript('lib/jquery/dmuploader.min.js');
        };
        UploadBox.prototype.addLog = function (message) {
        };
        UploadBox.prototype.addFile = function (id, file) {
            var template = "\n            <div class=\"file\" id=\"uploadFile".concat(id, "\">\n                <div class=\"info\">\n                    <i class=\"ui icon\"></i><span class=\"filename\" title=\"Size: ").concat(file.size, "bytes - Mimetype: ").concat(file.type, "\">").concat(file.name, "</span><br />\n                    <small><span class=\"status\">Waiting</span></small>\n                </div>\n                <div class=\"ui attached progress\">\n                    <div class=\"bar\"></div>\n                </div>\n            </div>");
            template = $(template).prependTo(this.templateEl.find('#fileList'));
            template.find('.progress').progress();
            this._currentFile = file;
        };
        UploadBox.prototype.updateFileStatus = function (id, status, message, data) {
            var el = this.templateEl.find('#uploadFile' + id);
            el.find('span.status').html(message).addClass(status);
            if (status === 'success' || status === 'error')
                el.find('.progress').remove();
            if (status === 'success') {
                el.find('.icon').addClass('green check');
                if (this.success)
                    this.success(data, this._currentFile);
                this.trigger(_sui.EVENT_UPLOAD_SUCCESS, data);
                if (data && data.download && data.download.base64BytesString) {
                    _util.download(_util.base64StringToByteArray(data.download.base64BytesString), this.downloadFileName || data.download.fileName || 'download', this.downloadFileType || data.download.type);
                }
            }
        };
        UploadBox.prototype.updateFileProgress = function (id, percent) {
            this.templateEl.find('#uploadFile' + id).find('.progress').progress('set percent', percent);
        };
        UploadBox.prototype.doRender = function (el) {
            el.find('.title').html(this.title);
            if (this.subTitle) {
                var subTitle = el.find('.subTitle');
                var defaultUploadSubTitleColor = _context.getSetting('_sui.defaultUploadSubTitleColor');
                subTitle.html(this.subTitle);
                if (defaultUploadSubTitleColor) {
                    subTitle.css('color', defaultUploadSubTitleColor);
                }
            }
            el.find('.description').html(this.description);
            if (this.accept)
                el.find('input[type="file"]').attr('accept', this.accept);
            var add_file = this.addFile.bind(this);
            var add_log = this.addLog.bind(this);
            var update_file_status = this.updateFileStatus.bind(this);
            var update_file_progress = this.updateFileProgress.bind(this);
            var opt = {
                onBeforeUpload: function (id) {
                    add_log('Starting the upload of #' + id);
                    update_file_status(id, 'uploading', _locale.common.uploading || '正在上传...');
                },
                onNewFile: function (id, file) {
                    add_log('New file added to queue #' + id);
                    add_file(id, file);
                },
                onComplete: function (e) { return add_log('All pending tranfers finished'); },
                onUploadProgress: function (id, percent) {
                    var percentStr = percent + '%';
                    update_file_progress(id, percentStr);
                },
                onUploadSuccess: function (id, data) {
                    add_log('Upload of file #' + id + ' completed');
                    add_log('Server Response for file #' + id + ': ' + JSON.stringify(data));
                    update_file_status(id, 'success', _locale.common.complete || '导入完成.', data);
                    update_file_progress(id, '100%');
                },
                onUploadError: function (id, xhr, status, message) {
                    var msg = xhr.responseJSON ? xhr.responseJSON.message : xhr.responseText;
                    if (msg)
                        message = msg;
                    add_log('Failed to Upload file #' + id + ': ' + message);
                    update_file_status(id, 'error', message);
                    alert(message);
                },
                onFileTypeError: function (file) {
                    add_log('File \'' + file.name + '\' cannot be added: must be an image');
                },
                onFileSizeError: function (file) {
                    add_log('File \'' + file.name + '\' cannot be added: size excess limit');
                },
                onFallbackMode: function (message) {
                    alert('Browser not supported(do something else here!): ' + message);
                }
            };
            opt = Object.assign(opt, this);
            el.dmUploader(opt);
        };
        return UploadBox;
    }(Component));
    _sui.UploadBox = UploadBox;
    var Steps = (function (_super) {
        __extends(Steps, _super);
        function Steps() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.template = "\n            <div class=\"ui top attached steps\"></div>\n        ";
            _this.stepTemplate = "\n            <div class=\"step\">\n                <i class=\"icon\"></i>\n                <div class=\"content\">\n                    <div class=\"title\"></div>\n                    <div class=\"description\"></div>\n                </div>\n            </div>\n        ";
            return _this;
        }
        Steps.prototype.doSetData = function (steps) {
            var _this = this;
            steps = steps || [];
            this.data = steps;
            var el = this.templateEl;
            el.empty();
            steps.forEach(function (step) {
                var e = $(_this.stepTemplate);
                if (step.active)
                    e.addClass('active');
                e.find('.icon').addClass(step.icon);
                e.find('.title').append(step.title);
                e.find('.description').append(step.description);
                e.appendTo(el);
            });
            if (!el.children('.step.active').length)
                el.children('.step').eq(0).addClass('active');
        };
        return Steps;
    }(Component));
    _sui.Steps = Steps;
    function steps(el, options) {
        if (options === void 0) { options = null; }
        return new Steps(el, options).render();
    }
    _sui.steps = steps;
    var uploadModalEl;
    function showUploadModal(url, options) {
        var _this = this;
        if (options === void 0) { options = null; }
        if (!uploadModalEl) {
            uploadModalEl = $('<div class="ui modal">');
        }
        url = _context.fullDataUrl(url, true);
        var defaultOpt = _sui.defaultUploadOptions;
        if (typeof defaultOpt === 'function')
            defaultOpt = defaultOpt();
        var box = new UploadBox(url, uploadModalEl, Object.assign({}, defaultOpt, options)).render();
        box.rendered().then(function (box) {
            _this.showModal(uploadModalEl, { onHidden: function (e) { return uploadModalEl.empty(); } });
        });
        return box;
    }
    _sui.showUploadModal = showUploadModal;
    var Search = (function (_super) {
        __extends(Search, _super);
        function Search(parentEl, options) {
            if (options === void 0) { options = null; }
            var _this = _super.call(this, 'search', parentEl, options) || this;
            _this.template = "\n            <div class=\"ui search _suisearch\">\n                <div class=\"ui icon input\">\n                  <input class=\"prompt2\" type=\"text\" placeholder=\"\" style=\"padding-right:0.8em !important;\">\n                  <i class=\"search icon link\"></i>\n                </div>\n                <div class=\"results\"></div>\n            </div>\n        ";
            _this.defaultSuiOptions = { minCharacters: 0, searchDelay: 300, showNoResults: false, searchOnFocus: false, maxResults: 100, selector: { prompt: '.prompt2' } };
            _this.titleField = 'name';
            _this.showByKey = true;
            return _this;
        }
        Search.prototype.doRender = function (el, prepare) {
            var _this = this;
            el.find('input.prompt2').attr('placeholder', this.placeholder);
            this.defaultSuiOptions = Object.assign(this.defaultSuiOptions, {
                fields: { title: this.titleField },
                onSelect: function (item) {
                    _this.valueItem = item;
                    _this.trigger('change');
                },
                apiSettings: toApiSetting(this.dataProvider || this.data, undefined, undefined, function (ret) {
                    return { results: ret };
                })
            });
            if (this.showByKey)
                this.suiOptions = { automatic: false };
            _super.prototype.doRender.call(this, el, prepare);
            el.find('.search.icon').click(function () {
                _this.behavior('cancel query');
                _this.behavior('query');
            });
        };
        Search.prototype.getValue = function () {
            return this.behavior('get value');
        };
        Search.prototype.setValue = function (v) {
            if (v === null || v === undefined || (typeof v !== 'string' && typeof v !== 'number'))
                return this.setValueItem(v);
            return this.behavior('set value', v);
        };
        Search.prototype.getValueItem = function () {
            return this.valueItem;
        };
        Search.prototype.setValueItem = function (item) {
            this.valueItem = item;
            this.setValue(item ? item[this.titleField] : '');
        };
        Search.prototype.clear = function () {
            this.setValueItem(null);
            this.behavior('clear cache');
        };
        Search.prototype.clearCache = function () {
            this.behavior('clear cache');
        };
        return Search;
    }(SuiComponent));
    _sui.Search = Search;
    function search(parentEl, options) {
        if (options === void 0) { options = {}; }
        return new Search(parentEl, options).render();
    }
    _sui.search = search;
    var AccordionMenu = (function (_super) {
        __extends(AccordionMenu, _super);
        function AccordionMenu(parentEl, options) {
            if (options === void 0) { options = null; }
            var _this = _super.call(this, parentEl, options) || this;
            _this.template = "\n            <div class=\"ui vertical menu\" style=\"border-radius:0;\"></div>\n        ";
            _this.animation = 'fly up in';
            return _this;
        }
        AccordionMenu.prototype.doRender = function (el, prepare) {
            var _this = this;
            _super.prototype.doRender.call(this, el, prepare);
            if (this.activeIndex === undefined && this.data)
                this.data.some(function (item, i) {
                    if (item.active === true)
                        _this.activeIndex = i;
                    return _this.activeIndex !== undefined;
                });
            this.currentIndex = this.activeIndex = this.activeIndex || 0;
            this.templateEl.css({ width: '100%', height: '100%', display: 'flex', flexDirection: 'column' });
            if (this.data)
                this.data.forEach(function (item, i) {
                    _this.addItem(item, i);
                });
            else {
                var self_3 = this;
                this.getParentEl().children().each(function (i) {
                    if (this !== el[0]) {
                        self_3.addItem($(this));
                    }
                });
            }
        };
        AccordionMenu.prototype.addItem = function (item, i) {
            if (i === void 0) { i = -1; }
            if (i === -1)
                i = this.templateEl.find('> .item').length;
            var self = this;
            var node, header, menu;
            if (_util.isjquery(item)) {
                item.addClass('item').appendTo(this.templateEl);
                node = item;
                header = node.find('.header').eq(0);
                menu = node.find('.menu').eq(0);
            }
            else {
                node = $('<div class="item">').appendTo(this.templateEl);
                var title = item.title;
                if (typeof item.icon === 'string' && typeof title === 'string')
                    title = '<i class="' + item.icon + ' icon"></i>' + title;
                header = $('<div class="header">').html(title).appendTo(node);
                if (item.actions || item.action) {
                    var acs = item.actions || item.action;
                    if (!Array.isArray(acs))
                        acs = [acs];
                    acs.reverse().forEach(function (a) {
                        if (hasPermission(a)) {
                            var ai_1 = $('<div>').css('float', 'right').appendTo(header).click(function () {
                                _context.doAction(a, item, self, ai_1);
                                return !!a.clickActive;
                            });
                            if (a.icon)
                                $('<i class="icon">').addClass(a.icon).appendTo(ai_1);
                            else if (a.title || a.label)
                                ai_1.text(a.title || a.label);
                            if (a.tip)
                                ai_1.attr('title', a.tip);
                        }
                    });
                }
                menu = $('<div class="menu">').appendTo(node);
                this.itemRender(item, menu);
            }
            node.css({ flex: this.activeIndex === i ? 'auto' : 'none', display: 'flex', flexDirection: 'column', minHeight: '0%' });
            if (item.item !== false)
                header.css('cursor', 'pointer').click(function () {
                    self.activeAt(i);
                });
            menu.css({ flex: 'auto', overflow: 'auto', lineHeight: 'normal' });
            if (this.activeIndex !== i)
                menu.css('display', 'none');
        };
        AccordionMenu.prototype.itemRender = function (item, el) {
            el.html(item.html || item.content);
        };
        AccordionMenu.prototype.activeAt = function (i) {
            if (this.currentIndex === i)
                return;
            var toUp = i > this.currentIndex;
            this.currentIndex = i;
            var nodes = this.templateEl.find('> .item'), node = nodes.eq(i);
            nodes.css('flex', 'none');
            this.templateEl.find('> .item > .menu').css('display', 'none');
            node.css('flex', 'auto');
            node.find('> .menu').css('display', 'block');
            if (toUp)
                node.transition({ animation: this.animation, displayType: 'flex' });
        };
        return AccordionMenu;
    }(Component));
    _sui.AccordionMenu = AccordionMenu;
    function accordionMenu(el, options) {
        if (options === void 0) { options = {}; }
        return new AccordionMenu(el, options).render();
    }
    _sui.accordionMenu = accordionMenu;
    var Card = (function (_super) {
        __extends(Card, _super);
        function Card(parentEl, options) {
            if (options === void 0) { options = null; }
            var _this = _super.call(this, parentEl, options) || this;
            _this.template = '<a class="ui card">';
            return _this;
        }
        Card.prototype.doRender = function (el, prepare) {
            _super.prototype.doRender.call(this, el, prepare);
            var self = this;
            if (this.href)
                el.attr('href', this.href);
            if (this.action)
                el.click(function () {
                    _context.doAction(self.action, self.data || self.options, this, el);
                    return false;
                });
            if (this.contents) {
                if (Array.isArray(this.contents))
                    this.contents.forEach(function (c) { return $(c).appendTo(el); });
                else
                    $(this.contents).appendTo(el);
            }
            else {
                var contentEl = $('<div class="content">').appendTo(el);
                if (this.img)
                    $('<img>').attr('src', this.img).prependTo(el);
                if (this.title)
                    $('<div class="header">').html(this.title).appendTo(contentEl);
                if (this.meta)
                    $('<div class="meta">').html(this.meta).appendTo(contentEl);
                if (this.description)
                    $('<div class="description">').html(this.description).appendTo(contentEl);
                if (this.extra)
                    $('<div class="extra content">').html(this.extra).appendTo(el);
            }
        };
        return Card;
    }(Component));
    _sui.Card = Card;
    function card(el, options) {
        if (options === void 0) { options = {}; }
        return new Card(el, options).render();
    }
    _sui.card = card;
    var Cards = (function (_super) {
        __extends(Cards, _super);
        function Cards(parentEl, options) {
            if (options === void 0) { options = null; }
            var _this = _super.call(this, parentEl, options) || this;
            _this.template = '<div class="ui cards">';
            _this.templateCss = { margin: 0 };
            _this.data = [];
            return _this;
        }
        Cards.prototype.doRender = function (el, prepare) {
            _super.prototype.doRender.call(this, el, prepare);
            this.data.forEach(function (d) {
                card(el, d);
            });
        };
        return Cards;
    }(Component));
    _sui.Cards = Cards;
    function cards(el, options) {
        if (options === void 0) { options = {}; }
        return new Cards(el, options).render();
    }
    _sui.cards = cards;
    var alertEl;
    function showModal(selector, setting) {
        if (setting === void 0) { setting = null; }
        var commonModalArgs = [];
        for (var _i = 2; _i < arguments.length; _i++) {
            commonModalArgs[_i - 2] = arguments[_i];
        }
        if (arguments.length > 2 && Array.isArray(arguments[2]))
            return this.showCommonModal.apply(this, arguments);
        maskModalsDimmer(false);
        var el = $(selector);
        var autoRemove = !el.parent().length;
        el.addClass('ui modal');
        setting = setting || {};
        if (setting.autoRemove !== undefined)
            autoRemove = setting.autoRemove;
        if (setting.modalClass)
            el.addClass(setting.modalClass);
        if (setting.allowMultiple == undefined)
            setting.allowMultiple = true;
        if (setting.observeChanges === undefined)
            setting.observeChanges = true;
        var onHidden = setting.onHidden;
        setting.onHidden = function () {
            if (autoRemove) {
                destroyComponents(el[0]);
                el.remove();
            }
            if (onHidden)
                onHidden();
        };
        el = el.modal(setting);
        if (setting._showAnimateOpacity !== false) {
            var timer_2;
            $('<i class="icon eye mini slash outline">').css({ position: 'absolute', right: 10, top: 10, opacity: 0.4 }).appendTo(el).hover(function () {
                clearTimeout(timer_2);
                timer_2 = setTimeout(function () {
                    el.animate({ opacity: 0.1 });
                }, 400);
            }, function () {
                clearTimeout(timer_2);
                el.animate({ opacity: 1 });
            });
        }
        el.modal('show');
        return el;
    }
    _sui.showModal = showModal;
    var maskingDimmers;
    function maskModalsDimmer(hide) {
        if (hide === void 0) { hide = true; }
        if (maskingDimmers && maskingDimmers.length)
            maskingDimmers.removeClass('dimmer-invalid-mask').css('cssText', 'display:flex!important');
        if (hide) {
            maskingDimmers = $('.ui.dimmer.modals.visible.active').addClass('dimmer-invalid-mask').css('display', 'none');
        }
    }
    _sui.maskModalsDimmer = maskModalsDimmer;
    function showCommonModal(header, content, actions, modalOptions) {
        if (modalOptions === void 0) { modalOptions = null; }
        var template = "\n            <div class=\"ui modal\">\n                <div class=\"header\" style=\"overflow-x:auto;white-space:pre-line;\"></div>\n                <div class=\"content\" style=\"overflow:auto;padding-bottom:1px;\"></div>\n                <div class=\"actions\"></div>\n            </div>\n        ";
        template = $(template);
        var headerEl = template.children('.header');
        var contentEl = template.children('.content');
        var actionsEl = template.children('.actions');
        headerEl.append(header);
        contentEl.append(content);
        if (modalOptions && modalOptions.fixContentHeight) {
            contentEl.css('overflow', 'hidden');
        }
        if (actions)
            actions.forEach(function (a) { return actionsEl.append($(a)); });
        modalOptions = Object.assign({
            onHidden: function (e) { return template.remove(); }
        }, modalOptions);
        showModal(template, modalOptions);
        return template;
    }
    _sui.showCommonModal = showCommonModal;
    function showGridModal(gridOptions, header, modalOptions, actions) {
        if (actions === void 0) { actions = undefined; }
        var el = $('<div>');
        _sui.grid(el, gridOptions);
        if (actions === undefined && modalOptions && modalOptions.closable === false) {
            actions = [
                { label: '关闭', class: 'cancel' }
            ];
        }
        return showCommonModal(header, el, actions, modalOptions);
    }
    _sui.showGridModal = showGridModal;
    function closeModal(selector) {
        var modalEl = $(selector);
        while (!modalEl.hasClass('modal') && modalEl.parent().length) {
            modalEl = modalEl.parent();
        }
        modalEl.modal('hide');
    }
    _sui.closeModal = closeModal;
    function doAlert(msg, title, actions, setting) {
        if (setting === void 0) { setting = {}; }
        if (alertEl)
            alertEl.remove();
        alertEl = $(Templates.alertModal).appendTo('body');
        alertEl.children('.header').html(title);
        alertEl.children('.content').html(_util.xssUtil.filterXSS(msg));
        alertEl.children('.actions').empty();
        actions = actions || [];
        actions.forEach(function (value, i) {
            var actionEl = value.html ? $(value.html) : $('<a class="ui button mini">').text(value.title || value.label || value.text);
            if (value.class)
                actionEl.addClass(value.class);
            actionEl.appendTo(alertEl.children('.actions'));
            if (value.fn) {
                var fn_1, scope_1;
                if (typeof (value.fn) === 'function')
                    fn_1 = value.fn;
                else {
                    fn_1 = value.fn.fn;
                    scope_1 = value.fn.scope;
                }
                var avalue_1 = value;
                api(actionEl, function () {
                    return fn_1.call(scope_1, alertEl, avalue_1);
                });
            }
        });
        if (setting.closable === undefined)
            setting.closable = false;
        if (setting.allowMultiple === undefined)
            setting.allowMultiple = true;
        return showModal(alertEl, setting);
    }
    _sui.doAlert = doAlert;
    function getTopSui() {
        var b = window['_sui'];
        try {
            var w = window;
            while (w.parent && w.parent !== w && w.parent['_sui']) {
                b = window.parent['_sui'];
                w = w.parent;
            }
        }
        catch (err) {
        }
        return b;
    }
    _sui.getTopSui = getTopSui;
    function alert(msg, title, options) {
        var _this = this;
        if (title === void 0) { title = '<i class="icon info"></i>'; }
        if (options === void 0) { options = undefined; }
        var topSui = getTopSui();
        if (topSui !== _sui)
            return topSui.alert(msg, title, options);
        var btns = [{ html: "<div class=\"ui approve positive right labeled icon button mini\">".concat(_locale.common.ok, "<i class=\"checkmark icon\"></i></div>") }];
        var content = $('<div class="sui-alert-content" style="word-wrap:break-word;padding: 0 5px;">');
        if (options && options.textMode == true)
            content.text(msg);
        else
            content.html(msg);
        return new Promise(function (resolve, reject) {
            var modal = doAlert(content, title, btns, {
                _showAnimateOpacity: false,
                onHidden: function () {
                    resolve(_this);
                }
            });
            resolve(modal);
        });
    }
    _sui.alert = alert;
    function alertError(msg, browserAlert) {
        if (browserAlert === void 0) { browserAlert = undefined; }
        try {
            if (window.top && window.top != window && window.top['_sui'] && window.top['_sui'].alertError)
                return window.top['_sui'].alertError(msg, browserAlert);
        }
        catch (e) { }
        var detail;
        if (typeof msg === 'string')
            try {
                detail = msg;
                msg = JSON.parse(msg);
            }
            catch (e) { }
        var title = '错误信息', icon = 'info circle';
        if (msg && typeof msg === 'object') {
            if (browserAlert === undefined)
                browserAlert = msg.browserAlert;
            if (msg.title !== undefined)
                title = msg.title;
            if (msg.icon !== undefined)
                icon = msg.icon;
            detail = msg.detail || msg.stacktrace || detail;
            if (msg.uuid) {
                detail = 'uuid:' + msg.uuid + '    \r\n' + detail;
            }
            msg = msg.message || msg.text || msg.content || msg.error;
        }
        if (browserAlert)
            window.alert(msg);
        else {
            var btns = [{ html: "<div class=\"ui approve positive right labeled icon button mini\">".concat(_locale.common.ok, "<i class=\"checkmark icon\"></i></div>") }];
            var titleEl = "<div><i class=\"icon ".concat(icon, "\"></i>").concat(title, "</div>");
            if (detail) {
                titleEl = $('<div>');
                $('<i class="icon">').addClass(icon).appendTo(titleEl).click(function () {
                    window.open('', '', '').document.write(detail);
                });
                titleEl.append(title);
            }
            _sui.alert(msg, titleEl).then(function (modalEl) {
                if (detail && _context.getSetting('_sui.alertErrorShowDetail') != false)
                    $('<a>').text('查看错误详细信息').css({ fontSize: '10px', position: 'absolute', top: '20px', right: '20px', cursor: 'pointer' }).click(function () {
                        window.open('', '', '').document.write(detail);
                    }).appendTo(modalEl);
            });
        }
    }
    _sui.alertError = alertError;
    function confirm(msg, title, okText, cancelText) {
        if (title === void 0) { title = '<i class="help circle icon"></i>请确认'; }
        if (okText === void 0) { okText = null; }
        if (cancelText === void 0) { cancelText = null; }
        var actionTexts = ["<div class=\"ui cancel button mini\">".concat(_locale.common.cancel, "</div>"), "<div class=\"ui approve positive right labeled icon button mini\">".concat(_locale.common.ok, "<i class=\"checkmark icon\"></i></div>")];
        if (cancelText) {
            var t = $(actionTexts[0]);
            t.html(cancelText);
            actionTexts[0] = t;
        }
        if (okText) {
            var t = $(actionTexts[1]);
            t.html(okText);
            actionTexts[1] = t;
        }
        return new Promise(function (resolve, reject) {
            var acts = [{
                    html: actionTexts[0], fn: function () { setTimeout(function () { return reject(false); }, 200); }
                }, {
                    html: actionTexts[1], fn: function () { setTimeout(function () { return resolve(true); }, 200); }
                }];
            var modal, keyfn = function (e) {
                if (e.which == 27)
                    modal.find('.cancel.button').click();
                else if (e.which == 13)
                    modal.find('.approve.button').click();
            };
            $(document).keyup(keyfn);
            modal = doAlert(msg, title, acts, {
                _showAnimateOpacity: false, onHidden: function () {
                    $(document).off('keyup', keyfn);
                }
            });
        }).catch();
    }
    _sui.confirm = confirm;
    function prompt(options, okFn, cancelFn) {
        if (cancelFn === void 0) { cancelFn = null; }
        if (typeof options === 'string')
            options = { title: options };
        var title = options.title, value = options.value || '', allowEmpty = options.allowEmpty === true, placeholder = options.placeholder || '', inputType = options.inputType || 'text';
        var inputTagName = options.inputTagName || 'input';
        var form = options.el || "<div class=\"ui form\">\n            <div class=\"field\">\n                <".concat(inputTagName, " id=\"_promptInput_\" type=\"").concat(inputType, "\" placeholder=\"").concat(placeholder, "\" value=\"").concat(value, "\"></").concat(inputTagName, ">\n      </div>\n      </div>");
        var okBtn = $('<div class="ui green right labeled icon button mini"></div>').text(_locale.common.ok).append('<i class="checkmark icon"></i>');
        if (!_util.isjquery(form))
            form = $(form);
        var inputEl = form.find('#_promptInput_');
        if (!inputEl.length)
            inputEl = form.find('input');
        if (!inputEl.length)
            inputEl = form.find('textarea');
        if (value)
            inputEl.val(value);
        var ok = function (alertModalEl) {
            var text = inputEl.val();
            text = text.trim();
            if (text.length || allowEmpty) {
                if (okFn(text) !== false)
                    alertModalEl.modal('hide');
            }
        };
        var acts = [{
                html: "<div class=\"ui cancel button mini\">".concat(_locale.common.cancel, "</div>"), fn: cancelFn
            }, {
                html: okBtn, fn: ok
            }];
        this.doAlert(form, title, acts, Object.assign({
            _showAnimateOpacity: false,
            onShow: function () {
                var text = inputEl;
                if (text.is('input'))
                    text.keypress(function (e) {
                        if (e.which === 13 && !okBtn.hasClass('disabled'))
                            okBtn.click();
                    });
                text.select();
                if (!allowEmpty) {
                    function ifempty() {
                        okBtn.toggleClass('disabled', !text.val());
                    }
                    ifempty();
                    text.on('input', ifempty);
                }
            }
        }, options.alertOptions));
    }
    _sui.prompt = prompt;
    var curretnMessageBoxInstances = [];
    function message(content, title, autoHide, hideInfoIcon) {
        if (autoHide === void 0) { autoHide = true; }
        if (hideInfoIcon === void 0) { hideInfoIcon = false; }
        var container = $('#message-container.message-container');
        if (!container.length)
            container = $('body');
        if (!content && !title) {
            container.empty();
            curretnMessageBoxInstances.length = 0;
            return;
        }
        var box = $(Templates.messageBoxEl).appendTo(container);
        if (hideInfoIcon)
            box.find('.icon').remove();
        box.___title = title || '';
        box.___content = content || '';
        box.find('.header').html(box.___title);
        box.find('p').html(box.___content);
        var samePos = curretnMessageBoxInstances.findIndex(function (cb) { return cb.___title == box.___title && cb.___content == box.___content; });
        if (samePos !== -1) {
            var same = curretnMessageBoxInstances[samePos];
            clearTimeout(same.___timer);
            same.remove();
            curretnMessageBoxInstances.splice(samePos, 1);
        }
        function hide() {
            box.transition({
                animation: 'fade out', onComplete: function () {
                    box.remove();
                }
            });
            var pos = curretnMessageBoxInstances.indexOf(box);
            if (pos != -1)
                curretnMessageBoxInstances.splice(pos, 1);
        }
        curretnMessageBoxInstances.push(box);
        box.transition({ animation: 'fade in' });
        var messageBoxLimit = getSetting('messageBoxLimit') || 3;
        while (curretnMessageBoxInstances.length > messageBoxLimit) {
            curretnMessageBoxInstances.shift().click();
        }
        if (autoHide) {
            box.___timeout = typeof autoHide === 'number' ? autoHide : (getSetting('messageBoxTime') || 3500);
            box.___timer = setTimeout(hide, box.___timeout);
        }
        box.unbind('click').click(hide);
        return box;
    }
    _sui.message = message;
    function removeMessage(messageBox) {
        if (!messageBox)
            return;
        clearTimeout(messageBox.___timer);
        messageBox.remove();
        var pos = curretnMessageBoxInstances.indexOf(messageBox);
        if (pos !== -1) {
            curretnMessageBoxInstances.splice(pos, 1);
        }
    }
    _sui.removeMessage = removeMessage;
    function getSetting(name) {
        return window['_context'] && window['_context'].getSetting(name);
    }
    function showWindow(config, cache) {
        if (cache === void 0) { cache = false; }
        if (!config.contentStyle && config.style) {
            config.contentStyle = config.style;
            delete config.style;
        }
        config.title = config.title || config.label;
        delete config.options;
        return this.mdi(config, cache);
    }
    _sui.showWindow = showWindow;
    function closeWindow(selector, top) {
        if (top === void 0) { top = false; }
        if (top)
            return getTopSui().closeWindow(selector, false);
        if (selector instanceof Mdi)
            return selector.close();
        var el = $(selector).closest('.mdi.window');
        if (el.length)
            el[0].__componentInstance.close();
    }
    _sui.closeWindow = closeWindow;
    var Mdi = (function (_super) {
        __extends(Mdi, _super);
        function Mdi(options) {
            if (options === void 0) { options = {}; }
            var _this = _super.call(this, $('body'), options) || this;
            _this.template = "\n            <div class=\"mdi window\">\n                <div class=\"header\">\n                <i class=\"icon\"></i>\n                <span class=\"title\"></span>\n                <span class=\"btns\"></i><i class=\"window minimize icon\"></i><i class=\"window maximize icon\"></i><i class=\"window close icon\"></i></span>\n                </div>\n                <div class=\"mdicontent\">\n                </div>\n            </div>\n        ";
            _this.title = '';
            _this.icon = 'comment alternate';
            _this.content = '';
            _this.contentUrl = '';
            _this.url = '';
            _this.width = 0;
            _this.height = 0;
            _this.x = 0;
            _this.y = 0;
            _this.miniable = true;
            _this.maxable = true;
            _this.closable = true;
            _this.resizable = true;
            return _this;
        }
        Mdi.prototype.doRender = function (el, pre) {
            var _this = this;
            var dimmer = Mdi.dimmer;
            if (!dimmer) {
                dimmer = Mdi.dimmer = $('<div class="mdidimmer">').appendTo($('body'));
            }
            el.appendTo(dimmer).css('pointer-events', 'all');
            var w = el;
            w.css('background-color', $('body').css('background-color'));
            if (this.width)
                w.css('width', this.width);
            if (this.height)
                w.css('height', this.height);
            var posed = false;
            if (this.x) {
                w.css('left', this.x);
                posed = true;
            }
            if (this.y) {
                w.css('top', this.y);
                posed = true;
            }
            if (this.options.top !== undefined) {
                w.css('top', this.options.top);
                posed = true;
            }
            if (this.options.bottom !== undefined) {
                w.css('bottom', this.options.bottom);
                posed = true;
            }
            if (this.options.left !== undefined) {
                w.css('left', this.options.left);
                posed = true;
            }
            if (this.options.right !== undefined) {
                w.css('right', this.options.right);
                posed = true;
            }
            w.find('.header > .icon').addClass(this.icon).dblclick(function () {
                var iframe = w.find('.mdicontent iframe');
                if (iframe.length) {
                    window.open(iframe.attr('src'), '_blank');
                }
            });
            w.find('.header .title').html(this.title || this.options.label);
            var contentEl = w.find('.mdicontent');
            if (typeof this.options.contentStyle === 'string')
                contentEl.attr('style', this.options.contentStyle);
            else if (typeof this.options.contentStyle === 'object')
                contentEl.css(this.options.contentStyle);
            if (this.actionContext) {
                this.actionContext.mdiWindow = this;
                render(contentEl, this.actionContext.action, this.actionContext);
            }
            else if (this.content)
                contentEl.html(this.content);
            else if (this.contentUrl) {
                var url = this.contentUrl;
                if (_context)
                    url = _context.fixUrl(url);
                $.get(url, function (data) { return contentEl.html(data); });
            }
            else if (this.url)
                $('<iframe allowfullscreen>').attr('src', this.url).appendTo(contentEl);
            if (!posed) {
                this.autoCenter(w);
                setTimeout(function () {
                    _this.autoCenter(w, true);
                }, 600);
            }
            w.draggable({
                addClasses: false, refreshPositions: false,
                cancel: 'a', handle: 'div.header', containment: [-2000, 0, 2000, 2000], start: function () {
                    dimmer.css('pointer-events', 'all');
                    w.css({ right: '', bottom: '' });
                }, stop: this._disableDimmerPointerEvents
            });
            if (this.resizable)
                w.resizable({
                    start: this._enableDimmerPointerEvents, stop: this._disableDimmerPointerEvents,
                    autoHide: true, minHeight: 30, minWidth: 200
                });
            w.find('.header').dblclick(function () { return _this.restore(); });
            if (this.miniable)
                w.find('.minimize.icon').click(function () { return _this.minimize(); });
            else
                w.find('.minimize.icon').remove();
            if (this.maxable)
                w.find('.maximize.icon').click(function () { return _this.toggleMax(); });
            else
                w.find('.maximize.icon').remove();
            if (this.closable)
                w.find('.close.icon').click(function () { return _this.close(); });
            else
                w.find('.close.icon').remove();
            this.toFront();
            w.mousedown(function () { return _this.toFront(); });
            _super.prototype.doRender.call(this, el, pre);
        };
        Mdi.prototype._enableDimmerPointerEvents = function () {
            Mdi.dimmer.css('pointer-events', 'all');
        };
        Mdi.prototype._disableDimmerPointerEvents = function () {
            Mdi.dimmer.css('pointer-events', 'none');
        };
        Mdi.prototype.autoCenter = function (w, secondTime) {
            if (secondTime === void 0) { secondTime = false; }
            var width = w.width() || 0, height = w.height() || 0, W = $('.mdidimmer').width(), H = $('.mdidimmer').height();
            if (width < 400)
                width = 400;
            if (height < 400)
                height = 400;
            var x = (W - width) / 2, y = (H - height) / 2;
            if (x < 0)
                x = 0;
            if (y < 0)
                y = 0;
            if (!secondTime)
                w.css({ left: x, top: y });
            else {
                var pos = w.position();
                if (pos.left + width > W || pos.top + height > H)
                    w.css({ left: x, top: y });
            }
        };
        Mdi.prototype.minimize = function () {
            var el = this.templateEl;
            if (this.resizable)
                el.resizable('option', 'disabled', true);
            el.addClass('mini');
            var pos = el.position();
            if (pos.left && pos.left < 0)
                el.css('left', 0);
            if (pos.top && pos.top < 0)
                el.css('top', 0);
            el.transition('pulse');
            this.trigger('minimize');
        };
        Mdi.prototype.maximize = function () {
            var w = this.templateEl;
            if (w.hasClass('full'))
                return;
            w.attr({
                'data-t': w.css('top'),
                'data-l': w.css('left'),
                'data-r': w.css('right'),
                'data-b': w.css('bottom'),
                'data-w': w.css('width'),
                'data-h': w.css('height')
            }).addClass('full').css({
                'top': '0',
                'left': '0',
                'right': '0',
                'bottom': '0',
                'width': '100%',
                'height': '100%'
            });
            if (this.resizable)
                w.resizable('option', 'disabled', true);
            this.trigger('maximize');
        };
        Mdi.prototype.close = function () {
            var w = this.templateEl;
            if (this.closeCallback)
                this.closeCallback(w);
            else
                w.fadeOut(function () {
                    destroyComponents(w[0]);
                    w.remove();
                });
            this.trigger('close');
        };
        Mdi.prototype.show = function () {
            this.templateEl.transition('scale in');
            this.trigger('show');
        };
        Mdi.prototype.restore = function () {
            var w = this.templateEl;
            w.removeClass('mini').removeClass('full');
            if (this.resizable)
                w.resizable('option', 'disabled', false);
            var top = w.attr('data-t'), left = w.attr('data-l'), right = w.attr('data-r'), bottom = w.attr('data-b'), width = w.attr('data-w'), height = w.attr('data-h');
            if (top) {
                w.css({ top: top, left: left, right: right, bottom: bottom, width: width, height: height });
            }
            var pos = w.position();
            if (pos.top && pos.top < 0)
                w.css('top', 0);
            this.trigger('restore');
        };
        Mdi.prototype.toggleMax = function () {
            this.templateEl.hasClass('full') ? this.restore() : this.maximize();
        };
        Mdi.prototype.toFront = function () {
            $('.mdi.window').removeClass('active');
            this.templateEl.addClass('active');
        };
        Mdi.dimmer = null;
        return Mdi;
    }(Component));
    _sui.Mdi = Mdi;
    var instanceCache = {};
    var currentMdiId = 0;
    function mdi(options, cache) {
        if (cache === void 0) { cache = false; }
        var w, id = options.id || options.name || options.title;
        if (cache)
            w = instanceCache[id];
        if (!w) {
            if (cache)
                options.closeCallback = function (w) { w.transition('scale out'); ; };
            w = new Mdi(options).render();
            if (cache) {
                instanceCache[id] = w;
            }
        }
        else {
            w.restore();
        }
        w.show();
        return w;
    }
    _sui.mdi = mdi;
    function minimizeAllMdiWindow(top) {
        if (top === void 0) { top = false; }
        if (top)
            return getTopSui().minimizeAllMdiWindow(false);
        var visible = $('.mdi.window:visible').not('.hidden');
        visible.each(function () {
            this.__componentInstance.minimize();
        });
        return visible;
    }
    _sui.minimizeAllMdiWindow = minimizeAllMdiWindow;
    var Carousel = (function (_super) {
        __extends(Carousel, _super);
        function Carousel() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.template = "<div style=\"display:none;\"></div>";
            _this.defaultCarouselOptions = {
                bringToFront: true
            };
            return _this;
        }
        Carousel.prototype.prepare = function () {
            _super.prototype.prepare.call(this);
            if (!$.prototype.Cloud9Carousel)
                return _util.loadScript('lib/jquery/cloud9carousel.js');
        };
        Carousel.prototype.doRender = function (el, pre) {
            var _this = this;
            if (this.data)
                this.data.forEach(function (item) {
                    if (_this.itemRenderer)
                        item = _this.itemRenderer(item);
                    $(item).addClass('cloud9-item').appendTo(el);
                });
            el.Cloud9Carousel(Object.assign({
                onLoaded: function () {
                    el.show();
                }
            }, this.defaultCarouselOptions, this.options));
        };
        return Carousel;
    }(Component));
    function carousel(parentEl, options) {
        if (options === void 0) { options = null; }
        return new Carousel(parentEl, options).render();
    }
    _sui.carousel = carousel;
    function showSelect(gridFactory, labelField, title, selected, modalOptions) {
        if (selected === void 0) { selected = null; }
        if (modalOptions === void 0) { modalOptions = null; }
        return new Promise(function (resolve, reject) {
            var header = $('<div class="header">').html(title);
            var content = $('<div class="grid-select-container">');
            var gridsOrPromise = gridFactory(content);
            _util.dataCallback(function (grids) {
                if (!Array.isArray(grids))
                    grids = [grids];
                var labels = $('<div style="text-align:start;"></div>');
                var selects = [];
                var cancelBtn = $('<div class="ui cancel button mini"></div>'), okBtn = $('<div class="ui primary ok button mini disabled"></div>');
                cancelBtn.text(_locale.common.cancel);
                okBtn.text(_locale.common.ok);
                var resetBtn = $('<div class="ui reset button mini">').text(_locale.common.reset);
                grids.forEach(function (grid) {
                    grid._initDataForReset = grid.data;
                });
                resetBtn.click(function () {
                    selects.length = 0;
                    labels.empty();
                    grids.forEach(function (grid) {
                        grid.checkAll(false);
                        if (grid._initDataForReset)
                            grid.setData(grid._initDataForReset);
                    });
                });
                modalOptions = Object.assign({ fixContentHeight: true }, modalOptions, { closable: false, onApprove: function (e) { return resolve(selects); }, onDeny: function (e) { return reject(); } });
                _sui.showModal(header, content, [labels, cancelBtn, resetBtn, okBtn], modalOptions);
                function add(item) {
                    if (selects.find(function (s) { return s.id === item.id; }))
                        return;
                    selects.push(item);
                    okBtn.removeClass('disabled');
                    var text = typeof labelField === 'function' ? labelField(item) : item[labelField];
                    var label = $('<div class="ui label mini">').css('margin-bottom', '2px').text(text).data('id', item.id).appendTo(labels);
                    $('<i class="delete icon">').appendTo(label).click(function () {
                        remove(item);
                        grids.forEach(function (grid) {
                            grid.checkRow(item, false);
                        });
                    });
                }
                function remove(item) {
                    if (!selects.find(function (s) { return s.id === item.id; }))
                        return;
                    selects.splice(selects.findIndex(function (s) { return s.id === item.id; }), 1);
                    okBtn.toggleClass('disabled', selects.length === 0);
                    labels.children().filter(function () {
                        return $(this).data('id') === item.id;
                    }).remove();
                }
                grids.forEach(function (grid) {
                    grid.on('checkchange', function (e, data) {
                        if (data.checked && grids.length > 1 && grid.rowCheckable === 'single' && selects.length) {
                            remove(selects[0]);
                            grids.forEach(function (g) {
                                if (g !== grid)
                                    g.checkAll(false);
                            });
                        }
                        data.checked ? add(data.row) : remove(data.row);
                    });
                });
                if (selected) {
                    if (!Array.isArray(selected))
                        selected = [selected];
                    grids.forEach(function (grid) {
                        var fn = function () {
                            grid.rendered().then(function () {
                                selected.forEach(function (s) {
                                    grid.checkRow(s);
                                });
                                grid.off('setdata', fn);
                            });
                        };
                        if (grid.data && Array.isArray(grid.data))
                            fn();
                        else
                            grid.on('setdata', fn);
                    });
                }
            }, gridsOrPromise);
        });
    }
    _sui.showSelect = showSelect;
    function showDataSelects(data, title, selected, labelField, gridOpt, modalOpt) {
        if (selected === void 0) { selected = undefined; }
        if (labelField === void 0) { labelField = undefined; }
        if (gridOpt === void 0) { gridOpt = undefined; }
        if (modalOpt === void 0) { modalOpt = undefined; }
        labelField = labelField || fetchLabel;
        var gridFactory = function (el) {
            return grid(el, Object.assign({
                data: data, columns: [
                    {
                        name: 'name', label: '名称', getLabel: labelField, showMaxLength: false
                    }
                ]
            }, gridOpt));
        };
        return showSelect(gridFactory, labelField, title, selected, modalOpt);
    }
    _sui.showDataSelects = showDataSelects;
    function showDataSelect(data, title, selected, labelField, gridOpt, modalOpt) {
        if (selected === void 0) { selected = undefined; }
        if (labelField === void 0) { labelField = undefined; }
        if (gridOpt === void 0) { gridOpt = undefined; }
        if (modalOpt === void 0) { modalOpt = undefined; }
        return showDataSelects(data, title, selected ? [selected] : selected, labelField, Object.assign({ rowCheckable: 'single' }, gridOpt), modalOpt).then(function (os) {
            if (Array.isArray(os))
                os = os[0];
            return os;
        });
    }
    _sui.showDataSelect = showDataSelect;
    var Templates = (function () {
        function Templates() {
        }
        Templates.alertModal = "\n        <div class=\"ui tiny modal\">\n            <div class=\"ui small header\"></div>\n            <div class=\"content\"></div>\n            <div class=\"actions\"></div>\n        </div>\n        ";
        Templates.messageBoxEl = "\n                <div class=\"ui small black icon message\" style=\"width:auto;max-width:80vw;\">\n                    <i class=\"info icon\" style=\"font-size:1em;\"></i>\n                    <div class=\"content\">\n                    <div class=\"header\"></div>\n                    <p></p>\n                    </div>\n                </div>\n            ";
        return Templates;
    }());
    _sui.Templates = Templates;
    var Progress = (function (_super) {
        __extends(Progress, _super);
        function Progress(parentEl, options) {
            if (options === void 0) { options = null; }
            var _this = _super.call(this, 'progress', parentEl, Object.assign({}, { class: 'ui active tiny indicating' }, options)) || this;
            _this.template = "\n            <div class=\"ui progress\">\n                <div class=\"bar\"><div class=\"progress\"></div></div><div class=\"label\"></div>\n            </div>\n        ";
            _this.total = 100;
            _this.label = '';
            return _this;
        }
        Progress.prototype.doRender = function (el, prepare) {
            var labelEl = this.getSuiEl().children('.label');
            if (this.label === false)
                labelEl.remove();
            else
                labelEl.html(this.label);
            if (this.labelColor)
                labelEl.css('color', this.labelColor);
            if (this.state)
                this.getSuiEl().addClass(this.state);
            this.suiOptions = { label: this.label, total: this.total, value: this.value };
            _super.prototype.doRender.call(this, el, prepare);
        };
        Progress.prototype.setTotal = function (v) {
            this.behavior('set total', v);
        };
        Progress.prototype.setValue = function (v, label) {
            if (label === void 0) { label = undefined; }
            this.behavior('set progress', v);
            if (label !== undefined)
                this.setLabel(label);
        };
        Progress.prototype.setPercent = function (v, label) {
            if (label === void 0) { label = undefined; }
            this.behavior('set percent', v);
            if (label !== undefined)
                this.setLabel(label);
        };
        Progress.prototype.setLabel = function (v) {
            this.behavior('set label', v);
        };
        Progress.prototype.setState = function (v, b) {
            var method = b === false ? 'remove' : 'set';
            method += ' ' + v;
            this.behavior(method, v);
            this.state = v;
        };
        Progress.prototype.increment = function (v, label) {
            if (label === void 0) { label = undefined; }
            this.behavior('increment', v);
            if (label)
                this.setLabel(label);
        };
        Progress.prototype.complete = function (label) {
            if (label === void 0) { label = undefined; }
            if (label)
                this.setLabel(label);
            this.setPercent(100);
            this.behavior('complete');
            if (this.completeCallback)
                this.completeCallback(this.getEl());
        };
        Progress.prototype.reset = function (label) {
            if (label === void 0) { label = undefined; }
            this.behavior('reset');
            if (label)
                this.setLabel(label);
        };
        return Progress;
    }(SuiComponent));
    _sui.Progress = Progress;
    function progress(el, options) {
        if (options === void 0) { options = {}; }
        return new Progress(el, options).render();
    }
    _sui.progress = progress;
    function contextMenu(el, options) {
        _util.loadCss(['lib/jquery/context-menu/jquery.contextMenu.min.css']);
        _util.loadScripts(['lib/jquery/context-menu/jquery.ui.position.min.js', 'lib/jquery/context-menu/jquery.contextMenu.min.js'], function () {
            return !!$.contextMenu;
        }).then(function () {
            var selector = el;
            if (!(typeof selector === 'string')) {
                selector = _util.getFullSelector(el);
            }
            options = Object.assign({ selector: selector }, options);
            return $.contextMenu(options);
        });
    }
    _sui.contextMenu = contextMenu;
    var CheckBoxList = (function (_super) {
        __extends(CheckBoxList, _super);
        function CheckBoxList(parentEl, options) {
            if (options === void 0) { options = {}; }
            var _this = _super.call(this, parentEl, options) || this;
            _this.options = options;
            _this.template = '<div class="form inline fields">';
            _this.showCheckAll = true;
            _this.checkInputList = [];
            return _this;
        }
        CheckBoxList.prototype.prepare = function () {
            return this.options.data;
        };
        CheckBoxList.prototype.doRender = function (el, prepareDatas) {
            var _this = this;
            if (this.showCheckAll) {
                var data = {};
                data[this.titleField] = '全选';
                data[this.valueField] = 'all';
                var item = this.createItem(data);
                var input = item.find('input');
                if (input) {
                    this.checkInputList.push(input);
                    this.allCheckInput = input;
                    input.change(function (e) {
                        var checked = $(e.target).prop('checked');
                        _this.checkInputList.forEach(function (checkInput) {
                            if (e.target != checkInput) {
                                $(checkInput).prop('checked', checked);
                            }
                        });
                        _this.trigger('change', _this.getSelectValues());
                    });
                }
                item.appendTo(this.templateEl);
            }
            if (prepareDatas) {
                prepareDatas.forEach(function (prepareData) {
                    var item = _this.createItem(prepareData);
                    var input = item.find('input');
                    if (input) {
                        _this.checkInputList.push(input);
                        input.change(function (e) {
                            _this.trigger('change', _this.getSelectValues());
                        });
                    }
                    item.appendTo(_this.templateEl);
                });
            }
            if (this.value) {
                this.setValue(this.value);
            }
            this.templateEl.appendTo(el);
        };
        CheckBoxList.prototype.createItem = function (itemData) {
            var lable = itemData[this.titleField];
            var value = itemData[this.valueField];
            var itme = $(' <div class="ui checked checkbox" style="margin: 5px;">' +
                ' <input type="checkbox" value=' + value + ' id="' + value + '">' +
                ' <label>' + lable + '</label>' +
                ' </div>');
            return itme;
        };
        CheckBoxList.prototype.setValue = function (vals) {
            var _this = this;
            if (!vals) {
                return;
            }
            if (!Array.isArray(vals)) {
                vals = [vals];
            }
            vals.forEach(function (val) {
                _this.checkInputList.forEach(function (checkInput) {
                    if (val && $(checkInput).val() && val == $(checkInput).val()) {
                        $(checkInput).prop('checked', true);
                    }
                });
            });
        };
        CheckBoxList.prototype.getSelectValues = function () {
            var _this = this;
            this.value = [];
            this.checkInputList.forEach(function (checkInput) {
                var checked = $(checkInput).prop('checked');
                if (_this.allCheckInput != checkInput && checked) {
                    _this.value.push($(checkInput).val());
                }
            });
            return this.value;
        };
        return CheckBoxList;
    }(Component));
    _sui.CheckBoxList = CheckBoxList;
    function createCheckBoxList(el, data, value, titleField, valueField, showCheckAll, options) {
        if (value === void 0) { value = null; }
        if (titleField === void 0) { titleField = 'name'; }
        if (valueField === void 0) { valueField = 'id'; }
        if (showCheckAll === void 0) { showCheckAll = true; }
        if (options === void 0) { options = {}; }
        Object.assign(options, {
            titleField: titleField,
            valueField: valueField,
            showCheckAll: showCheckAll,
            data: data,
            value: value
        });
        var checkBoxList = new CheckBoxList(el, options).render();
        return checkBoxList;
    }
    _sui.createCheckBoxList = createCheckBoxList;
})(_sui || (_sui = {}));
//# sourceMappingURL=_sui.js.map