using ET;
using Microsoft.Office.Interop.Excel;
using Microsoft.Office.Interop.Visio;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Windows.Forms;
using Page = Microsoft.Office.Interop.Visio.Page;
using TextBox = System.Windows.Forms.TextBox;

namespace HyExcelVsto.Module.Common
{
    public static partial class VisioHelperFunctions
    {
        #region 写入图衔
        /// <summary>
        /// 在新线程中执行写入图衔操作
        /// </summary>
        /// <param name="filePathRange">文件路径范围</param>
        /// <param name="pageNameRange">页面名称范围</param>
        /// <param name="targetDirectory">目标目录</param>
        /// <param name="isDirectSave">是否直接保存</param>
        /// <param name="writeRange图纸名称">图纸名称写入范围</param>
        /// <param name="writeRange单位比例">单位比例写入范围</param>
        /// <param name="writeRange出图日期">出图日期写入范围</param>
        /// <param name="writeRange图纸编号">图纸编号写入范围</param>
        /// <param name="sourceVisioFilePath">Visio文件路径</param>
        /// <param name="tuxianType">图衔类型</param>
        /// <param name="scaleKeywords">比例关键字</param>
        /// <param name="useScaleKeywords">是否使用比例关键字</param>
        /// <param name="deleteLeftContent">是否删除左侧内容</param>
        /// <param name="deleteDrawingNumber">是否删除图号</param>
        /// <param name="textBoxError">错误信息文本框</param>
        /// <param name="textBoxProgress">进度信息文本框</param>
        public static void 写入图衔PerRowsThread(
            Range filePathRange,
            Range pageNameRange,
            string targetDirectory,
            bool isDirectSave,
            Range writeRange图纸名称,
            Range writeRange单位比例,
            Range writeRange出图日期,
            Range writeRange图纸编号,
            string sourceVisioFilePath,
            string tuxianType,
            string scaleKeywords,
            bool useScaleKeywords,
            bool deleteLeftContent,
            bool deleteDrawingNumber,
            TextBox textBoxError,
            TextBox textBoxProgress)
        {
            Thread thread = new(
                () =>
                {
                    try
                    {
                        写入图衔PerRows(
                            filePathRange,
                            pageNameRange,
                            targetDirectory,
                            isDirectSave,
                            writeRange图纸名称,
                            writeRange单位比例,
                            writeRange出图日期,
                            writeRange图纸编号,
                            sourceVisioFilePath,
                            tuxianType,
                            scaleKeywords,
                            useScaleKeywords,
                            deleteLeftContent,
                            deleteDrawingNumber,
                            textBoxError,
                            textBoxProgress);
                    }
                    catch (Exception ex)
                    {
                        textBoxError.WriteLog($"执行过程中发生异常：{ex.Message}");
                    }
                });
            thread.SetApartmentState(ApartmentState.STA);
            thread.Start();
        }

        /// <summary>
        /// 执行写入图衔操作
        /// </summary>
        /// <remarks>
        /// 此方法处理多个Visio文件，写入图衔信息。它会打开Visio应用程序，逐个处理文件和页面， 并在完成后关闭Visio应用程序。方法包含错误处理和进度报告功能。
        /// </remarks>
        public static void 写入图衔PerRows(
            Range filePathRange,
            Range pageNameRange,
            string targetDirectory,
            bool isDirectSave,
            Range writeRange图纸名称,
            Range writeRange单位比例,
            Range writeRange出图日期,
            Range writeRange图纸编号,
            string sourceVisioFilePath,
            string tuxianType,
            string scaleKeywords,
            bool useScaleKeywords,
            bool deleteLeftContent,
            bool deleteDrawingNumber,
            TextBox textBoxError,
            TextBox textBoxProgress)
        {
            #region 变量值获取，信息初始化
            if (filePathRange == null)
            {
                textBoxProgress.WriteLog("请选择文件路径所在的列");
                return;
            }

            if (pageNameRange == null)
            {
                // 如果页面名称为空，则对所有前景页进行操作
                writeRange图纸名称 = null;
                writeRange出图日期 = null;
                writeRange图纸编号 = null;
                writeRange单位比例 = null;
            }
            else if (pageNameRange.Columns.Count == 1 && filePathRange.Columns.Count > 1)
            {
                pageNameRange = filePathRange.Columns[2];
            }

            filePathRange = filePathRange.OptimizeRangeSize();

            // 检查是否有操作内容
            if (string.IsNullOrEmpty(sourceVisioFilePath) &&
                writeRange图纸名称 == null &&
                writeRange出图日期 == null &&
                writeRange图纸编号 == null &&
                writeRange单位比例 == null &&
                !useScaleKeywords)
            {
                MessageBox.Show("没有选择任何操作内容，请选择至少一项操作。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            #endregion 变量值获取，信息初始化

            // 根据tuxianType的值选择不同的字典
            Dictionary<string, RectanglPosition> selectedPositions = tuxianType switch
            {
                "上海院" => VisShapePosition.SH相对位置,
                "南方院" => VisShapePosition.NF相对位置,
                _ => null
            };

            if (selectedPositions == null)
            {
                textBoxError.WriteLog("请选择正确的图衔名称：上海院或南方院");
                return;
            }

            Microsoft.Office.Interop.Visio.Application visioApp = null;
            try
            {
                visioApp = ETVisio.CreateApplication(false);
                Document docPrev = null;
                Range filePathCellPrev = null;

                int rowCount = filePathRange.GetVisibleRowCount();
                int currentRow = 1;

                Document sourceDoc = null;
                if (!string.IsNullOrEmpty(sourceVisioFilePath) && File.Exists(sourceVisioFilePath))
                {
                    sourceDoc = ETVisio.Open(visioApp, sourceVisioFilePath, textBoxProgress, false);
                    if (sourceDoc == null)
                    {
                        textBoxError.WriteLog($"打开来源Visio文件失败，结束执行。文件名：{System.IO.Path.GetFileName(sourceVisioFilePath)}");
                        return;
                    }
                }

                foreach (Range row in filePathRange.Rows)
                {
                    if (row.EntireRow.Hidden)
                        continue;

                    Range filePathCell = filePathRange.Cells[row.Row, 1];
                    Range pageNameCell = pageNameRange?.Cells[row.Row, 1];
                    if (ETExcelExtensions.IsCellEmpty(filePathCell))
                        continue;

                    string filePath = filePathCell.Value2.ToString();
                    string pageName = pageNameCell?.Value2?.ToString();
                    string fileName = System.IO.Path.GetFileName(filePath);

                    textBoxProgress.WriteLog(
                        $"{currentRow++}/{rowCount}.正在处理 {DateTime.Now.ToString("HH:mm:ss")} ：{fileName}");

                    Document doc = null;
                    try
                    {
                        doc = ETVisio.FindOpenDocumentByPath(visioApp, filePath);
                        if (doc == null)
                        {
                            if (isDirectSave)
                                docPrev?.Save(true);
                            else
                                docPrev?.SaveToAnotherDirectory(targetDirectory, true);
                            if (filePathCellPrev != null)
                                filePathCellPrev.Format条件格式警示色(EnumWarningColor.蓝绿);

                            doc = ETVisio.Open(visioApp, filePath, textBoxProgress, false);
                            docPrev = doc;
                            filePathCellPrev = filePathCell;
                        }
                        else
                        {
                            // 文件已经打开，更新引用
                            docPrev = doc;
                            filePathCellPrev = filePathCell;
                        }

                        if (doc == null)
                        {
                            int filterRowNumber = filePathRange.GetAutoFilterRowNumber();
                            if (row.Row > filterRowNumber)
                            {
                                textBoxError.WriteLog($"出现意外错误，无法处理文件：{fileName}");
                                filePathCell.Format条件格式警示色(EnumWarningColor.虾红);
                            }
                            continue;
                        }

                        // 准备要删除的图衔内容的键
                        string[] keysToFilter = tuxianType switch
                        {
                            "上海院" => new string[] { "日期", "左侧" },
                            _ => new string[] { "工程名称", "日期", "左侧" } // 默认为南方院
                        };

                        if (deleteLeftContent && !keysToFilter.Contains("左侧"))
                        {
                            keysToFilter = keysToFilter.Append("左侧").ToArray();
                        }

                        if (deleteDrawingNumber && !keysToFilter.Contains("图号"))
                        {
                            keysToFilter = keysToFilter.Append("图号").ToArray();
                        }

                        Dictionary<string, RectanglPosition> filteredPositions = ETVisio.CreateFilteredDictionary(
                            selectedPositions,
                            keysToFilter);


                        // 处理文件的代码
                        ProcessVisioFile(
                            doc,
                            pageName,
                            sourceDoc,
                            selectedPositions,
                            writeRange单位比例,
                            useScaleKeywords,
                            scaleKeywords,
                            filteredPositions,
                            textBoxError,
                            textBoxProgress);

                        // 处理特定页面的代码
                        if (pageNameRange != null)
                        {
                            ProcessSpecificPage(
                                doc,
                                pageName,
                                selectedPositions,
                                writeRange图纸名称,
                                writeRange出图日期,
                                writeRange图纸编号,
                                deleteDrawingNumber,
                                row,
                                textBoxError,
                                textBoxProgress);
                        }
                    }
                    catch (Exception ex)
                    {
                        textBoxError.WriteLog($"处理文件 {fileName} 时发生错误：{ex.Message}");
                        filePathCell.Format条件格式警示色(EnumWarningColor.虾红);
                    }
                }

                // 保存最后一个文档
                if (isDirectSave)
                    docPrev?.Save(true);
                else
                    docPrev?.SaveToAnotherDirectory(targetDirectory, true);

                if (filePathCellPrev != null)
                    filePathCellPrev.Format条件格式警示色(EnumWarningColor.蓝绿);

                sourceDoc?.Close(false);
            }
            catch (Exception ex)
            {
                textBoxError.WriteLog($"执行过程中发生异常：{ex.Message}");
            }
            finally
            {
                if (visioApp != null)
                {
                    visioApp.Quit();
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(visioApp);
                }
                textBoxProgress.WriteLog("执行完成");
            }
        }

        /// <summary>
        /// 处理Visio文件
        /// </summary>
        /// <param name="doc">要处理的Visio文档</param>
        /// <param name="pageName">页面名称</param>
        /// <param name="sourceDoc">源文档</param>
        /// <param name="selectedPositions">选定的位置字典</param>
        /// <param name="writeRange单位比例">单位比例的写入范围</param>
        /// <param name="useScaleKeywords">是否使用比例关键字</param>
        /// <param name="scaleKeywords">比例关键字</param>
        /// <param name="filteredPositions">过滤后的位置字典</param>
        /// <param name="textBoxError">错误信息文本框</param>
        /// <param name="textBoxProgress">进度信息文本框</param>
        public static void ProcessVisioFile(
           Document doc,
            string pageName,
           Document sourceDoc,
            Dictionary<string, RectanglPosition> selectedPositions,
            Range writeRange单位比例,
            bool useScaleKeywords,
            string scaleKeywords,
            Dictionary<string, RectanglPosition> filteredPositions,
            TextBox textBoxError,
            TextBox textBoxProgress)
        {
            // 创建一个字典来跟踪已处理的背景页
            Dictionary<int, bool> processedBackgrounds = [];

            if (string.IsNullOrEmpty(pageName))
            {
                // 对所有前景页进行操作
                for (int i = 1; i <= doc.Pages.Count; i++)
                {
                    Page page = (Page)doc.Pages[i];

                    // 在处理每个页面之前
                    //ETVisio.JumpToPage(page);

                    ETVisio.FitPageToWindow(page.Application);

                    //仅处理前景页面
                    if (ETVisio.IsBackgroundPage(page))
                        continue;

                    ProcessPageWithBackgroundCheck(
                        page,
                        sourceDoc,
                        selectedPositions,
                        writeRange单位比例,
                        useScaleKeywords,
                        scaleKeywords,
                        filteredPositions,
                        processedBackgrounds,
                        textBoxError,
                        textBoxProgress);
                }
            }
            else
            {
                Page page = ETVisio.FindPage(doc, pageName);
                if (page == null)
                {
                    textBoxError.WriteLog($"文件 {doc.Name} 中没有找到页面 {pageName}");
                    return;
                }

                textBoxProgress.WriteLog($"，{pageName}", false);

                ProcessPageWithBackgroundCheck(
                    page,
                    sourceDoc,
                    selectedPositions,
                    writeRange单位比例,
                    useScaleKeywords,
                    scaleKeywords,
                    filteredPositions,
                    processedBackgrounds,
                    textBoxError,
                    textBoxProgress);
            }
        }

        /// <summary>
        /// 处理特定页面
        /// </summary>
        /// <param name="doc">要处理的Visio文档</param>
        /// <param name="pageName">页面名称</param>
        /// <param name="selectedPositions">选定的位置字典</param>
        /// <param name="writeRange图纸名称">图纸名称写入范围</param>
        /// <param name="writeRange出图日期">出图日期写入范围</param>
        /// <param name="writeRange图纸编号">图纸编号写入范围</param>
        /// <param name="deleteDrawingNumber">是否删除图号</param>
        /// <param name="row">当前行</param>
        /// <param name="textBoxError">错误信息文本框</param>
        /// <param name="textBoxProgress">进度信息文本框</param>
        public static void ProcessSpecificPage(
           Document doc,
            string pageName,
            Dictionary<string, RectanglPosition> selectedPositions,
            Range writeRange图纸名称,
            Range writeRange出图日期,
            Range writeRange图纸编号,
            bool deleteDrawingNumber,
            Range row,
            TextBox textBoxError,
            TextBox textBoxProgress)
        {
            Page page = ETVisio.FindPage(doc, pageName);
            if (page == null)
            {
                textBoxError.WriteLog($"文件 {doc.Name} 中没有找到页面 {pageName}");
                return;
            }

            textBoxProgress.WriteLog($"，{pageName}", false);

            if (writeRange图纸名称 != null)
                ProcessShapeText(page, selectedPositions["图名"], writeRange图纸名称.Cells[row.Row, 1]);

            if (writeRange出图日期 != null)
                ProcessShapeText(page, selectedPositions["日期"], writeRange出图日期.Cells[row.Row, 1]);

            if (writeRange图纸编号 != null && !deleteDrawingNumber)
                ProcessShapeText(page, selectedPositions["图号"], writeRange图纸编号.Cells[row.Row, 1]);
        }

        /// <summary>
        /// 处理页面及其背景页（如果存在）
        /// </summary>
        /// <param name="page">要处理的Visio页面</param>
        /// <param name="sourceDoc">源文档</param>
        /// <param name="selectedPositions">选定的位置字典</param>
        /// <param name="writeRange单位比例">单位比例的写入范围</param>
        /// <param name="useScaleKeywords">是否使用比例关键字</param>
        /// <param name="scaleKeywords">比例关键字</param>
        /// <param name="filteredPositions">过滤后的位置字典</param>
        /// <param name="processedBackgrounds">已处理的背景页字典</param>
        /// <param name="textBoxError">错误信息文本框</param>
        /// <param name="textBoxProgress">进度信息文本框</param>
        /// <remarks>
        /// 此方法首先处理背景页（如果存在且未处理过），然后处理前景页。 它使用背景页的ID作为唯一标识符来跟踪已处理的背景页。
        /// </remarks>
        public static void ProcessPageWithBackgroundCheck(
            Page page,
            Document sourceDoc,
            Dictionary<string, RectanglPosition> selectedPositions,
            Range writeRange单位比例,
            bool useScaleKeywords,
            string scaleKeywords,
            Dictionary<string, RectanglPosition> filteredPositions,
            Dictionary<int, bool> processedBackgrounds,
            TextBox textBoxError,
            TextBox textBoxProgress)
        {
            // 获取背景页
            Page backgroundPage = ETVisio.FindLastBackgroundPage(page);

            // 如果有背景页且未处理过，则处理背景页
            if (backgroundPage != null)
            {
                int backgroundId = backgroundPage.ID;
                if (!processedBackgrounds.ContainsKey(backgroundId))
                {
                    ProcessBackgroundPage(
                        backgroundPage,
                        sourceDoc,
                        selectedPositions,
                        filteredPositions,
                        processedBackgrounds,
                        textBoxError,
                        textBoxProgress);
                }
            }

            // 处理前景页
            ProcessForegroundPage(
                page,
                selectedPositions,
                writeRange单位比例,
                useScaleKeywords,
                scaleKeywords,
                filteredPositions,
                textBoxError,
                textBoxProgress);
        }

        /// <summary>
        /// 处理背景页
        /// </summary>
        /// <param name="backgroundPage">要处理的背景页</param>
        /// <param name="sourceDoc">源文档</param>
        /// <param name="selectedPositions">选定的位置字典</param>
        /// <param name="filteredPositions">过滤后的位置字典</param>
        /// <param name="processedBackgrounds">已处理的背景页字典</param>
        /// <param name="textBoxError">错误信息文本框</param>
        /// <param name="textBoxProgress">进度信息文本框</param>
        public static void ProcessBackgroundPage(
            Page backgroundPage,
            Document sourceDoc,
            Dictionary<string, RectanglPosition> selectedPositions,
            Dictionary<string, RectanglPosition> filteredPositions,
            Dictionary<int, bool> processedBackgrounds,
            TextBox textBoxError,
            TextBox textBoxProgress)
        {
            if (sourceDoc != null)
            {
                // 使用FindPage函数查找与backgroundPage同名的页面
                dynamic sourcePage = ETVisio.FindPage(sourceDoc, backgroundPage.Name);
                if (sourcePage != null)
                {
                    string isCopySuccess = ETVisio.Copy图衔SinglePage(sourcePage, backgroundPage, null);
                    if (!string.IsNullOrEmpty(isCopySuccess))
                    {
                        textBoxError.WriteLog($"[{sourceDoc.Name}] 背景页{backgroundPage.Name}复制失败,{isCopySuccess}");
                        textBoxProgress.WriteLog($"[{sourceDoc.Name}] ，背景页{backgroundPage.Name}复制失败", false);
                        return;
                    }
                    processedBackgrounds[backgroundPage.ID] = true;
                }
                else
                {
                    textBoxError.WriteLog($"[{sourceDoc.Name}] 源文件中未找到名为{backgroundPage.Name}的背景页");
                    textBoxProgress.WriteLog($"[{sourceDoc.Name}] ，背景页{backgroundPage.Name}复制失败", false);
                    return;
                }
            }
        }

        /// <summary>
        /// 处理前景页
        /// </summary>
        public static void ProcessForegroundPage(
            Page page,
            Dictionary<string, RectanglPosition> selectedPositions,
            Range writeRange单位比例,
            bool useScaleKeywords,
            string scaleKeywords,
            Dictionary<string, RectanglPosition> filteredPositions,
            TextBox textBoxError,
            TextBox textBoxProgress)
        {
            // 删除原有图衔内容
            ETVisio.Delete图衔子信息ForSinglePage(page, filteredPositions);

            if (writeRange单位比例 != null || useScaleKeywords)
            {
                RectangleRange rect单位比例位置_BL = ETVisio.Get图衔子图形在页面中的位置_BL(
                    page,
                    selectedPositions["单位比例"],
                    0.015,
                    0.02,
                    0.008,
                    0.008);
                if (rect单位比例位置_BL != null)
                {
                    if (writeRange单位比例 != null)
                    {
                        Range cell单位比例 = writeRange单位比例.Cells[page.Index, 1];
                        if (!ETExcelExtensions.IsCellEmpty(cell单位比例))
                            ETVisio.CreateRectangleText(
                                page,
                                ETVisio.Get图衔子图形在页面中的位置_BL(page, selectedPositions["单位比例"], 0, 0, 0, 0),
                                cell单位比例.Value.ToString());
                    }
                    else if (useScaleKeywords && !string.IsNullOrWhiteSpace(scaleKeywords))
                    {
                        // 将关键词字符串分割成数组
                        string[] keywords = scaleKeywords.Split(
                            new char[] { ',', '，', ';', '；', '|', '、' },
                            StringSplitOptions.RemoveEmptyEntries);
                        string 图纸名称 = ETVisio.ReadTextFromShapes(
                            page,
                            new Dictionary<string, RectangleRange>
                            {
                            { "图纸名称", ETVisio.Get图衔子图形在页面中的位置_BL(page, selectedPositions["图名"], 0.01, 0.03, 0.006) }
                            })["图纸名称"][0].ShapeText;
                        if (keywords.Any(keyword => 图纸名称.Contains(keyword)))
                        {
                            double actualScale = ETVisio.GetPaperDrawingScale(page);
                            string scaleText = $"mm,1:{actualScale.ToString()}";
                            ETVisio.CreateRectangleText(
                                page,
                                ETVisio.Get图衔子图形在页面中的位置_BL(page, selectedPositions["单位比例"], 0, 0, 0, 0),
                                scaleText);
                        }
                        else
                        {
                            ETVisio.CreateRectangleText(
                                page,
                                ETVisio.Get图衔子图形在页面中的位置_BL(page, selectedPositions["单位比例"], 0, 0, 0, 0),
                                "mm，示意");
                        }
                    }
                }
            }
        }

        public static void ProcessShapeText(Page page, RectanglPosition position, Range cell)
        {
            if (!ETExcelExtensions.IsCellEmpty(cell))
            {
                string cellValue = cell.Value.ToString();
                cellValue = cellValue.Replace(Environment.NewLine, string.Empty);
                if (cellValue.IndexOf("5G基站") > 0)
                    cellValue = cellValue.Replace("5G基站", $"{Environment.NewLine}5G基站");
                else if (cellValue.IndexOf("基站") > 0)
                    cellValue = cellValue.Replace("基站", $"{Environment.NewLine}基站");

                ETVisio.CreateRectangleText(page, ETVisio.Get图衔子图形在页面中的位置_BL(page, position, 0, 0, 0, 0), cellValue);
            }
        }
        #endregion 写入图衔
    }
}