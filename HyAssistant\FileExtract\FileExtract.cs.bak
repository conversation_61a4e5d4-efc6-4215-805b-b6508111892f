﻿using ET;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HyAssistant
{
    /// <summary>
    /// 文件提取处理窗体类
    /// </summary>
    public partial class FileExtract : Form
    {
        // 配置组前缀常量
        public const string SECTION_PREFIX = "FileExtract-";

        // 配置文件路径
        readonly string configFilePath = ETConfig.GetConfigDirectory("fileExtract.ini");

        // 存储所有的处理任务
        readonly Dictionary<string, Task> processingTasks = new Dictionary<string, Task>();

        // 取消令牌源
        CancellationTokenSource cancellationTokenSource = new CancellationTokenSource();

        // 文件提取服务
        readonly FileExtractService _fileExtractService = new FileExtractService();

        // 服务线程
        Thread _serviceThread;

        // 线程同步对象
        readonly object _syncLock = new object();

        // 线程安全日志服务
        readonly ThreadSafeLogService _logService;

        /// <summary>
        /// 初始化FileExtract实例
        /// </summary>
        public FileExtract()
        {
            InitializeComponent();
            Visible = false;
            _logService = new ThreadSafeLogService(textboxLog);
        }

        /// <summary>
        /// 开始处理文件
        /// </summary>
        public async Task StartProcessing()
        {
            // 确保先停止任何可能正在运行的任务
            await EnsureServiceStopped().ConfigureAwait(false);

            // 创建并启动新的服务线程
            _serviceThread = new Thread(RunFileExtractService)
            {
                IsBackground = true,
                Name = "FileExtractServiceThread"
            };
            _serviceThread.Start();
        }

        /// <summary>
        /// 在独立线程中运行文件提取服务
        /// </summary>
        void RunFileExtractService()
        {
            try
            {
                // 使用Task.Run和等待是为了避免在线程中阻塞
                Task.Run(async () => await _fileExtractService.StartProcessing(configFilePath, LogMessage).ConfigureAwait(false)).Wait();
            }
            catch (Exception ex)
            {
                // 记录线程异常
                LogMessage("错误", $"文件提取服务线程异常：{ex.Message}", true);
            }
        }

        /// <summary>
        /// 确保服务完全停止
        /// </summary>
        async Task EnsureServiceStopped()
        {
            // 首先确保FileExtractService内部状态被重置
            await Task.Run(() => _fileExtractService.CancelAllProcessing()).ConfigureAwait(false);

            // 然后停止服务线程
            StopServiceThread();
        }

        /// <summary>
        /// 停止服务线程
        /// </summary>
        void StopServiceThread()
        {
            if (_serviceThread != null && _serviceThread.IsAlive)
            {
                try
                {
                    // 给线程一些时间来清理资源
                    if (!_serviceThread.Join(3000))
                    {
                        // 如果线程在给定时间内没有结束，记录警告但不强制终止
                        LogMessage("警告", "文件提取服务线程未能正常终止", true);
                    }
                }
                catch (Exception ex)
                {
                    LogMessage("错误", $"停止服务线程时出错：{ex.Message}", true);
                }
                finally
                {
                    _serviceThread = null;
                }
            }
        }

        /// <summary>
        /// 处理消息并记录日志
        /// </summary>
        /// <param name="messageType">消息类型</param>
        /// <param name="messageInfo">消息内容</param>
        /// <param name="logToMainForm">是否记录到主窗体</param>
        void LogMessage(string messageType, string messageInfo, bool logToMainForm)
        {
            _logService.Log(messageType, messageInfo, logToMainForm);
        }

        /// <summary>
        /// 取消所有处理任务并等待完成
        /// </summary>
        public async Task CancelAllProcessingAsync()
        {
            try
            {
                await _fileExtractService.CancelAllProcessing().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                LogMessage("错误", $"取消处理任务时出错：{ex.Message}", true);
            }
        }

        void buttonFileExtract_Click(object sender, EventArgs e)
        {
            // 打开配置窗体
            using (FileExtractConfigForm configForm = new FileExtractConfigForm())
            {
                configForm.ShowDialog(this);
            }
        }

        void FileExtract_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                e.Cancel = true;
                ETForm.Hide(this);
            }
        }

        void FileExtract_Load(object sender, EventArgs e) { checkBoxFileExtract_CheckedChanged(null, null); }

        async void checkBoxFileExtract_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                // 禁用控件，防止用户重复点击
                checkBoxFileExtract.Enabled = false;

                if (checkBoxFileExtract.Checked)
                {
                    await StartProcessing().ConfigureAwait(false);
                }
                else
                {
                    await EnsureServiceStopped().ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                LogMessage("错误", $"切换文件提取服务状态时出错：{ex.Message}", true);

                // 发生错误时，将复选框状态设置为与服务状态一致（线程安全）
                SafeUpdateCheckBoxState();
            }
            finally
            {
                // 重新启用控件（线程安全）
                SafeEnableCheckBox();
            }
        }

        /// <summary>
        /// 线程安全地更新复选框状态
        /// </summary>
        void SafeUpdateCheckBoxState()
        {
            if (checkBoxFileExtract.InvokeRequired)
            {
                checkBoxFileExtract.Invoke(new Action(() =>
                {
                    checkBoxFileExtract.Checked = _serviceThread != null && _serviceThread.IsAlive;
                }));
            }
            else
            {
                checkBoxFileExtract.Checked = _serviceThread != null && _serviceThread.IsAlive;
            }
        }

        /// <summary>
        /// 线程安全地启用复选框
        /// </summary>
        void SafeEnableCheckBox()
        {
            if (checkBoxFileExtract.InvokeRequired)
            {
                checkBoxFileExtract.Invoke(new Action(() =>
                {
                    checkBoxFileExtract.Enabled = true;
                }));
            }
            else
            {
                checkBoxFileExtract.Enabled = true;
            }
        }

        // 在Form关闭时释放资源
        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            try
            {
                // 确保服务停止
                EnsureServiceStopped().Wait();

                if (_fileExtractService != null)
                {
                    try
                    {
                        _fileExtractService.Dispose();
                    }
                    catch
                    {
                        // 忽略释放资源时的错误
                    }
                }
            }
            catch
            {
                // 忽略关闭时的错误
            }
            finally
            {
                base.OnFormClosed(e);
            }
        }
    }

    /// <summary>
    /// 日志服务接口
    /// </summary>
    public interface ILogService
    {
        void Log(string messageType, string messageInfo, bool logToMainForm);
    }

    /// <summary>
    /// 线程安全的日志服务实现
    /// </summary>
    public class ThreadSafeLogService : ILogService
    {
        readonly TextBox _logTextBox;
        readonly object _syncLock = new object();

        public ThreadSafeLogService(TextBox logTextBox)
        {
            _logTextBox = logTextBox;
        }

        public void Log(string messageType, string messageInfo, bool logToMainForm)
        {
            string logMessage = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} {messageType}：{messageInfo}";

            SafeLogToTextBox(_logTextBox, logMessage);

            if (logToMainForm && Program.mainForm != null && Program.mainForm.textBoxLog != null)
            {
                SafeLogToTextBox(Program.mainForm.textBoxLog, logMessage);
            }
        }

        /// <summary>
        /// 线程安全地记录日志到TextBox
        /// </summary>
        void SafeLogToTextBox(TextBox textBox, string message)
        {
            if (textBox == null) return;

            try
            {
                if (textBox.InvokeRequired)
                {
                    textBox.BeginInvoke(new Action(() =>
                    {
                        lock (_syncLock)
                        {
                            textBox.WriteLog(message);
                        }
                    }));
                }
                else
                {
                    lock (_syncLock)
                    {
                        textBox.WriteLog(message);
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录异常，但不向上抛出，以避免日志系统崩溃导致主程序崩溃
                System.Diagnostics.Debug.WriteLine($"记录日志时出错: {ex.Message}");
            }
        }
    }
}
