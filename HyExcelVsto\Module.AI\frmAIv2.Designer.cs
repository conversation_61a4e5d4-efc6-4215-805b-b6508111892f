/*
 * ============================================================================
 * 功能模块：AI辅助工具v2窗体设计器文件
 * ============================================================================
 * 
 * 模块作用：定义AI辅助工具v2窗体的界面布局和控件初始化代码
 * 
 * 主要功能：
 * - 初始化AI辅助工具v2窗体的界面元素
 * - 定义控件的布局和属性设置
 * - 配置事件处理程序的绑定
 * 
 * 执行逻辑：
 * 1. 创建和配置窗体控件（标签、文本框、按钮、下拉框等）
 * 2. 设置控件的属性和样式
 * 3. 绑定事件处理程序
 * 4. 配置选项卡页面和相关资源
 * 
 * 注意事项：
 * - 此文件由Visual Studio设计器自动生成，请勿手动修改
 * - 修改界面应通过设计器进行，避免直接编辑代码
 * - 控件命名应保持一致性以便维护
 * ============================================================================
 */

using ET.Controls;

namespace HyExcelVsto.Module.AI
{
    partial class frmAIv2
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmAIv2));
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPageMain = new System.Windows.Forms.TabPage();
            this.checkFillNullValues = new System.Windows.Forms.CheckBox();
            this.radioColumnMode = new System.Windows.Forms.RadioButton();
            this.radioRowMode = new System.Windows.Forms.RadioButton();
            this.groupBoxConfig = new System.Windows.Forms.GroupBox();
            this.comboGlobalPrompt = new System.Windows.Forms.ComboBox();
            this.comboModelConfig = new System.Windows.Forms.ComboBox();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBoxRanges = new System.Windows.Forms.GroupBox();
            this.ucFileSource = new ET.Controls.ETRangeSelectControl();
            this.ucPromptRange = new ET.Controls.ETRangeSelectControl();
            this.ucTargetRange = new ET.Controls.ETRangeSelectControl();
            this.ucDataSource = new ET.Controls.ETRangeSelectControl();
            this.label6 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.groupBoxActions = new System.Windows.Forms.GroupBox();
            this.buttonOpenConfigDir = new System.Windows.Forms.Button();
            this.buttonStop = new System.Windows.Forms.Button();
            this.buttonExecute = new System.Windows.Forms.Button();
            this.groupBoxProgress = new System.Windows.Forms.GroupBox();
            this.textBoxLog = new System.Windows.Forms.TextBox();
            this.labelProgress = new System.Windows.Forms.Label();
            this.progressBar = new System.Windows.Forms.ProgressBar();
            this.tabPageSettings = new System.Windows.Forms.TabPage();
            this.textBoxSettings = new System.Windows.Forms.TextBox();
            this.labelSettings = new System.Windows.Forms.Label();
            this.tabControl1.SuspendLayout();
            this.tabPageMain.SuspendLayout();
            this.groupBoxConfig.SuspendLayout();
            this.groupBoxRanges.SuspendLayout();
            this.groupBoxActions.SuspendLayout();
            this.groupBoxProgress.SuspendLayout();
            this.tabPageSettings.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPageMain);
            this.tabControl1.Controls.Add(this.tabPageSettings);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(0, 0);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(784, 476);
            this.tabControl1.TabIndex = 0;
            // 
            // tabPageMain
            // 
            this.tabPageMain.Controls.Add(this.groupBoxConfig);
            this.tabPageMain.Controls.Add(this.groupBoxRanges);
            this.tabPageMain.Controls.Add(this.groupBoxActions);
            this.tabPageMain.Controls.Add(this.groupBoxProgress);
            this.tabPageMain.Location = new System.Drawing.Point(4, 22);
            this.tabPageMain.Name = "tabPageMain";
            this.tabPageMain.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageMain.Size = new System.Drawing.Size(776, 450);
            this.tabPageMain.TabIndex = 0;
            this.tabPageMain.Text = "AI填写表格v2";
            this.tabPageMain.UseVisualStyleBackColor = true;
            // 
            // checkFillNullValues
            // 
            this.checkFillNullValues.AutoSize = true;
            this.checkFillNullValues.Location = new System.Drawing.Point(666, 27);
            this.checkFillNullValues.Name = "checkFillNullValues";
            this.checkFillNullValues.Size = new System.Drawing.Size(84, 16);
            this.checkFillNullValues.TabIndex = 2;
            this.checkFillNullValues.Text = "回填null值";
            this.checkFillNullValues.UseVisualStyleBackColor = true;
            // 
            // radioColumnMode
            // 
            this.radioColumnMode.AutoSize = true;
            this.radioColumnMode.Location = new System.Drawing.Point(569, 27);
            this.radioColumnMode.Name = "radioColumnMode";
            this.radioColumnMode.Size = new System.Drawing.Size(59, 16);
            this.radioColumnMode.TabIndex = 1;
            this.radioColumnMode.Text = "列模式";
            this.radioColumnMode.UseVisualStyleBackColor = true;
            // 
            // radioRowMode
            // 
            this.radioRowMode.AutoSize = true;
            this.radioRowMode.Checked = true;
            this.radioRowMode.Location = new System.Drawing.Point(484, 27);
            this.radioRowMode.Name = "radioRowMode";
            this.radioRowMode.Size = new System.Drawing.Size(59, 16);
            this.radioRowMode.TabIndex = 0;
            this.radioRowMode.TabStop = true;
            this.radioRowMode.Text = "行模式";
            this.radioRowMode.UseVisualStyleBackColor = true;
            // 
            // groupBoxConfig
            // 
            this.groupBoxConfig.Controls.Add(this.comboGlobalPrompt);
            this.groupBoxConfig.Controls.Add(this.comboModelConfig);
            this.groupBoxConfig.Controls.Add(this.label2);
            this.groupBoxConfig.Controls.Add(this.label1);
            this.groupBoxConfig.Location = new System.Drawing.Point(6, 126);
            this.groupBoxConfig.Name = "groupBoxConfig";
            this.groupBoxConfig.Size = new System.Drawing.Size(764, 54);
            this.groupBoxConfig.TabIndex = 3;
            this.groupBoxConfig.TabStop = false;
            this.groupBoxConfig.Text = "配置选择";
            // 
            // comboGlobalPrompt
            // 
            this.comboGlobalPrompt.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboGlobalPrompt.FormattingEnabled = true;
            this.comboGlobalPrompt.Location = new System.Drawing.Point(470, 20);
            this.comboGlobalPrompt.Name = "comboGlobalPrompt";
            this.comboGlobalPrompt.Size = new System.Drawing.Size(280, 20);
            this.comboGlobalPrompt.TabIndex = 3;
            // 
            // comboModelConfig
            // 
            this.comboModelConfig.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboModelConfig.FormattingEnabled = true;
            this.comboModelConfig.Location = new System.Drawing.Point(80, 20);
            this.comboModelConfig.Name = "comboModelConfig";
            this.comboModelConfig.Size = new System.Drawing.Size(280, 20);
            this.comboModelConfig.TabIndex = 2;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(390, 23);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(77, 12);
            this.label2.TabIndex = 1;
            this.label2.Text = "全局提示词：";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(15, 23);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "模型配置：";
            // 
            // groupBoxRanges
            // 
            this.groupBoxRanges.Controls.Add(this.ucFileSource);
            this.groupBoxRanges.Controls.Add(this.ucPromptRange);
            this.groupBoxRanges.Controls.Add(this.ucTargetRange);
            this.groupBoxRanges.Controls.Add(this.ucDataSource);
            this.groupBoxRanges.Controls.Add(this.label6);
            this.groupBoxRanges.Controls.Add(this.label5);
            this.groupBoxRanges.Controls.Add(this.label4);
            this.groupBoxRanges.Controls.Add(this.label3);
            this.groupBoxRanges.Location = new System.Drawing.Point(6, 6);
            this.groupBoxRanges.Name = "groupBoxRanges";
            this.groupBoxRanges.Size = new System.Drawing.Size(764, 114);
            this.groupBoxRanges.TabIndex = 2;
            this.groupBoxRanges.TabStop = false;
            this.groupBoxRanges.Text = "区域选择";
            // 
            // ucFileSource
            // 
            this.ucFileSource.EnableEnterThenSelect = false;
            this.ucFileSource.HideParentForm = true;
            this.ucFileSource.InputPromptText = "请选择：";
            this.ucFileSource.Location = new System.Drawing.Point(470, 50);
            this.ucFileSource.Name = "ucFileSource";
            this.ucFileSource.SelectedRange = null;
            this.ucFileSource.Size = new System.Drawing.Size(280, 21);
            this.ucFileSource.TabIndex = 7;
            // 
            // ucPromptRange
            // 
            this.ucPromptRange.EnableEnterThenSelect = false;
            this.ucPromptRange.HideParentForm = true;
            this.ucPromptRange.InputPromptText = "请选择：";
            this.ucPromptRange.Location = new System.Drawing.Point(80, 80);
            this.ucPromptRange.Name = "ucPromptRange";
            this.ucPromptRange.SelectedRange = null;
            this.ucPromptRange.Size = new System.Drawing.Size(280, 21);
            this.ucPromptRange.TabIndex = 6;
            // 
            // ucTargetRange
            // 
            this.ucTargetRange.EnableEnterThenSelect = false;
            this.ucTargetRange.HideParentForm = true;
            this.ucTargetRange.InputPromptText = "请选择：";
            this.ucTargetRange.Location = new System.Drawing.Point(470, 20);
            this.ucTargetRange.Name = "ucTargetRange";
            this.ucTargetRange.SelectedRange = null;
            this.ucTargetRange.Size = new System.Drawing.Size(280, 21);
            this.ucTargetRange.TabIndex = 5;
            // 
            // ucDataSource
            // 
            this.ucDataSource.EnableEnterThenSelect = false;
            this.ucDataSource.HideParentForm = true;
            this.ucDataSource.InputPromptText = "请选择：";
            this.ucDataSource.Location = new System.Drawing.Point(80, 20);
            this.ucDataSource.Name = "ucDataSource";
            this.ucDataSource.SelectedRange = null;
            this.ucDataSource.Size = new System.Drawing.Size(280, 21);
            this.ucDataSource.TabIndex = 4;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(390, 53);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(65, 12);
            this.label6.TabIndex = 3;
            this.label6.Text = "文件来源：";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(15, 83);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(77, 12);
            this.label5.TabIndex = 2;
            this.label5.Text = "提示词区域：";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(390, 23);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(65, 12);
            this.label4.TabIndex = 1;
            this.label4.Text = "回填区域：";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(15, 23);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(53, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "数据源：";
            // 
            // groupBoxActions
            // 
            this.groupBoxActions.Controls.Add(this.checkFillNullValues);
            this.groupBoxActions.Controls.Add(this.radioColumnMode);
            this.groupBoxActions.Controls.Add(this.radioRowMode);
            this.groupBoxActions.Controls.Add(this.buttonOpenConfigDir);
            this.groupBoxActions.Controls.Add(this.buttonStop);
            this.groupBoxActions.Controls.Add(this.buttonExecute);
            this.groupBoxActions.Location = new System.Drawing.Point(6, 186);
            this.groupBoxActions.Name = "groupBoxActions";
            this.groupBoxActions.Size = new System.Drawing.Size(764, 60);
            this.groupBoxActions.TabIndex = 1;
            this.groupBoxActions.TabStop = false;
            this.groupBoxActions.Text = "操作";
            // 
            // buttonOpenConfigDir
            // 
            this.buttonOpenConfigDir.Location = new System.Drawing.Point(300, 20);
            this.buttonOpenConfigDir.Name = "buttonOpenConfigDir";
            this.buttonOpenConfigDir.Size = new System.Drawing.Size(120, 30);
            this.buttonOpenConfigDir.TabIndex = 2;
            this.buttonOpenConfigDir.Text = "打开配置目录";
            this.buttonOpenConfigDir.UseVisualStyleBackColor = true;
            this.buttonOpenConfigDir.Click += new System.EventHandler(this.ButtonOpenConfigDir_Click);
            // 
            // buttonStop
            // 
            this.buttonStop.Enabled = false;
            this.buttonStop.Location = new System.Drawing.Point(150, 20);
            this.buttonStop.Name = "buttonStop";
            this.buttonStop.Size = new System.Drawing.Size(120, 30);
            this.buttonStop.TabIndex = 1;
            this.buttonStop.Text = "停止";
            this.buttonStop.UseVisualStyleBackColor = true;
            this.buttonStop.Click += new System.EventHandler(this.ButtonStop_Click);
            // 
            // buttonExecute
            // 
            this.buttonExecute.Location = new System.Drawing.Point(15, 20);
            this.buttonExecute.Name = "buttonExecute";
            this.buttonExecute.Size = new System.Drawing.Size(120, 30);
            this.buttonExecute.TabIndex = 0;
            this.buttonExecute.Text = "执行";
            this.buttonExecute.UseVisualStyleBackColor = true;
            this.buttonExecute.Click += new System.EventHandler(this.ButtonExecute_Click);
            // 
            // groupBoxProgress
            // 
            this.groupBoxProgress.Controls.Add(this.textBoxLog);
            this.groupBoxProgress.Controls.Add(this.labelProgress);
            this.groupBoxProgress.Controls.Add(this.progressBar);
            this.groupBoxProgress.Location = new System.Drawing.Point(6, 252);
            this.groupBoxProgress.Name = "groupBoxProgress";
            this.groupBoxProgress.Size = new System.Drawing.Size(764, 191);
            this.groupBoxProgress.TabIndex = 0;
            this.groupBoxProgress.TabStop = false;
            this.groupBoxProgress.Text = "进度和日志";
            // 
            // textBoxLog
            // 
            this.textBoxLog.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.textBoxLog.Location = new System.Drawing.Point(15, 50);
            this.textBoxLog.Multiline = true;
            this.textBoxLog.Name = "textBoxLog";
            this.textBoxLog.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBoxLog.Size = new System.Drawing.Size(735, 130);
            this.textBoxLog.TabIndex = 2;
            // 
            // labelProgress
            // 
            this.labelProgress.AutoSize = true;
            this.labelProgress.Location = new System.Drawing.Point(15, 30);
            this.labelProgress.Name = "labelProgress";
            this.labelProgress.Size = new System.Drawing.Size(53, 12);
            this.labelProgress.TabIndex = 1;
            this.labelProgress.Text = "准备就绪";
            // 
            // progressBar
            // 
            this.progressBar.Location = new System.Drawing.Point(200, 25);
            this.progressBar.Name = "progressBar";
            this.progressBar.Size = new System.Drawing.Size(550, 20);
            this.progressBar.TabIndex = 0;
            // 
            // tabPageSettings
            // 
            this.tabPageSettings.Controls.Add(this.textBoxSettings);
            this.tabPageSettings.Controls.Add(this.labelSettings);
            this.tabPageSettings.Location = new System.Drawing.Point(4, 22);
            this.tabPageSettings.Name = "tabPageSettings";
            this.tabPageSettings.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageSettings.Size = new System.Drawing.Size(776, 450);
            this.tabPageSettings.TabIndex = 1;
            this.tabPageSettings.Text = "设置";
            this.tabPageSettings.UseVisualStyleBackColor = true;
            // 
            // textBoxSettings
            // 
            this.textBoxSettings.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.textBoxSettings.Location = new System.Drawing.Point(6, 30);
            this.textBoxSettings.Multiline = true;
            this.textBoxSettings.Name = "textBoxSettings";
            this.textBoxSettings.ReadOnly = true;
            this.textBoxSettings.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBoxSettings.Size = new System.Drawing.Size(764, 499);
            this.textBoxSettings.TabIndex = 1;
            this.textBoxSettings.Text = resources.GetString("textBoxSettings.Text");
            // 
            // labelSettings
            // 
            this.labelSettings.AutoSize = true;
            this.labelSettings.Location = new System.Drawing.Point(6, 10);
            this.labelSettings.Name = "labelSettings";
            this.labelSettings.Size = new System.Drawing.Size(137, 12);
            this.labelSettings.TabIndex = 0;
            this.labelSettings.Text = "AI辅助工具v2使用说明：";
            // 
            // frmAIv2
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(784, 476);
            this.Controls.Add(this.tabControl1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frmAIv2";
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "AI辅助工具v2";
            this.Load += new System.EventHandler(this.frmAIv2_Load);
            this.tabControl1.ResumeLayout(false);
            this.tabPageMain.ResumeLayout(false);
            this.groupBoxConfig.ResumeLayout(false);
            this.groupBoxConfig.PerformLayout();
            this.groupBoxRanges.ResumeLayout(false);
            this.groupBoxRanges.PerformLayout();
            this.groupBoxActions.ResumeLayout(false);
            this.groupBoxActions.PerformLayout();
            this.groupBoxProgress.ResumeLayout(false);
            this.groupBoxProgress.PerformLayout();
            this.tabPageSettings.ResumeLayout(false);
            this.tabPageSettings.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabPageMain;
        private System.Windows.Forms.TabPage tabPageSettings;
        private System.Windows.Forms.GroupBox groupBoxProgress;
        private System.Windows.Forms.TextBox textBoxLog;
        private System.Windows.Forms.Label labelProgress;
        private System.Windows.Forms.ProgressBar progressBar;
        private System.Windows.Forms.GroupBox groupBoxActions;
        private System.Windows.Forms.Button buttonStop;
        private System.Windows.Forms.Button buttonExecute;
        private System.Windows.Forms.GroupBox groupBoxRanges;
        private ETRangeSelectControl ucFileSource;
        private ETRangeSelectControl ucPromptRange;
        private ETRangeSelectControl ucTargetRange;
        private ETRangeSelectControl ucDataSource;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.GroupBox groupBoxConfig;
        private System.Windows.Forms.ComboBox comboGlobalPrompt;
        private System.Windows.Forms.ComboBox comboModelConfig;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.CheckBox checkFillNullValues;
        private System.Windows.Forms.RadioButton radioColumnMode;
        private System.Windows.Forms.RadioButton radioRowMode;
        private System.Windows.Forms.Button buttonOpenConfigDir;
        private System.Windows.Forms.TextBox textBoxSettings;
        private System.Windows.Forms.Label labelSettings;
    }
}
