/*
 * ============================================================================
 * 功能模块：文件复制器
 * ============================================================================
 * 
 * 模块作用：提供自动化文件复制功能，支持定时复制和条件筛选
 * 
 * 主要功能：
 * - 配置化文件复制：通过INI配置文件定义复制任务
 * - 定时复制：支持按时间间隔自动执行复制任务
 * - 条件筛选：支持按文件创建/修改时间、文件模式等条件筛选
 * - 目标路径定制：支持创建日期时间子目录
 * 
 * 执行逻辑：
 * 1. 读取配置文件中的复制任务定义
 * 2. 为每个启用的任务创建复制器实例
 * 3. 根据配置的时间间隔定时执行复制操作
 * 4. 记录复制过程的日志信息
 * 
 * 注意事项：
 * - 复制任务可以通过UI界面开启或关闭
 * - 支持多个独立的复制任务同时运行
 * - 任务配置可通过配置窗口修改
 * ============================================================================
 */

using ET;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HyAssistant
{
    /// <summary>
    /// 文件复制器窗体类，提供自动化文件复制功能的用户界面
    /// </summary>
    /// <remarks>
    /// 该类负责管理多个文件复制任务，通过配置文件定义复制规则
    /// 支持定时复制、条件筛选和日志记录功能
    /// </remarks>
    public partial class FileCopier : Form
    {
        /// <summary>
        /// 存储文件复制器实例的字典，键为配置文件中的section名
        /// </summary>
        private static readonly Dictionary<string, ET.ETFileCopier> _copiers = new Dictionary<string, ET.ETFileCopier>();

        /// <summary>
        /// 日志锁对象，用于确保多线程环境下日志记录的线程安全
        /// </summary>
        private static readonly object _logLock = new object();

        /// <summary>
        /// 配置文件的完整路径
        /// </summary>
        private readonly string _iniFilePath;

        /// <summary>
        /// 用于取消任务的取消标记源
        /// </summary>
        private CancellationTokenSource _cts;

        /// <summary>
        /// 用于跟踪当前的初始化任务
        /// </summary>
        private Task _initTask;

        /// <summary>
        /// 构造函数，初始化文件复制器窗体
        /// </summary>
        /// <remarks>
        /// 初始化组件、设置窗体不可见、准备配置文件路径和取消标记源
        /// </remarks>
        public FileCopier()
        {
            InitializeComponent();
            Visible = false;
            _iniFilePath = ETConfig.GetConfigDirectory("filecopier.ini");
            _cts = new CancellationTokenSource();
        }

        /// <summary>
        /// 开始文件复制任务
        /// </summary>
        /// <remarks>
        /// 执行逻辑：取消当前任务 → 创建新的取消标记 → 启动后台任务处理配置
        /// </remarks>
        public void StartCopy()
        {
            try
            {
                // 取消之前可能正在进行的初始化任务
                CancelAllCopying();

                // 创建新的取消标记源
                _cts = new CancellationTokenSource();

                // 启动一个新的后台任务来处理配置
                _initTask = Task.Run(() => StartCopyInternal(_cts.Token), _cts.Token);
            }
            catch (Exception ex)
            {
                LogError($"启动文件复制任务时发生错误: {ex.Message}");
                MessageBox.Show($"启动文件复制任务时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 在后台线程中执行的文件复制初始化方法
        /// </summary>
        /// <param name="cancellationToken">取消标记，用于支持取消操作</param>
        /// <remarks>
        /// 执行逻辑：检查取消状态 → 验证配置文件存在 → 读取并处理配置节
        /// </remarks>
        private void StartCopyInternal(CancellationToken cancellationToken)
        {
            try
            {
                if (cancellationToken.IsCancellationRequested)
                    return;

                if (!File.Exists(_iniFilePath))
                {
                    // 配置文件不存在，直接返回
                    return;
                }

                ETIniFile iniFile = new ETIniFile(_iniFilePath);
                ProcessConfigSections(iniFile, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，安静地退出
                LogInfo("文件复制任务已取消");
            }
            catch (Exception ex)
            {
                LogError($"启动文件复制任务时发生错误: {ex.Message}");

                // 使用Invoke确保在UI线程上显示消息框
                if (!IsDisposed && IsHandleCreated)
                {
                    Invoke(new Action(() => MessageBox.Show($"启动文件复制任务时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)));
                }
            }
        }

        /// <summary>
        /// 处理配置文件中的各个部分
        /// </summary>
        /// <param name="iniFile">INI配置文件对象</param>
        /// <param name="cancellationToken">取消标记</param>
        /// <remarks>
        /// 执行逻辑：遍历配置节 → 创建复制器实例 → 安排复制任务 → 更新全局字典
        /// </remarks>
        private void ProcessConfigSections(ETIniFile iniFile, CancellationToken cancellationToken)
        {
            // 创建一个临时字典来存储新的复制器
            Dictionary<string, ET.ETFileCopier> newCopiers = new Dictionary<string, ET.ETFileCopier>();

            foreach (string sectionName in iniFile.GetSectionNames())
            {
                if (cancellationToken.IsCancellationRequested)
                    return;

                try
                {
                    if (sectionName.StartsWith("filesync-", StringComparison.OrdinalIgnoreCase))
                    {
                        if (iniFile.GetValue(sectionName, "enable") == "1")
                        {
                            string copierKey = sectionName;

                            // 创建新的复制器
                            ETFileCopier copier = CreateCopier(iniFile, sectionName);
                            if (copier != null)
                            {
                                newCopiers[copierKey] = copier;
                                copier.Messages += OnMessages;

                                // 安排复制任务
                                copier.ScheduleCopying(iniFile.GetValue(sectionName, "name", string.Empty));
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogError($"处理配置节 {sectionName} 时发生错误: {ex.Message}");
                }
            }

            // 使用线程安全的方式更新全局字典
            lock (_copiers)
            {
                // 清除旧的复制器
                foreach (ETFileCopier copier in _copiers.Values)
                {
                    try
                    {
                        copier.Messages -= OnMessages;
                        copier.CancelScheduledCopying();
                    }
                    catch (Exception ex)
                    {
                        LogError($"取消旧的复制任务时发生错误: {ex.Message}");
                    }
                }

                _copiers.Clear();

                // 添加新的复制器
                foreach (KeyValuePair<string, ETFileCopier> item in newCopiers)
                {
                    _copiers.Add(item.Key, item.Value);
                }
            }
        }

        /// <summary>
        /// 创建文件复制器实例
        /// </summary>
        /// <param name="iniFile">INI配置文件对象</param>
        /// <param name="sectionName">配置节名称</param>
        /// <returns>创建的ETFileCopier对象，失败时返回null</returns>
        /// <remarks>
        /// 根据配置节中的参数创建并配置ETFileCopier实例
        /// </remarks>
        private ETFileCopier CreateCopier(ETIniFile iniFile, string sectionName)
        {
            try
            {
                ETFileCopier copier = new ET.ETFileCopier
                {
                    SourcePaths = iniFile.GetValue(sectionName, "SourcePaths"),
                    TargetPath = iniFile.GetValue(sectionName, "TargetPath"),
                    FilePattern = iniFile.GetValue(sectionName, "FilePattern", "*.*"),
                    IntervalInMinutes = ParseIntervalValue(iniFile.GetValue(sectionName, "IntervalInMinutes", "0")),
                    CreationTimeLimit = ParseDateTime(iniFile.GetValue(sectionName, "CreationTimeLimit", DateTime.MinValue.ToString())),
                    ModificationTimeLimit = ParseDateTime(iniFile.GetValue(sectionName, "ModificationTimeLimit", DateTime.MinValue.ToString())),
                    CreateDateTimeSubDir = iniFile.GetValue(sectionName, "CreateDateTimeSubDir", "0") == "1",
                    UseCreationTimeForSubDir = iniFile.GetValue(sectionName, "UseCreationTimeForSubDir", "0") == "1"
                };

                return copier;
            }
            catch (Exception ex)
            {
                LogError($"创建文件复制器时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 解析时间间隔值
        /// </summary>
        /// <param name="value">要解析的字符串值</param>
        /// <returns>解析后的整数值，解析失败时返回0</returns>
        private int ParseIntervalValue(string value)
        {
            if (int.TryParse(value, out int result))
            {
                return result;
            }
            return 0; // 默认值
        }

        /// <summary>
        /// 解析日期时间值
        /// </summary>
        /// <param name="value">要解析的字符串值</param>
        /// <returns>解析后的DateTime值，解析失败时返回DateTime.MinValue</returns>
        private DateTime ParseDateTime(string value)
        {
            if (DateTime.TryParse(value, out DateTime result))
            {
                return result;
            }
            return DateTime.MinValue; // 默认值
        }

        /// <summary>
        /// 处理消息并记录日志
        /// </summary>
        /// <param name="messageType">消息类型（如"信息"、"错误"等）</param>
        /// <param name="messageInfo">消息内容</param>
        /// <remarks>
        /// 将消息记录到当前窗体和主窗体的日志文本框中
        /// </remarks>
        public void OnMessages(string messageType, string messageInfo)
        {
            string logMessage = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} {messageType}：{messageInfo}";

            lock (_logLock)
            {
                // 在文本框中记录日志
                UpdateLogTextBox(textboxLog, logMessage);

                // 输出日志到主窗体
                if (Program.mainForm != null && Program.mainForm.textBoxLog != null)
                {
                    UpdateLogTextBox(Program.mainForm.textBoxLog, logMessage);
                }
            }
        }

        /// <summary>
        /// 记录信息日志
        /// </summary>
        /// <param name="infoMessage">信息消息内容</param>
        private void LogInfo(string infoMessage)
        {
            OnMessages("信息", infoMessage);
        }

        /// <summary>
        /// 更新日志文本框
        /// </summary>
        /// <param name="textBox">要更新的文本框控件</param>
        /// <param name="message">日志消息内容</param>
        /// <remarks>
        /// 支持跨线程调用，确保在UI线程上更新文本框
        /// </remarks>
        private void UpdateLogTextBox(TextBox textBox, string message)
        {
            if (textBox.InvokeRequired)
            {
                textBox.Invoke(new Action(() => textBox.WriteLog(message)));
            }
            else
            {
                textBox.WriteLog(message);
            }
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="errorMessage">错误消息内容</param>
        private void LogError(string errorMessage)
        {
            OnMessages("错误", errorMessage);
        }

        /// <summary>
        /// 取消所有复制任务
        /// </summary>
        /// <remarks>
        /// 执行逻辑：发出取消信号 → 等待任务完成 → 清理复制器资源 → 重置取消标记源
        /// </remarks>
        public void CancelAllCopying()
        {
            try
            {
                // 发出取消信号
                if (_cts != null && !_cts.IsCancellationRequested)
                {
                    _cts.Cancel();
                }

                // 等待初始化任务完成（如果正在运行）
                if (_initTask != null && !_initTask.IsCompleted)
                {
                    try
                    {
                        // 设置超时，避免无限等待
                        _initTask.Wait(TimeSpan.FromSeconds(2));
                    }
                    catch (AggregateException)
                    {
                        // 忽略因取消而产生的异常
                    }
                }

                lock (_copiers)
                {
                    // 取消字典中所有FileCopier的定时复制任务
                    foreach (KeyValuePair<string, ET.ETFileCopier> item in _copiers)
                    {
                        try
                        {
                            item.Value.Messages -= OnMessages;
                            item.Value.CancelScheduledCopying();
                        }
                        catch (Exception ex)
                        {
                            LogError($"取消复制任务 {item.Key} 时发生错误: {ex.Message}");
                        }
                    }

                    // 清空字典
                    _copiers.Clear();
                }

                // 处理完成后，重新创建取消标记源
                _cts = new CancellationTokenSource();
            }
            catch (Exception ex)
            {
                LogError($"取消复制任务时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 打开配置文件按钮点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 打开文件复制器配置窗体，允许用户修改复制任务配置
        /// </remarks>
        private void buttonFileCopier_Click(object sender, EventArgs e)
        {
            try
            {
                using (FileCopierConfigForm configForm = new FileCopierConfigForm())
                {
                    configForm.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                LogError($"打开配置窗体时发生错误: {ex.Message}");
                MessageBox.Show($"打开配置窗体时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 复制开关状态变化事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 根据复选框状态启动或取消文件复制任务
        /// </remarks>
        private void checkBoxFileCopier_CheckStateChanged(object sender, EventArgs e)
        {
            try
            {
                if (checkBoxFileCopier.Checked)
                {
                    StartCopy();
                }
                else
                {
                    CancelAllCopying();
                }
            }
            catch (Exception ex)
            {
                LogError($"切换复制状态时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 窗体关闭事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">窗体关闭事件参数</param>
        /// <remarks>
        /// 当用户关闭窗口时，隐藏窗体而不是真正关闭它
        /// </remarks>
        private void FileCopier_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                // 取消默认的关闭操作
                e.Cancel = true;
                ETForm.Hide(this);
            }
        }

        /// <summary>
        /// 窗体加载事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 窗体加载时根据复选框状态初始化复制任务
        /// </remarks>
        private void FileCopier_Load(object sender, EventArgs e)
        {
            checkBoxFileCopier_CheckStateChanged(null, null);
        }
    }
}