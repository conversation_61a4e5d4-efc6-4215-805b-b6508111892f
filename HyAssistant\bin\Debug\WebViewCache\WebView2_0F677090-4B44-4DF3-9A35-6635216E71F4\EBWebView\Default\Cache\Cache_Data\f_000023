var _util;
(function (_util) {
    $.ajaxSetup({
        xhrFields: {
            withCredentials: true
        }
    });
    $.fn.safeHtml = function (content) {
        if (typeof content === 'string') {
            return this.text(content);
        }
        return this.html(content);
    };
    function formatDate(d, f) {
        f = f || 'YYYY-MM-DD HH:mm';
        if (typeof d === 'number')
            d = new Date(d);
        if (typeof d === 'object') {
            if (window['moment'])
                d = window['moment'](d).format(f);
            else if (d.Format)
                d = d.Format(f);
            else
                d = _formatDate(d, f);
        }
        return d;
    }
    _util.formatDate = formatDate;
    function _formatDate(date, fmt) {
        var o = {
            "M+": date.getMonth() + 1,
            "D+": date.getDate(),
            "H+": date.getHours(),
            "m+": date.getMinutes(),
            "s+": date.getSeconds(),
            "q+": Math.floor((date.getMonth() + 3) / 3),
            "S": date.getMilliseconds()
        };
        if (/(Y+)/.test(fmt))
            fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (var k in o)
            if (new RegExp("(" + k + ")").test(fmt))
                fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
    }
    function getUrlParams(url) {
        if (url === void 0) { url = null; }
        url = url || window.location.href;
        var vars = {}, hash;
        var hashes = url.slice(url.indexOf('?') + 1).split('&');
        for (var i = 0; i < hashes.length; i++) {
            hash = hashes[i].split('=');
            vars[hash[0]] = decodeURIComponent(hash[1]);
        }
        return vars;
    }
    _util.getUrlParams = getUrlParams;
    function appendUrlParam(url, k, v) {
        if (v === undefined) {
            if (typeof k === 'object') {
                for (var k1 in k)
                    url = appendUrlParam(url, k1, k[k1]);
                return url;
            }
            else if (typeof k === 'string') {
                var j = url.indexOf('?') === -1 ? '?' : '&';
                return url + j + k;
            }
        }
        if (v === undefined || v === null)
            return url;
        if (typeof v === 'object')
            v = JSON.stringify(v);
        v = encodeURIComponent(v);
        var j = url.indexOf('?') === -1 ? '?' : '&';
        return url + j + k + '=' + v;
    }
    _util.appendUrlParam = appendUrlParam;
    function isjquery(o) {
        try {
            return o.jquery;
        }
        catch (e) {
            return false;
        }
    }
    _util.isjquery = isjquery;
    function getFullSelector(el) {
        if (!isjquery(el))
            el = $(el);
        var selector = el
            .parents()
            .map(function () { return this.tagName; })
            .get()
            .reverse()
            .concat([this.nodeName])
            .join(">");
        var id = el.attr("id");
        if (id) {
            selector += "#" + id;
        }
        var classNames = el.attr("class");
        if (classNames) {
            selector += "." + $.trim(classNames).replace(/\s/gi, ".");
        }
        return selector;
    }
    _util.getFullSelector = getFullSelector;
    function jQueryToPlain(node, childrenNode, singleChildAsAttr, nodeFix) {
        if (childrenNode === void 0) { childrenNode = 'children'; }
        if (singleChildAsAttr === void 0) { singleChildAsAttr = false; }
        if (nodeFix === void 0) { nodeFix = null; }
        if (isjquery(node)) {
            if (node.length === 0)
                return [];
            var ret = [];
            node.each(function (i, n) { return ret.push(jQueryToPlain(n, childrenNode, singleChildAsAttr, nodeFix)); });
            return ret;
        }
        var o = {};
        if (!node)
            return null;
        if (node.attributes)
            $.each(node.attributes, function (i, a) {
                var v = a.value;
                if (v === 'true')
                    v = true;
                else if (v === 'false')
                    v = false;
                else if (v.endsWith(':int'))
                    v = parseInt(v.substring(0, v.length - 4));
                else if (v.endsWith(':float'))
                    v = parseInt(v.substring(0, v.length - 6));
                o[a.name] = v;
            });
        o.tagName = o.tagName || node.tagName;
        var children = node.children;
        if (!children && node.childNodes)
            children = node.childNodes;
        if (children && children.length && childrenNode !== false) {
            var childrenAttr_1;
            if (typeof childrenNode === 'string')
                childrenAttr_1 = childrenNode;
            $.each(children, function (i, c) {
                if (c.nodeType !== 1)
                    return;
                var n = jQueryToPlain(c, childrenNode, singleChildAsAttr, nodeFix);
                var name = childrenAttr_1;
                if (!name) {
                    if (childrenNode === true)
                        name = n.tagName;
                    else if (typeof childrenNode === 'object')
                        name = childrenNode[n.tagName];
                    else if (typeof childrenNode === 'function')
                        name = childrenNode(n.tagName);
                }
                name = name || 'children';
                var ex = o[name];
                if (!ex)
                    o[name] = singleChildAsAttr ? n : [n];
                else {
                    if ($.isArray(ex))
                        ex.push(n);
                    else
                        o[name] = [ex, n];
                }
            });
        }
        if (!o.content && node.textContent)
            o.content = $.trim(node.textContent);
        if (nodeFix)
            nodeFix(o);
        return o;
    }
    _util.jQueryToPlain = jQueryToPlain;
    function dataCallback(callback, dataProvider, dataFnParams, dataFnScope) {
        if (dataFnParams === void 0) { dataFnParams = null; }
        if (dataFnScope === void 0) { dataFnScope = null; }
        if (typeof dataProvider === 'function') {
            try {
                var data = dataProvider.apply(dataFnScope, dataFnParams);
                return dataCallback(callback, data, dataFnParams, dataFnScope);
            }
            catch (e) {
                console.error(e);
                return callback(e, true);
            }
        }
        else if (isPromise(dataProvider)) {
            return dataProvider.then(function (data) {
                dataCallback(callback, data, dataFnParams, dataFnScope);
            }, function (err) {
                if (window['_context'] && window['_context'].debugMode)
                    console.log(err);
                return callback(err, true);
            });
        }
        else {
            return callback(dataProvider);
        }
    }
    _util.dataCallback = dataCallback;
    function appendDataCallback(el, dataProvider) {
        dataCallback(function (data) {
            el.append(data);
        }, dataProvider);
    }
    _util.appendDataCallback = appendDataCallback;
    function loadingRender(dataProvider, el, iconClass, trigger) {
        if (el === void 0) { el = undefined; }
        if (iconClass === void 0) { iconClass = undefined; }
        if (trigger === void 0) { trigger = undefined; }
        el = el || '<div>';
        if (typeof el === 'string')
            el = $(el);
        iconClass = iconClass || 'loading spinner';
        var icon = $('<i class="icon">').addClass(iconClass).appendTo(el);
        if (dataProvider)
            dataCallback(function (data) {
                icon.remove();
                el.append(data);
                if (trigger)
                    trigger(data);
            }, dataProvider);
        return el;
    }
    _util.loadingRender = loadingRender;
    function fetchField(item, field) {
        if (item === null || item === undefined)
            return item;
        if (Array.isArray(field)) {
            var r_1;
            field.some(function (f) {
                r_1 = fetchField(item, f);
                return r_1;
            });
            return r_1;
        }
        if (typeof field === 'string')
            return fetchPropertyValue(item, field);
        if (typeof field === 'function')
            return field(item);
        return field;
    }
    _util.fetchField = fetchField;
    function doAction(action, params, source, targetEl) {
        if (source === void 0) { source = null; }
        if (targetEl === void 0) { targetEl = null; }
        if (action instanceof Function)
            return action(params, source, targetEl);
        if (action.fn instanceof Function)
            return action.fn(params, source, targetEl);
        return _context.doAction(action, params, source, targetEl);
    }
    _util.doAction = doAction;
    function getCookie(name) {
        var cookieStr = document.cookie;
        if (cookieStr.length > 0) {
            var cookieArr = cookieStr.split(";");
            for (var i = 0; i < cookieArr.length; i++) {
                var cookieVal = cookieArr[i].split("=");
                if (cookieVal[0].trim() == name) {
                    return unescape(cookieVal[1]);
                }
            }
        }
    }
    _util.getCookie = getCookie;
    function setCookie(name, value, days) {
        if (days === void 0) { days = 365; }
        var expires;
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date['toGMTString']();
        }
        else {
            expires = "";
        }
        document.cookie = encodeURIComponent(name) + "=" + encodeURIComponent(value) + expires + ";";
    }
    _util.setCookie = setCookie;
    function isSafari() {
        return navigator.userAgent.indexOf('Safari') !== -1 && navigator.userAgent.indexOf('Chome') === -1;
    }
    _util.isSafari = isSafari;
    function fetchPropertyValue(o, name) {
        var v = o[name];
        if (v !== undefined)
            return v;
        if (typeof name === 'string') {
            if (name.indexOf('.') === -1)
                return v;
            name = name.split('.');
        }
        v = o[name.shift()];
        if (v === undefined)
            return v;
        name.forEach(function (n) {
            if (v)
                v = v[n];
        });
        return v;
    }
    _util.fetchPropertyValue = fetchPropertyValue;
    function windowNameSpaceDefined(namespace) {
        if (!namespace)
            return false;
        if (typeof namespace === 'function')
            return namespace();
        var loaded = true;
        var ns = namespace.split('.'), parentNs = window;
        for (var i = 0; i < ns.length; i++) {
            parentNs = parentNs[ns[i]];
            if (!parentNs) {
                loaded = false;
                break;
            }
        }
        return loaded ? parentNs : false;
    }
    var scriptsLoading = {};
    function loadScript(url, windowNameSpace, resolveWhenError) {
        if (windowNameSpace === void 0) { windowNameSpace = undefined; }
        if (resolveWhenError === void 0) { resolveWhenError = false; }
        var nm = windowNameSpaceDefined(windowNameSpace);
        if (nm)
            return Promise.resolve(nm);
        var promise = scriptsLoading[url] || new Promise(function (resolve, reject) {
            var script = document.createElement('script');
            script.onload = function (e) {
                delete scriptsLoading[url];
                resolve(e);
            };
            script.onerror = function (e) {
                delete scriptsLoading[url];
                resolveWhenError ? resolve(e) : reject(e);
            };
            script.src = url;
            document.head.appendChild(script);
        });
        return promise;
    }
    _util.loadScript = loadScript;
    function loadScripts(urls, windowNameSpace, inOrder) {
        if (windowNameSpace === void 0) { windowNameSpace = undefined; }
        if (inOrder === void 0) { inOrder = false; }
        if (Array.isArray(windowNameSpace)) {
            var nurls_1 = [];
            urls.forEach(function (url, i) {
                var nm = windowNameSpace[i];
                if (!nm || !windowNameSpaceDefined(nm))
                    nurls_1.push(url);
            });
            urls = nurls_1;
        }
        else {
            var nm = windowNameSpaceDefined(windowNameSpace);
            if (nm)
                return Promise.resolve(nm);
        }
        if (!inOrder)
            return Promise.all(urls.map(function (url) { return loadScript(url, undefined, true).then(undefined, function (e) { return Promise.resolve(e); }); }));
        urls = urls.slice();
        return new Promise(function (resolve, reject) {
            function fetch() {
                var url = urls.shift();
                if (!url)
                    return resolve(null);
                loadScript(url).then(fetch, fetch);
            }
            fetch();
        });
    }
    _util.loadScripts = loadScripts;
    function loadCss(urls, attrs) {
        if (attrs === void 0) { attrs = undefined; }
        function loadone(url) {
            var links = document.getElementsByTagName('link');
            for (var i = 0; i < links.length; i++) {
                if (links[i].href === url)
                    return Promise.resolve(null);
            }
            return new Promise(function (resolve, reject) {
                var link = document.createElement('link');
                link.href = url;
                attrs = Object.assign({ type: 'text/css', rel: 'stylesheet' }, attrs);
                for (var k in attrs)
                    link[k] = attrs[k];
                document.head.appendChild(link);
                link.onload = function (e) {
                    resolve(null);
                };
                link.onerror = function (e) {
                    reject();
                };
            });
        }
        if (typeof urls === 'string')
            urls = [urls];
        return Promise.all(urls.map(function (url) { return loadone(url); }));
    }
    _util.loadCss = loadCss;
    function isPromise(o) {
        return o && typeof o.then === 'function';
    }
    _util.isPromise = isPromise;
    function merge(to, from, mergeArray, compare, fix) {
        if (mergeArray === void 0) { mergeArray = true; }
        if (compare === void 0) { compare = null; }
        if (fix === void 0) { fix = null; }
        if (to === from)
            return to;
        if (Array.isArray(to) && Array.isArray(from)) {
            from.forEach(function (f) {
                var exist = to.find(function (t) {
                    if (compare && compare(t, f))
                        return true;
                    return t === f;
                });
                if (exist)
                    merge(exist, f, mergeArray, compare);
                else
                    to.push(f);
            });
        }
        else if (typeof to === 'object' && typeof from === 'object') {
            if (fix)
                fix(from);
            for (var k in from) {
                if (from.hasOwnProperty(k)) {
                    var o = to[k], t = from[k];
                    if ((Array.isArray(o) && Array.isArray(t) && mergeArray) || (typeof t === 'object' && typeof o === 'object'))
                        merge(o, t, mergeArray, compare, fix);
                    else if (t !== undefined)
                        to[k] = t;
                }
            }
        }
        return to;
    }
    _util.merge = merge;
    function assignIf(to, from) {
        to = to || {};
        for (var i = 1; i < arguments.length; i++) {
            from = arguments[i];
            for (var k in from) {
                if (from[k] !== undefined)
                    to[k] = from[k];
            }
        }
        return to;
    }
    _util.assignIf = assignIf;
    function merges(to, from, mergeArray, compare, fix) {
        if (mergeArray === void 0) { mergeArray = true; }
        if (compare === void 0) { compare = null; }
        if (fix === void 0) { fix = null; }
        from.forEach(function (f) { return merge(to, f, mergeArray, compare, fix); });
        return to;
    }
    _util.merges = merges;
    function traval(o, t) {
        if (!o || o instanceof HTMLElement || isjquery(o))
            return;
        t(o);
        for (var k in o)
            if (typeof o[k] === 'object')
                traval(o[k], t);
    }
    _util.traval = traval;
    function scrollToIf(el, parentEl, transition) {
        if (parentEl === void 0) { parentEl = null; }
        if (transition === void 0) { transition = false; }
        el = $(el);
        parentEl = parentEl ? $(parentEl) : el.parent();
        var top = el[0].offsetTop;
        if (parentEl[0] !== el[0].offsetParent)
            top -= parentEl[0].offsetTop;
        var current = parentEl.scrollTop();
        if (top < current || top > current + parentEl[0].offsetHeight)
            parentEl.scrollTop(top - parentEl.height() / 2);
        if (transition) {
            if (transition === true)
                transition = 'glow';
            if (typeof transition === 'string')
                transition = { animation: transition };
            if (transition.completeClass)
                transition.onComplete = function () {
                    el.addClass(transition.completeClass);
                };
            el.transition(transition);
        }
    }
    _util.scrollToIf = scrollToIf;
    function replaceVar(str, scope, nullIfReplaceNone) {
        if (nullIfReplaceNone === void 0) { nullIfReplaceNone = false; }
        var replaced = true;
        var nstr = str.replace(/\${[^}]+}/g, function (key) {
            key = key.substring(2, key.length - 1);
            var v;
            if (typeof scope === 'function')
                v = scope(key);
            else if (scope) {
                v = fetchPropertyValue(scope, key);
            }
            if (v === undefined) {
                replaced = false;
                v = '';
            }
            return v;
        });
        if (nullIfReplaceNone && !replaced)
            return null;
        return nstr;
    }
    _util.replaceVar = replaceVar;
    function setStorage(key, value, remote) {
        if (remote === void 0) { remote = false; }
        if (value === null || value === undefined) {
            localStorage.removeItem(key);
        }
        else {
            value = JSON.stringify(value);
            localStorage.setItem(key, value);
        }
        if (remote && window['_bean']) {
            return window['_bean'].post('app/storage.do', { name: key, value: value, _quiet: true });
        }
    }
    _util.setStorage = setStorage;
    function getStorage(key, remote) {
        if (remote === void 0) { remote = undefined; }
        if (remote !== undefined) {
            if (remote && window['_bean'])
                return window['_bean'].post('app/storage.do', { name: key, _quiet: true }).then(function (data) {
                    return data.value ? JSON.parse(data.value) : getStorage(key, undefined);
                }, function () {
                    return getStorage(key, undefined);
                });
            return Promise.resolve(getStorage(key, undefined));
        }
        var v = localStorage.getItem(key);
        return v ? JSON.parse(v) : undefined;
    }
    _util.getStorage = getStorage;
    function parsekv(s, transformBool) {
        if (transformBool === void 0) { transformBool = true; }
        if (typeof s !== 'string')
            return s;
        var bp;
        if (s.startsWith('{') || s.startsWith('['))
            bp = JSON.parse(s);
        else {
            bp = {};
            s.split(',').forEach(function (kv) {
                var kvs = kv.split(':');
                var v = kvs[1].trim();
                if (transformBool && ('true' === v || 'false' === v))
                    v = 'true' === v;
                bp[kvs[0].trim()] = v;
            });
        }
        return bp;
    }
    _util.parsekv = parsekv;
    function reload() {
        setTimeout(function () { location.reload(); }, 100);
    }
    _util.reload = reload;
    function intToRgb(i) {
        var r = ((i >> 16) & 0xff).toString(16);
        if (r.length === 1)
            r = '0' + r;
        var g = ((i >> 8) & 0xff).toString(16);
        if (g.length === 1)
            g = '0' + g;
        var b = (i & 0xff).toString(16);
        if (b.length === 1)
            b = '0' + b;
        return r + g + b;
    }
    _util.intToRgb = intToRgb;
    function bind(el, scope, handler, attr, trigger) {
        if (!_util.isjquery(el))
            el = $(el);
        function getAttr(ele) {
            var k = attr;
            if (!k)
                k = ele.attr('name') || ele.attr('id');
            if (typeof k === 'function')
                k = k(ele);
            return k;
        }
        el.each(function () {
            var ele = $(this);
            var k = getAttr(ele);
            if (!k)
                return;
            var v = scope[k];
            var inputType = ele.attr('type');
            if (inputType === 'checkbox')
                ele.prop('checked', v);
            else
                ele.val(v);
        });
        el.on(trigger || 'change', function () {
            var ele = $(this), inputType = ele.attr('type'), v = ele.val();
            if (inputType === 'checkbox')
                v = ele.prop('checked');
            var k = getAttr(ele);
            if (k)
                scope[k] = v;
            if (handler)
                handler(k, v);
        });
    }
    _util.bind = bind;
    function ajax() {
        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments[_i];
        }
        return new Promise(function (reslove, reject) {
            $.ajax.apply($, args).done(function (data) { return reslove(data); }).fail(function (data) { return reject(data); });
        });
    }
    _util.ajax = ajax;
    function get() {
        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments[_i];
        }
        return new Promise(function (reslove, reject) {
            $.get.apply($, args).done(function (data) { return reslove(data); }).fail(function (data) { return reject(data); });
        });
    }
    _util.get = get;
    function post() {
        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments[_i];
        }
        return new Promise(function (reslove, reject) {
            $.post.apply($, args).done(function (data) { return reslove(data); }).fail(function (data) { return reject(data); });
        });
    }
    _util.post = post;
    function getContextPath() {
        var base = document.getElementsByTagName('base')[0];
        if (base && base.href && base.href.length) {
            base = base.href;
        }
        else {
            base = document.URL;
        }
        base = base.substring(0, base.indexOf('?'));
        var pos = base.lastIndexOf('/');
        if (pos - 1 === base.indexOf('//'))
            pos = -1;
        return pos === -1 ? base : base.substring(0, pos);
    }
    _util.getContextPath = getContextPath;
    function style(el, style) {
        var css = {}, kv;
        if (typeof style !== 'string')
            css = style;
        else
            style.split(';').forEach(function (kvs) {
                kv = kvs.split(':');
                if (kv.length === 2)
                    css[kv[0].trim()] = kv[1].trim();
            });
        if (!isjquery(el))
            el = $(el);
        el.css(css);
    }
    _util.style = style;
    function upload(blob, url, fieldName) {
        var formData = new FormData();
        formData.append(fieldName || 'file', blob);
        return $.ajax({ url: url, type: 'POST', cache: false, data: formData, processData: false, contentType: false });
    }
    _util.upload = upload;
    var _selectfileinput;
    function selectFile(callback, accept, multiple) {
        if (accept === void 0) { accept = undefined; }
        if (multiple === void 0) { multiple = false; }
        if (_selectfileinput)
            _selectfileinput.remove();
        var input = _selectfileinput = $('<input type="file">').css({ display: 'none', position: 'absolute' });
        if (accept)
            input.attr('accept', accept);
        if (multiple)
            input.attr('multiple', true);
        input.appendTo(document.body);
        input.change(function (e) {
            input.remove();
            _selectfileinput = undefined;
            callback(multiple ? e.target.files : e.target.files[0]);
        });
        input.click();
    }
    _util.selectFile = selectFile;
    function selectFiles(callback, accept) {
        if (accept === void 0) { accept = undefined; }
        return selectFile(callback, accept, true);
    }
    _util.selectFiles = selectFiles;
    function selectFileAndRead(callback, accept) {
        if (accept === void 0) { accept = undefined; }
        return selectFile(function (file) {
            var reader = new FileReader();
            reader.readAsBinaryString(file);
            reader.onload = function (e) {
                var data = e.target.result;
                callback(data);
            };
        }, accept);
    }
    _util.selectFileAndRead = selectFileAndRead;
    function base64StringToByteArray(base64String) {
        try {
            var sliceSize = 1024;
            var byteCharacters = atob(base64String);
            var bytesLength = byteCharacters.length;
            var slicesCount = Math.ceil(bytesLength / sliceSize);
            var byteArrays = new Array(slicesCount);
            for (var sliceIndex = 0; sliceIndex < slicesCount; ++sliceIndex) {
                var begin = sliceIndex * sliceSize;
                var end = Math.min(begin + sliceSize, bytesLength);
                var bytes = new Array(end - begin);
                for (var offset = begin, i = 0; offset < end; ++i, ++offset) {
                    bytes[i] = byteCharacters[offset].charCodeAt(0);
                }
                byteArrays[sliceIndex] = new Uint8Array(bytes);
            }
            return byteArrays;
        }
        catch (e) {
            console.log("Couldn't convert to byte array: " + e);
            return undefined;
        }
    }
    _util.base64StringToByteArray = base64StringToByteArray;
    function download(byteArray, fileName, type, options) {
        if (options === void 0) { options = undefined; }
        var alink = document.createElement("a");
        options = Object.assign({ type: type }, options);
        var blob = new Blob(byteArray, options);
        if (fileName)
            alink.download = fileName;
        else
            alink.target = '_blank';
        var href = alink.href = URL.createObjectURL(blob);
        alink.click();
        alink.remove();
        URL.revokeObjectURL(href);
    }
    _util.download = download;
    function computeDisplayWidth(innerHtml, parentEl, returnNumber) {
        if (returnNumber === void 0) { returnNumber = true; }
        parentEl = parentEl || $('body');
        var div = $('<div>').html(innerHtml).appendTo(parentEl).css({ display: 'none', position: 'absolute' });
        var w = div.css('width');
        div.remove();
        if (returnNumber && typeof w === 'string') {
            w = w.replace('px', '');
            w = parseFloat(w);
        }
        return w;
    }
    _util.computeDisplayWidth = computeDisplayWidth;
    function copy(text, successTip) {
        try {
            navigator['clipboard'].writeText(text);
            if (successTip && window['_context'])
                window['_context'].message(successTip);
            return;
        }
        catch (e) { }
        var transfer = document.createElement('input');
        document.body.appendChild(transfer);
        transfer.value = text;
        transfer.focus();
        transfer.select();
        document.execCommand('copy', false, null);
        transfer.blur();
        document.body.removeChild(transfer);
        if (successTip && window['_context'])
            window['_context'].message(successTip);
    }
    _util.copy = copy;
    function evalScriptNode(nodeDef, async) {
        if (async === void 0) { async = false; }
        var src = nodeDef.url || nodeDef.src;
        if (src) {
            if (window['_context'])
                src = window['_context'].fixUrl(src);
            $.ajax({ url: src, type: "GET", dataType: "script", cache: true, async: async, global: false, "throws": true });
        }
        else if (nodeDef.content)
            eval(nodeDef.content);
    }
    _util.evalScriptNode = evalScriptNode;
    function delayFn(fn, time, scope) {
        var timer, args;
        function nfn() {
            fn.apply(scope, args);
        }
        function wfn() {
            clearTimeout(timer);
            args = arguments;
            timer = setTimeout(nfn, time);
        }
        return wfn;
    }
    _util.delayFn = delayFn;
    var iconSizeCache = {};
    function findIconSize(iconUrl) {
        if (iconSizeCache[iconUrl])
            return Promise.resolve(iconSizeCache[iconUrl]);
        return new Promise(function (resolve, reject) {
            $('<image style="display:none;" src="' + iconUrl + '">').appendTo('body').on('load', function () {
                var w = this.naturalWidth, h = this.naturalHeight;
                $(this).remove();
                var wh = { width: w, height: h };
                iconSizeCache[iconUrl] = wh;
                resolve(wh);
            });
        });
    }
    _util.findIconSize = findIconSize;
    var strChineseFirstPY = "YDYQSXMWZSSXJBYMGCCZQPSSQBYCDSCDQLDYLYBSSJGYZZJJFKCCLZDHWDWZJLJPFYYNWJJTMYHZWZHFLZPPQHGSCYYYNJQYXXGJHHSDSJNKKTMOMLCRXYPSNQSECCQZGGLLYJLMYZZSECYKYYHQWJSSGGYXYZYJWWKDJHYCHMYXJTLXJYQBYXZLDWRDJRWYSRLDZJPCBZJJBRCFTLECZSTZFXXZHTRQHYBDLYCZSSYMMRFMYQZPWWJJYFCRWFDFZQPYDDWYXKYJAWJFFXYPSFTZYHHYZYSWCJYXSCLCXXWZZXNBGNNXBXLZSZSBSGPYSYZDHMDZBQBZCWDZZYYTZHBTSYYBZGNTNXQYWQSKBPHHLXGYBFMJEBJHHGQTJCYSXSTKZHLYCKGLYSMZXYALMELDCCXGZYRJXSDLTYZCQKCNNJWHJTZZCQLJSTSTBNXBTYXCEQXGKWJYFLZQLYHYXSPSFXLMPBYSXXXYDJCZYLLLSJXFHJXPJBTFFYABYXBHZZBJYZLWLCZGGBTSSMDTJZXPTHYQTGLJSCQFZKJZJQNLZWLSLHDZBWJNCJZYZSQQYCQYRZCJJWYBRTWPYFTWEXCSKDZCTBZHYZZYYJXZCFFZZMJYXXSDZZOTTBZLQWFCKSZSXFYRLNYJMBDTHJXSQQCCSBXYYTSYFBXDZTGBCNSLCYZZPSAZYZZSCJCSHZQYDXLBPJLLMQXTYDZXSQJTZPXLCGLQTZWJBHCTSYJSFXYEJJTLBGXSXJMYJQQPFZASYJNTYDJXKJCDJSZCBARTDCLYJQMWNQNCLLLKBYBZZSYHQQLTWLCCXTXLLZNTYLNEWYZYXCZXXGRKRMTCNDNJTSYYSSDQDGHSDBJGHRWRQLYBGLXHLGTGXBQJDZPYJSJYJCTMRNYMGRZJCZGJMZMGXMPRYXKJNYMSGMZJYMKMFXMLDTGFBHCJHKYLPFMDXLQJJSMTQGZSJLQDLDGJYCALCMZCSDJLLNXDJFFFFJCZFMZFFPFKHKGDPSXKTACJDHHZDDCRRCFQYJKQCCWJDXHWJLYLLZGCFCQDSMLZPBJJPLSBCJGGDCKKDEZSQCCKJGCGKDJTJDLZYCXKLQSCGJCLTFPCQCZGWPJDQYZJJBYJHSJDZWGFSJGZKQCCZLLPSPKJGQJHZZLJPLGJGJJTHJJYJZCZMLZLYQBGJWMLJKXZDZNJQSYZMLJLLJKYWXMKJLHSKJGBMCLYYMKXJQLBMLLKMDXXKWYXYSLMLPSJQQJQXYXFJTJDXMXXLLCXQBSYJBGWYMBGGBCYXPJYGPEPFGDJGBHBNSQJYZJKJKHXQFGQZKFHYGKHDKLLSDJQXPQYKYBNQSXQNSZSWHBSXWHXWBZZXDMNSJBSBKBBZKLYLXGWXDRWYQZMYWSJQLCJXXJXKJEQXSCYETLZHLYYYSDZPAQYZCMTLSHTZCFYZYXYLJSDCJQAGYSLCQLYYYSHMRQQKLDXZSCSSSYDYCJYSFSJBFRSSZQSBXXPXJYSDRCKGJLGDKZJZBDKTCSYQPYHSTCLDJDHMXMCGXYZHJDDTMHLTXZXYLYMOHYJCLTYFBQQXPFBDFHHTKSQHZYYWCNXXCRWHOWGYJLEGWDQCWGFJYCSNTMYTOLBYGWQWESJPWNMLRYDZSZTXYQPZGCWXHNGPYXSHMYQJXZTDPPBFYHZHTJYFDZWKGKZBLDNTSXHQEEGZZYLZMMZYJZGXZXKHKSTXNXXWYLYAPSTHXDWHZYMPXAGKYDXBHNHXKDPJNMYHYLPMGOCSLNZHKXXLPZZLBMLSFBHHGYGYYGGBHSCYAQTYWLXTZQCEZYDQDQMMHTKLLSZHLSJZWFYHQSWSCWLQAZYNYTLSXTHAZNKZZSZZLAXXZWWCTGQQTDDYZTCCHYQZFLXPSLZYGPZSZNGLNDQTBDLXGTCTAJDKYWNSYZLJHHZZCWNYYZYWMHYCHHYXHJKZWSXHZYXLYSKQYSPSLYZWMYPPKBYGLKZHTYXAXQSYSHXASMCHKDSCRSWJPWXSGZJLWWSCHSJHSQNHCSEGNDAQTBAALZZMSSTDQJCJKTSCJAXPLGGXHHGXXZCXPDMMHLDGTYBYSJMXHMRCPXXJZCKZXSHMLQXXTTHXWZFKHCCZDYTCJYXQHLXDHYPJQXYLSYYDZOZJNYXQEZYSQYAYXWYPDGXDDXSPPYZNDLTWRHXYDXZZJHTCXMCZLHPYYYYMHZLLHNXMYLLLMDCPPXHMXDKYCYRDLTXJCHHZZXZLCCLYLNZSHZJZZLNNRLWHYQSNJHXYNTTTKYJPYCHHYEGKCTTWLGQRLGGTGTYGYHPYHYLQYQGCWYQKPYYYTTTTLHYHLLTYTTSPLKYZXGZWGPYDSSZZDQXSKCQNMJJZZBXYQMJRTFFBTKHZKBXLJJKDXJTLBWFZPPTKQTZTGPDGNTPJYFALQMKGXBDCLZFHZCLLLLADPMXDJHLCCLGYHDZFGYDDGCYYFGYDXKSSEBDHYKDKDKHNAXXYBPBYYHXZQGAFFQYJXDMLJCSQZLLPCHBSXGJYNDYBYQSPZWJLZKSDDTACTBXZDYZYPJZQSJNKKTKNJDJGYYPGTLFYQKASDNTCYHBLWDZHBBYDWJRYGKZYHEYYFJMSDTYFZJJHGCXPLXHLDWXXJKYTCYKSSSMTWCTTQZLPBSZDZWZXGZAGYKTYWXLHLSPBCLLOQMMZSSLCMBJCSZZKYDCZJGQQDSMCYTZQQLWZQZXSSFPTTFQMDDZDSHDTDWFHTDYZJYQJQKYPBDJYYXTLJHDRQXXXHAYDHRJLKLYTWHLLRLLRCXYLBWSRSZZSYMKZZHHKYHXKSMDSYDYCJPBZBSQLFCXXXNXKXWYWSDZYQOGGQMMYHCDZTTFJYYBGSTTTYBYKJDHKYXBELHTYPJQNFXFDYKZHQKZBYJTZBXHFDXKDASWTAWAJLDYJSFHBLDNNTNQJTJNCHXFJSRFWHZFMDRYJYJWZPDJKZYJYMPCYZNYNXFBYTFYFWYGDBNZZZDNYTXZEMMQBSQEHXFZMBMFLZZSRXYMJGSXWZJSPRYDJSJGXHJJGLJJYNZZJXHGXKYMLPYYYCXYTWQZSWHWLYRJLPXSLSXMFSWWKLCTNXNYNPSJSZHDZEPTXMYYWXYYSYWLXJQZQXZDCLEEELMCPJPCLWBXSQHFWWTFFJTNQJHJQDXHWLBYZNFJLALKYYJLDXHHYCSTYYWNRJYXYWTRMDRQHWQCMFJDYZMHMYYXJWMYZQZXTLMRSPWWCHAQBXYGZYPXYYRRCLMPYMGKSJSZYSRMYJSNXTPLNBAPPYPYLXYYZKYNLDZYJZCZNNLMZHHARQMPGWQTZMXXMLLHGDZXYHXKYXYCJMFFYYHJFSBSSQLXXNDYCANNMTCJCYPRRNYTYQNYYMBMSXNDLYLYSLJRLXYSXQMLLYZLZJJJKYZZCSFBZXXMSTBJGNXYZHLXNMCWSCYZYFZLXBRNNNYLBNRTGZQYSATSWRYHYJZMZDHZGZDWYBSSCSKXSYHYTXXGCQGXZZSHYXJSCRHMKKBXCZJYJYMKQHZJFNBHMQHYSNJNZYBKNQMCLGQHWLZNZSWXKHLJHYYBQLBFCDSXDLDSPFZPSKJYZWZXZDDXJSMMEGJSCSSMGCLXXKYYYLNYPWWWGYDKZJGGGZGGSYCKNJWNJPCXBJJTQTJWDSSPJXZXNZXUMELPXFSXTLLXCLJXJJLJZXCTPSWXLYDHLYQRWHSYCSQYYBYAYWJJJQFWQCQQCJQGXALDBZZYJGKGXPLTZYFXJLTPADKYQHPMATLCPDCKBMTXYBHKLENXDLEEGQDYMSAWHZMLJTWYGXLYQZLJEEYYBQQFFNLYXRDSCTGJGXYYNKLLYQKCCTLHJLQMKKZGCYYGLLLJDZGYDHZWXPYSJBZKDZGYZZHYWYFQYTYZSZYEZZLYMHJJHTSMQWYZLKYYWZCSRKQYTLTDXWCTYJKLWSQZWBDCQYNCJSRSZJLKCDCDTLZZZACQQZZDDXYPLXZBQJYLZLLLQDDZQJYJYJZYXNYYYNYJXKXDAZWYRDLJYYYRJLXLLDYXJCYWYWNQCCLDDNYYYNYCKCZHXXCCLGZQJGKWPPCQQJYSBZZXYJSQPXJPZBSBDSFNSFPZXHDWZTDWPPTFLZZBZDMYYPQJRSDZSQZSQXBDGCPZSWDWCSQZGMDHZXMWWFYBPDGPHTMJTHZSMMBGZMBZJCFZWFZBBZMQCFMBDMCJXLGPNJBBXGYHYYJGPTZGZMQBQTCGYXJXLWZKYDPDYMGCFTPFXYZTZXDZXTGKMTYBBCLBJASKYTSSQYYMSZXFJEWLXLLSZBQJJJAKLYLXLYCCTSXMCWFKKKBSXLLLLJYXTYLTJYYTDPJHNHNNKBYQNFQYYZBYYESSESSGDYHFHWTCJBSDZZTFDMXHCNJZYMQWSRYJDZJQPDQBBSTJGGFBKJBXTGQHNGWJXJGDLLTHZHHYYYYYYSXWTYYYCCBDBPYPZYCCZYJPZYWCBDLFWZCWJDXXHYHLHWZZXJTCZLCDPXUJCZZZLYXJJTXPHFXWPYWXZPTDZZBDZCYHJHMLXBQXSBYLRDTGJRRCTTTHYTCZWMXFYTWWZCWJWXJYWCSKYBZSCCTZQNHXNWXXKHKFHTSWOCCJYBCMPZZYKBNNZPBZHHZDLSYDDYTYFJPXYNGFXBYQXCBHXCPSXTYZDMKYSNXSXLHKMZXLYHDHKWHXXSSKQYHHCJYXGLHZXCSNHEKDTGZXQYPKDHEXTYKCNYMYYYPKQYYYKXZLTHJQTBYQHXBMYHSQCKWWYLLHCYYLNNEQXQWMCFBDCCMLJGGXDQKTLXKGNQCDGZJWYJJLYHHQTTTNWCHMXCXWHWSZJYDJCCDBQCDGDNYXZTHCQRXCBHZTQCBXWGQWYYBXHMBYMYQTYEXMQKYAQYRGYZSLFYKKQHYSSQYSHJGJCNXKZYCXSBXYXHYYLSTYCXQTHYSMGSCPMMGCCCCCMTZTASMGQZJHKLOSQYLSWTMXSYQKDZLJQQYPLSYCZTCQQPBBQJZCLPKHQZYYXXDTDDTSJCXFFLLCHQXMJLWCJCXTSPYCXNDTJSHJWXDQQJSKXYAMYLSJHMLALYKXCYYDMNMDQMXMCZNNCYBZKKYFLMCHCMLHXRCJJHSYLNMTJZGZGYWJXSRXCWJGJQHQZDQJDCJJZKJKGDZQGJJYJYLXZXXCDQHHHEYTMHLFSBDJSYYSHFYSTCZQLPBDRFRZTZYKYWHSZYQKWDQZRKMSYNBCRXQBJYFAZPZZEDZCJYWBCJWHYJBQSZYWRYSZPTDKZPFPBNZTKLQYHBBZPNPPTYZZYBQNYDCPJMMCYCQMCYFZZDCMNLFPBPLNGQJTBTTNJZPZBBZNJKLJQYLNBZQHKSJZNGGQSZZKYXSHPZSNBCGZKDDZQANZHJKDRTLZLSWJLJZLYWTJNDJZJHXYAYNCBGTZCSSQMNJPJYTYSWXZFKWJQTKHTZPLBHSNJZSYZBWZZZZLSYLSBJHDWWQPSLMMFBJDWAQYZTCJTBNNWZXQXCDSLQGDSDPDZHJTQQPSWLYYJZLGYXYZLCTCBJTKTYCZJTQKBSJLGMGZDMCSGPYNJZYQYYKNXRPWSZXMTNCSZZYXYBYHYZAXYWQCJTLLCKJJTJHGDXDXYQYZZBYWDLWQCGLZGJGQRQZCZSSBCRPCSKYDZNXJSQGXSSJMYDNSTZTPBDLTKZWXQWQTZEXNQCZGWEZKSSBYBRTSSSLCCGBPSZQSZLCCGLLLZXHZQTHCZMQGYZQZNMCOCSZJMMZSQPJYGQLJYJPPLDXRGZYXCCSXHSHGTZNLZWZKJCXTCFCJXLBMQBCZZWPQDNHXLJCTHYZLGYLNLSZZPCXDSCQQHJQKSXZPBAJYEMSMJTZDXLCJYRYYNWJBNGZZTMJXLTBSLYRZPYLSSCNXPHLLHYLLQQZQLXYMRSYCXZLMMCZLTZSDWTJJLLNZGGQXPFSKYGYGHBFZPDKMWGHCXMSGDXJMCJZDYCABXJDLNBCDQYGSKYDQTXDJJYXMSZQAZDZFSLQXYJSJZYLBTXXWXQQZBJZUFBBLYLWDSLJHXJYZJWTDJCZFQZQZZDZSXZZQLZCDZFJHYSPYMPQZMLPPLFFXJJNZZYLSJEYQZFPFZKSYWJJJHRDJZZXTXXGLGHYDXCSKYSWMMZCWYBAZBJKSHFHJCXMHFQHYXXYZFTSJYZFXYXPZLCHMZMBXHZZSXYFYMNCWDABAZLXKTCSHHXKXJJZJSTHYGXSXYYHHHJWXKZXSSBZZWHHHCWTZZZPJXSNXQQJGZYZYWLLCWXZFXXYXYHXMKYYSWSQMNLNAYCYSPMJKHWCQHYLAJJMZXHMMCNZHBHXCLXTJPLTXYJHDYYLTTXFSZHYXXSJBJYAYRSMXYPLCKDUYHLXRLNLLSTYZYYQYGYHHSCCSMZCTZQXKYQFPYYRPFFLKQUNTSZLLZMWWTCQQYZWTLLMLMPWMBZSSTZRBPDDTLQJJBXZCSRZQQYGWCSXFWZLXCCRSZDZMCYGGDZQSGTJSWLJMYMMZYHFBJDGYXCCPSHXNZCSBSJYJGJMPPWAFFYFNXHYZXZYLREMZGZCYZSSZDLLJCSQFNXZKPTXZGXJJGFMYYYSNBTYLBNLHPFZDCYFBMGQRRSSSZXYSGTZRNYDZZCDGPJAFJFZKNZBLCZSZPSGCYCJSZLMLRSZBZZLDLSLLYSXSQZQLYXZLSKKBRXBRBZCYCXZZZEEYFGKLZLYYHGZSGZLFJHGTGWKRAAJYZKZQTSSHJJXDCYZUYJLZYRZDQQHGJZXSSZBYKJPBFRTJXLLFQWJHYLQTYMBLPZDXTZYGBDHZZRBGXHWNJTJXLKSCFSMWLSDQYSJTXKZSCFWJLBXFTZLLJZLLQBLSQMQQCGCZFPBPHZCZJLPYYGGDTGWDCFCZQYYYQYSSCLXZSKLZZZGFFCQNWGLHQYZJJCZLQZZYJPJZZBPDCCMHJGXDQDGDLZQMFGPSYTSDYFWWDJZJYSXYYCZCYHZWPBYKXRYLYBHKJKSFXTZJMMCKHLLTNYYMSYXYZPYJQYCSYCWMTJJKQYRHLLQXPSGTLYYCLJSCPXJYZFNMLRGJJTYZBXYZMSJYJHHFZQMSYXRSZCWTLRTQZSSTKXGQKGSPTGCZNJSJCQCXHMXGGZTQYDJKZDLBZSXJLHYQGGGTHQSZPYHJHHGYYGKGGCWJZZYLCZLXQSFTGZSLLLMLJSKCTBLLZZSZMMNYTPZSXQHJCJYQXYZXZQZCPSHKZZYSXCDFGMWQRLLQXRFZTLYSTCTMJCXJJXHJNXTNRZTZFQYHQGLLGCXSZSJDJLJCYDSJTLNYXHSZXCGJZYQPYLFHDJSBPCCZHJJJQZJQDYBSSLLCMYTTMQTBHJQNNYGKYRQYQMZGCJKPDCGMYZHQLLSLLCLMHOLZGDYYFZSLJCQZLYLZQJESHNYLLJXGJXLYSYYYXNBZLJSSZCQQCJYLLZLTJYLLZLLBNYLGQCHXYYXOXCXQKYJXXXYKLXSXXYQXCYKQXQCSGYXXYQXYGYTQOHXHXPYXXXULCYEYCHZZCBWQBBWJQZSCSZSSLZYLKDESJZWMYMCYTSDSXXSCJPQQSQYLYYZYCMDJDZYWCBTJSYDJKCYDDJLBDJJSODZYSYXQQYXDHHGQQYQHDYXWGMMMAJDYBBBPPBCMUUPLJZSMTXERXJMHQNUTPJDCBSSMSSSTKJTSSMMTRCPLZSZMLQDSDMJMQPNQDXCFYNBFSDQXYXHYAYKQYDDLQYYYSSZBYDSLNTFQTZQPZMCHDHCZCWFDXTMYQSPHQYYXSRGJCWTJTZZQMGWJJTJHTQJBBHWZPXXHYQFXXQYWYYHYSCDYDHHQMNMTMWCPBSZPPZZGLMZFOLLCFWHMMSJZTTDHZZYFFYTZZGZYSKYJXQYJZQBHMBZZLYGHGFMSHPZFZSNCLPBQSNJXZSLXXFPMTYJYGBXLLDLXPZJYZJYHHZCYWHJYLSJEXFSZZYWXKZJLUYDTMLYMQJPWXYHXSKTQJEZRPXXZHHMHWQPWQLYJJQJJZSZCPHJLCHHNXJLQWZJHBMZYXBDHHYPZLHLHLGFWLCHYYTLHJXCJMSCPXSTKPNHQXSRTYXXTESYJCTLSSLSTDLLLWWYHDHRJZSFGXTSYCZYNYHTDHWJSLHTZDQDJZXXQHGYLTZPHCSQFCLNJTCLZPFSTPDYNYLGMJLLYCQHYSSHCHYLHQYQTMZYPBYWRFQYKQSYSLZDQJMPXYYSSRHZJNYWTQDFZBWWTWWRXCWHGYHXMKMYYYQMSMZHNGCEPMLQQMTCWCTMMPXJPJJHFXYYZSXZHTYBMSTSYJTTQQQYYLHYNPYQZLCYZHZWSMYLKFJXLWGXYPJYTYSYXYMZCKTTWLKSMZSYLMPWLZWXWQZSSAQSYXYRHSSNTSRAPXCPWCMGDXHXZDZYFJHGZTTSBJHGYZSZYSMYCLLLXBTYXHBBZJKSSDMALXHYCFYGMQYPJYCQXJLLLJGSLZGQLYCJCCZOTYXMTMTTLLWTGPXYMZMKLPSZZZXHKQYSXCTYJZYHXSHYXZKXLZWPSQPYHJWPJPWXQQYLXSDHMRSLZZYZWTTCYXYSZZSHBSCCSTPLWSSCJCHNLCGCHSSPHYLHFHHXJSXYLLNYLSZDHZXYLSXLWZYKCLDYAXZCMDDYSPJTQJZLNWQPSSSWCTSTSZLBLNXSMNYYMJQBQHRZWTYYDCHQLXKPZWBGQYBKFCMZWPZLLYYLSZYDWHXPSBCMLJBSCGBHXLQHYRLJXYSWXWXZSLDFHLSLYNJLZYFLYJYCDRJLFSYZFSLLCQYQFGJYHYXZLYLMSTDJCYHBZLLNWLXXYGYYHSMGDHXXHHLZZJZXCZZZCYQZFNGWPYLCPKPYYPMCLQKDGXZGGWQBDXZZKZFBXXLZXJTPJPTTBYTSZZDWSLCHZHSLTYXHQLHYXXXYYZYSWTXZKHLXZXZPYHGCHKCFSYHUTJRLXFJXPTZTWHPLYXFCRHXSHXKYXXYHZQDXQWULHYHMJTBFLKHTXCWHJFWJCFPQRYQXCYYYQYGRPYWSGSUNGWCHKZDXYFLXXHJJBYZWTSXXNCYJJYMSWZJQRMHXZWFQSYLZJZGBHYNSLBGTTCSYBYXXWXYHXYYXNSQYXMQYWRGYQLXBBZLJSYLPSYTJZYHYZAWLRORJMKSCZJXXXYXCHDYXRYXXJDTSQFXLYLTSFFYXLMTYJMJUYYYXLTZCSXQZQHZXLYYXZHDNBRXXXJCTYHLBRLMBRLLAXKYLLLJLYXXLYCRYLCJTGJCMTLZLLCYZZPZPCYAWHJJFYBDYYZSMPCKZDQYQPBPCJPDCYZMDPBCYYDYCNNPLMTMLRMFMMGWYZBSJGYGSMZQQQZTXMKQWGXLLPJGZBQCDJJJFPKJKCXBLJMSWMDTQJXLDLPPBXCWRCQFBFQJCZAHZGMYKPHYYHZYKNDKZMBPJYXPXYHLFPNYYGXJDBKXNXHJMZJXSTRSTLDXSKZYSYBZXJLXYSLBZYSLHXJPFXPQNBYLLJQKYGZMCYZZYMCCSLCLHZFWFWYXZMWSXTYNXJHPYYMCYSPMHYSMYDYSHQYZCHMJJMZCAAGCFJBBHPLYZYLXXSDJGXDHKXXTXXNBHRMLYJSLTXMRHNLXQJXYZLLYSWQGDLBJHDCGJYQYCMHWFMJYBMBYJYJWYMDPWHXQLDYGPDFXXBCGJSPCKRSSYZJMSLBZZJFLJJJLGXZGYXYXLSZQYXBEXYXHGCXBPLDYHWETTWWCJMBTXCHXYQXLLXFLYXLLJLSSFWDPZSMYJCLMWYTCZPCHQEKCQBWLCQYDPLQPPQZQFJQDJHYMMCXTXDRMJWRHXCJZYLQXDYYNHYYHRSLSRSYWWZJYMTLTLLGTQCJZYABTCKZCJYCCQLJZQXALMZYHYWLWDXZXQDLLQSHGPJFJLJHJABCQZDJGTKHSSTCYJLPSWZLXZXRWGLDLZRLZXTGSLLLLZLYXXWGDZYGBDPHZPBRLWSXQBPFDWOFMWHLYPCBJCCLDMBZPBZZLCYQXLDOMZBLZWPDWYYGDSTTHCSQSCCRSSSYSLFYBFNTYJSZDFNDPDHDZZMBBLSLCMYFFGTJJQWFTMTPJWFNLBZCMMJTGBDZLQLPYFHYYMJYLSDCHDZJWJCCTLJCLDTLJJCPDDSQDSSZYBNDBJLGGJZXSXNLYCYBJXQYCBYLZCFZPPGKCXZDZFZTJJFJSJXZBNZYJQTTYJYHTYCZHYMDJXTTMPXSPLZCDWSLSHXYPZGTFMLCJTYCBPMGDKWYCYZCDSZZYHFLYCTYGWHKJYYLSJCXGYWJCBLLCSNDDBTZBSCLYZCZZSSQDLLMQYYHFSLQLLXFTYHABXGWNYWYYPLLSDLDLLBJCYXJZMLHLJDXYYQYTDLLLBUGBFDFBBQJZZMDPJHGCLGMJJPGAEHHBWCQXAXHHHZCHXYPHJAXHLPHJPGPZJQCQZGJJZZUZDMQYYBZZPHYHYBWHAZYJHYKFGDPFQSDLZMLJXKXGALXZDAGLMDGXMWZQYXXDXXPFDMMSSYMPFMDMMKXKSYZYSHDZKXSYSMMZZZMSYDNZZCZXFPLSTMZDNMXCKJMZTYYMZMZZMSXHHDCZJEMXXKLJSTLWLSQLYJZLLZJSSDPPMHNLZJCZYHMXXHGZCJMDHXTKGRMXFWMCGMWKDTKSXQMMMFZZYDKMSCLCMPCGMHSPXQPZDSSLCXKYXTWLWJYAHZJGZQMCSNXYYMMPMLKJXMHLMLQMXCTKZMJQYSZJSYSZHSYJZJCDAJZYBSDQJZGWZQQXFKDMSDJLFWEHKZQKJPEYPZYSZCDWYJFFMZZYLTTDZZEFMZLBNPPLPLPEPSZALLTYLKCKQZKGENQLWAGYXYDPXLHSXQQWQCQXQCLHYXXMLYCCWLYMQYSKGCHLCJNSZKPYZKCQZQLJPDMDZHLASXLBYDWQLWDNBQCRYDDZTJYBKBWSZDXDTNPJDTCTQDFXQQMGNXECLTTBKPWSLCTYQLPWYZZKLPYGZCQQPLLKCCYLPQMZCZQCLJSLQZDJXLDDHPZQDLJJXZQDXYZQKZLJCYQDYJPPYPQYKJYRMPCBYMCXKLLZLLFQPYLLLMBSGLCYSSLRSYSQTMXYXZQZFDZUYSYZTFFMZZSMZQHZSSCCMLYXWTPZGXZJGZGSJSGKDDHTQGGZLLBJDZLCBCHYXYZHZFYWXYZYMSDBZZYJGTSMTFXQYXQSTDGSLNXDLRYZZLRYYLXQHTXSRTZNGZXBNQQZFMYKMZJBZYMKBPNLYZPBLMCNQYZZZSJZHJCTZKHYZZJRDYZHNPXGLFZTLKGJTCTSSYLLGZRZBBQZZKLPKLCZYSSUYXBJFPNJZZXCDWXZYJXZZDJJKGGRSRJKMSMZJLSJYWQSKYHQJSXPJZZZLSNSHRNYPZTWCHKLPSRZLZXYJQXQKYSJYCZTLQZYBBYBWZPQDWWYZCYTJCJXCKCWDKKZXSGKDZXWWYYJQYYTCYTDLLXWKCZKKLCCLZCQQDZLQLCSFQCHQHSFSMQZZLNBJJZBSJHTSZDYSJQJPDLZCDCWJKJZZLPYCGMZWDJJBSJQZSYZYHHXJPBJYDSSXDZNCGLQMBTSFSBPDZDLZNFGFJGFSMPXJQLMBLGQCYYXBQKDJJQYRFKZTJDHCZKLBSDZCFJTPLLJGXHYXZCSSZZXSTJYGKGCKGYOQXJPLZPBPGTGYJZGHZQZZLBJLSQFZGKQQJZGYCZBZQTLDXRJXBSXXPZXHYZYCLWDXJJHXMFDZPFZHQHQMQGKSLYHTYCGFRZGNQXCLPDLBZCSCZQLLJBLHBZCYPZZPPDYMZZSGYHCKCPZJGSLJLNSCDSLDLXBMSTLDDFJMKDJDHZLZXLSZQPQPGJLLYBDSZGQLBZLSLKYYHZTTNTJYQTZZPSZQZTLLJTYYLLQLLQYZQLBDZLSLYYZYMDFSZSNHLXZNCZQZPBWSKRFBSYZMTHBLGJPMCZZLSTLXSHTCSYZLZBLFEQHLXFLCJLYLJQCBZLZJHHSSTBRMHXZHJZCLXFNBGXGTQJCZTMSFZKJMSSNXLJKBHSJXNTNLZDNTLMSJXGZJYJCZXYJYJWRWWQNZTNFJSZPZSHZJFYRDJSFSZJZBJFZQZZHZLXFYSBZQLZSGYFTZDCSZXZJBQMSZKJRHYJZCKMJKHCHGTXKXQGLXPXFXTRTYLXJXHDTSJXHJZJXZWZLCQSBTXWXGXTXXHXFTSDKFJHZYJFJXRZSDLLLTQSQQZQWZXSYQTWGWBZCGZLLYZBCLMQQTZHZXZXLJFRMYZFLXYSQXXJKXRMQDZDMMYYBSQBHGZMWFWXGMXLZPYYTGZYCCDXYZXYWGSYJYZNBHPZJSQSYXSXRTFYZGRHZTXSZZTHCBFCLSYXZLZQMZLMPLMXZJXSFLBYZMYQHXJSXRXSQZZZSSLYFRCZJRCRXHHZXQYDYHXSJJHZCXZBTYNSYSXJBQLPXZQPYMLXZKYXLXCJLCYSXXZZLXDLLLJJYHZXGYJWKJRWYHCPSGNRZLFZWFZZNSXGXFLZSXZZZBFCSYJDBRJKRDHHGXJLJJTGXJXXSTJTJXLYXQFCSGSWMSBCTLQZZWLZZKXJMLTMJYHSDDBXGZHDLBMYJFRZFSGCLYJBPMLYSMSXLSZJQQHJZFXGFQFQBPXZGYYQXGZTCQWYLTLGWSGWHRLFSFGZJMGMGBGTJFSYZZGZYZAFLSSPMLPFLCWBJZCLJJMZLPJJLYMQDMYYYFBGYGYZMLYZDXQYXRQQQHSYYYQXYLJTYXFSFSLLGNQCYHYCWFHCCCFXPYLYPLLZYXXXXXKQHHXSHJZCFZSCZJXCPZWHHHHHAPYLQALPQAFYHXDYLUKMZQGGGDDESRNNZLTZGCHYPPYSQJJHCLLJTOLNJPZLJLHYMHEYDYDSQYCDDHGZUNDZCLZYZLLZNTNYZGSLHSLPJJBDGWXPCDUTJCKLKCLWKLLCASSTKZZDNQNTTLYYZSSYSSZZRYLJQKCQDHHCRXRZYDGRGCWCGZQFFFPPJFZYNAKRGYWYQPQXXFKJTSZZXSWZDDFBBXTBGTZKZNPZZPZXZPJSZBMQHKCYXYLDKLJNYPKYGHGDZJXXEAHPNZKZTZCMXCXMMJXNKSZQNMNLWBWWXJKYHCPSTMCSQTZJYXTPCTPDTNNPGLLLZSJLSPBLPLQHDTNJNLYYRSZFFJFQWDPHZDWMRZCCLODAXNSSNYZRESTYJWJYJDBCFXNMWTTBYLWSTSZGYBLJPXGLBOCLHPCBJLTMXZLJYLZXCLTPNCLCKXTPZJSWCYXSFYSZDKNTLBYJCYJLLSTGQCBXRYZXBXKLYLHZLQZLNZCXWJZLJZJNCJHXMNZZGJZZXTZJXYCYYCXXJYYXJJXSSSJSTSSTTPPGQTCSXWZDCSYFPTFBFHFBBLZJCLZZDBXGCXLQPXKFZFLSYLTUWBMQJHSZBMDDBCYSCCLDXYCDDQLYJJWMQLLCSGLJJSYFPYYCCYLTJANTJJPWYCMMGQYYSXDXQMZHSZXPFTWWZQSWQRFKJLZJQQYFBRXJHHFWJJZYQAZMYFRHCYYBYQWLPEXCCZSTYRLTTDMQLYKMBBGMYYJPRKZNPBSXYXBHYZDJDNGHPMFSGMWFZMFQMMBCMZZCJJLCNUXYQLMLRYGQZCYXZLWJGCJCGGMCJNFYZZJHYCPRRCMTZQZXHFQGTJXCCJEAQCRJYHPLQLSZDJRBCQHQDYRHYLYXJSYMHZYDWLDFRYHBPYDTSSCNWBXGLPZMLZZTQSSCPJMXXYCSJYTYCGHYCJWYRXXLFEMWJNMKLLSWTXHYYYNCMMCWJDQDJZGLLJWJRKHPZGGFLCCSCZMCBLTBHBQJXQDSPDJZZGKGLFQYWBZYZJLTSTDHQHCTCBCHFLQMPWDSHYYTQWCNZZJTLBYMBPDYYYXSQKXWYYFLXXNCWCXYPMAELYKKJMZZZBRXYYQJFLJPFHHHYTZZXSGQQMHSPGDZQWBWPJHZJDYSCQWZKTXXSQLZYYMYSDZGRXCKKUJLWPYSYSCSYZLRMLQSYLJXBCXTLWDQZPCYCYKPPPNSXFYZJJRCEMHSZMSXLXGLRWGCSTLRSXBZGBZGZTCPLUJLSLYLYMTXMTZPALZXPXJTJWTCYYZLBLXBZLQMYLXPGHDSLSSDMXMBDZZSXWHAMLCZCPJMCNHJYSNSYGCHSKQMZZQDLLKABLWJXSFMOCDXJRRLYQZKJMYBYQLYHETFJZFRFKSRYXFJTWDSXXSYSQJYSLYXWJHSNLXYYXHBHAWHHJZXWMYLJCSSLKYDZTXBZSYFDXGXZJKHSXXYBSSXDPYNZWRPTQZCZENYGCXQFJYKJBZMLJCMQQXUOXSLYXXLYLLJDZBTYMHPFSTTQQWLHOKYBLZZALZXQLHZWRRQHLSTMYPYXJJXMQSJFNBXYXYJXXYQYLTHYLQYFMLKLJTMLLHSZWKZHLJMLHLJKLJSTLQXYLMBHHLNLZXQJHXCFXXLHYHJJGBYZZKBXSCQDJQDSUJZYYHZHHMGSXCSYMXFEBCQWWRBPYYJQTYZCYQYQQZYHMWFFHGZFRJFCDPXNTQYZPDYKHJLFRZXPPXZDBBGZQSTLGDGYLCQMLCHHMFYWLZYXKJLYPQHSYWMQQGQZMLZJNSQXJQSYJYCBEHSXFSZPXZWFLLBCYYJDYTDTHWZSFJMQQYJLMQXXLLDTTKHHYBFPWTYYSQQWNQWLGWDEBZWCMYGCULKJXTMXMYJSXHYBRWFYMWFRXYQMXYSZTZZTFYKMLDHQDXWYYNLCRYJBLPSXCXYWLSPRRJWXHQYPHTYDNXHHMMYWYTZCSQMTSSCCDALWZTCPQPYJLLQZYJSWXMZZMMYLMXCLMXCZMXMZSQTZPPQQBLPGXQZHFLJJHYTJSRXWZXSCCDLXTYJDCQJXSLQYCLZXLZZXMXQRJMHRHZJBHMFLJLMLCLQNLDXZLLLPYPSYJYSXCQQDCMQJZZXHNPNXZMEKMXHYKYQLXSXTXJYYHWDCWDZHQYYBGYBCYSCFGPSJNZDYZZJZXRZRQJJYMCANYRJTLDPPYZBSTJKXXZYPFDWFGZZRPYMTNGXZQBYXNBUFNQKRJQZMJEGRZGYCLKXZDSKKNSXKCLJSPJYYZLQQJYBZSSQLLLKJXTBKTYLCCDDBLSPPFYLGYDTZJYQGGKQTTFZXBDKTYYHYBBFYTYYBCLPDYTGDHRYRNJSPTCSNYJQHKLLLZSLYDXXWBCJQSPXBPJZJCJDZFFXXBRMLAZHCSNDLBJDSZBLPRZTSWSBXBCLLXXLZDJZSJPYLYXXYFTFFFBHJJXGBYXJPMMMPSSJZJMTLYZJXSWXTYLEDQPJMYGQZJGDJLQJWJQLLSJGJGYGMSCLJJXDTYGJQJQJCJZCJGDZZSXQGSJGGCXHQXSNQLZZBXHSGZXCXYLJXYXYYDFQQJHJFXDHCTXJYRXYSQTJXYEFYYSSYYJXNCYZXFXMSYSZXYYSCHSHXZZZGZZZGFJDLTYLNPZGYJYZYYQZPBXQBDZTZCZYXXYHHSQXSHDHGQHJHGYWSZTMZMLHYXGEBTYLZKQWYTJZRCLEKYSTDBCYKQQSAYXCJXWWGSBHJYZYDHCSJKQCXSWXFLTYNYZPZCCZJQTZWJQDZZZQZLJJXLSBHPYXXPSXSHHEZTXFPTLQYZZXHYTXNCFZYYHXGNXMYWXTZSJPTHHGYMXMXQZXTSBCZYJYXXTYYZYPCQLMMSZMJZZLLZXGXZAAJZYXJMZXWDXZSXZDZXLEYJJZQBHZWZZZQTZPSXZTDSXJJJZNYAZPHXYYSRNQDTHZHYYKYJHDZXZLSWCLYBZYECWCYCRYLCXNHZYDZYDYJDFRJJHTRSQTXYXJRJHOJYNXELXSFSFJZGHPZSXZSZDZCQZBYYKLSGSJHCZSHDGQGXYZGXCHXZJWYQWGYHKSSEQZZNDZFKWYSSTCLZSTSYMCDHJXXYWEYXCZAYDMPXMDSXYBSQMJMZJMTZQLPJYQZCGQHXJHHLXXHLHDLDJQCLDWBSXFZZYYSCHTYTYYBHECXHYKGJPXHHYZJFXHWHBDZFYZBCAPNPGNYDMSXHMMMMAMYNBYJTMPXYYMCTHJBZYFCGTYHWPHFTWZZEZSBZEGPFMTSKFTYCMHFLLHGPZJXZJGZJYXZSBBQSCZZLZCCSTPGXMJSFTCCZJZDJXCYBZLFCJSYZFGSZLYBCWZZBYZDZYPSWYJZXZBDSYUXLZZBZFYGCZXBZHZFTPBGZGEJBSTGKDMFHYZZJHZLLZZGJQZLSFDJSSCBZGPDLFZFZSZYZYZSYGCXSNXXCHCZXTZZLJFZGQSQYXZJQDCCZTQCDXZJYQJQCHXZTDLGSCXZSYQJQTZWLQDQZTQCHQQJZYEZZZPBWKDJFCJPZTYPQYQTTYNLMBDKTJZPQZQZZFPZSBNJLGYJDXJDZZKZGQKXDLPZJTCJDQBXDJQJSTCKNXBXZMSLYJCQMTJQWWCJQNJNLLLHJCWQTBZQYDZCZPZZDZYDDCYZZZCCJTTJFZDPRRTZTJDCQTQZDTJNPLZBCLLCTZSXKJZQZPZLBZRBTJDCXFCZDBCCJJLTQQPLDCGZDBBZJCQDCJWYNLLZYZCCDWLLXWZLXRXNTQQCZXKQLSGDFQTDDGLRLAJJTKUYMKQLLTZYTDYYCZGJWYXDXFRSKSTQTENQMRKQZHHQKDLDAZFKYPBGGPZREBZZYKZZSPEGJXGYKQZZZSLYSYYYZWFQZYLZZLZHWCHKYPQGNPGBLPLRRJYXCCSYYHSFZFYBZYYTGZXYLXCZWXXZJZBLFFLGSKHYJZEYJHLPLLLLCZGXDRZELRHGKLZZYHZLYQSZZJZQLJZFLNBHGWLCZCFJYSPYXZLZLXGCCPZBLLCYBBBBUBBCBPCRNNZCZYRBFSRLDCGQYYQXYGMQZWTZYTYJXYFWTEHZZJYWLCCNTZYJJZDEDPZDZTSYQJHDYMBJNYJZLXTSSTPHNDJXXBYXQTZQDDTJTDYYTGWSCSZQFLSHLGLBCZPHDLYZJYCKWTYTYLBNYTSDSYCCTYSZYYEBHEXHQDTWNYGYCLXTSZYSTQMYGZAZCCSZZDSLZCLZRQXYYELJSBYMXSXZTEMBBLLYYLLYTDQYSHYMRQWKFKBFXNXSBYCHXBWJYHTQBPBSBWDZYLKGZSKYHXQZJXHXJXGNLJKZLYYCDXLFYFGHLJGJYBXQLYBXQPQGZTZPLNCYPXDJYQYDYMRBESJYYHKXXSTMXRCZZYWXYQYBMCLLYZHQYZWQXDBXBZWZMSLPDMYSKFMZKLZCYQYCZLQXFZZYDQZPZYGYJYZMZXDZFYFYTTQTZHGSPCZMLCCYTZXJCYTJMKSLPZHYSNZLLYTPZCTZZCKTXDHXXTQCYFKSMQCCYYAZHTJPCYLZLYJBJXTPNYLJYYNRXSYLMMNXJSMYBCSYSYLZYLXJJQYLDZLPQBFZZBLFNDXQKCZFYWHGQMRDSXYCYTXNQQJZYYPFZXDYZFPRXEJDGYQBXRCNFYYQPGHYJDYZXGRHTKYLNWDZNTSMPKLBTHBPYSZBZTJZSZZJTYYXZPHSSZZBZCZPTQFZMYFLYPYBBJQXZMXXDJMTSYSKKBJZXHJCKLPSMKYJZCXTMLJYXRZZQSLXXQPYZXMKYXXXJCLJPRMYYGADYSKQLSNDHYZKQXZYZTCGHZTLMLWZYBWSYCTBHJHJFCWZTXWYTKZLXQSHLYJZJXTMPLPYCGLTBZZTLZJCYJGDTCLKLPLLQPJMZPAPXYZLKKTKDZCZZBNZDYDYQZJYJGMCTXLTGXSZLMLHBGLKFWNWZHDXUHLFMKYSLGXDTWWFRJEJZTZHYDXYKSHWFZCQSHKTMQQHTZHYMJDJSKHXZJZBZZXYMPAGQMSTPXLSKLZYNWRTSQLSZBPSPSGZWYHTLKSSSWHZZLYYTNXJGMJSZSUFWNLSOZTXGXLSAMMLBWLDSZYLAKQCQCTMYCFJBSLXCLZZCLXXKSBZQCLHJPSQPLSXXCKSLNHPSFQQYTXYJZLQLDXZQJZDYYDJNZPTUZDSKJFSLJHYLZSQZLBTXYDGTQFDBYAZXDZHZJNHHQBYKNXJJQCZMLLJZKSPLDYCLBBLXKLELXJLBQYCXJXGCNLCQPLZLZYJTZLJGYZDZPLTQCSXFDMNYCXGBTJDCZNBGBQYQJWGKFHTNPYQZQGBKPBBYZMTJDYTBLSQMPSXTBNPDXKLEMYYCJYNZCTLDYKZZXDDXHQSHDGMZSJYCCTAYRZLPYLTLKXSLZCGGEXCLFXLKJRTLQJAQZNCMBYDKKCXGLCZJZXJHPTDJJMZQYKQSECQZDSHHADMLZFMMZBGNTJNNLGBYJBRBTMLBYJDZXLCJLPLDLPCQDHLXZLYCBLCXZZJADJLNZMMSSSMYBHBSQKBHRSXXJMXSDZNZPXLGBRHWGGFCXGMSKLLTSJYYCQLTSKYWYYHYWXBXQYWPYWYKQLSQPTNTKHQCWDQKTWPXXHCPTHTWUMSSYHBWCRWXHJMKMZNGWTMLKFGHKJYLSYYCXWHYECLQHKQHTTQKHFZLDXQWYZYYDESBPKYRZPJFYYZJCEQDZZDLATZBBFJLLCXDLMJSSXEGYGSJQXCWBXSSZPDYZCXDNYXPPZYDLYJCZPLTXLSXYZYRXCYYYDYLWWNZSAHJSYQYHGYWWAXTJZDAXYSRLTDPSSYYFNEJDXYZHLXLLLZQZSJNYQYQQXYJGHZGZCYJCHZLYCDSHWSHJZYJXCLLNXZJJYYXNFXMWFPYLCYLLABWDDHWDXJMCXZTZPMLQZHSFHZYNZTLLDYWLSLXHYMMYLMBWWKYXYADTXYLLDJPYBPWUXJMWMLLSAFDLLYFLBHHHBQQLTZJCQJLDJTFFKMMMBYTHYGDCQRDDWRQJXNBYSNWZDBYYTBJHPYBYTTJXAAHGQDQTMYSTQXKBTZPKJLZRBEQQSSMJJBDJOTGTBXPGBKTLHQXJJJCTHXQDWJLWRFWQGWSHCKRYSWGFTGYGBXSDWDWRFHWYTJJXXXJYZYSLPYYYPAYXHYDQKXSHXYXGSKQHYWFDDDPPLCJLQQEEWXKSYYKDYPLTJTHKJLTCYYHHJTTPLTZZCDLTHQKZXQYSTEEYWYYZYXXYYSTTJKLLPZMCYHQGXYHSRMBXPLLNQYDQHXSXXWGDQBSHYLLPJJJTHYJKYPPTHYYKTYEZYENMDSHLCRPQFDGFXZPSFTLJXXJBSWYYSKSFLXLPPLBBBLBSFXFYZBSJSSYLPBBFFFFSSCJDSTZSXZRYYSYFFSYZYZBJTBCTSBSDHRTJJBYTCXYJEYLXCBNEBJDSYXYKGSJZBXBYTFZWGENYHHTHZHHXFWGCSTBGXKLSXYWMTMBYXJSTZSCDYQRCYTWXZFHMYMCXLZNSDJTTTXRYCFYJSBSDYERXJLJXBBDEYNJGHXGCKGSCYMBLXJMSZNSKGXFBNBPTHFJAAFXYXFPXMYPQDTZCXZZPXRSYWZDLYBBKTYQPQJPZYPZJZNJPZJLZZFYSBTTSLMPTZRTDXQSJEHBZYLZDHLJSQMLHTXTJECXSLZZSPKTLZKQQYFSYGYWPCPQFHQHYTQXZKRSGTTSQCZLPTXCDYYZXSQZSLXLZMYCPCQBZYXHBSXLZDLTCDXTYLZJYYZPZYZLTXJSJXHLPMYTXCQRBLZSSFJZZTNJYTXMYJHLHPPLCYXQJQQKZZSCPZKSWALQSBLCCZJSXGWWWYGYKTJBBZTDKHXHKGTGPBKQYSLPXPJCKBMLLXDZSTBKLGGQKQLSBKKTFXRMDKBFTPZFRTBBRFERQGXYJPZSSTLBZTPSZQZSJDHLJQLZBPMSMMSXLQQNHKNBLRDDNXXDHDDJCYYGYLXGZLXSYGMQQGKHBPMXYXLYTQWLWGCPBMQXCYZYDRJBHTDJYHQSHTMJSBYPLWHLZFFNYPMHXXHPLTBQPFBJWQDBYGPNZTPFZJGSDDTQSHZEAWZZYLLTYYBWJKXXGHLFKXDJTMSZSQYNZGGSWQSPHTLSSKMCLZXYSZQZXNCJDQGZDLFNYKLJCJLLZLMZZNHYDSSHTHZZLZZBBHQZWWYCRZHLYQQJBEYFXXXWHSRXWQHWPSLMSSKZTTYGYQQWRSLALHMJTQJSMXQBJJZJXZYZKXBYQXBJXSHZTSFJLXMXZXFGHKZSZGGYLCLSARJYHSLLLMZXELGLXYDJYTLFBHBPNLYZFBBHPTGJKWETZHKJJXZXXGLLJLSTGSHJJYQLQZFKCGNNDJSSZFDBCTWWSEQFHQJBSAQTGYPQLBXBMMYWXGSLZHGLZGQYFLZBYFZJFRYSFMBYZHQGFWZSYFYJJPHZBYYZFFWODGRLMFTWLBZGYCQXCDJYGZYYYYTYTYDWEGAZYHXJLZYYHLRMGRXXZCLHNELJJTJTPWJYBJJBXJJTJTEEKHWSLJPLPSFYZPQQBDLQJJTYYQLYZKDKSQJYYQZLDQTGJQYZJSUCMRYQTHTEJMFCTYHYPKMHYZWJDQFHYYXWSHCTXRLJHQXHCCYYYJLTKTTYTMXGTCJTZAYYOCZLYLBSZYWJYTSJYHBYSHFJLYGJXXTMZYYLTXXYPZLXYJZYZYYPNHMYMDYYLBLHLSYYQQLLNJJYMSOYQBZGDLYXYLCQYXTSZEGXHZGLHWBLJHEYXTWQMAKBPQCGYSHHEGQCMWYYWLJYJHYYZLLJJYLHZYHMGSLJLJXCJJYCLYCJPCPZJZJMMYLCQLNQLJQJSXYJMLSZLJQLYCMMHCFMMFPQQMFYLQMCFFQMMMMHMZNFHHJGTTHHKHSLNCHHYQDXTMMQDCYZYXYQMYQYLTDCYYYZAZZCYMZYDLZFFFMMYCQZWZZMABTBYZTDMNZZGGDFTYPCGQYTTSSFFWFDTZQSSYSTWXJHXYTSXXYLBYQHWWKXHZXWZNNZZJZJJQJCCCHYYXBZXZCYZTLLCQXYNJYCYYCYNZZQYYYEWYCZDCJYCCHYJLBTZYYCQWMPWPYMLGKDLDLGKQQBGYCHJXY";
    var oMultiDiff = { "19969": "DZ", "19975": "WM", "19988": "QJ", "20048": "YL", "20056": "SC", "20060": "NM", "20094": "QG", "20127": "QJ", "20167": "QC", "20193": "YG", "20250": "KH", "20256": "ZC", "20282": "SC", "20285": "QJG", "20291": "TD", "20314": "YD", "20340": "NE", "20375": "TD", "20389": "YJ", "20391": "CZ", "20415": "PB", "20446": "YS", "20447": "SQ", "20504": "TC", "20608": "KG", "20854": "QJ", "20857": "ZC", "20911": "PF", "20985": "AW", "21032": "PB", "21048": "XQ", "21049": "SC", "21089": "YS", "21119": "JC", "21242": "SB", "21273": "SC", "21305": "YP", "21306": "QO", "21330": "ZC", "21333": "SDC", "21345": "QK", "21378": "CA", "21397": "SC", "21414": "XS", "21442": "SC", "21477": "JG", "21480": "TD", "21484": "ZS", "21494": "YX", "21505": "YX", "21512": "HG", "21523": "XH", "21537": "PB", "21542": "PF", "21549": "KH", "21571": "E", "21574": "DA", "21588": "TD", "21589": "O", "21618": "ZC", "21621": "KHA", "21632": "ZJ", "21654": "KG", "21679": "LKG", "21683": "KH", "21710": "A", "21719": "YH", "21734": "WOE", "21769": "A", "21780": "WN", "21804": "XH", "21834": "A", "21899": "ZD", "21903": "RN", "21908": "WO", "21939": "ZC", "21956": "SA", "21964": "YA", "21970": "TD", "22003": "A", "22031": "JG", "22040": "XS", "22060": "ZC", "22066": "ZC", "22079": "MH", "22129": "XJ", "22179": "XA", "22237": "NJ", "22244": "TD", "22280": "JQ", "22300": "YH", "22313": "XW", "22331": "YQ", "22343": "YJ", "22351": "PH", "22395": "DC", "22412": "TD", "22484": "PB", "22500": "PB", "22534": "ZD", "22549": "DH", "22561": "PB", "22612": "TD", "22771": "KQ", "22831": "HB", "22841": "JG", "22855": "QJ", "22865": "XQ", "23013": "ML", "23081": "WM", "23487": "SX", "23558": "QJ", "23561": "YW", "23586": "YW", "23614": "YW", "23615": "SN", "23631": "PB", "23646": "ZS", "23663": "ZT", "23673": "YG", "23762": "TD", "23769": "ZS", "23780": "QJ", "23884": "QK", "24055": "XH", "24113": "DC", "24162": "ZC", "24191": "GA", "24273": "QJ", "24324": "NL", "24377": "TD", "24378": "QJ", "24439": "PF", "24554": "ZS", "24683": "TD", "24694": "WE", "24733": "LK", "24925": "TN", "25094": "ZG", "25100": "XQ", "25103": "XH", "25153": "PB", "25170": "PB", "25179": "KG", "25203": "PB", "25240": "ZS", "25282": "FB", "25303": "NA", "25324": "KG", "25341": "ZY", "25373": "WZ", "25375": "XJ", "25384": "A", "25457": "A", "25528": "SD", "25530": "SC", "25552": "TD", "25774": "ZC", "25874": "ZC", "26044": "YW", "26080": "WM", "26292": "PB", "26333": "PB", "26355": "ZY", "26366": "CZ", "26397": "ZC", "26399": "QJ", "26415": "ZS", "26451": "SB", "26526": "ZC", "26552": "JG", "26561": "TD", "26588": "JG", "26597": "CZ", "26629": "ZS", "26638": "YL", "26646": "XQ", "26653": "KG", "26657": "XJ", "26727": "HG", "26894": "ZC", "26937": "ZS", "26946": "ZC", "26999": "KJ", "27099": "KJ", "27449": "YQ", "27481": "XS", "27542": "ZS", "27663": "ZS", "27748": "TS", "27784": "SC", "27788": "ZD", "27795": "TD", "27812": "O", "27850": "PB", "27852": "MB", "27895": "SL", "27898": "PL", "27973": "QJ", "27981": "KH", "27986": "HX", "27994": "XJ", "28044": "YC", "28065": "WG", "28177": "SM", "28267": "QJ", "28291": "KH", "28337": "ZQ", "28463": "TL", "28548": "DC", "28601": "TD", "28689": "PB", "28805": "JG", "28820": "QG", "28846": "PB", "28952": "TD", "28975": "ZC", "29100": "A", "29325": "QJ", "29575": "SL", "29602": "FB", "30010": "TD", "30044": "CX", "30058": "PF", "30091": "YSP", "30111": "YN", "30229": "XJ", "30427": "SC", "30465": "SX", "30631": "YQ", "30655": "QJ", "30684": "QJG", "30707": "SD", "30729": "XH", "30796": "LG", "30917": "PB", "31074": "NM", "31085": "JZ", "31109": "SC", "31181": "ZC", "31192": "MLB", "31293": "JQ", "31400": "YX", "31584": "YJ", "31896": "ZN", "31909": "ZY", "31995": "XJ", "32321": "PF", "32327": "ZY", "32418": "HG", "32420": "XQ", "32421": "HG", "32438": "LG", "32473": "GJ", "32488": "TD", "32521": "QJ", "32527": "PB", "32562": "ZSQ", "32564": "JZ", "32735": "ZD", "32793": "PB", "33071": "PF", "33098": "XL", "33100": "YA", "33152": "PB", "33261": "CX", "33324": "BP", "33333": "TD", "33406": "YA", "33426": "WM", "33432": "PB", "33445": "JG", "33486": "ZN", "33493": "TS", "33507": "QJ", "33540": "QJ", "33544": "ZC", "33564": "XQ", "33617": "YT", "33632": "QJ", "33636": "XH", "33637": "YX", "33694": "WG", "33705": "PF", "33728": "YW", "33882": "SR", "34067": "WM", "34074": "YW", "34121": "QJ", "34255": "ZC", "34259": "XL", "34425": "JH", "34430": "XH", "34485": "KH", "34503": "YS", "34532": "HG", "34552": "XS", "34558": "YE", "34593": "ZL", "34660": "YQ", "34892": "XH", "34928": "SC", "34999": "QJ", "35048": "PB", "35059": "SC", "35098": "ZC", "35203": "TQ", "35265": "JX", "35299": "JX", "35782": "SZ", "35828": "YS", "35830": "E", "35843": "TD", "35895": "YG", "35977": "MH", "36158": "JG", "36228": "QJ", "36426": "XQ", "36466": "DC", "36710": "JC", "36711": "ZYG", "36767": "PB", "36866": "SK", "36951": "YW", "37034": "YX", "37063": "XH", "37218": "ZC", "37325": "ZC", "38063": "PB", "38079": "TD", "38085": "QY", "38107": "DC", "38116": "TD", "38123": "YD", "38224": "HG", "38241": "XTC", "38271": "ZC", "38415": "YE", "38426": "KH", "38461": "YD", "38463": "AE", "38466": "PB", "38477": "XJ", "38518": "YT", "38551": "WK", "38585": "ZC", "38704": "XS", "38739": "LJ", "38761": "GJ", "38808": "SQ", "39048": "JG", "39049": "XJ", "39052": "HG", "39076": "CZ", "39271": "XT", "39534": "TD", "39552": "TD", "39584": "PB", "39647": "SB", "39730": "LG", "39748": "TPB", "40109": "ZQ", "40479": "ND", "40516": "HG", "40536": "HG", "40583": "QJ", "40765": "YQ", "40784": "QJ", "40840": "YK", "40863": "QJG" };
    function checkCh(ch, multi) {
        if (multi === void 0) { multi = false; }
        var uni = ch.charCodeAt(0);
        if (uni > 40869 || uni < 19968)
            return ch;
        return (oMultiDiff[uni] && multi ? oMultiDiff[uni] : (strChineseFirstPY.charAt(uni - 19968)));
    }
    function getChineseHeadLetter(str, toLowerCase, multiSpell) {
        if (toLowerCase === void 0) { toLowerCase = false; }
        if (multiSpell === void 0) { multiSpell = false; }
        var s = '';
        for (var i = 0; i < str.length; i++) {
            s += checkCh(str.charAt(i), multiSpell);
        }
        return toLowerCase ? s.toLowerCase() : s;
    }
    _util.getChineseHeadLetter = getChineseHeadLetter;
    var Evented = (function () {
        function Evented() {
        }
        Evented.prototype.on = function (type, fn) {
            this._typelistens = this._typelistens || {};
            if (typeof type === 'object')
                for (var k in type) {
                    this.on(k, type[k]);
                }
            else {
                var fnlist = this._typelistens[type];
                if (!fnlist)
                    fnlist = this._typelistens[type] = [];
                if (fnlist.indexOf(fn) === -1)
                    fnlist.push(fn);
            }
            return this;
        };
        Evented.prototype.off = function (type, fn) {
            if (!this._typelistens)
                return this;
            if (typeof type === 'object')
                for (var k in type) {
                    this.off(k, type[k]);
                }
            else {
                var fnlist = this._typelistens[type];
                if (fnlist) {
                    if (!fn)
                        fnlist.length = 0;
                    else if (fnlist.indexOf(fn) !== -1) {
                        fnlist.splice(fnlist.indexOf(fn), 1);
                    }
                }
            }
            return this;
        };
        Evented.prototype.once = function (type, fn) {
            if (typeof type === 'object')
                for (var k in type) {
                    this.once(k, type[k]);
                }
            else {
                var self_1 = this;
                var nfn_1 = function () {
                    fn();
                    self_1.off(type, nfn_1);
                };
                self_1.on(type, nfn_1);
            }
            return this;
        };
        Evented.prototype.trigger = function (type, event) {
            if (!this._typelistens)
                return;
            var fnlist = this._typelistens[type];
            if (fnlist)
                fnlist.every(function (fn) { return fn(event) !== false; });
        };
        Evented.prototype.hasListener = function (type) {
            return this._typelistens && this._typelistens[type];
        };
        Evented.prototype.addEventListener = function () {
            return this.on.apply(this, arguments);
        };
        Evented.prototype.removeEventListener = function () {
            return this.off.apply(this, arguments);
        };
        Evented.prototype.fire = function () {
            return this.trigger.apply(this, arguments);
        };
        return Evented;
    }());
    _util.Evented = Evented;
    _util.xls = {
        _xlsx: null,
        _utils: null,
        prepare: function () {
            var _this = this;
            if (this._xlsx)
                return Promise.resolve();
            return loadScript('lib/xlsx/jszip.js', 'JSZipSync').then(function () {
                return loadScript('lib/xlsx/xlsx.js', 'XLSX').then(function () {
                    _this._xlsx = window['XLSX'];
                    _this._utils = _this._xlsx.utils;
                });
            });
        },
        exec: function (fn) {
            var _this = this;
            return this.prepare().then(function () {
                return fn(_this._xlsx, _this._utils);
            });
        },
        read: function (file, opt) {
            return this.exec(function (xlsx, utils) {
                return xlsx.read(file, opt);
            });
        },
        selectFileAndRead: function (callback, accept) {
            var _this = this;
            _util.selectFileAndRead(function (data) {
                _this.read(data, { type: 'binary' }).then(function (ws) {
                    _this.exec(function (xlsx, utils) {
                        var sheet = ws.Sheets.Sheet1;
                        var rows = utils.sheet_to_json(sheet, { defval: '' });
                        callback(rows);
                    });
                });
            }, accept);
        },
        dataToSheet: function (data, headers, opts) {
            if (headers && headers.length && typeof headers[0] !== 'string') {
                data = data.map(function (d) {
                    var ndata = {};
                    headers.forEach(function (h) {
                        var k = h.title;
                        if (k === undefined) {
                            k = h.label;
                        }
                        if (k === undefined) {
                            k = h.name;
                        }
                        if (h.getLabel && typeof h.getLabel === 'function') {
                            ndata[k] = h.getLabel(d);
                        }
                        else if (h.getLabel && typeof h.getLabel === 'string') {
                            var row = d;
                            ndata[k] = eval(h.getLabel);
                        }
                        else {
                            ndata[k] = d[h.name];
                        }
                    });
                    return ndata;
                });
            }
            return this._utils.json_to_sheet(data, opts);
        },
        write: function (data, headers, fileName, opts) {
            var _this = this;
            return this.exec(function (xlsx, utils) {
                var sheet = _this.dataToSheet(data, headers, opts);
                return xlsx.writeFile(utils.sheet_to_workbook(sheet), fileName);
            });
        },
        writeSheets: function (sheets, fileName, opts) {
            var _this = this;
            return this.exec(function (xlsx, utils) {
                var wb = utils.book_new();
                sheets.forEach(function (s, i) {
                    var sheet;
                    if (s.table)
                        sheet = _this.objectTableToSheet(s.table, opts);
                    else
                        sheet = _this.dataToSheet(s.data, s.headers, s.opts);
                    utils.book_append_sheet(wb, sheet, s.title || s.name || 'sheet' + i);
                });
                return xlsx.writeFile(wb, fileName, opts);
            });
        },
        objectTableToSheet: function (table, opts) {
            var el = this._getTableEl(table);
            this._hideTableFixIf(el);
            var sheet = this._utils.table_to_sheet(el[0], Object.assign({ raw: false, display: true }, opts));
            this._resumeTableFixIf(el);
            return sheet;
        },
        _getTableEl: function (table) {
            if (table.removeDetailRow)
                table.removeDetailRow();
            var el;
            if (table.getEl) {
                el = table.getEl().find('table._maintable');
                if (!el.length)
                    el = table.find('table').eq(0);
                if (!el.length)
                    el = table[0];
            }
            else
                el = table;
            return el;
        },
        writeObjectTable: function (table, fileName, opts) {
            var _this = this;
            if (opts === void 0) { opts = {}; }
            return this.exec(function (xlsx, utils) {
                var el = _this._getTableEl(table);
                _this._hideTableFixIf(el);
                var book = utils.table_to_book(el[0], Object.assign({ raw: false, display: true }, opts));
                var objectType = table.objectType;
                if (objectType)
                    _bean.getMeta(objectType).then(function (om) {
                        fileName = fileName || om.label;
                        if (fileName.lastIndexOf('.') === -1)
                            fileName += '.csv';
                        xlsx.writeFile(book, fileName, { _globalFont: false });
                        _this._resumeTableFixIf(el);
                    });
                else {
                    if (fileName.lastIndexOf('.') === -1)
                        fileName += '.csv';
                    xlsx.writeFile(book, fileName, { _globalFont: false });
                    _this._resumeTableFixIf(el);
                }
            });
        },
        _hideTableFixIf: function (table) {
            table.find('th.colhead,td.colhead,._col_no_export').each(function () {
                if (this._bakDisplay === undefined) {
                    this._bakDisplay = this.style.display;
                    this.style.display = 'none';
                }
            });
            if (table.find('.suitablerow ._rowchecker.checked').length) {
                table.find('tr.suitablerow').each(function () {
                    if (!($(this).find('._rowchecker.checked').length)) {
                        if (this._bakDisplay === undefined) {
                            this._bakDisplay = this.style.display;
                            this.style.display = 'none';
                        }
                    }
                });
            }
        },
        _resumeTableFixIf: function (table) {
            table.find('th,td.colhead,._col_no_export').each(function () {
                if (this._bakDisplay !== undefined) {
                    this.style.display = this._bakDisplay;
                    delete this._bakDisplay;
                }
            });
            table.find('tr.suitablerow').each(function () {
                if (this._bakDisplay !== undefined) {
                    this.style.display = this._bakDisplay;
                    delete this._bakDisplay;
                }
            });
        }
    };
    _util.xssUtil = {
        xxsOptions: {
            whiteList: {
                a: ['target', 'href', 'title', 'class'],
                abbr: ['title', 'class'],
                address: [, 'class'],
                area: ['shape', 'coords', 'href', 'alt', , 'class'],
                article: [, 'class'],
                aside: [, 'class'],
                audio: ['autoplay', 'controls', 'crossorigin', 'loop', 'muted', 'preload', 'src', 'class'],
                b: ['class'],
                bdi: ['dir', 'class'],
                bdo: ['dir', 'class'],
                big: ['class'],
                blockquote: ['cite', 'class'],
                br: ['class'],
                caption: ['class'],
                center: ['class'],
                cite: ['class'],
                code: ['class'],
                col: ['align', 'valign', 'span', 'width', 'class'],
                colgroup: ['align', 'valign', 'span', 'width', 'class'],
                dd: ['class'],
                del: ['datetime', 'class'],
                details: ['open', 'class'],
                div: ['class'],
                dl: ['class'],
                dt: ['class'],
                em: ['class'],
                figcaption: ['class'],
                figure: ['class'],
                font: ['color', 'size', 'face', 'class'],
                footer: ['class'],
                h1: ['class'],
                h2: ['class'],
                h3: ['class'],
                h4: ['class'],
                h5: ['class'],
                h6: ['class'],
                header: ['class'],
                hr: ['class'],
                i: ['class'],
                img: ['src', 'alt', 'title', 'width', 'height', 'class'],
                ins: ['datetime'],
                li: ['class'],
                mark: ['class'],
                nav: ['class'],
                ol: ['class'],
                p: ['class'],
                pre: ['class'],
                s: ['class'],
                section: ['class'],
                small: ['class'],
                span: ['class'],
                strike: ['class'],
                strong: ['class'],
                sub: ['class'],
                summary: ['class'],
                sup: ['class'],
                table: ['width', 'border', 'align', 'valign', 'class'],
                tbody: ['align', 'valign', 'class'],
                td: ['width', 'rowspan', 'colspan', 'align', 'valign', 'class'],
                tfoot: ['align', 'valign', 'class'],
                th: ['width', 'rowspan', 'colspan', 'align', 'valign', 'class'],
                thead: ['align', 'valign', 'class'],
                tr: ['rowspan', 'align', 'valign', 'class'],
                tt: ['class'],
                u: ['class'],
                ul: ['class'],
                video: ['autoplay', 'controls', 'crossorigin', 'loop', 'muted', 'playsinline', 'poster', 'preload', 'src', 'height', 'width', 'class']
            }
        },
        init: false,
        prepare: function () {
            var _this = this;
            if (this.init) {
                return Promise.resolve();
            }
            return loadScript('lib/xss/xss.min.js', 'JSZipSync').then(function () {
                _this.init = true;
            });
        },
        filterXSS: function (text) {
            if (!this.init) {
                return text;
            }
            try {
                if (text == null) {
                    return text;
                }
                if (typeof text !== 'string') {
                    return text;
                }
                var filterXSS = window['filterXSS'];
                return filterXSS(text, this.xxsOptions);
            }
            catch (e) {
                return text;
            }
        }
    };
    _util.xssUtil.prepare();
})(_util || (_util = {}));
;
//# sourceMappingURL=_util.js.map