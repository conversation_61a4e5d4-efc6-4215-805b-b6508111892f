/*
 * ============================================================================
 * 功能模块：照片数据访问接口
 * ============================================================================
 * 
 * 模块作用：定义照片数据访问的接口规范，用于统一照片数据的存取操作
 * 
 * 主要功能：
 * - 定义照片数据的基本操作接口
 * - 提供照片信息的查询方法
 * - 支持照片数据的增删改查操作
 * 
 * 执行逻辑：
 * 1. 定义标准的数据访问接口
 * 2. 规范照片数据的操作方法
 * 3. 提供异步操作支持
 * 
 * 注意事项：
 * - 所有数据访问实现类都应实现此接口
 * - 异步方法应遵循.NET异步编程规范
 * ============================================================================
 */

using System.Collections.Generic;
using System.Threading.Tasks;
using HyAssistant.ChinaTowerDownload.Models;

namespace HyAssistant.ChinaTowerDownload.Data
{
    /// <summary>
    /// 照片数据访问接口，定义照片相关数据操作的标准方法
    /// </summary>
    public interface IPhotoRepository
    {
        /// <summary>
        /// 根据给定的站点ID异步获取照片信息列表。
        /// </summary>
        /// <param name="stationId">要查询的站点唯一标识符。</param>
        /// <returns>一个表示异步操作的任务，其结果是包含匹配照片信息的 <see cref="T:System.Collections.Generic.IEnumerable`1"/> 集合。如果未找到任何照片，则返回空集合。</returns>
        Task<IEnumerable<PhotoInfoExcerpt>> GetPhotosByStationIdAsync(string stationId);

        /// <summary>
        /// 根据给定的照片ID异步获取照片的详细信息。
        /// </summary>
        /// <param name="photoId">要查询的照片唯一标识符。</param>
        /// <returns>一个表示异步操作的任务，其结果是 <see cref="T:HyAssistant.ChinaTowerDownload.Models.PhotoInfoExcerpt"/> 对象，包含指定ID的照片信息。如果未找到匹配的照片，则返回 <c>null</c>。</returns>
        Task<PhotoInfoExcerpt> GetPhotoByIdAsync(string photoId);

        /// <summary>
        /// 异步保存单张照片的信息。如果照片已存在，则更新其信息；否则，添加为新照片。
        /// </summary>
        /// <param name="photo">要保存的 <see cref="T:HyAssistant.ChinaTowerDownload.Models.PhotoInfoExcerpt"/> 对象，包含照片的详细信息。</param>
        /// <returns>一个表示异步操作的任务，其结果是布尔值：如果照片保存成功则为 <c>true</c>；否则为 <c>false</c>。</returns>
        Task<bool> SavePhotoAsync(PhotoInfoExcerpt photo);

        /// <summary>
        /// 异步删除指定ID的照片信息。
        /// </summary>
        /// <param name="photoId">要删除的照片的唯一标识符。</param>
        /// <returns>一个表示异步操作的任务，其结果是布尔值：如果照片删除成功则为 <c>true</c>；否则为 <c>false</c>。</returns>
        Task<bool> DeletePhotoAsync(string photoId);

        /// <summary>
        /// 异步批量保存多张照片的信息。此方法将遍历提供的照片列表，并对每张照片执行保存操作（如果照片已存在则更新，否则添加）。
        /// </summary>
        /// <param name="photos">一个包含要保存的 <see cref="T:HyAssistant.ChinaTowerDownload.Models.PhotoInfoExcerpt"/> 对象的 <see cref="T:System.Collections.Generic.IEnumerable`1"/> 集合。</param>
        /// <returns>一个表示异步操作的任务，其结果是布尔值：如果所有照片都成功保存则为 <c>true</c>；如果在保存过程中有任何照片失败，则为 <c>false</c>。</returns>
        Task<bool> SavePhotosAsync(IEnumerable<PhotoInfoExcerpt> photos);
    }
}