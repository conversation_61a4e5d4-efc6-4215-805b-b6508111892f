/*
 * ============================================================================
 * 功能模块：文件提取处理
 * ============================================================================
 * 
 * 模块作用：提供文件提取功能，根据配置的规则从源目录提取文件到目标目录
 * 
 * 主要功能：
 * - 文件提取：根据配置的规则提取文件
 * - 服务管理：启动、停止文件提取服务
 * - 日志记录：记录文件提取过程中的各种事件和错误
 * - 配置管理：通过配置窗体管理文件提取规则
 * 
 * 执行逻辑：
 * 1. 从INI文件加载文件提取配置
 * 2. 在后台线程中启动文件提取服务
 * 3. 根据配置规则提取文件
 * 4. 记录处理日志
 * 
 * 注意事项：
 * - 服务在后台线程中运行，避免阻塞UI线程
 * - 提供线程安全的日志记录机制
 * - 支持优雅停止服务，确保资源正确释放
 * ============================================================================
 */

using ET;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HyAssistant
{
    /// <summary>
    /// 文件提取处理窗体类，提供文件提取功能的用户界面和控制逻辑
    /// </summary>
    /// <remarks>
    /// 该类负责管理文件提取服务的生命周期，包括启动、停止服务，
    /// 以及提供用户界面来配置和监控文件提取过程
    /// </remarks>
    public partial class FileExtract : Form
    {
        /// <summary>
        /// 配置组前缀常量，用于标识INI文件中的文件提取配置节
        /// </summary>
        public const string SECTION_PREFIX = "FileExtract-";

        /// <summary>
        /// 配置文件路径，存储文件提取任务的配置信息
        /// </summary>
        readonly string configFilePath = ETConfig.GetConfigDirectory("fileExtract.ini");

        /// <summary>
        /// 存储所有的处理任务，键为任务ID，值为任务对象
        /// </summary>
        readonly Dictionary<string, Task> processingTasks = new Dictionary<string, Task>();

        /// <summary>
        /// 取消令牌源，用于取消正在执行的任务
        /// </summary>
        CancellationTokenSource cancellationTokenSource = new CancellationTokenSource();

        /// <summary>
        /// 文件提取服务实例，负责实际的文件提取操作
        /// </summary>
        readonly FileExtractService _fileExtractService = new FileExtractService();

        /// <summary>
        /// 服务线程，用于在后台运行文件提取服务
        /// </summary>
        Thread _serviceThread;

        /// <summary>
        /// 线程同步对象，用于确保线程安全操作
        /// </summary>
        readonly object _syncLock = new object();

        /// <summary>
        /// 线程安全日志服务，用于记录操作日志
        /// </summary>
        readonly ThreadSafeLogService _logService;

        /// <summary>
        /// 初始化FileExtract实例，设置窗体和日志服务
        /// </summary>
        public FileExtract()
        {
            InitializeComponent();
            Visible = false;
            _logService = new ThreadSafeLogService(textboxLog);
        }

        /// <summary>
        /// 开始处理文件，启动文件提取服务
        /// </summary>
        /// <returns>表示异步操作的任务</returns>
        /// <remarks>
        /// 该方法首先确保停止任何可能正在运行的任务，
        /// 然后创建并启动新的服务线程来执行文件提取操作
        /// </remarks>
        public async Task StartProcessing()
        {
            // 确保先停止任何可能正在运行的任务
            await EnsureServiceStopped().ConfigureAwait(false);

            // 创建并启动新的服务线程
            _serviceThread = new Thread(RunFileExtractService)
            {
                IsBackground = true,
                Name = "FileExtractServiceThread"
            };
            _serviceThread.Start();
        }

        /// <summary>
        /// 在独立线程中运行文件提取服务
        /// </summary>
        /// <remarks>
        /// 该方法在单独的线程中执行，调用文件提取服务的StartProcessing方法，
        /// 并捕获可能发生的异常，记录到日志中
        /// </remarks>
        void RunFileExtractService()
        {
            try
            {
                // 使用Task.Run和等待是为了避免在线程中阻塞
                Task.Run(async () => await _fileExtractService.StartProcessing(configFilePath, LogMessage).ConfigureAwait(false)).Wait();
            }
            catch (Exception ex)
            {
                // 记录线程异常
                LogMessage("错误", $"文件提取服务线程异常：{ex.Message}", true);
            }
        }

        /// <summary>
        /// 确保服务完全停止
        /// </summary>
        /// <returns>表示异步操作的任务</returns>
        /// <remarks>
        /// 该方法首先调用FileExtractService的CancelAllProcessing方法，
        /// 然后停止服务线程，确保资源被正确释放
        /// </remarks>
        async Task EnsureServiceStopped()
        {
            // 首先确保FileExtractService内部状态被重置
            await Task.Run(() => _fileExtractService.CancelAllProcessing()).ConfigureAwait(false);

            // 然后停止服务线程
            StopServiceThread();
        }

        /// <summary>
        /// 停止服务线程
        /// </summary>
        /// <remarks>
        /// 该方法尝试优雅地停止服务线程，给予一定的时间让线程自行结束，
        /// 如果超时则记录警告但不强制终止线程
        /// </remarks>
        void StopServiceThread()
        {
            if (_serviceThread != null && _serviceThread.IsAlive)
            {
                try
                {
                    // 给线程一些时间来清理资源
                    if (!_serviceThread.Join(3000))
                    {
                        // 如果线程在给定时间内没有结束，记录警告但不强制终止
                        LogMessage("警告", "文件提取服务线程未能正常终止", true);
                    }
                }
                catch (Exception ex)
                {
                    LogMessage("错误", $"停止服务线程时出错：{ex.Message}", true);
                }
                finally
                {
                    _serviceThread = null;
                }
            }
        }

        /// <summary>
        /// 处理消息并记录日志
        /// </summary>
        /// <param name="messageType">消息类型，如"信息"、"警告"、"错误"等</param>
        /// <param name="messageInfo">消息内容</param>
        /// <param name="logToMainForm">是否同时记录到主窗体的日志中</param>
        /// <remarks>
        /// 该方法通过线程安全的日志服务记录消息，确保在多线程环境下安全操作
        /// </remarks>
        void LogMessage(string messageType, string messageInfo, bool logToMainForm)
        {
            _logService.Log(messageType, messageInfo, logToMainForm);
        }

        /// <summary>
        /// 取消所有处理任务并等待完成
        /// </summary>
        /// <returns>表示异步操作的任务</returns>
        /// <remarks>
        /// 该方法调用文件提取服务的CancelAllProcessing方法，
        /// 取消所有正在执行的文件提取任务
        /// </remarks>
        public async Task CancelAllProcessingAsync()
        {
            try
            {
                await _fileExtractService.CancelAllProcessing().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                LogMessage("错误", $"取消处理任务时出错：{ex.Message}", true);
            }
        }

        /// <summary>
        /// 文件提取按钮点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 打开文件提取配置窗体，允许用户配置文件提取规则
        /// </remarks>
        void buttonFileExtract_Click(object sender, EventArgs e)
        {
            // 打开配置窗体
            using (FileExtractConfigForm configForm = new FileExtractConfigForm())
            {
                configForm.ShowDialog(this);
            }
        }

        /// <summary>
        /// 窗体关闭事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 当用户尝试关闭窗体时，取消关闭操作并隐藏窗体，
        /// 使应用程序可以继续在后台运行
        /// </remarks>
        void FileExtract_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                e.Cancel = true;
                ETForm.Hide(this);
            }
        }

        /// <summary>
        /// 窗体加载事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 窗体加载时触发复选框状态变更事件，初始化服务状态
        /// </remarks>
        void FileExtract_Load(object sender, EventArgs e) { checkBoxFileExtract_CheckedChanged(null, null); }

        /// <summary>
        /// 文件提取复选框状态变更事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 根据复选框的选中状态，启动或停止文件提取服务
        /// </remarks>
        async void checkBoxFileExtract_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                // 禁用控件，防止用户重复点击
                checkBoxFileExtract.Enabled = false;

                if (checkBoxFileExtract.Checked)
                {
                    await StartProcessing().ConfigureAwait(false);
                }
                else
                {
                    await EnsureServiceStopped().ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                LogMessage("错误", $"切换文件提取服务状态时出错：{ex.Message}", true);

                // 发生错误时，将复选框状态设置为与服务状态一致（线程安全）
                SafeUpdateCheckBoxState();
            }
            finally
            {
                // 重新启用控件（线程安全）
                SafeEnableCheckBox();
            }
        }

        /// <summary>
        /// 线程安全地更新复选框状态
        /// </summary>
        /// <remarks>
        /// 根据服务线程的状态更新复选框的选中状态，
        /// 确保在多线程环境下安全操作UI控件
        /// </remarks>
        void SafeUpdateCheckBoxState()
        {
            if (checkBoxFileExtract.InvokeRequired)
            {
                checkBoxFileExtract.Invoke(new Action(() =>
                {
                    checkBoxFileExtract.Checked = _serviceThread != null && _serviceThread.IsAlive;
                }));
            }
            else
            {
                checkBoxFileExtract.Checked = _serviceThread != null && _serviceThread.IsAlive;
            }
        }

        /// <summary>
        /// 线程安全地启用复选框
        /// </summary>
        /// <remarks>
        /// 启用文件提取复选框控件，确保在多线程环境下安全操作UI控件
        /// </remarks>
        void SafeEnableCheckBox()
        {
            if (checkBoxFileExtract.InvokeRequired)
            {
                checkBoxFileExtract.Invoke(new Action(() =>
                {
                    checkBoxFileExtract.Enabled = true;
                }));
            }
            else
            {
                checkBoxFileExtract.Enabled = true;
            }
        }

        /// <summary>
        /// 窗体关闭时释放资源
        /// </summary>
        /// <param name="e">窗体关闭事件参数</param>
        /// <remarks>
        /// 确保在窗体关闭时停止服务并释放资源，
        /// 捕获可能的异常但不向上传播，确保资源正确释放
        /// </remarks>
        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            try
            {
                // 确保服务停止
                EnsureServiceStopped().Wait();

                if (_fileExtractService != null)
                {
                    try
                    {
                        _fileExtractService.Dispose();
                    }
                    catch
                    {
                        // 忽略释放资源时的错误
                    }
                }
            }
            catch
            {
                // 忽略关闭时的错误
            }
            finally
            {
                base.OnFormClosed(e);
            }
        }
    }

    /// <summary>
    /// 日志服务接口，定义日志记录的基本操作
    /// </summary>
    public interface ILogService
    {
        /// <summary>
        /// 记录日志消息
        /// </summary>
        /// <param name="messageType">消息类型</param>
        /// <param name="messageInfo">消息内容</param>
        /// <param name="logToMainForm">是否记录到主窗体</param>
        void Log(string messageType, string messageInfo, bool logToMainForm);
    }

    /// <summary>
    /// 线程安全的日志服务实现，确保在多线程环境下安全记录日志
    /// </summary>
    /// <remarks>
    /// 该类实现ILogService接口，提供线程安全的日志记录功能，
    /// 支持将日志记录到指定的TextBox控件和主窗体的日志控件
    /// </remarks>
    public class ThreadSafeLogService : ILogService
    {
        /// <summary>
        /// 日志文本框控件
        /// </summary>
        readonly TextBox _logTextBox;
        
        /// <summary>
        /// 线程同步对象
        /// </summary>
        readonly object _syncLock = new object();

        /// <summary>
        /// 初始化ThreadSafeLogService实例
        /// </summary>
        /// <param name="logTextBox">用于显示日志的文本框控件</param>
        public ThreadSafeLogService(TextBox logTextBox)
        {
            _logTextBox = logTextBox;
        }

        /// <summary>
        /// 记录日志消息
        /// </summary>
        /// <param name="messageType">消息类型，如"信息"、"警告"、"错误"等</param>
        /// <param name="messageInfo">消息内容</param>
        /// <param name="logToMainForm">是否同时记录到主窗体的日志中</param>
        /// <remarks>
        /// 该方法格式化日志消息，并将其记录到指定的文本框控件，
        /// 如果logToMainForm为true，还会记录到主窗体的日志控件
        /// </remarks>
        public void Log(string messageType, string messageInfo, bool logToMainForm)
        {
            string logMessage = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} {messageType}：{messageInfo}";

            SafeLogToTextBox(_logTextBox, logMessage);

            if (logToMainForm && Program.mainForm != null && Program.mainForm.textBoxLog != null)
            {
                SafeLogToTextBox(Program.mainForm.textBoxLog, logMessage);
            }
        }

        /// <summary>
        /// 线程安全地记录日志到TextBox
        /// </summary>
        /// <param name="textBox">目标文本框控件</param>
        /// <param name="message">日志消息</param>
        /// <remarks>
        /// 该方法确保在多线程环境下安全地将日志消息添加到文本框控件，
        /// 使用Invoke或BeginInvoke在UI线程上执行操作，并使用锁确保线程安全
        /// </remarks>
        void SafeLogToTextBox(TextBox textBox, string message)
        {
            if (textBox == null) return;

            try
            {
                if (textBox.InvokeRequired)
                {
                    textBox.BeginInvoke(new Action(() =>
                    {
                        lock (_syncLock)
                        {
                            textBox.WriteLog(message);
                        }
                    }));
                }
                else
                {
                    lock (_syncLock)
                    {
                        textBox.WriteLog(message);
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录异常，但不向上抛出，以避免日志系统崩溃导致主程序崩溃
                System.Diagnostics.Debug.WriteLine($"记录日志时出错: {ex.Message}");
            }
        }
    }
}
