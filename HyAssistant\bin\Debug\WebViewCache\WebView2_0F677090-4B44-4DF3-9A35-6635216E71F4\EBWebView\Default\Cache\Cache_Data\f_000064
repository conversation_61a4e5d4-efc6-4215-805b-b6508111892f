<?xml version="1.0" encoding="utf-8"?>
<application localeModule="g5" objMetaUrl="config/g5.meta.xml,config/g5.meta.s.xml,config/g5.meta.o.xml,config/g5.meta.e.xml">
    <preload>
        <![CDATA[
                // 从后端获取路径
                let arcgisLayer={}
                 _bean.callService('mapServiceImpl', 'getArcgisLayerUrl', []).then(res => {
                    arcgisLayer=res
                    _context.arcgisLayerMapper=arcgisLayer
                    _context.events.trigger('preload_complete')
                 }).catch(err=>{
                    _context.events.trigger('preload_complete')
                 })
            ]]>
    </preload>
    <map showOverview="true" initFullExtent="true" title="{{g5.map.title}}" view="114.4493,22.728412" zoom="9" minZoom="0" maxZoom="23" selectable="true" entityTypeField="METACATEGORY" entityIdField="entityid" defaultZoomLevel="-5"
         showMoveResourceControl="true" updateCrs="false"
    >
        <!--右键菜单-->
        <context-menu></context-menu>
        <!--        <crs code="EPSG:4326" def="+proj=longlat +ellps=WGS84 +datum=WGS84 +no_defs" origin="-400,400"-->
        <!--             resolutions="-->
        <!--                0.010231682325070206,-->
        <!--                0.00475892201166056,-->
        <!--                0.00237946100583028,-->
        <!--                0.00118973050291514,-->
        <!--                0.00059486525145757,-->
        <!--                0.000297432625728785,-->
        <!--                0.00015228550437313792,-->
        <!--                0.00007614275218656896,-->
        <!--                0.00003807137609328448,-->
        <!--                0.00001903568804664224,-->
        <!--                0.00000951784402332112,-->
        <!--                0.00000475892201166056,-->
        <!--                0.00000237946100583028,-->
        <!--                0.00000118973050291514,-->
        <!--                0.00000059486525145757,-->
        <!--                0.000000297432625728785,-->
        <!--                0.00000011897305029151401-->
        <!--                "/>-->

        <!--广州底图-->
        <layer title="{{g5.map.cache}}" type="tile" shardingId="200" center="" baseLayer="true" url="" minZoom="0" maxZoom="20"/>
        <!--清远底图-->
        <layer title="{{g5.map.cache}}" type="tile" shardingId="763" center="" baseLayer="true" url="" minZoom="0" maxZoom="20"/>
        <!--汕头底图-->
        <layer title="{{g5.map.cache}}" type="tile" shardingId="754" center="" baseLayer="true" url="" minZoom="0" maxZoom="20"/>
        <!--汕尾底图-->
        <layer title="{{g5.map.cache}}" type="tile" shardingId="660" center="" baseLayer="true" url="" minZoom="0" maxZoom="20"/>
        <!--江门底图-->
        <layer title="{{g5.map.cache}}" type="tile" shardingId="750" center="" baseLayer="true" url="" minZoom="0" maxZoom="20"/>
        <!--韶关底图-->
        <layer title="{{g5.map.cache}}" type="tile" shardingId="751" center="" baseLayer="true" url="" minZoom="0" maxZoom="20"/>

        <!--东莞底图-->
        <layer title="{{g5.map.cache}}" type="tile" shardingId="769" center="" baseLayer="true" url="" minZoom="0" maxZoom="20"/>
        <!--中山底图-->
        <layer title="{{g5.map.cache}}" type="tile" shardingId="760" center="" baseLayer="true" url="" minZoom="0" maxZoom="20"/>
        <!--云浮底图-->
        <layer title="{{g5.map.cache}}" type="tile" shardingId="766" center="" baseLayer="true" url="" minZoom="0" maxZoom="20"/>
        <!--珠海底图-->
        <layer title="{{g5.map.cache}}" type="tile" shardingId="756" center="" baseLayer="true" url="" minZoom="0" maxZoom="20"/>
        <!--湛江底图-->
        <layer title="{{g5.map.cache}}" type="tile" shardingId="759" center="" baseLayer="true" url="" minZoom="0" maxZoom="20"/>
        <!--肇庆底图-->
        <layer title="{{g5.map.cache}}" type="tile" shardingId="758" center="" baseLayer="true" url="" minZoom="0" maxZoom="20"/>
        <!--茂名底图-->
        <layer title="{{g5.map.cache}}" type="tile" shardingId="668" center="" baseLayer="true" url="" minZoom="0" maxZoom="20"/>

        <!--佛山底图-->
        <layer title="{{g5.map.cache}}" type="tile" shardingId="757" center="" baseLayer="true" url="" minZoom="0" maxZoom="20"/>
        <!--潮州底图-->
        <layer title="{{g5.map.cache}}" type="tile" shardingId="768" center="" baseLayer="true" url="" minZoom="0" maxZoom="20"/>
        <!--河源底图-->
        <layer title="{{g5.map.cache}}" type="tile" shardingId="762" center="" baseLayer="true" url="" minZoom="0" maxZoom="20"/>
        <!--惠州底图-->
        <layer title="{{g5.map.cache}}" type="tile" shardingId="752" center="" baseLayer="true" url="" minZoom="0" maxZoom="20"/>
        <!--揭阳底图-->
        <layer title="{{g5.map.cache}}" type="tile" shardingId="663" center="" baseLayer="true" url="" minZoom="0" maxZoom="20"/>
        <!--梅州底图-->
        <layer title="{{g5.map.cache}}" type="tile" shardingId="753" center="" baseLayer="true" url="" minZoom="0" maxZoom="20"/>
        <!--阳江底图-->
        <layer title="{{g5.map.cache}}" type="tile" shardingId="662" center="" baseLayer="true" url="" minZoom="0" maxZoom="20"/>

        <!--深圳底图-->
        <layer title="{{g5.map.cache}}" type="tile" shardingId="755" center="" baseLayer="true" url="" minZoom="0" maxZoom="20"/>

        <!--深圳-矢量地图-->
        <!--        <layer title="矢量地图" type="tile" visible="false" minZoom="10" url=""/>-->
        <!--清远-矢量地图-->
        <!--        <layer title="矢量地图" type="tile" shardingId="763" visible="false" minZoom="10" url=""/>-->
        <!--        <layer title="矢量地图" type="tile" visible="false" minZoom="10" url=""/>-->
        <!--矢量地图-->
        <layer title="矢量地图" type="wms" visible="false" minZoom="21" maxZoom="23" url="" layers="national_map_info:gd_bui_s_cgcs2000,national_map_info:roa_cl_44_cgcs2000,national_map_info:poi_p_44_cgcs2000,national_map_info:riv_l_44_cgcs2000,national_map_info:gre_s_44_cgcs2000"/>
        <!--卫星地图-->
        <layer title="卫星地图" type="tile" visible="false"  url="" minZoom="0" maxZoom="20"/>

        <!--深圳分公司地图服务10.2-->
        <!--        <layer title="深圳分公司地图服务10.2" type="arcgis.dynamic" visible="false" url="http://************:6080/arcgis/rest/services/SZtelcommap/MapServer"/>-->

        <!--
         <layer title="osm" type="tile" baseLayer="true" url="http://{s}.tile.osm.org/{z}/{x}/{y}.png"/> -->
        <layer id="resource" incrementLoad="false" title="{{g5.map.resources}}" type="object" minZoom="12" labelField="NAME" labelSize="11" labelColor="0x000000" labelMinLevel="20"
               iconExpression="assets/icon/map/${METACATEGORY}_${zoom}.png">
            <!--
            <layer id="resource" title="{{g5.map.resources}}" type="object" minZoom="9" labelField="NAME" labelSize="11" labelColor="0x000000" labelMinLevel="20"
              iconExpression="assets/icon/map/${METACATEGORY}_${zoom}.png">
            -->
            <!--空间资源-->
            <mapType category="{{g5.map.spaceResource}}" title="{{g5.map.site}}" objectType="map.site" icon="assets/icon/map/SITE.png" minZoom="14" labelBlockSize="220,160" labelPosition="top" />
            <mapType category="{{g5.map.spaceResource}}" title="{{g5.map.room}}" objectType="map.room" icon="assets/icon/map/ROOM_K01.png" minZoom="16" labelBlockSize="220,160" labelPosition="top" />
            <mapType category="{{g5.map.spaceResource}}" title="{{g5.map.outdooraddress}}" objectType="map.outdooraddress" icon="assets/icon/map/OUTDOORADDRESS.png" minZoom="16" labelBlockSize="220,160" labelPosition="top" />
            <mapType category="{{g5.map.spaceResource}}" title="{{g5.map.accesspoint}}" objectType="map.accesspoint" icon="assets/icon/map/OUTDOORADDRESS.png" minZoom="16" labelBlockSize="220,160" labelPosition="top" />
            <mapType category="{{g5.map.spaceResource}}" title="{{g5.map.childaccesspoint}}" objectType="map.childaccesspoint" icon="assets/icon/map/OUTDOORADDRESS.png" minZoom="16" labelBlockSize="220,160" labelPosition="top" />
            <mapType category="{{g5.map.spaceResource}}" shardingId="755" title="机房服务区图层" objectType="map.room.area" fill="0x0000FF" alpha="0.3" lineColor="0xFF0000" minZoom="13" visible="false">
                <labelStyle fontSize="9" fontWeight="bold" fill="#f70202"/>
            </mapType>
            <!--电缆网 -->
            <mapType category="{{g5.map.ecables}}" title="{{g5.map.ccp}}" objectType="map.ccp" icon="assets/icon/map/CCP.png" minZoom="17" labelBlockSize="false" labelPosition="top" visible="false">
                <labelStyle fontWeight="normal" stroke="white" strokeThickness="4" fontSize="14" fill="0x0000ff"/>
            </mapType>>
            <mapType category="{{g5.map.ecables}}" title="{{g5.map.dp}}" objectType="map.dp" icon="assets/icon/map/DP1.png" minZoom="18" labelBlockSize="false" labelMinLevel="13" labelPosition="0.5,-1" visible="false">
                <labelStyle fontWeight="normal" stroke="white" strokeThickness="2" fontSize="12" fill="0x0000ff"/>
            </mapType>
            <mapType category="{{g5.map.ecables}}" title="{{g5.map.dpgroup}}" objectType="map.dpgroup" icon="assets/icon/map/DPGROUP_1.png" minZoom="18" labelBlockSize="false" labelPosition="top" visible="false">
                <labelStyle fontWeight="normal" stroke="white" strokeThickness="2" fontSize="12" fill="0x0000ff"/>
            </mapType>>
            <mapType category="{{g5.map.ecables}}" title="{{g5.map.ecablesection}}" objectType="map.ecablesection" minZoom="17" lineWidth="1" color="0x4169E1" labelBlockSize="220,160" labelPosition="top" visible="false" >
                <labelStyle fontWeight="normal" stroke="white" strokeThickness="2" fontSize="12" fill="0x0000ff"/>
            </mapType>

            <!--光缆网 -->
            <mapType category="{{g5.map.ocables}}" title="{{g5.map.gj}}" objectType="map.gj" icon="assets/icon/map/GJ.png" minZoom="17" labelBlockSize="false" labelPosition="top">
                <labelStyle fontWeight="normal" stroke="white" strokeThickness="4" fontSize="14" fill="0xeb59f7"/>
            </mapType>
            <mapType category="{{g5.map.ocables}}" title="{{g5.map.gf}}" objectType="map.gf" icon="assets/icon/map/GF.png" minZoom="18" labelBlockSize="60,30" labelMinLevel="13" labelPosition="0.5,-1">
                <labelStyle fontWeight="normal" stroke="white" strokeThickness="2" fontSize="12" fill="0xeb59f7"/>
            </mapType>
            <mapType category="{{g5.map.ocables}}" title="XBOX光交箱" objectType="map.xbox" icon="assets/icon/map/GJ.png" iconExpression="assets/icon/map/GJ_${zoom}.png" minZoom="17" labelBlockSize="false" labelPosition="top">
                <labelStyle fontWeight="normal" stroke="white" strokeThickness="4" fontSize="14" fill="0xeb59f7" />
            </mapType>
            <mapType category="{{g5.map.ocables}}" title="HUBBOX分光箱" objectType="map.hubbox" icon="assets/icon/map/GF.png" iconExpression="assets/icon/map/GF_${zoom}.png" minZoom="18" labelBlockSize="60,30" labelMinLevel="13" labelPosition="0.5,-1">
                <labelStyle fontWeight="normal" stroke="white" strokeThickness="2" fontSize="12" fill="0xeb59f7" />
            </mapType>
            <mapType category="{{g5.map.ocables}}" title="{{g5.map.gb}}" objectType="map.gb" icon="assets/icon/map/GB.png" minZoom="18" labelBlockSize="false" labelMinLevel="13" labelPosition="top">
                <labelStyle fontWeight="normal" stroke="white" strokeThickness="2" fontSize="12" fill="0xeb59f7"/>
            </mapType>
            <mapType category="{{g5.map.ocables}}" title="{{g5.map.obd}}" objectType="map.obd" editable="false" editMessage="OBD安装地址或坐标跟随所属箱体，请修改其所属箱体!" icon="assets/icon/map/OBD_3.png" minZoom="16" visible="false" labelBlockSize="false" labelPosition="top">
                <labelStyle fontWeight="normal" stroke="white" strokeThickness="2" fontSize="10" fill="0xeb59f7"/>
            </mapType>
            <!--            <mapType category="{{g5.map.ocables}}" title="{{g5.map.onu}}" objectType="map.onu" icon="assets/icon/map/ONU.png" minZoom="11" visible="false" labelBlockSize="false" labelPosition="top">-->
            <!--                <labelStyle fontWeight="normal" stroke="white" strokeThickness="2" fontSize="10" fill="0xeb59f7"/>-->
            <!--            </mapType>-->
            <mapType category="{{g5.map.ocables}}" title="{{g5.map.zhx}}" objectType="map.zhx" icon="assets/icon/map/ZHX.png" minZoom="17" labelBlockSize="false" labelPosition="top">
                <labelStyle fontWeight="normal" stroke="white" strokeThickness="2" fontSize="12" fill="0xeb59f7"/>
            </mapType>
            <mapType category="{{g5.map.ocables}}" title="{{g5.map.ocablesection}}" objectType="map.ocablesection" color="0x228B22" lineWidth="1" minZoom="16" visible="false" labelBlockSize="220,160" labelPosition="top">
                <labelStyle fontWeight="normal" stroke="white" strokeThickness="2" fontSize="12" fill="0xeb59f7"/>
            </mapType>

            <!--支撑网 -->
            <mapType category="{{g5.map.supports}}" title="{{g5.map.pole}}" objectType="map.pole" icon="assets/icon/map/POLE.png" minZoom="17" labelBlockSize="false" labelPosition="top" />
            <mapType category="{{g5.map.supports}}" title="{{g5.map.well}}" objectType="map.well" icon="assets/icon/map/WELL_1.png" minZoom="17"
                     iconExpression="assets/icon/map/${METACATEGORY}_${zoom}_${DRAWFLAG}.png" labelBlockSize="false" labelMinLevel="12" labelPosition="0.5,1.5">
                <labelStyle fontWeight="normal" stroke="white" strokeThickness="4" fontSize="12" fill="0x7d161b"/>
            </mapType>>
            <mapType category="{{g5.map.supports}}" title="{{g5.map.underwell}}" objectType="map.underwell" icon="assets/icon/map/UNDERWELL_1.png" minZoom="17" labelBlockSize="false" labelPosition="top" />
            <mapType category="{{g5.map.supports}}" title="{{g5.map.supportpoint}}" objectType="map.supportpoint" icon="assets/icon/map/SUPPORTPT.png" minZoom="17" labelBlockSize="false" labelPosition="top" />
            <mapType category="{{g5.map.supports}}" title="{{g5.map.drawingpoint}}" objectType="map.drawingpoint" icon="assets/icon/obj/map_drawingpoint.png" minZoom="17" labelBlockSize="220,160" labelPosition="top" />
            <mapType category="{{g5.map.supports}}" title="标石" objectType="map.markstone" icon="assets/icon/map/MARKSTONE.png" minZoom="17" labelBlockSize="220,160" labelPosition="top" visible="false" />
            <mapType category="{{g5.map.supports}}" title="{{g5.map.pipesection}}" objectType="map.pipesection" lineWidth="1" color="0x990000" minZoom="17" labelBlockSize="220,160" labelPosition="center" editable="true">
                <render>
                    <![CDATA[
                    if (obj.DRAWFLAG == 1) draw.symbol = {lineColor: 0x00ff00};

                ]]>
                </render>
                <labelStyle fontWeight="normal" stroke="white" strokeThickness="3" fontSize="12" fill="0x7d161b"/>
            </mapType>
            <mapType category="{{g5.map.supports}}" title="{{g5.map.hanglingsection}}" objectType="map.hanglingsection" lineWidth="1" color="0x5B5B5B" minZoom="17" labelBlockSize="220,160"  labelPosition="top" editable="true"/>
            <mapType category="{{g5.map.supports}}" title="{{g5.map.commonline}}" objectType="map.commonline" lineWidth="1" color="0x5B5B5B" minZoom="17" labelBlockSize="220,160" labelPosition="top" editable="true"/>
            <mapType category="{{g5.map.supports}}" title="直埋段" objectType="map.lineburiedseg" lineWidth="1" color="0x99ffff" minZoom="17" labelBlockSize="220,160" labelPosition="top" editable="true" visible="false" />
            <mapType category="{{g5.map.supports}}" title="{{g5.map.safetysign}}" objectType="map.safetysign" iconExpression="" icon="assets/icon/obj/dp_default.png" minZoom="17" labelBlockSize="false" labelPosition="top" />
            <mapType category="{{g5.map.ocables}}" title="{{g5.map.gt}}" objectType="map.pointgt" icon="assets/icon/map/GT_3.png" minZoom="18" labelBlockSize="220,160" labelMinLevel="12" labelPosition="top">
                <labelStyle fontWeight="normal" stroke="white" strokeThickness="4" fontSize="12" fill="0xeb59f7"/>
            </mapType>

            <!--标准地址图形 -->
            <mapType category="{{g5.map.standardaddress}}" title="{{g5.map.addressPolygon}}" objectType="map.polygonaddress" fill="0xFFD700" alpha="0.4" lineColor="0x00008B" minZoom="19" visible="false" />
            <mapType category="{{g5.map.standardaddress}}" title="{{g5.map.addressLine}}" objectType="map.lineaddress" fill="0xFFD700" alpha="0.5" lineColor="0x00008B" minZoom="18" visible="false"/>
            <mapType category="{{g5.map.standardaddress}}" title="{{g5.map.addressPoint}}" objectType="map.pointaddress" icon="assets/icon/map/POINTADDRESS.png" iconExpression="" minZoom="18" visible="false" />

            <!--区县营服网格-->
            <mapType category="区县营服网格" whereJson='{"DRAWFLAG": 2}' title="区县" objectType="map.polygonregion" fill="0xDC143C" alpha="0.01" lineColor="0x800080" lineWidth="3" minZoom="12" visible="false"><labelStyle fontSize="18" fontWeight="bold" fill = "0x000000"/></mapType>
            <mapType category="区县营服网格" whereJson='{"DRAWFLAG": 0}' title="营服中心" objectType="map.polygonregion" fill="0x87cefa" alpha="0.01" lineColor="0x00BFFF" lineWidth="3" minZoom="12" visible="false"><labelStyle fontSize="18" fontWeight="bold" fill = "0x000000"/></mapType>
            <mapType category="区县营服网格" whereJson='{"DRAWFLAG": 1}' title="网格" objectType="map.polygonregion" fill="0x008000" alpha="0.01" lineColor="0xFFA500" lineWidth="3" minZoom="12" visible="false"><labelStyle fontSize="18" fontWeight="bold" fill = "0x000000"/></mapType>

            <!--建筑物群图形-->
            <mapType category="建筑物群" whereJson='{"DRAWFLAG": 1}' title="建筑群" objectType="map.polygonaddress" fill="0xDC143C" alpha="0.01" lineColor="0xDC143C" minZoom="12" visible="false"><labelStyle fontSize="13" fontWeight="bold" fill = "#f70202"/></mapType>
            <mapType category="建筑物群" whereJson='{"DRAWFLAG": 2}' title="建筑物" objectType="map.polygonaddress" fill="0x00FF00" alpha="0.01" lineColor="0x69CC44" minZoom="16" visible="false"><labelStyle fontSize="13" fontWeight="bold"/></mapType>
            <mapType category="建筑物群" whereJson='{"DRAWFLAG": 3}' title="临街商铺" objectType="map.polygonaddress" fill="0x1D8BDE" alpha="0.01" lineColor="0x1D8BDE" minZoom="16" visible="false"><labelStyle fontSize="13" fontWeight="bold"/></mapType>
            <mapType category="建筑物群" whereJson='{"DRAWFLAG": 4}' title="独栋" objectType="map.polygonaddress" fill="0xA47DD3" alpha="0.01" lineColor="0x9400D3" minZoom="16" visible="false"><labelStyle fontSize="13" fontWeight="bold" fill = "#f70202"/></mapType>

            <!--图幅层-->
            <mapType title="图幅层" objectType="map.mappable.unit"
                     fill="0x1D8BDE" alpha="0.01" lineColor="0x1D8BDE" minZoom="14" maxZoom="18" visible="false">
                <labelStyle fontSize="13" fontWeight="bold" fill="#f70202"/>
            </mapType>
        </layer>

        <!--        <layer title="矢量地图服务" type="arcgis.dynamic" visible="false" url="http://************:6080/arcgis/rest/services/sztelcom_hxx/MapServer"/>-->
        <!--        <layer title="影像地图服务" type="arcgis.dynamic" visible="false" url="http://************:6080/arcgis/rest/services/sztelcom_image/MapServer"/>-->
        <!--图幅层-->
        <!--        <layer title="图幅层" type="leaflet.wms" visible="false" url="" layers="national_map_info:gd_bui_s_cgcs2000" minZoom="15" maxZoom="23"/>-->

        <!--        <layer title="{{g5.map.dynamic}}" type="arcgis.dynamic" minZoom="9" url="http://**************:8813/ugis/map?app=smartpipe&amp;token=c21hcnRwaXBlLm1hcC4xNDg5NzU2MzY0MzUy&amp;shardingid=yunfu"/>-->
        <!--        <layer title="Bing" type="bing" key="AuhiCJHlGzhg93IqUH_oCpl_-ZUrIE6SPftlyGYUvr9Amx5nzA-WqGcPquyFZl4L" visible="false" />-->

        <!--<layer id="xgisproject" type="xgis" title="自定义图形" visible="false" projectId="356" script="apps/xgis/layerCreator.js"/>-->
        <layer id="xgisproject" type="xgis" title="地图工具自定义点线面" visible="false" projectId="10001" script="apps/xgis/layerCreator.js"
               useRootMapBaseLayer="true" useRootMapLayerDefs='[{"layerName": "resource", "visible":true,"mapTypes": ["map.polygonaddress"]}]' />
        <options>
            <![CDATA[
             // 地图初始化完成
             _context.mapReady((map,L)=>{
                // 切换本地网后自动缩放
                try {
                   const userRouteSwitchKey = 'user-route-switch'
                   // 用户是否切换，如果进行切换进行地图缩放到合适范围
                   const userRouteWwitch = localStorage.getItem(userRouteSwitchKey)
                   if (userRouteWwitch && userRouteWwitch == 'true') {
                     localStorage.removeItem(userRouteSwitchKey)
                     setTimeout(()=>{
                       _context.map.setCenter(_context.map.defaultView,_context.map.defaultZoom||0)
                     },500)
                   }
                  } catch (e) {
                 }
                 // 执行action
                 var actions = _context.getUrlParam('actionJson');
                 if(actions){
                      console.log(actions);
                      actions = JSON.parse(actions);
                      if (!Array.isArray(actions)) actions = [actions];
                        actions.forEach(function(a){
                          _context.doAction(a);
                      });
                  }
                    // 增加右键菜单 边建边敷
                   _context.map.getContextMenu().addItem({text: '{{g5.menu.layinput}}',callback: (e)=>{
                        _context.doAction({mainConfig:'modules/layinput/layinput.xml'});
                   }})

                    // 增加右键菜单 获取当前位置坐标
                   _context.map.getContextMenu().addItem({text: '{{g5.map.getCurrentPositionCoordinates}}',callback: (e)=>{
                        _sui.alert(e.latlng.lng+','+e.latlng.lat,_locale.getString("g5.map.currentPositionCoordinates"))
                   }})
                   // 增加右键菜单 图幅定位
                   _context.map.getContextMenu().addItem({text: '图幅定位',callback: (e)=>{
                                            _sui.prompt({
                                                title: '图幅号',
                                                inputType: 'text'
                                            },doLocation);
                   }})
                   addAddressGraphicMove(map)
                   function doLocation(e){
                    let location = [];
                        _bean.find("map.mappable.unit",{NAME: e}).then(function(res){
                            if (!res) {
                                return _sui.alert('没有坐标信息可定位')
                            }
                            location.push(res.shape)
                            // 清空画图
                            if (_context.rootContext) {
                                // 清空画图
                                _context.rootContext.map.clear()
                                // 地图显示
                                _context.rootContext.map.locateByWkts(location)
                            } else {
                                // 清空画图
                                _context.map.clear()
                                // 地图显示
                                _context.map.locateByWkts(location)
                            }
                        })
                   }

                   // 63035 【广东电信】地图右键增加批量录入OBD功能
                   function doAddObd(e) {
                    var shape = 'POINT(' + e.latlng.lng + ' ' + e.latlng.lat + ')';
                    _bean.getTopBean().showAdd('OBD', {shape: shape});
                   }
                   var isCreatingObd = false;
                   var addObdItem = map.getContextMenu().addItem({text: '录入OBD',callback: (e)=>{
                      var isCreating = isCreatingObd;
                      isCreatingObd = !isCreating;
                      if (!isCreating) {
                        addObdItem.innerHTML = '取消录入OBD';
                        map.setCursor('crosshair');

                        map.activeDraw(false);
                        map.activeEdit(false);
                        map.clickSelect = false;
                        map.on('singleclick', doAddObd);
                      } else {
                        addObdItem.innerHTML = '录入OBD';
                        map.setCursor('auto');
                        map.clickSelect = true;
                        map.off('singleclick', doAddObd);
                      }
                   }});
             })



            //layerNodes对应上面的layer节点定义列表,1对应的是资源图层
            //mapType[0]对应局站,[1]机房,[2]安装点,[3]光交接箱,[4]光分纤盒,[5]光终端盒,[6]分光器,[7]综合箱,[8]光缆段,[9]交接箱,[10]分线盒,[11]DP群,[12]电缆段,[13]电杆,[14]人手井,[15]地下进线室
            function shortLabel(g) {
              var label = g.properties.NAME;
              return label ? label.substring(label.lastIndexOf('/')+1) : '';
            }
            function shortLabel2(g) {
              var label = g.properties.NAME;
              return label ? label.substring(label.lastIndexOf('(')) : '';
            }
            const newLayerNodes = []
                let user = _context.getUser(true)
                layerNodes.forEach(layerNode=>{
                    // 根据本地网选择底图
                    if(layerNode&&(!layerNode.shardingId || !user || layerNode.shardingId ==user.SHARDING_ID.toString())){
                        newLayerNodes.push(layerNode)
                    }
                    if(layerNode&&layerNode.mapType){
                        const newMapTypes = []
                        //mapType[4]光分纤盒
        		        layerNode.mapType[4].labelField = shortLabel2
        		        //mapType[5]光终端盒
        		        layerNode.mapType[5].labelField = shortLabel2
        		        //mapType[10]分线盒
        		        layerNode.mapType[10].labelField = shortLabel
        		        //mapType[11]DP群
        		        layerNode.mapType[11].labelField = shortLabel
        		        //mapType[13]电杆
        		        layerNode.mapType[13].labelField = shortLabel
        		        layerNode.mapType.forEach(mapType=>{
        		            // 根据本地网选择业务图层
                            if(mapType&&(!mapType.shardingId || !user || mapType.shardingId == user.SHARDING_ID.toString())){
                                newMapTypes.push(mapType)
                            }
        		        })

        		       layerNode.mapType = newMapTypes
                    }
                })
                //mapType[4]光分纤盒
        		// layerNodes[1].mapType[4].labelField = shortLabel2;
        		//mapType[5]光终端盒
        		// layerNodes[1].mapType[5].labelField = shortLabel2;

        		//mapType[10]分线盒
        		// layerNodes[1].mapType[10].labelField = shortLabel;
        		//mapType[11]DP群
        		// layerNodes[1].mapType[11].labelField = shortLabel;
        		//mapType[13]电杆
        		// layerNodes[1].mapType[13].labelField = shortLabel;

        		 layerNodes = newLayerNodes
        		 const arcgisLayer=_context.arcgisLayerMapper
        		 if(arcgisLayer!=null){
                      layerNodes.forEach(layerNode=>{
                            if(arcgisLayer.baseLayer&&layerNode.title=='底图图层'){
                                layerNode.url=arcgisLayer.baseLayer
                                if(arcgisLayer.center){
                                    layerNode.center=arcgisLayer.center
                                }
                            }else if(arcgisLayer.vectorLyer&&layerNode.title=='矢量地图'){
                                layerNode.url=arcgisLayer.vectorLyer
                            }else if(arcgisLayer.satelliteLayer&&layerNode.title=='卫星地图'){
                                layerNode.url=arcgisLayer.satelliteLayer
                            }else if(arcgisLayer.viewFrameLayer&&layerNode.title=='图幅层'){
                                layerNode.url=arcgisLayer.viewFrameLayer
                            }
        		      })
        		  }
                  function addAddressGraphicMove(map) {
                      debugger
                      let thiz = map;

                      const modalDiv = $('<div id="topDiv" class="ui modal">');
                      $('<div class="header">请选择要移动的地址级别</div>').appendTo(modalDiv)
                      $('<div class="content"> ' +
                          '<select id ="choseLevel" class="ui dropdown" >' +
                          '  <option value="6">6级</option>' +
                          '  <option value="7">7级</option>' +
                          '  <option value="67">6和7级</option>' +
                          '</select> </div>').appendTo(modalDiv)
                      $('<div class="actions">' +
                          '<div class="ui blue deny button">取消</div>' +
                          '<div id="okDiv" class="ui green rapproveight labeled icon button">确定<i class="checkmark icon"></i></div> ' +
                          '</div>').appendTo(modalDiv)

                      let AddressGraphicMoveControl = L.Control.extend({
                        onAdd: function (map) {
                          let c = L.DomUtil.create('div', 'leaflet-control leaflet-bar');
                          let btn = L.DomUtil.create('a', 'fa fa-map', c);
                          btn.href = '#';
                          btn.title = '地址图形移动';
                          L.DomEvent.on(btn, 'click', L.DomEvent.stop).on(btn, 'click', () => {
                            _sui.showModal(modalDiv, {dimmerSettings: { opacity: .7 } });
                            $('#okDiv').click(() => {
                                $('#topDiv').modal('hide');
                                const addressLevelValue = $('#choseLevel').val()
                                const result = thiz.activeBatchMove(thiz.selectFeatures)
                                _context.events.one(_map.EVENT_BATCH_MOVE_END, ((e, data) => {
                                  debugger;
                                  //目前只处理面图形
                                  if (!data || !data.polygonList) {
                                    result.endMove()
                                    return
                                  }
                                  if (data.polygonList.length > 100) {
                                    _sui.alert("超出移动个数限制(100个)")
                                    result.endMove()
                                    return
                                  }
                                  let newPolygonLists = new Array();
                                  //目前只处理地址面
                                  let filterLists = data.polygonList.filter(l => {
                                      return l.feature && l.feature.objectType == 'POSITION'
                                  })
                                  filterLists.forEach(item => {
                                      let newPolygonList = {}
                                      let wkt = _map.toWkt(item)
                                      newPolygonList.id = item.feature.id
                                      newPolygonList.shape = wkt
                                      newPolygonList._checkShape = true
                                      newPolygonLists.push(newPolygonList)
                                  })
                                  if(_context.map) {
                                      _context.map.setWaiting();
                                  }
                                  _bean.callService('mapServiceImpl','batchMovePolygon',[newPolygonLists,addressLevelValue]).then(function (res){
                                      _sui.alert("批量移动成功!")
                                      if (_context.map) {
                                          _context.map.setWaiting(false);
                                          _context.map.refresh();
                                      }
                                  }).catch(e=>{
                                      if (_context.map) {
                                          _context.map.setWaiting(false);
                                          _context.map.refresh();
                                      }
                                  })
                                  result.endMove()
                                }))
                            })
                          });
                          return c;
                        }
                      });
                      new AddressGraphicMoveControl({position: map.defaultControlPosition}).addTo(map.lmap);
                  }
        	]]>
        </options>
    </map>
</application>