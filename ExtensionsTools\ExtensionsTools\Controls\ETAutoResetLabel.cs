﻿/*
 * ============================================================================
 * 功能模块：自动重置标签控件 (ETAutoResetLabel)
 * ============================================================================
 * 
 * 模块作用：提供一个可在指定时间后自动恢复默认文本的标签控件
 * 
 * 主要功能：
 * - 显示文本
 * - 在文本被修改后，经过指定延迟时间自动恢复为默认文本
 * 
 * 执行逻辑：
 * 1. 初始化控件时创建并配置计时器
 * 2. 当文本属性被设置为非默认文本时，启动计时器
 * 3. 计时器触发时，将文本恢复为默认文本
 * 
 * 注意事项：
 * - 需要手动管理控件的生命周期，确保资源得到正确释放
 * ============================================================================
 */

using System;
using System.Windows.Forms;

namespace ET.Controls
{
    /// <summary>
    /// 自动重置标签控件，可在指定时间后自动恢复默认文本
    /// </summary>
    public class ETAutoResetLabel : Label
    {
        /// <summary>
        /// 用于在文本更改后延迟重置文本的计时器
        /// </summary>
        private Timer _resetTimer;
        
        /// <summary>
        /// 控件的默认文本
        /// </summary>
        private string _defaultText = string.Empty;
        
        /// <summary>
        /// 重置延迟时间（毫秒）
        /// </summary>
        private int _resetDelay = 5000;

        /// <summary>
        /// 初始化 ETAutoResetLabel 的新实例
        /// </summary>
        public ETAutoResetLabel()
        {
            InitializeTimer();
        }

        /// <summary>
        /// 获取或设置默认文本
        /// </summary>
        public string DefaultText
        {
            get => _defaultText;
            set
            {
                _defaultText = value;
                Text = _defaultText;
            }
        }

        /// <summary>
        /// 获取或设置重置延迟时间（毫秒）
        /// </summary>
        public int DefaultDelay
        {
            get => _resetDelay;
            set
            {
                _resetDelay = value;
                _resetTimer.Interval = _resetDelay;
            }
        }

        /// <summary>
        /// 获取或设置标签文本，非默认文本时启动重置计时器
        /// </summary>
        public new string Text
        {
            get => base.Text;
            set
            {
                base.Text = value;
                // [修改名称] 建议属性名：DisplayText
                // 当前属性名与基类属性名相同，可能会引起混淆
                if (value != _defaultText)
                {
                    RestartResetTimer();
                }
            }
        }

        /// <summary>
        /// 初始化重置计时器
        /// </summary>
        private void InitializeTimer()
        {
            _resetTimer = new Timer
            {
                Interval = _resetDelay
            };
            _resetTimer.Tick += ResetTimer_Tick;
        }

        /// <summary>
        /// 重新启动重置计时器
        /// </summary>
        private void RestartResetTimer()
        {
            _resetTimer.Stop();
            _resetTimer.Start();
        }

        /// <summary>
        /// 重置计时器触发事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void ResetTimer_Tick(object sender, EventArgs e)
        {
            _resetTimer.Stop();
            base.Text = _defaultText;
        }

        /// <summary>
        /// 释放由 ETAutoResetLabel 使用的资源
        /// </summary>
        /// <param name="disposing">是否释放托管资源</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _resetTimer?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}