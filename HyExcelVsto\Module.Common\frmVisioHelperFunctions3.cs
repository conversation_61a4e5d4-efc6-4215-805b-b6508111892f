/*
 * ============================================================================
 * 功能模块：Visio辅助功能模块 - 第三部分
 * ============================================================================
 * 
 * 模块作用：提供Visio文档说明文本的读取和写入功能，专门处理铁塔站点信息
 * 
 * 主要功能：
 * - 说明文本操作：从Visio图形中读取和写入说明文本信息
 * - 铁塔信息提取：自动识别和提取铁塔站名、编码、经纬度、地址等信息
 * - 安全风险点处理：处理安全风险点相关文本信息
 * - 数据交换：支持Excel与Visio之间的数据双向传输
 * 
 * 执行逻辑：
 * 1. 文本识别：使用正则表达式识别特定格式的文本信息
 * 2. 数据提取：从Visio图形文本中提取结构化数据
 * 3. 数据写入：将Excel中的数据写入到Visio图形
 * 4. 批量处理：支持批量处理多个文件和页面
 * 
 * 注意事项：
 * - 依赖特定的文本格式和正则表达式模式
 * - 需要确保Visio图形包含预期的文本结构
 * - 支持铁塔站点信息的标准化处理
 * ============================================================================
 */

using ET;
using Microsoft.Office.Interop.Excel;
using Microsoft.Office.Interop.Visio;
using System;
using System.Collections.Generic;
using TextBox = System.Windows.Forms.TextBox;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// Visio辅助功能静态类 - 第三部分
    /// 提供说明文本操作和铁塔信息处理功能
    /// </summary>
    public static partial class VisioHelperFunctions
    {
        #region 变量
        /// <summary>
        /// 铁塔站名识别正则表达式模式
        /// 匹配格式：铁塔站址名称为/铁塔站址名称/铁塔站名 + 冒号（可选） + 站名内容
        /// </summary>
        const string pattern铁塔站名 = @"(?:铁塔站址名称为|铁塔站址名称|铁塔站名)[:：]?\s*([\u4e00-\u9fa5\p{N}\p{L}\s]+)";
        
        /// <summary>
        /// 铁塔编码识别正则表达式模式
        /// 匹配14-22位数字的铁塔编码
        /// </summary>
        const string pattern铁塔编码 = @"(\d{14,22})";
        
        /// <summary>
        /// 地址信息识别正则表达式模式
        /// 匹配地址标识后的内容或行尾内容
        /// </summary>
        const string pattern地址 = "(?:地址：?(.*))|(.+)(?=\\r?\\n\\s*\\Z)\r\n";
        
        /// <summary>
        /// 经度识别正则表达式模式，引用ETGPS类的标准模式
        /// </summary>
        static string pattern经度 = ETGPS.LongitudePattern;
        
        /// <summary>
        /// 纬度识别正则表达式模式，引用ETGPS类的标准模式
        /// </summary>
        static string pattern纬度 = ETGPS.LatitudePattern;
        #endregion 变量

        #region 操作说明文本
        /// <summary>
        /// 在新线程中执行说明文本操作，避免阻塞UI界面
        /// </summary>
        /// <param name="filePathRange">包含Visio文件路径的Excel单元格范围</param>
        /// <param name="pageNameRange">包含页面名称的Excel单元格范围</param>
        /// <param name="targetDirectory">保存处理后文件的目标目录</param>
        /// <param name="isDirectSave">是否直接保存到原文件</param>
        /// <param name="readRange铁塔站名编码">读取铁塔站名和编码的Excel范围</param>
        /// <param name="readRange经纬度及地址">读取经纬度和地址的Excel范围</param>
        /// <param name="readRange安全风险点">读取安全风险点的Excel范围</param>
        /// <param name="readRange说明">读取说明信息的Excel范围</param>
        /// <param name="writeRange铁塔站名编码">写入铁塔站名和编码的Excel范围</param>
        /// <param name="writeRange经纬度及地址">写入经纬度和地址的Excel范围</param>
        /// <param name="writeRange安全风险点">写入安全风险点的Excel范围</param>
        /// <param name="writeRange说明">写入说明信息的Excel范围</param>
        /// <param name="textBoxProgress">进度显示文本框</param>
        /// <param name="textBoxError">错误信息显示文本框</param>
        /// <remarks>
        /// 创建独立线程执行说明文本的读取和写入操作，
        /// 支持从Visio图形中提取铁塔站点信息并与Excel数据进行交换
        /// </remarks>
        public static void 操作说明文本PerRowsThread(
            Range filePathRange,
            Range pageNameRange,
            string targetDirectory,
            bool isDirectSave,
            Range readRange铁塔站名编码,
            Range readRange经纬度及地址,
            Range readRange安全风险点,
            Range readRange说明,
            Range writeRange铁塔站名编码,
            Range writeRange经纬度及地址,
            Range writeRange安全风险点,
            Range writeRange说明,
            TextBox textBoxProgress,
            TextBox textBoxError)
        {
            // 创建新线程执行说明文本操作
            new System.Threading.Thread(
                () => 操作说明文本PerRows(
                    filePathRange,
                    pageNameRange,
                    targetDirectory,
                    isDirectSave,
                    readRange铁塔站名编码,
                    readRange经纬度及地址,
                    readRange安全风险点,
                    readRange说明,
                    writeRange铁塔站名编码,
                    writeRange经纬度及地址,
                    writeRange安全风险点,
                    writeRange说明,
                    textBoxProgress,
                    textBoxError)).Start();
        }

        /// <summary>
        /// 执行说明文本操作的核心方法，处理Visio图形中的文本信息
        /// </summary>
        /// <param name="filePathRange">文件路径范围</param>
        /// <param name="pageNameRange">页面名称范围</param>
        /// <param name="targetDirectory">目标保存目录</param>
        /// <param name="isDirectSave">是否直接保存</param>
        /// <param name="readRange铁塔站名编码">铁塔站名编码读取范围</param>
        /// <param name="readRange经纬度及地址">经纬度地址读取范围</param>
        /// <param name="readRange安全风险点">安全风险点读取范围</param>
        /// <param name="readRange说明">说明信息读取范围</param>
        /// <param name="writeRange铁塔站名编码">铁塔站名编码写入范围</param>
        /// <param name="writeRange经纬度及地址">经纬度地址写入范围</param>
        /// <param name="writeRange安全风险点">安全风险点写入范围</param>
        /// <param name="writeRange说明">说明信息写入范围</param>
        /// <param name="textBoxProgress">进度显示控件</param>
        /// <param name="textBoxError">错误信息显示控件</param>
        /// <remarks>
        /// 执行逻辑：
        /// 1. 参数验证和范围初始化
        /// 2. 遍历文件列表，逐个处理Visio文档
        /// 3. 在每个页面中查找"说明"和"安全风险点"图形
        /// 4. 使用正则表达式提取结构化信息
        /// 5. 根据配置进行读取或写入操作
        /// 6. 保存处理结果并清理资源
        /// </remarks>
        public static void 操作说明文本PerRows(
            Range filePathRange,
            Range pageNameRange,
            string targetDirectory,
            bool isDirectSave,
            Range readRange铁塔站名编码,
            Range readRange经纬度及地址,
            Range readRange安全风险点,
            Range readRange说明,
            Range writeRange铁塔站名编码,
            Range writeRange经纬度及地址,
            Range writeRange安全风险点,
            Range writeRange说明,
            TextBox textBoxProgress,
            TextBox textBoxError)
        {
            #region 变量值获取，信息初始化
            // 验证必需的文件路径范围参数
            if (filePathRange == null)
            {
                textBoxProgress.WriteLog("请选择文件路径所在的列");
                return;
            }

            // 处理页面名称范围的逻辑
            switch (pageNameRange)
            {
                case null when filePathRange.Columns.Count > 1:
                    // 如果文件路径范围有多列，使用第二列作为页面名称
                    pageNameRange = filePathRange.Columns[2];
                    break;

                case null:
                    // 没有指定页面名称，默认操作第一页
                    textBoxProgress.WriteLog("没有选择页面所在列，默认操作第一页");
                    break;
            }
            // 优化文件路径范围，去除空行和隐藏行
            filePathRange = filePathRange.OptimizeRangeSize();

            // 自动设置读取范围：如果铁塔站名编码范围包含多列，自动分配其他范围
            if (readRange铁塔站名编码 != null && readRange铁塔站名编码.Columns.Count > 5 && readRange经纬度及地址 == null)
                readRange经纬度及地址 = readRange铁塔站名编码.GetColumnsInRange(3, 5).EntireColumn;
            if (readRange铁塔站名编码 != null && readRange铁塔站名编码.Columns.Count > 2 && readRange安全风险点 == null)
                readRange安全风险点 = readRange铁塔站名编码.Columns[6].EntireColumn;
            if (readRange铁塔站名编码 != null && readRange铁塔站名编码.Columns.Count > 3 && readRange说明 == null)
                readRange说明 = readRange铁塔站名编码.Columns[7].EntireColumn;
            // 判断是否需要执行读取操作
            bool needRead = readRange铁塔站名编码 != null ||
                readRange经纬度及地址 != null ||
                readRange安全风险点 != null ||
                readRange说明 != null;

            // 自动设置写入范围：如果铁塔站名编码范围包含多列，自动分配其他范围
            if (writeRange铁塔站名编码 != null && writeRange铁塔站名编码.Columns.Count > 1 && writeRange经纬度及地址 == null)
                writeRange经纬度及地址 = writeRange铁塔站名编码.GetColumnsInRange(3, 5).EntireColumn;
            if (writeRange铁塔站名编码 != null && writeRange铁塔站名编码.Columns.Count > 2 && writeRange安全风险点 == null)
                writeRange安全风险点 = writeRange铁塔站名编码.Columns[6].EntireColumn;
            if (writeRange铁塔站名编码 != null && writeRange铁塔站名编码.Columns.Count > 3 && writeRange说明 == null)
                writeRange说明 = writeRange铁塔站名编码.Columns[7].EntireColumn;
            // 判断是否需要执行写入操作
            bool needWrite = writeRange铁塔站名编码 != null ||
                writeRange经纬度及地址 != null ||
                writeRange安全风险点 != null ||
                writeRange说明 != null;
            #endregion 变量值获取，信息初始化
            // 创建Visio应用程序实例（可见模式）
            Microsoft.Office.Interop.Visio.Application visioApp = ETVisio.CreateApplication(true);
            
            Document docPrev = null;           // 跟踪上一个处理的文档
            Range filePathCellPrev = null;     // 跟踪上一个处理的单元格

            // 获取处理进度相关信息
            int rowCount = filePathRange.GetVisibleRowCount();
            int currentRow = 1;

            // 定义要查找的文本图形类型字典
            Dictionary<string, string> textShapes_Dic = new()
            {
                { "安全风险点", "安全风险点" },
                { "说明", "说明" }
            };

            // 遍历文件路径范围中的每一行
            foreach (Range row in filePathRange.Rows)
            {
                // 跳过隐藏行
                if (row.EntireRow.Hidden)
                    continue;

                // 获取当前行的文件路径和页面名称单元格
                Range filePathCell = filePathRange.Cells[row.Row, 1];
                Range pageNameCell = pageNameRange?.Cells[row.Row, 1];
                // 跳过空的文件路径单元格
                if (ETExcelExtensions.IsCellEmpty(filePathCell))
                    continue;

                // 提取文件信息
                string filePath = filePathCell.Value2.ToString();
                string pageName = pageNameCell?.Value2.ToString();
                string fileName = System.IO.Path.GetFileName(filePath);
                Document doc;

                // 显示当前处理进度
                textBoxProgress.WriteLog($"{currentRow++}/{rowCount}.正在处理 {fileName}");

                // 检查文件是否已在Visio中打开
                doc = ETVisio.FindOpenDocumentByPath(visioApp, filePath);
                if (doc == null)
                {
                    // 文件未打开，先保存上一个文档（如果需要写入操作）
                    if (needWrite)
                    {
                        if (isDirectSave)
                            docPrev.Save(true);
                        else
                            docPrev.SaveToAnotherDirectory(targetDirectory, true);
                    }

                    // 标记上一个文件处理完成
                    if (filePathCellPrev != null)
                        filePathCellPrev.Format条件格式警示色(EnumWarningColor.蓝绿);

                    // 打开新文件
                    doc = ETVisio.Open(visioApp, filePath, textBoxProgress, false);
                    docPrev = doc;
                    filePathCellPrev = filePathCell;
                }
                else
                {
                    // 文件已经打开，更新文档引用
                    docPrev = doc;
                    filePathCellPrev = filePathCell;
                }

                // 检查文档是否成功打开
                if (doc == null)
                {
                    textBoxProgress.WriteLog($"出现意外错误，无法处理文件：{fileName}");
                    textBoxError.WriteLog($"出现意外错误，无法处理文件：{fileName}");
                    filePathCell.Format条件格式警示色(EnumWarningColor.虾红);
                    continue;
                }

                // 获取目标页面：如果未指定页面名称则使用第一页，否则查找指定页面
                dynamic page = string.IsNullOrEmpty(pageName) ? doc.Pages[1] : ETVisio.FindPage(doc, pageName);
                if (page == null)
                {
                    textBoxProgress.WriteLog($"文件{fileName}中没有找到页面{pageName}");
                    textBoxError.WriteLog($"文件{fileName}中没有找到页面{pageName}");
                    filePathCell.Format条件格式警示色(EnumWarningColor.虾红);
                    continue;
                }

                // 在页面中查找以指定文本开头的图形
                Dictionary<string, List<dynamic>> textShapesDictionary = ETVisio.FindShapesStartingWithText(
                    page,
                    textShapes_Dic);

                // 处理"说明"图形
                if (textShapesDictionary.ContainsKey("说明"))
                {
                    List<dynamic> shapes = textShapesDictionary["说明"];
                    if (shapes.Count == 0)
                        continue;
                    Microsoft.Office.Interop.Visio.Shape shape = shapes[0];
                    string shapeText = shape.GetShapeText();

                    // 移除第一行的"说明"标题，获取纯文本内容
                    string originalText说明 = shapeText.RemoveFirstLineIfStartsWith("说明");

                    // 使用正则表达式提取各种信息
                    string originalValue铁塔站名 = ETString.RegexMatchText(shapeText, pattern铁塔站名, 1);
                    string originalValue铁塔编码 = ETString.RegexMatchText(shapeText, pattern铁塔编码);
                    string originalValue经度 = ETString.RegexMatchText(shapeText, pattern经度);
                    string originalValue纬度 = ETString.RegexMatchText(shapeText, pattern纬度);
                    string originalValue地址 = ETString.RegexMatchText(shapeText, pattern地址, 1);

                    // 如果需要读取操作，将提取的信息写入Excel
                    if (needRead)
                    {
                        ShapeTextToExcel(readRange说明, row.Row, 1, originalText说明);

                        ShapeTextToExcel(readRange铁塔站名编码, row.Row, 1, originalValue铁塔站名);
                        ShapeTextToExcel(readRange铁塔站名编码, row.Row, 2, originalValue铁塔编码);
                        Set文本格式(readRange铁塔站名编码, row.Row, 2, originalValue铁塔编码);
                        ShapeTextToExcel(readRange经纬度及地址, row.Row, 1, originalValue经度);
                        ShapeTextToExcel(readRange经纬度及地址, row.Row, 2, originalValue纬度);
                        ShapeTextToExcel(readRange经纬度及地址, row.Row, 3, originalValue地址);
                    }
                    // 如果需要写入操作，将Excel中的数据写入Visio图形
                    if (needWrite)
                    {
                        ETVisio.SetShapeText(shape, writeRange说明, row.Row, 1);

                        ETVisio.ReplaceText(shape, originalValue铁塔站名, writeRange铁塔站名编码, row.Row, 1);
                        ETVisio.ReplaceText(shape, originalValue铁塔编码, writeRange铁塔站名编码, row.Row, 2);
                        ETVisio.ReplaceText(shape, originalValue经度, writeRange经纬度及地址, row.Row, 1);
                        ETVisio.ReplaceText(shape, originalValue纬度, writeRange经纬度及地址, row.Row, 2);
                        ETVisio.ReplaceText(shape, originalValue地址, writeRange经纬度及地址, row.Row, 3);
                    }
                }

                // 处理"安全风险点"图形
                if (textShapesDictionary.ContainsKey("安全风险点"))
                {
                    List<dynamic> shapes = textShapesDictionary["安全风险点"];
                    if (shapes.Count == 0)
                        continue;
                    Microsoft.Office.Interop.Visio.Shape shape = shapes[0];
                    string shapeText = shape.GetShapeText();

                    // 移除第一行的"安全风险点"标题，获取纯文本内容
                    string originalText安全风险点 = shapeText.RemoveFirstLineIfStartsWith("安全风险点");

                    // 如果需要读取操作，将安全风险点信息写入Excel
                    if (needRead)
                    {
                        ShapeTextToExcel(readRange安全风险点, row.Row, 1, originalText安全风险点);
                    }
                    // 如果需要写入操作，将Excel中的数据写入Visio图形
                    if (needWrite)
                    {
                        ETVisio.SetShapeText(shape, readRange安全风险点, row.Row, 1);
                    }
                }
            }

            // 处理完所有文件后，保存最后一个文档（如果需要写入操作）
            if (needWrite)
            {
                if (isDirectSave)
                    docPrev.Save(true);
                else
                    docPrev.SaveToAnotherDirectory(targetDirectory, true);
            }
            // 标记最后一个文件处理完成
            if (filePathCellPrev != null)
                filePathCellPrev.Format条件格式警示色(EnumWarningColor.蓝绿);

            // 关闭Visio应用程序并清理资源
            visioApp.Quit();
            textBoxProgress.WriteLog("执行完成");
        }

        /// <summary>
        /// 将Visio图形中提取的文本写入Excel指定单元格
        /// </summary>
        /// <param name="range">目标Excel范围对象</param>
        /// <param name="rowIndex">目标行索引（从1开始，Excel标准）</param>
        /// <param name="colIndex">目标列索引（从1开始，Excel标准）</param>
        /// <param name="value">要写入的文本值</param>
        /// <exception cref="ETException">当参数无效或Excel操作失败时抛出</exception>
        /// <remarks>
        /// 执行逻辑：
        /// 1. 验证输入参数的有效性（范围对象、索引值）
        /// 2. 检查目标单元格是否在指定范围内
        /// 3. 将文本值写入目标单元格
        /// 4. 记录操作日志和异常处理
        /// 
        /// 注意事项：
        /// - 空值会被跳过，不执行写入操作
        /// - 超出范围的单元格会被跳过并记录日志
        /// - 所有操作都有完整的异常处理和日志记录
        /// </remarks>
        public static void ShapeTextToExcel(Range range, int rowIndex, int colIndex, string value)
        {
            // 验证必需参数
            if (range == null)
                throw new ETException("Excel范围对象不能为空", "Visio.ShapeTextToCell");
            if (rowIndex < 0)
                throw new ETException("行索引不能为负数", "Visio.ShapeTextToCell");
            if (colIndex < 0)
                throw new ETException("列索引不能为负数", "Visio.ShapeTextToCell");

            try
            {
                // 检查要写入的值是否为空
                if (string.IsNullOrEmpty(value))
                {
                    ETLogManager.Info("Visio.ShapeTextToCell", "要写入的值为空，操作已跳过");
                    return;
                }

                // 检查目标单元格是否在范围内
                if (range.Columns.Count < colIndex || range.Rows.Count <= rowIndex)
                {
                    ETLogManager.Info("Visio.ShapeTextToCell", "目标单元格超出范围，操作已跳过");
                    return;
                }

                // 执行写入操作
                range.Cells[rowIndex, colIndex].Value = value;
                ETLogManager.Info("Visio.ShapeTextToCell", $"成功将文本写入单元格[{rowIndex},{colIndex}]: {value}");
            }
            catch (Exception ex)
            {
                // 异常处理和日志记录
                string errorMessage = $"写入Excel单元格[{rowIndex},{colIndex}]失败";
                ETLogManager.Error("Visio.ShapeTextToCell", ex);
                throw new ETException(errorMessage, "Visio.ShapeTextToCell", ex);
            }
        }


        #endregion 操作说明文本
    }
}