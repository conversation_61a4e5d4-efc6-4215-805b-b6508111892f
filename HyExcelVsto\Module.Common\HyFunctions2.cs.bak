﻿using ET;

using Microsoft.Office.Core;
using Microsoft.Office.Interop.Excel;

namespace HyExcelVsto.Module.Common
{
    partial class HyFunctions
    {
        public static void Button公式转数值_Click(CommandBarButton sender, ref bool cancelDefault)
        {
            Range selectionRange = ETExcelExtensions.GetSelectionRange();
            selectionRange.ConvertToNumeric(true);
            ETExcelExtensions.SetAppNormalMode(true);
        }
    }
}