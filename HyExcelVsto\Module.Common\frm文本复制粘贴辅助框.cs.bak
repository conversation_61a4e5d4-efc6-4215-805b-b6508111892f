﻿using ET;
using HyExcelVsto.Interfaces;
using Microsoft.Office.Interop.Excel;
using System;
using System.Windows.Forms;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// 文本复制粘贴辅助框窗体类
    /// </summary>
    public partial class frm文本复制粘贴辅助框 : Form, IExcelMessageReceiver
    {
        private Range selectedRange;
        private int currentRowIndex;
        private const int MAX_SEARCH_ROWS = 10000;
        private bool isManuallyResized = false;
        private float columnRatio = 0.2f; // 默认第一列占总宽度的20%

        /// <summary>
        /// 构造函数
        /// </summary>
        public frm文本复制粘贴辅助框()
        {
            InitializeComponent();
            InitializeListView();
            InitializeButtons();
            UpdateListViewHeader(); // 添加这一行
        }

        /// <summary>
        /// 初始化ListView控件
        /// </summary>
        private void InitializeListView()
        {
            listView1.FullRowSelect = true;
            listView1.GridLines = true;
            listView1.View = View.Details;
            listView1.LabelEdit = true;
            listView1.HeaderStyle = ColumnHeaderStyle.Nonclickable; // 显示标题栏，但不可点击
            listView1.Click += ListView1_Click;
            listView1.ColumnWidthChanged += ListView1_ColumnWidthChanged;
        }

        /// <summary>
        /// 初始化按钮状态
        /// </summary>
        private void InitializeButtons()
        {
            buttonPreviousRow.Enabled = false;
            buttonNextRow.Enabled = false;
        }

        /// <summary>
        /// 指示是否正在更新
        /// </summary>
        public bool IsUpdating { get; private set; }

        /// <summary>
        /// 更新选定范围和当前行
        /// </summary>
        /// <param name="forceUpdate">是否强制更新</param>
        public void UpdateSelectedRange(bool forceUpdate = false)
        {
            if (IsUpdating)
                return;

            IsUpdating = true;

            try
            {
                if (!forceUpdate && !checkBoxTrackSelection.Checked)
                    return;

                Range newRange = ETExcelExtensions.GetSelectionRange();
                if (newRange == null)
                    return;

                selectedRange = newRange;
                currentRowIndex = selectedRange.Row;

                // 检查是否存在筛选行
                bool hasFilterRow = CheckForFilterRow(selectedRange.Worksheet);

                UpdateListViewContent(hasFilterRow);
                UpdateListViewHeader(); // 添加这一行
            }
            finally
            {
                IsUpdating = false;
            }
        }

        /// <summary>
        /// 检查工作表是否存在筛选行
        /// </summary>
        /// <param name="worksheet">要检查的工作表</param>
        /// <returns>如果存在筛选行返回true，否则返回false</returns>
        private bool CheckForFilterRow(Worksheet worksheet)
        {
            // 检查工作表是否有自动筛选
            return worksheet.AutoFilterMode;
        }

        /// <summary>
        /// 更新ListView的内容
        /// </summary>
        /// <param name="hasFilterRow">是否存在筛选行</param>
        private void UpdateListViewContent(bool hasFilterRow)
        {
            listView1.BeginUpdate();
            try
            {
                listView1.Items.Clear();

                if (selectedRange == null)
                    return;

                // 只有在列数发生变化时才清除并重新添加列
                if ((hasFilterRow && listView1.Columns.Count != 2) || (!hasFilterRow && listView1.Columns.Count != 1))
                {
                    listView1.Columns.Clear();
                    if (hasFilterRow)
                    {
                        listView1.Columns.Add("标题", -2, System.Windows.Forms.HorizontalAlignment.Left);
                        listView1.Columns.Add("内容", -2, System.Windows.Forms.HorizontalAlignment.Left);
                    }
                    else
                    {
                        listView1.Columns.Add("内容", -2, System.Windows.Forms.HorizontalAlignment.Left);
                    }
                    isManuallyResized = false;
                }

                // 获取筛选行（如果存在）
                Range filterRow = hasFilterRow ? selectedRange.Worksheet.AutoFilter.Range.Rows[1] : null;

                // 遍历所有选中的区域
                foreach (Range area in selectedRange.Areas)
                {
                    // 获取当前区域的当前行
                    Range currentRowArea = area.Worksheet.Range[
                        area.Worksheet.Cells[currentRowIndex, area.Column],
                        area.Worksheet.Cells[currentRowIndex, area.Column + area.Columns.Count - 1]];

                    // 遍历当前区域的所有单元格
                    foreach (Range cell in currentRowArea.Cells)
                    {
                        // 检查列是否隐藏
                        if (!cell.EntireColumn.Hidden)
                        {
                            string cellText = cell.Text;
                            ListViewItem item;

                            if (hasFilterRow)
                            {
                                string headerText = filterRow.Cells[1, cell.Column].Text;
                                item = new ListViewItem(new string[] { headerText, cellText });
                            }
                            else
                            {
                                item = new ListViewItem(cellText);
                            }

                            listView1.Items.Add(item);
                        }
                    }
                }

                UpdateButtonStates();

                // 如果列数发生变化，重新调整列宽
                if (!isManuallyResized)
                {
                    AdjustColumnWidths();
                }
            }
            finally
            {
                listView1.EndUpdate();
            }
        }

        /// <summary>
        /// 更新按钮状态
        /// </summary>
        private void UpdateButtonStates()
        {
            buttonPreviousRow.Enabled = currentRowIndex > 1;
            buttonNextRow.Enabled = true; // 总是启用"下一行"按钮，因为我们不知道下一行是否有数据
        }

        /// <summary>
        /// 处理ListView的单击事件
        /// </summary>
        private void ListView1_Click(object sender, EventArgs e)
        {
            if (listView1.SelectedItems.Count > 0)
            {
                string selectedText;

                if (listView1.Columns.Count == 2)
                {
                    // 如果有两列，复制第二列的值
                    selectedText = listView1.SelectedItems[0].SubItems[1].Text;
                }
                else
                {
                    // 如果只有一列，复制第一列的值
                    selectedText = listView1.SelectedItems[0].Text;
                }

                string trimmedText = selectedText.Trim();
                if (double.TryParse(trimmedText, out _))
                {
                    selectedText = trimmedText;
                }

                Clipboard.SetText(selectedText);
            }
        }

        /// <summary>
        /// 处理窗体大小改变事件
        /// </summary>
        private void frm文本复制粘贴辅助框_SizeChanged(object sender, EventArgs e)
        {
            listView1.Dock = DockStyle.Fill;
            AdjustColumnWidths();
        }

        /// <summary>
        /// 处理上一行按钮点击事件
        /// </summary>
        private void buttonPreviousRow_Click(object sender, EventArgs e)
        {
            if (currentRowIndex > 1)
            {
                int newRowIndex = FindPreviousVisibleRow(currentRowIndex - 1);
                if (newRowIndex > 0)
                {
                    currentRowIndex = newRowIndex;
                    // 检查是否存在筛选行
                    bool hasFilterRow = CheckForFilterRow(selectedRange.Worksheet);
                    UpdateListViewContent(hasFilterRow);
                    UpdateListViewHeader(); // 添加这一行
                }
            }
        }

        /// <summary>
        /// 处理下一行按钮点击事件
        /// </summary>
        private void buttonNextRow_Click(object sender, EventArgs e)
        {
            int newRowIndex = FindNextVisibleRow(currentRowIndex + 1);
            if (newRowIndex > 0)
            {
                currentRowIndex = newRowIndex;
                // 检查是否存在筛选行
                bool hasFilterRow = CheckForFilterRow(selectedRange.Worksheet);
                UpdateListViewContent(hasFilterRow);
                UpdateListViewHeader(); // 添加这一行
            }
            else
            {
                listView1.Items.Clear();
                listView1.Items.Add("连续查找1万行，找不到数据。");
            }
        }

        /// <summary>
        /// 查找上一个可见行
        /// </summary>
        /// <param name="startRow">开始查找的行号</param>
        /// <returns>找到的可见行号，如果没找到返回-1</returns>
        private int FindPreviousVisibleRow(int startRow)
        {
            for (int i = startRow; i > 0; i--)
            {
                if (!selectedRange.Worksheet.Rows[i].Hidden)
                {
                    return i;
                }
            }
            return -1;
        }

        /// <summary>
        /// 查找下一个可见行
        /// </summary>
        /// <param name="startRow">开始查找的行号</param>
        /// <returns>找到的可见行号，如果没找到返回-1</returns>
        private int FindNextVisibleRow(int startRow)
        {
            // 获取工作表的有效工作区域
            Range usedRange = selectedRange.Worksheet.UsedRange;
            int lastUsedRow = usedRange.Row + usedRange.Rows.Count - 1;

            // 计算实际的结束行
            int endRow = Math.Min(Math.Min(startRow + MAX_SEARCH_ROWS, lastUsedRow), selectedRange.Worksheet.Rows.Count);

            for (int i = startRow; i <= endRow; i++)
            {
                if (!selectedRange.Worksheet.Rows[i].Hidden)
                {
                    Range currentRow = selectedRange.Worksheet.Range[
                        selectedRange.Worksheet.Cells[i, selectedRange.Column],
                        selectedRange.Worksheet.Cells[i, selectedRange.Column + selectedRange.Columns.Count - 1]];

                    if (!IsRowEmpty(currentRow))
                    {
                        return i;
                    }
                }
            }
            return -1;
        }

        /// <summary>
        /// 检查行是否为空
        /// </summary>
        /// <param name="row">要检查的行</param>
        /// <returns>如果行为空返回true，否则返回false</returns>
        private bool IsRowEmpty(Range row)
        {
            foreach (Range cell in row.Cells)
            {
                if (!cell.EntireColumn.Hidden && !string.IsNullOrWhiteSpace(cell.Text))
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 处理跟踪选择复选框状态改变事件
        /// </summary>
        private void checkBoxTrackSelection_CheckedChanged(object sender, EventArgs e)
        {
            if (checkBoxTrackSelection.Checked)
            {
                UpdateSelectedRange(true);
            }
        }

        /// <summary>
        /// 处理列宽变化事件
        /// </summary>
        private void ListView1_ColumnWidthChanged(object sender, ColumnWidthChangedEventArgs e)
        {
            isManuallyResized = true;
            if (listView1.Columns.Count == 2)
            {
                columnRatio = (float)listView1.Columns[0].Width / listView1.ClientSize.Width;
            }
        }

        /// <summary>
        /// 重新调整列宽
        /// </summary>
        private void AdjustColumnWidths()
        {
            if (listView1.Columns.Count > 0)
            {
                if (listView1.Columns.Count == 1)
                {
                    listView1.Columns[0].Width = listView1.ClientSize.Width;
                }
                else if (listView1.Columns.Count == 2)
                {
                    // 计算大约6个字符的宽度
                    int approxSixCharWidth = TextRenderer.MeasureText("XXXXXXXXXXXX", listView1.Font).Width;

                    if (!isManuallyResized)
                    {
                        // 如果没有手动调整过，使用6个字符的宽度
                        listView1.Columns[0].Width = approxSixCharWidth;
                        columnRatio = (float)approxSixCharWidth / listView1.ClientSize.Width;
                    }
                    else
                    {
                        // 如果已经手动调整过，使用保存的比例，但不小于6个字符宽度
                        int firstColumnWidth = Math.Max(
                            approxSixCharWidth,
                            (int)(listView1.ClientSize.Width * columnRatio));
                        listView1.Columns[0].Width = firstColumnWidth;
                    }

                    // 设置第二列宽度
                    listView1.Columns[1].Width = listView1.ClientSize.Width - listView1.Columns[0].Width;
                }
            }
        }

        /// <summary>
        /// 更新ListView的第一列标题，显示当前行号
        /// </summary>
        private void UpdateListViewHeader()
        {
            if (listView1.Columns.Count > 0)
            {
                listView1.Columns[0].Text = $"第 {currentRowIndex} 行";
            }
        }

        /// <summary>
        /// 设置窗体是否置顶
        /// </summary>
        /// <param name="isTopMost">是否置顶</param>
        private void SetTopMost(bool isTopMost)
        { TopMost = isTopMost; }

        /// <summary>
        /// 处理置顶复选框状态改变事件
        /// </summary>
        private void checkBoxOnTop_CheckedChanged(object sender, EventArgs e)
        { SetTopMost(checkBoxOnTop.Checked); }

        private void frm文本复制粘贴辅助框_Load(object sender, EventArgs e)
        { SetTopMost(checkBoxOnTop.Checked); }

        public string FormIdentifier => "文本复制辅助";

        public void OnExcelSelectionMessage(Range target, string message = null)
        {
            UpdateSelectedRange();
        }
    }
}