﻿using ET;
using ET.Stock;
using HyExcelVsto.Interfaces;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;

namespace HyExcelVsto.Module.Stock
{
    public partial class StockLinkage : Form, IExcelMessageReceiver
    {
        /// <summary>
        /// 配置文件操作对象
        /// </summary>
        readonly ETIniFile _stockConfig;

        /// <summary>
        /// 存储当前股票代码
        /// </summary>
        string _currentStockCode;

        /// <summary>
        /// 股票配置文件路径
        /// </summary>
        static readonly string StockConfigPath = ETConfig.GetConfigPath("stock.ini");

        /// <summary>
        /// 股票CSV文件路径
        /// </summary>
        static readonly string StockCsvPath = ETConfig.GetConfigPath("stock.csv");

        /// <summary>
        /// 存储股票代码和名称的字典 key: 股票代码, value: 股票名称
        /// </summary>
        Dictionary<string, string> _stockDictionary;

        public StockLinkage()
        {
            InitializeComponent();

            // 初始化配置文件
            _stockConfig = new ETIniFile(ETConfig.GetConfigPath("stock.ini"));

            // 初始化事件和列表
            InitializeListBoxEvents();
            InitializeStockSoftwareList();

            // 绑定菜单事件
            监控配置ToolStripMenuItem.Click += 监控配置ToolStripMenuItem_Click;
            股票记录ToolStripMenuItem.Click += 股票记录ToolStripMenuItem_Click;
            配置文件ToolStripMenuItem.Click += 配置文件ToolStripMenuItem_Click;

            _stockDictionary = [];
        }

        /// <summary>
        /// 初始化列表框事件
        /// </summary>
        void InitializeListBoxEvents()
        {
            listBoxLinkage.MouseDoubleClick += ListBoxLinkage_MouseDoubleClick;
            listBoxStockSoftware.MouseDoubleClick += ListBoxStockSoftware_MouseDoubleClick;
        }

        /// <summary>
        /// 初始化股票软件列表
        /// </summary>
        void InitializeStockSoftwareList()
        {
            listBoxStockSoftware.Items.Clear();
            listBoxStockSoftware.Items.AddRange(new string[] { "通达信", "大智慧" });
        }

        /// <summary>
        /// 股票软件列表双击事件处理
        /// </summary>
        void ListBoxStockSoftware_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (listBoxStockSoftware.SelectedItem == null || string.IsNullOrEmpty(_currentStockCode))
                return;

            string selectedSoftware = listBoxStockSoftware.SelectedItem.ToString();
            StockSoftwareCommand.StockSoftwareType softwareType;

            switch (selectedSoftware)
            {
                case "通达信":
                    softwareType = StockSoftwareCommand.StockSoftwareType.TDX;
                    break;
                case "同花顺":
                    softwareType = StockSoftwareCommand.StockSoftwareType.THS;
                    break;
                case "东方财富":
                    softwareType = StockSoftwareCommand.StockSoftwareType.DFCF;
                    break;
                case "大智慧":
                    softwareType = StockSoftwareCommand.StockSoftwareType.DZH;
                    FindDzhWindowClass();
                    break;
                default:
                    return;
            }

            if (StockSoftwareCommand.JumpToStock(_currentStockCode, softwareType))
            {
                if (radioButton分钟图.Checked)
                {
                    StockSoftwareCommand.JumpToStock("FST", softwareType, 200);
                }
                else if (radioButtonK线图.Checked)
                {
                    StockSoftwareCommand.JumpToStock("K", softwareType, 200);
                }
            }
        }

        /// <summary>
        /// 清除列表项
        /// </summary>
        public void ClearLinkageItems()
        {
            listBoxLinkage.Items.Clear();
            _currentStockCode = null;
        }

        /// <summary>
        /// 打开预警配置Excel文件
        /// </summary>
        void 监控配置ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            string excelPath = _stockConfig.GetValue("File", "预警配置Excel");
            if (!string.IsNullOrEmpty(excelPath))
            {
                if (System.IO.File.Exists(excelPath))
                {
                    Process.Start(excelPath);
                }
                else
                {
                    MessageBox.Show(
                        $"预警配置Excel文件不存在:\n{excelPath}",
                        "文件不存在",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning);
                }
            }
        }

        /// <summary>
        /// 打开个股记录Excel文件
        /// </summary>
        void 股票记录ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            string excelPath = _stockConfig.GetValue("File", "个股记录Excel");
            if (!string.IsNullOrEmpty(excelPath))
            {
                if (System.IO.File.Exists(excelPath))
                {
                    Process.Start(excelPath);
                }
                else
                {
                    MessageBox.Show(
                        $"个股记录Excel文件不存在:\n{excelPath}",
                        "文件不存在",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning);
                }
            }
        }

        /// <summary>
        /// 打开配置文件
        /// </summary>
        void 配置文件ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // 调用HyFile类的OpenConfigFile方法打开stock.ini配置文件
            ET.ETConfig.OpenConfigFile("stock.ini");
        }

        /// <summary>
        /// URL列表双击事件处理
        /// </summary>
        void ListBoxLinkage_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (listBoxLinkage.SelectedItem == null)
                return;

            string selectedText = listBoxLinkage.SelectedItem.ToString();
            if (string.IsNullOrEmpty(selectedText))
                return;

            // 解析选中项获取URL配置键
            string[] parts = selectedText.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length < 2)
                return;

            string urlKey = parts[0];
            string stockCode = parts[1];

            // 直接从ini文件读取URL
            string configPath = ETConfig.GetConfigPath("stock.ini");
            if (!File.Exists(configPath))
                return;

            bool inUrlSection = false;
            string url = null;
            foreach (string line in File.ReadAllLines(configPath))
            {
                string trimmedLine = line.Trim();

                // 检查是否进入[Url]节
                if (trimmedLine.Equals("[Url]", StringComparison.OrdinalIgnoreCase))
                {
                    inUrlSection = true;
                    continue;
                }
                // 检查是否离开[Url]节
                else if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]"))
                {
                    inUrlSection = false;
                    continue;
                }

                // 如果在[Url]节中且找到对应的键
                if (inUrlSection && trimmedLine.StartsWith($"{urlKey}="))
                {
                    int equalIndex = trimmedLine.IndexOf('=');
                    if (equalIndex > 0)
                    {
                        url = trimmedLine.Substring(equalIndex + 1).Trim();
                        break;
                    }
                }
            }

            // 如果找到URL，则替换股票代码并打开
            if (!string.IsNullOrEmpty(url))
            {
                url = url.Replace("{stock_code_noprefix}", stockCode);
                Process.Start(url);
            }
        }

        /// <summary>
        /// 添加股票相关URL到列表框
        /// </summary>
        /// <param name="stockCode">股票代码</param>
        public void AddStockUrls(string stockCode)
        {
            if (string.IsNullOrWhiteSpace(stockCode))
                return;

            _currentStockCode = stockCode; // 保存当前股票代码
            listBoxLinkage.Items.Clear();

            // 读取ini文件的原始内容
            string configPath = ETConfig.GetConfigPath("stock.ini");
            if (!File.Exists(configPath))
                return;

            bool inUrlSection = false;
            foreach (string line in File.ReadAllLines(configPath))
            {
                string trimmedLine = line.Trim();

                // 检查是否进入[Url]节
                if (trimmedLine.Equals("[Url]", StringComparison.OrdinalIgnoreCase))
                {
                    inUrlSection = true;
                    continue;
                }
                // 检查是否离开[Url]节
                else if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]"))
                {
                    inUrlSection = false;
                    continue;
                }

                // 如果在[Url]节中且行不为空
                if (inUrlSection && !string.IsNullOrWhiteSpace(trimmedLine))
                {
                    // 找到第一个等号的位置
                    int equalIndex = trimmedLine.IndexOf('=');
                    if (equalIndex > 0)  // 确保找到等号且不在开头
                    {
                        string key = trimmedLine.Substring(0, equalIndex).Trim();
                        string displayText = $"{key}  {stockCode}";
                        listBoxLinkage.Items.Add(displayText);
                    }
                }
            }
        }

        /// <summary>
        /// 实现IExcelMessageReceiver接口,处理Excel单元格选择事件
        /// </summary>
        /// <param name="target">选中的单元格Range对象</param>
        /// <param name="message">附加消息,可为null</param>
        /// <remarks>
        /// 处理逻辑: 1. 优先检查选中单元格是否为股票代码 2. 如果不是，检查所在行第一列是否为股票代码 3. 如果都不是，则直接使用选中单元格的值 4. 仅当选中第1、2列时检查股票名称
        /// </remarks>
        public void OnExcelSelectionMessage(Range target, string message = null)
        {
            if (target == null)
                return;

            try
            {
                string stockCode = null;
                Range currentRow = target.Cells[1, 1].EntireRow;
                int selectedColumn = target.Cells[1, 1].Column;

                // 1. 检查选中单元格的值是否为股票代码
                string selectedCellValue = Convert.ToString(target.Cells[1, 1].Value2 ?? string.Empty);
                if (!string.IsNullOrEmpty(selectedCellValue) && _stockDictionary.ContainsKey(selectedCellValue))
                {
                    stockCode = selectedCellValue;
                }

                // 2. 如果选中单元格不是股票代码，检查所在行第一列
                if (stockCode == null)
                {
                    string firstColumnValue = Convert.ToString(currentRow.Cells[1, 1].Value2 ?? string.Empty);
                    if (!string.IsNullOrEmpty(firstColumnValue) && _stockDictionary.ContainsKey(firstColumnValue))
                    {
                        stockCode = firstColumnValue;
                    }
                    else
                    {
                        // 3. 如果都不是，使用选中单元格的值
                        stockCode = selectedCellValue;
                    }
                }

                // 更新URL列表
                if (!string.IsNullOrEmpty(stockCode))
                {
                    AddStockUrls(stockCode);
                }

                // 仅当选中第1、2列时检查并更新股票名称
                if (selectedColumn <= 2 && _stockDictionary.TryGetValue(stockCode, out string correctName))
                {
                    Range nameCell = currentRow.Cells[1, 2];
                    string currentName = Convert.ToString(nameCell.Value2 ?? string.Empty);

                    if (string.IsNullOrEmpty(currentName))
                    {
                        nameCell.Value2 = correctName;
                    }
                    else if (currentName != correctName)
                    {
                        nameCell.Interior.Color = Color.Yellow;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"处理Excel选择事件时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 实现IExcelMessageReceiver接口的FormIdentifier属性
        /// </summary>
        public string FormIdentifier => "StockLinkage";

        /// <summary>
        /// 更新个股清单菜单点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 从AkTools服务器获取A股股票列表并保存为CSV文件
        /// </remarks>
        async void 更新个股清单ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // 创建AkTools客户端
                AkToolsClient client = new();

                // 获取股票列表数据
                List<Dictionary<string, object>> stockList = await client.GetDataAsync<List<Dictionary<string, object>>>(
                    "stock_zh_a_spot_em");

                // 写入CSV文件
                using (StreamWriter writer = new(StockCsvPath, false, System.Text.Encoding.UTF8))
                {
                    // 写入数据行
                    foreach (Dictionary<string, object> stock in stockList)
                    {
                        string code = stock["代码"]?.ToString() ?? string.Empty;
                        string name = stock["名称"]?.ToString() ?? string.Empty;
                        writer.WriteLine($"{code},{name}");
                    }
                }

                // 重新加载股票数据
                LoadStockData();

                MessageBox.Show("股票清单更新成功!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"更新股票清单失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 窗体加载时初始化股票数据
        /// </summary>
        void StockLinkage_Load(object sender, EventArgs e) { LoadStockData(); }

        /// <summary>
        /// 加载股票CSV文件数据
        /// </summary>
        void LoadStockData()
        {
            try
            {
                if (File.Exists(StockCsvPath))
                {
                    _stockDictionary.Clear();
                    string[] lines = File.ReadAllLines(StockCsvPath);
                    foreach (string line in lines)
                    {
                        string[] parts = line.Split(',');
                        if (parts.Length == 2)
                        {
                            string code = parts[0].Trim();
                            string name = parts[1].Trim();
                            _stockDictionary[code] = name;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"读取股票CSV文件失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 用于调试时查找大智慧实际窗口类名的辅助方法
        /// </summary>
        public static void FindDzhWindowClass()
        {
            IntPtr desktopWindow = GetDesktopWindow();
            List<string> windowClasses = [];
            EnumChildWindows(desktopWindow, (hwnd, lparam) =>
            {
                StringBuilder className = new(256);
                StringBuilder windowTitle = new(256);
                GetClassName(hwnd, className, className.Capacity);
                GetWindowText(hwnd, windowTitle, windowTitle.Capacity);

                if (windowTitle.ToString().Contains("大智慧") || className.ToString().Contains("Dzh"))
                {
                    string info = $"Window Title: {windowTitle}, Class Name: {className}";
                    MessageBox.Show(info);
                }
                return true;
            }, IntPtr.Zero);
        }

        [DllImport("user32.dll")]
        static extern IntPtr GetDesktopWindow();

        [DllImport("user32.dll")]
        static extern bool EnumChildWindows(IntPtr hwndParent, EnumWindowsProc lpEnumFunc, IntPtr lParam);

        [DllImport("user32.dll")]
        static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

        [DllImport("user32.dll")]
        static extern int GetClassName(IntPtr hWnd, StringBuilder lpClassName, int nMaxCount);

        delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);
    }
}
