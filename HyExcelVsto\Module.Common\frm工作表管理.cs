﻿/*
 * ============================================================================
 * 功能模块：Excel工作表管理工具
 * ============================================================================
 * 
 * 模块作用：提供Excel工作簿中工作表的显示、隐藏和深度隐藏等管理功能
 * 
 * 主要功能：
 * - 工作表列表显示：以列表形式展示当前工作簿中的所有工作表及其可见性状态
 * - 可见性切换：通过复选框快速切换工作表的显示/隐藏状态
 * - 深度隐藏：支持将工作表设置为深度隐藏状态（xlSheetVeryHidden），用户无法通过右键菜单显示
 * - 状态标识：使用不同颜色标识工作表的可见性状态（正常、隐藏、深度隐藏）
 * - 权限控制：根据授权级别控制深度隐藏等高级功能的访问权限
 * 
 * 执行逻辑：
 * 1. 窗体加载：获取Excel应用程序实例→遍历工作簿中的所有工作表→构建列表显示
 * 2. 状态切换：用户点击复选框→获取目标工作表→设置可见性属性→更新界面显示
 * 3. 深度隐藏：右键菜单选择→设置xlSheetVeryHidden状态→更新颜色标识
 * 4. 列表刷新：重新扫描工作表→清空现有列表→重新构建显示项
 * 
 * 注意事项：
 * - 深度隐藏的工作表用灰色字体显示，普通用户无法通过Excel界面显示
 * - 支持专业版功能控制，可限制普通用户访问深度隐藏功能
 * - 操作过程中会阻止重复加载，避免界面闪烁和性能问题
 * - 所有操作都有完善的异常处理和日志记录
 * ============================================================================
 */

using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Drawing;
using System.Windows.Forms;
using Application = Microsoft.Office.Interop.Excel.Application;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// Excel工作表管理窗体，提供工作表显示/隐藏等管理功能
    /// </summary>
    public partial class frm工作表管理 : Form
    {
        #region 变量

        /// <summary>
        /// Excel应用程序实例
        /// </summary>
        public Application XlApp;

        /// <summary>
        /// 是否启用专业版功能（控制深度隐藏等高级功能的访问权限）
        /// </summary>
        readonly bool _proFunctionEnabled = true; // ThisAddIn.AuthorizationManager.IsAuthorized("hhy");

        /// <summary>
        /// 标识是否正在重新加载工作表列表
        /// </summary>
        bool _isReloading;

        #endregion 变量

        /// <summary>
        /// 处理工作表项选中/取消选中事件
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">包含选中项信息的事件参数</param>
        void listView工作表_ItemChecked(object sender, ItemCheckedEventArgs e)
        {
            if (_isReloading) return;
            SetSheetVisible(e.Item, e.Item.Checked ? XlSheetVisibility.xlSheetVisible : XlSheetVisibility.xlSheetHidden);
        }

        /// <summary>
        /// 设置指定工作表的可见性
        /// </summary>
        /// <param name="listItem">要设置的工作表对应的列表项</param>
        /// <param name="visibility">目标可见性状态</param>
        /// <returns>操作是否成功</returns>
        bool SetSheetVisible(ListViewItem listItem, XlSheetVisibility visibility)
        {
            try
            {
                string sheetName = listItem.SubItems[0].Text;
                Worksheet targetSheet = XlApp.ActiveWorkbook.GetWorksheetByName(sheetName);
                if (targetSheet == null)
                {
                    ETLogManager.Error("未找到工作表", new Exception($"工作表不存在：{sheetName}"));
                    return true;
                }

                targetSheet.Visible = visibility;
                listItem.ForeColor = visibility == XlSheetVisibility.xlSheetVeryHidden ? Color.DarkGray : Color.Black;
                ETLogManager.Info($"已设置工作表 {sheetName} 的可见性为: {visibility}");
                return true;
            }
            catch (Exception ex)
            {
                listItem.Checked = !listItem.Checked;
                listItem.ForeColor = visibility == XlSheetVisibility.xlSheetVeryHidden ? Color.DarkGray : Color.Black;
                throw new ETException("设置工作表可见性失败", "工作表操作", ex);
            }
        }

        /// <summary>
        /// 更新工作表列表显示
        /// </summary>
        void UpdateDisplay()
        {
            try
            {
                if (XlApp.Workbooks.Count == 0) return;

                _isReloading = true;
                listView工作表.Items.Clear();
                foreach (Worksheet worksheet in XlApp.ActiveWorkbook.Worksheets)
                {
                    ListViewItem sheetItem = new(worksheet.Name) { UseItemStyleForSubItems = false };

                    switch (worksheet.Visible)
                    {
                        case XlSheetVisibility.xlSheetVisible:
                            sheetItem.SubItems.Add(worksheet.Name);
                            sheetItem.Checked = true;
                            break;

                        case XlSheetVisibility.xlSheetHidden:
                            sheetItem.SubItems.Add(worksheet.Name);
                            sheetItem.Checked = false;
                            break;

                        case XlSheetVisibility.xlSheetVeryHidden:
                            sheetItem.SubItems.Add(worksheet.Name);
                            sheetItem.Checked = false;
                            break;
                    }

                    if (worksheet.Visible != XlSheetVisibility.xlSheetVeryHidden)
                    {
                        listView工作表.Items.Add(sheetItem);
                    }
                    else if (_proFunctionEnabled)
                    {
                        listView工作表.Items.Add(sheetItem);
                        sheetItem.ForeColor = Color.DarkGray;
                    }
                }

                ETLogManager.Info("工作表列表更新完成");
            }
            catch (Exception ex)
            {
                throw new ETException("更新工作表列表失败", "工作表操作", ex);
            }
            finally
            {
                _isReloading = false;
            }
        }

        /// <summary>
        /// 处理深度隐藏菜单项点击事件
        /// </summary>
        void 深度隐藏ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                foreach (ListViewItem selectedItem in listView工作表.SelectedItems)
                {
                    selectedItem.Checked = false;
                    selectedItem.ForeColor = Color.DarkGray;
                    if (SetSheetVisible(selectedItem, XlSheetVisibility.xlSheetVeryHidden)) continue;
                    selectedItem.ForeColor = Color.Black;
                    selectedItem.Checked = true;
                }
            }
            catch (Exception ex)
            {
                throw new ETException("深度隐藏工作表失败", "工作表操作", ex);
            }
        }

        #region 初始化

        /// <summary>
        /// 初始化工作表管理窗体
        /// </summary>
        public frm工作表管理()
        {
            InitializeComponent();
            XlApp = Globals.ThisAddIn.Application;
        }

        /// <summary>
        /// 窗体加载事件处理
        /// </summary>
        void frm工作表管理_Load(object sender, EventArgs e)
        {
            UpdateDisplay();
            listView工作表.ContextMenuStrip = _proFunctionEnabled ? contextMenuStrip1 : null;
        }

        /// <summary>
        /// 刷新按钮点击事件处理
        /// </summary>
        void 刷新ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            UpdateDisplay();
        }

        #endregion 初始化
    }
}