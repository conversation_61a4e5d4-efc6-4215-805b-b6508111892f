﻿using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Windows.Forms;

namespace HyExcelVsto.Module.Common
{
    public partial class frmWordHelper : Form
    {
        public frmWordHelper()
        {
            InitializeComponent();
        }

        void frmHelper_Load(object sender, EventArgs e)
        {
            ETForm.BindWindowsFormControl(textboxSavePath输出路径, ThisAddIn.ConfigurationSettings, "Word辅助填写", "textboxSavePath输出路径");
            ETForm.BindWindowsFormControl(ucFileSelectTemplatePath, ThisAddIn.ConfigurationSettings, "Word辅助填写", "ucFileSelectTemplatePath");
        }

        void button替换Word关键字_Click(object sender, EventArgs e)
        {
            ucFileSelectTemplatePath.SavePathHistoryToFile();

            // 在线程中执行工作
            Range dataRange = ucExcelRangeSelectData.SelectedRange.OptimizeRangeSize();
            if (dataRange == null) return;

            ETWord.ReplaceTextInWordPerRowsThread(ucFileSelectTemplatePath.Text, dataRange, textboxSavePath输出路径.Text, textBoxProgress, textBoxError);
        }

        void button执行_转换为PDF_Click(object sender, EventArgs e)
        {
            // 在线程中执行工作
            Range dataRange = ucERS文件路径_转换为PDF.SelectedRange.OptimizeRangeSize();
            if (dataRange == null) return;

            ETWord.WordToPdfPerRowsThread(dataRange, ds输出路径_转换为PDF.Text, ds原根路径_转换为PDF.Text, textBoxProgress, textBoxError);
        }
    }
}