﻿/*
 * ============================================================================
 * 功能模块：Excel自动脚本执行工具
 * ============================================================================
 *
 * 模块作用：为Excel提供自动化脚本执行和管理功能
 *
 * 主要功能：
 * - 脚本管理：创建、编辑和管理自动执行脚本
 * - 脚本执行：按照预定义的脚本自动执行Excel操作
 * - 控制表管理：管理autoExecute和autoUpdate控制工作表
 * - 参数配置：支持脚本参数的动态配置和下拉选择
 * - 日志记录：记录脚本执行过程和结果
 * - 分类管理：支持脚本的分类组织和筛选
 * - 定时执行：支持定时自动执行脚本
 * - 可见性控制：控制脚本工作表的显示和隐藏
 *
 * 执行逻辑：
 * 1. 初始化控制工作表（autoExecute和autoUpdate）
 * 2. 加载脚本配置和候选项数据
 * 3. 设置下拉列表和参数验证
 * 4. 用户选择脚本并配置参数
 * 5. 执行选定的脚本并记录日志
 * 6. 更新执行状态和结果
 *
 * 注意事项：
 * - 需要模板文件支持控制表的创建
 * - 脚本执行可能影响Excel性能
 * - 支持兼容旧版控制表格式
 * ============================================================================
 */

using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Windows.Forms;
using Application = Microsoft.Office.Interop.Excel.Application;
using Range = Microsoft.Office.Interop.Excel.Range;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// Excel自动脚本执行工具窗体
    /// </summary>
    /// <remarks>提供脚本管理、执行和监控功能，支持自动化Excel操作</remarks>
    public partial class frm自动脚本 : Form
    {
        #region 字段和常量

        /// <summary>
        /// Excel应用程序实例
        /// </summary>
        public Application XlApp;

        /// <summary>
        /// 自动执行控制工作表
        /// </summary>
        public Worksheet autoExecuteControlWorksheet;

        /// <summary>
        /// 自动更新控制工作表
        /// </summary>
        public Worksheet autoUpdateControlSheet;

        /// <summary>
        /// Excel更新器实例
        /// </summary>
        public HHExcelUpdater excelUpdater = new();

        /// <summary>
        /// 工作表是否可见
        /// </summary>
        private bool _worksheetVisible;

        /// <summary>
        /// 脚本列表框行索引映射
        /// </summary>
        private readonly Dictionary<int, int> _checkedListBox脚本RowIndex = [];

        // 列号常量定义
        /// <summary>
        /// 可用命令名列号
        /// </summary>
        private const int 可用命令名_列号 = 26;

        /// <summary>
        /// 可用候选项列号
        /// </summary>
        private const int 可用候选项_列号 = 27;

        /// <summary>
        /// 可用命令行状态列号
        /// </summary>
        private const int 可用命令行状态_列号 = 28;

        /// <summary>
        /// 动作集合名列号
        /// </summary>
        private const int 动作集合名_列号 = 1;

        /// <summary>
        /// 命令行状态列号
        /// </summary>
        private const int 命令行状态_列号 = 2;

        /// <summary>
        /// 命令行列号
        /// </summary>
        private const int 命令行_列号 = 3;

        /// <summary>
        /// 作用对象列号
        /// </summary>
        private const int 作用对象_列号 = 4;

        /// <summary>
        /// 参数1列号
        /// </summary>
        private const int 参数1_列号 = 5;

        /// <summary>
        /// 参数2列号
        /// </summary>
        private const int 参数2_列号 = 6;

        /// <summary>
        /// 参数3列号
        /// </summary>
        private const int 参数3_列号 = 7;

        /// <summary>
        /// 参数4列号
        /// </summary>
        private const int 参数4_列号 = 8;

        /// <summary>
        /// 输出1列号
        /// </summary>
        private const int 输出1_列号 = 9;

        /// <summary>
        /// 输出2列号
        /// </summary>
        private const int 输出2_列号 = 10;

        /// <summary>
        /// 备注列号
        /// </summary>
        private const int 备注_列号 = 11;

        // 按钮文本常量定义
        /// <summary>
        /// 隐藏脚本表按钮文本
        /// </summary>
        private const string 隐藏脚本表text = "隐藏脚本表";

        /// <summary>
        /// 打开脚本表按钮文本
        /// </summary>
        private const string 打开脚本表text = "打开脚本表";

        /// <summary>
        /// 隐藏更新数据策略表按钮文本
        /// </summary>
        private const string 隐藏更新数据策略表text = "隐藏更新数据策略表";

        /// <summary>
        /// 打开更新数据策略表按钮文本
        /// </summary>
        private const string 打开更新数据策略表text = "打开更新数据策略表";

        #endregion 字段和常量

        #region 初始化

        /// <summary>
        /// 初始化自动脚本窗体和控制工作表
        /// </summary>
        /// <param name="addTabel">是否添加表格，默认为true</param>
        /// <returns>初始化是否成功</returns>
        /// <remarks>执行逻辑：获取控制工作表 → 创建缺失的工作表 → 设置下拉列表 → 填入候选数据</remarks>
        public bool 初始化(bool addTabel = true)
        {
            // 获取自动执行控制工作表
            autoExecuteControlWorksheet = XlApp.ActiveWorkbook.GetWorksheetByName("autoExecute");
            // 获取自动更新控制工作表
            autoUpdateControlSheet = XlApp.ActiveWorkbook.GetWorksheetByName("autoUpdate");

            // 处理自动执行控制工作表
            if (autoExecuteControlWorksheet == null)
            {
                if (!addTabel)
                {
                    弹出修改控制表提示();
                    ETExcelExtensions.SetAppNormalMode(true);
                    return false;
                }
                弹出修改控制表提示();
                autoExecuteControlWorksheet = ETExcelExtensions.CopyWorksheetFromTemplateToWorkbook(
                    "autoExecute",
                    XlApp.ActiveWorkbook);
            }

            // 处理自动更新控制工作表
            if (autoUpdateControlSheet == null && addTabel)
            {
                autoUpdateControlSheet = ETExcelExtensions.CopyWorksheetFromTemplateToWorkbook(
                    "autoUpdate",
                    XlApp.ActiveWorkbook);
            }

            // 检查自动执行控制工作表是否存在
            if (autoExecuteControlWorksheet == null)
            {
                textboxLog.WriteLog("找不到控制列表");
                ETExcelExtensions.SetAppNormalMode(true);
                return false;
            }

            // 填入候选信息
            填入候选信息();

            try
            {
                // 设置单元格值和下拉列表
                autoExecuteControlWorksheet.Cells[1, 命令行状态_列号].Value = "动作状态";
                设置下拉列表(命令行_列号, 可用命令名_列号);
                设置下拉列表(命令行状态_列号, 可用命令行状态_列号);
                设置下拉列表(参数1_列号, 可用候选项_列号);
                设置下拉列表(参数2_列号, 可用候选项_列号);
                设置下拉列表(参数3_列号, 可用候选项_列号);
                设置下拉列表(参数4_列号, 可用候选项_列号);
            }
            catch (Exception ex)
            {
                textboxLog.WriteLog(ex.Message);
            }

            ETExcelExtensions.SetAppNormalMode(true);
            return true;
        }

        /// <summary>
        /// 初始化自动脚本窗体
        /// </summary>
        /// <param name="worksheetVisible">工作表是否可见，默认为true</param>
        /// <param name="timerInterval">定时器间隔（毫秒），默认为2000</param>
        /// <remarks>根据可见性参数调整窗体布局和控件显示状态</remarks>
        public frm自动脚本(bool worksheetVisible = true, int timerInterval = 2000)
        {
            InitializeComponent();
            XlApp = Globals.ThisAddIn.Application;
            _worksheetVisible = worksheetVisible;

            timerCloseWindow.Interval = timerInterval;

            if (!worksheetVisible)
            {
                调整控件位置和可见性();
            }
        }

        /// <summary>
        /// 调整控件位置和可见性（隐藏模式）
        /// </summary>
        /// <remarks>在隐藏模式下调整窗体大小和控件布局，只显示日志区域</remarks>
        private void 调整控件位置和可见性()
        {
            // 调整日志文本框位置
            textboxLog.Left = 6;
            textboxLog.Top = 3;

            // 缩小窗体高度
            Height = 206;

            // 隐藏不必要的控件
            button执行脚本.Visible = false;
            button打开脚本表.Visible = false;
            checkedListBox脚本.Visible = false;
            checkedListBox分类.Visible = false;
        }

        /// <summary>
        /// 窗体加载事件处理
        /// </summary>
        private void frm自动脚本_Load(object sender, EventArgs e)
        {
            重新加载ToolStripMenuItem_Click(sender, e);

            if (autoUpdateControlSheet != null && _worksheetVisible)
            {
                button打开脚本表.Text = 隐藏更新数据策略表text;
                autoUpdateControlSheet.Visible = XlSheetVisibility.xlSheetVisible;
            }

            if (autoExecuteControlWorksheet != null && _worksheetVisible)
            {
                button打开脚本表.Text = 隐藏脚本表text;
                autoExecuteControlWorksheet.Visible = XlSheetVisibility.xlSheetVisible;
                autoExecuteControlWorksheet.Activate();
            }
        }

        /// <summary>
        /// 窗体关闭事件处理
        /// </summary>
        private void frm自动脚本_FormClosed(object sender, FormClosedEventArgs e)
        {
            初始化(false);
            if (autoExecuteControlWorksheet != null && _worksheetVisible)
                autoExecuteControlWorksheet.Visible = XlSheetVisibility.xlSheetVeryHidden;

            if (autoUpdateControlSheet != null && _worksheetVisible)
                autoUpdateControlSheet.Visible = XlSheetVisibility.xlSheetVeryHidden;
        }

        /// <summary>
        /// 设置指定列的下拉列表
        /// </summary>
        /// <param name="targetColumn">目标列号</param>
        /// <param name="sourceColumn">源列号</param>
        private void 设置下拉列表(int targetColumn, int sourceColumn)
        {
            ETExcelExtensions.SetOptional设置下拉可选项(
                autoExecuteControlWorksheet.Columns[targetColumn],
                "=" + autoExecuteControlWorksheet.Columns[sourceColumn].Address);
        }

        /// <summary>
        /// 填入候选信息
        /// </summary>
        private void 填入候选信息()
        {
            if (autoExecuteControlWorksheet == null)
                return;

            try
            {
                ETExcelExtensions.SetAppFastMode();
                填入列数据(可用命令名_列号, 可用命令名Array);
                填入列数据(可用候选项_列号, 可用候选项Array);
                填入列数据(可用命令行状态_列号, 命令行状态Array);
                ETExcelExtensions.SetAppNormalMode(true);
            }
            catch (Exception)
            {
                // 忽略异常
            }
        }

        /// <summary>
        /// 弹出修改控制表提示信息
        /// </summary>
        /// <remarks>兼容旧版控制表，提示用户升级到新版控制表格式</remarks>
        private void 弹出修改控制表提示()
        {
            // 检查是否存在旧版控制表
            Worksheet tmpWs = XlApp.ActiveWorkbook.GetWorksheetByName("RunByList");
            if (tmpWs == null)
                return;

            // 记录日志并提示用户
            textboxLog.WriteLog("启用新版控制表");
            MessageBox.Show(
                "启用新版控制表 autoExecute，原控制表RunByList失效",
                "提示",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        /// <summary>
        /// 向指定列填入数据数组
        /// </summary>
        /// <param name="columnIndex">目标列索引</param>
        /// <param name="dataArray">要填入的数据数组</param>
        /// <remarks>从第1行开始逐行填入数据</remarks>
        private void 填入列数据(int columnIndex, string[] dataArray)
        {
            for (int i = 1; i <= dataArray.Length; i++)
                autoExecuteControlWorksheet.Columns[columnIndex].Cells[i, 1].Value = dataArray[i - 1];
        }

        #endregion 初始化

        #region 事件处理

        /// <summary>
        /// 打开/隐藏脚本表按钮点击事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>切换autoExecute控制工作表的可见性，如果不存在则尝试创建</remarks>
        private void button打开脚本表_Click(object sender, EventArgs e)
        {
            // 检查并创建自动执行控制工作表
            if (autoExecuteControlWorksheet == null)
            {
                弹出修改控制表提示();
                autoExecuteControlWorksheet = ETExcelExtensions.CopyWorksheetFromTemplateToWorkbook(
                    "autoExecute",
                    XlApp.ActiveWorkbook);
            }

            // 验证工作表是否创建成功
            if (autoExecuteControlWorksheet == null)
            {
                button打开脚本表.Text = 打开脚本表text;
                MessageBox.Show("模版文件缺少对应子表");
                return;
            }

            // 切换工作表可见性
            切换脚本表可见性();
        }

        /// <summary>
        /// 切换脚本表的可见性状态
        /// </summary>
        /// <remarks>根据当前按钮文本状态切换工作表显示/隐藏，并更新按钮文本</remarks>
        private void 切换脚本表可见性()
        {
            if (button打开脚本表.Text == 打开脚本表text)
            {
                // 显示工作表并激活
                autoExecuteControlWorksheet.Visible = XlSheetVisibility.xlSheetVisible;
                autoExecuteControlWorksheet.Activate();
                button打开脚本表.Text = 隐藏脚本表text;

                // 重新加载脚本列表
                重新加载ToolStripMenuItem_Click(null, EventArgs.Empty);
            }
            else
            {
                // 隐藏工作表
                autoExecuteControlWorksheet.Visible = XlSheetVisibility.xlSheetVeryHidden;
                button打开脚本表.Text = 打开脚本表text;
            }
        }

        /// <summary>
        /// 打开/隐藏更新数据策略表按钮点击事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>切换autoUpdate控制工作表的可见性，如果不存在则尝试创建</remarks>
        private void button打开更新数据策略表_Click(object sender, EventArgs e)
        {
            // 检查并创建自动更新控制工作表
            if (autoUpdateControlSheet == null)
            {
                弹出修改控制表提示();
                autoUpdateControlSheet = ETExcelExtensions.CopyWorksheetFromTemplateToWorkbook(
                    "autoUpdate",
                    XlApp.ActiveWorkbook);
            }

            // 验证工作表是否创建成功
            if (autoUpdateControlSheet == null)
            {
                button打开更新数据策略表.Text = 打开更新数据策略表text;
                MessageBox.Show("模版文件缺少对应子表");
                return;
            }

            // 切换工作表可见性
            切换更新数据策略表可见性();
        }

        /// <summary>
        /// 切换更新数据策略表的可见性状态
        /// </summary>
        /// <remarks>根据当前按钮文本状态切换工作表显示/隐藏，并更新按钮文本</remarks>
        private void 切换更新数据策略表可见性()
        {
            if (button打开更新数据策略表.Text == 打开更新数据策略表text)
            {
                // 显示工作表并激活
                autoUpdateControlSheet.Visible = XlSheetVisibility.xlSheetVisible;
                autoUpdateControlSheet.Activate();
                button打开更新数据策略表.Text = 隐藏更新数据策略表text;
            }
            else
            {
                // 隐藏工作表
                autoUpdateControlSheet.Visible = XlSheetVisibility.xlSheetVeryHidden;
                button打开更新数据策略表.Text = 打开更新数据策略表text;
            }
        }

        /// <summary>
        /// 重新加载菜单项点击事件处理器
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>清空现有列表并重新加载脚本分类和脚本列表</remarks>
        private void 重新加载ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // 清空所有列表框
            清空列表框();

            // 重新初始化，如果失败则直接返回
            if (!初始化(false))
                return;

            // 加载脚本分类列表
            加载分类列表();
        }

        /// <summary>
        /// 清空所有列表框内容
        /// </summary>
        /// <remarks>清空分类和脚本两个列表框的所有项目</remarks>
        private void 清空列表框()
        {
            checkedListBox分类.Items.Clear();
            checkedListBox脚本.Items.Clear();
        }

        /// <summary>
        /// 从Excel工作表加载脚本分类列表
        /// </summary>
        /// <remarks>从autoExecute工作表的A列读取分类数据，并设置特殊格式项的状态</remarks>
        private void 加载分类列表()
        {
            // 从Excel范围获取分类数据
            List<string> valueList = ETExcelExtensions.ConvertRangeToStringList(
                autoExecuteControlWorksheet.Range["A2:A10000"],
                true);
            if (valueList == null)
                return;

            // 将分类数据加载到列表框
            ETForm.LoadCheckedListBox(checkedListBox分类, valueList);

            // 处理特殊格式的分类项（以'---'开头的项）
            for (int i = 0; i < checkedListBox分类.Items.Count; i++)
                if (checkedListBox分类.Items[i].ToString().StartsWith("---"))
                {
                    // 设置为不确定状态且不选中
                    checkedListBox分类.SetItemCheckState(i, CheckState.Indeterminate);
                    checkedListBox分类.SetItemChecked(i, false);
                }
        }

        #endregion 事件处理

        /// <summary>
        /// 全选菜单项点击事件
        /// </summary>
        private void 全选ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // 选中脚本清单中的所有项，并在结果文本框中记录操作
            for (int i = 0; i < checkedListBox脚本.Items.Count; i++)
                checkedListBox脚本.SetItemChecked(i, true);
            textboxLog.WriteLog("全选");
        }

        /// <summary>
        /// 全取消菜单项点击事件
        /// </summary>
        private void 全取消ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // 取消选中脚本清单中的所有项，并在结果文本框中记录操作
            for (int i = 0; i < checkedListBox脚本.Items.Count; i++)
                checkedListBox脚本.SetItemChecked(i, false);
            textboxLog.WriteLog("全取消");
        }

        /// <summary>
        /// 分类项检查事件
        /// </summary>
        private void 分类项检查事件_ItemCheck(object sender, ItemCheckEventArgs e)
        {
            // 当当前状态为不确定时，保持不变
            if (e.CurrentValue == CheckState.Indeterminate)
                e.NewValue = CheckState.Indeterminate;
        }

        /// <summary>
        /// 分类列表选择变更事件
        /// </summary>
        private void CheckedListBox分类_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 清空脚本清单和相关索引数据
            checkedListBox脚本.Items.Clear();
            _checkedListBox脚本RowIndex.Clear();

            // 获取已选择分类项的集合
            HashSet<string> selectedCategories = GetSelectedCategories();

            // 遍历Excel范围内的每一行，根据选择的分类项添加对应的脚本到脚本清单中
            Range rng = ETExcelExtensions.OptimizeRangeSize(autoExecuteControlWorksheet.Range["A2:B1000"]);
            foreach (Range rngRow in rng.Rows)
            {
                if (IsValidRow(rngRow) && selectedCategories.Contains(rngRow.Cells[1, 动作集合名_列号].Value.ToString()))
                {
                    AddScriptToList(rngRow);
                }
            }
        }

        /// <summary>
        /// 获取已选择的分类
        /// </summary>
        /// <returns>已选择的分类集合</returns>
        private HashSet<string> GetSelectedCategories()
        {
            HashSet<string> selectedCategories = [];
            for (int i = 0; i < checkedListBox分类.Items.Count; i++)
                if (checkedListBox分类.GetItemChecked(i) && !checkedListBox分类.Items[i].ToString().StartsWith("---"))
                    selectedCategories.Add(checkedListBox分类.Items[i].ToString());
            return selectedCategories;
        }

        /// <summary>
        /// 检查行是否有效
        /// </summary>
        /// <param name="rngRow">行范围</param>
        /// <returns>是否有效</returns>
        private bool IsValidRow(Range rngRow)
        {
            return !ETExcelExtensions.IsCellEmpty(rngRow.Cells[1, 动作集合名_列号]) &&
                !ETExcelExtensions.IsCellEmpty(rngRow.Cells[1, 命令行_列号]);
        }

        /// <summary>
        /// 添加脚本到列表
        /// </summary>
        /// <param name="rngRow">行范围</param>
        private void AddScriptToList(Range rngRow)
        {
            checkedListBox脚本.Items.Add(rngRow.Cells[1, 命令行_列号].Value.ToString());
            checkedListBox脚本.SetItemChecked(checkedListBox脚本.Items.Count - 1, true);
            _checkedListBox脚本RowIndex.Add(rngRow.Row, checkedListBox脚本.Items.Count - 1);
        }

        /// <summary>
        /// 执行脚本按钮点击事件
        /// </summary>
        private void Button执行脚本_Click(object sender, EventArgs e)
        { Run执行脚本(); }

        /// <summary>
        /// 清空日志菜单项点击事件
        /// </summary>
        private void 清空ToolStripMenuItem_Click(object sender, EventArgs e)
        { textboxLog.Text = string.Empty; }

        /// <summary>
        /// 结果文本框鼠标悬停事件
        /// </summary>
        private void TextBox结果_MouseHover(object sender, EventArgs e)
        { timerCloseWindow.Enabled = false; }

        /// <summary>
        /// 检查范围是否为空并输出消息
        /// </summary>
        /// <param name="rng">范围</param>
        /// <param name="msg">消息</param>
        /// <returns>范围是否为空</returns>
        private bool CheckRangeIsNullAndOutputMsg(Range rng, string msg)
        {
            if (rng == null)
            {
                textboxLog.WriteLog($"{DateTime.Now:mm:ss}  {msg}  目标对象找不到");
                return true;
            }

            textboxLog.WriteLog($"{DateTime.Now:mm:ss}  {msg}({rng.Parent.Name}!{rng.Address})");
            return false;
        }

        /// <summary>
        /// 打印消息
        /// </summary>
        /// <param name="method">方法名</param>
        /// <param name="msg">附加消息</param>
        private void PrintMessage(string method, string msg = null)
        { textboxLog.WriteLog($"   执行完成{(msg == null ? string.Empty : $",{msg}")}", false); }

        /// <summary>
        /// 关闭窗口定时器事件
        /// </summary>
        private void TimerCloseWindow_Tick(object sender, EventArgs e)
        { Close(); }

        /// <summary>
        /// 通知消息
        /// </summary>
        /// <param name="msg">消息内容</param>
        private void NotifyMessage(string msg)
        {
            // 此方法目前为空，可以根据需要实现通知逻辑
        }
    }
}