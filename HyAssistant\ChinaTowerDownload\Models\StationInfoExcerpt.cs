using System;

namespace HyAssistant.ChinaTowerDownload.Models
{
    /// <summary>
    /// 表示中国铁塔站点信息的摘要类，用于存储和管理站点的基本信息。
    /// <para>包含站点标识、名称、编码等核心属性，支持序列化和比较操作。</para>
    /// </summary>
    public class StationInfoExcerpt
    {
        /// <summary>
        /// 获取或设置站点的唯一标识符。
        /// <para>此ID在系统中具有唯一性，用于标识特定的铁塔站点。</para>
        /// </summary>
        public string STATION_ID { get; set; }

        /// <summary>
        /// 获取或设置站点的名称。
        /// <para>通常包含站点的地理位置描述或功能标识，用于用户界面显示和日志记录。</para>
        /// </summary>
        public string STATION_NAME { get; set; }

        /// <summary>
        /// 获取或设置站点的编码。
        /// <para>通常为站点的业务编码或简称，用于业务逻辑处理和外部系统交互。</para>
        /// </summary>
        public string STATION_CODE { get; set; }

        /// <summary>
        /// 获取或设置该站点信息最后被抓取的时间，以Unix时间戳格式存储。
        /// <para>Unix时间戳表示自1970年1月1日00:00:00 UTC以来的秒数。</para>
        /// <para>可使用 <see cref="DateTimeOffset.FromUnixTimeSeconds(long)"/> 方法转换为标准日期时间。</para>
        /// </summary>
        public long LastCrawlTime { get; set; }

        /// <summary>
        /// 初始化 <see cref="StationInfoExcerpt"/> 类的新实例，使用默认值初始化所有属性。
        /// <para>默认情况下，<see cref="STATION_ID"/>、<see cref="STATION_NAME"/> 和 <see cref="STATION_CODE"/> 为 <c>null</c>，
        /// <see cref="LastCrawlTime"/> 为 <c>0</c>。</para>
        /// </summary>
        public StationInfoExcerpt()
        {
        }

        /// <summary>
        /// 使用指定的站点信息初始化 <see cref="StationInfoExcerpt"/> 类的新实例。
        /// </summary>
        /// <param name="stationId">站点的唯一标识符</param>
        /// <param name="stationName">站点的名称</param>
        /// <param name="stationCode">站点的编码</param>
        /// <param name="lastCrawlTime">站点信息最后被抓取的Unix时间戳（可选，默认为0）</param>
        public StationInfoExcerpt(string stationId, string stationName, string stationCode, long lastCrawlTime = 0)
        {
            STATION_ID = stationId;
            STATION_NAME = stationName;
            STATION_CODE = stationCode;
            LastCrawlTime = lastCrawlTime;
        }

        /// <summary>
        /// 返回站点信息的字符串表示，包含站点编码、名称和ID。
        /// <para>格式：<c>站点[STATION_CODE] STATION_NAME (ID: STATION_ID)</c></para>
        /// <para>主要用于调试输出、日志记录和用户界面显示。</para>
        /// </summary>
        /// <returns>格式化的站点信息字符串</returns>
        public override string ToString()
        {
            return $"站点[{STATION_CODE}] {STATION_NAME} (ID: {STATION_ID})";
        }

        /// <summary>
        /// 确定当前实例是否与另一个对象相等，基于 <see cref="STATION_ID"/> 的值进行比较。
        /// </summary>
        /// <param name="obj">要与当前实例进行比较的对象</param>
        /// <returns>如果 <paramref name="obj"/> 是 <see cref="StationInfoExcerpt"/> 且其 <see cref="STATION_ID"/> 与当前实例相同（不区分大小写），则为 <c>true</c>；否则为 <c>false</c>。</returns>
        public override bool Equals(object obj)
        {
            if (obj is StationInfoExcerpt other)
            {
                return string.Equals(STATION_ID, other.STATION_ID, StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }

        /// <summary>
        /// 返回当前实例的哈希代码，基于 <see cref="STATION_ID"/> 的值计算。
        /// <para>哈希代码用于在哈希表中进行快速查找和比较操作。</para>
        /// </summary>
        /// <returns>当前实例的哈希代码</returns>
        public override int GetHashCode()
        {
            return STATION_ID?.GetHashCode() ?? 0;
        }
    }
}
