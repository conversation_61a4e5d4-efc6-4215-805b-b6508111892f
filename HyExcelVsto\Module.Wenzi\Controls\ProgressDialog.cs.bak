using System;
using System.Drawing;
using System.Windows.Forms;

namespace HyExcelVsto.Module.Wenzi.Controls
{
    /// <summary>
    /// 进度显示对话框
    /// </summary>
    /// <remarks>
    /// 用于显示长时间操作的进度，支持进度条显示、状态文本更新和取消操作。
    /// 该对话框是线程安全的，可以从任何线程更新进度。
    /// </remarks>
    public class ProgressDialog : Form
    {
        /// <summary>
        /// 进度条控件
        /// </summary>
        private readonly ProgressBar _progressBar;

        /// <summary>
        /// 状态标签控件，用于显示当前操作状态
        /// </summary>
        private readonly Label _statusLabel;

        /// <summary>
        /// 取消按钮控件
        /// </summary>
        private readonly Button _cancelButton;

        /// <summary>
        /// 标记操作是否已被用户取消
        /// </summary>
        private bool _isCancelled;

        /// <summary>
        /// 获取一个值，指示用户是否已取消操作
        /// </summary>
        public bool IsCancelled => _isCancelled;

        /// <summary>
        /// 获取进度报告器接口，用于更新进度
        /// </summary>
        /// <remarks>
        /// 该属性返回的Progress对象是线程安全的，可以从任何线程调用
        /// </remarks>
        public IProgress<int> Progress { get; }

        /// <summary>
        /// 初始化进度对话框的新实例
        /// </summary>
        /// <param name="title">对话框标题</param>
        public ProgressDialog(string title)
        {
            // 设置窗体基本属性
            Text = title;
            Size = new Size(400, 150);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            StartPosition = FormStartPosition.CenterScreen;
            ShowInTaskbar = false;

            // 初始化进度条控件
            _progressBar = new ProgressBar
            {
                Location = new Point(20, 20),
                Size = new Size(340, 23),
                Style = ProgressBarStyle.Continuous
            };
            Controls.Add(_progressBar);

            // 初始化状态标签控件
            _statusLabel = new Label
            {
                Location = new Point(20, 50),
                Size = new Size(340, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            Controls.Add(_statusLabel);

            // 初始化取消按钮控件
            _cancelButton = new Button
            {
                Text = "取消",
                Location = new Point(285, 80),
                Size = new Size(75, 23),
                DialogResult = DialogResult.Cancel
            };
            _cancelButton.Click += (s, e) => _isCancelled = true;
            Controls.Add(_cancelButton);

            // 创建线程安全的进度报告器
            Progress = new Progress<int>(value =>
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(() => UpdateProgress(value)));
                }
                else
                {
                    UpdateProgress(value);
                }
            });
        }

        /// <summary>
        /// 更新进度条和状态信息
        /// </summary>
        /// <param name="percentage">进度百分比（0-100）</param>
        /// <param name="status">状态信息文本，如果为null则不更新状态文本</param>
        public void UpdateProgress(int percentage, string status = null)
        {
            // 确保进度值在有效范围内
            _progressBar.Value = Math.Min(Math.Max(percentage, 0), 100);

            // 如果提供了状态信息，则更新状态标签
            if (!string.IsNullOrEmpty(status))
            {
                _statusLabel.Text = status;
            }
        }

        /// <summary>
        /// 完成处理，更新进度条到100%并显示完成消息
        /// </summary>
        /// <param name="message">完成时显示的消息，如果为null则显示默认消息</param>
        public void Complete(string message = null)
        {
            _progressBar.Value = 100;
            _statusLabel.Text = message ?? "处理完成";
            _cancelButton.Enabled = false;
        }

        /// <summary>
        /// 更新进度条值
        /// </summary>
        /// <param name="value">进度值（0-100）</param>
        private void UpdateProgress(int value)
        {
            _progressBar.Value = Math.Min(Math.Max(value, 0), 100);
        }
    }
}