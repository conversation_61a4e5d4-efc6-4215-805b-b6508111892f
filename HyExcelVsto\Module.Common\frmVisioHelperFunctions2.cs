/*
 * ============================================================================
 * 功能模块：Visio辅助功能模块 - 第二部分
 * ============================================================================
 * 
 * 模块作用：提供Visio文档图衔写入和批量处理功能，支持多线程操作
 * 
 * 主要功能：
 * - 图衔写入：向Visio文档的图纸标题栏写入指定信息
 * - 批量处理：支持批量处理多个Visio文件和页面
 * - 多线程支持：提供异步处理能力，避免UI阻塞
 * - 错误处理：完善的异常处理和进度报告机制
 * 
 * 执行逻辑：
 * 1. 图衔写入：验证参数 → 打开Visio应用 → 遍历文件列表 → 定位图衔区域 → 写入信息 → 保存文件
 * 2. 多线程处理：创建STA线程 → 执行批量操作 → 异常捕获和报告
 * 3. 文件处理：打开文档 → 处理页面 → 应用模板 → 保存结果
 * 
 * 注意事项：
 * - 需要Visio应用程序支持
 * - 支持上海院和南方院两种图衔模板
 * - 处理大批量文件时注意内存管理
 * - 使用STA线程模式确保COM组件正常工作
 * ============================================================================
 */

using ET;
using Microsoft.Office.Interop.Excel;
using Microsoft.Office.Interop.Visio;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Windows.Forms;
using Page = Microsoft.Office.Interop.Visio.Page;
using TextBox = System.Windows.Forms.TextBox;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// Visio辅助功能静态类 - 第二部分
    /// 提供图衔写入和批量处理功能，支持多线程操作
    /// </summary>
    public static partial class VisioHelperFunctions
    {
        #region 写入图衔
        /// <summary>
        /// 在新线程中执行写入图衔操作，避免阻塞UI界面
        /// </summary>
        /// <param name="filePathRange">包含Visio文件路径的Excel单元格范围</param>
        /// <param name="pageNameRange">包含页面名称的Excel单元格范围</param>
        /// <param name="targetDirectory">保存处理后文件的目标目录</param>
        /// <param name="isDirectSave">是否直接保存到原文件（true）或保存到目标目录（false）</param>
        /// <param name="writeRange图纸名称">包含图纸名称数据的Excel单元格范围</param>
        /// <param name="writeRange单位比例">包含单位比例数据的Excel单元格范围</param>
        /// <param name="writeRange出图日期">包含出图日期数据的Excel单元格范围</param>
        /// <param name="writeRange图纸编号">包含图纸编号数据的Excel单元格范围</param>
        /// <param name="sourceVisioFilePath">作为模板的源Visio文件路径</param>
        /// <param name="tuxianType">图衔类型（上海院/南方院）</param>
        /// <param name="scaleKeywords">比例尺关键字，用于自动识别比例</param>
        /// <param name="useScaleKeywords">是否启用比例关键字功能</param>
        /// <param name="deleteLeftContent">是否删除图衔左侧内容</param>
        /// <param name="deleteDrawingNumber">是否删除图纸编号</param>
        /// <param name="textBoxError">用于显示错误信息的文本框控件</param>
        /// <param name="textBoxProgress">用于显示处理进度的文本框控件</param>
        /// <remarks>
        /// 使用STA（Single Threaded Apartment）线程模式，确保COM组件正常工作
        /// 包含完整的异常处理机制，确保操作安全性
        /// </remarks>
        public static void 写入图衔PerRowsThread(
            Range filePathRange,
            Range pageNameRange,
            string targetDirectory,
            bool isDirectSave,
            Range writeRange图纸名称,
            Range writeRange单位比例,
            Range writeRange出图日期,
            Range writeRange图纸编号,
            string sourceVisioFilePath,
            string tuxianType,
            string scaleKeywords,
            bool useScaleKeywords,
            bool deleteLeftContent,
            bool deleteDrawingNumber,
            TextBox textBoxError,
            TextBox textBoxProgress)
        {
            // 创建新线程执行图衔写入操作
            Thread thread = new(
                () =>
                {
                    try
                    {
                        // 调用实际的图衔写入方法
                        写入图衔PerRows(
                            filePathRange,
                            pageNameRange,
                            targetDirectory,
                            isDirectSave,
                            writeRange图纸名称,
                            writeRange单位比例,
                            writeRange出图日期,
                            writeRange图纸编号,
                            sourceVisioFilePath,
                            tuxianType,
                            scaleKeywords,
                            useScaleKeywords,
                            deleteLeftContent,
                            deleteDrawingNumber,
                            textBoxError,
                            textBoxProgress);
                    }
                    catch (Exception ex)
                    {
                        // 捕获并记录执行过程中的异常
                        textBoxError.WriteLog($"执行过程中发生异常：{ex.Message}");
                    }
                });
            // 设置为STA线程模式，确保COM组件正常工作
            thread.SetApartmentState(ApartmentState.STA);
            // 启动线程
            thread.Start();
        }

        /// <summary>
        /// 执行批量写入图衔操作的核心方法
        /// </summary>
        /// <param name="filePathRange">包含Visio文件路径的Excel单元格范围</param>
        /// <param name="pageNameRange">包含页面名称的Excel单元格范围</param>
        /// <param name="targetDirectory">保存处理后文件的目标目录</param>
        /// <param name="isDirectSave">是否直接保存到原文件</param>
        /// <param name="writeRange图纸名称">图纸名称数据范围</param>
        /// <param name="writeRange单位比例">单位比例数据范围</param>
        /// <param name="writeRange出图日期">出图日期数据范围</param>
        /// <param name="writeRange图纸编号">图纸编号数据范围</param>
        /// <param name="sourceVisioFilePath">源Visio模板文件路径</param>
        /// <param name="tuxianType">图衔类型</param>
        /// <param name="scaleKeywords">比例关键字</param>
        /// <param name="useScaleKeywords">是否使用比例关键字</param>
        /// <param name="deleteLeftContent">是否删除左侧内容</param>
        /// <param name="deleteDrawingNumber">是否删除图号</param>
        /// <param name="textBoxError">错误信息显示控件</param>
        /// <param name="textBoxProgress">进度信息显示控件</param>
        /// <remarks>
        /// 执行逻辑：
        /// 1. 参数验证和初始化
        /// 2. 创建Visio应用程序实例
        /// 3. 遍历文件列表，逐个处理
        /// 4. 对每个文件的指定页面写入图衔信息
        /// 5. 保存处理结果并清理资源
        /// 
        /// 支持的操作：
        /// - 写入图纸名称、单位比例、出图日期、图纸编号
        /// - 删除指定的图衔内容
        /// - 应用比例关键字自动识别
        /// - 使用源文件作为模板
        /// </remarks>
        public static void 写入图衔PerRows(
            Range filePathRange,
            Range pageNameRange,
            string targetDirectory,
            bool isDirectSave,
            Range writeRange图纸名称,
            Range writeRange单位比例,
            Range writeRange出图日期,
            Range writeRange图纸编号,
            string sourceVisioFilePath,
            string tuxianType,
            string scaleKeywords,
            bool useScaleKeywords,
            bool deleteLeftContent,
            bool deleteDrawingNumber,
            TextBox textBoxError,
            TextBox textBoxProgress)
        {
            #region 变量值获取，信息初始化
            // 验证必需的文件路径范围参数
            if (filePathRange == null)
            {
                textBoxProgress.WriteLog("请选择文件路径所在的列");
                return;
            }

            // 处理页面名称范围的逻辑
            if (pageNameRange == null)
            {
                // 如果页面名称为空，则对所有前景页进行操作，清空相关写入范围
                writeRange图纸名称 = null;
                writeRange出图日期 = null;
                writeRange图纸编号 = null;
                writeRange单位比例 = null;
            }
            else if (pageNameRange.Columns.Count == 1 && filePathRange.Columns.Count > 1)
            {
                // 如果页面名称范围只有一列，但文件路径范围有多列，则使用第二列作为页面名称
                pageNameRange = filePathRange.Columns[2];
            }

            // 优化文件路径范围，去除空行和隐藏行
            filePathRange = filePathRange.OptimizeRangeSize();

            // 检查是否至少选择了一项操作内容
            if (string.IsNullOrEmpty(sourceVisioFilePath) &&
                writeRange图纸名称 == null &&
                writeRange出图日期 == null &&
                writeRange图纸编号 == null &&
                writeRange单位比例 == null &&
                !useScaleKeywords)
            {
                MessageBox.Show("没有选择任何操作内容，请选择至少一项操作。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            #endregion 变量值获取，信息初始化

            // 根据图衔类型选择对应的位置配置字典
            Dictionary<string, RectanglPosition> selectedPositions = tuxianType switch
            {
                "上海院" => VisShapePosition.SH相对位置,
                "南方院" => VisShapePosition.NF相对位置,
                _ => null
            };

            // 验证图衔类型配置是否有效
            if (selectedPositions == null)
            {
                textBoxError.WriteLog("请选择正确的图衔名称：上海院或南方院");
                return;
            }

            Microsoft.Office.Interop.Visio.Application visioApp = null;
            try
            {
                // 创建Visio应用程序实例（不可见模式）
                visioApp = ETVisio.CreateApplication(false);
                Document docPrev = null;           // 跟踪上一个处理的文档
                Range filePathCellPrev = null;     // 跟踪上一个处理的单元格

                // 获取处理进度相关信息
                int rowCount = filePathRange.GetVisibleRowCount();
                int currentRow = 1;

                // 处理源模板文件
                Document sourceDoc = null;
                if (!string.IsNullOrEmpty(sourceVisioFilePath) && File.Exists(sourceVisioFilePath))
                {
                    sourceDoc = ETVisio.Open(visioApp, sourceVisioFilePath, textBoxProgress, false);
                    if (sourceDoc == null)
                    {
                        textBoxError.WriteLog($"打开来源Visio文件失败，结束执行。文件名：{System.IO.Path.GetFileName(sourceVisioFilePath)}");
                        return;
                    }
                }

                // 遍历文件路径范围中的每一行
                foreach (Range row in filePathRange.Rows)
                {
                    // 跳过隐藏行
                    if (row.EntireRow.Hidden)
                        continue;

                    // 获取当前行的文件路径和页面名称单元格
                    Range filePathCell = filePathRange.Cells[row.Row, 1];
                    Range pageNameCell = pageNameRange?.Cells[row.Row, 1];
                    // 跳过空的文件路径单元格
                    if (ETExcelExtensions.IsCellEmpty(filePathCell))
                        continue;

                    // 提取文件路径、页面名称和文件名
                    string filePath = filePathCell.Value2.ToString();
                    string pageName = pageNameCell?.Value2?.ToString();
                    string fileName = System.IO.Path.GetFileName(filePath);

                    // 显示当前处理进度
                    textBoxProgress.WriteLog(
                        $"{currentRow++}/{rowCount}.正在处理 {DateTime.Now.ToString("HH:mm:ss")} ：{fileName}");

                    Document doc = null;
                    try
                    {
                        // 检查文件是否已在Visio中打开
                        doc = ETVisio.FindOpenDocumentByPath(visioApp, filePath);
                        if (doc == null)
                        {
                            // 文件未打开，先保存上一个文档
                            if (isDirectSave)
                                docPrev?.Save(true);
                            else
                                docPrev?.SaveToAnotherDirectory(targetDirectory, true);
                            // 标记上一个文件处理完成
                            if (filePathCellPrev != null)
                                filePathCellPrev.Format条件格式警示色(EnumWarningColor.蓝绿);

                            // 打开新文件
                            doc = ETVisio.Open(visioApp, filePath, textBoxProgress, false);
                            docPrev = doc;
                            filePathCellPrev = filePathCell;
                        }
                        else
                        {
                            // 文件已经打开，更新文档引用
                            docPrev = doc;
                            filePathCellPrev = filePathCell;
                        }

                        // 检查文档是否成功打开
                        if (doc == null)
                        {
                            int filterRowNumber = filePathRange.GetAutoFilterRowNumber();
                            if (row.Row > filterRowNumber)
                            {
                                textBoxError.WriteLog($"出现意外错误，无法处理文件：{fileName}");
                                filePathCell.Format条件格式警示色(EnumWarningColor.虾红);
                            }
                            continue;
                        }

                        // 根据图衔类型和用户选项准备要删除的图衔内容键
                        string[] keysToFilter = tuxianType switch
                        {
                            "上海院" => new string[] { "日期", "左侧" },
                            _ => new string[] { "工程名称", "日期", "左侧" } // 默认为南方院
                        };

                        // 根据用户选择添加额外的删除项
                        if (deleteLeftContent && !keysToFilter.Contains("左侧"))
                        {
                            keysToFilter = keysToFilter.Append("左侧").ToArray();
                        }

                        if (deleteDrawingNumber && !keysToFilter.Contains("图号"))
                        {
                            keysToFilter = keysToFilter.Append("图号").ToArray();
                        }

                        // 创建过滤后的位置字典，排除要删除的内容
                        Dictionary<string, RectanglPosition> filteredPositions = ETVisio.CreateFilteredDictionary(
                            selectedPositions,
                            keysToFilter);


                        // 调用文件处理方法，执行主要的图衔操作
                        ProcessVisioFile(
                            doc,
                            pageName,
                            sourceDoc,
                            selectedPositions,
                            writeRange单位比例,
                            useScaleKeywords,
                            scaleKeywords,
                            filteredPositions,
                            textBoxError,
                            textBoxProgress);

                        // 如果指定了页面名称范围，则处理特定页面的图衔信息
                        if (pageNameRange != null)
                        {
                            ProcessSpecificPage(
                                doc,
                                pageName,
                                selectedPositions,
                                writeRange图纸名称,
                                writeRange出图日期,
                                writeRange图纸编号,
                                deleteDrawingNumber,
                                row,
                                textBoxError,
                                textBoxProgress);
                        }
                    }
                    catch (Exception ex)
                    {
                        // 捕获单个文件处理过程中的异常
                        textBoxError.WriteLog($"处理文件 {fileName} 时发生错误：{ex.Message}");
                        filePathCell.Format条件格式警示色(EnumWarningColor.虾红);
                    }
                }

                // 处理完所有文件后，保存最后一个文档
                if (isDirectSave)
                    docPrev?.Save(true);
                else
                    docPrev?.SaveToAnotherDirectory(targetDirectory, true);

                // 标记最后一个文件处理完成
                if (filePathCellPrev != null)
                    filePathCellPrev.Format条件格式警示色(EnumWarningColor.蓝绿);

                // 关闭源模板文档
                sourceDoc?.Close(false);
            }
            catch (Exception ex)
            {
                // 捕获整个处理过程中的异常
                textBoxError.WriteLog($"执行过程中发生异常：{ex.Message}");
            }
            finally
            {
                // 确保Visio应用程序被正确关闭和释放
                if (visioApp != null)
                {
                    visioApp.Quit();
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(visioApp);
                }
                textBoxProgress.WriteLog("执行完成");
            }
        }

        /// <summary>
        /// 处理Visio文件的核心方法，负责页面遍历和图衔操作
        /// </summary>
        /// <param name="doc">要处理的Visio文档对象</param>
        /// <param name="pageName">指定的页面名称，为空时处理所有前景页</param>
        /// <param name="sourceDoc">作为模板的源Visio文档</param>
        /// <param name="selectedPositions">图衔元素位置配置字典</param>
        /// <param name="writeRange单位比例">单位比例数据的Excel范围</param>
        /// <param name="useScaleKeywords">是否启用比例关键字自动识别</param>
        /// <param name="scaleKeywords">比例关键字字符串</param>
        /// <param name="filteredPositions">过滤后的位置字典（排除要删除的元素）</param>
        /// <param name="textBoxError">错误信息显示控件</param>
        /// <param name="textBoxProgress">进度信息显示控件</param>
        /// <remarks>
        /// 处理逻辑：
        /// 1. 如果未指定页面名称，遍历所有前景页面
        /// 2. 如果指定了页面名称，只处理该特定页面
        /// 3. 对每个页面执行背景页检查和处理
        /// 4. 使用字典跟踪已处理的背景页，避免重复处理
        /// </remarks>
        public static void ProcessVisioFile(
           Document doc,
            string pageName,
           Document sourceDoc,
            Dictionary<string, RectanglPosition> selectedPositions,
            Range writeRange单位比例,
            bool useScaleKeywords,
            string scaleKeywords,
            Dictionary<string, RectanglPosition> filteredPositions,
            TextBox textBoxError,
            TextBox textBoxProgress)
        {
            // 创建字典跟踪已处理的背景页，避免重复处理
            Dictionary<int, bool> processedBackgrounds = [];

            if (string.IsNullOrEmpty(pageName))
            {
                // 页面名称为空时，对所有前景页进行操作
                for (int i = 1; i <= doc.Pages.Count; i++)
                {
                    Page page = (Page)doc.Pages[i];

                    // 调整页面显示以适应窗口（注释掉的跳转页面功能）
                    //ETVisio.JumpToPage(page);
                    ETVisio.FitPageToWindow(page.Application);

                    // 仅处理前景页面，跳过背景页面
                    if (ETVisio.IsBackgroundPage(page))
                        continue;

                    // 处理当前页面及其关联的背景页
                    ProcessPageWithBackgroundCheck(
                        page,
                        sourceDoc,
                        selectedPositions,
                        writeRange单位比例,
                        useScaleKeywords,
                        scaleKeywords,
                        filteredPositions,
                        processedBackgrounds,
                        textBoxError,
                        textBoxProgress);
                }
            }
            else
            {
                // 指定了页面名称时，只处理该特定页面
                Page page = ETVisio.FindPage(doc, pageName);
                if (page == null)
                {
                    textBoxError.WriteLog($"文件 {doc.Name} 中没有找到页面 {pageName}");
                    return;
                }

                // 显示当前处理的页面名称
                textBoxProgress.WriteLog($"，{pageName}", false);

                // 处理指定页面及其关联的背景页
                ProcessPageWithBackgroundCheck(
                    page,
                    sourceDoc,
                    selectedPositions,
                    writeRange单位比例,
                    useScaleKeywords,
                    scaleKeywords,
                    filteredPositions,
                    processedBackgrounds,
                    textBoxError,
                    textBoxProgress);
            }
        }

        /// <summary>
        /// 处理特定页面的图衔信息写入操作
        /// </summary>
        /// <param name="doc">要处理的Visio文档对象</param>
        /// <param name="pageName">目标页面名称</param>
        /// <param name="selectedPositions">图衔元素位置配置字典</param>
        /// <param name="writeRange图纸名称">图纸名称数据的Excel范围</param>
        /// <param name="writeRange出图日期">出图日期数据的Excel范围</param>
        /// <param name="writeRange图纸编号">图纸编号数据的Excel范围</param>
        /// <param name="deleteDrawingNumber">是否删除图纸编号（true时不写入图号）</param>
        /// <param name="row">当前处理的Excel行对象</param>
        /// <param name="textBoxError">错误信息显示控件</param>
        /// <param name="textBoxProgress">进度信息显示控件</param>
        /// <remarks>
        /// 此方法专门处理指定页面的图衔信息写入：
        /// 1. 查找指定名称的页面
        /// 2. 根据配置的位置信息定位图衔区域
        /// 3. 将Excel中的数据写入到对应的图衔位置
        /// 4. 支持选择性写入（根据参数决定是否写入特定信息）
        /// </remarks>
        public static void ProcessSpecificPage(
           Document doc,
            string pageName,
            Dictionary<string, RectanglPosition> selectedPositions,
            Range writeRange图纸名称,
            Range writeRange出图日期,
            Range writeRange图纸编号,
            bool deleteDrawingNumber,
            Range row,
            TextBox textBoxError,
            TextBox textBoxProgress)
        {
            // 在文档中查找指定名称的页面
            Page page = ETVisio.FindPage(doc, pageName);
            if (page == null)
            {
                textBoxError.WriteLog($"文件 {doc.Name} 中没有找到页面 {pageName}");
                return;
            }

            // 显示当前处理的页面名称
            textBoxProgress.WriteLog($"，{pageName}", false);

            // 根据配置的范围写入图纸名称
            if (writeRange图纸名称 != null)
                ProcessShapeText(page, selectedPositions["图名"], writeRange图纸名称.Cells[row.Row, 1]);

            // 根据配置的范围写入出图日期
            if (writeRange出图日期 != null)
                ProcessShapeText(page, selectedPositions["日期"], writeRange出图日期.Cells[row.Row, 1]);

            // 根据配置的范围写入图纸编号（如果未选择删除图号）
            if (writeRange图纸编号 != null && !deleteDrawingNumber)
                ProcessShapeText(page, selectedPositions["图号"], writeRange图纸编号.Cells[row.Row, 1]);
        }

        /// <summary>
        /// 处理页面及其关联的背景页，确保背景页不被重复处理
        /// </summary>
        /// <param name="page">要处理的Visio前景页面对象</param>
        /// <param name="sourceDoc">作为模板的源Visio文档</param>
        /// <param name="selectedPositions">图衔元素位置配置字典</param>
        /// <param name="writeRange单位比例">单位比例数据的Excel范围</param>
        /// <param name="useScaleKeywords">是否启用比例关键字自动识别</param>
        /// <param name="scaleKeywords">比例关键字字符串</param>
        /// <param name="filteredPositions">过滤后的位置字典（排除要删除的元素）</param>
        /// <param name="processedBackgrounds">已处理背景页的跟踪字典，以页面ID为键</param>
        /// <param name="textBoxError">错误信息显示控件</param>
        /// <param name="textBoxProgress">进度信息显示控件</param>
        /// <remarks>
        /// 处理逻辑：
        /// 1. 首先检查并处理关联的背景页（如果存在且未处理过）
        /// 2. 使用背景页的ID作为唯一标识符来跟踪已处理的背景页
        /// 3. 然后处理前景页面的图衔信息
        /// 4. 确保每个背景页只被处理一次，提高效率
        /// </remarks>
        public static void ProcessPageWithBackgroundCheck(
            Page page,
            Document sourceDoc,
            Dictionary<string, RectanglPosition> selectedPositions,
            Range writeRange单位比例,
            bool useScaleKeywords,
            string scaleKeywords,
            Dictionary<string, RectanglPosition> filteredPositions,
            Dictionary<int, bool> processedBackgrounds,
            TextBox textBoxError,
            TextBox textBoxProgress)
        {
            // 查找当前页面关联的最后一个背景页
            Page backgroundPage = ETVisio.FindLastBackgroundPage(page);

            // 如果存在背景页且尚未处理过，则先处理背景页
            if (backgroundPage != null)
            {
                int backgroundId = backgroundPage.ID;
                if (!processedBackgrounds.ContainsKey(backgroundId))
                {
                    // 处理背景页的图衔信息
                    ProcessBackgroundPage(
                        backgroundPage,
                        sourceDoc,
                        selectedPositions,
                        filteredPositions,
                        processedBackgrounds,
                        textBoxError,
                        textBoxProgress);
                }
            }

            // 处理前景页面的图衔信息
            ProcessForegroundPage(
                page,
                selectedPositions,
                writeRange单位比例,
                useScaleKeywords,
                scaleKeywords,
                filteredPositions,
                textBoxError,
                textBoxProgress);
        }

        /// <summary>
        /// 处理背景页
        /// </summary>
        /// <param name="backgroundPage">要处理的背景页</param>
        /// <param name="sourceDoc">源文档</param>
        /// <param name="selectedPositions">选定的位置字典</param>
        /// <param name="filteredPositions">过滤后的位置字典</param>
        /// <param name="processedBackgrounds">已处理的背景页字典</param>
        /// <param name="textBoxError">错误信息文本框</param>
        /// <param name="textBoxProgress">进度信息文本框</param>
        public static void ProcessBackgroundPage(
            Page backgroundPage,
            Document sourceDoc,
            Dictionary<string, RectanglPosition> selectedPositions,
            Dictionary<string, RectanglPosition> filteredPositions,
            Dictionary<int, bool> processedBackgrounds,
            TextBox textBoxError,
            TextBox textBoxProgress)
        {
            if (sourceDoc != null)
            {
                // 使用FindPage函数查找与backgroundPage同名的页面
                dynamic sourcePage = ETVisio.FindPage(sourceDoc, backgroundPage.Name);
                if (sourcePage != null)
                {
                    string isCopySuccess = ETVisio.Copy图衔SinglePage(sourcePage, backgroundPage, null);
                    if (!string.IsNullOrEmpty(isCopySuccess))
                    {
                        textBoxError.WriteLog($"[{sourceDoc.Name}] 背景页{backgroundPage.Name}复制失败,{isCopySuccess}");
                        textBoxProgress.WriteLog($"[{sourceDoc.Name}] ，背景页{backgroundPage.Name}复制失败", false);
                        return;
                    }
                    processedBackgrounds[backgroundPage.ID] = true;
                }
                else
                {
                    textBoxError.WriteLog($"[{sourceDoc.Name}] 源文件中未找到名为{backgroundPage.Name}的背景页");
                    textBoxProgress.WriteLog($"[{sourceDoc.Name}] ，背景页{backgroundPage.Name}复制失败", false);
                    return;
                }
            }
        }

        /// <summary>
        /// 处理前景页
        /// </summary>
        public static void ProcessForegroundPage(
            Page page,
            Dictionary<string, RectanglPosition> selectedPositions,
            Range writeRange单位比例,
            bool useScaleKeywords,
            string scaleKeywords,
            Dictionary<string, RectanglPosition> filteredPositions,
            TextBox textBoxError,
            TextBox textBoxProgress)
        {
            // 删除原有图衔内容
            ETVisio.Delete图衔子信息ForSinglePage(page, filteredPositions);

            if (writeRange单位比例 != null || useScaleKeywords)
            {
                RectangleRange rect单位比例位置_BL = ETVisio.Get图衔子图形在页面中的位置_BL(
                    page,
                    selectedPositions["单位比例"],
                    0.015,
                    0.02,
                    0.008,
                    0.008);
                if (rect单位比例位置_BL != null)
                {
                    if (writeRange单位比例 != null)
                    {
                        Range cell单位比例 = writeRange单位比例.Cells[page.Index, 1];
                        if (!ETExcelExtensions.IsCellEmpty(cell单位比例))
                            ETVisio.CreateRectangleText(
                                page,
                                ETVisio.Get图衔子图形在页面中的位置_BL(page, selectedPositions["单位比例"], 0, 0, 0, 0),
                                cell单位比例.Value.ToString());
                    }
                    else if (useScaleKeywords && !string.IsNullOrWhiteSpace(scaleKeywords))
                    {
                        // 将关键词字符串分割成数组
                        string[] keywords = scaleKeywords.Split(
                            new char[] { ',', '，', ';', '；', '|', '、' },
                            StringSplitOptions.RemoveEmptyEntries);
                        string 图纸名称 = ETVisio.ReadTextFromShapes(
                            page,
                            new Dictionary<string, RectangleRange>
                            {
                            { "图纸名称", ETVisio.Get图衔子图形在页面中的位置_BL(page, selectedPositions["图名"], 0.01, 0.03, 0.006) }
                            })["图纸名称"][0].ShapeText;
                        if (keywords.Any(keyword => 图纸名称.Contains(keyword)))
                        {
                            double actualScale = ETVisio.GetPaperDrawingScale(page);
                            string scaleText = $"mm,1:{actualScale.ToString()}";
                            ETVisio.CreateRectangleText(
                                page,
                                ETVisio.Get图衔子图形在页面中的位置_BL(page, selectedPositions["单位比例"], 0, 0, 0, 0),
                                scaleText);
                        }
                        else
                        {
                            ETVisio.CreateRectangleText(
                                page,
                                ETVisio.Get图衔子图形在页面中的位置_BL(page, selectedPositions["单位比例"], 0, 0, 0, 0),
                                "mm，示意");
                        }
                    }
                }
            }
        }

        public static void ProcessShapeText(Page page, RectanglPosition position, Range cell)
        {
            if (!ETExcelExtensions.IsCellEmpty(cell))
            {
                string cellValue = cell.Value.ToString();
                cellValue = cellValue.Replace(Environment.NewLine, string.Empty);
                if (cellValue.IndexOf("5G基站") > 0)
                    cellValue = cellValue.Replace("5G基站", $"{Environment.NewLine}5G基站");
                else if (cellValue.IndexOf("基站") > 0)
                    cellValue = cellValue.Replace("基站", $"{Environment.NewLine}基站");

                ETVisio.CreateRectangleText(page, ETVisio.Get图衔子图形在页面中的位置_BL(page, position, 0, 0, 0, 0), cellValue);
            }
        }
        #endregion 写入图衔
    }
}