﻿using ET;
using HyExcelVsto.Interfaces;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using ListBox = System.Windows.Forms.ListBox;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// 标签及下拉候选项设置窗体
    /// 提供Excel单元格标签和下拉选项的管理功能
    /// </summary>
    public partial class frm设置标签及下拉候选项 : Form, IExcelMessageReceiver
    {
        #region 字段和属性

        /// <summary>
        /// 窗体标识符
        /// </summary>
        public string FormIdentifier => "设置标签及下拉候选项";

        /// <summary>
        /// 符合条件的Range集合
        /// </summary>
        List<Range> _matchedCellRanges = [];

        /// <summary>
        /// 手动输入文本框的上一次内容
        /// </summary>
        string _previousRichTextBoxInput = string.Empty;

        /// <summary>
        /// 上一次处理的Range地址
        /// </summary>
        string _previousRangeAddress = string.Empty;

        #endregion

        #region 构造函数和初始化

        /// <summary>
        /// 初始化标签及下拉候选项设置窗体
        /// </summary>
        public frm设置标签及下拉候选项()
        {
            InitializeComponent();
        }

        #endregion

        #region Excel消息处理

        /// <summary>
        /// 处理Excel单元格选择变更消息
        /// </summary>
        /// <param name="target">选中的单元格Range对象</param>
        /// <param name="message">附加消息内容，用于指示是否包含数据验证</param>
        /// <exception cref="HyException">当Excel操作发生错误时抛出</exception>
        public void OnExcelSelectionMessage(Range target, string message = null)
        {
            try
            {
                bool hasValidation = message == "yes";
                显示标签(forceRenew: false, isContainValidation: hasValidation);
            }
            catch (Exception ex)
            {
                throw new ETException("处理Excel选择变更消息时发生错误", "单元格选择操作", ex);
            }
        }

        /// <summary>
        /// 显示当前选中列的标签信息
        /// </summary>
        /// <param name="forceRenew">是否强制刷新显示</param>
        /// <param name="isContainValidation">是否包含数据验证</param>
        /// <exception cref="HyException">当Excel操作发生错误时抛出</exception>
        void 显示标签(bool forceRenew, bool isContainValidation)
        {
            try
            {
                // 获取当前选择的列
                Range selectedColumn = ETExcelExtensions.GetSelectionColumn();
                if (selectedColumn == null)
                    return;

                // 获取当前选择的列的地址
                string selectedColumnAddress = ETExcelExtensions.GetCodeName(selectedColumn.Cells[1, 1].EntireColumn);

                // 检查是否需要更新显示
                bool isSameColumn = _previousRangeAddress == selectedColumnAddress;
                _previousRangeAddress = selectedColumnAddress;
                if (isSameColumn && !forceRenew)
                    return;

                // 处理无数据验证的情况
                if (!isContainValidation && !GlobalSettings.CellCandidateValues.ContainsKey(selectedColumnAddress))
                {
                    ClearControls();
                    return;
                }

                UpdateTagDisplay(selectedColumn, selectedColumnAddress);
            }
            catch (Exception ex)
            {
                throw new ETException("显示标签时发生错误", "标签显示操作", ex);
            }
        }

        /// <summary>
        /// 清空控件内容
        /// </summary>
        void ClearControls()
        {
            checkedListBox标签.Items.Clear();
            checkedListBox候选项.Items.Clear();
            textBox候选项.Text = string.Empty;
        }

        /// <summary>
        /// 更新标签显示
        /// </summary>
        /// <param name="selectedColumn">选中的列</param>
        /// <param name="columnAddress">列地址</param>
        void UpdateTagDisplay(Range selectedColumn, string columnAddress)
        {
            List<string> tagList;
            if (GlobalSettings.CellCandidateValues.ContainsKey(columnAddress))
            {
                tagList = GlobalSettings.CellCandidateValues[columnAddress];
            }
            else
            {
                tagList = ETExcelExtensions.ExtractAllTags(selectedColumn.Cells[1, 1]);
                if (tagList?.Count > 0)
                {
                    GlobalSettings.CellCandidateValues[columnAddress] = tagList;
                }
            }

            if (tagList == null || tagList.Count == 0)
            {
                ETForm.LoadCheckedListBox(checkedListBox标签, ["无可选项列表"], 100, true);
                return;
            }

            UpdateControlsByActiveTab(tagList);
        }

        /// <summary>
        /// 根据当前活动标签页更新控件显示
        /// </summary>
        /// <param name="tagList">标签列表</param>
        void UpdateControlsByActiveTab(List<string> tagList)
        {
            if (tabControl1.SelectedTab == tabPage标签输入工具)
            {
                ETForm.LoadCheckedListBox(checkedListBox标签, tagList, 100, true);
            }
            else
            {
                ETForm.LoadCheckedListBox(checkedListBox候选项, tagList, 100, true);
                textBox候选项.Text = string.Join(Environment.NewLine, tagList);
            }
        }

        #endregion

        #region 标签操作

        /// <summary>
        /// 获取已选择的标签集合
        /// </summary>
        /// <returns>已选择的标签HashSet</returns>
        HashSet<string> 获取已选择的标签()
        {
            if (!string.IsNullOrWhiteSpace(richTextBox手动输入.Text.Trim()))
            {
                return richTextBox手动输入.Text.ToHashset();
            }

            HashSet<string> selectedTags = [];
            for (int i = 0; i < checkedListBox标签.Items.Count; i++)
            {
                if (checkedListBox标签.GetItemChecked(i) &&
                    !string.IsNullOrWhiteSpace(checkedListBox标签.Items[i].ToString()))
                {
                    selectedTags.Add(checkedListBox标签.Items[i].ToString().Trim());
                }
            }

            return selectedTags;
        }

        /// <summary>
        /// 对目标单元格增删标签
        /// </summary>
        /// <param name="range">目标Range对象</param>
        /// <param name="isAdd">true为添加，false为删除</param>
        /// <exception cref="HyException">当Excel操作发生错误时抛出</exception>
        void 对目标单元格增删标签(Range range, bool isAdd)
        {
            try
            {
                if (range == null || ThisAddIn.ExcelApplication.Selection.Columns.Count > 1)
                    return;

                HashSet<string> selectedTags = 获取已选择的标签();
                if (selectedTags.Count == 0)
                    return;

                ETExcelExtensions.SetAppFastMode();
                int filterRow = range.GetParent().GetWorksheetFilterRowNumber();

                foreach (Range cell in range.Cells)
                {
                    if (checkBox仅标注标题以下.Checked && cell.Row <= filterRow)
                        continue;

                    string newTags = 生产新标签字符串(
                        cell.IsCellEmptyOrWhiteSpace() ? string.Empty : cell.Value.ToString(),
                        selectedTags,
                        isAdd);
                    cell.Value = newTags;
                }
            }
            catch (Exception ex)
            {
                throw new ETException("修改单元格标签时发生错误", "标签编辑操作", ex);
            }
            finally
            {
                ETExcelExtensions.SetAppNormalMode(true);
            }
        }

        /// <summary>
        /// 生成新的标签字符串
        /// </summary>
        /// <param name="inputString">输入字符串</param>
        /// <param name="labelHashset">标签集合</param>
        /// <param name="isAdd">true为添加，false为删除</param>
        /// <returns>处理后的标签字符串</returns>
        string 生产新标签字符串(string inputString, HashSet<string> labelHashset, bool isAdd)
        {
            HashSet<string> tags = string.IsNullOrWhiteSpace(inputString)
                ? []
                : inputString.ToHashset();

            foreach (string tag in labelHashset)
            {
                string trimmedTag = tag.Trim();
                if (string.IsNullOrEmpty(trimmedTag))
                    continue;

                if (isAdd)
                {
                    tags.Add(trimmedTag);
                }
                else
                {
                    tags.Remove(trimmedTag);
                }
            }

            return string.Join(";", tags);
        }

        #endregion

        #region 初始化及界面

        void richTextBox手动输入_Enter(object sender, EventArgs e) { richTextBox手动输入.Text = _previousRichTextBoxInput; }

        void checkedListBox标签_Enter(object sender, EventArgs e)
        {
            _previousRichTextBoxInput = richTextBox手动输入.Text;
            richTextBox手动输入.Text = string.Empty;
        }

        void 清空ToolStripMenuItem_Click(object sender, EventArgs e) { textBox候选项.Text = string.Empty; }

        void checkedListBox标签_Click(object sender, EventArgs e) { checkedListBox标签_Enter(sender, e); }

        void button取消_Click(object sender, EventArgs e) { Close(); }

        void 全选ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < checkedListBox候选项.Items.Count; i++)
                checkedListBox候选项.SetItemChecked(i, true);
        }

        void 全取消ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < checkedListBox候选项.Items.Count; i++)
                checkedListBox候选项.SetItemChecked(i, false);
        }

        public void tabPage标签输入工具_DoubleClick(object sender, EventArgs e)
        {
            设置隐藏下拉项();
            显示标签(true, false);
            richTextBox手动输入.Text = string.Empty;
        }

        void checkedListBox候选项_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (checkedListBox候选项.Items.Count == 0)
                tabPage标签输入工具_DoubleClick(null, null);
        }

        void textBox候选项_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(textBox候选项.Text))
                tabPage标签输入工具_DoubleClick(null, null);
        }

        void tabControl1_SelectedIndexChanged(object sender, EventArgs e) { 显示标签(true, false); }

        void 清除下拉可选项ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            Range selectColumn = ETExcelExtensions.GetSelectionColumn();
            selectColumn.DelValidation();
        }

        void checkedListBox标签_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            // 如果没有标签项，则跳转到标签输入工具页面
            if (checkedListBox标签.Items.Count == 0)
            {
                tabPage标签输入工具_DoubleClick(null, null);
                return;
            }

            // 获取当前选择范围
            Range selectionRange = ETExcelExtensions.GetSelectionRange();
            if (selectionRange == null)
                return;

            // 获取双击的标签项
            int i = checkedListBox标签.IndexFromPoint(e.Location);
            if (i == ListBox.NoMatches)
                return;

            // 根据不同的选择类型处理标签
            if (tabControl1.SelectedTab == tabPage标签输入工具 && radioButton双击替换.Checked)
            {
                // 替换标签项
                selectionRange.Value = checkedListBox标签.Items[i].ToString();
            }
            else
            {
                checkedListBox标签.SetItemChecked(i, true);
                对目标单元格增删标签(selectionRange, true);
                checkedListBox标签.SetItemChecked(i, true);
                // 添加新的标签项
                //var newTags = new HashSet<string> {checkedListBox标签.Items[i].ToString()};
                //selectionRange.Value = 生产新标签字符串(
                //    selectionRange.IsNullOrWhiteSpace() ? "" : selectionRange.Value.ToString(),
                //    newTags, true);
            }
        }

        void button增加_Click(object sender, EventArgs e) { 对目标单元格增删标签(ETExcelExtensions.GetSelectionRange(), true); }

        void button删除_Click(object sender, EventArgs e) { 对目标单元格增删标签(ETExcelExtensions.GetSelectionRange(), false); }

        void button设置临时下拉项_Click(object sender, EventArgs e)
        {
            HashSet<string> selectList = tabControl1.SelectedTab == tabPage列表方式 ? 获取已选择的下拉可选项() : 获取已输入的下拉可选项();

            if (selectList.Count == 0)
                return;

            设置隐藏下拉项(new List<string>(selectList));
        }

        void button清除筛选_Click(object sender, EventArgs e)
        {
            if (_matchedCellRanges == null || _matchedCellRanges.Count == 0)
                return;
            _matchedCellRanges[0].Filter取消设置指定列筛选();
        }

        #endregion 初始化及界面

        #region 筛选标签

        void 筛查没有勾选部分ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            HashSet<string> selectLabelHashset = 获取已选择的标签();
            if (selectLabelHashset.Count == 0)
                return;

            Range selectionRange = ETExcelExtensions.GetSelectionRange().EntireColumn.OptimizeRangeSize();
            if (selectionRange == null)
                return;

            ETExcelExtensions.SetAppFastMode();

            _matchedCellRanges.Clear();

            //_hs已选单元表格 = HHExcelExtensions.HyFindNoContainTags(selectionRange, selectLabelHashset, checkBox仅标注标题以下.Checked);
            _matchedCellRanges = ETExcelExtensions.FindCellsByTagCondition(
                selectionRange,
                selectLabelHashset,
                false,
                checkBox仅标注标题以下.Checked)
                .Cast<Range>()
                .ToList();
            ;
            ETExcelExtensions.Filter标色并筛选行(_matchedCellRanges);

            ETExcelExtensions.SetAppNormalMode(true);
        }

        void button筛选勾选部分_Click(object sender, EventArgs e)
        {
            HashSet<string> selectLabelHashset = 获取已选择的标签();
            if (selectLabelHashset.Count == 0)
                return;

            Range selectionRange = ETExcelExtensions.GetSelectionRange().EntireColumn.OptimizeRangeSize();
            if (selectionRange == null)
                return;

            ETExcelExtensions.SetAppFastMode();

            _matchedCellRanges?.Clear();

            //_hs已选单元表格 = HHExcelExtensions.HyFindContainTags(selectionRange, selectLabelHashset, checkBox仅标注标题以下.Checked);
            _matchedCellRanges = ETExcelExtensions.FindCellsByTagCondition(
                selectionRange,
                selectLabelHashset,
                true,
                checkBox仅标注标题以下.Checked)
                .Cast<Range>()
                .ToList();

            ETExcelExtensions.Filter标色并筛选行(_matchedCellRanges);

            ETExcelExtensions.SetAppNormalMode(true);
        }
        #endregion 筛选标签

        #region 设置标签

        /// <summary>
        /// 获取已选择的下拉可选项
        /// </summary>
        /// <returns>已选择的下拉可选项集合</returns>
        HashSet<string> 获取已选择的下拉可选项()
        {
            HashSet<string> selectedOptions = [];
            for (int i = 0; i < checkedListBox候选项.Items.Count; i++)
            {
                if (checkedListBox候选项.GetItemChecked(i) &&
                    !string.IsNullOrWhiteSpace(checkedListBox候选项.Items[i].ToString()))
                {
                    selectedOptions.Add(checkedListBox候选项.Items[i].ToString().Trim());
                }
            }

            return selectedOptions;
        }

        /// <summary>
        /// 获取已输入的下拉可选项
        /// </summary>
        /// <returns>已输入的下拉可选项集合</returns>
        HashSet<string> 获取已输入的下拉可选项()
        {
            return textBox候选项.Text.ToHashset(";,|、\n\t", true, true);
        }

        /// <summary>
        /// 设置下拉项按钮点击事件处理
        /// </summary>
        /// <exception cref="HyException">当Excel操作发生错误时抛出</exception>
        void button设置下拉项_Click(object sender, EventArgs e)
        {
            try
            {
                HashSet<string> selectedOptions = tabControl1.SelectedTab == tabPage列表方式
                    ? 获取已选择的下拉可选项()
                    : 获取已输入的下拉可选项();

                if (selectedOptions.Count == 0)
                    return;

                Range selectedColumn = ETExcelExtensions.GetSelectionColumn();
                selectedColumn.SetOptional设置下拉可选项(selectedOptions);

                更新存储的标签选项(selectedColumn);
            }
            catch (Exception ex)
            {
                throw new ETException("设置下拉选项时发生错误", "下拉选项操作", ex);
            }
        }

        /// <summary>
        /// 快速设置下拉可选项菜单项点击事件处理
        /// </summary>
        /// <exception cref="HyException">当Excel操作发生错误时抛出</exception>
        void 快速设置下拉可选项ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                Range selectedColumn = ETExcelExtensions.GetSelectionColumn();
                List<string> values = ETExcelExtensions.ExtractAllTags(selectedColumn.Cells[1, 1]);
                if (values == null || values.Count == 0)
                    return;

                if (string.Join(",", values).Length > 1023)
                {
                    MessageBox.Show(@"出错或者标签太长");
                    return;
                }

                selectedColumn.SetOptional设置下拉可选项(values);
                更新存储的标签选项(selectedColumn);
            }
            catch (Exception ex)
            {
                throw new ETException("快速设置下拉可选项时发生错误", "下拉选项操作", ex);
            }
        }

        /// <summary>
        /// 更新存储的标签选项
        /// </summary>
        /// <param name="selectedColumn">选中的列</param>
        /// <exception cref="HyException">当Excel操作发生错误时抛出</exception>
        public void 更新存储的标签选项(Range selectedColumn)
        {
            try
            {
                string columnAddress = selectedColumn.EntireColumn.GetCodeName();
                List<string> tagList = ETExcelExtensions.ExtractAllTags(selectedColumn.Cells[1, 1]);

                if (tagList == null || tagList.Count == 0)
                {
                    selectedColumn.DelValidation();
                    return;
                }

                if (GlobalSettings.CellCandidateValues.ContainsKey(columnAddress))
                {
                    GlobalSettings.CellCandidateValues[columnAddress] = tagList;
                }
                else
                {
                    GlobalSettings.CellCandidateValues.Add(columnAddress, tagList);
                }

                ThisAddIn.DropdownInputForm.PreEntireColumnAddress = string.Empty;
            }
            catch (Exception ex)
            {
                throw new ETException("更新存储的标签选项时发生错误", "标签选项操作", ex);
            }
        }

        /// <summary>
        /// 设置隐藏下拉项
        /// </summary>
        /// <param name="stringList">标签列表，如果为null则从当前选中列获取</param>
        /// <exception cref="HyException">当Excel操作发生错误时抛出</exception>
        public void 设置隐藏下拉项(List<string> stringList = null)
        {
            try
            {
                Range selectedColumn = ETExcelExtensions.GetSelectionColumn();
                if (selectedColumn == null)
                    return;

                string columnAddress = selectedColumn.EntireColumn.GetCodeName();

                stringList ??= new List<string>(ETExcelExtensions.ExtractAllTags(selectedColumn));
                if (stringList.Count > 100)
                {
                    MessageBox.Show(@"出错或者标签太多(超过100个)");
                    return;
                }

                if (GlobalSettings.CellCandidateValues.ContainsKey(columnAddress))
                {
                    GlobalSettings.CellCandidateValues[columnAddress] = stringList;
                }
                else
                {
                    GlobalSettings.CellCandidateValues.Add(columnAddress, stringList);
                }

                ThisAddIn.DropdownInputForm.PreEntireColumnAddress = string.Empty;
            }
            catch (Exception ex)
            {
                throw new ETException("设置隐藏下拉项时发生错误", "下拉选项操作", ex);
            }
        }

        #endregion 设置标签
    }
}