﻿/*
 * ============================================================================
 * 功能模块：Word辅助工具窗体
 * ============================================================================
 * 
 * 模块作用：提供Word文档处理的用户界面，支持关键字替换和PDF转换功能
 * 
 * 主要功能：
 * - Word关键字替换：使用Excel数据批量替换Word模板中的关键字
 * - Word转PDF：批量将Word文档转换为PDF格式
 * - 配置管理：保存和恢复用户的路径设置
 * - 进度显示：实时显示处理进度和错误信息
 * 
 * 执行逻辑：
 * 1. 窗体加载时绑定配置设置到控件
 * 2. 用户选择模板文件、数据范围和输出路径
 * 3. 调用后台线程执行Word处理操作
 * 4. 实时显示处理进度和结果
 * 
 * 注意事项：
 * - 需要安装Microsoft Word应用程序
 * - 使用多线程避免UI阻塞
 * - 支持配置信息的持久化存储
 * ============================================================================
 */

using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Windows.Forms;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// Word辅助工具窗体类
    /// 提供Word文档批量处理功能的用户界面
    /// </summary>
    public partial class frmWordHelper : Form
    {
        /// <summary>
        /// 初始化Word辅助工具窗体
        /// </summary>
        public frmWordHelper()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 窗体加载事件处理程序，绑定配置设置到相关控件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 将用户的配置设置绑定到界面控件，实现配置的自动保存和恢复：
        /// - 输出路径文本框绑定到配置文件
        /// - 模板文件选择控件绑定到配置文件
        /// </remarks>
        void frmHelper_Load(object sender, EventArgs e)
        {
            // 绑定输出路径文本框到配置设置
            ETForm.BindWindowsFormControl(textboxSavePath输出路径, ThisAddIn.ConfigurationSettings, "Word辅助填写", "textboxSavePath输出路径");
            // 绑定模板文件选择控件到配置设置
            ETForm.BindWindowsFormControl(ucFileSelectTemplatePath, ThisAddIn.ConfigurationSettings, "Word辅助填写", "ucFileSelectTemplatePath");
        }

        /// <summary>
        /// 替换Word关键字按钮点击事件处理程序
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 执行Word模板关键字替换操作：
        /// 1. 保存模板路径历史记录
        /// 2. 获取并优化Excel数据范围
        /// 3. 在后台线程中执行批量替换操作
        /// 4. 使用Excel数据逐行替换Word模板中的关键字
        /// </remarks>
        void button替换Word关键字_Click(object sender, EventArgs e)
        {
            // 保存模板文件路径到历史记录
            ucFileSelectTemplatePath.SavePathHistoryToFile();

            // 获取用户选择的Excel数据范围并优化
            Range dataRange = ucExcelRangeSelectData.SelectedRange.OptimizeRangeSize();
            if (dataRange == null) return;

            // 在后台线程中执行Word关键字替换操作
            ETWord.ReplaceTextInWordPerRowsThread(ucFileSelectTemplatePath.Text, dataRange, textboxSavePath输出路径.Text, textBoxProgress, textBoxError);
        }

        /// <summary>
        /// 转换为PDF按钮点击事件处理程序
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 执行Word文档批量转PDF操作：
        /// 1. 获取并优化包含Word文件路径的Excel数据范围
        /// 2. 在后台线程中执行批量转换操作
        /// 3. 将指定路径的Word文档转换为PDF格式
        /// 4. 支持原根路径和输出路径的灵活配置
        /// </remarks>
        void button执行_转换为PDF_Click(object sender, EventArgs e)
        {
            // 获取包含Word文件路径的Excel数据范围并优化
            Range dataRange = ucERS文件路径_转换为PDF.SelectedRange.OptimizeRangeSize();
            if (dataRange == null) return;

            // 在后台线程中执行Word转PDF操作
            ETWord.WordToPdfPerRowsThread(dataRange, ds输出路径_转换为PDF.Text, ds原根路径_转换为PDF.Text, textBoxProgress, textBoxError);
        }
    }
}