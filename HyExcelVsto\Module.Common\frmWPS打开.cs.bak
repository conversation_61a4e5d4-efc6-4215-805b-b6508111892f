﻿using ET;
using System;
using System.Windows.Forms;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// WPS与Excel应用程序切换窗体
    /// </summary>
    /// <remarks>
    /// 此窗体用于处理在WPS和Excel之间切换打开文件的操作
    /// </remarks>
    public partial class frmWpsExce切换 : Form
    {
        /// <summary>
        /// 初始化WPS与Excel切换窗体
        /// </summary>
        public frmWpsExce切换()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 窗体加载事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        void frmWPS打开_Load(object sender, EventArgs e)
        {
            try
            {
                if (!CheckWorkbookIsAvailable())
                {
                    MessageBox.Show(@"文件未保存或者不存在。", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    Close();
                    return;
                }

                AdjustGroupBoxPosition(groupBoxExcel, groupBoxWPS);

                bool isWpsApplication = ETExcelExtensions.IsWPS();
                groupBoxWPS.Visible = !isWpsApplication;
                groupBoxExcel.Visible = isWpsApplication;
            }
            catch (Exception ex)
            {
                throw new ETException("窗体加载失败", ex);
            }
        }

        /// <summary>
        /// 检查当前工作簿是否可用
        /// </summary>
        /// <returns>如果工作簿存在且路径有效返回true，否则返回false</returns>
        bool CheckWorkbookIsAvailable()
        {
            try
            {
                Microsoft.Office.Interop.Excel.Workbook activeWorkbook = ThisAddIn.ExcelApplication.ActiveWorkbook;
                return activeWorkbook != null && activeWorkbook.FullName.Split('\\').Length > 1;
            }
            catch (Exception ex)
            {
                throw new ETException("检查工作簿状态失败", ex);
            }
        }

        /// <summary>
        /// 关闭当前工作簿并使用指定应用程序重新打开
        /// </summary>
        /// <param name="saveChanges">是否保存更改</param>
        /// <param name="workbookPath">要重新打开的工作簿路径</param>
        void CloseAndReopenWorkbook(bool saveChanges, string workbookPath)
        {
            try
            {
                if (!CheckWorkbookIsAvailable()) return;

                Microsoft.Office.Interop.Excel.Workbook currentWorkbook = ThisAddIn.ExcelApplication.ActiveWorkbook;
                currentWorkbook.Close(saveChanges);

                if (!ETExcelExtensions.IsWPS())
                    ETExcelExtensions.OpenFileByWps(workbookPath);
                else
                    ETExcelExtensions.OpenFileByDefaultOffice(workbookPath);

                Close();
            }
            catch (Exception ex)
            {
                throw new ETException("关闭并重新打开工作簿失败", ex);
            }
        }

        /// <summary>
        /// 调整两个GroupBox的位置使其对齐
        /// </summary>
        /// <param name="targetGroupBox">目标GroupBox</param>
        /// <param name="sourceGroupBox">源GroupBox</param>
        void AdjustGroupBoxPosition(GroupBox targetGroupBox, GroupBox sourceGroupBox)
        {
            targetGroupBox.Top = sourceGroupBox.Top;
            targetGroupBox.Left = sourceGroupBox.Left;
        }

        /// <summary>
        /// 处理按钮点击事件的通用方法
        /// </summary>
        /// <param name="saveChanges">是否保存更改</param>
        void HandleButtonClick(bool saveChanges)
        {
            try
            {
                string workbookPath = ThisAddIn.ExcelApplication.ActiveWorkbook.FullName;
                CloseAndReopenWorkbook(saveChanges, workbookPath);
            }
            catch (Exception ex)
            {
                throw new ETException("处理按钮点击事件失败", ex);
            }
        }


        void button保存后关闭_WPS_Click(object sender, EventArgs e)
        {
            HandleButtonClick(true);
        }
        void button不保存并关闭_WPS_Click(object sender, EventArgs e)
        {
            HandleButtonClick(false);
        }
        void button保存后关闭_Excel_Click(object sender, EventArgs e)
        {
            HandleButtonClick(true);
        }
        void button不保存并关闭_Excel_Click(object sender, EventArgs e)
        {
            HandleButtonClick(false);
        }
    }
}